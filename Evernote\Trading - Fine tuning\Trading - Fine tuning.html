<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta itemprop="application" content="Evernote">
    <meta itemprop="export-data" content="20240302T221738Z">
    <meta itemprop="version" content="10.77.3">
    <style>
/*!
 * ce-177.3.1
 * Copyright 2013-2024 Evernote Corp. All rights reserved.
 * http://www.evernote.com
 *
 */en-note.peso,en-note.peso *{box-sizing:border-box}en-note.peso{word-wrap:break-word;font-feature-settings:"liga" 0;-webkit-touch-callout:text;box-sizing:initial;color:#333;display:block;flex-grow:1;font-family:Source Sans Pro,-apple-system,system-ui,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif;font-size:16px;font-variant-ligatures:none;line-height:1.45!important;min-height:100%;outline:none;overflow-wrap:break-word;padding:0 20px 80px;position:relative;touch-action:manipulation;-webkit-user-select:text;-moz-user-select:text;user-select:text;white-space:pre-wrap;white-space:break-spaces}en-note.peso>:last-child:last-child{margin-bottom:26px}:global(body.ui-revamp) en-note.peso{background-color:var(--color-background-fill-primary);font-family:Inter,Source Sans Pro,-apple-system,system-ui,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif;padding-bottom:inherit;padding-left:48px;padding-right:48px;padding-top:inherit;padding-top:0!important}@media print{en-note.peso{max-width:none!important;padding:0!important}}en-note.peso .para,en-note.peso h1,en-note.peso h2,en-note.peso h3,en-note.peso h4,en-note.peso h5{color:var(--textblock-lightmode-color,inherit)}en-note.peso table{max-width:100%}body.darkMode en-note.peso{background-color:#262626;color:#e6e6e6}body.darkMode en-note.peso .para,body.darkMode en-note.peso h1,body.darkMode en-note.peso h2,body.darkMode en-note.peso h3,body.darkMode en-note.peso h4,body.darkMode en-note.peso h5{color:var(--textblock-darkmode-color,inherit)}en-note.peso p{margin:0}.drag-image-holder a,en-note.peso a{color:#026fac;cursor:pointer;text-decoration:underline}body.darkMode .drag-image-holder a,body.darkMode en-note.peso a{color:#00a3f4}body.ui-revamp .drag-image-holder a,body.ui-revamp en-note.peso a{color:var(--color-surface-fill-secondarybrand-enabled)}en-note.peso pre{white-space:pre-wrap}en-note.peso li{pointer-events:none;position:relative}en-note.peso li>*{pointer-events:auto}.drag-image-holder .qwLsO,en-note.peso .qwLsO{background-color:#f2f2f2;border:1px solid #ccc;border-radius:3px;color:#333;display:inline;font-style:normal;font-weight:400;margin:0 2px;padding:2px 4px;text-decoration:none;vertical-align:initial}body.darkMode .drag-image-holder .qwLsO,body.darkMode en-note.peso .qwLsO{background-color:#333;border-color:#737373;color:#e6e6e6}body.ui-revamp .drag-image-holder .qwLsO,body.ui-revamp en-note.peso .qwLsO{background-color:var(--color-surface-fill-tertiary-enabled);border:0;color:var(--color-text-fill-primary-code);font-family:Fira Code;font-size:14px;margin:0}.drag-image-holder .jk8tJ,en-note.peso .jk8tJ{font-family:Source Sans Pro,sans-serif}.drag-image-holder h1 .jk8tJ,en-note.peso h1 .jk8tJ{font-weight:600}.drag-image-holder h1 .jk8tJ b,en-note.peso h1 .jk8tJ b{font-weight:700}.drag-image-holder h2 .jk8tJ,en-note.peso h2 .jk8tJ{font-weight:400}.drag-image-holder h2 .jk8tJ b,.drag-image-holder h3 .jk8tJ,.drag-image-holder h4 .jk8tJ,.drag-image-holder h5 .jk8tJ,.drag-image-holder h6 .jk8tJ,en-note.peso h2 .jk8tJ b,en-note.peso h3 .jk8tJ,en-note.peso h4 .jk8tJ,en-note.peso h5 .jk8tJ,en-note.peso h6 .jk8tJ{font-weight:600}.drag-image-holder h3 .jk8tJ b,.drag-image-holder h4 .jk8tJ b,.drag-image-holder h5 .jk8tJ b,.drag-image-holder h6 .jk8tJ b,en-note.peso h3 .jk8tJ b,en-note.peso h4 .jk8tJ b,en-note.peso h5 .jk8tJ b,en-note.peso h6 .jk8tJ b{font-weight:700}.drag-image-holder .PzcDn,en-note.peso .PzcDn{font-family:"Source Serif Pro",serif}.drag-image-holder h1 .PzcDn,en-note.peso h1 .PzcDn{font-weight:600}.drag-image-holder h1 .PzcDn b,en-note.peso h1 .PzcDn b{font-weight:700}.drag-image-holder h2 .PzcDn,en-note.peso h2 .PzcDn{font-weight:400}.drag-image-holder h2 .PzcDn b,.drag-image-holder h3 .PzcDn,.drag-image-holder h4 .PzcDn,.drag-image-holder h5 .PzcDn,.drag-image-holder h6 .PzcDn,en-note.peso h2 .PzcDn b,en-note.peso h3 .PzcDn,en-note.peso h4 .PzcDn,en-note.peso h5 .PzcDn,en-note.peso h6 .PzcDn{font-weight:600}.drag-image-holder h3 .PzcDn b,.drag-image-holder h4 .PzcDn b,.drag-image-holder h5 .PzcDn b,.drag-image-holder h6 .PzcDn b,en-note.peso h3 .PzcDn b,en-note.peso h4 .PzcDn b,en-note.peso h5 .PzcDn b,en-note.peso h6 .PzcDn b{font-weight:700}.drag-image-holder .I87I1,en-note.peso .I87I1{font-family:Zilla Slab,serif,slab-serif}.drag-image-holder h1 .I87I1,en-note.peso h1 .I87I1{font-weight:600}.drag-image-holder h1 .I87I1 b,en-note.peso h1 .I87I1 b{font-weight:700}.drag-image-holder h2 .I87I1,en-note.peso h2 .I87I1{font-weight:400}.drag-image-holder h2 .I87I1 b,en-note.peso h2 .I87I1 b{font-weight:600}.drag-image-holder h3 .I87I1,.drag-image-holder h4 .I87I1,.drag-image-holder h5 .I87I1,.drag-image-holder h6 .I87I1,en-note.peso h3 .I87I1,en-note.peso h4 .I87I1,en-note.peso h5 .I87I1,en-note.peso h6 .I87I1{font-size:20px;font-weight:600}.drag-image-holder h3 .I87I1 b,.drag-image-holder h4 .I87I1 b,.drag-image-holder h5 .I87I1 b,.drag-image-holder h6 .I87I1 b,en-note.peso h3 .I87I1 b,en-note.peso h4 .I87I1 b,en-note.peso h5 .I87I1 b,en-note.peso h6 .I87I1 b{font-weight:700}.drag-image-holder .O_pm7,en-note.peso .O_pm7{font-family:Source Code Pro,monospace;letter-spacing:-.01em}.drag-image-holder h1 .O_pm7,en-note.peso h1 .O_pm7{font-weight:600}.drag-image-holder h1 .O_pm7 b,en-note.peso h1 .O_pm7 b{font-weight:700}.drag-image-holder h2 .O_pm7,en-note.peso h2 .O_pm7{font-weight:400}.drag-image-holder h2 .O_pm7 b,.drag-image-holder h3 .O_pm7,.drag-image-holder h4 .O_pm7,.drag-image-holder h5 .O_pm7,.drag-image-holder h6 .O_pm7,en-note.peso h2 .O_pm7 b,en-note.peso h3 .O_pm7,en-note.peso h4 .O_pm7,en-note.peso h5 .O_pm7,en-note.peso h6 .O_pm7{font-weight:600}.drag-image-holder h3 .O_pm7 b,.drag-image-holder h4 .O_pm7 b,.drag-image-holder h5 .O_pm7 b,.drag-image-holder h6 .O_pm7 b,en-note.peso h3 .O_pm7 b,en-note.peso h4 .O_pm7 b,en-note.peso h5 .O_pm7 b,en-note.peso h6 .O_pm7 b{font-weight:700}.drag-image-holder .to9US,en-note.peso .to9US{font-family:Dancing Script,cursive,script}.drag-image-holder h1 .to9US,en-note.peso h1 .to9US{font-weight:400}.drag-image-holder h1 .to9US b,en-note.peso h1 .to9US b{font-weight:700}.drag-image-holder h2 .to9US,en-note.peso h2 .to9US{font-weight:400}.drag-image-holder h2 .to9US b,en-note.peso h2 .to9US b{font-weight:700}.drag-image-holder h3 .to9US,.drag-image-holder h4 .to9US,.drag-image-holder h5 .to9US,.drag-image-holder h6 .to9US,en-note.peso h3 .to9US,en-note.peso h4 .to9US,en-note.peso h5 .to9US,en-note.peso h6 .to9US{font-weight:400}.drag-image-holder h3 .to9US b,.drag-image-holder h4 .to9US b,.drag-image-holder h5 .to9US b,.drag-image-holder h6 .to9US b,en-note.peso h3 .to9US b,en-note.peso h4 .to9US b,en-note.peso h5 .to9US b,en-note.peso h6 .to9US b{font-weight:700}.drag-image-holder .WvEwz,en-note.peso .WvEwz{font-family:Kalam,cursive,handwritten;font-weight:300;letter-spacing:-.01em}.drag-image-holder .WvEwz b,.drag-image-holder h1 .WvEwz,en-note.peso .WvEwz b,en-note.peso h1 .WvEwz{font-weight:400}.drag-image-holder h1 .WvEwz b,en-note.peso h1 .WvEwz b{font-weight:700}.drag-image-holder h2 .WvEwz,en-note.peso h2 .WvEwz{font-weight:300}.drag-image-holder h2 .WvEwz b,.drag-image-holder h3 .WvEwz,.drag-image-holder h4 .WvEwz,.drag-image-holder h5 .WvEwz,.drag-image-holder h6 .WvEwz,en-note.peso h2 .WvEwz b,en-note.peso h3 .WvEwz,en-note.peso h4 .WvEwz,en-note.peso h5 .WvEwz,en-note.peso h6 .WvEwz{font-weight:400}.drag-image-holder h3 .WvEwz b,.drag-image-holder h4 .WvEwz b,.drag-image-holder h5 .WvEwz b,.drag-image-holder h6 .WvEwz b,en-note.peso h3 .WvEwz b,en-note.peso h4 .WvEwz b,en-note.peso h5 .WvEwz b,en-note.peso h6 .WvEwz b{font-weight:700}.drag-image-holder .UrtAp,en-note.peso .UrtAp{color:var(--lightmode-color)}body.darkMode .drag-image-holder .UrtAp,body.darkMode en-note.peso .UrtAp{color:var(--darkmode-color)}.drag-image-holder .R64m3,en-note.peso .R64m3{padding:3px 0}.drag-image-holder .R64m3.zTQp5,en-note.peso .R64m3.zTQp5{background-color:rgba(255,219,39,.45)}body.darkMode .drag-image-holder .R64m3.zTQp5,body.darkMode en-note.peso .R64m3.zTQp5{background-color:hsla(50,74%,72%,.45)}.drag-image-holder .R64m3.nyVDb,en-note.peso .R64m3.nyVDb{background-color:rgba(253,117,151,.45)}body.darkMode .drag-image-holder .R64m3.nyVDb,body.darkMode en-note.peso .R64m3.nyVDb{background-color:rgba(234,167,182,.45)}.drag-image-holder .R64m3.OV29h,en-note.peso .R64m3.OV29h{background-color:rgba(95,237,153,.45)}body.darkMode .drag-image-holder .R64m3.OV29h,body.darkMode en-note.peso .R64m3.OV29h{background-color:rgba(156,227,185,.45)}.drag-image-holder .R64m3.vfGi6,en-note.peso .R64m3.vfGi6{background-color:rgba(73,213,231,.45)}body.darkMode .drag-image-holder .R64m3.vfGi6,body.darkMode en-note.peso .R64m3.vfGi6{background-color:rgba(145,214,222,.45)}.drag-image-holder .R64m3.hKQev,en-note.peso .R64m3.hKQev{background-color:rgba(139,137,255,.45)}body.darkMode .drag-image-holder .R64m3.hKQev,body.darkMode en-note.peso .R64m3.hKQev{background-color:rgba(178,176,236,.45)}.drag-image-holder .R64m3.ZvszV,en-note.peso .R64m3.ZvszV{background-color:rgba(255,153,79,.45)}body.darkMode .drag-image-holder .R64m3.ZvszV,body.darkMode en-note.peso .R64m3.ZvszV{background-color:hsla(25,70%,75%,.45)}@media print{.drag-image-holder .R64m3,en-note.peso .R64m3{-webkit-print-color-adjust:exact!important;print-color-adjust:exact!important}}body[data-checklist-completion-styles=true] .drag-image-holder li[data-checked=true] .R64m3,body[data-checklist-completion-styles=true] en-note.peso li[data-checked=true] .R64m3{text-decoration:line-through}.Yhh_v{display:block;height:0;position:absolute;-webkit-user-select:all;-moz-user-select:all;user-select:all;width:0}.kJXje{height:0;-webkit-user-select:none;-moz-user-select:none;user-select:none}.drag-image-holder{font-family:Source Sans Pro,-apple-system,system-ui,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif}.elPx5{align-items:flex-start;background-color:#fff;border:1px solid #66b3da;border-radius:4px;box-shadow:0 3px 10px rgba(0,0,0,.15);display:flex;flex-direction:column;height:48px;justify-content:space-between;padding:10px;width:84px}body.darkMode .elPx5{background-color:#262626;border-color:#00a3f4}.elPx5>*{background:#b2d9ec;height:4px}.elPx5 :first-child,.elPx5 :nth-child(3){width:100%}.elPx5 :nth-child(2){width:70%}.EgxW9{word-wrap:break-word;background-color:#fff;border:1px solid #66b3da;border-radius:4px;box-shadow:0 3px 10px rgba(0,0,0,.15);font-size:16px;margin:0;max-height:300px;max-width:300px;overflow:hidden;padding:8px 16px;white-space:pre-wrap;width:auto}body.darkMode .EgxW9{background-color:#262626;border-color:#00a3f4}.EgxW9 address,.EgxW9 blockquote,.EgxW9 center,.EgxW9 dd,.EgxW9 div,.EgxW9 dt,.EgxW9 p{line-height:1.5}.EgxW9 blockquote{margin:0;padding-left:20px}.EgxW9>*{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.wyPK9{font-size:20px}.d8P7W{background-color:#66b3da;pointer-events:none;position:absolute;z-index:50}body.darkMode .d8P7W{background-color:#026fac}.d8P7W.rectangle{background-color:transparent;border:2px solid #66b3da}body.darkMode .d8P7W.rectangle{background-color:transparent;border-color:#026fac}.d8P7W.overX{border-right:2px dotted #66b3da}body.darkMode .d8P7W.overX{border-color:#026fac}.d8P7W.overY{border-bottom:2px dotted #66b3da}body.darkMode .d8P7W.overY{border-color:#026fac}.drag-image-holder{position:fixed}.drag-image-holder:after{background-color:#fff;content:"";display:block;height:2px;left:calc(100% - 2px);position:fixed;right:calc(100% - 2px);width:2px}body.darkMode .drag-image-holder:after{background-color:#333}.bZFPJ{border:2px solid #f2f2f2;border-radius:6px;height:auto;margin:12px 0;max-width:100%;outline:none;position:relative;transition:border-color .2s;width:375px}body.neutron .bZFPJ{width:343px}td .bZFPJ{min-width:375px}body.neutron td .bZFPJ{min-width:343px}body.darkMode .bZFPJ{border-color:#333}body.ui-revamp .bZFPJ{margin:var(--spacing-1-5) var(--spacing-0-75);width:auto}.bZFPJ._JjaU{border-color:#e6f6ea}body.darkMode .bZFPJ._JjaU{border-color:#203627}body.ui-revamp .bZFPJ._JjaU{border-color:var(--color-text-fill-brandsecondary-enabled)}.editing-locked .bZFPJ.bZFPJ{border-color:var(--selection-border-color-light)}body.darkMode .editing-locked .bZFPJ.bZFPJ{border-color:var(--selection-border-color-dark)}body.ui-revamp .editing-locked .bZFPJ.bZFPJ{border-color:var(--color-text-fill-brandsecondary-enabled)}.bZFPJ.iTLhb.iTLhb.iTLhb{width:100%}.bZFPJ.VEXjJ .yzJOC,.bZFPJ._hisT .yzJOC{font-style:italic}.bZFPJ>:first-child{border-radius:4px 4px 0 0}.bZFPJ>:last-child{border-radius:0 0 4px 4px}.bZFPJ>:only-child{border-radius:4px}.bZFPJ:hover{border-color:#e6e6e6}body.darkMode .bZFPJ:hover{border-color:#404040}.bZFPJ:hover .IRFZk{--header-background:#e6e6e6;background:#e6e6e6}body.darkMode .bZFPJ:hover .IRFZk{--header-background:#404040;background-color:#404040}.bZFPJ:hover .IRFZk.N3Rt5{--header-background:#026fac;background:#026fac}body.darkMode .bZFPJ:hover .IRFZk.N3Rt5{--header-background:#00a3f4;background-color:#00a3f4}body.ui-revamp .bZFPJ:hover .IRFZk.N3Rt5{--header-background:var(--color-text-fill-brandsecondary-enabled);background-color:var(--color-text-fill-brandsecondary-enabled)}.bZFPJ._JjaU:hover{border-color:#daeade}body.darkMode .bZFPJ._JjaU:hover{border-color:#2c4532}.bZFPJ._JjaU:hover .IRFZk{--header-background:#daeade;background:#daeade}body.darkMode .bZFPJ._JjaU:hover .IRFZk{--header-background:#2c4532;background-color:#2c4532}@media print{.bZFPJ{border-color:#f2f2f2!important}.bZFPJ._JjaU{border-color:#e6f6ea!important}}.collaborator-selected .bZFPJ.bZFPJ{border-color:var(--selection-border-color-light)}body.darkMode .collaborator-selected .bZFPJ.bZFPJ{border-color:var(--selection-border-color-dark)}body.ui-revamp .collaborator-selected .bZFPJ.bZFPJ{border-color:var(--color-text-fill-brandsecondary-enabled)}.selected .bZFPJ.bZFPJ{border-color:#66b3da}body.darkMode .selected .bZFPJ.bZFPJ{border-color:#00a3f4}body.ui-revamp .selected .bZFPJ.bZFPJ{border-color:var(--color-text-fill-brandsecondary-enabled)}.hasNestedSelection .bZFPJ.bZFPJ{border-color:rgba(102,179,218,.5)}body.darkMode .hasNestedSelection .bZFPJ.bZFPJ{border-color:rgba(0,163,244,.5)}body.ui-revamp .hasNestedSelection .bZFPJ.bZFPJ{border-color:var(--color-text-fill-brandsecondary-enabled)}.android-unselectable .bZFPJ,.android-unselectable .bZFPJ *{-webkit-user-select:none!important;-moz-user-select:none!important;user-select:none!important}@keyframes xjcYr{0%{left:-112px}to{left:100%}}.IRFZk{--header-background:#f2f2f2;align-items:center;background-color:#f2f2f2;color:#666;cursor:pointer;display:flex;font-size:16px;font-weight:600;height:32px;justify-content:space-between;padding:0 6px;transition:background-color .2s;width:100%}.IRFZk.N3Rt5{--header-background:#0081c2;background:#0081c2;color:#fff}body.darkMode .IRFZk{--header-background:#333;background-color:#333;color:#a6a6a6}body.darkMode .IRFZk.N3Rt5{--header-background:#00a3f4;background:#00a3f4;color:#000}.bZFPJ._JjaU .IRFZk{--header-background:#e6f6ea;background-color:#e6f6ea}body.darkMode .bZFPJ._JjaU .IRFZk{--header-background:#203627;background-color:#203627}body.ui-revamp .bZFPJ._JjaU .IRFZk{--header-background:rgba(77,100,255,.15);background-color:rgba(77,100,255,.15)}body.neutron .IRFZk{font-weight:700;height:44px}body.ui-revamp .IRFZk{color:var(--color-text-fill-secondary-enabled);font-size:14px;font-weight:500;height:auto;padding:var(--spacing-1) var(--spacing-2)}body.ui-revamp .IRFZk svg{color:#666}.IRFZk *{-webkit-user-select:none;-moz-user-select:none;user-select:none}body.firefox .IRFZk *{-webkit-user-select:all;-moz-user-select:all;user-select:all}.IRFZk .VonD6{background:transparent;border-radius:6px 6px 0 0;height:6px;left:-2px;overflow:hidden;position:absolute;right:-2px;top:-2px}.selected .IRFZk .VonD6{top:0}.IRFZk .VonD6:before{background:#d9d9d9;content:"";height:2px;position:absolute;top:0;width:100%}body.darkMode .IRFZk .VonD6:before{background:#404040}.IRFZk .VonD6:after{content:"";height:2px;position:absolute;top:0}.IRFZk .VonD6:not(.ixZHA):after{animation:xjcYr 2s linear infinite;background:linear-gradient(270deg,rgba(0,129,194,0),#0081c2 51%,rgba(0,129,194,0));width:112px}.IRFZk .VonD6.ixZHA:after{background:#0081c2;transform:scaleX(var(--loadingProgress));transform-origin:top left;transition:transform .5s ease-in-out;width:100%}.P0rnC{position:relative}.P0rnC .dSbRl{background:transparent;height:100%;overflow:hidden;position:absolute;resize:none}.P0rnC .AZVFJ{overflow-wrap:break-word;position:relative;visibility:hidden;white-space:pre-wrap;word-break:break-word}.AZVFJ,.dSbRl{padding:0;width:100%}.UMc3U{--cursor-color:#00a82d;background-image:none;background-position:top;background-repeat:no-repeat;background-size:auto;border:0;margin:-3px -3px 0;padding:0 3px;pointer-events:none;position:relative;top:-3px;word-break:normal;z-index:2}@media print{.UMc3U{display:none!important}}.UMc3U:after{background-color:var(--cursor-color);border:1px solid var(--cursor-color);content:"";margin:-1px;position:relative;top:3px}.yww35{bottom:0;left:0;overflow:hidden;position:absolute;right:0;top:0}.yww35 .EXxMJ{border-radius:2px;color:#fff;font-size:13px;font-weight:600;line-height:1.333;max-width:150px;overflow:hidden;padding:0 4px;text-overflow:ellipsis;white-space:nowrap;z-index:20}@media print{.yww35{display:none!important}.sharedCursorTextSelection{background-color:transparent!important}}.tMehR{border:1px solid #fff;border-radius:20px;height:12px;margin-right:4px;width:12px}.z8hBw{display:none}.sRXtK{align-items:center;display:flex;font-size:13px;height:17px;line-height:17px;max-width:98%;max-width:calc(100% - 16px);padding:0 4px 0 2px;position:absolute}.sRXtK svg{flex:12px 0 0;height:12px;margin:2px}.sRXtK b{flex:auto 0 1;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.sRXtK span{flex:auto 0 0;white-space:pre}.vWtIF{cursor:grab}.vWtIF>svg{fill:currentColor;display:block;height:100%;opacity:.25;transition:opacity .2s ease-in-out;width:100%}.vWtIF:hover>svg{opacity:.4}.iXopo{border:2px solid #f2f2f2;border-radius:6px;height:auto;margin:12px 0;max-width:100%;outline:none;position:relative;transition:border-color .2s;width:375px}body.neutron .iXopo{width:343px}td .iXopo{min-width:375px}body.neutron td .iXopo{min-width:343px}body.darkMode .iXopo{border-color:#333}body.ui-revamp .iXopo{margin:var(--spacing-1-5) var(--spacing-0-75);width:auto}.iXopo.X4WUr{border-color:#e6f6ea}body.darkMode .iXopo.X4WUr{border-color:#203627}body.ui-revamp .iXopo.X4WUr{border-color:var(--color-text-fill-brandsecondary-enabled)}.editing-locked .iXopo.iXopo{border-color:var(--selection-border-color-light)}body.darkMode .editing-locked .iXopo.iXopo{border-color:var(--selection-border-color-dark)}body.ui-revamp .editing-locked .iXopo.iXopo{border-color:var(--color-text-fill-brandsecondary-enabled)}.iXopo.dEOSM.dEOSM.dEOSM{width:100%}.iXopo.FRMbU .Cy7kJ,.iXopo.SIaGV .Cy7kJ{font-style:italic}.iXopo>:first-child{border-radius:4px 4px 0 0}.iXopo>:last-child{border-radius:0 0 4px 4px}.iXopo>:only-child{border-radius:4px}.iXopo:hover{border-color:#e6e6e6}body.darkMode .iXopo:hover{border-color:#404040}.iXopo:hover .wH9sk{--header-background:#e6e6e6;background:#e6e6e6}body.darkMode .iXopo:hover .wH9sk{--header-background:#404040;background-color:#404040}.iXopo:hover .wH9sk.dazQy{--header-background:#026fac;background:#026fac}body.darkMode .iXopo:hover .wH9sk.dazQy{--header-background:#00a3f4;background-color:#00a3f4}body.ui-revamp .iXopo:hover .wH9sk.dazQy{--header-background:var(--color-text-fill-brandsecondary-enabled);background-color:var(--color-text-fill-brandsecondary-enabled)}.iXopo.X4WUr:hover{border-color:#daeade}body.darkMode .iXopo.X4WUr:hover{border-color:#2c4532}.iXopo.X4WUr:hover .wH9sk{--header-background:#daeade;background:#daeade}body.darkMode .iXopo.X4WUr:hover .wH9sk{--header-background:#2c4532;background-color:#2c4532}@media print{.iXopo{border-color:#f2f2f2!important}.iXopo.X4WUr{border-color:#e6f6ea!important}}.collaborator-selected .iXopo.iXopo{border-color:var(--selection-border-color-light)}body.darkMode .collaborator-selected .iXopo.iXopo{border-color:var(--selection-border-color-dark)}body.ui-revamp .collaborator-selected .iXopo.iXopo{border-color:var(--color-text-fill-brandsecondary-enabled)}.selected .iXopo.iXopo{border-color:#66b3da}body.darkMode .selected .iXopo.iXopo{border-color:#00a3f4}body.ui-revamp .selected .iXopo.iXopo{border-color:var(--color-text-fill-brandsecondary-enabled)}.hasNestedSelection .iXopo.iXopo{border-color:rgba(102,179,218,.5)}body.darkMode .hasNestedSelection .iXopo.iXopo{border-color:rgba(0,163,244,.5)}body.ui-revamp .hasNestedSelection .iXopo.iXopo{border-color:var(--color-text-fill-brandsecondary-enabled)}.android-unselectable .iXopo,.android-unselectable .iXopo *{-webkit-user-select:none!important;-moz-user-select:none!important;user-select:none!important}@keyframes SbtBL{0%{left:-112px}to{left:100%}}.wH9sk{--header-background:#f2f2f2;align-items:center;background-color:#f2f2f2;color:#666;cursor:pointer;display:flex;font-size:16px;font-weight:600;height:32px;justify-content:space-between;padding:0 6px;transition:background-color .2s;width:100%}.wH9sk.dazQy{--header-background:#0081c2;background:#0081c2;color:#fff}body.darkMode .wH9sk{--header-background:#333;background-color:#333;color:#a6a6a6}body.darkMode .wH9sk.dazQy{--header-background:#00a3f4;background:#00a3f4;color:#000}.iXopo.X4WUr .wH9sk{--header-background:#e6f6ea;background-color:#e6f6ea}body.darkMode .iXopo.X4WUr .wH9sk{--header-background:#203627;background-color:#203627}body.ui-revamp .iXopo.X4WUr .wH9sk{--header-background:rgba(77,100,255,.15);background-color:rgba(77,100,255,.15)}body.neutron .wH9sk{font-weight:700;height:44px}body.ui-revamp .wH9sk{color:var(--color-text-fill-secondary-enabled);font-size:14px;font-weight:500;height:auto;padding:var(--spacing-1) var(--spacing-2)}body.ui-revamp .wH9sk svg{color:#666}.wH9sk *{-webkit-user-select:none;-moz-user-select:none;user-select:none}body.firefox .wH9sk *{-webkit-user-select:all;-moz-user-select:all;user-select:all}.wH9sk .nTeiz{background:transparent;border-radius:6px 6px 0 0;height:6px;left:-2px;overflow:hidden;position:absolute;right:-2px;top:-2px}.selected .wH9sk .nTeiz{top:0}.wH9sk .nTeiz:before{background:#d9d9d9;content:"";height:2px;position:absolute;top:0;width:100%}body.darkMode .wH9sk .nTeiz:before{background:#404040}.wH9sk .nTeiz:after{content:"";height:2px;position:absolute;top:0}.wH9sk .nTeiz:not(._Nncr):after{animation:SbtBL 2s linear infinite;background:linear-gradient(270deg,rgba(0,129,194,0),#0081c2 51%,rgba(0,129,194,0));width:112px}.wH9sk .nTeiz._Nncr:after{background:#0081c2;transform:scaleX(var(--loadingProgress));transform-origin:top left;transition:transform .5s ease-in-out;width:100%}.task{--task-height:40px;border:none;max-width:760px;outline:none;position:relative}.drag-image-holder .task{background-color:#fff}body.darkMode .drag-image-holder .task{background-color:#333}.task button{margin:0}.task.ProseMirror-selectednode,.task.selected{border-color:none;outline:none}.task.ProseMirror-selectednode{border-radius:6px;box-shadow:0 3px 10px rgba(0,0,0,.15)}.PeA_7{--task-action-color:rgba(0,0,0,.45);--task-overdue-color:#e54e40;align-items:flex-start;border:1px solid transparent;border-radius:6px;cursor:default;display:flex;flex-wrap:wrap;margin-bottom:2px;margin-right:30px;max-width:760px;min-height:var(--task-height);outline:none;padding:0 8px}[data-background-luminance-lightmode=light] .PeA_7{--task-action-color:rgba(0,0,0,.45)}[data-background-luminance-lightmode=dark] .PeA_7,body.darkMode .PeA_7{--task-action-color:hsla(0,0%,100%,.8)}body.darkMode [data-background-luminance-darkmode=light] .PeA_7{--task-action-color:rgba(0,0,0,.45)}body.darkMode [data-background-luminance-darkmode=dark] .PeA_7{--task-action-color:hsla(0,0%,100%,.8)}[data-background-luminance-lightmode=light] .PeA_7,body.darkMode .PeA_7{--task-overdue-color:#e54e40}[data-background-luminance-lightmode=dark] .PeA_7{--task-overdue-color:#f2b6b3}body.darkMode [data-background-luminance-darkmode=light] .PeA_7{--task-overdue-color:#e54e40}body.darkMode [data-background-luminance-darkmode=dark] .PeA_7{--task-overdue-color:#f2b6b3}.PeA_7.WJyLU{background-color:#f8f8f8}[data-background-luminance-lightmode=light] .PeA_7.WJyLU{background-color:hsla(0,0%,100%,.5)}[data-background-luminance-lightmode=dark] .PeA_7.WJyLU{background-color:rgba(0,0,0,.2)}body.darkMode .PeA_7.WJyLU{background-color:#333;border-color:#404040}body.ui-revamp .PeA_7.WJyLU{background-color:var(--color-surface-fill-secondary-enabled);border-color:transparent}body.darkMode.ui-revamp .PeA_7.WJyLU{background-color:var(--color-surface-fill-tertiary-enabled);border-color:transparent}body.darkMode [data-background-luminance-darkmode=light] .PeA_7.WJyLU{background-color:rgba(0,0,0,.2);border-color:transparent}body.darkMode [data-background-luminance-darkmode=dark] .PeA_7.WJyLU{background-color:rgba(0,0,0,.1);border-color:transparent}.PeA_7.JTqL_{background-color:#f8f8f8}[data-background-luminance-lightmode=light] .PeA_7.JTqL_{background-color:hsla(0,0%,100%,.5)}[data-background-luminance-lightmode=dark] .PeA_7.JTqL_{background-color:rgba(0,0,0,.2)}body.darkMode .PeA_7.JTqL_{background-color:#333;border-color:#404040}body.ui-revamp .PeA_7.JTqL_{background-color:var(--color-surface-fill-secondary-enabled);border-color:transparent}body.darkMode.ui-revamp .PeA_7.JTqL_{background-color:var(--color-surface-fill-tertiary-enabled);border-color:transparent}body.darkMode [data-background-luminance-darkmode=light] .PeA_7.JTqL_{background-color:rgba(0,0,0,.2);border-color:transparent}body.darkMode [data-background-luminance-darkmode=dark] .PeA_7.JTqL_{background-color:rgba(0,0,0,.1);border-color:transparent}@media(hover:hover){.PeA_7:hover:not(.JTqL_){background-color:#f8f8f8}[data-background-luminance-lightmode=light] .PeA_7:hover:not(.JTqL_){background-color:hsla(0,0%,100%,.5)}[data-background-luminance-lightmode=dark] .PeA_7:hover:not(.JTqL_){background-color:rgba(0,0,0,.2)}body.darkMode .PeA_7:hover:not(.JTqL_){background-color:#333;border-color:#404040}body.ui-revamp .PeA_7:hover:not(.JTqL_){background-color:var(--color-surface-fill-secondary-enabled);border-color:transparent}body.darkMode.ui-revamp .PeA_7:hover:not(.JTqL_){background-color:var(--color-surface-fill-tertiary-enabled);border-color:transparent}body.darkMode [data-background-luminance-darkmode=light] .PeA_7:hover:not(.JTqL_){background-color:rgba(0,0,0,.2);border-color:transparent}body.darkMode [data-background-luminance-darkmode=dark] .PeA_7:hover:not(.JTqL_){background-color:rgba(0,0,0,.1);border-color:transparent}}.PeA_7.ZJLBj.vOgKN{cursor:pointer}.PeA_7 .UXHS9>*,.PeA_7>*{min-height:var(--task-height)}.task.ProseMirror-selectednode .PeA_7,.task.selected .PeA_7{border-color:#66b3da;outline:none}body.darkMode .task.ProseMirror-selectednode .PeA_7,body.darkMode .task.selected .PeA_7{border-color:#00a3f4}body.ui-revamp .task.ProseMirror-selectednode .PeA_7,body.ui-revamp .task.selected .PeA_7{border-color:var(--color-text-fill-brandsecondary-enabled)}.PeA_7.X6Pln .nkDa1{color:#a6a6a6}body.darkMode .PeA_7.X6Pln .nkDa1{color:#737373}.PeA_7.RKOIN .hbOcq{opacity:.65}.PeA_7.RKOIN{font-style:italic}.FG_0Y{background-color:transparent;border:none;cursor:pointer;display:inline-block;outline:none;padding:0}.FG_0Y.zsLjd,.FG_0Y:disabled,.ZJLBj:not(.vOgKN) .FG_0Y{cursor:default}.FG_0Y.zsLjd svg,.FG_0Y.zsLjd svg:hover,.FG_0Y.zsLjd.FjxAy svg,.FG_0Y.zsLjd.FjxAy svg:hover,.FG_0Y:disabled svg,.FG_0Y:disabled svg:hover,.FG_0Y:disabled.FjxAy svg,.FG_0Y:disabled.FjxAy svg:hover,.ZJLBj:not(.vOgKN) .FG_0Y svg,.ZJLBj:not(.vOgKN) .FG_0Y svg:hover,.ZJLBj:not(.vOgKN) .FG_0Y.FjxAy svg,.ZJLBj:not(.vOgKN) .FG_0Y.FjxAy svg:hover{color:rgba(0,0,0,.45);pointer-events:none}[data-background-luminance-lightmode=light] .FG_0Y.zsLjd svg,[data-background-luminance-lightmode=light] .FG_0Y.zsLjd svg:hover,[data-background-luminance-lightmode=light] .FG_0Y.zsLjd.FjxAy svg,[data-background-luminance-lightmode=light] .FG_0Y.zsLjd.FjxAy svg:hover,[data-background-luminance-lightmode=light] .FG_0Y:disabled svg,[data-background-luminance-lightmode=light] .FG_0Y:disabled svg:hover,[data-background-luminance-lightmode=light] .FG_0Y:disabled.FjxAy svg,[data-background-luminance-lightmode=light] .FG_0Y:disabled.FjxAy svg:hover,[data-background-luminance-lightmode=light] .ZJLBj:not(.vOgKN) .FG_0Y svg,[data-background-luminance-lightmode=light] .ZJLBj:not(.vOgKN) .FG_0Y svg:hover,[data-background-luminance-lightmode=light] .ZJLBj:not(.vOgKN) .FG_0Y.FjxAy svg,[data-background-luminance-lightmode=light] .ZJLBj:not(.vOgKN) .FG_0Y.FjxAy svg:hover{color:rgba(0,0,0,.45)}[data-background-luminance-lightmode=dark] .FG_0Y.zsLjd svg,[data-background-luminance-lightmode=dark] .FG_0Y.zsLjd svg:hover,[data-background-luminance-lightmode=dark] .FG_0Y.zsLjd.FjxAy svg,[data-background-luminance-lightmode=dark] .FG_0Y.zsLjd.FjxAy svg:hover,[data-background-luminance-lightmode=dark] .FG_0Y:disabled svg,[data-background-luminance-lightmode=dark] .FG_0Y:disabled svg:hover,[data-background-luminance-lightmode=dark] .FG_0Y:disabled.FjxAy svg,[data-background-luminance-lightmode=dark] .FG_0Y:disabled.FjxAy svg:hover,[data-background-luminance-lightmode=dark] .ZJLBj:not(.vOgKN) .FG_0Y svg,[data-background-luminance-lightmode=dark] .ZJLBj:not(.vOgKN) .FG_0Y svg:hover,[data-background-luminance-lightmode=dark] .ZJLBj:not(.vOgKN) .FG_0Y.FjxAy svg,[data-background-luminance-lightmode=dark] .ZJLBj:not(.vOgKN) .FG_0Y.FjxAy svg:hover,body.darkMode .FG_0Y.zsLjd svg,body.darkMode .FG_0Y.zsLjd svg:hover,body.darkMode .FG_0Y.zsLjd.FjxAy svg,body.darkMode .FG_0Y.zsLjd.FjxAy svg:hover,body.darkMode .FG_0Y:disabled svg,body.darkMode .FG_0Y:disabled svg:hover,body.darkMode .FG_0Y:disabled.FjxAy svg,body.darkMode .FG_0Y:disabled.FjxAy svg:hover,body.darkMode .ZJLBj:not(.vOgKN) .FG_0Y svg,body.darkMode .ZJLBj:not(.vOgKN) .FG_0Y svg:hover,body.darkMode .ZJLBj:not(.vOgKN) .FG_0Y.FjxAy svg,body.darkMode .ZJLBj:not(.vOgKN) .FG_0Y.FjxAy svg:hover,body.darkMode [data-background-luminance-darkmode=dark] .FG_0Y.zsLjd svg,body.darkMode [data-background-luminance-darkmode=dark] .FG_0Y.zsLjd svg:hover,body.darkMode [data-background-luminance-darkmode=dark] .FG_0Y.zsLjd.FjxAy svg,body.darkMode [data-background-luminance-darkmode=dark] .FG_0Y.zsLjd.FjxAy svg:hover,body.darkMode [data-background-luminance-darkmode=dark] .FG_0Y:disabled svg,body.darkMode [data-background-luminance-darkmode=dark] .FG_0Y:disabled svg:hover,body.darkMode [data-background-luminance-darkmode=dark] .FG_0Y:disabled.FjxAy svg,body.darkMode [data-background-luminance-darkmode=dark] .FG_0Y:disabled.FjxAy svg:hover,body.darkMode [data-background-luminance-darkmode=dark] .ZJLBj:not(.vOgKN) .FG_0Y svg,body.darkMode [data-background-luminance-darkmode=dark] .ZJLBj:not(.vOgKN) .FG_0Y svg:hover,body.darkMode [data-background-luminance-darkmode=dark] .ZJLBj:not(.vOgKN) .FG_0Y.FjxAy svg,body.darkMode [data-background-luminance-darkmode=dark] .ZJLBj:not(.vOgKN) .FG_0Y.FjxAy svg:hover{color:hsla(0,0%,100%,.8)}body.darkMode [data-background-luminance-darkmode=light] .FG_0Y.zsLjd svg,body.darkMode [data-background-luminance-darkmode=light] .FG_0Y.zsLjd svg:hover,body.darkMode [data-background-luminance-darkmode=light] .FG_0Y.zsLjd.FjxAy svg,body.darkMode [data-background-luminance-darkmode=light] .FG_0Y.zsLjd.FjxAy svg:hover,body.darkMode [data-background-luminance-darkmode=light] .FG_0Y:disabled svg,body.darkMode [data-background-luminance-darkmode=light] .FG_0Y:disabled svg:hover,body.darkMode [data-background-luminance-darkmode=light] .FG_0Y:disabled.FjxAy svg,body.darkMode [data-background-luminance-darkmode=light] .FG_0Y:disabled.FjxAy svg:hover,body.darkMode [data-background-luminance-darkmode=light] .ZJLBj:not(.vOgKN) .FG_0Y svg,body.darkMode [data-background-luminance-darkmode=light] .ZJLBj:not(.vOgKN) .FG_0Y svg:hover,body.darkMode [data-background-luminance-darkmode=light] .ZJLBj:not(.vOgKN) .FG_0Y.FjxAy svg,body.darkMode [data-background-luminance-darkmode=light] .ZJLBj:not(.vOgKN) .FG_0Y.FjxAy svg:hover{color:rgba(0,0,0,.45)}body.ui-revamp .FG_0Y{width:-moz-max-content;width:max-content}.ZJLBj.vOgKN .FG_0Y{cursor:pointer}.FG_0Y svg{border-radius:4px;color:var(--task-action-color);display:inline-block;height:24px;max-width:24px;vertical-align:middle}.UXHS9.LVtVQ .FG_0Y svg:hover:not(.IAHuG){background-color:#e6e6e6}[data-background-luminance-lightmode=dark] .UXHS9.LVtVQ .FG_0Y svg:hover:not(.IAHuG),body.darkMode .UXHS9.LVtVQ .FG_0Y svg:hover:not(.IAHuG){background-color:#404040}body.darkMode [data-background-luminance-darkmode=light] .UXHS9.LVtVQ .FG_0Y svg:hover:not(.IAHuG){background-color:#e6e6e6}.UXHS9 .FG_0Y{display:none;margin:0 3px}.PeA_7.WJyLU .UXHS9 .FG_0Y{display:inline-block}.UXHS9 .FG_0Y svg{color:#737373;height:14px}body.darkMode .UXHS9 .FG_0Y svg{color:#a6a6a6}.PeA_7.WJyLU .UXHS9 .FG_0Y svg{height:24px}.M3l87{visibility:hidden}.R3xxL{align-items:center;background:#fff;border-radius:8px;box-shadow:1px 2px 6px rgba(0,0,0,.15);color:#737373;display:inline-flex;flex:0 0 auto;font-size:14px;justify-content:flex-end;min-height:0;min-width:32px;padding:4px 7px;position:absolute;right:38px;top:-16px}body.darkMode .R3xxL{background-color:#262626;color:#a6a6a6}.R3xxL button:first-of-type{margin:0 3px 0 0}.R3xxL button:last-of-type{margin:0 0 0 3px}.R3xxL .FG_0Y{margin:0 3px}.R3xxL .FG_0Y svg{color:#737373}body.darkMode .R3xxL .FG_0Y svg{color:#a6a6a6}.R3xxL .REzLi{padding:0 0 0 3px}.R3xxL .FG_0Y.REzLi svg._bDRr{color:inherit}.R3xxL .FG_0Y.REzLi.wUa6z+.FG_0Y.REzLi.o5Et0 svg.IAHuG{height:12px;top:-1px}.R3xxL .FG_0Y.REzLi.o5Et0{margin-right:-5px}.R3xxL .FG_0Y.REzLi.o5Et0>svg{height:15px}.R3xxL .FG_0Y.REzLi.o5Et0 svg.IAHuG{height:12px;position:relative;top:-2px}.R3xxL .FG_0Y.bUYqz.Lhfwt svg{color:#e54e40}.R3xxL .FG_0Y.Hwgxx.kuWKQ svg{color:#0081c2}.R3xxL .FG_0Y.ujVcs{position:relative}.R3xxL .FG_0Y.viFwv{position:relative;top:2px}.R3xxL .FG_0Y.Hwgxx:hover,.R3xxL .FG_0Y.Jh0Ej:hover,.R3xxL .FG_0Y.bUYqz:hover,.R3xxL .FG_0Y.ujVcs:hover,.R3xxL .FG_0Y.viFwv:hover{background-color:#f8f8f8}body.darkMode .R3xxL .FG_0Y.Hwgxx:hover,body.darkMode .R3xxL .FG_0Y.Jh0Ej:hover,body.darkMode .R3xxL .FG_0Y.bUYqz:hover,body.darkMode .R3xxL .FG_0Y.ujVcs:hover,body.darkMode .R3xxL .FG_0Y.viFwv:hover{background-color:#333}.R3xxL .gry_t .PNboT{background-color:#d9d9d9}body.darkMode .R3xxL .gry_t .PNboT{background-color:#737373}.PeA_7.X6Pln .R3xxL,.PeA_7.X6Pln .R3xxL .FG_0Y svg,.PeA_7.X6Pln .R3xxL .FG_0Y.Hwgxx.kuWKQ svg,.PeA_7.X6Pln .R3xxL .FG_0Y.bUYqz.Lhfwt svg,.PeA_7.X6Pln .UXHS9,.PeA_7.X6Pln .UXHS9 .FG_0Y svg,.PeA_7.X6Pln .UXHS9 .FG_0Y.Hwgxx.kuWKQ svg,.PeA_7.X6Pln .UXHS9 .FG_0Y.bUYqz.Lhfwt svg{color:#a6a6a6}body.darkMode .PeA_7.X6Pln .R3xxL,body.darkMode .PeA_7.X6Pln .R3xxL .FG_0Y svg,body.darkMode .PeA_7.X6Pln .R3xxL .FG_0Y.Hwgxx.kuWKQ svg,body.darkMode .PeA_7.X6Pln .R3xxL .FG_0Y.bUYqz.Lhfwt svg,body.darkMode .PeA_7.X6Pln .UXHS9,body.darkMode .PeA_7.X6Pln .UXHS9 .FG_0Y svg,body.darkMode .PeA_7.X6Pln .UXHS9 .FG_0Y.Hwgxx.kuWKQ svg,body.darkMode .PeA_7.X6Pln .UXHS9 .FG_0Y.bUYqz.Lhfwt svg{color:#737373}.FG_0Y.FjxAy{flex:0 0 24px}.FG_0Y.FjxAy svg{color:#a73cbd;vertical-align:text-top}body.darkMode .FG_0Y.FjxAy svg{color:#a73cbd}body.ui-revamp .FG_0Y.FjxAy svg{color:var(--color-icon-fill-purple-enabled)}[data-background-luminance-lightmode=light] .FG_0Y.FjxAy svg{--fill-opacity:0.85;--fill-color:#fff;--stroke-opacity:0.85;--stroke-color:#fff}[data-background-luminance-lightmode=dark] .FG_0Y.FjxAy svg{--fill-opacity:0.85;--fill-color:#fff;--stroke-opacity:0.2;--stroke-color:#000}body.darkMode [data-background-luminance-darkmode=dark] .FG_0Y.FjxAy svg,body.darkMode [data-background-luminance-darkmode=light] .FG_0Y.FjxAy svg{--fill-opacity:0.85;--fill-color:#262626;--stroke-opacity:0.2;--stroke-color:#000}.PeA_7.RKOIN .FG_0Y.FjxAy svg,.PeA_7.X6Pln .FG_0Y.FjxAy svg{color:#a6a6a6}body.darkMode .PeA_7.RKOIN .FG_0Y.FjxAy svg,body.darkMode .PeA_7.X6Pln .FG_0Y.FjxAy svg{color:#737373}body.ui-revamp .PeA_7.RKOIN .FG_0Y.FjxAy svg,body.ui-revamp .PeA_7.X6Pln .FG_0Y.FjxAy svg{color:var(--color-icon-fill-purple-enabled)}.FG_0Y.FjxAy.RKOIN svg,.FG_0Y.FjxAy.RKOIN svg:hover{--obsolete-stroke:#fff;--obsolete-fill:#f0a00d}body.darkMode .FG_0Y.FjxAy.RKOIN svg,body.darkMode .FG_0Y.FjxAy.RKOIN svg:hover{--obsolete-stroke:#000;--obsolete-fill:#db8f00}.KxwvA{margin-right:4px}.KxwvA,.UXHS9 .bDTec{align-items:center;display:flex}.UXHS9 .bDTec{background-color:#fff;border:1px dashed #ccc;border-radius:40px;color:#737373;cursor:pointer;font-size:14px;font-weight:400;height:26px;margin:0 4px;padding:0 10px 0 8px}body.darkMode .UXHS9 .bDTec{background-color:#1a1a1a;border:1px dashed #404040;color:#737373}.UXHS9 .bDTec:focus,.UXHS9 .bDTec:hover{background-color:#f2f2f2;border-color:#a6a6a6;color:#666}body.darkMode .UXHS9 .bDTec:focus,body.darkMode .UXHS9 .bDTec:hover{background-color:#1a1a1a;border-color:#666;color:#a6a6a6}.UXHS9 .bDTec:focus svg,.UXHS9 .bDTec:hover svg{color:#666}body.darkMode .UXHS9 .bDTec:focus svg,body.darkMode .UXHS9 .bDTec:hover svg{color:#737373}.UXHS9 .FG_0Y.bUYqz.Lhfwt{display:inline-block}.UXHS9 .FG_0Y.bUYqz.Lhfwt svg{color:#e54e40}body.darkMode .UXHS9 .FG_0Y.bUYqz.Lhfwt svg{color:#e86357}.PeA_7.RKOIN .UXHS9 .FG_0Y.bUYqz.Lhfwt svg,.PeA_7.X6Pln:not(.WJyLU) .UXHS9 .FG_0Y.bUYqz.Lhfwt svg{color:#a6a6a6}body.darkMode .PeA_7.RKOIN .UXHS9 .FG_0Y.bUYqz.Lhfwt svg,body.darkMode .PeA_7.X6Pln:not(.WJyLU) .UXHS9 .FG_0Y.bUYqz.Lhfwt svg{color:#737373}.UXHS9 .FG_0Y.Hwgxx.kuWKQ{display:inline-block}.UXHS9 .FG_0Y.Hwgxx.kuWKQ svg{color:#0081c2}body.darkMode .UXHS9 .FG_0Y.Hwgxx.kuWKQ svg{color:#00a3f4}body.ui-revamp .UXHS9 .FG_0Y.Hwgxx.kuWKQ svg{color:var(--color-text-fill-brandsecondary-enabled)}.PeA_7.RKOIN .UXHS9 .FG_0Y.Hwgxx.kuWKQ svg,.PeA_7.X6Pln:not(.WJyLU) .UXHS9 .FG_0Y.Hwgxx.kuWKQ svg{color:#a6a6a6}body.darkMode .PeA_7.RKOIN .UXHS9 .FG_0Y.Hwgxx.kuWKQ svg,body.darkMode .PeA_7.X6Pln:not(.WJyLU) .UXHS9 .FG_0Y.Hwgxx.kuWKQ svg{color:#737373}.FG_0Y.REzLi{color:inherit;font-size:14px;margin-bottom:-3px;margin-right:2px}.UXHS9 .FG_0Y.REzLi{display:inline-block}.grdL8:not(.X6Pln):not(.RKOIN) .FG_0Y.REzLi .hlPgk{color:var(--task-overdue-color)}.RKOIN .FG_0Y.REzLi .hlPgk{font-style:italic}.FG_0Y.REzLi ._bDRr{color:inherit}.grdL8:not(.X6Pln):not(.RKOIN) .FG_0Y.REzLi ._bDRr{color:var(--task-overdue-color)}.FG_0Y.REzLi svg._bDRr{height:24px;margin:-3px 0 0 4px}.grdL8:not(.X6Pln):not(.RKOIN) .FG_0Y.REzLi svg._bDRr{color:var(--task-overdue-color)}.FG_0Y.REzLi .IAHuG{color:inherit;height:11px;margin:0 0 2px -8px}.PeA_7.WJyLU .FG_0Y.REzLi .IAHuG{height:14px}.grdL8:not(.X6Pln):not(.RKOIN) .FG_0Y.REzLi .IAHuG{color:var(--task-overdue-color)}.FG_0Y.o5Et0{min-width:22px}.FG_0Y.REzLi.wUa6z{margin-bottom:-5px}.RKOIN .FG_0Y.REzLi.wUa6z{font-style:italic}.U1y6M{display:flex;flex-direction:column;padding:0 16px}.QDSTv{align-content:center;border-top:1px solid #f2f2f2;display:flex;flex-direction:row;justify-content:space-between;margin:0;min-width:295px;padding:12px 0;white-space:nowrap}.QDSTv:first-child{border-top:none}body.darkMode .QDSTv{border-top:1px solid #a6a6a6}body.darkMode .QDSTv:first-child{border-top:none}.WXMgM{color:#333;font-size:14px;line-height:24px;margin-right:10px}.WXMgM.ZJLBj{cursor:default}body.darkMode .WXMgM{color:#e6e6e6}.i66jI{display:none}.QDSTv:hover .i66jI{display:block}.i66jI svg{color:#a6a6a6;height:24px;width:24px}.i66jI svgbody.darkMode .i66jI svg{color:#737373}.FG_0Y.xSWKf,.PeA_7.WJyLU .FG_0Y.xSWKf{align-items:center;display:flex;flex-direction:row;justify-content:flex-start}.FG_0Y.xSWKf svg,.FG_0Y.xSWKf svg:hover,.PeA_7.WJyLU .FG_0Y.xSWKf svg,.PeA_7.WJyLU .FG_0Y.xSWKf svg:hover{color:#0081c2;height:24px;width:24px}body.darkMode .FG_0Y.xSWKf svg,body.darkMode .FG_0Y.xSWKf svg:hover,body.darkMode .PeA_7.WJyLU .FG_0Y.xSWKf svg,body.darkMode .PeA_7.WJyLU .FG_0Y.xSWKf svg:hover{color:#00a3f4}body.ui-revamp .FG_0Y.xSWKf svg,body.ui-revamp .FG_0Y.xSWKf svg:hover,body.ui-revamp .PeA_7.WJyLU .FG_0Y.xSWKf svg,body.ui-revamp .PeA_7.WJyLU .FG_0Y.xSWKf svg:hover{color:var(--color-text-fill-brandsecondary-enabled)}.FG_0Y.xSWKf .WXMgM,.FG_0Y.xSWKf .WXMgM:hover,.PeA_7.WJyLU .FG_0Y.xSWKf .WXMgM,.PeA_7.WJyLU .FG_0Y.xSWKf .WXMgM:hover{color:#0081c2;font-size:14px;margin-left:10px}body.darkMode .FG_0Y.xSWKf .WXMgM,body.darkMode .FG_0Y.xSWKf .WXMgM:hover,body.darkMode .PeA_7.WJyLU .FG_0Y.xSWKf .WXMgM,body.darkMode .PeA_7.WJyLU .FG_0Y.xSWKf .WXMgM:hover{color:#00a3f4}body.ui-revamp .FG_0Y.xSWKf .WXMgM,body.ui-revamp .FG_0Y.xSWKf .WXMgM:hover,body.ui-revamp .PeA_7.WJyLU .FG_0Y.xSWKf .WXMgM,body.ui-revamp .PeA_7.WJyLU .FG_0Y.xSWKf .WXMgM:hover{color:var(--color-text-fill-brandsecondary-enabled)}.FG_0Y.viFwv{display:inline-block}.FG_0Y.viFwv img,.FG_0Y.viFwv svg{border-radius:1em;height:20px;width:20px}.FG_0Y.xImWB.xImWB{cursor:pointer}.VizL_.X6Pln .viFwv img,.VizL_.ZJLBj:not(.vOgKN):not(.X6Pln) .viFwv img{filter:opacity(.5)}.xV3tv{background-color:#f6ebf8;color:#a73cbd;font-size:13px!important}body.darkMode .xV3tv{background-color:#a73cbd;color:#f6ebf8}body.ui-revamp .xV3tv{background-color:var(--color-filterpill-base-fill-default);color:var(--color-text-fill-secondary-enabled);font-family:Inter,Source Sans Pro,-apple-system,system-ui,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif;font-size:14px!important;line-height:20px;padding:0 var(--spacing-0-5)}body.ui-revamp .xV3tv svg{color:var(--color-icon-fill-purple-enabled)}body.ui-revamp .xV3tv.X6Pln,body.ui-revamp .xV3tv.X6Pln svg{color:var(--color-filterpill-icons-fill-default)}.xV3tv.ci0wa{padding:5px 8px 5px 5px}.xV3tv.ci0wa.X6Pln{background-color:#f2f2f2;color:#a6a6a6}body.darkMode .xV3tv.ci0wa.X6Pln{background-color:#404040;color:#f2f2f2}body.ui-revamp .xV3tv.ci0wa{background-color:var(--color-filterpill-base-fill-task)!important;color:var(--color-text-fill-secondary-enabled)}body.ui-revamp .xV3tv.ci0wa.X6Pln{background-color:var(--color-filterpill-base-fill-default)!important;color:var(--color-filterpill-icons-fill-default)}.xV3tv .BGX9K{margin:-4px 4px 0 -4px;width:16px}.xV3tv.XB9ra{padding:5px 8px}.xV3tv .mhKrb{margin:0 2px 0 1px;width:12px}.Ppjsu{background-color:transparent;border:0;color:inherit;cursor:text;font-family:inherit;font-size:16px;left:39px;line-height:24px;opacity:.5;outline:none;padding:calc((var(--task-height) - 24px)/2) 0;position:absolute;white-space:nowrap}.hbOcq{flex:1 1 0}.hbOcq .PJ4zl{color:#e54e40;margin-left:6px;margin-top:-8px}.nkDa1{cursor:text;flex:1 1 0;font-size:16px;line-height:24px;margin-left:6px;position:relative;word-break:break-word}.nkDa1>:first-child{margin-bottom:calc((var(--task-height) - 24px)/2);margin-top:calc((var(--task-height) - 24px)/2)}body.ui-revamp .nkDa1>:first-child{line-height:1.6}.ZJLBj .nkDa1{cursor:default}.ZJLBj.vOgKN .nkDa1{cursor:pointer}.X6Pln .nkDa1{text-decoration:line-through}body.ui-revamp .nkDa1{font-size:15px}.UXHS9{align-items:center;color:#737373;display:inline-flex;flex:0 0 auto;justify-content:flex-end}body.darkMode .UXHS9{color:#a6a6a6}.XtxGO{display:none}@media only screen and (max-width:768px){body:not(.neutron) .UXHS9.LVtVQ,body:not(.neutron) .UXHS9.TMhKH{flex-basis:100%;justify-content:flex-start;margin-top:-10px}body:not(.neutron) .UXHS9.TMhKH{order:2}body:not(.neutron) .UXHS9.LjJAC{order:1}body:not(.neutron) .UXHS9.LVtVQ,body:not(.neutron) .UXHS9.TMhKH{padding-left:30px}body:not(.neutron) .UXHS9.LVtVQ .FG_0Y.Jh0Ej,body:not(.neutron) .UXHS9.LVtVQ .FG_0Y.Jh0Ej .IAHuG,body:not(.neutron) .UXHS9.LVtVQ .FG_0Y.REzLi,body:not(.neutron) .UXHS9.LVtVQ .FG_0Y.REzLi .IAHuG,body:not(.neutron) .UXHS9.TMhKH .FG_0Y.Jh0Ej,body:not(.neutron) .UXHS9.TMhKH .FG_0Y.Jh0Ej .IAHuG,body:not(.neutron) .UXHS9.TMhKH .FG_0Y.REzLi,body:not(.neutron) .UXHS9.TMhKH .FG_0Y.REzLi .IAHuG{margin-left:0}body:not(.neutron) .UXHS9.LVtVQ .FG_0Y.o5Et0,body:not(.neutron) .UXHS9.TMhKH .FG_0Y.o5Et0{margin-left:-4px}body:not(.neutron) .PeA_7.VizL_ .UXHS9.q8JTj{display:none}body:not(.neutron) .REzLi{flex-shrink:1}}body:not(.neutron) td>.taskgroup .UXHS9.LVtVQ,body:not(.neutron) td>.taskgroup .UXHS9.TMhKH,body:not(.neutron) th>.taskgroup .UXHS9.LVtVQ,body:not(.neutron) th>.taskgroup .UXHS9.TMhKH{flex-basis:100%;justify-content:flex-start;margin-top:-10px}body:not(.neutron) td>.taskgroup .UXHS9.TMhKH,body:not(.neutron) th>.taskgroup .UXHS9.TMhKH{order:2}body:not(.neutron) td>.taskgroup .UXHS9.LjJAC,body:not(.neutron) th>.taskgroup .UXHS9.LjJAC{order:1}body:not(.neutron) td>.taskgroup .UXHS9.LVtVQ,body:not(.neutron) td>.taskgroup .UXHS9.TMhKH,body:not(.neutron) th>.taskgroup .UXHS9.LVtVQ,body:not(.neutron) th>.taskgroup .UXHS9.TMhKH{padding-left:30px}body:not(.neutron) td>.taskgroup .UXHS9.LVtVQ .FG_0Y.Jh0Ej,body:not(.neutron) td>.taskgroup .UXHS9.LVtVQ .FG_0Y.Jh0Ej .IAHuG,body:not(.neutron) td>.taskgroup .UXHS9.LVtVQ .FG_0Y.REzLi,body:not(.neutron) td>.taskgroup .UXHS9.LVtVQ .FG_0Y.REzLi .IAHuG,body:not(.neutron) td>.taskgroup .UXHS9.TMhKH .FG_0Y.Jh0Ej,body:not(.neutron) td>.taskgroup .UXHS9.TMhKH .FG_0Y.Jh0Ej .IAHuG,body:not(.neutron) td>.taskgroup .UXHS9.TMhKH .FG_0Y.REzLi,body:not(.neutron) td>.taskgroup .UXHS9.TMhKH .FG_0Y.REzLi .IAHuG,body:not(.neutron) th>.taskgroup .UXHS9.LVtVQ .FG_0Y.Jh0Ej,body:not(.neutron) th>.taskgroup .UXHS9.LVtVQ .FG_0Y.Jh0Ej .IAHuG,body:not(.neutron) th>.taskgroup .UXHS9.LVtVQ .FG_0Y.REzLi,body:not(.neutron) th>.taskgroup .UXHS9.LVtVQ .FG_0Y.REzLi .IAHuG,body:not(.neutron) th>.taskgroup .UXHS9.TMhKH .FG_0Y.Jh0Ej,body:not(.neutron) th>.taskgroup .UXHS9.TMhKH .FG_0Y.Jh0Ej .IAHuG,body:not(.neutron) th>.taskgroup .UXHS9.TMhKH .FG_0Y.REzLi,body:not(.neutron) th>.taskgroup .UXHS9.TMhKH .FG_0Y.REzLi .IAHuG{margin-left:0}body:not(.neutron) td>.taskgroup .UXHS9.LVtVQ .FG_0Y.o5Et0,body:not(.neutron) td>.taskgroup .UXHS9.TMhKH .FG_0Y.o5Et0,body:not(.neutron) th>.taskgroup .UXHS9.LVtVQ .FG_0Y.o5Et0,body:not(.neutron) th>.taskgroup .UXHS9.TMhKH .FG_0Y.o5Et0{margin-left:-4px}body:not(.neutron) td>.taskgroup .PeA_7.VizL_ .UXHS9.q8JTj,body:not(.neutron) th>.taskgroup .PeA_7.VizL_ .UXHS9.q8JTj{display:none}body:not(.neutron) td>.taskgroup .REzLi,body:not(.neutron) th>.taskgroup .REzLi{flex-shrink:1}body.neutron .PeA_7{align-content:flex-start;align-items:flex-start;display:flex;flex-direction:column;outline:none}body.neutron .pumyF{color:var(--task-action-color);flex:0 0 auto;font-size:14px;margin:-5px 0 0 30px;min-height:0}body.neutron .pumyF .IAHuG{height:12px;margin:0 0 -1px 4px}body.neutron .UXHS9 .pumyF{display:inline-block}body.neutron .grdL8:not(.X6Pln):not(.RKOIN) .pumyF{color:var(--task-overdue-color)}body.neutron .iYMHh{align-items:flex-start;display:flex;flex-direction:row-reverse;justify-content:flex-end;margin:0;outline:none;width:100%}body.neutron .iYMHh>*{min-height:var(--task-height)}body.neutron .CunBS{flex-grow:1;outline:none}body.neutron .CunBS,body.neutron .b0T1g{align-items:flex-start;display:flex;flex-direction:row;justify-content:flex-start}body.neutron .b0T1g{margin-left:auto;margin-right:10px}body.neutron .nkDa1{flex-grow:1;outline:none;-webkit-user-select:text;-moz-user-select:text;user-select:text}body.neutron .Ppjsu{left:5px;outline:none;position:relative}body.neutron .xl9bu{flex-grow:0;height:var(--task-height);line-height:var(--task-height);margin:0 8.5px;vertical-align:middle}body.neutron .xl9bu svg{color:#0081c2;display:inline-block;height:14px;vertical-align:middle}body.neutron .viFwv{flex-grow:0;height:var(--task-height);line-height:var(--task-height);margin:0 5px 0 0;vertical-align:middle}body.neutron .viFwv img{border-radius:1em;height:22px;vertical-align:middle;width:22px}body.neutron .viFwv svg{height:20px;vertical-align:middle}body.neutron .L7gle{flex-grow:0;height:var(--task-height);line-height:var(--task-height);margin:0 5px 0 0}body.neutron .L7gle svg{color:#e54e40;display:inline-block;height:14px;vertical-align:middle}body.darkMode body.neutron .PeA_7.RKOIN .L7gle svg,body.darkMode body.neutron .PeA_7.RKOIN .xl9bu svg,body.darkMode body.neutron .PeA_7.X6Pln .L7gle svg,body.darkMode body.neutron .PeA_7.X6Pln .xl9bu svg,body.neutron .PeA_7.RKOIN .L7gle svg,body.neutron .PeA_7.RKOIN .xl9bu svg,body.neutron .PeA_7.X6Pln .L7gle svg,body.neutron .PeA_7.X6Pln .xl9bu svg{color:var(--task-action-color)}body.neutron .PeA_7.WJyLU{background:none;box-sizing:border-box}body.neutron .PeA_7.WJyLU .UXHS9{display:block;text-align:right;width:100%}body.neutron .PeA_7.WJyLU .pumyF,body.neutron .PeA_7.WJyLU .viFwv,body.neutron .PeA_7.WJyLU .xl9bu{display:none}body.neutron .PeA_7.WJyLU .XtxGO{display:block;margin:0 22px}body.neutron .PeA_7.WJyLU .hn24K{align-items:center;background-color:#f2f2f2;border-radius:40px;display:inline-flex;flex-direction:row;height:28px;justify-content:space-between;margin:0 4px 4px 0;padding:0 6px;vertical-align:middle;white-space:nowrap}body.neutron .PeA_7.WJyLU .hn24K .pWE0q{display:block;margin-right:4.5px}body.neutron .PeA_7.WJyLU .hn24K.nRLjt svg{height:18px;margin:0 6px 0 0}body.neutron .PeA_7.WJyLU .hn24K.nRLjt img{border-radius:1em;height:18px;margin:0 6px 0 0;width:18px}body.neutron .PeA_7.WJyLU .hn24K.nu6aF .pWE0q{color:#a73cbd;height:18px;margin-top:-1px}body.neutron .PeA_7.WJyLU .hn24K.nu6aF .KFEET{color:#333;height:13px;margin:-1px 0 0 4px}body.neutron .PeA_7.WJyLU .hn24K.OZS_n .pWE0q{color:#0081c2;height:18px}body.neutron .PeA_7.WJyLU .hn24K .HQbjI{color:#333;font-size:14px}body.neutron .PeA_7.WJyLU .hn24K ._Om_z svg{height:18px;margin-left:8px;width:18px}body.neutron .xV3tv{font-size:12px;height:18px}body.neutron .xV3tv.ci0wa{padding:0 5px 0 1px}body.neutron .xV3tv.ci0wa span{padding:0 1px 1px 2px}body.neutron .xV3tv .BGX9K{height:15px;margin-top:-3px;width:14px}body.neutron .xV3tv.XB9ra{padding:0 5px 0 4px}body.neutron .xV3tv.XB9ra span{padding:1px}body.neutron .xV3tv .mhKrb{height:11px;margin:3px -1px 0 0;width:11px}body.neutron.darkMode .PeA_7.WJyLU{border-color:transparent}body.neutron.darkMode .PeA_7.WJyLU .hn24K{background-color:#404040}body.neutron.darkMode .PeA_7.WJyLU .hn24K .HQbjI{color:#d9d9d9}body.neutron.darkMode .PeA_7.WJyLU .hn24K.OZS_n .pWE0q{color:#00a3f4}body.neutron.darkMode .PeA_7.WJyLU .hn24K.nu6aF .KFEET{color:#ccc}body.neutron.darkMode .PeA_7.WJyLU .hn24K ._Om_z{color:#262626}body.neutron.darkMode .L7gle svg{color:#e86357}body.neutron.darkMode .xl9bu svg{color:#00a3f4}.taskgroup{--drag-handle-dimension:14px}body.ui-revamp .taskgroup{margin:var(--spacing-3) var(--spacing-1) var(--spacing-1-5)}td>.taskgroup,th>.taskgroup{min-width:375px;padding-left:var(--drag-handle-dimension);padding-right:var(--drag-handle-dimension)}body.neutron td>.taskgroup,body.neutron th>.taskgroup{min-width:343px}.Km22N{height:var(--drag-handle-dimension);left:-16px;position:absolute;top:calc(var(--task-height)/2 - var(--drag-handle-dimension)/2);width:var(--drag-handle-dimension)}body.peso-dragging en-note.peso .Km22N{display:none}.X98ws{background:#f8f8f8;border:1px solid #f2f2f2;border-radius:3px;color:#666;overflow:hidden}body.darkMode .X98ws{background:#262626;border-color:#333;color:#a6a6a6}.MOd4s{fill:currentColor;align-items:center;background:#f2f2f2;display:flex;font-weight:600;padding:6px}body.darkMode .MOd4s{background:#333}.MOd4s svg{padding-right:8px;width:24px}.X98ws .p9y6r{line-height:1.3;margin:1em;padding:2px 6px}[data-task=true]:focus .h3k8E{visibility:visible}.h3k8E{position:absolute;right:2px;top:7px;visibility:hidden}.h3k8E svg{color:#a6a6a6}.h3k8E svg:focus,.h3k8E svg:hover{color:#737373}.h3k8E:focus,.h3k8E:hover{cursor:pointer;visibility:visible}.zA9bQ{border-radius:4px;min-height:24px;visibility:visible}.zA9bQ svg{color:#737373}.zA9bQ:focus,.zA9bQ:hover{background-color:#f8f8f8}body.darkMode .zA9bQ:focus,body.darkMode .zA9bQ:hover{background-color:#404040}.gry_t{align-items:center;display:flex;margin:0 4px 0 6px}.gry_t .PNboT{background-color:#d9d9d9;height:20px;width:1px}body.darkMode .gry_t .PNboT{background-color:#737373}.fvqn2{box-shadow:1px 2px 6px rgba(0,0,0,.15);font-family:-apple-system,system-ui,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif;font-size:13px;line-height:1.4;max-width:320px;opacity:0;outline:0;overflow-wrap:break-word;padding:4px 5px;position:absolute;text-align:center;transition-property:transform,visibility,opacity;white-space:normal;z-index:2147483647}body.neutron .fvqn2{font-family:Source Sans Pro,-apple-system,system-ui,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif}@media print{.fvqn2{display:none!important}}.xCDwD{--tooltip-border-color:#666;--tooltip-background:#404040;--tooltip-text:#fff;background-clip:padding-box;background-color:var(--tooltip-background);border:1px solid var(--tooltip-border-color);border-radius:6px;color:var(--tooltip-text)}body.darkMode .xCDwD{--tooltip-border-color:#737373;--tooltip-background:#ccc;--tooltip-text:#111}.psuSi{--tooltip-border-color:#66b3da;--tooltip-background:#0081c2;--tooltip-text:#fff;background-clip:padding-box;background-color:var(--tooltip-background);border:1px solid var(--tooltip-border-color);border-radius:6px;color:var(--tooltip-text)}body.darkMode .psuSi{--tooltip-border-color:#66b3da;--tooltip-background:#00a3f4;--tooltip-text:#000}body.ui-revamp .psuSi{--tooltip-border-color:var(--color-text-fill-brandsecondary-enabled);--tooltip-background:var(--color-text-fill-brandsecondary-enabled)}.psuSi>.rkEC0,.xCDwD>.rkEC0{padding:5px 9px;position:relative;z-index:1}.psuSi>.DAAYs,.xCDwD>.DAAYs{height:16px;position:absolute;width:16px}.psuSi>.DAAYs:after,.psuSi>.DAAYs:before,.xCDwD>.DAAYs:after,.xCDwD>.DAAYs:before{content:"";position:absolute;z-index:-1}.psuSi.VIjeP>.DAAYs,.xCDwD.VIjeP>.DAAYs{bottom:0;transform:translate(-1px,-1px)}.psuSi.VIjeP>.DAAYs:before,.xCDwD.VIjeP>.DAAYs:before{border-radius:6px;border-top:7px solid var(--tooltip-border-color);bottom:-7px}.psuSi.VIjeP>.DAAYs:after,.xCDwD.VIjeP>.DAAYs:after{border-radius:6px;border-top:8px solid var(--tooltip-background);top:13px}.psuSi.nHIdk>.DAAYs,.xCDwD.nHIdk>.DAAYs{top:0;transform:translate(-1px,1px)}.psuSi.nHIdk>.DAAYs:before,.xCDwD.nHIdk>.DAAYs:before{border-bottom:7px solid var(--tooltip-border-color);border-radius:6px;bottom:16px}.psuSi.nHIdk>.DAAYs:after,.xCDwD.nHIdk>.DAAYs:after{border-bottom:7px solid var(--tooltip-background);border-radius:6px;bottom:14px}.psuSi.DrJiP>.DAAYs,.xCDwD.DrJiP>.DAAYs{left:0;transform:translate(1px,-1px)}.psuSi.DrJiP>.DAAYs:before,.xCDwD.DrJiP>.DAAYs:before{border-radius:6px;border-right:7px solid var(--tooltip-border-color);right:16px}.psuSi.DrJiP>.DAAYs:after,.xCDwD.DrJiP>.DAAYs:after{border-radius:6px;border-right:7px solid var(--tooltip-background);right:14px}.psuSi.eOzz0>.DAAYs,.xCDwD.eOzz0>.DAAYs{right:0;transform:translate(-1px,-1px)}.psuSi.eOzz0>.DAAYs:before,.xCDwD.eOzz0>.DAAYs:before{border-left:7px solid var(--tooltip-border-color);border-radius:6px;left:16px}.psuSi.eOzz0>.DAAYs:after,.xCDwD.eOzz0>.DAAYs:after{border-left:7px solid var(--tooltip-background);border-radius:6px;left:14px}.psuSi.VIjeP>.DAAYs:after,.psuSi.VIjeP>.DAAYs:before,.psuSi.nHIdk>.DAAYs:after,.psuSi.nHIdk>.DAAYs:before,.xCDwD.VIjeP>.DAAYs:after,.xCDwD.VIjeP>.DAAYs:before,.xCDwD.nHIdk>.DAAYs:after,.xCDwD.nHIdk>.DAAYs:before{border-left:7px solid transparent;border-radius:6px;border-right:7px solid transparent;left:0}.psuSi.DrJiP>.DAAYs:after,.psuSi.DrJiP>.DAAYs:before,.psuSi.eOzz0>.DAAYs:after,.psuSi.eOzz0>.DAAYs:before,.xCDwD.DrJiP>.DAAYs:after,.xCDwD.DrJiP>.DAAYs:before,.xCDwD.eOzz0>.DAAYs:after,.xCDwD.eOzz0>.DAAYs:before{border-bottom:7px solid transparent;border-radius:6px;border-top:7px solid transparent;top:0}en-note.peso .zNVgd dl,en-note.peso .zNVgd li,en-note.peso .zNVgd ol,en-note.peso .zNVgd ul{margin:revert;padding:revert;position:revert}en-note.peso .zNVgd li{list-style-type:revert}.DDDuM{justify-content:flex-start}.t45vR{fill:currentColor;width:24px}.uvsth{margin-top:3px}.PpNmx{margin:0 4px 3px 10px;width:11px}.PpNmx.tYeaP{transform:rotate(180deg)}.RcxZl{align-items:center;-moz-column-gap:4px;column-gap:4px;display:grid;white-space:nowrap;width:100%}.Yjr4v{overflow:hidden;text-overflow:ellipsis}.QepkM{margin-right:20px}.hoPee{font-size:14px;font-weight:400;text-align:right}.vvEPo{max-width:760px;width:100%}.DXKCQ{-moz-user-select:none;user-select:none;-webkit-user-select:none}.lN7MT{color:#666;font-size:16px;padding:10px}body.darkMode .lN7MT{color:#a6a6a6}body.ui-revamp .lN7MT{color:var(--color-text-fill-quaternary-enabled);font-size:13px;font-weight:400;line-height:20px;padding:var(--spacing-2)}.lN7MT .TOxNt{margin-bottom:8px;min-height:29px}body.ui-revamp .lN7MT .TOxNt{display:flex;margin-bottom:var(--spacing-1-5);min-height:auto}.lN7MT .BeTHR{color:#333;font-weight:600;margin-right:4px;white-space:nowrap}body.darkMode .lN7MT .BeTHR{color:#e6e6e6}body.ui-revamp .lN7MT .BeTHR{color:var(--color-text-fill-secondary-enabled);font-size:13px;font-weight:500;line-height:20px;width:150px}.lN7MT .Io2pq{display:grid;grid-template-columns:min-content auto min-content;margin-bottom:4px}body.ui-revamp .lN7MT .Io2pq{align-items:center;display:flex;grid-template-columns:none;justify-content:flex-start}.lN7MT .YPDP8{display:grid;grid-template-columns:auto;position:relative}.lN7MT .Hz3Sd{overflow:hidden}body.ios .lN7MT .Hz3Sd,body.ipados .lN7MT .Hz3Sd{-moz-user-select:none;user-select:none;-webkit-user-select:none}.lN7MT .Hz3Sd.q_Oz9{position:absolute;visibility:hidden}.lN7MT .zrf6b{align-items:center;background-color:#f1f0f0;border-radius:20px;color:#4d4d4d;cursor:pointer;display:inline-flex;font-size:13px;height:29px;margin:0 4px 4px 0;max-width:100%;padding:6px;white-space:nowrap}body.darkMode .lN7MT .zrf6b{background-color:#333;color:#ccc}body.ui-revamp .lN7MT .zrf6b{background-color:var(--color-filter-pill-base-fill-default);border-radius:var(--radius-sm);margin:0 4px 0 0}.lN7MT .zrf6b .yE6yM{overflow:hidden;text-overflow:ellipsis}.lN7MT .zrf6b .JoHsa{--default-img-bg:#ccc;--default-img-head:#737373;background-size:cover;border-radius:50%;height:16px;margin-right:6px;min-width:16px;width:16px}body.darkMode .lN7MT .zrf6b .JoHsa{--default-img-bg:#666;--default-img-head:#393939}.lN7MT .AElaB{margin-left:5px;white-space:nowrap}.lN7MT .c9bJW{--lineHeight:1.5;-webkit-line-clamp:var(--maxLines);-webkit-box-orient:vertical;display:-webkit-box;line-height:var(--lineHeight);max-height:calc(var(--maxLines)*var(--lineHeight)*1em);overflow:hidden}.lN7MT .u_p1b{font-size:14px}body.ui-revamp .lN7MT .u_p1b{font-size:13px;margin:var(--spacing-1-5) 0;margin-top:var(--spacing-3)}.lN7MT .JS87n{color:#026fac;cursor:pointer}body.darkMode .lN7MT .JS87n{color:#00a3f4}body.ui-revamp .lN7MT .JS87n{color:var(--color-text-fill-quaternary-enabled)}.lN7MT .FYJIG{display:flex;flex-direction:row;justify-content:space-between;margin:16px 0 8px}body.neutron .lN7MT .FYJIG{flex-direction:column;margin-bottom:0}.lN7MT .GcGbV,.lN7MT .j8iyW{font-size:14px;line-height:20px}.lN7MT .j8iyW{color:#00a82d;cursor:pointer;display:flex;font-weight:600;margin:0 12px 0 auto;transition:color .15s}.lN7MT .j8iyW svg{fill:#00a82d;height:20px;margin-right:2px;transition:fill .15s;width:20px}body.neutron .lN7MT .j8iyW.kDZrj{border:1px solid #d9d9d9;border-radius:4px;color:#00a82d;justify-content:center;margin-top:16px;padding:10px 0;width:100%}body.neutron .lN7MT .j8iyW.kDZrj svg{fill:#00a82d}body.darkMode .lN7MT .j8iyW{color:#26b54c}body.darkMode .lN7MT .j8iyW svg{fill:#26b54c}body.ui-revamp .lN7MT .j8iyW{color:var(--color-text-fill-brand-secondary-enabled)}body.ui-revamp .lN7MT .j8iyW svg{fill:var(--color-text-fill-brand-secondary-enabled)}body.darkMode.neutron .lN7MT .j8iyW.kDZrj{border-color:#404040;color:#26b54c}body.darkMode.neutron .lN7MT .j8iyW.kDZrj svg{fill:#26b54c}.lN7MT .j8iyW:hover{color:#666}.lN7MT .j8iyW:hover svg{fill:#666}body.darkMode .lN7MT .j8iyW:hover{color:#fff}body.darkMode .lN7MT .j8iyW:hover svg{fill:#fff}:global(.task){--task-height:40px;border:none;max-width:760px;outline:none;position:relative}:global(.drag-image-holder) :global(.task){background-color:#fff}:global(body.darkMode) :global(.drag-image-holder) :global(.task){background-color:#333}:global(.task) button{margin:0}:global(.task.ProseMirror-selectednode),:global(.task.selected){border-color:none;outline:none}:global(.task.ProseMirror-selectednode){border-radius:6px;box-shadow:0 3px 10px rgba(0,0,0,.15)}.tasktitle{--task-action-color:rgba(0,0,0,.45);--task-overdue-color:#e54e40;align-items:flex-start;border:1px solid transparent;border-radius:6px;cursor:default;display:flex;flex-wrap:wrap;margin-bottom:2px;margin-right:30px;max-width:760px;min-height:var(--task-height);outline:none;padding:0 8px}[data-background-luminance-lightmode=light] .tasktitle{--task-action-color:rgba(0,0,0,.45)}[data-background-luminance-lightmode=dark] .tasktitle{--task-action-color:hsla(0,0%,100%,.8)}:global(body.darkMode) .tasktitle{--task-action-color:hsla(0,0%,100%,.8)}:global(body.darkMode) [data-background-luminance-darkmode=light] .tasktitle{--task-action-color:rgba(0,0,0,.45)}:global(body.darkMode) [data-background-luminance-darkmode=dark] .tasktitle{--task-action-color:hsla(0,0%,100%,.8)}:global(body.darkMode) .tasktitle{--task-overdue-color:#e54e40}[data-background-luminance-lightmode=light] .tasktitle{--task-overdue-color:#e54e40}[data-background-luminance-lightmode=dark] .tasktitle{--task-overdue-color:#f2b6b3}:global(body.darkMode) [data-background-luminance-darkmode=light] .tasktitle{--task-overdue-color:#e54e40}:global(body.darkMode) [data-background-luminance-darkmode=dark] .tasktitle{--task-overdue-color:#f2b6b3}.tasktitle.edit{background-color:#f8f8f8}[data-background-luminance-lightmode=light] .tasktitle.edit{background-color:hsla(0,0%,100%,.5)}[data-background-luminance-lightmode=dark] .tasktitle.edit{background-color:rgba(0,0,0,.2)}:global(body.darkMode) .tasktitle.edit{background-color:#333;border-color:#404040}:global(body.ui-revamp) .tasktitle.edit{background-color:var(--color-surface-fill-secondary-enabled);border-color:transparent}:global(body.darkMode.ui-revamp) .tasktitle.edit{background-color:var(--color-surface-fill-tertiary-enabled);border-color:transparent}:global(body.darkMode) [data-background-luminance-darkmode=light] .tasktitle.edit{background-color:rgba(0,0,0,.2);border-color:transparent}:global(body.darkMode) [data-background-luminance-darkmode=dark] .tasktitle.edit{background-color:rgba(0,0,0,.1);border-color:transparent}.tasktitle.remindersOpen{background-color:#f8f8f8}[data-background-luminance-lightmode=light] .tasktitle.remindersOpen{background-color:hsla(0,0%,100%,.5)}[data-background-luminance-lightmode=dark] .tasktitle.remindersOpen{background-color:rgba(0,0,0,.2)}:global(body.darkMode) .tasktitle.remindersOpen{background-color:#333;border-color:#404040}:global(body.ui-revamp) .tasktitle.remindersOpen{background-color:var(--color-surface-fill-secondary-enabled);border-color:transparent}:global(body.darkMode.ui-revamp) .tasktitle.remindersOpen{background-color:var(--color-surface-fill-tertiary-enabled);border-color:transparent}:global(body.darkMode) [data-background-luminance-darkmode=light] .tasktitle.remindersOpen{background-color:rgba(0,0,0,.2);border-color:transparent}:global(body.darkMode) [data-background-luminance-darkmode=dark] .tasktitle.remindersOpen{background-color:rgba(0,0,0,.1);border-color:transparent}@media(hover:hover){.tasktitle:hover:not(.remindersOpen){background-color:#f8f8f8}[data-background-luminance-lightmode=light] .tasktitle:hover:not(.remindersOpen){background-color:hsla(0,0%,100%,.5)}[data-background-luminance-lightmode=dark] .tasktitle:hover:not(.remindersOpen){background-color:rgba(0,0,0,.2)}:global(body.darkMode) .tasktitle:hover:not(.remindersOpen){background-color:#333;border-color:#404040}:global(body.ui-revamp) .tasktitle:hover:not(.remindersOpen){background-color:var(--color-surface-fill-secondary-enabled);border-color:transparent}:global(body.darkMode.ui-revamp) .tasktitle:hover:not(.remindersOpen){background-color:var(--color-surface-fill-tertiary-enabled);border-color:transparent}:global(body.darkMode) [data-background-luminance-darkmode=light] .tasktitle:hover:not(.remindersOpen){background-color:rgba(0,0,0,.2);border-color:transparent}:global(body.darkMode) [data-background-luminance-darkmode=dark] .tasktitle:hover:not(.remindersOpen){background-color:rgba(0,0,0,.1);border-color:transparent}}.tasktitle.readonly.assignedToUser{cursor:pointer}.tasktitle .taskactions>*,.tasktitle>*{min-height:var(--task-height)}:global(.task.ProseMirror-selectednode) .tasktitle,:global(.task.selected) .tasktitle{border-color:#66b3da;outline:none}:global(body.darkMode) :global(.task.ProseMirror-selectednode) .tasktitle,:global(body.darkMode) :global(.task.selected) .tasktitle{border-color:#00a3f4}:global(body.ui-revamp) :global(.task.ProseMirror-selectednode) .tasktitle,:global(body.ui-revamp) :global(.task.selected) .tasktitle{border-color:var(--color-text-fill-brandsecondary-enabled)}.tasktitle.completed .taskinput{color:#a6a6a6}:global(body.darkMode) .tasktitle.completed .taskinput{color:#737373}.tasktitle.obsolete .taskInputOuter{opacity:.65}.tasktitle.obsolete{font-style:italic}.taskbutton{background-color:transparent;border:none;cursor:pointer;display:inline-block;outline:none;padding:0}.readonly:not(.assignedToUser) .taskbutton,.taskbutton.taskbuttondisabled,.taskbutton:disabled{cursor:default}.readonly:not(.assignedToUser) .taskbutton svg,.readonly:not(.assignedToUser) .taskbutton svg:hover,.readonly:not(.assignedToUser) .taskbutton.taskcheckbox svg,.readonly:not(.assignedToUser) .taskbutton.taskcheckbox svg:hover,.taskbutton.taskbuttondisabled svg,.taskbutton.taskbuttondisabled svg:hover,.taskbutton.taskbuttondisabled.taskcheckbox svg,.taskbutton.taskbuttondisabled.taskcheckbox svg:hover,.taskbutton:disabled svg,.taskbutton:disabled svg:hover,.taskbutton:disabled.taskcheckbox svg,.taskbutton:disabled.taskcheckbox svg:hover{color:rgba(0,0,0,.45);pointer-events:none}[data-background-luminance-lightmode=light] .readonly:not(.assignedToUser) .taskbutton svg,[data-background-luminance-lightmode=light] .readonly:not(.assignedToUser) .taskbutton svg:hover,[data-background-luminance-lightmode=light] .readonly:not(.assignedToUser) .taskbutton.taskcheckbox svg,[data-background-luminance-lightmode=light] .readonly:not(.assignedToUser) .taskbutton.taskcheckbox svg:hover,[data-background-luminance-lightmode=light] .taskbutton.taskbuttondisabled svg,[data-background-luminance-lightmode=light] .taskbutton.taskbuttondisabled svg:hover,[data-background-luminance-lightmode=light] .taskbutton.taskbuttondisabled.taskcheckbox svg,[data-background-luminance-lightmode=light] .taskbutton.taskbuttondisabled.taskcheckbox svg:hover,[data-background-luminance-lightmode=light] .taskbutton:disabled svg,[data-background-luminance-lightmode=light] .taskbutton:disabled svg:hover,[data-background-luminance-lightmode=light] .taskbutton:disabled.taskcheckbox svg,[data-background-luminance-lightmode=light] .taskbutton:disabled.taskcheckbox svg:hover{color:rgba(0,0,0,.45)}[data-background-luminance-lightmode=dark] .readonly:not(.assignedToUser) .taskbutton svg,[data-background-luminance-lightmode=dark] .readonly:not(.assignedToUser) .taskbutton svg:hover,[data-background-luminance-lightmode=dark] .readonly:not(.assignedToUser) .taskbutton.taskcheckbox svg,[data-background-luminance-lightmode=dark] .readonly:not(.assignedToUser) .taskbutton.taskcheckbox svg:hover,[data-background-luminance-lightmode=dark] .taskbutton.taskbuttondisabled svg,[data-background-luminance-lightmode=dark] .taskbutton.taskbuttondisabled svg:hover,[data-background-luminance-lightmode=dark] .taskbutton.taskbuttondisabled.taskcheckbox svg,[data-background-luminance-lightmode=dark] .taskbutton.taskbuttondisabled.taskcheckbox svg:hover,[data-background-luminance-lightmode=dark] .taskbutton:disabled svg,[data-background-luminance-lightmode=dark] .taskbutton:disabled svg:hover,[data-background-luminance-lightmode=dark] .taskbutton:disabled.taskcheckbox svg,[data-background-luminance-lightmode=dark] .taskbutton:disabled.taskcheckbox svg:hover{color:hsla(0,0%,100%,.8)}:global(body.darkMode) .readonly:not(.assignedToUser) .taskbutton svg,:global(body.darkMode) .readonly:not(.assignedToUser) .taskbutton svg:hover,:global(body.darkMode) .readonly:not(.assignedToUser) .taskbutton.taskcheckbox svg,:global(body.darkMode) .readonly:not(.assignedToUser) .taskbutton.taskcheckbox svg:hover,:global(body.darkMode) .taskbutton.taskbuttondisabled svg,:global(body.darkMode) .taskbutton.taskbuttondisabled svg:hover,:global(body.darkMode) .taskbutton.taskbuttondisabled.taskcheckbox svg,:global(body.darkMode) .taskbutton.taskbuttondisabled.taskcheckbox svg:hover,:global(body.darkMode) .taskbutton:disabled svg,:global(body.darkMode) .taskbutton:disabled svg:hover,:global(body.darkMode) .taskbutton:disabled.taskcheckbox svg,:global(body.darkMode) .taskbutton:disabled.taskcheckbox svg:hover{color:hsla(0,0%,100%,.8)}:global(body.darkMode) [data-background-luminance-darkmode=dark] .readonly:not(.assignedToUser) .taskbutton svg,:global(body.darkMode) [data-background-luminance-darkmode=dark] .readonly:not(.assignedToUser) .taskbutton svg:hover,:global(body.darkMode) [data-background-luminance-darkmode=dark] .readonly:not(.assignedToUser) .taskbutton.taskcheckbox svg,:global(body.darkMode) [data-background-luminance-darkmode=dark] .readonly:not(.assignedToUser) .taskbutton.taskcheckbox svg:hover,:global(body.darkMode) [data-background-luminance-darkmode=dark] .taskbutton.taskbuttondisabled svg,:global(body.darkMode) [data-background-luminance-darkmode=dark] .taskbutton.taskbuttondisabled svg:hover,:global(body.darkMode) [data-background-luminance-darkmode=dark] .taskbutton.taskbuttondisabled.taskcheckbox svg,:global(body.darkMode) [data-background-luminance-darkmode=dark] .taskbutton.taskbuttondisabled.taskcheckbox svg:hover,:global(body.darkMode) [data-background-luminance-darkmode=dark] .taskbutton:disabled svg,:global(body.darkMode) [data-background-luminance-darkmode=dark] .taskbutton:disabled svg:hover,:global(body.darkMode) [data-background-luminance-darkmode=dark] .taskbutton:disabled.taskcheckbox svg,:global(body.darkMode) [data-background-luminance-darkmode=dark] .taskbutton:disabled.taskcheckbox svg:hover{color:hsla(0,0%,100%,.8)}:global(body.darkMode) [data-background-luminance-darkmode=light] .readonly:not(.assignedToUser) .taskbutton svg,:global(body.darkMode) [data-background-luminance-darkmode=light] .readonly:not(.assignedToUser) .taskbutton svg:hover,:global(body.darkMode) [data-background-luminance-darkmode=light] .readonly:not(.assignedToUser) .taskbutton.taskcheckbox svg,:global(body.darkMode) [data-background-luminance-darkmode=light] .readonly:not(.assignedToUser) .taskbutton.taskcheckbox svg:hover,:global(body.darkMode) [data-background-luminance-darkmode=light] .taskbutton.taskbuttondisabled svg,:global(body.darkMode) [data-background-luminance-darkmode=light] .taskbutton.taskbuttondisabled svg:hover,:global(body.darkMode) [data-background-luminance-darkmode=light] .taskbutton.taskbuttondisabled.taskcheckbox svg,:global(body.darkMode) [data-background-luminance-darkmode=light] .taskbutton.taskbuttondisabled.taskcheckbox svg:hover,:global(body.darkMode) [data-background-luminance-darkmode=light] .taskbutton:disabled svg,:global(body.darkMode) [data-background-luminance-darkmode=light] .taskbutton:disabled svg:hover,:global(body.darkMode) [data-background-luminance-darkmode=light] .taskbutton:disabled.taskcheckbox svg,:global(body.darkMode) [data-background-luminance-darkmode=light] .taskbutton:disabled.taskcheckbox svg:hover{color:rgba(0,0,0,.45)}:global(body.ui-revamp) .taskbutton{width:-moz-max-content;width:max-content}.readonly.assignedToUser .taskbutton{cursor:pointer}.taskbutton svg{border-radius:4px;color:var(--task-action-color);display:inline-block;height:24px;max-width:24px;vertical-align:middle}.taskactions.taskediting .taskbutton svg:hover:not(.recurringtask){background-color:#e6e6e6}[data-background-luminance-lightmode=dark] .taskactions.taskediting .taskbutton svg:hover:not(.recurringtask){background-color:#404040}:global(body.darkMode) .taskactions.taskediting .taskbutton svg:hover:not(.recurringtask){background-color:#404040}:global(body.darkMode) [data-background-luminance-darkmode=light] .taskactions.taskediting .taskbutton svg:hover:not(.recurringtask){background-color:#e6e6e6}.taskactions .taskbutton{display:none;margin:0 3px}.tasktitle.edit .taskactions .taskbutton{display:inline-block}.taskactions .taskbutton svg{color:#737373;height:14px}:global(body.darkMode) .taskactions .taskbutton svg{color:#a6a6a6}.tasktitle.edit .taskactions .taskbutton svg{height:24px}.hidden{visibility:hidden}.taskHoverActions{align-items:center;background:#fff;border-radius:8px;box-shadow:1px 2px 6px rgba(0,0,0,.15);color:#737373;display:inline-flex;flex:0 0 auto;font-size:14px;justify-content:flex-end;min-height:0;min-width:32px;padding:4px 7px;position:absolute;right:38px;top:-16px}:global(body.darkMode) .taskHoverActions{color:#a6a6a6}:global(body.darkMode) .taskHoverActions{background-color:#262626}.taskHoverActions button:first-of-type{margin:0 3px 0 0}.taskHoverActions button:last-of-type{margin:0 0 0 3px}.taskHoverActions .taskbutton{margin:0 3px}.taskHoverActions .taskbutton svg{color:#737373}:global(body.darkMode) .taskHoverActions .taskbutton svg{color:#a6a6a6}.taskHoverActions .taskduedatetextbutton{padding:0 0 0 3px}.taskHoverActions .taskbutton.taskduedatetextbutton svg.taskduetz{color:inherit}.taskHoverActions .taskbutton.taskduedatetextbutton.dateWithTz+.taskbutton.taskduedatetextbutton.taskrecurrenceindicator svg.recurringtask{height:12px;top:-1px}.taskHoverActions .taskbutton.taskduedatetextbutton.taskrecurrenceindicator{margin-right:-5px}.taskHoverActions .taskbutton.taskduedatetextbutton.taskrecurrenceindicator>svg{height:15px}.taskHoverActions .taskbutton.taskduedatetextbutton.taskrecurrenceindicator svg.recurringtask{height:12px;position:relative;top:-2px}.taskHoverActions .taskbutton.taskflag.flagged svg{color:#e54e40}.taskHoverActions .taskbutton.taskreminderbutton.hasreminders svg{color:#0081c2}.taskHoverActions .taskbutton.taskassign{position:relative}.taskHoverActions .taskbutton.taskassigned{position:relative;top:2px}.taskHoverActions .taskbutton.taskassign:hover,.taskHoverActions .taskbutton.taskassigned:hover,.taskHoverActions .taskbutton.taskduedatebutton:hover,.taskHoverActions .taskbutton.taskflag:hover,.taskHoverActions .taskbutton.taskreminderbutton:hover{background-color:#f8f8f8}:global(body.darkMode) .taskHoverActions .taskbutton.taskassign:hover,:global(body.darkMode) .taskHoverActions .taskbutton.taskassigned:hover,:global(body.darkMode) .taskHoverActions .taskbutton.taskduedatebutton:hover,:global(body.darkMode) .taskHoverActions .taskbutton.taskflag:hover,:global(body.darkMode) .taskHoverActions .taskbutton.taskreminderbutton:hover{background-color:#333}.taskHoverActions .taskactionseparatorbox .taskactionseparator{background-color:#d9d9d9}:global(body.darkMode) .taskHoverActions .taskactionseparatorbox .taskactionseparator{background-color:#737373}.tasktitle.completed .taskHoverActions,.tasktitle.completed .taskHoverActions .taskbutton svg,.tasktitle.completed .taskHoverActions .taskbutton.taskflag.flagged svg,.tasktitle.completed .taskHoverActions .taskbutton.taskreminderbutton.hasreminders svg,.tasktitle.completed .taskactions,.tasktitle.completed .taskactions .taskbutton svg,.tasktitle.completed .taskactions .taskbutton.taskflag.flagged svg,.tasktitle.completed .taskactions .taskbutton.taskreminderbutton.hasreminders svg{color:#a6a6a6}:global(body.darkMode) .tasktitle.completed .taskHoverActions,:global(body.darkMode) .tasktitle.completed .taskHoverActions .taskbutton svg,:global(body.darkMode) .tasktitle.completed .taskHoverActions .taskbutton.taskflag.flagged svg,:global(body.darkMode) .tasktitle.completed .taskHoverActions .taskbutton.taskreminderbutton.hasreminders svg,:global(body.darkMode) .tasktitle.completed .taskactions,:global(body.darkMode) .tasktitle.completed .taskactions .taskbutton svg,:global(body.darkMode) .tasktitle.completed .taskactions .taskbutton.taskflag.flagged svg,:global(body.darkMode) .tasktitle.completed .taskactions .taskbutton.taskreminderbutton.hasreminders svg{color:#737373}.taskbutton.taskcheckbox{flex:0 0 24px}.taskbutton.taskcheckbox svg{color:#a73cbd;vertical-align:text-top}:global(body.darkMode) .taskbutton.taskcheckbox svg{color:#a73cbd}:global(body.ui-revamp) .taskbutton.taskcheckbox svg{color:var(--color-icon-fill-purple-enabled)}[data-background-luminance-lightmode=light] .taskbutton.taskcheckbox svg{--fill-opacity:0.85;--fill-color:#fff;--stroke-opacity:0.85;--stroke-color:#fff}[data-background-luminance-lightmode=dark] .taskbutton.taskcheckbox svg{--fill-opacity:0.85;--fill-color:#fff;--stroke-opacity:0.2;--stroke-color:#000}:global(body.darkMode) [data-background-luminance-darkmode=light] .taskbutton.taskcheckbox svg{--fill-opacity:0.85;--fill-color:#262626;--stroke-opacity:0.2;--stroke-color:#000}:global(body.darkMode) [data-background-luminance-darkmode=dark] .taskbutton.taskcheckbox svg{--fill-opacity:0.85;--fill-color:#262626;--stroke-opacity:0.2;--stroke-color:#000}.tasktitle.completed .taskbutton.taskcheckbox svg,.tasktitle.obsolete .taskbutton.taskcheckbox svg{color:#a6a6a6}:global(body.darkMode) .tasktitle.completed .taskbutton.taskcheckbox svg,:global(body.darkMode) .tasktitle.obsolete .taskbutton.taskcheckbox svg{color:#737373}:global(body.ui-revamp) .tasktitle.completed .taskbutton.taskcheckbox svg,:global(body.ui-revamp) .tasktitle.obsolete .taskbutton.taskcheckbox svg{color:var(--color-icon-fill-purple-enabled)}.taskbutton.taskcheckbox.obsolete svg,.taskbutton.taskcheckbox.obsolete svg:hover{--obsolete-stroke:#fff;--obsolete-fill:#f0a00d}:global(body.darkMode) .taskbutton.taskcheckbox.obsolete svg,:global(body.darkMode) .taskbutton.taskcheckbox.obsolete svg:hover{--obsolete-stroke:#000;--obsolete-fill:#db8f00}.tasksuggestionpills{align-items:center;display:flex;margin-right:4px}.taskactions .tasksuggestionpill{align-items:center;background-color:#fff;border:1px dashed #ccc;border-radius:40px;color:#737373;cursor:pointer;display:flex;font-size:14px;font-weight:400;height:26px;margin:0 4px;padding:0 10px 0 8px}:global(body.darkMode) .taskactions .tasksuggestionpill{background-color:#1a1a1a;border:1px dashed #404040;color:#737373}.taskactions .tasksuggestionpill:focus,.taskactions .tasksuggestionpill:hover{background-color:#f2f2f2;border-color:#a6a6a6;color:#666}:global(body.darkMode) .taskactions .tasksuggestionpill:focus,:global(body.darkMode) .taskactions .tasksuggestionpill:hover{background-color:#1a1a1a;border-color:#666;color:#a6a6a6}.taskactions .tasksuggestionpill:focus svg,.taskactions .tasksuggestionpill:hover svg{color:#666}:global(body.darkMode) .taskactions .tasksuggestionpill:focus svg,:global(body.darkMode) .taskactions .tasksuggestionpill:hover svg{color:#737373}.taskactions .taskbutton.taskflag.flagged{display:inline-block}.taskactions .taskbutton.taskflag.flagged svg{color:#e54e40}:global(body.darkMode) .taskactions .taskbutton.taskflag.flagged svg{color:#e86357}.tasktitle.completed:not(.edit) .taskactions .taskbutton.taskflag.flagged svg,.tasktitle.obsolete .taskactions .taskbutton.taskflag.flagged svg{color:#a6a6a6}:global(body.darkMode) .tasktitle.completed:not(.edit) .taskactions .taskbutton.taskflag.flagged svg,:global(body.darkMode) .tasktitle.obsolete .taskactions .taskbutton.taskflag.flagged svg{color:#737373}.taskactions .taskbutton.taskreminderbutton.hasreminders{display:inline-block}.taskactions .taskbutton.taskreminderbutton.hasreminders svg{color:#0081c2}:global(body.darkMode) .taskactions .taskbutton.taskreminderbutton.hasreminders svg{color:#00a3f4}:global(body.ui-revamp) .taskactions .taskbutton.taskreminderbutton.hasreminders svg{color:var(--color-text-fill-brandsecondary-enabled)}.tasktitle.completed:not(.edit) .taskactions .taskbutton.taskreminderbutton.hasreminders svg,.tasktitle.obsolete .taskactions .taskbutton.taskreminderbutton.hasreminders svg{color:#a6a6a6}:global(body.darkMode) .tasktitle.completed:not(.edit) .taskactions .taskbutton.taskreminderbutton.hasreminders svg,:global(body.darkMode) .tasktitle.obsolete .taskactions .taskbutton.taskreminderbutton.hasreminders svg{color:#737373}.taskbutton.taskduedatetextbutton{color:inherit;font-size:14px;margin-bottom:-3px;margin-right:2px}.taskactions .taskbutton.taskduedatetextbutton{display:inline-block}.overdue:not(.completed):not(.obsolete) .taskbutton.taskduedatetextbutton .taskduedatetext{color:var(--task-overdue-color)}.obsolete .taskbutton.taskduedatetextbutton .taskduedatetext{font-style:italic}.taskbutton.taskduedatetextbutton .taskduetz{color:inherit}.overdue:not(.completed):not(.obsolete) .taskbutton.taskduedatetextbutton .taskduetz{color:var(--task-overdue-color)}.taskbutton.taskduedatetextbutton svg.taskduetz{height:24px;margin:-3px 0 0 4px}.overdue:not(.completed):not(.obsolete) .taskbutton.taskduedatetextbutton svg.taskduetz{color:var(--task-overdue-color)}.taskbutton.taskduedatetextbutton .recurringtask{color:inherit;height:11px;margin:0 0 2px -8px}.tasktitle.edit .taskbutton.taskduedatetextbutton .recurringtask{height:14px}.overdue:not(.completed):not(.obsolete) .taskbutton.taskduedatetextbutton .recurringtask{color:var(--task-overdue-color)}.taskbutton.taskrecurrenceindicator{min-width:22px}.taskbutton.taskduedatetextbutton.dateWithTz{margin-bottom:-5px}.obsolete .taskbutton.taskduedatetextbutton.dateWithTz{font-style:italic}.taskremindermenu{display:flex;flex-direction:column;padding:0 16px}.taskremindermenuitem{align-content:center;border-top:1px solid #f2f2f2;display:flex;flex-direction:row;justify-content:space-between;margin:0;min-width:295px;padding:12px 0;white-space:nowrap}.taskremindermenuitem:first-child{border-top:none}:global(body.darkMode) .taskremindermenuitem{border-top:1px solid #a6a6a6}:global(body.darkMode) .taskremindermenuitem:first-child{border-top:none}.taskremindermenutext{color:#333;font-size:14px;line-height:24px;margin-right:10px}.taskremindermenutext.readonly{cursor:default}:global(body.darkMode) .taskremindermenutext{color:#e6e6e6}.taskbuttonreminderlistremove{display:none}.taskremindermenuitem:hover .taskbuttonreminderlistremove{display:block}.taskbuttonreminderlistremove svg{color:#a6a6a6;height:24px;width:24px}.taskbuttonreminderlistremove svg:global(body.darkMode) .taskbuttonreminderlistremove svg{color:#737373}.taskbutton.taskbuttonreminderlistadd,.tasktitle.edit .taskbutton.taskbuttonreminderlistadd{align-items:center;display:flex;flex-direction:row;justify-content:flex-start}.taskbutton.taskbuttonreminderlistadd svg,.taskbutton.taskbuttonreminderlistadd svg:hover,.tasktitle.edit .taskbutton.taskbuttonreminderlistadd svg,.tasktitle.edit .taskbutton.taskbuttonreminderlistadd svg:hover{color:#0081c2;height:24px;width:24px}:global(body.darkMode) .taskbutton.taskbuttonreminderlistadd svg,:global(body.darkMode) .taskbutton.taskbuttonreminderlistadd svg:hover,:global(body.darkMode) .tasktitle.edit .taskbutton.taskbuttonreminderlistadd svg,:global(body.darkMode) .tasktitle.edit .taskbutton.taskbuttonreminderlistadd svg:hover{color:#00a3f4}:global(body.ui-revamp) .taskbutton.taskbuttonreminderlistadd svg,:global(body.ui-revamp) .taskbutton.taskbuttonreminderlistadd svg:hover,:global(body.ui-revamp) .tasktitle.edit .taskbutton.taskbuttonreminderlistadd svg,:global(body.ui-revamp) .tasktitle.edit .taskbutton.taskbuttonreminderlistadd svg:hover{color:var(--color-text-fill-brandsecondary-enabled)}.taskbutton.taskbuttonreminderlistadd .taskremindermenutext,.taskbutton.taskbuttonreminderlistadd .taskremindermenutext:hover,.tasktitle.edit .taskbutton.taskbuttonreminderlistadd .taskremindermenutext,.tasktitle.edit .taskbutton.taskbuttonreminderlistadd .taskremindermenutext:hover{color:#0081c2;font-size:14px;margin-left:10px}:global(body.darkMode) .taskbutton.taskbuttonreminderlistadd .taskremindermenutext,:global(body.darkMode) .taskbutton.taskbuttonreminderlistadd .taskremindermenutext:hover,:global(body.darkMode) .tasktitle.edit .taskbutton.taskbuttonreminderlistadd .taskremindermenutext,:global(body.darkMode) .tasktitle.edit .taskbutton.taskbuttonreminderlistadd .taskremindermenutext:hover{color:#00a3f4}:global(body.ui-revamp) .taskbutton.taskbuttonreminderlistadd .taskremindermenutext,:global(body.ui-revamp) .taskbutton.taskbuttonreminderlistadd .taskremindermenutext:hover,:global(body.ui-revamp) .tasktitle.edit .taskbutton.taskbuttonreminderlistadd .taskremindermenutext,:global(body.ui-revamp) .tasktitle.edit .taskbutton.taskbuttonreminderlistadd .taskremindermenutext:hover{color:var(--color-text-fill-brandsecondary-enabled)}.taskbutton.taskassigned{display:inline-block}.taskbutton.taskassigned img,.taskbutton.taskassigned svg{border-radius:1em;height:20px;width:20px}.taskbutton.isobsolete.isobsolete{cursor:pointer}.notediting.completed .taskassigned img,.notediting.readonly:not(.assignedToUser):not(.completed) .taskassigned img{filter:opacity(.5)}.headerIndicator{background-color:#f6ebf8;color:#a73cbd;font-size:13px!important}:global(body.darkMode) .headerIndicator{background-color:#a73cbd;color:#f6ebf8}:global(body.ui-revamp) .headerIndicator{background-color:var(--color-filterpill-base-fill-default);color:var(--color-text-fill-secondary-enabled);font-family:Inter,Source Sans Pro,-apple-system,system-ui,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif;font-size:14px!important;line-height:20px;padding:0 var(--spacing-0-5)}:global(body.ui-revamp) .headerIndicator svg{color:var(--color-icon-fill-purple-enabled)}:global(body.ui-revamp) .headerIndicator.completed{color:var(--color-filterpill-icons-fill-default)}:global(body.ui-revamp) .headerIndicator.completed svg{color:var(--color-filterpill-icons-fill-default)}.headerIndicator.countIndicator{padding:5px 8px 5px 5px}.headerIndicator.countIndicator.completed{background-color:#f2f2f2;color:#a6a6a6}:global(body.darkMode) .headerIndicator.countIndicator.completed{background-color:#404040;color:#f2f2f2}:global(body.ui-revamp) .headerIndicator.countIndicator{background-color:var(--color-filterpill-base-fill-task)!important;color:var(--color-text-fill-secondary-enabled)}:global(body.ui-revamp) .headerIndicator.countIndicator.completed{background-color:var(--color-filterpill-base-fill-default)!important;color:var(--color-filterpill-icons-fill-default)}.headerIndicator .countIcon{margin:-4px 4px 0 -4px;width:16px}.headerIndicator.defaultIndicator{padding:5px 8px}.headerIndicator .defaultIcon{margin:0 2px 0 1px;width:12px}.taskplaceholdertext{background-color:transparent;border:0;color:inherit;cursor:text;font-family:inherit;font-size:16px;left:39px;line-height:24px;opacity:.5;outline:none;padding:calc((var(--task-height) - 24px)/2) 0;position:absolute;white-space:nowrap}.taskInputOuter{flex:1 1 0}.taskInputOuter .charLimitError{color:#e54e40;margin-left:6px;margin-top:-8px}.taskinput{cursor:text;flex:1 1 0;font-size:16px;line-height:24px;margin-left:6px;position:relative;word-break:break-word}.taskinput>:first-child{margin-bottom:calc((var(--task-height) - 24px)/2);margin-top:calc((var(--task-height) - 24px)/2)}:global(body.ui-revamp) .taskinput>:first-child{line-height:1.6}.readonly .taskinput{cursor:default}.readonly.assignedToUser .taskinput{cursor:pointer}.completed .taskinput{text-decoration:line-through}:global(body.ui-revamp) .taskinput{font-size:15px}.taskactions{align-items:center;color:#737373;display:inline-flex;flex:0 0 auto;justify-content:flex-end}:global(body.darkMode) .taskactions{color:#a6a6a6}.taskpillbox{display:none}@media only screen and (max-width:768px){:global(body:not(.neutron)) .taskactions.datedisplay,:global(body:not(.neutron)) .taskactions.taskediting{flex-basis:100%;justify-content:flex-start;margin-top:-10px}:global(body:not(.neutron)) .taskactions.datedisplay{order:2}:global(body:not(.neutron)) .taskactions.otherdisplay{order:1}:global(body:not(.neutron)) .taskactions.datedisplay,:global(body:not(.neutron)) .taskactions.taskediting{padding-left:30px}:global(body:not(.neutron)) .taskactions.datedisplay .taskbutton.taskduedatebutton,:global(body:not(.neutron)) .taskactions.datedisplay .taskbutton.taskduedatetextbutton,:global(body:not(.neutron)) .taskactions.taskediting .taskbutton.taskduedatebutton,:global(body:not(.neutron)) .taskactions.taskediting .taskbutton.taskduedatetextbutton{margin-left:0}:global(body:not(.neutron)) .taskactions.datedisplay .taskbutton.taskduedatebutton .recurringtask,:global(body:not(.neutron)) .taskactions.datedisplay .taskbutton.taskduedatetextbutton .recurringtask,:global(body:not(.neutron)) .taskactions.taskediting .taskbutton.taskduedatebutton .recurringtask,:global(body:not(.neutron)) .taskactions.taskediting .taskbutton.taskduedatetextbutton .recurringtask{margin-left:0}:global(body:not(.neutron)) .taskactions.datedisplay .taskbutton.taskrecurrenceindicator,:global(body:not(.neutron)) .taskactions.taskediting .taskbutton.taskrecurrenceindicator{margin-left:-4px}:global(body:not(.neutron)) .tasktitle.notediting .taskactions.tasksnometadata{display:none}:global(body:not(.neutron)) .taskduedatetextbutton{flex-shrink:1}}:global(body:not(.neutron)) td>:global(.taskgroup) .taskactions.datedisplay,:global(body:not(.neutron)) td>:global(.taskgroup) .taskactions.taskediting,:global(body:not(.neutron)) th>:global(.taskgroup) .taskactions.datedisplay,:global(body:not(.neutron)) th>:global(.taskgroup) .taskactions.taskediting{flex-basis:100%;justify-content:flex-start;margin-top:-10px}:global(body:not(.neutron)) td>:global(.taskgroup) .taskactions.datedisplay,:global(body:not(.neutron)) th>:global(.taskgroup) .taskactions.datedisplay{order:2}:global(body:not(.neutron)) td>:global(.taskgroup) .taskactions.otherdisplay,:global(body:not(.neutron)) th>:global(.taskgroup) .taskactions.otherdisplay{order:1}:global(body:not(.neutron)) td>:global(.taskgroup) .taskactions.datedisplay,:global(body:not(.neutron)) td>:global(.taskgroup) .taskactions.taskediting,:global(body:not(.neutron)) th>:global(.taskgroup) .taskactions.datedisplay,:global(body:not(.neutron)) th>:global(.taskgroup) .taskactions.taskediting{padding-left:30px}:global(body:not(.neutron)) td>:global(.taskgroup) .taskactions.datedisplay .taskbutton.taskduedatebutton,:global(body:not(.neutron)) td>:global(.taskgroup) .taskactions.datedisplay .taskbutton.taskduedatetextbutton,:global(body:not(.neutron)) td>:global(.taskgroup) .taskactions.taskediting .taskbutton.taskduedatebutton,:global(body:not(.neutron)) td>:global(.taskgroup) .taskactions.taskediting .taskbutton.taskduedatetextbutton,:global(body:not(.neutron)) th>:global(.taskgroup) .taskactions.datedisplay .taskbutton.taskduedatebutton,:global(body:not(.neutron)) th>:global(.taskgroup) .taskactions.datedisplay .taskbutton.taskduedatetextbutton,:global(body:not(.neutron)) th>:global(.taskgroup) .taskactions.taskediting .taskbutton.taskduedatebutton,:global(body:not(.neutron)) th>:global(.taskgroup) .taskactions.taskediting .taskbutton.taskduedatetextbutton{margin-left:0}:global(body:not(.neutron)) td>:global(.taskgroup) .taskactions.datedisplay .taskbutton.taskduedatebutton .recurringtask,:global(body:not(.neutron)) td>:global(.taskgroup) .taskactions.datedisplay .taskbutton.taskduedatetextbutton .recurringtask,:global(body:not(.neutron)) td>:global(.taskgroup) .taskactions.taskediting .taskbutton.taskduedatebutton .recurringtask,:global(body:not(.neutron)) td>:global(.taskgroup) .taskactions.taskediting .taskbutton.taskduedatetextbutton .recurringtask,:global(body:not(.neutron)) th>:global(.taskgroup) .taskactions.datedisplay .taskbutton.taskduedatebutton .recurringtask,:global(body:not(.neutron)) th>:global(.taskgroup) .taskactions.datedisplay .taskbutton.taskduedatetextbutton .recurringtask,:global(body:not(.neutron)) th>:global(.taskgroup) .taskactions.taskediting .taskbutton.taskduedatebutton .recurringtask,:global(body:not(.neutron)) th>:global(.taskgroup) .taskactions.taskediting .taskbutton.taskduedatetextbutton .recurringtask{margin-left:0}:global(body:not(.neutron)) td>:global(.taskgroup) .taskactions.datedisplay .taskbutton.taskrecurrenceindicator,:global(body:not(.neutron)) td>:global(.taskgroup) .taskactions.taskediting .taskbutton.taskrecurrenceindicator,:global(body:not(.neutron)) th>:global(.taskgroup) .taskactions.datedisplay .taskbutton.taskrecurrenceindicator,:global(body:not(.neutron)) th>:global(.taskgroup) .taskactions.taskediting .taskbutton.taskrecurrenceindicator{margin-left:-4px}:global(body:not(.neutron)) td>:global(.taskgroup) .tasktitle.notediting .taskactions.tasksnometadata,:global(body:not(.neutron)) th>:global(.taskgroup) .tasktitle.notediting .taskactions.tasksnometadata{display:none}:global(body:not(.neutron)) td>:global(.taskgroup) .taskduedatetextbutton,:global(body:not(.neutron)) th>:global(.taskgroup) .taskduedatetextbutton{flex-shrink:1}:global(body.neutron) .tasktitle{align-content:flex-start;align-items:flex-start;display:flex;flex-direction:column;outline:none}:global(body.neutron) .taskdue{color:var(--task-action-color);flex:0 0 auto;font-size:14px;margin:-5px 0 0 30px;min-height:0}:global(body.neutron) .taskdue .recurringtask{height:12px;margin:0 0 -1px 4px}:global(body.neutron) .taskactions .taskdue{display:inline-block}:global(body.neutron) .overdue:not(.completed):not(.obsolete) .taskdue{color:var(--task-overdue-color)}:global(body.neutron) .taskinputrow{align-items:flex-start;display:flex;flex-direction:row-reverse;justify-content:flex-end;margin:0;outline:none;width:100%}:global(body.neutron) .taskinputrow>*{min-height:var(--task-height)}:global(body.neutron) .taskinputinnercontainer{align-items:flex-start;display:flex;flex-direction:row;flex-grow:1;justify-content:flex-start;outline:none}:global(body.neutron) .taskinputnopills{align-items:flex-start;display:flex;flex-direction:row;justify-content:flex-start;margin-left:auto;margin-right:10px}:global(body.neutron) .taskinput{flex-grow:1;outline:none;-webkit-user-select:text;-moz-user-select:text;user-select:text}:global(body.neutron) .taskplaceholdertext{left:5px;outline:none;position:relative}:global(body.neutron) .taskreminderindicator{flex-grow:0;height:var(--task-height);line-height:var(--task-height);margin:0 8.5px;vertical-align:middle}:global(body.neutron) .taskreminderindicator svg{color:#0081c2;display:inline-block;height:14px;vertical-align:middle}:global(body.neutron) .taskassigned{flex-grow:0;height:var(--task-height);line-height:var(--task-height);margin:0 5px 0 0;vertical-align:middle}:global(body.neutron) .taskassigned img{border-radius:1em;height:22px;vertical-align:middle;width:22px}:global(body.neutron) .taskassigned svg{height:20px;vertical-align:middle}:global(body.neutron) .taskflagindicator{flex-grow:0;height:var(--task-height);line-height:var(--task-height);margin:0 5px 0 0}:global(body.neutron) .taskflagindicator svg{color:#e54e40;display:inline-block;height:14px;vertical-align:middle}:global(body.neutron) .tasktitle.completed .taskflagindicator svg,:global(body.neutron) .tasktitle.completed .taskreminderindicator svg,:global(body.neutron) .tasktitle.obsolete .taskflagindicator svg,:global(body.neutron) .tasktitle.obsolete .taskreminderindicator svg{color:var(--task-action-color)}:global(body.darkMode) :global(body.neutron) .tasktitle.completed .taskflagindicator svg,:global(body.darkMode) :global(body.neutron) .tasktitle.completed .taskreminderindicator svg,:global(body.darkMode) :global(body.neutron) .tasktitle.obsolete .taskflagindicator svg,:global(body.darkMode) :global(body.neutron) .tasktitle.obsolete .taskreminderindicator svg{color:var(--task-action-color)}:global(body.neutron) .tasktitle.edit{background:none;box-sizing:border-box}:global(body.neutron) .tasktitle.edit .taskactions{display:block;text-align:right;width:100%}:global(body.neutron) .tasktitle.edit .taskassigned,:global(body.neutron) .tasktitle.edit .taskreminderindicator{display:none}:global(body.neutron) .tasktitle.edit .taskdue{display:none}:global(body.neutron) .tasktitle.edit .taskpillbox{display:block;margin:0 22px}:global(body.neutron) .tasktitle.edit .taskpill{align-items:center;background-color:#f2f2f2;border-radius:40px;display:inline-flex;flex-direction:row;height:28px;justify-content:space-between;margin:0 4px 4px 0;padding:0 6px;vertical-align:middle;white-space:nowrap}:global(body.neutron) .tasktitle.edit .taskpill .taskpillicon{display:block;margin-right:4.5px}:global(body.neutron) .tasktitle.edit .taskpill.taskpillassignee svg{height:18px;margin:0 6px 0 0}:global(body.neutron) .tasktitle.edit .taskpill.taskpillassignee img{border-radius:1em;height:18px;margin:0 6px 0 0;width:18px}:global(body.neutron) .tasktitle.edit .taskpill.taskpillduedate .taskpillicon{color:#a73cbd;height:18px;margin-top:-1px}:global(body.neutron) .tasktitle.edit .taskpill.taskpillduedate .taskpillextraicon{color:#333;height:13px;margin:-1px 0 0 4px}:global(body.neutron) .tasktitle.edit .taskpill.taskpillreminder .taskpillicon{color:#0081c2;height:18px}:global(body.neutron) .tasktitle.edit .taskpill .taskpilltext{color:#333;font-size:14px}:global(body.neutron) .tasktitle.edit .taskpill .taskpillclosebutton svg{height:18px;margin-left:8px;width:18px}:global(body.neutron) .headerIndicator{font-size:12px;height:18px}:global(body.neutron) .headerIndicator.countIndicator{padding:0 5px 0 1px}:global(body.neutron) .headerIndicator.countIndicator span{padding:0 1px 1px 2px}:global(body.neutron) .headerIndicator .countIcon{height:15px;margin-top:-3px;width:14px}:global(body.neutron) .headerIndicator.defaultIndicator{padding:0 5px 0 4px}:global(body.neutron) .headerIndicator.defaultIndicator span{padding:1px}:global(body.neutron) .headerIndicator .defaultIcon{height:11px;margin:3px -1px 0 0;width:11px}:global(body.neutron.darkMode) .tasktitle.edit{border-color:transparent}:global(body.neutron.darkMode) .tasktitle.edit .taskpill{background-color:#404040}:global(body.neutron.darkMode) .tasktitle.edit .taskpill .taskpilltext{color:#d9d9d9}:global(body.neutron.darkMode) .tasktitle.edit .taskpill.taskpillreminder .taskpillicon{color:#00a3f4}:global(body.neutron.darkMode) .tasktitle.edit .taskpill.taskpillduedate .taskpillextraicon{color:#ccc}:global(body.neutron.darkMode) .tasktitle.edit .taskpill .taskpillclosebutton{color:#262626}:global(body.neutron.darkMode) .taskflagindicator svg{color:#e86357}:global(body.neutron.darkMode) .taskreminderindicator svg{color:#00a3f4}:global(.taskgroup){--drag-handle-dimension:14px}:global(body.ui-revamp) :global(.taskgroup){margin:var(--spacing-3) var(--spacing-1) var(--spacing-1-5)}td>:global(.taskgroup),th>:global(.taskgroup){min-width:375px;padding-left:var(--drag-handle-dimension);padding-right:var(--drag-handle-dimension)}:global(body.neutron) td>:global(.taskgroup),:global(body.neutron) th>:global(.taskgroup){min-width:343px}.task-drag-handle{height:var(--drag-handle-dimension);left:-16px;position:absolute;top:calc(var(--task-height)/2 - var(--drag-handle-dimension)/2);width:var(--drag-handle-dimension)}:global(body.peso-dragging en-note.peso) .task-drag-handle{display:none}.taskdisabled{background:#f8f8f8;border:1px solid #f2f2f2;border-radius:3px;color:#666;overflow:hidden}:global(body.darkMode) .taskdisabled{background:#262626;border-color:#333;color:#a6a6a6}.taskdisabledheader{fill:currentColor;align-items:center;background:#f2f2f2;display:flex;font-weight:600;padding:6px}:global(body.darkMode) .taskdisabledheader{background:#333}.taskdisabledheader svg{padding-right:8px;width:24px}.taskdisabled .taskdisabledbody{line-height:1.3;margin:1em;padding:2px 6px}[data-task=true]:focus .taskdeleteicon{visibility:visible}.taskdeleteicon{position:absolute;right:2px;top:7px;visibility:hidden}.taskdeleteicon svg{color:#a6a6a6}.taskdeleteicon svg:focus,.taskdeleteicon svg:hover{color:#737373}.taskdeleteicon:focus,.taskdeleteicon:hover{cursor:pointer;visibility:visible}.taskdeleteiconedit{border-radius:4px;min-height:24px;visibility:visible}.taskdeleteiconedit svg{color:#737373}.taskdeleteiconedit:focus,.taskdeleteiconedit:hover{background-color:#f8f8f8}:global(body.darkMode) .taskdeleteiconedit:focus,:global(body.darkMode) .taskdeleteiconedit:hover{background-color:#404040}.taskactionseparatorbox{align-items:center;display:flex;margin:0 4px 0 6px}.taskactionseparatorbox .taskactionseparator{background-color:#d9d9d9;height:20px;width:1px}:global(body.darkMode) .taskactionseparatorbox .taskactionseparator{background-color:#737373}.t55Jt{box-sizing:initial;display:block;margin:0;padding:0}.neutron .t55Jt{font-family:Source Sans Pro,-apple-system,system-ui,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif}.darkMode .t55Jt{background-color:#262626}.ui-revamp .t55Jt{font-family:Inter,Source Sans Pro,-apple-system,system-ui,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif;padding-bottom:inherit;padding-left:48px;padding-right:48px;padding-top:inherit}.darkMode.ui-revamp .t55Jt,.ui-revamp .t55Jt{background-color:var(--color-background-fill-primary)}@media print{.t55Jt{max-width:none!important;padding:0!important}}.s9EjL{border:none;color:#333;font-family:Source Sans Pro,-apple-system,system-ui,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif;font-size:30px;font-weight:600;line-height:40px;outline:none;padding-bottom:4px;width:100%}.s9EjL *{caret-color:auto}.darkMode .s9EjL{color:#f2f2f2}.ui-revamp .s9EjL{color:var(--color-text-fill-secondary-enabled);font-family:Inter,Source Sans Pro,-apple-system,system-ui,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif;font-size:30px;font-weight:600;line-height:40px;margin:0 var(--spacing-1);padding:var(--spacing-0-5) 0}.s9EjL::-moz-placeholder{color:#ababab;opacity:1}.s9EjL.Dx0E3,.s9EjL::placeholder{color:#ababab;opacity:1}.darkMode .s9EjL::-moz-placeholder{color:#737373}.darkMode .s9EjL.Dx0E3,.darkMode .s9EjL::placeholder{color:#737373}.BWzJG,.dDi4b{color:transparent;font-family:Source Sans Pro,-apple-system,system-ui,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif;font-size:30px;font-weight:600;left:0;line-height:40px;padding-left:inherit;padding-right:inherit;position:absolute;right:0}.darkMode .BWzJG,.darkMode .dDi4b{background-color:#262626}.ui-revamp .BWzJG,.ui-revamp .dDi4b{background-color:var(--color-background-fill-primary)}.dDi4b{z-index:0}.BWzJG,.dDi4b,.dvp4r{overflow-wrap:break-word;white-space:pre-wrap;word-break:break-word}.dvp4r{-webkit-user-select:text;-moz-user-select:text;user-select:text}.tEa6p{cursor:pointer;display:flex;flex:0 1 auto;height:28px;min-width:0;outline:none;padding:2px 7px 2px 2px;text-decoration:none}.tEa6p>svg{fill:#a6a6a6;flex:0 0 14px;margin:0 4px 0 5px;padding:0;width:14px}.tEa6p>svg,.wG0gF{display:inline-block}.wG0gF{color:#a6a6a6;flex:1 1;font-size:14px;line-height:24px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.qnYnO{display:flex;flex:1 1;margin-bottom:10px;min-width:0}.o06iV{fill:#a6a6a6;display:inline-block;height:24px;margin:2px 0;width:8px}.NdtdE{fill:#fff;background:#4d4d4d;border-radius:10px;height:20px;margin-top:6px;min-width:20px;padding:3px}.SiiOu{align-items:center;border:2px solid transparent;border-radius:1em;cursor:pointer;display:inline-flex;font-family:-apple-system,system-ui,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif;font-size:13px;font-weight:400;height:28px;line-height:30px;max-width:375px;padding:0 6px;text-align:center}body.ui-revamp .SiiOu{background-color:var(--color-filterpill-base-fill-default);border-radius:var(--radius-sm)}body.neutron .SiiOu{border-radius:9px;font-family:Source Sans Pro,-apple-system,system-ui,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif;font-size:12px;height:18px;line-height:18px;margin:0;max-width:165px;padding:0 5px 0 2px}.SiiOu.WZavu{border-color:#0081c2}body.darkMode .SiiOu.WZavu{background-color:#404040;border-color:#026fac;color:#d9d9d9}.SiiOu:focus-visible{outline-color:#0081c2;outline-style:solid}body.darkMode .SiiOu:focus-visible{outline-color:#026fac;outline-style:solid}body.neutron .gmpJr{height:12px;margin-right:2px;vertical-align:middle;width:12px}.qtEz7{display:none}@media(orientation:landscape)and (min-height:400px),(orientation:portrait)and (min-height:900px){body.neutron .qtEz7{align-items:center;display:flex;padding:12px 16px}}.COla5{color:#a6a6a6;font-family:Source Sans Pro,-apple-system,system-ui,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif;font-size:14px;font-weight:600;text-transform:uppercase}body.ui-revamp .COla5{font-family:Inter,Source Sans Pro,-apple-system,system-ui,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif}.aWXP8{background:none;border:none;border-radius:3px;font-size:16px;padding:6px 14px}@media(orientation:landscape)and (min-height:400px),(orientation:portrait)and (min-height:900px){body.neutron .aWXP8{border-radius:8px}}.aWXP8.CpeWO,.aWXP8:hover{cursor:pointer}.hLr_f{background-color:#fff;border-radius:3px;box-shadow:0 0 6px 0 rgba(0,0,0,.3);max-height:308px;outline:0;padding:12px 0;z-index:10}.darkMode .hLr_f{background-color:#333;box-shadow:0 0 6px 0 rgba(0,0,0,.5)}@media(orientation:landscape)and (min-height:400px),(orientation:portrait)and (min-height:900px){body.neutron .hLr_f{border-radius:8px;max-height:312px;padding:0}}.sIGrz{background-color:#fff;border:none;color:#333;display:flex;font-family:-apple-system,system-ui,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif;font-size:14px;font-weight:500;height:28px;line-height:26px;margin:0;max-width:280px;min-width:110px;outline:0;padding:0 20px;width:100%}body.neutron .sIGrz{font-family:Source Sans Pro,-apple-system,system-ui,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif}.darkMode .sIGrz{background-color:#333;color:#e6e6e6}.sIGrz.S4Dr5{color:#ccc;cursor:not-allowed}.darkMode .sIGrz.S4Dr5{color:#737373}@media(orientation:landscape)and (min-height:400px),(orientation:portrait)and (min-height:900px){body.neutron .sIGrz{align-items:center;color:#262626;font-size:18px;height:50px;max-width:320px;padding:0 8px}body.neutron.darkMode .sIGrz{background-color:#333;color:#e6e6e6}}.sIGrz:focus,.sIGrz:not([disabled]):active,.sIGrz:not([disabled]):hover{background-color:#f2f2f2;cursor:pointer}.darkMode .sIGrz:focus,.darkMode .sIGrz:not([disabled]):active,.darkMode .sIGrz:not([disabled]):hover{background-color:#4d4d4d}.fNUy3{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.veP0d{fill:#666;height:24px;margin-right:8px;margin-top:.5px;width:24px}.darkMode .veP0d{fill:#e6e6e6}@media(orientation:landscape)and (min-height:400px),(orientation:portrait)and (min-height:900px){body.neutron .veP0d{fill:#262626;height:38px;padding-top:8px;width:38px}body.neutron.darkMode .veP0d{fill:#e6e6e6}}.uB5GB{outline-style:none;overflow-y:auto;scrollbar-color:transparent transparent;scrollbar-width:thin}.uB5GB::-webkit-scrollbar{-webkit-appearance:auto}.uB5GB::-webkit-scrollbar:vertical{width:8px}.uB5GB::-webkit-scrollbar-corner{background-color:transparent}.uB5GB::-webkit-scrollbar{height:8px;width:8px}.uB5GB::-webkit-scrollbar-thumb{background-color:transparent;border-radius:10px}.uB5GB::-webkit-scrollbar-track{background-color:transparent}.uB5GB:hover{scrollbar-color:#a6a6a6 transparent}.uB5GB:hover::-webkit-scrollbar-thumb{background-color:#a6a6a6}.uB5GB::-webkit-scrollbar-track:hover{background-color:transparent}body.darkMode .uB5GB:hover{scrollbar-color:hsla(0,0%,100%,.4) hsla(0,0%,100%,.12)}body.darkMode .uB5GB:hover::-webkit-scrollbar-thumb{background-color:hsla(0,0%,100%,.4)}body.darkMode .uB5GB::-webkit-scrollbar-track:hover{background-color:hsla(0,0%,100%,.12)}.TsMki{background-color:#f2f2f2;color:#4d4d4d}.TsMki>.zZDaP{fill:#0081c2}body.darkMode .TsMki{background-color:#404040;color:#ccc}body.darkMode .TsMki>.zZDaP{fill:#00a3f4}body.ui-revamp .TsMki>.zZDaP{fill:var(--color-text-fill-brandsecondary-enabled)}.TsMki.xrKoa{background-color:#0081c2;color:#fff}.TsMki.xrKoa>.zZDaP{fill:#fff}body.darkMode .TsMki.xrKoa{background-color:#00a3f4;color:#000}body.darkMode .TsMki.xrKoa>.zZDaP{fill:#000}body.ui-revamp .TsMki.xrKoa{background-color:var(--color-text-fill-brandsecondary-enabled)}.TsMki.qmyEi{color:#a6a6a6;text-decoration:line-through}.TsMki.qmyEi>.zZDaP{fill:#a6a6a6}body.darkMode .TsMki.qmyEi{color:#737373}body.darkMode .TsMki.qmyEi>.zZDaP{fill:#737373}.QDr4y{align-items:center;background-color:#f2f2f2;color:#4d4d4d;display:flex;flex:0 1 auto;height:24px;min-width:0;padding:4px 6px!important}body.darkMode .QDr4y{background-color:#404040;color:#a6a6a6}.yicM6{font-size:13px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}body.neutron .yicM6{font-size:12px}.cMtx1{display:flex;flex:1 1;flex-wrap:wrap;gap:10px 3px;justify-content:flex-end;max-height:24px;min-width:0;overflow:hidden;position:relative}button.U1NcO{background-color:transparent;border:none;cursor:pointer;display:none;line-height:24px;min-width:0;outline:none;padding:2px 0 2px 3px;text-decoration:none}body.neutron button.U1NcO{display:none}body.neutron button.U1NcO.mrTBH,button.U1NcO.mrTBH{display:inline-flex}.tXsDO{color:#a6a6a6;display:inline-block;font-size:13px;margin-right:5px}body.neutron .tXsDO{font-size:12px}.t8jit{display:flex;justify-content:flex-end}body.neutron .t8jit{display:block;margin-top:-7px}body.ui-revamp .t8jit{gap:var(--spacing-1-5);justify-content:flex-start;margin:var(--spacing-2) var(--spacing-1)}.MzThR,.Q7baq,.WR0b5,.bXMZZ{display:flex;flex:1 1;gap:4px}.MzThR>button,.MzThR>div,.Q7baq>button,.Q7baq>div,.WR0b5>button,.WR0b5>div,.bXMZZ>button,.bXMZZ>div{display:inline-flex;flex:0 0 auto}body.ui-revamp .MzThR,body.ui-revamp .Q7baq,body.ui-revamp .WR0b5,body.ui-revamp .bXMZZ{gap:var(--spacing-1-5)}.bXMZZ{align-items:center;flex-direction:row;justify-content:flex-end}body.ui-revamp .bXMZZ{justify-content:flex-start}.Q7baq{align-items:flex-start;flex:1 1 auto;margin-left:-9px}body.android .Q7baq{margin-left:-6px}.MzThR{align-items:flex-end;flex:1 10 auto!important;flex-direction:row-reverse}body.ui-revamp .MzThR{align-items:flex-start;flex-direction:row}.k5Omm{margin-bottom:-2px;margin-top:-2px}body.neutron .k5Omm{height:38px;padding-top:10px}body.neutron .WGkYh{margin-bottom:8px}body.neutron .ekEMc{height:34px;padding-bottom:7px}.SvhxT{margin-bottom:4px}.WobRs{margin-left:4px}.klJtG{background-color:#f2f2f2;color:#4d4d4d}body.darkMode .klJtG{background-color:#404040;color:#d9d9d9}.yjBnv{line-height:18px;max-width:187.5px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}body.neutron .yjBnv{max-width:106.5px}.qu2hq{fill:#4d4d4d}body.darkMode .qu2hq{fill:#d9d9d9}.Jgmos{height:12px;margin:0 4px 0 2px;width:12px}body.neutron .Jgmos{height:10px;margin:0 4px 0 2px;width:10px}.VSyn1{height:6px;margin:2px 0 0 6px;width:10.5px}.pn2Nu{border-color:#0081c2}body.darkMode .pn2Nu{background-color:#404040;border-color:#026fac;color:#d9d9d9}.iCWlt{align-items:center;border:2px solid transparent;border-radius:1em;cursor:pointer;display:inline-flex;font-family:-apple-system,system-ui,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif;font-size:13px;font-weight:400;height:28px;line-height:30px;max-width:375px;padding:0 6px;text-align:center}body.ui-revamp .iCWlt{background-color:var(--color-filterpill-base-fill-default);border-radius:var(--radius-sm)}body.neutron .iCWlt{border-radius:9px;font-family:Source Sans Pro,-apple-system,system-ui,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif;font-size:12px;height:18px;line-height:18px;margin:0;max-width:165px;padding:0 5px 0 2px}.iCWlt.v4NGn{border-color:#0081c2}body.darkMode .iCWlt.v4NGn{background-color:#404040;border-color:#026fac;color:#d9d9d9}.iCWlt:focus-visible{outline-color:#0081c2;outline-style:solid}body.darkMode .iCWlt:focus-visible{outline-color:#026fac;outline-style:solid}body.neutron .MFy4Y{height:12px;margin-right:2px;vertical-align:middle;width:12px}.OZvff{display:none}@media(orientation:landscape)and (min-height:400px),(orientation:portrait)and (min-height:900px){body.neutron .OZvff{align-items:center;display:flex;padding:12px 16px}}.chb7N{color:#a6a6a6;font-family:Source Sans Pro,-apple-system,system-ui,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif;font-size:14px;font-weight:600;text-transform:uppercase}body.ui-revamp .chb7N{font-family:Inter,Source Sans Pro,-apple-system,system-ui,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif}.iCWlt{background-color:#f2f2f2;color:#4d4d4d}body.darkMode .iCWlt{background-color:#404040;color:#d9d9d9}.MyfRW{max-width:324.5px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}body.neutron .MyfRW{max-width:118.5px}.mqPQf{color:#737373;margin-left:3px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}body.darkMode .mqPQf{color:#a6a6a6}body.neutron .mqPQf{margin-left:0}@media(orientation:landscape)and (min-height:400px),(orientation:portrait)and (min-height:900px){body.neutron .mqPQf{margin-left:3px}}.R2R8K{fill:#4d4d4d;height:6px;margin:2px 0 0 5px;width:10.5px}body.darkMode .R2R8K{fill:#d9d9d9}.ylNVJ{fill:#00a82d;height:17px;width:17px}body.neutron .ylNVJ{height:14px;width:14px}.E5ktJ{min-height:174px;overflow-y:hidden!important;width:280px}.CsPy9{opacity:.2;pointer-events:none}.dAqZn{background:linear-gradient(180deg,hsla(0,0%,100%,0),#f8f8f8);bottom:144px;height:30px;position:absolute;width:100%;z-index:1}.darkMode .dAqZn{background:linear-gradient(180deg,rgba(64,64,64,0),#404040)}.tRNtH{align-items:center;background-color:#f8f8f8;bottom:0;display:flex;flex-direction:column;height:144px;position:absolute;text-align:center;width:100%;z-index:1}.darkMode .tRNtH{background-color:#404040}.Dwllp{color:#000;font-size:16px;font-weight:600;margin:8px 0 0}.darkMode .Dwllp{color:#f2f2f2}.xwoD3{color:#4d4d4d;font-size:15px;font-weight:400;margin:8px 0 0;padding:0 1px}.darkMode .xwoD3{color:#ccc}.jKzoV{align-items:center;background-color:#f0a00d;border:0;border-radius:4px;display:flex;height:32px;justify-content:center;margin:16px 0 0;padding:8px 18px}.jKzoV:hover{background-color:#fcb100;cursor:pointer}.KIBVi{height:18px;margin-right:3px;width:18px}.GTqpr{color:#000;font-size:14px;font-weight:600}.Flbhm{left:25px;position:relative;top:-54px}body.neutron .Flbhm{left:-25px;top:-50px}.WLIcP{animation:T9hex 2s linear infinite;background-color:#026fac;border-radius:50%;height:80px;opacity:.2;position:absolute;width:80px}.JMwul{animation:GFFDu 2s linear infinite}@keyframes T9hex{0%{opacity:.3;transform:scale(.25)}50%{opacity:.3;transform:scale(.33)}to{opacity:.3;transform:scale(.25)}}@keyframes GFFDu{0%{opacity:.2;transform:scale(.53)}50%{opacity:.2;transform:scale(.43)}to{opacity:.2;transform:scale(.53)}}.accentuate,accentuate{background:#d6e627;border-bottom:2px solid #bbcd00;display:inline}body.darkMode .accentuate,body.darkMode accentuate{background-color:#616a00;border-color:#a2a958}body.ui-revamp .accentuate,body.ui-revamp accentuate{background-color:rgba(255,217,25,.5);border-color:var(--color-icon-fill-yellow-enabled);border-radius:var(--radius-xxs)}.accentuate.primary,accentuate.primary{background:#0081c2;border-color:#036ca0;color:#fff}body.darkMode .accentuate.primary,body.darkMode accentuate.primary{background-color:rgba(2,111,172,.6);border-color:#0081c2}body.ui-revamp .accentuateTitle{font-size:34px;margin-left:var(--spacing-1)}foreign-content{display:block}.R0W7A .VcvZr{fill:#666;flex-shrink:0;height:24px;padding:2px;width:24px}body.neutron .R0W7A .VcvZr{height:24px;padding:0;width:24px}.R0W7A .tvfU_{stroke:#666}.R0W7A .W6sUk{flex-grow:1;min-width:0;overflow:hidden;padding:0 6px;text-align:left;text-overflow:ellipsis;white-space:nowrap}.R0W7A .NSlFZ,.R0W7A .RWVb1{position:relative}.R0W7A .NSlFZ{border-left:2px solid transparent;border-right:2px solid transparent;overflow-x:auto;width:100%}body.neutron .R0W7A .NSlFZ{-webkit-overflow-scrolling:touch;overflow-x:auto}.R0W7A .a_pAG{height:auto;margin:6px;position:relative;-webkit-user-select:text;-moz-user-select:text;user-select:text;white-space:normal}.R0W7A .a_pAG *{caret-color:inherit!important}.R0W7A .a_pAG:focus{outline:none}.R0W7A .a_pAG .GTWFL{align-items:center;background-color:#f8f8f8;border:1px solid #ccc;border-radius:3px;color:#4d4d4d;cursor:pointer;display:inline-flex;font-size:15px;padding:4px;position:relative}.R0W7A .a_pAG .GTWFL svg{height:20px;margin-right:2px;width:20px}.R0W7A .a_pAG en-todo{background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkBAMAAAATLoWrAAAAIVBMVEUAAACxsbG0tLS1tbW7u7vz8/Pz8/O0tLS0tLS0tLT7+/v8d5OVAAAACXRSTlMAJ66vy+Xp8PLhUQqEAAAASElEQVQoz2NgIA4wasxEAk0CQCHxmSigECik6bYKCaRMAgp1ZiELLZsBFJq5CgXMHBUamkKoUbt0BkYCCJmEPZkwWmAkJmIAAHlqRIXBCNTWAAAAAElFTkSuQmCC);background-position:50%;background-repeat:no-repeat;background-size:18px 18px;border:none;display:inline-block;height:24px;width:24px}.R0W7A .a_pAG en-todo[checked=true]{background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAMAAADW3miqAAAAXVBMVEUAAAB2dnZ3d3d2dnaEhITr6+vs7Ox2dnZ2dnZ2dnaFhYWJiYmKioqLi4uNjY2Pj4+lpaWtra2/v7/Hx8fMzMzU1NTn5+fo6Ojs7Ozu7u7y8vL4+Pj5+fn6+vr7+/sSwu0tAAAACXRSTlMAJ66vy+Xp8PLhUQqEAAAArklEQVQ4y9XUuxqDIAwFYKBeOK21d7UFff/HrIIdiBA7uHjW/EPIlyDElpFKIxGt5GxKMCm9UsiKIZEig3JII2lGBe0QMDAB9oX65/29huwVOBseTQZoWORNbTnkzamL9PR5vCImRPYC3PqFCVEzlUZFTIhM5RQ1pKf26MrE0Nd5RcxiBLMKzHJOToUmMsy2Qt2tboFp7H7Wlz3OfD5O7szz35n/9WEIeVj/ejbKFxwhWmc59bkmAAAAAElFTkSuQmCC)}.R0W7A .a_pAG .ip7np{background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAI4AAAAoCAMAAAAfZjLJAAAAOVBMVEX///////8AAAAGBgYHBwcLCwsMDAwuLi4vLy8wMDA0NDQ4ODiQkJCRkZGSkpLk5OT4+Pj5+fn///+VivCvAAAAAnRSTlP7/tveKlUAAACYSURBVFjD7djNCoAgEEZRtTR/MrX3f9ggKiHL5TiLb5aXWRwQFBRCMhohdlbDjSM5aSQ44IADDjhsOSUGY0IsY8qbk5w6x6UR5c0p14ZSNtOXhrOpZzb60nBCXQn0peFMdWWmLz2OoS/cDyvWlUhfGk6298aS6cv/NWjTiPLxSKxea7+WMQUvOjjggAMOOH0O/gZ7HF4fuQcB4Uk8+rg51gAAAABJRU5ErkJggg==);background-size:71px 20px;border:1px solid #000;border-radius:3px;display:inline-block;height:20px;width:71px}.R0W7A .Kye2q,.R0W7A .iaiNu{background-repeat:repeat-y;background-size:6px 1px;bottom:0;opacity:0;position:absolute;top:0;transition:opacity .15s;width:6px;z-index:50}.R0W7A .Kye2q{background-image:linear-gradient(90deg,rgba(38,38,38,.15),rgba(38,38,38,0));left:0}.R0W7A .iaiNu{background-image:linear-gradient(270deg,rgba(38,38,38,.15),rgba(38,38,38,0));right:0}.QUmBb .R0W7A,.QUmBb .R0W7A *{-webkit-user-select:none!important;-moz-user-select:none!important;user-select:none!important}.R0W7A .EikNO{overflow-x:overlay!important}.R0W7A .EikNO::-webkit-scrollbar{background-color:transparent;height:7px}.R0W7A .EikNO::-webkit-scrollbar-thumb{border:2px solid transparent;border-radius:3.5px;box-shadow:inset 0 0 7px 7px #857f7f}.TJ3_K .y7X5r{fill:#666;background-color:transparent;flex-shrink:0;height:24px;padding:2px;width:24px}body.darkMode .TJ3_K .y7X5r{fill:#a6a6a6}.TJ3_K .f1nDD{flex-grow:1;min-width:0;overflow:hidden;padding:0 6px;text-align:left;text-overflow:ellipsis;white-space:nowrap}.jFn6H{background:#fff;cursor:pointer;display:flex;flex-flow:row nowrap;height:120px;width:100%}body.darkMode .jFn6H{background:#1a1a1a}.jFn6H .asP35{color:#666;display:flex;flex:1 1;flex-flow:column nowrap;font-family:Source Sans Pro,-apple-system,system-ui,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif;font-size:14px;gap:5px;line-height:1.4;min-width:0;padding:12px 16px}body.darkMode .jFn6H .asP35{color:#a6a6a6}body.ui-revamp .jFn6H .asP35{font-family:Inter,Source Sans Pro,-apple-system,system-ui,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif}.jFn6H .asP35 .aRmqv{flex:1 0 auto;height:53px}.jFn6H .asP35 .aRmqv.Ll8Am{align-items:center;color:#a6a6a6;display:flex;flex-flow:row nowrap;font-style:italic;justify-content:center;padding:0 40px;text-align:center}body.darkMode .jFn6H .asP35 .aRmqv.Ll8Am{color:#737373}.jFn6H .asP35 .aRmqv .IJZW5{-webkit-line-clamp:4;-webkit-box-orient:vertical;display:block;display:-webkit-box;line-height:18px;max-height:72px;overflow:hidden;text-overflow:ellipsis}.jFn6H .asP35 .FaJMG{display:flex;flex:0 0 20px;flex-flow:row nowrap;height:20px}.jFn6H .asP35 .FaJMG .LR52w{fill:#666;flex:0 0 20px;height:20px;margin-inline-end:2px;margin-inline-start:-3px;padding:2px 1px 0;width:20px}body.darkMode .jFn6H .asP35 .FaJMG .LR52w{fill:#a6a6a6}.jFn6H .asP35 .FaJMG .R54J0{flex:1 0;min-width:0;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.jFn6H .RHphs{flex:0 0 108px;padding:12px 12px 12px 0}.jFn6H .RHphs .D_RBD{border:1px solid rgba(0,0,0,.06);border-radius:4px;height:96px;overflow:hidden;width:96px}.VJ1Ep{overflow:hidden;padding-top:56.25%;position:relative}.hjSpD{vertical-align:top}.hjSpD,.ultdG{height:100%;left:0;position:absolute;top:0;width:100%}.ultdG{background:#1a1a1a}.LfLpV{animation:ky1jh 2s linear infinite;border:6px solid #f2f2f2;border-radius:50%;border-top-color:#1e83bd;height:100px;left:50%;position:absolute;top:50%;width:100px}@keyframes ky1jh{0%{transform:translate(-50%,-50%) rotate(0deg)}to{transform:translate(-50%,-50%) rotate(1turn)}}.MdaOR .ZVVh7{background-color:transparent;flex-shrink:0;height:24px;width:24px}.MdaOR .ZVVh7 body.neutron{padding:0}.MdaOR .Rm2JI{fill:#a6a6a6;background-color:transparent;border:none;cursor:pointer;flex-shrink:0;height:24px;width:24px}body.darkMode .MdaOR .Rm2JI{fill:#737373}body.neutron .MdaOR .Rm2JI{padding:0}.MdaOR .Rm2JI:focus{outline:none}.MdaOR .Hseq7{flex-grow:1;min-width:0;overflow:hidden;padding:0 6px;text-align:left;text-overflow:ellipsis;white-space:nowrap}.k_KLX{align-items:center;background:none;border:none;color:#0081c2;cursor:pointer;display:flex;flex:none;font-family:inherit;font-size:14px;padding:0;position:relative}.k_KLX svg{--c-background:#a6a6a6;--c-badge:#666;--c-foreground:var(--header-background)}body.darkMode .k_KLX svg{--c-badge:#a6a6a6;--c-background:#737373}body.darkMode .k_KLX{color:#00a3f4}body.ui-revamp .k_KLX{color:var(--color-text-fill-brandsecondary-enabled)}.k_KLX:hover{text-decoration:underline}.k_KLX .FL6RN{padding:0 12px}.KSEvr{font-size:13px;line-height:16px;padding:10px 16px;text-align:center}.HQeK2{font-style:italic;margin-top:8px}.Zr4LP .xevWk{background-color:transparent;flex-shrink:0;height:24px;padding:2px;width:24px}body.neutron .Zr4LP .xevWk{height:24px;padding:0;width:24px}.Zr4LP .kGEtB{fill:#a6a6a6}.Zr4LP .YCAUH{fill:#0081c2;flex-shrink:0;height:24px;transition:background-color .2s ease-in-out,fill .2s ease-in-out;width:24px}.Zr4LP .YCAUH:hover{fill:#02588e}body.darkMode .Zr4LP .YCAUH{fill:#00a3f4}body.darkMode .Zr4LP .YCAUH:hover{fill:rgba(0,163,244,.5)}body.ui-revamp .Zr4LP .YCAUH{fill:var(--color-text-fill-brandsecondary-enabled)}body.ui-revamp .Zr4LP .YCAUH:hover{fill:rgba(var(--color-text-fill-brandsecondary-enabled),.5)}body.neutron .Zr4LP .YCAUH{height:32px;width:32px}.Zr4LP .YCAUH:focus{outline-color:#66b3da}body.neutron .Zr4LP .YCAUH{outline:none}.Zr4LP .eeypK{flex-grow:1;min-width:0;overflow:hidden;padding:0 6px;text-align:left;text-overflow:ellipsis;white-space:nowrap}.Zr4LP .GPV7u,.Zr4LP .hp_WN{font-style:italic}.Zr4LP .XUDlg{opacity:.4}.Zr4LP .vjNVM{align-items:center;display:flex;flex-shrink:0;font-size:14px;font-weight:400;justify-content:center;margin-right:6px}body.neutron .Zr4LP .vjNVM{font-style:normal;font-weight:400}.Zr4LP .wSTI6+.IWIUl:before{content:"•";margin:0 4px}body.neutron .gJbG8.gJbG8{height:30px;width:30px}.nodeContainer{border:2px solid #f2f2f2;border-radius:6px;height:auto;margin:12px 0;max-width:100%;outline:none;position:relative;transition:border-color .2s;width:375px}:global(body.neutron) .nodeContainer{width:343px}:global(td) .nodeContainer{min-width:375px}:global(body.neutron) :global(td) .nodeContainer{min-width:343px}:global(body.darkMode) .nodeContainer{border-color:#333}:global(body.ui-revamp) .nodeContainer{margin:var(--spacing-1-5) var(--spacing-0-75);width:auto}.nodeContainer.evernoteContainer{border-color:#e6f6ea}:global(body.darkMode) .nodeContainer.evernoteContainer{border-color:#203627}:global(body.ui-revamp) .nodeContainer.evernoteContainer{border-color:var(--color-text-fill-brandsecondary-enabled)}:global(.editing-locked) .nodeContainer.nodeContainer{border-color:var(--selection-border-color-light)}:global(body.darkMode) :global(.editing-locked) .nodeContainer.nodeContainer{border-color:var(--selection-border-color-dark)}:global(body.ui-revamp) :global(.editing-locked) .nodeContainer.nodeContainer{border-color:var(--color-text-fill-brandsecondary-enabled)}.nodeContainer.hasBody.hasBody.hasBody{width:100%}.nodeContainer.error .title,.nodeContainer.loading .title{font-style:italic}.nodeContainer>:first-child{border-radius:4px 4px 0 0}.nodeContainer>:last-child{border-radius:0 0 4px 4px}.nodeContainer>:only-child{border-radius:4px}.nodeContainer:hover{border-color:#e6e6e6}:global(body.darkMode) .nodeContainer:hover{border-color:#404040}.nodeContainer:hover .header{--header-background:#e6e6e6;background:#e6e6e6}:global(body.darkMode) .nodeContainer:hover .header{--header-background:#404040;background-color:#404040}.nodeContainer:hover .header.playing{--header-background:#026fac;background:#026fac}:global(body.darkMode) .nodeContainer:hover .header.playing{--header-background:#00a3f4;background-color:#00a3f4}:global(body.ui-revamp) .nodeContainer:hover .header.playing{--header-background:var(--color-text-fill-brandsecondary-enabled);background-color:var(--color-text-fill-brandsecondary-enabled)}.nodeContainer.evernoteContainer:hover{border-color:#daeade}:global(body.darkMode) .nodeContainer.evernoteContainer:hover{border-color:#2c4532}.nodeContainer.evernoteContainer:hover .header{--header-background:#daeade;background:#daeade}:global(body.darkMode) .nodeContainer.evernoteContainer:hover .header{--header-background:#2c4532;background-color:#2c4532}@media print{.nodeContainer{border-color:#f2f2f2!important}.nodeContainer.evernoteContainer{border-color:#e6f6ea!important}}:global(.collaborator-selected) .nodeContainer.nodeContainer{border-color:var(--selection-border-color-light)}:global(body.darkMode) :global(.collaborator-selected) .nodeContainer.nodeContainer{border-color:var(--selection-border-color-dark)}:global(body.ui-revamp) :global(.collaborator-selected) .nodeContainer.nodeContainer{border-color:var(--color-text-fill-brandsecondary-enabled)}:global(.selected) .nodeContainer.nodeContainer{border-color:#66b3da}:global(body.darkMode) :global(.selected) .nodeContainer.nodeContainer{border-color:#00a3f4}:global(body.ui-revamp) :global(.selected) .nodeContainer.nodeContainer{border-color:var(--color-text-fill-brandsecondary-enabled)}:global(.hasNestedSelection) .nodeContainer.nodeContainer{border-color:rgba(102,179,218,.5)}:global(body.darkMode) :global(.hasNestedSelection) .nodeContainer.nodeContainer{border-color:rgba(0,163,244,.5)}:global(body.ui-revamp) :global(.hasNestedSelection) .nodeContainer.nodeContainer{border-color:var(--color-text-fill-brandsecondary-enabled)}:global(.android-unselectable) .nodeContainer,:global(.android-unselectable) .nodeContainer *{-webkit-user-select:none!important;-moz-user-select:none!important;user-select:none!important}@keyframes spin{0%{left:-112px}to{left:100%}}.header{--header-background:#f2f2f2;align-items:center;background-color:#f2f2f2;color:#666;cursor:pointer;display:flex;font-size:16px;font-weight:600;height:32px;justify-content:space-between;padding:0 6px;transition:background-color .2s;width:100%}.header.playing{--header-background:#0081c2;background:#0081c2;color:#fff}:global(body.darkMode) .header{--header-background:#333;background-color:#333;color:#a6a6a6}:global(body.darkMode) .header.playing{--header-background:#00a3f4;background:#00a3f4;color:#000}.nodeContainer.evernoteContainer .header{--header-background:#e6f6ea;background-color:#e6f6ea}:global(body.darkMode) .nodeContainer.evernoteContainer .header{--header-background:#203627;background-color:#203627}:global(body.ui-revamp) .nodeContainer.evernoteContainer .header{--header-background:rgba(77,100,255,.15);background-color:rgba(77,100,255,.15)}:global(body.neutron) .header{font-weight:700;height:44px}:global(body.ui-revamp) .header{color:var(--color-text-fill-secondary-enabled);font-size:14px;font-weight:500;height:auto;padding:var(--spacing-1) var(--spacing-2)}:global(body.ui-revamp) .header svg{color:#666}.header *{-webkit-user-select:none;-moz-user-select:none;user-select:none}:global(body.firefox) .header *{-webkit-user-select:all;-moz-user-select:all;user-select:all}.header .loader{background:transparent;border-radius:6px 6px 0 0;height:6px;left:-2px;overflow:hidden;position:absolute;right:-2px;top:-2px}:global(.selected) .header .loader{top:0}.header .loader:before{background:#d9d9d9;content:"";height:2px;position:absolute;top:0;width:100%}:global(body.darkMode) .header .loader:before{background:#404040}.header .loader:after{content:"";height:2px;position:absolute;top:0}.header .loader:not(.loaderWithProgress):after{animation:spin 2s linear infinite;background:linear-gradient(270deg,rgba(0,129,194,0),#0081c2 51%,rgba(0,129,194,0));width:112px}.header .loader.loaderWithProgress:after{background:#0081c2;transform:scaleX(var(--loadingProgress));transform-origin:top left;transition:transform .5s ease-in-out;width:100%}en-codeblock{background-color:#eee;background-color:#fbfaf8;background-position:initial initial;background-repeat:initial initial;border:1px solid rgba(0,0,0,.149);border-radius:4px;box-sizing:border-box;color:#333;display:block;font-family:monospace;font-family:Monaco,Menlo,Consolas,Courier New,monospace;font-size:12px;margin:3px 0;min-width:375px;padding:8px}:global(body.neutron) en-codeblock{min-width:343px}body.darkMode en-codeblock{background-color:#333;border-color:#737373;color:#e6e6e6}body.ui-revamp en-codeblock{background-color:var(--color-surface-fill-secondary-enabled);border-color:var(--color-surface-stroke-tertiary-enabled);font-family:Fira Code;font-size:14px;margin:var(--spacing-1-5) var(--spacing-0-75);padding:var(--spacing-2)}tt{background-color:#f0f0f0;border:1px solid #d5d5d5;color:#cd4c62;margin:5px 0;padding:2px 0}.N50Yn .mN_ra{background-color:transparent;flex-shrink:0}.N50Yn .xH17C{fill:#666;height:24px;padding:0;width:24px}body.darkMode .N50Yn .xH17C{fill:#ccc}body.neutron .N50Yn .xH17C{height:24px;padding:0;width:24px}.N50Yn .kHg6G{flex-grow:1;min-width:0;overflow:hidden;padding:0 4px;text-align:left;text-overflow:ellipsis;white-space:nowrap}.N50Yn .dEbe_{fill:currentColor;height:12px;margin:0 4px 1px 10px;padding:0;width:12px}.N50Yn .dEbe_.P0Qqg{transform:rotate(180deg)}.FbXVw{background-color:#f2f2f2;color:#4d4d4d;font-size:14px;padding:10px}body.darkMode .FbXVw{background-color:#333;color:#fff}.FbXVw .ERHvL{background:none;border:none;color:#4d4d4d;cursor:pointer;display:inline-block;font-weight:700;line-height:1.5;margin:0;padding:8px 0 0;text-decoration:underline}body.darkMode .FbXVw .ERHvL{color:#fff}.fRP_7{--encrypted-block-width:376px;--encrypted-block-height:36px;background:#f2f2f2;border:2px solid transparent;border-radius:6px;display:block;margin:12px 0;outline:none;position:relative}@media print{.fRP_7{-webkit-print-color-adjust:exact!important;print-color-adjust:exact!important}}body.neutron .fRP_7{--encrypted-block-width:343px;--encrypted-block-height:48px}body.darkMode .fRP_7{background-color:#333}:not(.editing-locked)>.fRP_7:hover{background:#e6e6e6}body.darkMode :not(.editing-locked)>.fRP_7:hover{background-color:#404040}.selected .fRP_7{background-color:#f2f2f2;border:2px solid #66b3da}body.darkMode .selected .fRP_7{border-color:#00a3f4}body.ui-revamp .selected .fRP_7{border-color:var(--color-text-fill-brandsecondary-enabled)}.collaborator-selected .fRP_7{border-color:var(--selection-border-color-light)}body.darkMode .collaborator-selected .fRP_7{border-color:var(--selection-border-color-dark)}.fRP_7 .rQyjS{align-items:center;display:flex;height:100%;justify-content:space-between;width:100%}.fRP_7 .rQyjS .Pi8Xv{color:#666;flex-shrink:0;height:24px;margin:0 6px;width:24px}.fRP_7 .rQyjS .iOoIt{align-items:center;display:flex;flex-grow:1;justify-content:flex-start}.fRP_7 .rQyjS .iOoIt .UZ_pG{flex-shrink:0;height:10px;margin:0 4px;width:10px}.fRP_7 .rQyjS .dhfNF{flex-grow:0;flex-shrink:0}.fRP_7 .dt6WO{align-items:flex-start;display:flex;min-height:100%;min-width:100%}.fRP_7 .dt6WO .YykQc{flex-grow:1;outline:none;padding:6px 0 6px 12px;width:100%;word-break:break-word}.fRP_7 .dt6WO .dhfNF{cursor:pointer}.fRP_7:not(.AdKvc){cursor:pointer;height:var(--encrypted-block-height);max-width:100%;width:var(--encrypted-block-width)}.editing-locked .fRP_7{border-color:var(--selection-border-color-light);cursor:not-allowed}body.darkMode .editing-locked .fRP_7{border-color:var(--selection-border-color-dark)}.fRP_7.AdKvc{background:#f2f2f2;max-height:none;min-height:var(--encrypted-block-height);width:100%}body.darkMode .fRP_7.AdKvc{background-color:#333;color:#e6e6e6}.fRP_7 .dhfNF{align-items:center;display:flex;height:var(--encrypted-block-height);padding:4px 8px}body:not(.firefox) .fRP_7 .dhfNF{-webkit-user-select:none;-moz-user-select:none;user-select:none}.fRP_7 .dhfNF svg{fill:#666;border-radius:3px;height:28px;transition:background-color .2s;width:28px}.fRP_7 .dhfNF svg:hover{background-color:#ccc}body.darkMode .fRP_7 .dhfNF svg{fill:#a6a6a6}body.darkMode .fRP_7 .dhfNF svg:hover{background-color:#4d4d4d}.drag-image-holder>h1,.drag-image-holder>h2,.drag-image-holder>h3,.drag-image-holder>h4,.drag-image-holder>h5,.drag-image-holder>h6,en-note.peso>h1,en-note.peso>h2,en-note.peso>h3,en-note.peso>h4,en-note.peso>h5,en-note.peso>h6{margin:12px 0 0;padding:0}.drag-image-holder h1,en-note.peso h1{font-size:30px;font-weight:500;line-height:1.3333}.drag-image-holder h2,en-note.peso h2{font-size:24px;font-weight:400;line-height:1.3333}.drag-image-holder h3,.drag-image-holder h4,.drag-image-holder h5,.drag-image-holder h6,en-note.peso h3,en-note.peso h4,en-note.peso h5,en-note.peso h6{font-size:18px;font-weight:600;line-height:1.3333}.drag-image-holder h1,.ui-revamp en-note.peso h1{font-weight:600;letter-spacing:-.5;margin:22px var(--spacing-1) var(--spacing-0-25);padding:3px 0}.drag-image-holder h2,.ui-revamp en-note.peso h2{font-weight:600;letter-spacing:-.5;margin:var(--spacing-2) var(--spacing-1) 0;padding:3px 0}.drag-image-holder h3,.drag-image-holder h4,.drag-image-holder h5,.drag-image-holder h6,.ui-revamp en-note.peso h3,.ui-revamp en-note.peso h4,.ui-revamp en-note.peso h5,.ui-revamp en-note.peso h6{font-weight:600;letter-spacing:-.5;margin:var(--spacing-2) var(--spacing-1) 0;padding:0}en-note.peso hr{border:none;border-bottom:thin solid #999;margin:12px 0;padding:0}body.darkMode en-note.peso hr{border-color:#a6a6a6}body.ui-revamp en-note.peso hr{border-bottom:thin solid var(--color-surface-stroke-tertiary-enabled)}en-note.peso .en-internal-link.en-internal-link{color:#00a82d}en-note.peso :global(body.ui-revamp){color:var(--color-surface-fill-secondarybrand-enabled)}.drag-image-holder,en-note.peso{--padded-area-width:36px;--bullet-content-padding:10px;--bullet-area-width:12px;--padded-area-height:22.5px;--bullet-left-position:calc(var(--padded-area-width) - var(--bullet-area-width) - var(--bullet-content-padding));--drag-handle-dimension:14px;--drag-handle-bullet-padding:6px}.drag-image-holder dl,.drag-image-holder ol,.drag-image-holder ul,en-note.peso dl,en-note.peso ol,en-note.peso ul{margin:0;padding:0 0 0 3px;position:relative}.drag-image-holder td>dl,.drag-image-holder td>ol,.drag-image-holder td>ul,en-note.peso td>dl,en-note.peso td>ol,en-note.peso td>ul{padding-left:var(--drag-handle-dimension)}.drag-image-holder li,en-note.peso li{margin:0;padding:0;position:relative}.drag-image-holder li h1,.drag-image-holder li h2,.drag-image-holder li h3,.drag-image-holder li h4,.drag-image-holder li h5,.drag-image-holder li h6,en-note.peso li h1,en-note.peso li h2,en-note.peso li h3,en-note.peso li h4,en-note.peso li h5,en-note.peso li h6{margin-block-end:0;margin-block-start:0}.drag-image-holder ol>li,.drag-image-holder ul>li,en-note.peso ol>li,en-note.peso ul>li{padding-left:var(--padded-area-width)}body.ui-revamp .drag-image-holder ol>li .list-content .para,body.ui-revamp .drag-image-holder ul>li .list-content .para,body.ui-revamp en-note.peso ol>li .list-content .para,body.ui-revamp en-note.peso ul>li .list-content .para{margin-left:var(--spacing-0-5)}.drag-image-holder ol,en-note.peso ol{--start:1;counter-reset:counters-0 calc(var(--start, 1) - 1);list-style-type:none}.drag-image-holder ol>li,en-note.peso ol>li{counter-increment:counters-0;list-style-type:none}.drag-image-holder ol>li>.list-content:before,en-note.peso ol>li>.list-content:before{content:counter(counters-0,decimal) ".";cursor:pointer;display:inline-block;font-size:15px;left:var(--bullet-left-position);line-height:var(--padded-area-height);position:absolute;text-align:right;top:0;white-space:nowrap}.drag-image-holder ul:not(.en-todolist),.drag-image-holder ul:not(.en-todolist)>li,en-note.peso ul:not(.en-todolist),en-note.peso ul:not(.en-todolist)>li{list-style-type:none}.drag-image-holder ul:not(.en-todolist)>li>.list-content:before,en-note.peso ul:not(.en-todolist)>li>.list-content:before{content:"•";cursor:pointer;display:inline-block;font-size:15px;left:var(--bullet-left-position);line-height:var(--padded-area-height);position:absolute;text-align:right;top:0;white-space:nowrap}.drag-image-holder ol ol,en-note.peso ol ol{--start:1;counter-reset:counters-1 calc(var(--start, 1) - 1);list-style-type:none}.drag-image-holder ol ol>li,en-note.peso ol ol>li{counter-increment:counters-1;list-style-type:none}.drag-image-holder ol ol>li>.list-content:before,en-note.peso ol ol>li>.list-content:before{content:counter(counters-1,lower-alpha) ".";cursor:pointer;display:inline-block;font-size:15px;left:var(--bullet-left-position);line-height:var(--padded-area-height);position:absolute;text-align:right;top:0;white-space:nowrap}.drag-image-holder ul:not(.en-todolist) ul:not(.en-todolist),.drag-image-holder ul:not(.en-todolist) ul:not(.en-todolist)>li,en-note.peso ul:not(.en-todolist) ul:not(.en-todolist),en-note.peso ul:not(.en-todolist) ul:not(.en-todolist)>li{list-style-type:none}.drag-image-holder ul:not(.en-todolist) ul:not(.en-todolist)>li>.list-content:before,en-note.peso ul:not(.en-todolist) ul:not(.en-todolist)>li>.list-content:before{content:"◦";cursor:pointer;display:inline-block;font-size:15px;left:var(--bullet-left-position);line-height:var(--padded-area-height);position:absolute;text-align:right;top:0;white-space:nowrap}.drag-image-holder ol>:is(ol,ul) ol,en-note.peso ol>:is(ol,ul) ol{--start:1;counter-reset:counters-2 calc(var(--start, 1) - 1);list-style-type:none}.drag-image-holder ol>:is(ol,ul) ol>li,en-note.peso ol>:is(ol,ul) ol>li{counter-increment:counters-2;list-style-type:none}.drag-image-holder ol>:is(ol,ul) ol>li>.list-content:before,en-note.peso ol>:is(ol,ul) ol>li>.list-content:before{content:counter(counters-2,lower-roman) ".";cursor:pointer;display:inline-block;font-size:15px;left:var(--bullet-left-position);line-height:var(--padded-area-height);position:absolute;text-align:right;top:0;white-space:nowrap}.drag-image-holder ul:not(.en-todolist)>:is(ol,ul) ul:not(.en-todolist),en-note.peso ul:not(.en-todolist)>:is(ol,ul) ul:not(.en-todolist){list-style-type:none}.drag-image-holder ul:not(.en-todolist)>:is(ol,ul) ul:not(.en-todolist)>li,en-note.peso ul:not(.en-todolist)>:is(ol,ul) ul:not(.en-todolist)>li{list-style-type:none}.drag-image-holder ul:not(.en-todolist)>:is(ol,ul) ul:not(.en-todolist)>li>.list-content:before,en-note.peso ul:not(.en-todolist)>:is(ol,ul) ul:not(.en-todolist)>li>.list-content:before{content:"▪";cursor:pointer;display:inline-block;font-size:15px;left:var(--bullet-left-position);line-height:var(--padded-area-height);position:absolute;text-align:right;top:0;white-space:nowrap}.drag-image-holder ol>:is(ol,ul)>:is(ol,ul) ol,en-note.peso ol>:is(ol,ul)>:is(ol,ul) ol{--start:1;counter-reset:counters-3 calc(var(--start, 1) - 1);list-style-type:none}.drag-image-holder ol>:is(ol,ul)>:is(ol,ul) ol>li,en-note.peso ol>:is(ol,ul)>:is(ol,ul) ol>li{counter-increment:counters-3;list-style-type:none}.drag-image-holder ol>:is(ol,ul)>:is(ol,ul) ol>li>.list-content:before,en-note.peso ol>:is(ol,ul)>:is(ol,ul) ol>li>.list-content:before{content:counter(counters-3,decimal) ".";cursor:pointer;display:inline-block;font-size:15px;left:var(--bullet-left-position);line-height:var(--padded-area-height);position:absolute;text-align:right;top:0;white-space:nowrap}.drag-image-holder ul:not(.en-todolist)>:is(ol,ul)>:is(ol,ul) ul:not(.en-todolist),en-note.peso ul:not(.en-todolist)>:is(ol,ul)>:is(ol,ul) ul:not(.en-todolist){list-style-type:none}.drag-image-holder ul:not(.en-todolist)>:is(ol,ul)>:is(ol,ul) ul:not(.en-todolist)>li,en-note.peso ul:not(.en-todolist)>:is(ol,ul)>:is(ol,ul) ul:not(.en-todolist)>li{list-style-type:none}.drag-image-holder ul:not(.en-todolist)>:is(ol,ul)>:is(ol,ul) ul:not(.en-todolist)>li>.list-content:before,en-note.peso ul:not(.en-todolist)>:is(ol,ul)>:is(ol,ul) ul:not(.en-todolist)>li>.list-content:before{content:"•";cursor:pointer;display:inline-block;font-size:15px;left:var(--bullet-left-position);line-height:var(--padded-area-height);position:absolute;text-align:right;top:0;white-space:nowrap}.drag-image-holder ol>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ol,en-note.peso ol>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ol{--start:1;counter-reset:counters-4 calc(var(--start, 1) - 1);list-style-type:none}.drag-image-holder ol>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ol>li,en-note.peso ol>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ol>li{counter-increment:counters-4;list-style-type:none}.drag-image-holder ol>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ol>li>.list-content:before,en-note.peso ol>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ol>li>.list-content:before{content:counter(counters-4,lower-alpha) ".";cursor:pointer;display:inline-block;font-size:15px;left:var(--bullet-left-position);line-height:var(--padded-area-height);position:absolute;text-align:right;top:0;white-space:nowrap}.drag-image-holder ul:not(.en-todolist)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ul:not(.en-todolist),en-note.peso ul:not(.en-todolist)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ul:not(.en-todolist){list-style-type:none}.drag-image-holder ul:not(.en-todolist)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ul:not(.en-todolist)>li,en-note.peso ul:not(.en-todolist)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ul:not(.en-todolist)>li{list-style-type:none}.drag-image-holder ul:not(.en-todolist)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ul:not(.en-todolist)>li>.list-content:before,en-note.peso ul:not(.en-todolist)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ul:not(.en-todolist)>li>.list-content:before{content:"◦";cursor:pointer;display:inline-block;font-size:15px;left:var(--bullet-left-position);line-height:var(--padded-area-height);position:absolute;text-align:right;top:0;white-space:nowrap}.drag-image-holder ol>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ol,en-note.peso ol>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ol{--start:1;counter-reset:counters-5 calc(var(--start, 1) - 1);list-style-type:none}.drag-image-holder ol>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ol>li,en-note.peso ol>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ol>li{counter-increment:counters-5;list-style-type:none}.drag-image-holder ol>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ol>li>.list-content:before,en-note.peso ol>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ol>li>.list-content:before{content:counter(counters-5,lower-roman) ".";cursor:pointer;display:inline-block;font-size:15px;left:var(--bullet-left-position);line-height:var(--padded-area-height);position:absolute;text-align:right;top:0;white-space:nowrap}.drag-image-holder ul:not(.en-todolist)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ul:not(.en-todolist),en-note.peso ul:not(.en-todolist)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ul:not(.en-todolist){list-style-type:none}.drag-image-holder ul:not(.en-todolist)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ul:not(.en-todolist)>li,en-note.peso ul:not(.en-todolist)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ul:not(.en-todolist)>li{list-style-type:none}.drag-image-holder ul:not(.en-todolist)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ul:not(.en-todolist)>li>.list-content:before,en-note.peso ul:not(.en-todolist)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ul:not(.en-todolist)>li>.list-content:before{content:"▪";cursor:pointer;display:inline-block;font-size:15px;left:var(--bullet-left-position);line-height:var(--padded-area-height);position:absolute;text-align:right;top:0;white-space:nowrap}.drag-image-holder ol>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ol,en-note.peso ol>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ol{--start:1;counter-reset:counters-6 calc(var(--start, 1) - 1);list-style-type:none}.drag-image-holder ol>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ol>li,en-note.peso ol>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ol>li{counter-increment:counters-6;list-style-type:none}.drag-image-holder ol>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ol>li>.list-content:before,en-note.peso ol>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ol>li>.list-content:before{content:counter(counters-6,decimal) ".";cursor:pointer;display:inline-block;font-size:15px;left:var(--bullet-left-position);line-height:var(--padded-area-height);position:absolute;text-align:right;top:0;white-space:nowrap}.drag-image-holder ul:not(.en-todolist)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ul:not(.en-todolist),en-note.peso ul:not(.en-todolist)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ul:not(.en-todolist){list-style-type:none}.drag-image-holder ul:not(.en-todolist)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ul:not(.en-todolist)>li,en-note.peso ul:not(.en-todolist)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ul:not(.en-todolist)>li{list-style-type:none}.drag-image-holder ul:not(.en-todolist)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ul:not(.en-todolist)>li>.list-content:before,en-note.peso ul:not(.en-todolist)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ul:not(.en-todolist)>li>.list-content:before{content:"•";cursor:pointer;display:inline-block;font-size:15px;left:var(--bullet-left-position);line-height:var(--padded-area-height);position:absolute;text-align:right;top:0;white-space:nowrap}.drag-image-holder ol>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ol,en-note.peso ol>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ol{--start:1;counter-reset:counters-7 calc(var(--start, 1) - 1);list-style-type:none}.drag-image-holder ol>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ol>li,en-note.peso ol>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ol>li{counter-increment:counters-7;list-style-type:none}.drag-image-holder ol>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ol>li>.list-content:before,en-note.peso ol>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ol>li>.list-content:before{content:counter(counters-7,lower-alpha) ".";cursor:pointer;display:inline-block;font-size:15px;left:var(--bullet-left-position);line-height:var(--padded-area-height);position:absolute;text-align:right;top:0;white-space:nowrap}.drag-image-holder ul:not(.en-todolist)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ul:not(.en-todolist),en-note.peso ul:not(.en-todolist)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ul:not(.en-todolist){list-style-type:none}.drag-image-holder ul:not(.en-todolist)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ul:not(.en-todolist)>li,en-note.peso ul:not(.en-todolist)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ul:not(.en-todolist)>li{list-style-type:none}.drag-image-holder ul:not(.en-todolist)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ul:not(.en-todolist)>li>.list-content:before,en-note.peso ul:not(.en-todolist)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ul:not(.en-todolist)>li>.list-content:before{content:"◦";cursor:pointer;display:inline-block;font-size:15px;left:var(--bullet-left-position);line-height:var(--padded-area-height);position:absolute;text-align:right;top:0;white-space:nowrap}.drag-image-holder ol>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ol,en-note.peso ol>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ol{--start:1;counter-reset:counters-8 calc(var(--start, 1) - 1);list-style-type:none}.drag-image-holder ol>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ol>li,en-note.peso ol>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ol>li{counter-increment:counters-8;list-style-type:none}.drag-image-holder ol>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ol>li>.list-content:before,en-note.peso ol>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ol>li>.list-content:before{content:counter(counters-8,lower-roman) ".";cursor:pointer;display:inline-block;font-size:15px;left:var(--bullet-left-position);line-height:var(--padded-area-height);position:absolute;text-align:right;top:0;white-space:nowrap}.drag-image-holder ul:not(.en-todolist)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ul:not(.en-todolist),en-note.peso ul:not(.en-todolist)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ul:not(.en-todolist){list-style-type:none}.drag-image-holder ul:not(.en-todolist)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ul:not(.en-todolist)>li,en-note.peso ul:not(.en-todolist)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ul:not(.en-todolist)>li{list-style-type:none}.drag-image-holder ul:not(.en-todolist)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ul:not(.en-todolist)>li>.list-content:before,en-note.peso ul:not(.en-todolist)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ul:not(.en-todolist)>li>.list-content:before{content:"▪";cursor:pointer;display:inline-block;font-size:15px;left:var(--bullet-left-position);line-height:var(--padded-area-height);position:absolute;text-align:right;top:0;white-space:nowrap}.drag-image-holder ol>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ol,en-note.peso ol>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ol{--start:1;counter-reset:counters-9 calc(var(--start, 1) - 1);list-style-type:none}.drag-image-holder ol>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ol>li,en-note.peso ol>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ol>li{counter-increment:counters-9;list-style-type:none}.drag-image-holder ol>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ol>li>.list-content:before,en-note.peso ol>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ol>li>.list-content:before{content:counter(counters-9,decimal) ".";cursor:pointer;display:inline-block;font-size:15px;left:var(--bullet-left-position);line-height:var(--padded-area-height);position:absolute;text-align:right;top:0;white-space:nowrap}.drag-image-holder ul:not(.en-todolist)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ul:not(.en-todolist),en-note.peso ul:not(.en-todolist)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ul:not(.en-todolist){list-style-type:none}.drag-image-holder ul:not(.en-todolist)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ul:not(.en-todolist)>li,en-note.peso ul:not(.en-todolist)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ul:not(.en-todolist)>li{list-style-type:none}.drag-image-holder ul:not(.en-todolist)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ul:not(.en-todolist)>li>.list-content:before,en-note.peso ul:not(.en-todolist)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ul:not(.en-todolist)>li>.list-content:before{content:"•";cursor:pointer;display:inline-block;font-size:15px;left:var(--bullet-left-position);line-height:var(--padded-area-height);position:absolute;text-align:right;top:0;white-space:nowrap}.drag-image-holder ol>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ol,en-note.peso ol>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ol{--start:1;counter-reset:counters-10 calc(var(--start, 1) - 1);list-style-type:none}.drag-image-holder ol>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ol>li,en-note.peso ol>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ol>li{counter-increment:counters-10;list-style-type:none}.drag-image-holder ol>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ol>li>.list-content:before,en-note.peso ol>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ol>li>.list-content:before{content:counter(counters-10,lower-alpha) ".";cursor:pointer;display:inline-block;font-size:15px;left:var(--bullet-left-position);line-height:var(--padded-area-height);position:absolute;text-align:right;top:0;white-space:nowrap}.drag-image-holder ul:not(.en-todolist)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ul:not(.en-todolist),en-note.peso ul:not(.en-todolist)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ul:not(.en-todolist){list-style-type:none}.drag-image-holder ul:not(.en-todolist)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ul:not(.en-todolist)>li,en-note.peso ul:not(.en-todolist)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ul:not(.en-todolist)>li{list-style-type:none}.drag-image-holder ul:not(.en-todolist)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ul:not(.en-todolist)>li>.list-content:before,en-note.peso ul:not(.en-todolist)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul)>:is(ol,ul) ul:not(.en-todolist)>li>.list-content:before{content:"◦";cursor:pointer;display:inline-block;font-size:15px;left:var(--bullet-left-position);line-height:var(--padded-area-height);position:absolute;text-align:right;top:0;white-space:nowrap}body:not(.firefox) .drag-image-holder.selected,body:not(.firefox) en-note.peso.selected{-webkit-user-select:none;-moz-user-select:none;user-select:none}.drag-image-holder ol,.drag-image-holder ul:not(.en-todolist),en-note.peso ol,en-note.peso ul:not(.en-todolist){--bullet-shape-dimension:6px}.drag-image-holder ol dl,.drag-image-holder ol ol,.drag-image-holder ol ul,.drag-image-holder ul:not(.en-todolist) dl,.drag-image-holder ul:not(.en-todolist) ol,.drag-image-holder ul:not(.en-todolist) ul,en-note.peso ol dl,en-note.peso ol ol,en-note.peso ol ul,en-note.peso ul:not(.en-todolist) dl,en-note.peso ul:not(.en-todolist) ol,en-note.peso ul:not(.en-todolist) ul{padding-left:calc(var(--bullet-content-padding) + var(--bullet-area-width))}.drag-image-holder ol>li[data-display-value-length="2"],.drag-image-holder ul:not(.en-todolist)>li[data-display-value-length="2"],en-note.peso ol>li[data-display-value-length="2"],en-note.peso ul:not(.en-todolist)>li[data-display-value-length="2"]{--bullet-area-width:24px}.drag-image-holder ol>li[data-display-value-length="3"],.drag-image-holder ul:not(.en-todolist)>li[data-display-value-length="3"],en-note.peso ol>li[data-display-value-length="3"],en-note.peso ul:not(.en-todolist)>li[data-display-value-length="3"]{--bullet-area-width:32px;--padded-area-width:calc(var(--bullet-area-width) + var(--bullet-content-padding))}.drag-image-holder ol>li[data-display-value-length="4"],.drag-image-holder ul:not(.en-todolist)>li[data-display-value-length="4"],en-note.peso ol>li[data-display-value-length="4"],en-note.peso ul:not(.en-todolist)>li[data-display-value-length="4"]{--bullet-area-width:42px;--padded-area-width:calc(var(--bullet-area-width) + var(--bullet-content-padding))}.drag-image-holder ol>li[data-display-value-length="5"],.drag-image-holder ul:not(.en-todolist)>li[data-display-value-length="5"],en-note.peso ol>li[data-display-value-length="5"],en-note.peso ul:not(.en-todolist)>li[data-display-value-length="5"]{--bullet-area-width:52px;--padded-area-width:calc(var(--bullet-area-width) + var(--bullet-content-padding))}.drag-image-holder ol>li[data-display-value-length="6"],.drag-image-holder ul:not(.en-todolist)>li[data-display-value-length="6"],en-note.peso ol>li[data-display-value-length="6"],en-note.peso ul:not(.en-todolist)>li[data-display-value-length="6"]{--bullet-area-width:62px;--padded-area-width:calc(var(--bullet-area-width) + var(--bullet-content-padding))}.drag-image-holder ol>li[data-display-value-length="7"],.drag-image-holder ul:not(.en-todolist)>li[data-display-value-length="7"],en-note.peso ol>li[data-display-value-length="7"],en-note.peso ul:not(.en-todolist)>li[data-display-value-length="7"]{--bullet-area-width:72px;--padded-area-width:calc(var(--bullet-area-width) + var(--bullet-content-padding))}.drag-image-holder ol>li[data-display-value-length="8"],.drag-image-holder ul:not(.en-todolist)>li[data-display-value-length="8"],en-note.peso ol>li[data-display-value-length="8"],en-note.peso ul:not(.en-todolist)>li[data-display-value-length="8"]{--bullet-area-width:82px;--padded-area-width:calc(var(--bullet-area-width) + var(--bullet-content-padding))}.drag-image-holder ol>li[data-display-value-length="9"],.drag-image-holder ul:not(.en-todolist)>li[data-display-value-length="9"],en-note.peso ol>li[data-display-value-length="9"],en-note.peso ul:not(.en-todolist)>li[data-display-value-length="9"]{--bullet-area-width:92px;--padded-area-width:calc(var(--bullet-area-width) + var(--bullet-content-padding))}.drag-image-holder ol>li[data-display-value-length="10"],.drag-image-holder ul:not(.en-todolist)>li[data-display-value-length="10"],en-note.peso ol>li[data-display-value-length="10"],en-note.peso ul:not(.en-todolist)>li[data-display-value-length="10"]{--bullet-area-width:102px;--padded-area-width:calc(var(--bullet-area-width) + var(--bullet-content-padding))}.drag-image-holder ol>li[data-display-value-length="11"],.drag-image-holder ul:not(.en-todolist)>li[data-display-value-length="11"],en-note.peso ol>li[data-display-value-length="11"],en-note.peso ul:not(.en-todolist)>li[data-display-value-length="11"]{--bullet-area-width:112px;--padded-area-width:calc(var(--bullet-area-width) + var(--bullet-content-padding))}.drag-image-holder ol>li[data-display-value-length="12"],.drag-image-holder ul:not(.en-todolist)>li[data-display-value-length="12"],en-note.peso ol>li[data-display-value-length="12"],en-note.peso ul:not(.en-todolist)>li[data-display-value-length="12"]{--bullet-area-width:122px;--padded-area-width:calc(var(--bullet-area-width) + var(--bullet-content-padding))}.drag-image-holder ol>li[data-display-value-length="13"],.drag-image-holder ul:not(.en-todolist)>li[data-display-value-length="13"],en-note.peso ol>li[data-display-value-length="13"],en-note.peso ul:not(.en-todolist)>li[data-display-value-length="13"]{--bullet-area-width:132px;--padded-area-width:calc(var(--bullet-area-width) + var(--bullet-content-padding))}.drag-image-holder ol>li[data-display-value-length="14"],.drag-image-holder ul:not(.en-todolist)>li[data-display-value-length="14"],en-note.peso ol>li[data-display-value-length="14"],en-note.peso ul:not(.en-todolist)>li[data-display-value-length="14"]{--bullet-area-width:142px;--padded-area-width:calc(var(--bullet-area-width) + var(--bullet-content-padding))}.drag-image-holder ol>li[data-display-value-length="15"],.drag-image-holder ul:not(.en-todolist)>li[data-display-value-length="15"],en-note.peso ol>li[data-display-value-length="15"],en-note.peso ul:not(.en-todolist)>li[data-display-value-length="15"]{--bullet-area-width:152px;--padded-area-width:calc(var(--bullet-area-width) + var(--bullet-content-padding))}.drag-image-holder ol>li[data-display-value-length="16"],.drag-image-holder ul:not(.en-todolist)>li[data-display-value-length="16"],en-note.peso ol>li[data-display-value-length="16"],en-note.peso ul:not(.en-todolist)>li[data-display-value-length="16"]{--bullet-area-width:162px;--padded-area-width:calc(var(--bullet-area-width) + var(--bullet-content-padding))}body.ui-revamp .drag-image-holder ol>li .list-content .para,body.ui-revamp .drag-image-holder ul:not(.en-todolist)>li .list-content .para,body.ui-revamp en-note.peso ol>li .list-content .para,body.ui-revamp en-note.peso ul:not(.en-todolist)>li .list-content .para{margin-left:0}.drag-image-holder .list-bullet-todo,en-note.peso .list-bullet-todo{display:none}.drag-image-holder ul.en-todolist,en-note.peso ul.en-todolist{--bullet-shape-dimension:16px}.drag-image-holder ul.en-todolist dl,.drag-image-holder ul.en-todolist ol,.drag-image-holder ul.en-todolist ul,en-note.peso ul.en-todolist dl,en-note.peso ul.en-todolist ol,en-note.peso ul.en-todolist ul{padding-left:calc(var(--bullet-content-padding) + var(--bullet-area-width))}.drag-image-holder ul.en-todolist>li,en-note.peso ul.en-todolist>li{list-style:none}@media(pointer:coarse){.drag-image-holder ul.en-todolist>li .list-bullet-todo-container,en-note.peso ul.en-todolist>li .list-bullet-todo-container{--toucheable-area-height:var(--padded-area-height);--toucheable-area-width:40px;--toucheable-area-top:-1px;--toucheable-area-left:-3px}.drag-image-holder ul.en-todolist>li .list-bullet-todo-container:after,en-note.peso ul.en-todolist>li .list-bullet-todo-container:after{content:"";display:inline-block;left:var(--toucheable-area-left);min-height:var(--toucheable-area-height);min-width:var(--toucheable-area-width);position:absolute;top:var(--toucheable-area-top)}}.drag-image-holder ul.en-todolist>li .list-bullet-todo,en-note.peso ul.en-todolist>li .list-bullet-todo{-moz-appearance:none;appearance:none;-webkit-appearance:none;background:#fff none;border:1px solid #a6a6a6;border-radius:3px;display:inline-block;height:var(--bullet-shape-dimension);left:var(--bullet-content-padding);margin:0;outline:none;padding:0;position:absolute;text-align:right;top:calc((var(--padded-area-height) - var(--bullet-shape-dimension))/2);white-space:nowrap;width:var(--bullet-shape-dimension)}body.darkMode .drag-image-holder ul.en-todolist>li .list-bullet-todo,body.darkMode en-note.peso ul.en-todolist>li .list-bullet-todo{background:#262626 none;border-color:#737373}body.ui-revamp .drag-image-holder ul.en-todolist>li .list-bullet-todo,body.ui-revamp en-note.peso ul.en-todolist>li .list-bullet-todo{--bullet-shape-dimension:17px;--bullet-area-width:17px;background:transparent;border-color:#a6a6a6;height:17px;top:calc((var(--padded-area-height) - var(--bullet-shape-dimension))/2);width:17px}body.ui-revamp .drag-image-holder ul.en-todolist>li .list-content .para,body.ui-revamp en-note.peso ul.en-todolist>li .list-content .para{margin-left:0}.drag-image-holder ul.en-todolist>li[data-checked=true] .list-bullet-todo,en-note.peso ul.en-todolist>li[data-checked=true] .list-bullet-todo{background:#fff url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxNCAxNCI+PHBhdGggZmlsbD0ibm9uZSIgc3Ryb2tlPSIjMDBBODJEIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIHN0cm9rZS13aWR0aD0iMiIgZD0iTTMgNy44NTcgNS4zNzUgMTAgMTEgNCIvPjwvc3ZnPg==);border-color:#d9d9d9}body.darkMode .drag-image-holder ul.en-todolist>li[data-checked=true] .list-bullet-todo,body.darkMode en-note.peso ul.en-todolist>li[data-checked=true] .list-bullet-todo{background:#262626 url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxNCAxNCI+PHBhdGggZmlsbD0ibm9uZSIgc3Ryb2tlPSIjMDBBODJEIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIHN0cm9rZS13aWR0aD0iMiIgZD0iTTMgNy44NTcgNS4zNzUgMTAgMTEgNCIvPjwvc3ZnPg==);border-color:#4d4d4d}body.ui-revamp .drag-image-holder ul.en-todolist>li[data-checked=true] .list-bullet-todo,body.ui-revamp en-note.peso ul.en-todolist>li[data-checked=true] .list-bullet-todo{background:url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgZmlsbD0ibm9uZSI+PHBhdGggZmlsbD0iIzRENjRGRiIgZmlsbC1ydWxlPSJldmVub2RkIiBkPSJNMy45NCAxLjY2N2EyLjI3MyAyLjI3MyAwIDAgMC0yLjI3MiAyLjI3MnYxMi4xMjJhMi4yNzMgMi4yNzMgMCAwIDAgMi4yNzMgMi4yNzJoMTIuMTJhMi4yNzMgMi4yNzMgMCAwIDAgMi4yNzQtMi4yNzJWMy45MzlhMi4yNzMgMi4yNzMgMCAwIDAtMi4yNzMtMi4yNzJIMy45NFptMTEuMDA1IDQuNjE2YS42ODIuNjgyIDAgMCAwLS45NjIuMDdsLTUuMDE1IDUuNzkzLTIuMDg1LTIuNDFhLjY4Mi42ODIgMCAxIDAtMS4wMzIuODkzbDIuNjAyIDMuMDA1YS42ODIuNjgyIDAgMCAwIDEuMDMgMGw1LjUzLTYuMzlhLjY4Mi42ODIgMCAwIDAtLjA2OC0uOTYxWiIgY2xpcC1ydWxlPSJldmVub2RkIi8+PC9zdmc+);background-position:50%;background-repeat:no-repeat;background-size:auto;border:1px solid var(--color-surface-fill-secondarybrand-enabled)}@media print{.drag-image-holder ul.en-todolist>li[data-checked=true] .list-bullet-todo,en-note.peso ul.en-todolist>li[data-checked=true] .list-bullet-todo{-webkit-print-color-adjust:exact!important;print-color-adjust:exact!important}}.drag-image-holder.editable ul.en-todolist>li .list-bullet-todo,en-note.peso.editable ul.en-todolist>li .list-bullet-todo{cursor:pointer}.drag-image-holder.editable ul.en-todolist>li .list-bullet-todo:hover,en-note.peso.editable ul.en-todolist>li .list-bullet-todo:hover{background-color:#f2f2f2;transition:background-color .15s,border-color .15s}body.darkMode .drag-image-holder.editable ul.en-todolist>li .list-bullet-todo:hover,body.darkMode en-note.peso.editable ul.en-todolist>li .list-bullet-todo:hover{background-color:#333}.drag-image-holder.editable ul.en-todolist>li[data-checked=true] .list-bullet-todo:hover,en-note.peso.editable ul.en-todolist>li[data-checked=true] .list-bullet-todo:hover{background-color:#f2f2f2;border-color:#a6a6a6}body.darkMode .drag-image-holder.editable ul.en-todolist>li[data-checked=true] .list-bullet-todo:hover,body.darkMode en-note.peso.editable ul.en-todolist>li[data-checked=true] .list-bullet-todo:hover{background-color:#333}.drag-image-holder li,en-note.peso li{--drag-handle-left-position:calc(var(--bullet-left-position) - var(--drag-handle-dimension) - var(--drag-handle-bullet-padding))}.drag-image-holder li .list-drag-handle,en-note.peso li .list-drag-handle{cursor:grab;height:var(--drag-handle-dimension);left:var(--drag-handle-left-position);position:absolute;top:calc((var(--padded-area-height) - var(--drag-handle-dimension))/2);width:var(--drag-handle-dimension)}.drag-image-holder li .list-drag-handle>svg,en-note.peso li .list-drag-handle>svg{fill:currentColor;display:block;height:100%;opacity:.25;transition:opacity .2s ease-in-out;width:100%}.drag-image-holder li .list-drag-handle:hover>svg,en-note.peso li .list-drag-handle:hover>svg{opacity:.4}@media print{.drag-image-holder li .list-drag-handle,en-note.peso li .list-drag-handle{display:none}}.drag-image-holder li.ProseMirror-selectednode,en-note.peso li.ProseMirror-selectednode{outline:none}.drag-image-holder li.ProseMirror-selectednode:after,en-note.peso li.ProseMirror-selectednode:after{border:1px solid #66b3da;border-radius:4px;bottom:-4px;box-shadow:0 3px 10px rgba(0,0,0,.15);content:"";left:calc(var(--bullet-left-position) - var(--drag-handle-bullet-padding));pointer-events:none;position:absolute;right:-4px;top:-4px}body.darkMode .drag-image-holder li.ProseMirror-selectednode:after,body.darkMode en-note.peso li.ProseMirror-selectednode:after{border-color:#02588e}.drag-image-holder .list-parent-child-dragSelection,en-note.peso .list-parent-child-dragSelection{border:1px solid #66b3da;border-radius:4px;bottom:0;box-shadow:0 3px 10px rgba(0,0,0,.15);left:0;pointer-events:none;position:absolute;right:0;top:0;z-index:0}body.darkMode .drag-image-holder .list-parent-child-dragSelection,body.darkMode en-note.peso .list-parent-child-dragSelection{border-color:#02588e}.drag-image-holder li[style*="direction: rtl"],.drag-image-holder li[style*="direction:rtl"],en-note.peso li[style*="direction: rtl"],en-note.peso li[style*="direction:rtl"]{direction:rtl;margin-left:-30px;margin-right:30px}body.peso-dragging en-note.peso .list-drag-handle,body.peso-dragging en-note.peso .list-parent-child-dragSelection{display:none}body.peso-dragging en-note.peso li.ProseMirror-selectednode,body.peso-dragging en-note.peso li.selected{opacity:.35}body.peso-dragging en-note.peso li.ProseMirror-selectednode:after,body.peso-dragging en-note.peso li.selected:after{display:none}.drag-image-holder .ghost__lists,en-note.peso.peso .ghost__lists{background-color:#fff;border:1px solid #66b3da;border-radius:4px;box-shadow:0 3px 10px rgba(0,0,0,.15);margin:0;overflow:hidden;padding:4px 4px 4px 0;pointer-events:none}body.darkMode .drag-image-holder .ghost__lists,body.darkMode en-note.peso.peso .ghost__lists{background-color:#262626;border-color:#02588e}.drag-image-holder .ghost__lists li,en-note.peso.peso .ghost__lists li{max-height:100%;max-width:100%}.drag-image-holder .ghost__lists li .list-content div,en-note.peso.peso .ghost__lists li .list-content div{line-height:1.5;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.drag-image-holder ol.ghost__lists,en-note.peso.peso ol.ghost__lists{padding-left:2px}body[data-checklist-completion-styles=true] .drag-image-holder ul.en-todolist>li[data-checked=true]>.list-content h1,body[data-checklist-completion-styles=true] .drag-image-holder ul.en-todolist>li[data-checked=true]>.list-content h2,body[data-checklist-completion-styles=true] .drag-image-holder ul.en-todolist>li[data-checked=true]>.list-content h3,body[data-checklist-completion-styles=true] .drag-image-holder ul.en-todolist>li[data-checked=true]>.list-content h4,body[data-checklist-completion-styles=true] .drag-image-holder ul.en-todolist>li[data-checked=true]>.list-content h5,body[data-checklist-completion-styles=true] .drag-image-holder ul.en-todolist>li[data-checked=true]>.list-content h6,body[data-checklist-completion-styles=true] .drag-image-holder ul.en-todolist>li[data-checked=true]>.list-content>.para,body[data-checklist-completion-styles=true] en-note.peso.peso ul.en-todolist>li[data-checked=true]>.list-content h1,body[data-checklist-completion-styles=true] en-note.peso.peso ul.en-todolist>li[data-checked=true]>.list-content h2,body[data-checklist-completion-styles=true] en-note.peso.peso ul.en-todolist>li[data-checked=true]>.list-content h3,body[data-checklist-completion-styles=true] en-note.peso.peso ul.en-todolist>li[data-checked=true]>.list-content h4,body[data-checklist-completion-styles=true] en-note.peso.peso ul.en-todolist>li[data-checked=true]>.list-content h5,body[data-checklist-completion-styles=true] en-note.peso.peso ul.en-todolist>li[data-checked=true]>.list-content h6,body[data-checklist-completion-styles=true] en-note.peso.peso ul.en-todolist>li[data-checked=true]>.list-content>.para{text-decoration:line-through}body[data-checklist-completion-styles=true] .drag-image-holder ul.en-todolist>li[data-checked=true]>.list-content>*,body[data-checklist-completion-styles=true] en-note.peso.peso ul.en-todolist>li[data-checked=true]>.list-content>*{opacity:.55}body[data-checklist-completion-styles=true] .drag-image-holder ul.en-todolist>li[data-checked=true]>.list-content .list-drag-handle,body[data-checklist-completion-styles=true] .drag-image-holder ul.en-todolist>li[data-checked=true]>.list-content>li,body[data-checklist-completion-styles=true] .drag-image-holder ul.en-todolist>li[data-checked=true]>.list-content>ol,body[data-checklist-completion-styles=true] .drag-image-holder ul.en-todolist>li[data-checked=true]>.list-content>ul,body[data-checklist-completion-styles=true] en-note.peso.peso ul.en-todolist>li[data-checked=true]>.list-content .list-drag-handle,body[data-checklist-completion-styles=true] en-note.peso.peso ul.en-todolist>li[data-checked=true]>.list-content>li,body[data-checklist-completion-styles=true] en-note.peso.peso ul.en-todolist>li[data-checked=true]>.list-content>ol,body[data-checklist-completion-styles=true] en-note.peso.peso ul.en-todolist>li[data-checked=true]>.list-content>ul{opacity:1;text-decoration:none}en-note.peso>address,en-note.peso>blockquote,en-note.peso>center,en-note.peso>dd,en-note.peso>div,en-note.peso>dt,en-note.peso>p{margin:0;padding:0}en-note.peso address,en-note.peso blockquote,en-note.peso center,en-note.peso dd,en-note.peso div,en-note.peso dt,en-note.peso p{line-height:1.5}en-note.peso blockquote{margin:0;padding-left:20px}en-note.peso .en-media-image{display:flex;position:relative;-webkit-user-select:all;-moz-user-select:all;user-select:all}.android-unselectable en-note.peso .en-media-image,.android-unselectable en-note.peso .en-media-image *{-webkit-user-select:none!important;-moz-user-select:none!important;user-select:none!important}en-note.peso .en-media-image--align-center{justify-content:center}en-note.peso .en-media-image--align-left{display:inline-flex;justify-content:flex-start}en-note.peso .en-media-image--align-right{justify-content:flex-end}en-note.peso .en-media-image--align-fullWidth{justify-content:center}en-note.peso .en-media-image--align-fullWidth .pfar2,en-note.peso .en-media-image--align-fullWidth .x3xxV{width:100%}en-note.peso .en-media-image+.para,en-note.peso .para+.en-media-image{margin-top:12px}body.ui-revamp en-note.peso .para+.en-media-image{margin:var(--spacing-1-5) 0}en-note.peso h1+.en-media-image{margin-top:12px}body.ui-revamp en-note.peso h1+.en-media-image{margin:var(--spacing-1-5) 0}en-note.peso h2+.en-media-image{margin-top:12px}body.ui-revamp en-note.peso h2+.en-media-image{margin:var(--spacing-1-5) 0}en-note.peso h3+.en-media-image{margin-top:12px}body.ui-revamp en-note.peso h3+.en-media-image{margin:var(--spacing-1-5) 0}en-note.peso h4+.en-media-image{margin-top:12px}body.ui-revamp en-note.peso h4+.en-media-image{margin:var(--spacing-1-5) 0}en-note.peso h5+.en-media-image{margin-top:12px}body.ui-revamp en-note.peso h5+.en-media-image{margin:var(--spacing-1-5) 0}en-note.peso h6+.en-media-image{margin-top:12px}body.ui-revamp en-note.peso h6+.en-media-image{margin:var(--spacing-1-5) 0}en-note.peso .en-media-image+.en-media-image{margin-top:12px}body.ui-revamp en-note.peso .en-media-image+.en-media-image{margin:var(--spacing-1-5) 0}en-note.peso .pfar2.JRPI_{position:relative}.CAW42{display:inline-block;font-size:0;line-height:0}.CAW42,.CAW42 svg{height:0;width:0}.x3xxV{border:2px solid transparent;height:auto;max-width:100%;vertical-align:bottom}.pfar2.JRPI_ .x3xxV{cursor:zoom-in}.pfar2.arbp4 .x3xxV{display:none}.editing-locked .x3xxV{border-color:var(--selection-border-color-light)}body.darkMode .editing-locked .x3xxV{border-color:var(--selection-border-color-dark)}.OtXjA{animation:GJSRM 2s linear infinite;border:3px solid #f3f3f3;border-radius:50%;border-top-color:#1e83bd;display:none;height:20px;left:2px;position:absolute;top:2px;width:20px}.pfar2.arbp4 .OtXjA{display:block}.pfar2.arbp4{height:24px;width:24px}.K1MFg{bottom:2px;left:2px;right:2px;top:2px;z-index:2}.K1MFg,.K1MFg .accentuate{pointer-events:none;position:absolute}.K1MFg .accentuate{display:block;opacity:.6;z-index:1}.KU37S{--frame-color:#66b3da;border:2px solid var(--frame-color);height:100%;left:0;opacity:0;pointer-events:none;position:absolute;top:0;width:100%;z-index:2}@media print{.KU37S{opacity:0!important}}body.android .KU37S,body.android .KU37S:before{-webkit-user-select:all;-moz-user-select:all;user-select:all}body.android .KU37S:before{content:" "}body.ui-revamp .KU37S{--frame-color:#4d64ff}.collaborator-selected .KU37S{--frame-color:var(--selection-border-color-light);opacity:1}.darkMode .collaborator-selected .KU37S{--frame-color:var(--selection-border-color-dark)}.pfar2.JRPI_ .KU37S{--frame-color:#66b3da;opacity:1}.darkMode .pfar2.JRPI_ .KU37S{--frame-color:#00a3f4}.ui-revamp .pfar2.JRPI_ .KU37S{--frame-color:var(--color-text-fill-brandsecondary-enabled)}.KU37S .pfar2.sKdfI{background:rgba(68,205,119,.16)}.nIdyX{background-color:var(--frame-color);bottom:-6px;cursor:nwse-resize;height:10px;opacity:0;position:absolute;right:-6px;width:10px}.pfar2.JRPI_ .nIdyX{opacity:1;pointer-events:auto}@media print{.pfar2.JRPI_ .nIdyX{opacity:0}}body.neutron .nIdyX{display:none!important}en-note.peso:not(.editable) .nIdyX{display:none}.qTUSS{position:relative}.qTUSS img{border:2px solid #66b3da;margin:0;max-height:150px;max-width:150px}.qTUSS .omIGf{background-color:#0081c2;border-radius:100%;color:#fff;font-size:14px;height:20px;line-height:20px;position:absolute;right:-10px;text-align:center;top:-10px;width:20px}.darkMode .qTUSS .omIGf{background-color:#00a3f4}.ui-revamp .qTUSS .omIGf{background-color:var(--color-text-fill-brandsecondary-enabled)}.S2YSb{fill:#666;margin-right:10px}.S2YSb,.zxR2A{height:24px;width:24px}@keyframes GJSRM{0%{transform:rotate(0deg)}to{transform:rotate(1turn)}}:export{tableCellGapSize:1px}en-note.peso.resize-cursor{cursor:ew-resize;cursor:col-resize}en-note.peso .column-resize-handle{background-color:#0081c2;bottom:0;pointer-events:none;position:absolute;right:-1px;top:0;width:2px;z-index:20}.drag-image-holder en-table,en-note.peso en-table{display:block;margin:0;padding:12px 0 16px;position:relative}.drag-image-holder en-table body:not(.neutron),en-note.peso en-table body:not(.neutron){transition:margin-left .1s,margin-right .1s}body.neutron .drag-image-holder en-table.active,body.neutron en-note.peso en-table.active{margin-left:20px}body.ui-revamp .drag-image-holder en-table,body.ui-revamp en-note.peso en-table{padding-left:8px;padding-right:8px}.drag-image-holder en-table:before,en-note.peso en-table:before{display:none}.drag-image-holder en-table .outline,en-note.peso en-table .outline{border:1px solid var(--border-color-lightmode);bottom:16px;left:0;pointer-events:none;position:absolute;right:0;top:12px;z-index:-1}body.darkMode .drag-image-holder en-table .outline,body.darkMode en-note.peso en-table .outline{border-color:var(--border-color-darkmode)}@media print{.drag-image-holder en-table .outline,en-note.peso en-table .outline{display:none!important}}.drag-image-holder en-table.collaborator-selected ::-moz-selection,.drag-image-holder en-table.collaborator-selected::-moz-selection,.drag-image-holder en-table.multirange ::-moz-selection,.drag-image-holder en-table.multirange::-moz-selection,.drag-image-holder en-table.selected ::-moz-selection,.drag-image-holder en-table.selected::-moz-selection,en-note.peso en-table.collaborator-selected ::-moz-selection,en-note.peso en-table.collaborator-selected::-moz-selection,en-note.peso en-table.multirange ::-moz-selection,en-note.peso en-table.multirange::-moz-selection,en-note.peso en-table.selected ::-moz-selection,en-note.peso en-table.selected::-moz-selection{background-color:transparent!important}.drag-image-holder en-table.collaborator-selected .sharedCursorTextSelection,.drag-image-holder en-table.collaborator-selected ::selection,.drag-image-holder en-table.collaborator-selected::selection,.drag-image-holder en-table.multirange .sharedCursorTextSelection,.drag-image-holder en-table.multirange ::selection,.drag-image-holder en-table.multirange::selection,.drag-image-holder en-table.selected .sharedCursorTextSelection,.drag-image-holder en-table.selected ::selection,.drag-image-holder en-table.selected::selection,en-note.peso en-table.collaborator-selected .sharedCursorTextSelection,en-note.peso en-table.collaborator-selected ::selection,en-note.peso en-table.collaborator-selected::selection,en-note.peso en-table.multirange .sharedCursorTextSelection,en-note.peso en-table.multirange ::selection,en-note.peso en-table.multirange::selection,en-note.peso en-table.selected .sharedCursorTextSelection,en-note.peso en-table.selected ::selection,en-note.peso en-table.selected::selection{background-color:transparent!important}.drag-image-holder en-table.collaborator-selected:before,.drag-image-holder en-table.selected:before,en-note.peso en-table.collaborator-selected:before,en-note.peso en-table.selected:before{border:2px solid #0081c2;z-index:5}.drag-image-holder en-table.collaborator-selected td:after,.drag-image-holder en-table.collaborator-selected th:after,.drag-image-holder en-table.selected td:after,.drag-image-holder en-table.selected th:after,en-note.peso en-table.collaborator-selected td:after,en-note.peso en-table.collaborator-selected th:after,en-note.peso en-table.selected td:after,en-note.peso en-table.selected th:after{z-index:50}.drag-image-holder en-table.collaborator-selected ui-table,.drag-image-holder en-table.selected ui-table,en-note.peso en-table.collaborator-selected ui-table,en-note.peso en-table.selected ui-table{display:none}@media print{.drag-image-holder en-table.collaborator-selected:before,.drag-image-holder en-table.selected:before,en-note.peso en-table.collaborator-selected:before,en-note.peso en-table.selected:before{display:none!important}}.drag-image-holder en-table td.collaborator-selectedCell,.drag-image-holder en-table td.selectedCell,.drag-image-holder en-table th.collaborator-selectedCell,.drag-image-holder en-table th.selectedCell,.drag-image-holder en-table.collaborator-selected td,.drag-image-holder en-table.collaborator-selected th,.drag-image-holder en-table.selected td,.drag-image-holder en-table.selected th,en-note.peso en-table td.collaborator-selectedCell,en-note.peso en-table td.selectedCell,en-note.peso en-table th.collaborator-selectedCell,en-note.peso en-table th.selectedCell,en-note.peso en-table.collaborator-selected td,en-note.peso en-table.collaborator-selected th,en-note.peso en-table.selected td,en-note.peso en-table.selected th{outline-color:transparent;position:relative}.drag-image-holder en-table td.collaborator-selectedCell:after,.drag-image-holder en-table td.selectedCell:after,.drag-image-holder en-table th.collaborator-selectedCell:after,.drag-image-holder en-table th.selectedCell:after,.drag-image-holder en-table.collaborator-selected td:after,.drag-image-holder en-table.collaborator-selected th:after,.drag-image-holder en-table.selected td:after,.drag-image-holder en-table.selected th:after,en-note.peso en-table td.collaborator-selectedCell:after,en-note.peso en-table td.selectedCell:after,en-note.peso en-table th.collaborator-selectedCell:after,en-note.peso en-table th.selectedCell:after,en-note.peso en-table.collaborator-selected td:after,en-note.peso en-table.collaborator-selected th:after,en-note.peso en-table.selected td:after,en-note.peso en-table.selected th:after{bottom:0;content:"";left:0;outline-style:solid;outline-width:1px;pointer-events:none;position:absolute;right:0;top:0;z-index:1}.drag-image-holder en-table td.collaborator-selectedCell img,.drag-image-holder en-table td.selectedCell img,.drag-image-holder en-table th.collaborator-selectedCell img,.drag-image-holder en-table th.selectedCell img,.drag-image-holder en-table.collaborator-selected td img,.drag-image-holder en-table.collaborator-selected th img,.drag-image-holder en-table.selected td img,.drag-image-holder en-table.selected th img,en-note.peso en-table td.collaborator-selectedCell img,en-note.peso en-table td.selectedCell img,en-note.peso en-table th.collaborator-selectedCell img,en-note.peso en-table th.selectedCell img,en-note.peso en-table.collaborator-selected td img,en-note.peso en-table.collaborator-selected th img,en-note.peso en-table.selected td img,en-note.peso en-table.selected th img{pointer-events:none}@media print{.drag-image-holder en-table td.collaborator-selectedCell,.drag-image-holder en-table td.selectedCell,.drag-image-holder en-table th.collaborator-selectedCell,.drag-image-holder en-table th.selectedCell,.drag-image-holder en-table.collaborator-selected td,.drag-image-holder en-table.collaborator-selected th,.drag-image-holder en-table.selected td,.drag-image-holder en-table.selected th,en-note.peso en-table td.collaborator-selectedCell,en-note.peso en-table td.selectedCell,en-note.peso en-table th.collaborator-selectedCell,en-note.peso en-table th.selectedCell,en-note.peso en-table.collaborator-selected td,en-note.peso en-table.collaborator-selected th,en-note.peso en-table.selected td,en-note.peso en-table.selected th{border-color:#ccc}.drag-image-holder en-table td.collaborator-selectedCell:after,.drag-image-holder en-table td.selectedCell:after,.drag-image-holder en-table th.collaborator-selectedCell:after,.drag-image-holder en-table th.selectedCell:after,.drag-image-holder en-table.collaborator-selected td:after,.drag-image-holder en-table.collaborator-selected th:after,.drag-image-holder en-table.selected td:after,.drag-image-holder en-table.selected th:after,en-note.peso en-table td.collaborator-selectedCell:after,en-note.peso en-table td.selectedCell:after,en-note.peso en-table th.collaborator-selectedCell:after,en-note.peso en-table th.selectedCell:after,en-note.peso en-table.collaborator-selected td:after,en-note.peso en-table.collaborator-selected th:after,en-note.peso en-table.selected td:after,en-note.peso en-table.selected th:after{display:none}}.drag-image-holder en-table td.collaborator-selectedCell:after,.drag-image-holder en-table th.collaborator-selectedCell:after,.drag-image-holder en-table.collaborator-selected td:after,.drag-image-holder en-table.collaborator-selected th:after,en-note.peso en-table td.collaborator-selectedCell:after,en-note.peso en-table th.collaborator-selectedCell:after,en-note.peso en-table.collaborator-selected td:after,en-note.peso en-table.collaborator-selected th:after{background-color:var(--table-background-selected);outline-color:var(--table-border-color-selected)}body.darkMode .drag-image-holder en-table td.collaborator-selectedCell:after,body.darkMode .drag-image-holder en-table th.collaborator-selectedCell:after,body.darkMode .drag-image-holder en-table.collaborator-selected td:after,body.darkMode .drag-image-holder en-table.collaborator-selected th:after,body.darkMode en-note.peso en-table td.collaborator-selectedCell:after,body.darkMode en-note.peso en-table th.collaborator-selectedCell:after,body.darkMode en-note.peso en-table.collaborator-selected td:after,body.darkMode en-note.peso en-table.collaborator-selected th:after{background-color:var(--table-background-selected-dark);outline-color:var(--table-border-color-selected-dark)}body.ui-revamp .drag-image-holder en-table td.collaborator-selectedCell:after,body.ui-revamp .drag-image-holder en-table th.collaborator-selectedCell:after,body.ui-revamp .drag-image-holder en-table.collaborator-selected td:after,body.ui-revamp .drag-image-holder en-table.collaborator-selected th:after,body.ui-revamp en-note.peso en-table td.collaborator-selectedCell:after,body.ui-revamp en-note.peso en-table th.collaborator-selectedCell:after,body.ui-revamp en-note.peso en-table.collaborator-selected td:after,body.ui-revamp en-note.peso en-table.collaborator-selected th:after{background-color:var(--color-surface-fill-secondarybrand-enabled);outline-color:var(--color-surface-fill-secondarybrand-enabled)}.drag-image-holder en-table td.selectedCell:after,.drag-image-holder en-table th.selectedCell:after,.drag-image-holder en-table.selected td:after,.drag-image-holder en-table.selected th:after,en-note.peso en-table td.selectedCell:after,en-note.peso en-table th.selectedCell:after,en-note.peso en-table.selected td:after,en-note.peso en-table.selected th:after{background-color:rgba(0,129,194,.1);outline-color:#66b3da}body.darkMode .drag-image-holder en-table td.selectedCell:after,body.darkMode .drag-image-holder en-table th.selectedCell:after,body.darkMode .drag-image-holder en-table.selected td:after,body.darkMode .drag-image-holder en-table.selected th:after,body.darkMode en-note.peso en-table td.selectedCell:after,body.darkMode en-note.peso en-table th.selectedCell:after,body.darkMode en-note.peso en-table.selected td:after,body.darkMode en-note.peso en-table.selected th:after{background-color:rgba(0,163,244,.1);outline-color:#026fac}body.ui-revamp .drag-image-holder en-table td.selectedCell:after,body.ui-revamp .drag-image-holder en-table th.selectedCell:after,body.ui-revamp .drag-image-holder en-table.selected td:after,body.ui-revamp .drag-image-holder en-table.selected th:after,body.ui-revamp en-note.peso en-table td.selectedCell:after,body.ui-revamp en-note.peso en-table th.selectedCell:after,body.ui-revamp en-note.peso en-table.selected td:after,body.ui-revamp en-note.peso en-table.selected th:after{background-color:rgba(var(--color-surface-fill-secondarybrand-enabled),.1);outline-color:var(--color-surface-fill-secondarybrand-enabled)}.drag-image-holder en-table.selected .outline,en-note.peso en-table.selected .outline{border-color:#0081c2;border-width:2px;z-index:2}.drag-image-holder en-table.selecting ui-table .colline,.drag-image-holder en-table.selecting ui-table .en-cell-btn,.drag-image-holder en-table.selecting ui-table .en-imageresizer-trigger,.drag-image-holder en-table.selecting ui-table .en-table-btn,.drag-image-holder en-table.selecting ui-table .handle,.drag-image-holder en-table.selecting ui-table .insertion,.drag-image-holder en-table.selecting ui-table .scrollbar,.drag-image-holder en-table.selecting ui-table img,en-note.peso en-table.selecting ui-table .colline,en-note.peso en-table.selecting ui-table .en-cell-btn,en-note.peso en-table.selecting ui-table .en-imageresizer-trigger,en-note.peso en-table.selecting ui-table .en-table-btn,en-note.peso en-table.selecting ui-table .handle,en-note.peso en-table.selecting ui-table .insertion,en-note.peso en-table.selecting ui-table .scrollbar,en-note.peso en-table.selecting ui-table img{pointer-events:none!important}.drag-image-holder en-table .container,en-note.peso en-table .container{-webkit-overflow-scrolling:touch;max-width:inherit;overflow-x:auto;overscroll-behavior-x:contain;position:relative;width:inherit;z-index:2}body:not(.neutron) .drag-image-holder en-table .container,body:not(.neutron) en-note.peso en-table .container{scrollbar-width:none}body:not(.neutron) .drag-image-holder en-table .container::-webkit-scrollbar,body:not(.neutron) en-note.peso en-table .container::-webkit-scrollbar{display:none}body.android .drag-image-holder en-table .container__scrolling,body.android en-note.peso en-table .container__scrolling{overflow-x:overlay!important}body.android .drag-image-holder en-table .container__scrolling::-webkit-scrollbar,body.android en-note.peso en-table .container__scrolling::-webkit-scrollbar{background-color:transparent;height:7px}body.android .drag-image-holder en-table .container__scrolling::-webkit-scrollbar-thumb,body.android en-note.peso en-table .container__scrolling::-webkit-scrollbar-thumb{border:2px solid transparent;border-radius:3.5px;box-shadow:inset 0 0 7px 7px #857f7f}.drag-image-holder en-table table,en-note.peso en-table table{grid-gap:1px;display:grid;max-width:none;padding:1px;position:relative}.drag-image-holder en-table table tbody,.drag-image-holder en-table table thead,.drag-image-holder en-table table tr,en-note.peso en-table table tbody,en-note.peso en-table table thead,en-note.peso en-table table tr{display:contents}.drag-image-holder en-table table td,.drag-image-holder en-table table th,en-note.peso en-table table td,en-note.peso en-table table th{background-color:var(--background-color-lightmode);color:var(--text-color-lightmode);display:block;display:flex;flex-direction:column;grid-column:span var(--cell-colspan,1);grid-row:span var(--cell-rowspan,1);justify-content:var(--vertical-align,flex-start);margin:0;min-width:30px;outline:thin solid var(--border-color-lightmode);padding:8px;position:relative;word-break:break-word}@media print{.drag-image-holder en-table table td,.drag-image-holder en-table table th,en-note.peso en-table table td,en-note.peso en-table table th{-webkit-print-color-adjust:exact!important;print-color-adjust:exact!important}}body.darkMode .drag-image-holder en-table table td,body.darkMode .drag-image-holder en-table table th,body.darkMode en-note.peso en-table table td,body.darkMode en-note.peso en-table table th{background-color:var(--background-color-darkmode);color:var(--text-color-darkmode);outline-color:var(--border-color-darkmode)}body.ui-revamp .drag-image-holder en-table table td,body.ui-revamp .drag-image-holder en-table table th,body.ui-revamp en-note.peso en-table table td,body.ui-revamp en-note.peso en-table table th{font-size:15px;outline:thin solid var(--color-input-base-stroke-default)}.drag-image-holder en-table table h1,.drag-image-holder en-table table h2,.drag-image-holder en-table table h3,.drag-image-holder en-table table h4,.drag-image-holder en-table table h5,.drag-image-holder en-table table h6,en-note.peso en-table table h1,en-note.peso en-table table h2,en-note.peso en-table table h3,en-note.peso en-table table h4,en-note.peso en-table table h5,en-note.peso en-table table h6{margin-block-end:0;margin-block-start:0}body.ui-revamp .drag-image-holder en-table table h1,body.ui-revamp .drag-image-holder en-table table h2,body.ui-revamp .drag-image-holder en-table table h3,body.ui-revamp .drag-image-holder en-table table h4,body.ui-revamp .drag-image-holder en-table table h5,body.ui-revamp .drag-image-holder en-table table h6,body.ui-revamp en-note.peso en-table table h1,body.ui-revamp en-note.peso en-table table h2,body.ui-revamp en-note.peso en-table table h3,body.ui-revamp en-note.peso en-table table h4,body.ui-revamp en-note.peso en-table table h5,body.ui-revamp en-note.peso en-table table h6{margin:var(--spacing-0-5) var(--spacing-1)}.drag-image-holder en-table.ghost__EnTable,en-note.peso en-table.ghost__EnTable{padding:0;width:auto}.drag-image-holder en-table.ghost__EnTable>div,en-note.peso en-table.ghost__EnTable>div{border-color:#cdd8d2}.drag-image-holder en-table.ghost__EnTable>div.ghost_EnTableColumn,en-note.peso en-table.ghost__EnTable>div.ghost_EnTableColumn{height:200px;width:80px}.drag-image-holder en-table.ghost__EnTable>div.ghost_EnTableRow,en-note.peso en-table.ghost__EnTable>div.ghost_EnTableRow{height:40px;width:200px}.drag-image-holder en-table.ghost__EnTable>div.ghost_EnTableColumn:after,.drag-image-holder en-table.ghost__EnTable>div.ghost_EnTableRow:after,en-note.peso en-table.ghost__EnTable>div.ghost_EnTableColumn:after,en-note.peso en-table.ghost__EnTable>div.ghost_EnTableRow:after{background-color:#e5f2f8;border:1px solid #0081c2;bottom:-1px;content:"";left:-1px;pointer-events:none;position:absolute;right:-1px;top:-1px}body.darkMode .drag-image-holder en-table.ghost__EnTable>div.ghost_EnTableColumn:after,body.darkMode .drag-image-holder en-table.ghost__EnTable>div.ghost_EnTableRow:after,body.darkMode en-note.peso en-table.ghost__EnTable>div.ghost_EnTableColumn:after,body.darkMode en-note.peso en-table.ghost__EnTable>div.ghost_EnTableRow:after{background-color:#333;border:1px solid #026fac}.drag-image-holder en-table.ghost__EnTable>div.ghost_EnTableCount,en-note.peso en-table.ghost__EnTable>div.ghost_EnTableCount{background-color:#0081c2;border-radius:100%;color:#fff;font-size:14px;height:20px;line-height:20px;position:absolute;right:-10px;text-align:center;top:-10px;width:20px}body.darkMode .drag-image-holder en-table.ghost__EnTable>div.ghost_EnTableCount,body.darkMode en-note.peso en-table.ghost__EnTable>div.ghost_EnTableCount{background-color:#00a3f4}body.ui-revamp .drag-image-holder en-table.ghost__EnTable>div.ghost_EnTableCount,body.ui-revamp en-note.peso en-table.ghost__EnTable>div.ghost_EnTableCount{background-color:var(--color-text-fill-brandsecondary-enabled)}.drag-image-holder en-table.ghost__EnTable>div.ghost_EnTableFull,en-note.peso en-table.ghost__EnTable>div.ghost_EnTableFull{height:67px;left:0;top:0;width:172px}.drag-image-holder en-table.ghost__EnTable>div.ghost_EnTableFull .ghost_EnTableHandle,en-note.peso en-table.ghost__EnTable>div.ghost_EnTableFull .ghost_EnTableHandle{background-color:#66b3da;border-top-left-radius:6px;height:12px;position:absolute;width:12px}body.darkMode .drag-image-holder en-table.ghost__EnTable>div.ghost_EnTableFull .ghost_EnTableHandle,body.darkMode en-note.peso en-table.ghost__EnTable>div.ghost_EnTableFull .ghost_EnTableHandle{background-color:#026fac}.drag-image-holder en-table.ghost__EnTable>div.ghost_EnTableFull .ghost_EnTableSides,en-note.peso en-table.ghost__EnTable>div.ghost_EnTableFull .ghost_EnTableSides{background-color:#b2d9ec;border:1px solid #66b3da;border-radius:6px 6px 0 6px;height:67px;position:absolute;width:172px}body.darkMode .drag-image-holder en-table.ghost__EnTable>div.ghost_EnTableFull .ghost_EnTableSides,body.darkMode en-note.peso en-table.ghost__EnTable>div.ghost_EnTableFull .ghost_EnTableSides{background-color:#026fac;border-color:#026fac}body.ui-revamp .drag-image-holder en-table.ghost__EnTable>div.ghost_EnTableFull .ghost_EnTableSides,body.ui-revamp en-note.peso en-table.ghost__EnTable>div.ghost_EnTableFull .ghost_EnTableSides{background-color:var(--color-surface-fill-secondarybrand-enabled);border-color:var(--color-surface-fill-secondarybrand-enabled)}.drag-image-holder en-table.ghost__EnTable>div.ghost_EnTableFull .ghost_EnTableSides:before,en-note.peso en-table.ghost__EnTable>div.ghost_EnTableFull .ghost_EnTableSides:before{border:1px solid #66b3da;border-top-left-radius:6px;content:"";height:38px;left:-1px;position:absolute;top:-1px;width:90px}body.darkMode .drag-image-holder en-table.ghost__EnTable>div.ghost_EnTableFull .ghost_EnTableSides:before,body.darkMode en-note.peso en-table.ghost__EnTable>div.ghost_EnTableFull .ghost_EnTableSides:before{background-color:#026fac;border-color:#026fac}body.ui-revamp .drag-image-holder en-table.ghost__EnTable>div.ghost_EnTableFull .ghost_EnTableSides:before,body.ui-revamp en-note.peso en-table.ghost__EnTable>div.ghost_EnTableFull .ghost_EnTableSides:before{background-color:var(--color-surface-fill-secondarybrand-enabled);border-color:var(--color-surface-fill-secondarybrand-enabled)}.drag-image-holder en-table.ghost__EnTable>div.ghost_EnTableFull .ghost_EnTableCells,en-note.peso en-table.ghost__EnTable>div.ghost_EnTableFull .ghost_EnTableCells{bottom:0;position:absolute;right:0}.drag-image-holder en-table.ghost__EnTable .ghost_EnTableCells,en-note.peso en-table.ghost__EnTable .ghost_EnTableCells{background-color:#e5f2f8;border:1px solid #66b3da;height:55px;width:160px}body.darkMode .drag-image-holder en-table.ghost__EnTable .ghost_EnTableCells,body.darkMode en-note.peso en-table.ghost__EnTable .ghost_EnTableCells{background-color:#333;border-color:#026fac}.drag-image-holder en-table.ghost__EnTable .ghost_EnTableCells:before,en-note.peso en-table.ghost__EnTable .ghost_EnTableCells:before{border-bottom:1px solid #b2d9ec;border-right:1px solid #b2d9ec;content:"";height:calc(50% - 1px);left:0;position:absolute;top:0;width:calc(50% - 1px)}body.darkMode .drag-image-holder en-table.ghost__EnTable .ghost_EnTableCells:before,body.darkMode en-note.peso en-table.ghost__EnTable .ghost_EnTableCells:before{border-color:#026fac}.drag-image-holder en-table.ghost__EnTable .ghost_EnTableCells:after,en-note.peso en-table.ghost__EnTable .ghost_EnTableCells:after{border-left:1px solid #b2d9ec;border-top:1px solid #b2d9ec;bottom:0;content:"";height:50%;position:absolute;right:0;width:50%}body.darkMode .drag-image-holder en-table.ghost__EnTable .ghost_EnTableCells:after,body.darkMode en-note.peso en-table.ghost__EnTable .ghost_EnTableCells:after{border-color:#026fac}:export{indentWidth:30;mainCopyColor:#333;maxZIndex:2147483647;tabletSizedQuery:"(orientation: landscape) and (min-height: 400px), (orientation: portrait) and (min-height: 900px)"}.en-todo{-webkit-focus-ring-color:transparent;-moz-appearance:none;appearance:none;-webkit-appearance:none;background-color:transparent;border-width:0;display:inline-block;font-size:16px;height:1em;margin:0;outline-color:transparent;padding:0 .25em;position:relative;-webkit-user-select:all;-moz-user-select:all;user-select:all;vertical-align:middle;width:1.5em}en-note.editable .en-todo{color:currentColor;cursor:pointer}body:not(.ios):not(.ipados) en-note.editable .en-todo{user-modify:read-write;-webkit-user-modify:read-write}.en-todo *{cursor:pointer}.en-todo:before{bottom:0;content:"";display:inline-block;left:.25em;position:absolute;right:.25em;top:0}@media print{.en-todo:before{-webkit-print-color-adjust:exact!important;print-color-adjust:exact!important}}.en-todo:before{background-image:url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxNiAxNiI+PHJlY3Qgd2lkdGg9IjE1IiBoZWlnaHQ9IjE1IiB4PSIuNSIgeT0iLjUiIGZpbGw9Im5vbmUiIHN0cm9rZT0iIzI2YjU0YyIgcng9IjMiLz48L3N2Zz4=)}.en-todo[checked=true]:before{background-image:url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxNiAxNiI+PHJlY3Qgd2lkdGg9IjE2IiBoZWlnaHQ9IjE2IiBmaWxsPSIjMjZiNTRjIiByeD0iMyIvPjxwYXRoIHN0cm9rZT0iI2ZmZiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBzdHJva2Utd2lkdGg9IjIiIGQ9Im01IDExIDYtNk01IDVsNiA2Ii8+PC9zdmc+)}@media(pointer:fine){en-note.editable .en-todo:hover:after{background:rgba(0,0,0,.05) none;border-radius:.25em;bottom:0;content:"";display:inline-block;left:.25em;position:absolute;right:.25em;top:0}}@media(pointer:coarse){.en-todo{--toucheable-area-height:24px;--toucheable-area-width:1em}.en-todo:after{content:"";display:inline-block;left:-calc(var(--toucheable-area-width)/2);min-height:var(--toucheable-area-height);min-width:var(--toucheable-area-width);position:absolute;top:calc((1em - var(--toucheable-area-height))/2)}}.en-todo.ProseMirror-selectednode,.en-todo.selected{outline:none}.en-todo.ProseMirror-selectednode:after,.en-todo.selected:after{background:rgba(0,129,194,.2);border-radius:.25em;content:"";display:inline-block;height:1.25em;left:.125em;min-height:1.25em;min-width:1.25em;position:absolute;top:-.125em;width:1.25em}@media print{.en-todo.ProseMirror-selectednode:after,.en-todo.selected:after{display:none}}:global(.android-unselectable) .en-todo,:global(.android-unselectable) .en-todo *{-webkit-user-select:none!important;-moz-user-select:none!important;user-select:none!important}body.ui-revamp .en-todo{height:20px;margin-right:var(--spacing-1);vertical-align:text-top;width:20px}body.ui-revamp .en-todo:before{background-image:url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgZmlsbD0ibm9uZSI+PHBhdGggc3Ryb2tlPSIjQTZBNkE2IiBzdHJva2Utd2lkdGg9IjEuMjUiIGQ9Ik0yLjI5MyAzLjk0YzAtLjkxLjczOC0xLjY0OSAxLjY0OC0xLjY0OWgxMi4xMmMuOTEgMCAxLjY0OS43MzggMS42NDkgMS42NDhWMTYuMDZjMCAuOTEtLjczOCAxLjY0OC0xLjY0OCAxLjY0OEgzLjk0Yy0uOTEgMC0xLjY0OC0uNzM4LTEuNjQ4LTEuNjQ4VjMuOTRaIi8+PC9zdmc+);background-repeat:no-repeat;background-size:20px;left:0;right:0}body.ui-revamp .en-todo[checked=true]:before{background-image:url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgZmlsbD0ibm9uZSI+PHBhdGggZmlsbD0iIzRENjRGRiIgZmlsbC1ydWxlPSJldmVub2RkIiBkPSJNNSAzYTIgMiAwIDAgMC0yIDJ2MTRhMiAyIDAgMCAwIDIgMmgxNGEyIDIgMCAwIDAgMi0yVjVhMiAyIDAgMCAwLTItMkg1Wm0zIDVhLjc1Ljc1IDAgMCAxIDEuMDYgMGwyLjk3IDIuOTdMMTUgOGEuNzUuNzUgMCAwIDEgMS4wNiAxLjA2bC0yLjk2OSAyLjk3IDIuOTcgMi45N0EuNzUuNzUgMCAwIDEgMTUgMTYuMDZsLTIuOTctMi45NjktMi45NyAyLjk3QS43NS43NSAwIDEgMSA4IDE1bDIuOTctMi45N0w4IDkuMDZBLjc1Ljc1IDAgMCAxIDggOFoiIGNsaXAtcnVsZT0iZXZlbm9kZCIvPjwvc3ZnPg==);background-position:50%;background-repeat:no-repeat;background-size:22px;left:0;right:0}.XgC4d{--contact-default-img-bg:#ccc;--contact-default-img-head:#737373}body.darkMode .XgC4d{--contact-default-img-bg:#666;--contact-default-img-head:#393939}.XgC4d .P3x2V{margin:20px;position:relative}.XgC4d .r5Dms{fill:#737373;width:16px}body.darkMode .XgC4d .r5Dms{fill:#a6a6a6}.XgC4d .cAEyv{flex-grow:1;min-width:0;overflow:hidden;padding:0 6px;text-align:left;text-overflow:ellipsis;white-space:nowrap}.XgC4d .KPCTe{grid-gap:12px;display:grid;grid-template-columns:minmax(100px,1fr) 48px;margin-bottom:16px}.XgC4d .KPCTe .wgXWm{color:#333;font-size:20px;font-weight:600;line-height:25px}body.darkMode .XgC4d .KPCTe .wgXWm{color:#fff}.XgC4d .KPCTe .IZIE8{color:#a6a6a6;font-size:16px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}body.darkMode .XgC4d .KPCTe .IZIE8{color:#a6a6a6}.XgC4d .KPCTe .KWgel{background-size:cover;border-radius:100px;height:48px;width:48px}.XgC4d .m6tr9{grid-gap:24px;display:grid;grid-template-columns:repeat(auto-fit,minmax(185px,1fr))}.XgC4d .xgJxn{color:#a6a6a6;font-size:14px;line-height:18px;text-transform:capitalize}body.darkMode .XgC4d .xgJxn{color:#a6a6a6}.XgC4d .yaXqq{align-items:center;display:inline-flex;justify-content:space-between;width:100%}.XgC4d .Vk4vk{color:#262626;font-size:16px}.XgC4d .Vk4vk.ZnvpU{white-space:pre-line;word-break:break-all}body.darkMode .XgC4d .Vk4vk{color:#fff}.XgC4d .y3g55{overflow-x:hidden;text-overflow:ellipsis;white-space:nowrap}.XgC4d .zhLQQ{border:1px solid #f2f2f2;border-radius:3px;box-sizing:border-box;width:62px}body.darkMode .XgC4d .zhLQQ{border-width:0}.XgC4d .YC5h6{fill:#00a82d;cursor:pointer;height:20px;margin-left:22px}.XgC4d .YC5h6.VCa0x,.XgC4d .YC5h6.gL9Ki{height:26px}.XgC4d .YC5h6.F3DpQ{height:27px}.XgC4d .kexSl{padding:5px 0}.XgC4d .ni4U0{margin-top:24px}.Y9Fap{background:rgba(249,179,0,.2);border-bottom:2px solid #f9b300;display:inline}.Y9Fap:hover{background:#f9b300}en-note.peso en-table.active ui-table .EWRLR,en-note.peso en-table.active ui-table .TK9X6,en-note.peso en-table.active ui-table .txY9K,en-note.peso en-table.active ui-table .x1TEB{display:block}.android-unselectable en-table,.android-unselectable en-table *{-webkit-user-select:none!important;-moz-user-select:none!important;user-select:none!important}ui-table{--handle-dim:19px;--extended-touchable-area-dim:0px;bottom:0;display:block;left:-20px;padding:12px 20px 0;pointer-events:none;position:absolute;right:-20px;top:0}@media(pointer:coarse){ui-table{--extended-touchable-area-dim:20px}}ui-table div.kw2zt{pointer-events:all}ui-table div{line-height:1!important;vertical-align:top}@media print{ui-table{display:none!important}}body.ui-revamp ui-table{left:-12px;right:-12px}ui-table:before{bottom:16px;content:"";display:block;left:10px;top:-18px;width:30px}.txY9K,ui-table:before{pointer-events:all;position:absolute}.txY9K{--btn-dim:19px;cursor:grab;display:none;height:var(--btn-dim);left:calc(20px - var(--btn-dim));padding:2.5px;top:calc(12px - var(--btn-dim));width:var(--btn-dim);z-index:2}.txY9K svg{fill:#ccc;height:100%;width:100%}body.darkMode .txY9K svg{fill:#737373}@media(pointer:coarse){.txY9K:before{bottom:0;content:"";display:inline-block;height:calc(var(--btn-dim) + var(--extended-touchable-area-dim));position:absolute;right:0;width:calc(var(--btn-dim) + var(--extended-touchable-area-dim))}}body:not(.neutron) en-table:hover .txY9K{display:block}.txY9K:hover{cursor:grab}.txY9K:hover svg{fill:#a6a6a6}en-note:not(.editable) .txY9K{display:none}en-table.active .txY9K{background:#ccc;border-top-left-radius:6px;display:block}.darkMode en-table.active .txY9K{background-color:#737373}@media(hover:hover){en-table.active .txY9K:hover{background:#a6a6a6}body.darkMode en-table.active .txY9K:hover{background-color:#4d4d4d}}body.darkMode.ui-revamp en-table.active .txY9K svg,en-table.active .txY9K svg{fill:#fff}body.ui-revamp en-table.active .txY9K svg{fill:var(--color-icon-fill-tertiary-enabled)}en-table.multirange .txY9K{background:#66b3da}body.darkMode en-table.multirange .txY9K{background-color:#026fac}body.darkMode.ui-revamp en-table.multirange .txY9K{background-color:#737373}body.ui-revamp en-table.multirange .txY9K{background-color:#e6e6e6}en-table.multirange .txY9K:active,en-table.multirange .txY9K:hover{background:#138ac7}body.darkMode en-table.multirange .txY9K:active,body.darkMode en-table.multirange .txY9K:hover{background-color:#0081c2}body.darkMode.ui-revamp en-table.multirange .txY9K:active,body.darkMode.ui-revamp en-table.multirange .txY9K:hover{background-color:#737373}body.ui-revamp en-table.multirange .txY9K:active,body.ui-revamp en-table.multirange .txY9K:hover{background-color:#e6e6e6}.x1TEB{height:41px;left:20px;padding-top:calc(41px - var(--handle-dim));right:20px;top:-29px}.EWRLR,.x1TEB{display:none;position:absolute;-webkit-user-select:none;-moz-user-select:none;user-select:none}.EWRLR{bottom:16px;left:-21px;padding-left:calc(41px - var(--handle-dim));top:12px;width:41px}.Yz_0G{position:absolute;z-index:10}.Yz_0G .Wlf2B{height:100%;width:100%;z-index:2}.x1TEB .Yz_0G{height:calc(var(--handle-dim) + var(--extended-touchable-area-dim));overflow-x:hidden;padding-top:var(--extended-touchable-area-dim);top:calc(41px - var(--handle-dim) - var(--extended-touchable-area-dim));width:100%}.EWRLR .Yz_0G{height:100%;width:var(--handle-dim)}.x1TEB .Wlf2B{-moz-column-gap:1px;column-gap:1px;display:grid;grid-auto-columns:auto;grid-auto-flow:column}.EWRLR .Wlf2B{display:grid;grid-auto-flow:row;grid-auto-rows:auto;row-gap:1px}.Wlf2B{background-color:#eee}body.darkMode .Wlf2B{background-color:#737373}.x1TEB .Wlf2B,.x1TEB .Wlf2B .QZiGN:last-child:after{border-radius:0 6px 0 0}.EWRLR .Wlf2B,.EWRLR .Wlf2B .QZiGN:last-child:after{border-radius:0 0 0 6px}.QZiGN{cursor:pointer;display:inline-block;pointer-events:all;position:relative;z-index:1}@media(pointer:coarse){.QZiGN:before{content:"";display:inline-block;position:absolute}.x1TEB .QZiGN:before{height:var(--extended-touchable-area-dim);left:0;right:0;top:calc(var(--handle-dim)*-1)}.EWRLR .QZiGN:before{bottom:0;left:calc(var(--handle-dim)*-1);top:0;width:var(--extended-touchable-area-dim)}}.QZiGN:after{background-color:#f2f2f2;bottom:0;content:"";left:0;position:absolute;right:0;top:0}body.darkMode .QZiGN:after{background-color:#4d4d4d}.x1TEB .QZiGN{height:100%}.EWRLR .QZiGN{width:100%}@media(hover:hover){.QZiGN:hover{z-index:2}.QZiGN:hover:after{background-color:#ebebeb}body.darkMode .QZiGN:hover:after{background-color:#404040}}.QZiGN.CeiFT{z-index:3}.QZiGN.CeiFT:after{background-color:#b2d9ec;border:0 solid #66b3da}body.darkMode .QZiGN.CeiFT:after{background-color:#026fac}body.ui-revamp .QZiGN.CeiFT:after{background-color:var(--color-surface-fill-quaternary-enabled);border-color:var(--color-text-fill-brandsecondary-enabled)}.x1TEB .QZiGN.CeiFT:after{border-left-width:thin;border-right-width:thin;left:-1px;right:-1px}.x1TEB .QZiGN.CeiFT:first-child:after{left:0}.x1TEB .QZiGN.CeiFT:last-child:after{border-right-width:0;right:0}.EWRLR .QZiGN.CeiFT:after{border-bottom-width:thin;border-top-width:thin;bottom:-1px;top:-1px}.EWRLR .QZiGN.CeiFT:first-child:after{top:0}.EWRLR .QZiGN.CeiFT:last-child:after{border-bottom-width:0;bottom:0}@media(hover:hover){.QZiGN.CeiFT:hover{z-index:5}.QZiGN.CeiFT:hover:after{background-color:#66b3da;border-color:#138ac7}body.darkMode .QZiGN.CeiFT:hover:after{background-color:#0081c2}body.ui-revamp .QZiGN.CeiFT:hover:after{background-color:var(--color-surface-fill-quaternary-enabled);border-color:var(--color-text-fill-brandsecondary-enabled)}}.QZiGN.eliN2{cursor:grab}.uq5TA{bottom:0;left:0;overflow:hidden;position:absolute;right:0;top:0;z-index:12}.x1TEB .uq5TA{padding-top:8px;right:-13px;top:0}.x1TEB .uq5TA .cgBg7{margin-left:9px}.EWRLR .uq5TA{bottom:-13px;left:0;padding-left:8px}.EWRLR .uq5TA .cgBg7{margin-top:10px}.yejbv{cursor:pointer;display:inline-block;height:18px;opacity:0;pointer-events:all;position:relative;transition:opacity .2s;width:18px;z-index:11}.x1TEB .yejbv{margin-top:8px}.x1TEB .yejbv.BFPkc{bottom:14px;left:-7px}.EWRLR .yejbv{margin-left:8px}.EWRLR .yejbv.BFPkc{right:14px;top:-6px}@media(hover:hover){.EWRLR:hover .yejbv,.x1TEB:hover .yejbv{opacity:1}.EWRLR:hover .yejbv.BFPkc,.x1TEB:hover .yejbv.BFPkc{opacity:0}.EWRLR:hover .yejbv.BFPkc:hover,.x1TEB:hover .yejbv.BFPkc:hover{opacity:1}}.yejbv.d0YtL,.yejbv.d0YtL.BFPkc.BFPkc.BFPkc{opacity:1}.yejbv:after,.yejbv:before{background-color:#fff;content:"";opacity:0;position:absolute;transition:opacity .1s ease-out;z-index:1}.yejbv:before{height:2px;left:3px;top:2px;width:10px}.yejbv:after{height:10px;left:7px;top:-2px;width:2px}.yejbv .SZtnQ{background-color:#d8d8d8;border-radius:13px 13px 13px 13px;height:26px;left:calc(50% - 14px);position:absolute;top:calc(50% - 20px);transform:translateY(14px) scale(.23) rotate(45deg);transition:border-radius .15s ease-out,background-color .15s ease-out,transform .15s ease-out;width:26px}body.darkMode .yejbv .SZtnQ{background-color:#a6a6a6}body.ui-revamp .yejbv .SZtnQ{background-color:var(--color-action-content-fill-secondary-default)}.EWRLR .yejbv:before{left:-3px;top:7px}.EWRLR .yejbv:after{left:1px;top:3px}.EWRLR .yejbv .SZtnQ{left:calc(50% - 20px);top:calc(50% - 14px);transform:translateX(14px) scale(.23) rotate(45deg)}.EWRLR .yejbv.d0YtL .SZtnQ,.EWRLR .yejbv:hover .SZtnQ{border-radius:13px 4px 13px 13px;transform:scale(1) rotate(45deg)}.yejbv.d0YtL,.yejbv:hover{z-index:13}.yejbv.d0YtL:after,.yejbv.d0YtL:before,.yejbv:hover:after,.yejbv:hover:before{opacity:1;transition-delay:50ms}.yejbv.d0YtL .SZtnQ,.yejbv:hover .SZtnQ{background-color:#0081c2;border-radius:13px 13px 4px 13px;transform:scale(1) rotate(45deg)}body.darkMode .yejbv.d0YtL .SZtnQ,body.darkMode .yejbv:hover .SZtnQ{background-color:#00a3f4}body.ui-revamp .yejbv.d0YtL .SZtnQ,body.ui-revamp .yejbv:hover .SZtnQ{background-color:var(--color-surface-fill-secondarybrand-enabled)}body.neutron .yejbv .SZtnQ,body.neutron .yejbv:after,body.neutron .yejbv:before{opacity:0}body.neutron .yejbv.d0YtL .SZtnQ,body.neutron .yejbv.d0YtL:after,body.neutron .yejbv.d0YtL:before{opacity:1}.oOQT2{overflow:hidden;padding-top:1px;position:relative;width:100%;z-index:9}.oOQT2 .D5lwp,.oOQT2 .IUAOM{height:100%;left:0;opacity:0;position:absolute;top:0;transition:opacity .15s ease-out;width:100%}.oOQT2 .D5lwp:before,.oOQT2 .IUAOM:before{background-color:#0081c2;content:"";height:inherit;left:inherit;position:absolute;top:inherit;width:inherit}body.darkMode .oOQT2 .D5lwp:before,body.darkMode .oOQT2 .IUAOM:before{background-color:#026fac}body.ui-revamp .oOQT2 .D5lwp:before,body.ui-revamp .oOQT2 .IUAOM:before{background-color:var(--color-surface-fill-secondarybrand-enabled)}.oOQT2 .D5lwp.IpPye,.oOQT2 .IUAOM.IpPye{opacity:1}.oOQT2 .IUAOM{height:14px}.oOQT2 .IUAOM:before{height:3px;top:5px}.oOQT2 .D5lwp{cursor:col-resize;width:14px}.oOQT2 .D5lwp:before{left:5px;width:3px}.oOQT2 .AGXRg,.oOQT2 .LeEo6{background-image:linear-gradient(90deg,rgba(38,38,38,.15),rgba(38,38,38,0));background-repeat:repeat-y;background-size:6px 1px;bottom:0;left:0;opacity:0;position:absolute;top:0;transition:opacity .15s;width:6px;z-index:50}.oOQT2 .LeEo6{background-image:linear-gradient(270deg,rgba(38,38,38,.15),rgba(38,38,38,0));left:auto;right:0}.niDMf{bottom:0;height:20px;opacity:0;pointer-events:all;position:sticky;transition:opacity .15s;width:100%;z-index:9}body.neutron .niDMf{display:none!important}.niDMf.MlSwR{opacity:1}.niDMf .TqjMZ{height:inherit;position:absolute;top:0;width:60px}.niDMf .TqjMZ:after{background-color:rgba(0,0,0,.1);border-radius:4px;content:"";height:4px;position:absolute;top:6px;transition:height .18s ease-out,background-color .18s ease-out;width:inherit}body.darkMode .niDMf .TqjMZ:after{background-color:#4d4d4d}.niDMf .TqjMZ.IpPye:after,.niDMf .TqjMZ:hover:after{background-color:rgba(0,0,0,.2);height:8px}body.darkMode .niDMf .TqjMZ.IpPye:after,body.darkMode .niDMf .TqjMZ:hover:after{background-color:#666}body.win .niDMf .TqjMZ.IpPye:after,body.win .niDMf .TqjMZ:after,body.win .niDMf .TqjMZ:hover:after{background-color:#d7d7d7;border-radius:0;height:8px;transition:none}en-note.peso en-table.active ui-table .niDMf,en-note.peso en-table:hover ui-table .niDMf{opacity:1}body.peso-dragging en-note.peso .QZiGN.CeiFT,body.peso-dragging en-note.peso .selectedCell,body.peso-dragging en-note.peso .txY9K{opacity:.5}.TK9X6{background-color:#ccc;border-radius:3px;cursor:pointer;display:none;height:18px;pointer-events:all;position:absolute;transition:background-color .2s;-webkit-user-select:none;-moz-user-select:none;user-select:none;width:18px;z-index:2}.TK9X6 svg{fill:#333;height:100%;left:0;position:absolute;top:0;transition:fill .2s;width:100%}body.darkMode .TK9X6{background-color:#737373}body.ui-revamp .TK9X6{background-color:transparent}body.ui-revamp .TK9X6 svg{fill:var(--color-icon-fill-tertiary-enabled)}@media(hover:hover){.TK9X6:hover{background-color:#0081c2}.TK9X6:hover svg{fill:#fff}body.darkMode .TK9X6:hover{background-color:#00a3f4}body.darkMode .TK9X6:hover svg{fill:#333}body.ui-revamp .TK9X6:hover{background-color:var(--color-text-fill-brandsecondary-enabled)}body.ui-revamp .TK9X6:hover svg{fill:#fff}}.mlElQ{position:relative}.mlElQ .LPxfY{background-color:#0081c2;border-radius:100%;color:#fff;font-size:14px;height:20px;line-height:20px;position:absolute;right:-10px;text-align:center;top:-10px;width:20px}.ZeoHY{padding:12px}.hUTyE{align-items:center;display:flex;flex-flow:row nowrap;max-width:420px}.SrA39{color:#4d4d4d;flex:1 0 40px;font-size:14px;font-weight:500;line-height:16px;padding:13px 16px 11px 0;text-align:right;white-space:nowrap}body.darkMode .SrA39{color:#fff}._kxhG{background:#f8f8f8;border:1px solid #f8f8f8;border-radius:4px;color:#4d4d4d;flex:0 0 350px;font-family:-apple-system,system-ui,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif;font-size:14px;height:40px;line-height:16px;padding:10px 8px 12px 16px;transition:border-color .15s;width:350px}body.neutron ._kxhG{font-family:Source Sans Pro,-apple-system,system-ui,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif}._kxhG::-moz-placeholder{color:#737373}._kxhG::placeholder{color:#737373}body.darkMode ._kxhG{background-color:#262626;border-color:#262626;color:#f2f2f2}body.darkMode ._kxhG::-moz-placeholder{color:#a6a6a6}body.darkMode ._kxhG::placeholder{color:#a6a6a6}._kxhG:focus{border-color:#026fac;outline:none;padding-right:33px}body.darkMode ._kxhG:focus{border-color:#00a3f4}body.ui-revamp ._kxhG:focus{border-color:var(--color-surface-fill-primary-enabled)}._kxhG.mw8sY{border-color:#e54e40}body.darkMode ._kxhG.mw8sY{border-color:#cc4033}.aW4SH{display:flex;flex-flow:row nowrap;position:relative}.aW4SH+.aW4SH{margin-top:8px}.aW4SH:not(:focus-within) div button{display:none}.AjZZz{fill:#737373;background:none;border:none;cursor:pointer;height:32px;padding:6px;position:absolute;right:4px;top:4px;transition:fill .2s;width:32px}.AjZZz:focus,.AjZZz:hover{fill:#a6a6a6}body.darkMode .AjZZz{fill:#d9d9d9}body.darkMode .AjZZz:focus,body.darkMode .AjZZz:hover{fill:#a6a6a6}.OuXs4{align-items:center;background:transparent;border:none;color:#00a82d;cursor:pointer;display:flex;padding:4px;position:absolute;right:4px;top:4px;transition:color .2s}.OuXs4:focus,.OuXs4:hover{color:#4d4d4d}body.darkMode .OuXs4{color:#26b54c}body.darkMode .OuXs4:focus,body.darkMode .OuXs4:hover{color:#d9d9d9}body.ui-revamp .OuXs4,body.ui-revamp .OuXs4:focus,body.ui-revamp .OuXs4:hover{color:var(--color-text-fill-brandsecondary-enabled)}.OuXs4 label{cursor:pointer;font-size:14px;line-height:16px}.OuXs4 .n6sIi{fill:currentColor;height:24px;width:24px}.ng6YK{border:1px solid transparent;border-radius:4px;cursor:pointer;flex:0 0 auto;font-family:-apple-system,system-ui,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif;font-size:14px;line-height:16px;padding:8px 19px;transition:color .15s,border-color .15s,background-color .15s;white-space:nowrap}body.neutron .ng6YK{font-family:Source Sans Pro,-apple-system,system-ui,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif}.ng6YK:focus,.ng6YK:hover{outline:none}.ng6YK.XoOE5{background:#00a82d;border-color:#00a82d;color:#fff;font-weight:600}.ng6YK.XoOE5:focus,.ng6YK.XoOE5:hover{background:#0e753d;border-color:#0e753d}.ng6YK.XoOE5.AxwcV{background:#ccc;border-color:#ccc;cursor:default}.ng6YK.ed7Ot{background:#fff;border-color:#ccc;color:#a6a6a6}body.darkMode .ng6YK.ed7Ot{background-color:#333;border-color:#737373;color:#fff}.ng6YK.ed7Ot:focus,.ng6YK.ed7Ot:hover{border-color:#999;color:#666}body.darkMode .ng6YK.ed7Ot:focus,body.darkMode .ng6YK.ed7Ot:hover{background-color:#4d4d4d}.ng6YK+.ng6YK{margin:0 8px}body.ui-revamp .ng6YK.XoOE5{background:var(--color-action-base-fill-primary-default);border-color:var(--color-action-base-fill-primary-default)}body.ui-revamp .ng6YK.XoOE5:focus,body.ui-revamp .ng6YK.XoOE5:hover{background:var(--color-action-base-fill-primary-hover);border-color:var(--color-action-base-fill-primary-hover)}body.ui-revamp .ng6YK.XoOE5.AxwcV{background:#ccc;border-color:#ccc;cursor:default}.SuKTD{display:flex;flex-flow:row-reverse nowrap;padding-top:16px}.C8rHv{color:#026fac;cursor:pointer;display:inline-block;flex:1 1;font-size:14px;line-height:16px;overflow:hidden;padding:8px 4px;text-decoration:none;text-overflow:ellipsis;white-space:nowrap}body.darkMode .C8rHv{color:#00a3f4}body.ui-revamp .C8rHv{color:var(--color-surface-fill-secondarybrand-enabled)}.C8rHv:hover{text-decoration:underline}.C8rHv.J1i4H{color:#00a82d}body.darkMode .C8rHv.J1i4H{color:#26b54c}.C8rHv .CoDy0{fill:currentColor;height:24px;margin:-5px 7px -7px 1px;padding:2px;width:24px}.cSPII{background:#fff;border:none;border-radius:4px;color:#262626;cursor:pointer;display:inline-block;flex:0 0;font-family:-apple-system,system-ui,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif;font-size:14px;line-height:16px;padding:8px 10px;transition:background-color .15s;white-space:nowrap}body.neutron .cSPII{font-family:Source Sans Pro,-apple-system,system-ui,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif}body.darkMode .cSPII{background-color:#333;color:#e6e6e6}.cSPII:focus,.cSPII:hover{background:#f2f2f2;outline:none}body.darkMode .cSPII:focus,body.darkMode .cSPII:hover{background-color:#4d4d4d}.azPVL{fill:currentColor;height:24px;margin:-2px -6px -6px;padding:2px;width:24px}.p_KY3{background:#d9d9d9;display:inline-block;flex:0 0 1px;height:20px;margin:0 3px;width:1px}body.darkMode .p_KY3{background-color:#404040}.pZJEe{margin-left:-32px}.Zv9PV,.pZJEe{height:32px;width:32px}.Zv9PV{mix-blend-mode:multiply;pointer-events:none;position:relative}body.darkMode .Zv9PV{mix-blend-mode:lighten}.BfDF1,.itrET{background-color:#b2d9ec;border-radius:50%;height:100%;left:50%;position:absolute;transform-origin:50% 50%;width:100%}body.darkMode .BfDF1,body.darkMode .itrET{background-color:#00a3f4}body.ui-revamp .BfDF1,body.ui-revamp .itrET{background-color:var(--color-text-fill-brandsecondary-enabled)}.BfDF1{animation:ADOlX 1.2s cubic-bezier(.455,.03,.515,.955) infinite alternate;opacity:.4}body.darkMode .BfDF1{opacity:.3}@keyframes ADOlX{0%{transform:translate(-50%) scale(1.44)}to{transform:translate(-50%) scale(1.2)}}.itrET{animation:zhN32 1.2s cubic-bezier(.455,.03,.515,.955) infinite alternate;opacity:.8}body.darkMode .itrET{opacity:.6}@keyframes zhN32{0%{transform:translate(-50%) scale(.75)}to{transform:translate(-50%) scale(1)}}.grXw1{bottom:0;font-family:-apple-system,system-ui,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif;font-size:14px;left:0;overflow:hidden;position:absolute;right:0;top:0}body.neutron .grXw1{font-family:Source Sans Pro,-apple-system,system-ui,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif}@media print{.grXw1{display:none!important}}.oGlLR{background:#fff;border-radius:3px;box-shadow:0 0 6px 0 rgba(0,0,0,.3);cursor:default;font-family:-apple-system,system-ui,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif;font-size:14px;height:inherit;min-width:180px;outline:none;overflow-x:hidden;overflow-y:auto;padding:12px 0;pointer-events:auto;position:absolute;z-index:2147483547}body.neutron .oGlLR{font-family:Source Sans Pro,-apple-system,system-ui,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif}.oGlLR::-webkit-scrollbar{-webkit-appearance:none}.oGlLR::-webkit-scrollbar:vertical{width:0}.VPcEL{opacity:.01;transform:translateY(-10px) translateZ(0)}.X5l3J{opacity:1;transform:translateY(0) translateZ(0);transition:transform .3s cubic-bezier(.215,.61,.355,1),opacity .3s cubic-bezier(.215,.61,.355,1)}.Dhm5w{opacity:1;transition:opacity .2s}.pxYvR{opacity:.01}.darkMode .oGlLR{background-color:#333;box-shadow:0 0 6px 0 rgba(0,0,0,.5)}body.ui-revamp .oGlLR{background-color:var(--color-surface-fill-primary-enabled);border:1px solid var(--color-surface-stroke-tertiary-enabled)}.m9PWN{color:#a6a6a6;display:inline-block;font-size:12px;height:20px;letter-spacing:.03em;line-height:14px;padding:0 20px;text-transform:uppercase}body.ui-revamp .m9PWN{font-size:8px;font-weight:600;letter-spacing:1px;line-height:16px}.AY6Th{background-color:#f2f2f2;height:1px;margin:8px 0;width:100%}.darkMode .AY6Th{background-color:#4d4d4d}.fullsize .grXw1{display:none}.IVPhh{align-content:center;align-items:center;color:#333;cursor:pointer;display:flex;flex-flow:row nowrap;font-weight:500;height:28px;line-height:26px;outline:none;overflow:hidden;padding:0 20px;position:relative;text-overflow:ellipsis;transition:all .15s;white-space:nowrap}.darkMode .IVPhh{color:#e6e6e6}.IVPhh.PdBWV{padding-right:34px}.IVPhh.GknL_{color:#ccc;cursor:not-allowed}.darkMode .IVPhh.GknL_{color:#737373}.IVPhh.gVRzW:not(.GknL_){background-color:#f2f2f2}.IVPhh.gVRzW:not(.GknL_) .z9lAE{fill:#737373}.darkMode .IVPhh.gVRzW:not(.GknL_){background-color:#4d4d4d;color:#fff}.darkMode .IVPhh.gVRzW:not(.GknL_) .z9lAE{fill:#fff}.IVPhh.R_w4A{padding-left:44px}.IVPhh.R_w4A.d7067{color:#026fac;font-weight:600}.darkMode .IVPhh.R_w4A.d7067{color:#00a3f4}.ui-revamp .IVPhh.R_w4A.d7067{color:var(--color-text-fill-brandsecondary-enabled)}.ui-revamp .IVPhh.R_w4A.d7067:after{background-color:var(--color-text-fill-brandsecondary-enabled);border-radius:50%;content:"";height:6px;left:var(--spacing-2);position:absolute;top:41%;width:6px}.IVPhh.R_w4A.d7067 .Xn9PU{fill:#026fac}.darkMode .IVPhh.R_w4A.d7067 .Xn9PU{fill:#00a3f4}body.ui-revamp .IVPhh.R_w4A.d7067 .Xn9PU{fill:var(--color-text-fill-brandsecondary-enabled);visibility:hidden}.IVPhh.R_w4A.d7067.gVRzW{background-color:#f2f2f2}.darkMode .IVPhh.R_w4A.d7067.gVRzW{background-color:#4d4d4d;color:#fff}.darkMode .IVPhh.R_w4A.d7067.gVRzW .Xn9PU{fill:#fff}body.ui-revamp .IVPhh.R_w4A.d7067{color:#4d64ff}body.darkMode.ui-revamp .IVPhh.R_w4A.d7067{color:#b1bcff}.fN8ju{background:#026fac;border-radius:12px;color:#fff;font-size:11px;letter-spacing:.5px;line-height:12px;margin:5px 0 7px 10px;padding:2px 6px;text-transform:uppercase}.darkMode .fN8ju{background:#00a3f4;color:#000}.ui-revamp .fN8ju{background:var(--color-text-fill-brandsecondary-enabled)}.Xn9PU{height:10px;top:calc(50% - 5px);width:13px}.Xn9PU,.z9lAE{position:absolute;transition:fill .15s}.z9lAE{fill:#737373;height:7px;right:8px;top:calc(50% - 3.5px);width:5px}.darkMode .z9lAE{fill:#ccc}.Xn9PU{fill:transparent;left:22px}.Zf8Zz{padding:0}.PGxAQ{color:#a6a6a6;font-size:12px;font-weight:600;letter-spacing:1px;line-height:24px;padding:0 16px;text-transform:uppercase}.jKwhK{display:flex;flex-wrap:wrap;padding:3px 14px;width:220px}.D43Zo{cursor:pointer;flex:0 0 auto;height:32px;outline:none;padding:3px;width:32px}.YJFy3{border-radius:50%;height:26px;position:relative;transition:all .15s;width:26px}.YJFy3.SoV6E{border:1px solid #dcdcdc}.YJFy3.Co3GE{transform:scale(1.15)}.YJFy3.AkNaA:after{border:2px solid #0081c2;border-radius:50%;content:"";height:28px;left:calc(50% - 16px);position:absolute;top:calc(50% - 16px);width:28px}body.darkMode .YJFy3.AkNaA:after{border-color:#00a3f4}body.ui-revamp .YJFy3.AkNaA:after{border-color:var(--color-text-fill-brandsecondary-enabled)}.Y8Io8{color:#b0b0b0;display:block;font-family:Source Sans Pro,-apple-system,system-ui,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif;font-size:16px;line-height:1;pointer-events:none;position:absolute;-webkit-user-select:none;-moz-user-select:none;user-select:none}body.firefox .Y8Io8{-webkit-user-select:text;-moz-user-select:text;user-select:text}body.firefox .Y8Io8 ::-moz-selection{background:transparent}.darkMode .Y8Io8{color:#a6a6a6}body.ui-revamp .Y8Io8{font-family:Inter,Source Sans Pro,-apple-system,system-ui,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif}.Y8Io8>*{margin-right:3px}.Y8Io8>:last-child{margin-right:0}body.peso-deactivated .Y8Io8{display:none}body.darkMode .grDe7{color:#737373}.QXp3s{background-color:#f8f8f8;border-radius:16px;color:#666;cursor:pointer;display:inline-block;font-size:.9em;font-weight:300;height:32px;line-height:24px;margin:-5px 0;padding:4px 16px 4px 12px;pointer-events:auto;transition:background-color .2s}.QXp3s .jIFj_{fill:#666;display:inline-block;height:24px;margin-right:.2em;vertical-align:top;width:24px}.QXp3s:focus,.QXp3s:hover{background-color:#f0f0f0}.darkMode .QXp3s{background-color:#4d4d4d;color:#ccc;font-weight:600}.darkMode .QXp3s .jIFj_{fill:#ccc}.darkMode .QXp3s:focus,.darkMode .QXp3s:hover{background-color:#5d5d5d}.zLFGz{fill:#a6a6a6;align-items:center;background-color:#f8f8f8;border:0;border-radius:4px;color:#a6a6a6;cursor:pointer;display:flex;font-size:16px;height:32px;margin:7px 14px 7px 0;padding:8px 14px 8px 8px;pointer-events:all}.zLFGz:hover{fill:#737373;background-color:#e6e6e6;color:#737373}.zLFGz:active{background-color:#f2f2f2}.darkMode .zLFGz{fill:#737373;background-color:#333;color:#737373}.darkMode .zLFGz:hover{fill:#ccc;color:#ccc}.darkMode .zLFGz:active,.darkMode .zLFGz:hover{background-color:#4d4d4d}body.ui-revamp .zLFGz{fill:var(--color-action-content-fill-secondary-default);background-color:transparent;border:1px solid var(--color-action-base-stroke-secondary-default);border-radius:var(--radius-xs);color:var(--color-action-content-fill-secondary-default);margin:0 14px 14px 0}.IdUYJ{display:inline-block;height:24px;margin-right:.2em;vertical-align:top;width:24px}.bLQyN{margin-left:6px}.cChTm{border-radius:4px;box-shadow:0 0 5px #a6a6a6;position:absolute;z-index:1}.vTLGm{color:#a6a6a6;font-family:-apple-system,system-ui,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif;font-size:14px;font-weight:600;letter-spacing:.05%;margin-bottom:16px;margin-top:42px}.darkMode .vTLGm{color:#737373}body.ui-revamp .vTLGm{color:#a6a6a6;font-size:15px;font-weight:400;margin-bottom:var(--spacing-1-5);text-transform:lowercase}body.ui-revamp .vTLGm:first-letter{text-transform:capitalize}.tBN5c{bottom:0;color:#262626;font-family:-apple-system,system-ui,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif;font-size:13px;left:0;overflow:hidden;position:absolute;right:0;top:0}body.neutron .tBN5c{font-family:Source Sans Pro,-apple-system,system-ui,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif}body.darkMode .tBN5c{color:#e6e6e6}body.peso-dragging .tBN5c{display:none}.Vov88{background:#fff;border-radius:4px;box-shadow:0 0 6px 0 rgba(0,0,0,.3);cursor:default;display:flex;flex-flow:row nowrap;left:0;outline:none;padding:4px;pointer-events:auto;position:absolute;top:0;z-index:2147483447}.Vov88.E1XQs:after,.Vov88.Pize6:after,.Vov88._Il0w:after,.Vov88.fMpJI:after{background-color:#fff;box-shadow:var(--arrowBoxShadow);content:"";display:block;height:12px;left:var(--arrowLeft);position:absolute;top:var(--arrowTop);transform:rotate(45deg);width:12px;z-index:-1}body.darkMode .Vov88.E1XQs:after,body.darkMode .Vov88.Pize6:after,body.darkMode .Vov88._Il0w:after,body.darkMode .Vov88.fMpJI:after{background-color:#333;box-shadow:var(--arrowBoxShadowDark)}body.ui-revamp .Vov88.E1XQs:after,body.ui-revamp .Vov88.Pize6:after,body.ui-revamp .Vov88._Il0w:after,body.ui-revamp .Vov88.fMpJI:after{background-color:var(--color-surface-fill-primary-enabled);box-shadow:var(--arrowBoxShadow)}.Vov88.E1XQs:after,.Vov88._Il0w:after{border-radius:3px 0}.Vov88.Pize6:after,.Vov88.fMpJI:after{border-radius:0 3px}body.darkMode .Vov88{background-color:#333}body.ui-revamp .Vov88{background:var(--color-surface-fill-primary-enabled);border:1px solid var(--color-surface-stroke-tertiary-enabled)}body.darkMode.ui-revamp .Vov88,body.ui-revamp .Vov88{box-shadow:0 0 0 0 rgba(0,0,0,.1),0 10px 22px 0 rgba(0,0,0,.1),0 40px 40px 0 rgba(0,0,0,.09),0 89px 53px 0 rgba(0,0,0,.05),0 158px 63px 0 rgba(0,0,0,.01),0 248px 69px 0 transparent}.sdrj8{opacity:.01;transform:translateY(-10px) translateZ(0)}.Syiip{opacity:1;transform:translateY(0) translateZ(0);transition:transform .3s cubic-bezier(.215,.61,.355,1),opacity .3s cubic-bezier(.215,.61,.355,1)}.meyPs{opacity:1;transition:transform .2s,opacity .2s}.TtfI6{opacity:.01;transform:translateY(20px)}@media print{.Vov88{display:none!important}}.BNjTm{margin-top:var(--spacing-5-5)}.fullsize .tBN5c{display:none}.wjsMv{background:#fff;border-radius:3px;cursor:pointer;flex:0 0 32px;transition:all .2s}body.darkMode .wjsMv{background-color:#333}body.ui-revamp .wjsMv{background-color:var(--color-surface-fill-primary-enabled)}.wjsMv .eH811{fill:#262626;display:block;height:32px;padding:4px;transition:all .2s;width:32px}body.darkMode .wjsMv .eH811{fill:#e6e6e6}body.ui-revamp .wjsMv .eH811{fill:var(--color-text-fill-quaternary-enabled)}.wjsMv.DvsAe:not(.M0I20){background:#f1f1f1}body.darkMode .wjsMv.DvsAe:not(.M0I20){background-color:#4d4d4d}.wjsMv.M0I20{cursor:not-allowed}.wjsMv.M0I20 .eH811{fill:#a6a6a6}body.darkMode .wjsMv.M0I20 .eH811{fill:#737373}.wjsMv.WuTxU{background-color:var(--color-surface-fill-primary-enabled)}body.darkMode .wjsMv.WuTxU{background-color:#4d4d4d}body.ui-revamp .wjsMv.WuTxU{background-color:var(--color-surface-fill-primary-enabled)}.wjsMv.WuTxU .eH811,body.ui-revamp .wjsMv.WuTxU .eH811{fill:var(--color-action-base-fill-primary-default)}.NG0pR{align-items:center;display:flex}.NG0pR .seLL7{color:#666;flex:0 0 auto;font-size:14px;text-align:center;width:60px}.darkMode .NG0pR .seLL7{color:#ccc}.NG0pR .IQ74C{color:#666;flex:0 0 auto;font-size:20px;height:40px;opacity:1;transition:opacity .15s ease;width:40px}.darkMode .NG0pR .IQ74C{color:#ccc}.NG0pR .IQ74C.Omth_{cursor:default;opacity:.3}.ux0j7{background-color:transparent;bottom:0;font-family:-apple-system,system-ui,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif;left:0;pointer-events:auto;position:fixed;right:0;top:0;transition:background-color .2s ease;z-index:2147481547}body.neutron .ux0j7{font-family:Source Sans Pro,-apple-system,system-ui,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif}@media(prefers-reduced-motion:reduce){.ux0j7{transition:none}}.ux0j7 button{background-color:transparent;border:none;cursor:pointer;opacity:0;outline:none;padding:0;transition:opacity .2s ease}@media(prefers-reduced-motion:reduce){.ux0j7 button{transition:none}}.ux0j7 .Z3jNz,.ux0j7 .qx29Y{height:160px;left:0;padding:63px 30px;position:absolute;top:calc(50% - 50px);width:80px;z-index:6}.ux0j7 .Z3jNz svg,.ux0j7 .qx29Y svg{transition:stroke .2s ease}@media(prefers-reduced-motion:reduce){.ux0j7 .Z3jNz svg,.ux0j7 .qx29Y svg{transition:none}}.ux0j7 .Z3jNz{left:auto;right:0}.rpYIz{align-items:center;background-color:#f2f2f2;border-bottom:1px solid #e6e6e6;display:flex;height:60px;left:0;opacity:0;position:absolute;right:0;top:0;transition:opacity .2s ease;z-index:5}@media(prefers-reduced-motion:reduce){.rpYIz{transition:none}}.darkMode .rpYIz{background-color:#1a1a1a;border-bottom:1px solid #262626}.rpYIz .nITOb{color:#737373;flex:1 0 100px;font-size:14px;margin:0 4px 0 16px;max-width:calc(50% - 90px);overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.darkMode .rpYIz .nITOb{color:#a6a6a6}.rpYIz .coVzp{display:none;flex:1 1 auto}@media only screen and (max-width:480px){.rpYIz .nITOb{display:none}.rpYIz .coVzp{display:block}}.rpYIz .vOqQV{display:flex;flex:1 0 40px;justify-content:flex-end;margin:0 10px}.rpYIz .c_hpe{flex:0 0 40px;height:40px;padding:11px;width:40px}.rpYIz .c_hpe svg{transition:fill .2s ease}@media(prefers-reduced-motion:reduce){.rpYIz .c_hpe svg{transition:none}}.YsukO{bottom:0;right:0;top:60px;z-index:3}.C6MUN,.YsukO{left:0;position:absolute}.C6MUN{cursor:-webkit-grab;cursor:pointer;height:100%;top:0;width:100%}.C6MUN .OJozw{height:1px;opacity:0;pointer-events:none;position:absolute;transform:translateZ(0);width:1px}.C6MUN .DU08R{transform:translate3d(-50%,-50%,0)}.ux0j7.nZbYr .Z3jNz,.ux0j7.nZbYr .qx29Y{display:none}.ux0j7.xBWhn{background-color:hsla(0,0%,97%,.95)}.darkMode .ux0j7.xBWhn{background-color:rgba(38,38,38,.95)}.ux0j7.xBWhn .Z3jNz,.ux0j7.xBWhn .c_hpe,.ux0j7.xBWhn .qx29Y,.ux0j7.xBWhn .rpYIz{opacity:1}.ux0j7.xBWhn .Z3jNz svg,.ux0j7.xBWhn .qx29Y svg{stroke:#a6a6a6}.ux0j7.xBWhn .Z3jNz:hover svg,.ux0j7.xBWhn .qx29Y:hover svg{stroke:#737373}.darkMode .ux0j7.xBWhn .Z3jNz:hover svg,.darkMode .ux0j7.xBWhn .qx29Y:hover svg{stroke:#ccc}.ux0j7.xBWhn .c_hpe svg{fill:#a6a6a6}.ux0j7.xBWhn .c_hpe:hover svg{fill:#737373}.darkMode .ux0j7.xBWhn .c_hpe:hover svg{fill:#ccc}.ux0j7.dhTN5 .C6MUN{cursor:zoom-in}.ux0j7.PPCIv .C6MUN{cursor:zoom-out}.ux0j7.Kq_Yq .C6MUN{cursor:-webkit-grabbing}.OswDl{background-color:#26b54c;border-radius:18px;bottom:12px;box-shadow:0 0 4px rgba(51,51,51,.5);color:#000;display:block;font-family:-apple-system,system-ui,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif;font-size:12px;font-style:italic;padding:6px 12px;position:fixed;right:6px;text-decoration:none;z-index:2147480640}body.neutron .OswDl{font-family:Source Sans Pro,-apple-system,system-ui,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif}body.darkMode .OswDl{background-color:#00a82d;box-shadow:0 0 4px rgba(0,0,0,.5);color:#fff}@media print{.OswDl{display:none}}.fullsize .OswDl{display:none}.T0OSd{fill:#a6a6a6;flex-shrink:0;height:24px;width:24px}.DDRwn{flex-grow:1;font-style:italic;padding:0 6px;text-align:left}.COsGk{width:170px}.COsGk,.q6AGe{display:inline-block}.q6AGe{height:auto;margin:0 2px;width:300px}.GHvgQ{align-items:center;display:flex;justify-content:center;width:100%}@keyframes veXUZ{0%{transform:rotate(-90deg)}to{transform:rotate(180deg)}}@keyframes PfDPF{0%{stroke-dashoffset:187}50%{stroke-dashoffset:46.75;transform:rotate(135deg)}to{stroke-dashoffset:187;transform:rotate(450deg)}}.L2F8l{align-self:center;animation:veXUZ 1s linear infinite;display:flex;flex:.99;justify-self:center}.nCVEu{stroke-linecap:butt;stroke-dasharray:187;stroke-dashoffset:0;stroke:#00a82d;animation:PfDPF 1s linear infinite;transform-origin:center}
/*# sourceMappingURL=headless.css.map*/
    </style>
  </head>
  <body>
    <en-note class="peso" style="white-space: inherit;">
      <icons>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="position: absolute; width: 0; height: 0" aria-hidden="true" id="__SVG_SPRITE_NODE__"><symbol xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 40 40" id="28OV3">
    <path fill="#C7A4D6" d="m22,24.5c0,-3.038 0,-1.962 0,-5s2.462,-5.5 5.5,-5.5s5.5,2.462 5.5,5.5c0,2.856 0,2.078 0,4.5l-7,0c0,0.341 0,0.405 0,0.414l0,0c0,-0.003 0,-0.003 0,0.086c0,0.828 0.672,1.5 1.5,1.5c0.651,0 5.5,0 5.5,0l0,4c0,0 -2.986,0 -5.5,0c-3.038,0 -5.5,-2.462 -5.5,-5.5zm7,-4.5c0,-0.215 0,-0.391 0,-0.5c0,-0.828 -0.672,-1.5 -1.5,-1.5s-1.5,0.672 -1.5,1.5c0,0.109 0,0.285 0,0.5l3,0zm-12.75,7l-4.5,0l-0.75,3l-4,0l5,-20l4,0l5,20l-4,0l-0.75,-3zm-2.25,-9l-1.25,5l2.5,0l-1.25,-5z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 40 40" id="bwxDq">
    <path fill="#F0573F" d="m30,19c0,-0.552 -0.448,-1 -1,-1c-0.299,0 -1.741,0 -3,0l0,12l-4,0l0,-16l2,0c0,0 0.792,0 2,0c0.838,0 1.869,0 3,0c2.761,0 5,2.239 5,5s0,11 0,11l-4,0c0,0 0,-10.448 0,-11zm-14.75,8l-4.5,0l-0.75,3l-4,0l5,-20l4,0l5,20l-4,0l-0.75,-3zm-2.25,-9l-1.25,5l2.5,0l-1.25,-5z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 40 40" id="1hw-q">
    <path fill="#95DA64" d="m27,24l-3,6l-4,0l0,-16l4,0l0,8l3,-6l3,6l0,-8l4,0l0,16l-4,0l-3,-6zm-15,6c-1.327,0 -6,0 -6,0l0,-20c0,0 4.673,0 6,0c3.866,0 7,3.134 7,7l0,6c0,3.866 -3.134,7 -7,7zm3,-13c0,-1.657 -1.343,-3 -3,-3c-0.797,0 -2,0 -2,0l0,12c0,0 1.203,0 2,0c1.657,0 3,-1.343 3,-3l0,-6z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 40 40" id="uB9-E">
    <path fill="#E1D7D9" d="m26,30c-1.022,0 -5,0 -5,0l0,-20l4,0l0,6c0.309,0 0.623,0 1,0c3.866,0 7,3.134 7,7s-3.134,7 -7,7zm0,-10c-0.388,0 -1,0 -1,0l0,6c0,0 0.6,0 1,0c1.657,0 3,-1.343 3,-3s-1.343,-3 -3,-3zm-15,10l-4,0l0,-20l12,0l0,4l-8,0l0,5l8,0l0,4l-8,0l0,7z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 40 40" id="3t8YR">
<path fill="#ED793E" d="m26,16l4,0l0,14l-4,0l0,-14zm0,-6l4,0l0,4l-4,0l0,-4zm-6.75,17l-4.5,0l-0.75,3l-4,0l5,-20l4,0l5,20l-4,0l-0.75,-3zm-2.25,-9l-1.25,5l2.5,0l-1.25,-5z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 40 40" id="8HXhz">
    <path fill="#ED689F" d="m17,23c0,-3.866 3.134,-7 7,-7c0.377,0 0.691,0 1,0l0,-6l4,0l0,20c0,0 -3.978,0 -5,0c-3.866,0 -7,-3.134 -7,-7zm8,-3c0,0 -0.612,0 -1,0c-1.657,0 -3,1.343 -3,3s1.343,3 3,3c0.4,0 1,0 1,0l0,-6zm-14,-10l4,0l0,20l-4,0l0,-20z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 40 40" id="2F7HN">
    <path fill="#59BEE1" d="m28,20c2.761,0 5,2.239 5,5s-2.239,5 -5,5c-2.761,0 -5,0 -5,0l0,-4c0,0 4.448,0 5,0s1,-0.448 1,-1s-0.448,-1 -1,-1c-2.761,0 -5,-2.239 -5,-5s2.239,-5 5,-5c2.761,0 5,0 5,0l0,4c0,0 -4.448,0 -5,0s-1,0.448 -1,1s0.448,1 1,1zm-14,4c-1.293,0 -3,0 -3,0l0,6l-4,0l0,-20c0,0 5.673,0 7,0c3.866,0 7,3.134 7,7s-3.134,7 -7,7zm0,-10c-0.797,0 -3,0 -3,0l0,6c0,0 2.203,0 3,0c1.657,0 3,-1.343 3,-3s-1.343,-3 -3,-3z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 40 40" id="b6Uhk">
    <path fill="#E191D5" d="m28,18c-0.552,0 -1,0.448 -1,1s0,11 0,11l-4,0c0,0 0,-8.239 0,-11s2.239,-5 5,-5c2.761,0 5,0 5,0l0,4c0,0 -4.448,0 -5,0zm-14,6c-1.293,0 -3,0 -3,0l0,6l-4,0l0,-20c0,0 5.673,0 7,0c3.866,0 7,3.134 7,7s-3.134,7 -7,7zm0,-10c-0.797,0 -3,0 -3,0l0,6c0,0 2.203,0 3,0c1.657,0 3,-1.343 3,-3s-1.343,-3 -3,-3z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 40 40" id="34PoG">
    <path fill="#56A0DD" d="m32,32l-24,0c-1.105,0 -2,-0.895 -2,-2l0,-20c0,-1.105 0.895,-2 2,-2l24,0c1.105,0 2,0.895 2,2l0,20c0,1.105 -0.895,2 -2,2zm-5,-21l-14,0l0,18l8,0l0,-6l6,0l0,-12zm-4,14l0,4l4,-4l-4,0zm-7,-6l8,0l0,2l-8,0l0,-2zm0,-4l8,0l0,2l-8,0l0,-2zm3,10l-3,0l0,-2l3,0l0,2z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 40 40" id="171HO">
    <path fill="#81C151" d="m25,30l0,-20l6,0l0,20l-6,0zm-8,-14l6,0l0,14l-6,0l0,-14zm-8,5l6,0l0,9l-6,0l0,-9z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 40 40" id="2CV-1">
    <path fill="#FAC25D" d="m26.5,8.5l-13,0c-0.828,0 -1.5,-0.672 -1.5,-1.5s0.672,-1.5 1.5,-1.5l13,0c0.828,0 1.5,0.672 1.5,1.5s-0.672,1.5 -1.5,1.5zm-1.5,2l4,14c-2.959,1.68 -5,3.972 -5,8c0,0 0,0.316 0,0.5c0,0.828 -0.672,1.5 -1.5,1.5s-1.5,-0.672 -1.5,-1.5c0,-0.466 0,-4.363 0,-7.684c1.163,-0.413 2,-1.512 2,-2.816c0,-1.657 -1.343,-3 -3,-3s-3,1.343 -3,3c0,1.304 0.837,2.403 2,2.816c0,3.321 0,7.218 0,7.684c0,0.828 -0.672,1.5 -1.5,1.5s-1.5,-0.672 -1.5,-1.5c0,-0.206 0,-0.5 0,-0.5c0,-4.028 -2.041,-6.32 -5,-8l4,-14l10,0z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 40 40" id="3ZE8w">
    <path fill="#2DBE60" d="m28,33l-16,0c-1.105,0 -2,-0.895 -2,-2l0,-22c0,-1.105 0.895,-2 2,-2l11,0l0,5c0,1.105 0.895,2 2,2l5,0l0,17c0,1.105 -0.895,2 -2,2zm-12.803,-15.521l0,0.014c0.041,-0.014 0.109,-0.055 0.109,-0.055c0.191,-0.082 0.424,-0.137 0.697,-0.137l1.121,0c0.068,0 0.123,-0.041 0.123,-0.11l-0.014,-1.235c0,-0.219 0.041,-0.412 0.123,-0.576l0.041,-0.069l-2.2,2.168zm9.538,-0.411c-0.082,-0.481 -0.369,-0.714 -0.628,-0.81c-0.274,-0.096 -0.834,-0.206 -1.544,-0.288c-0.561,-0.069 -1.23,-0.055 -1.627,-0.041c-0.054,-0.33 -0.287,-0.645 -0.546,-0.741c-0.697,-0.288 -1.763,-0.22 -2.037,-0.138c-0.218,0.055 -0.464,0.179 -0.601,0.385c-0.082,0.123 -0.136,0.288 -0.136,0.521l0,0.714c0,0.274 0.013,0.521 0.013,0.521c0,0.247 -0.205,0.439 -0.451,0.439l-1.12,0c-0.233,0 -0.424,0.041 -0.561,0.11c-0.136,0.069 -0.246,0.151 -0.314,0.247c-0.15,0.206 -0.178,0.453 -0.178,0.713c0,0 0,0.206 0.055,0.604c0.041,0.316 0.383,2.484 0.697,3.156c0.123,0.247 0.205,0.357 0.451,0.48c0.547,0.234 1.776,0.481 2.364,0.563c0.574,0.069 0.943,0.233 1.162,-0.233l0.095,-0.275c0.192,-0.576 0.219,-1.084 0.219,-1.44c0,-0.042 0.055,-0.042 0.055,0c0,0.247 -0.055,1.166 0.642,1.413c0.273,0.096 0.834,0.192 1.408,0.261c0.519,0.055 0.902,0.26 0.902,1.605c0,0.81 -0.178,0.919 -1.066,0.919c-0.725,0 -0.998,0.028 -0.998,-0.548c0,-0.467 0.465,-0.426 0.793,-0.426c0.164,0 0.054,-0.11 0.054,-0.398c0,-0.288 0.178,-0.453 0,-0.453c-1.175,-0.041 -1.858,0 -1.858,1.469c0,1.344 0.505,1.591 2.173,1.591c1.312,0 1.776,-0.041 2.309,-1.729c0.109,-0.329 0.369,-1.344 0.519,-3.046c0.11,-1.07 -0.095,-4.322 -0.246,-5.145zm-2.733,3.622c0.041,-0.329 0.178,-0.741 0.656,-0.727c0.533,0.027 0.601,0.535 0.601,0.878c-0.218,-0.11 -0.491,-0.165 -0.806,-0.178c-0.15,-0.014 -0.314,0 -0.451,0.027zm2.998,-13.69l5,5l-5,0l0,-5z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 40 40" id="fm5hW">
    <path fill="#69758C" d="m31.244,27.531l-10.452,6.25c-0.244,0.146 -0.518,0.219 -0.792,0.219s-0.548,-0.073 -0.792,-0.219l-10.452,-6.25c-0.469,-0.28 -0.756,-0.788 -0.756,-1.336l0,-12.39c0,-0.548 0.287,-1.056 0.756,-1.336l10.452,-6.25c0.244,-0.146 0.518,-0.219 0.792,-0.219s0.548,0.073 0.792,0.219l10.452,6.25c0.469,0.28 0.756,0.788 0.756,1.336l0,12.39c0,0.548 -0.287,1.056 -0.756,1.336zm-19.244,-4.002l3.91,-2.338l1.09,0.652l0,1.026l-4.123,2.466l6.123,3.661l0,-8.339l-7,-4.186l0,7.058zm9,-12.525l0,5.04l-1,0.599l-1,-0.599l0,-5.04l-6.123,3.661l7.123,4.259l7.123,-4.259l-6.123,-3.661zm0,9.653l0,8.339l6.123,-3.661l-4.123,-2.466l0,-1.026l1.09,-0.652l3.91,2.338l0,-7.058l-7,4.186z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 40 40" id="eQ0Tt">
    <path fill="#68BEBD" d="m28,19l0,-1c0,-0.552 -0.448,-1 -1,-1s-1,0.448 -1,1l0,4c0,0.552 0.448,1 1,1s1,-0.448 1,-1l0,-1l4.95,0l0,0l2.05,0c0.552,0 1,0.448 1,1s-0.448,1 -1,1l-2.362,0c-1.356,5.731 -6.493,10 -12.638,10s-11.282,-4.269 -12.638,-10l-2.362,0c-0.552,0 -1,-0.448 -1,-1s0.448,-1 1,-1l2.05,0l0,0l3.95,0l0,1c0,0.552 0.448,1 1,1s1,-0.448 1,-1l0,-4c0,-0.552 -0.448,-1 -1,-1s-1,0.448 -1,1l0,1l-3.95,0c0.513,-6.711 6.108,-12 12.95,-12s12.437,5.289 12.95,12l-4.95,0zm-12,-3c0,-0.552 -0.448,-1 -1,-1s-1,0.448 -1,1l0,8c0,0.552 0.448,1 1,1s1,-0.448 1,-1l0,-8zm3,-3c0,-0.552 -0.448,-1 -1,-1s-1,0.448 -1,1l0,14c0,0.552 0.448,1 1,1s1,-0.448 1,-1l0,-14zm3,4c0,-0.552 -0.448,-1 -1,-1s-1,0.448 -1,1l0,6c0,0.552 0.448,1 1,1s1,-0.448 1,-1l0,-6zm3,-2c0,-0.552 -0.448,-1 -1,-1s-1,0.448 -1,1l0,10c0,0.552 0.448,1 1,1s1,-0.448 1,-1l0,-10z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 40 40" id="IKJ7y">
    <path fill="#BEBEBE" d="m30,30l-4,0l0,-4l4,0l0,4zm-8,-4l0,-4l4,0l0,4l-4,0zm0,-12l4,0l0,4l-4,0l0,-4zm-8,4l0,-4l4,0l0,4l-4,0zm4,8l-4,0l0,-4l4,0l0,4zm0,-8l4,0l0,4l-4,0l0,-4zm4,12l-4,0l0,-4l4,0l0,4zm-12,0l0,-4l4,0l0,4l-4,0zm0,-8l0,-4l4,0l0,4l-4,0zm0,-12l4,0l0,4l-4,0l0,-4zm8,0l4,0l0,4l-4,0l0,-4zm12,0l0,4l-4,0l0,-4l4,0zm0,8l0,4l-4,0l0,-4l4,0z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" id="2_baX">
    <path d="M11.0445 17.6587C9.42497 19.2782 6.79914 19.2782 5.17957 17.6587C3.56001 16.0391 3.56001 13.4133 5.17957 11.7937L11.4146 5.55863C11.701 5.27231 11.701 4.80808 11.4146 4.52176C11.1283 4.23543 10.6641 4.23543 10.3778 4.52176L4.1427 10.7568C1.95048 12.9491 1.95048 16.5033 4.1427 18.6955C6.33491 20.8878 9.88919 20.8878 12.0814 18.6955L20.3961 10.3809C20.4086 10.3684 20.4205 10.3556 20.4319 10.3425C20.445 10.3311 20.4578 10.3192 20.4703 10.3067C21.8529 8.92409 21.8806 6.36611 20.3407 4.8262C18.8035 3.28902 16.2363 3.30218 14.851 4.68744L8.52121 11.0173C8.47125 11.0672 8.43 11.1226 8.39748 11.1815C7.72576 12.1035 7.80588 13.4026 8.63785 14.2345C9.55848 15.1552 11.0511 15.1552 11.9717 14.2345L16.0311 10.1751C16.3174 9.88881 16.3174 9.42459 16.0311 9.13826C15.7448 8.85194 15.2806 8.85194 14.9942 9.13826L10.9349 13.1976C10.5869 13.5456 10.0227 13.5456 9.67472 13.1976C9.32675 12.8497 9.32675 12.2855 9.67472 11.9375L9.67629 11.9359L15.8879 5.72432C16.6719 4.94035 18.3102 4.86948 19.3038 5.86308C20.2946 6.85393 20.22 8.48318 19.4334 9.26981C19.4209 9.28228 19.409 9.29508 19.3976 9.3082C19.3845 9.31958 19.3717 9.33151 19.3592 9.34398L11.0445 17.6587Z" fill="currentColor"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 40 40" id="1hglA">
    <path fill="#8788BE" d="m15,16l0,2l3.214,0c-1.482,4.753 -3.263,10.457 -3.294,10.563c-0.147,0.501 -0.497,0.899 -0.942,1.154c0.007,-0.073 0.022,-0.143 0.022,-0.217c0,-1.381 -1.119,-2.5 -2.5,-2.5s-2.5,1.119 -2.5,2.5s1.119,2.5 2.5,2.5c0.739,0 2.212,0 3.5,0c2.306,0 4.247,-1.561 4.825,-3.683c0.065,-0.241 1.766,-5.687 3.212,-10.317l3.963,0l0,-2l-3.338,0c0.787,-2.523 1.386,-4.442 1.4,-4.496c0.135,-0.53 0.496,-0.953 0.96,-1.218c-0.007,0.072 -0.022,0.141 -0.022,0.214c0,1.381 1.119,2.5 2.5,2.5c1.381,0 2.5,-1.119 2.5,-2.5s-1.119,-2.5 -2.5,-2.5c-1.12,0 -1.903,0 -3.5,0c-2.3,0 -4.237,1.553 -4.821,3.668c-0.02,0.074 -0.201,0.807 -0.683,2.213c-0.536,1.246 -1.773,2.119 -3.215,2.119c-0.281,0 -1.281,0 -1.281,0z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 40 40" id="3FBVJ">
    <path fill="#A5C6D4" d="m30,32l-20,0c-1.105,0 -2,-0.895 -2,-2l0,-20c0,-1.105 0.895,-2 2,-2l20,0c1.105,0 2,0.895 2,2l0,20c0,1.105 -0.895,2 -2,2zm0,-22l-20,0l0,18l20,0l0,-18zm-1,10.531l-1.317,-1.35c-0.743,-0.762 -1.732,-1.181 -2.786,-1.181c-1.053,0 -2.043,0.419 -2.785,1.181l-2.702,2.77c-0.706,-0.605 -1.579,-0.951 -2.512,-0.951c-1.054,0 -2.044,0.419 -2.786,1.181l-3.112,3.19l0,-14.371l18,0l0,9.531zm-13,-6.531c-1.105,0 -2,0.895 -2,2s0.895,2 2,2s2,-0.895 2,-2s-0.895,-2 -2,-2zm0.898,9c0.499,0 0.998,0.195 1.379,0.586l3.33,3.414l-9.419,0l3.33,-3.414c0.381,-0.391 0.88,-0.586 1.38,-0.586zm6.62,-2.414c0.381,-0.391 0.88,-0.586 1.379,-0.586c0.5,0 0.999,0.195 1.38,0.586l2.723,2.792l0,3.622l-4.617,0l-3.56,-3.651l2.695,-2.763z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 40 40" id="LwnzH">
    <path fill="#6CA9BD" d="m25.95,23.95c-2.734,2.733 -7.166,2.733 -9.9,0c-0.76,-0.76 -1.286,-1.657 -1.624,-2.607l2.598,-2.598c-0.07,1.107 0.301,2.237 1.148,3.083c1.562,1.563 4.094,1.563 5.656,0c1.563,-1.562 3.438,-3.437 5,-5c1.563,-1.562 1.563,-4.094 0,-5.656c-1.562,-1.563 -4.094,-1.563 -5.656,0c-0.668,0.667 -1.395,1.394 -2.128,2.127c-1.417,-0.374 -2.9,-0.35 -4.308,0.066c1.965,-1.966 1.845,-1.845 4.314,-4.315c2.734,-2.733 7.166,-2.733 9.9,0c2.733,2.734 2.733,7.166 0,9.9c-2.734,2.733 -2.267,2.266 -5,5zm-2,-7.9c0.76,0.76 1.286,1.657 1.624,2.607l-2.598,2.598c0.07,-1.107 -0.301,-2.237 -1.148,-3.083c-1.562,-1.563 -4.094,-1.563 -5.656,0c-1.563,1.562 -3.438,3.437 -5,5c-1.563,1.562 -1.563,4.094 0,5.656c1.562,1.563 4.094,1.563 5.656,0c0.668,-0.667 1.395,-1.394 2.128,-2.127c1.417,0.374 2.9,0.35 4.308,-0.066c-1.965,1.966 -1.845,1.845 -4.314,4.315c-2.734,2.733 -7.166,2.733 -9.9,0c-2.733,-2.734 -2.733,-7.166 0,-9.9c2.734,-2.733 2.267,-2.266 5,-5c2.734,-2.733 7.166,-2.733 9.9,0z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 40 40" id="2SWrN">
    <path fill="#D5594A" d="m33.627192,26.352981c-0.815,1.738 -2.544,2.492 -4.514,1.964c-1.453,-0.388 -3.326,-1.589 -5.116,-3.324c-2.085,0.163 -4.377,0.303 -7,1c-1.904,3.062 -4.048,5.694 -5.662,6.531c-0.642,0.332 -1.248,0.47 -1.798,0.47c-0.958,0 -1.744,-0.418 -2.254,-0.944c-1.647,-1.7 -1.71,-3.856 -0.164,-5.628c1.248,-1.431 3.819,-2.444 6.878,-3.429c0.906,-1.59 1.798,-3.656 2.576,-5.539c0.246,-0.595 0.243,-0.954 0.424,-1.461c-1.283,-2.392 -2.1,-4.79 -1.897,-6.247c0.228,-1.643 1.776,-2.719 3.508,-2.74c0.019,0 0.038,0 0.057,0c1.801,0 3.523,1.374 3.999,3.095c0.37,1.34 0.215,3.481 -0.667,5.892c1.149,1.808 2.466,3.105 3.891,4.727c2.645,-0.028 4.928,0.302 6.283,1.099c1.706,1.004 2.278,2.783 1.456,4.534zm-24.124,3.1c0.493,0.444 1.544,-0.972 2.475,-2.472c-2.166,1.401 -2.969,2.029 -2.475,2.472zm9.374,-19.197c-0.044,-0.004 -0.168,-0.254 -0.243,-0.253c-0.465,0.016 -0.528,0.504 -0.531,0.505c-0.114,0.263 0.162,0.852 0.894,2.485c-0.011,-1.642 0.072,-2.363 -0.12,-2.737zm1.089,9.081l-1.253,2.668l3.111,-0.372l-1.858,-2.296zm8.031,4.656c0.643,0.42 1.116,0.944 1.879,1.01c0.061,-0.161 0.097,-0.282 0.117,-0.367c-0.443,-0.564 -1.093,-0.565 -1.996,-0.643z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 40 40" id="laaBV">
    <path fill="#C5B79E" d="m29.328,16.243c1.553,0.55 2.672,2.016 2.672,3.757c0,1.862 -1.278,3.412 -3,3.859l0,6.418c0.595,0.347 1,0.985 1,1.723c0,1.105 -0.895,2 -2,2c-1.105,0 -2,-0.895 -2,-2c0,-0.738 0.405,-1.376 1,-1.723l0,-6.418c-1.722,-0.447 -3,-1.997 -3,-3.859c0,-1.176 0.517,-2.223 1.325,-2.955c-1.116,-2.383 -3.519,-4.045 -6.325,-4.045c-3.866,0 -7,3.134 -7,7s3.134,7 7,7c1.104,0 2,0.895 2,2s-0.896,2 -2,2c-6.075,0 -11,-4.925 -11,-11s4.925,-11 11,-11c3.167,0 5.996,1.36 8,3.504l0,-2.781c-0.595,-0.347 -1,-0.985 -1,-1.723c0,-1.105 0.895,-2 2,-2c1.105,0 2,0.895 2,2c0,0.738 -0.405,1.376 -1,1.723l0,5.797c0.108,0.241 0.236,0.473 0.328,0.723z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 40 40" id="fGBXJ">
    <path fill="#AD76A8" d="m32,30l-24,0c-1.105,0 -2,-0.895 -2,-2l0,-16c0,-1.105 0.895,-2 2,-2l24,0c1.105,0 2,0.895 2,2l0,16c0,1.105 -0.895,2 -2,2zm-22,-17l-2,0l0,2l2,0l0,-2zm0,4l-2,0l0,2l2,0l0,-2zm0,4l-2,0l0,2l2,0l0,-2zm0,4l-2,0l0,2l2,0l0,-2zm18,-13l-16,0l0,16l16,0l0,-16zm4,1l-2,0l0,2l2,0l0,-2zm0,4l-2,0l0,2l2,0l0,-2zm0,4l-2,0l0,2l2,0l0,-2zm0,4l-2,0l0,2l2,0l0,-2zm-5,2l-14,0l0,-14l14,0l0,14z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 40 40" id="3z4EI">
    <path fill="#8F8C8A" d="m28,33l-16,0c-1.105,0 -2,-0.895 -2,-2l0,-22c0,-1.105 0.895,-2 2,-2l8,0l0,2l-2,0l0,2l2,0l0,2l-2,0l0,2l2,0l0,2l-2,0l0,2l2,0l0,2l-2,0l0,4l4,0l0,-2l-2,0l0,-2l2,0l0,-2l-2,0l0,-2l2,0l0,-2l-2,0l0,-2l2,0l0,-2l-2,0l0,-2l2,0l0,-2l6,0c1.105,0 2,0.895 2,2l0,22c0,1.105 -0.895,2 -2,2z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" id="1B2I1">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M6.45833 3H13.75L19 8.25V20.2083C19 21.0139 18.3472 21.6667 17.5417 21.6667H6.45833C5.65275 21.6667 5 21.0139 5 20.2083V4.45833C5 3.65275 5.65275 3 6.45833 3Z" fill="#4B8AF8"></path>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M19 12.3333V8.24999L17.8333 7.08333H13.75L19 12.3333Z" fill="#4177E5"></path>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M19 8.25H15.2083C14.4027 8.25 13.75 7.59725 13.75 6.79167V3L19 8.25Z" fill="#A5C4FC"></path>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M8.5 15.8334V14.6667H15.5V15.8334H8.5ZM8.5 12.3333H15.5V13.5H8.5V12.3333ZM13.1667 18.1667H8.5V17.0001H13.1667V18.1667Z" fill="#F2F2F2"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" id="1f-jj">
<path d="M6.45833 3H13.75L19 8.25V20.2083C19 21.0139 18.3472 21.6667 17.5417 21.6667H6.45833C5.65275 21.6667 5 21.0139 5 20.2083V4.45833C5 3.65275 5.65275 3 6.45833 3Z" fill="#DB4233"></path>
<path d="M19 12.3335V8.25016L17.8333 7.0835H13.75L19 12.3335Z" fill="#BD3629"></path>
<path d="M19 8.25H15.2083C14.4027 8.25 13.75 7.59725 13.75 6.79167V3L19 8.25Z" fill="#EF9D96"></path>
<path d="M10.8327 10C12.4433 10 13.7493 11.3061 13.7493 12.9167C13.7493 14.5273 12.4433 15.8333 10.8327 15.8333C9.2221 15.8333 7.91602 14.5273 7.91602 12.9167C7.91602 11.3061 9.2221 10 10.8327 10Z" fill="#F9DAD6"></path>
<path d="M10.834 12.9165H16.084V18.1665H10.834V12.9165Z" fill="#B70B00"></path>
<path d="M10.834 15.8332V12.9165H13.7507C13.7507 14.5271 12.4446 15.8332 10.834 15.8332Z" fill="#D73927"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" id="2CKTK">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M6.45833 3H13.75L19 8.25V20.2083C19 21.0139 18.3472 21.6667 17.5417 21.6667H6.45833C5.65275 21.6667 5 21.0139 5 20.2083V4.45833C5 3.65275 5.65275 3 6.45833 3Z" fill="#784AC3"></path>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M19 12.3333V8.24999L17.8333 7.08333H13.75L19 12.3333Z" fill="#613AAE"></path>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M19 8.25H15.2083C14.4027 8.25 13.75 7.59725 13.75 6.79167V3L19 8.25Z" fill="#BFA5DF"></path>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M10.8333 18.1667V17H15.5V18.1667H10.8333ZM10.8333 14.6667H15.5V15.8333H10.8333V14.6667ZM10.8333 12.3333H15.5V13.5H10.8333V12.3333ZM8.5 17H9.66667V18.1667H8.5V17ZM8.5 14.6667H9.66667V15.8333H8.5V14.6667ZM8.5 12.3333H9.66667V13.5H8.5V12.3333Z" fill="#F2F2F2"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" id="1dWIz">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M6.45833 3H13.75L19 8.25V20.2083C19 21.0139 18.3472 21.6667 17.5417 21.6667H6.45833C5.65275 21.6667 5 21.0139 5 20.2083V4.45833C5 3.65275 5.65275 3 6.45833 3Z" fill="#DB4233"></path>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M19 12.3333V8.24999L17.8333 7.08333H13.75L19 12.3333Z" fill="#BD3629"></path>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M19 8.25H15.2083C14.4027 8.25 13.75 7.59725 13.75 6.79167V3L19 8.25Z" fill="#EF9D96"></path>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M12.5833 17.5833C12.5833 17.9053 12.3219 18.1667 11.9999 18.1667C11.6779 18.1667 11.4166 17.9053 11.4166 17.5833C11.4166 15.25 9.08325 14.0833 9.08325 11.75C9.08325 10.1394 10.3893 8.83333 11.9999 8.83333C13.6105 8.83333 14.9166 10.1394 14.9166 11.75C14.9166 14.0833 12.5833 15.25 12.5833 17.5833ZM11.9999 10.5833C11.3553 10.5833 10.8333 11.1054 10.8333 11.75C10.8333 12.3946 11.3553 12.9167 11.9999 12.9167C12.6445 12.9167 13.1666 12.3946 13.1666 11.75C13.1666 11.1054 12.6445 10.5833 11.9999 10.5833Z" fill="#F2F2F2"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" id="27rRg">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M6.45833 3H13.75L19 8.25V20.2083C19 21.0139 18.3472 21.6667 17.5417 21.6667H6.45833C5.65275 21.6667 5 21.0139 5 20.2083V4.45833C5 3.65275 5.65275 3 6.45833 3Z" fill="#14A663"></path>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M19 12.3333V8.24999L17.8333 7.08333H13.75L19 12.3333Z" fill="#139158"></path>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M19 8.25H15.2083C14.4027 8.25 13.75 7.59725 13.75 6.79167V3L19 8.25Z" fill="#8CD2B0"></path>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M8.5 18.1667V11.1667H15.5V18.1667H8.5ZM11.4167 12.3333H9.66667V13.5H11.4167V12.3333ZM11.4167 14.0833H9.66667V15.25H11.4167V14.0833ZM11.4167 15.8333H9.66667V17H11.4167V15.8333ZM14.3333 12.3333H12.5833V13.5H14.3333V12.3333ZM14.3333 14.0833H12.5833V15.25H14.3333V14.0833ZM14.3333 15.8333H12.5833V17H14.3333V15.8333Z" fill="#F2F2F2"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" id="33aQn">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M6.45833 3H13.75L19 8.25V20.2083C19 21.0139 18.3472 21.6667 17.5417 21.6667H6.45833C5.65275 21.6667 5 21.0139 5 20.2083V4.45833C5 3.65275 5.65275 3 6.45833 3Z" fill="#F7BC00"></path>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M19 12.3333V8.24999L17.8333 7.08333H13.75L19 12.3333Z" fill="#ECA400"></path>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M19 8.25H15.2083C14.4027 8.25 13.75 7.59725 13.75 6.79167V3L19 8.25Z" fill="#FBDD81"></path>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M14.3333 18.1667H9.66667C9.02208 18.1667 8.5 17.6446 8.5 17V12.3333C8.5 11.6888 9.02208 11.1667 9.66667 11.1667H14.3333C14.9779 11.1667 15.5 11.6888 15.5 12.3333V17C15.5 17.6446 14.9779 18.1667 14.3333 18.1667ZM14.3333 12.9167H9.66667V16.4167H14.3333V12.9167Z" fill="#F2F2F2"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" fill="none" id="3Zb6g">
<g clip-path="url(#3Zb6g_clip0_14_19272)">
<g style="mix-blend-mode:multiply" opacity="0.2">
<rect x="5.47803" y="2.41693" width="17.4917" height="20.2017" fill="url(#3Zb6g_pattern0)"></rect>
<g style="mix-blend-mode:multiply" opacity="0.2">
<path d="M21.0913 2.52979H7.22312C6.74123 2.52979 6.35059 2.92043 6.35059 3.40232V20.1652C6.35059 20.6471 6.74123 21.0378 7.22312 21.0378H21.0913C21.5732 21.0378 21.9638 20.6471 21.9638 20.1652V3.40232C21.9638 2.92043 21.5732 2.52979 21.0913 2.52979Z" fill="white"></path>
</g>
</g>
<g style="mix-blend-mode:multiply" opacity="0.12">
<rect x="5.88867" y="2.3349" width="16.5063" height="19.2163" fill="url(#3Zb6g_pattern1)"></rect>
<g style="mix-blend-mode:multiply" opacity="0.12">
<path d="M21.0913 2.52997H7.22312C6.74123 2.52997 6.35059 2.92062 6.35059 3.4025V20.1654C6.35059 20.6473 6.74123 21.038 7.22312 21.038H21.0913C21.5732 21.038 21.9638 20.6473 21.9638 20.1654V3.4025C21.9638 2.92062 21.5732 2.52997 21.0913 2.52997Z" fill="white"></path>
</g>
</g>
<path d="M14.4398 2.54016H7.25418C7.14056 2.53879 7.02782 2.56015 6.92258 2.60299C6.81734 2.64584 6.72174 2.7093 6.64139 2.78965C6.56105 2.86999 6.49758 2.9656 6.45474 3.07083C6.41189 3.17607 6.39053 3.28882 6.39191 3.40243V7.15946H14.4808L14.4398 2.54016Z" fill="#21A366"></path>
<path d="M21.091 2.54016H14.4392V7.15946H21.9635V3.39217C21.9622 3.27893 21.9386 3.16706 21.894 3.06296C21.8494 2.95886 21.7848 2.86456 21.7037 2.78544C21.6227 2.70632 21.5269 2.64394 21.4218 2.60185C21.3166 2.55977 21.2042 2.53881 21.091 2.54016Z" fill="#33C481"></path>
<path d="M21.9533 11.7788H14.4392V16.4084H21.9533V11.7788Z" fill="#107C41"></path>
<path d="M14.4396 16.4084V11.7788H6.35065V20.1654C6.34927 20.279 6.37063 20.3918 6.41348 20.497C6.45632 20.6022 6.51979 20.6978 6.60013 20.7782C6.68048 20.8585 6.77608 20.922 6.88132 20.9648C6.98656 21.0077 7.0993 21.029 7.21292 21.0277H21.0913C21.2054 21.029 21.3187 21.0077 21.4245 20.965C21.5303 20.9223 21.6265 20.859 21.7077 20.7788C21.7889 20.6986 21.8533 20.6031 21.8973 20.4978C21.9412 20.3925 21.9639 20.2795 21.9639 20.1654V16.4084H14.4396Z" fill="#185C37"></path>
<path d="M14.4498 7.14923H6.35059V11.7788H14.4498V7.14923Z" fill="#107C41"></path>
<path d="M21.9533 7.14923H14.4392V11.7788H21.9533V7.14923Z" fill="#21A366"></path>
<g style="mix-blend-mode:multiply" opacity="0.48">
<rect x="-0.25" y="5.31171" width="14.5354" height="14.5354" fill="url(#3Zb6g_pattern2)"></rect>
<g style="mix-blend-mode:multiply" opacity="0.48">
<path d="M11.2572 6.5744H2.59348C2.11159 6.5744 1.72095 6.96505 1.72095 7.44694V16.1107C1.72095 16.5926 2.11159 16.9832 2.59348 16.9832H11.2572C11.7391 16.9832 12.1298 16.5926 12.1298 16.1107V7.44694C12.1298 6.96505 11.7391 6.5744 11.2572 6.5744Z" fill="white"></path>
</g>
</g>
<g style="mix-blend-mode:multiply" opacity="0.24">
<rect x="1.4541" y="6.27661" width="11.0863" height="11.0863" fill="url(#3Zb6g_pattern3)"></rect>
<g style="mix-blend-mode:multiply" opacity="0.24">
<path d="M11.2572 6.5744H2.59348C2.11159 6.5744 1.72095 6.96505 1.72095 7.44694V16.1107C1.72095 16.5926 2.11159 16.9832 2.59348 16.9832H11.2572C11.7391 16.9832 12.1298 16.5926 12.1298 16.1107V7.44694C12.1298 6.96505 11.7391 6.5744 11.2572 6.5744Z" fill="white"></path>
</g>
</g>
<path d="M11.2572 6.57428H2.59348C2.11159 6.57428 1.72095 6.96493 1.72095 7.44681V16.1106C1.72095 16.5925 2.11159 16.9831 2.59348 16.9831H11.2572C11.7391 16.9831 12.1298 16.5925 12.1298 16.1106V7.44681C12.1298 6.96493 11.7391 6.57428 11.2572 6.57428Z" fill="#107C41"></path>
<path d="M4.41052 14.6017L6.22745 11.7788L4.5645 8.96614H5.90923L6.81256 10.7523C6.8813 10.8773 6.93967 11.0078 6.98706 11.1423C7.04383 11.005 7.10897 10.8713 7.1821 10.742L8.20861 8.95587H9.44043L7.68509 11.7788L9.44043 14.6222H8.12649L7.09998 12.641C7.04532 12.5612 7.00052 12.475 6.96654 12.3844C6.93241 12.4731 6.89122 12.5589 6.84335 12.641L5.75525 14.6222L4.41052 14.6017Z" fill="white"></path>
<g style="mix-blend-mode:soft-light" opacity="0.5">
<path style="mix-blend-mode:soft-light" opacity="0.5" d="M11.2572 6.57428H2.59348C2.11159 6.57428 1.72095 6.96493 1.72095 7.44681V16.1106C1.72095 16.5925 2.11159 16.9831 2.59348 16.9831H11.2572C11.7391 16.9831 12.1298 16.5925 12.1298 16.1106V7.44681C12.1298 6.96493 11.7391 6.57428 11.2572 6.57428Z" fill="url(#3Zb6g_paint0_linear_14_19272)"></path>
</g>
</g>
<defs>
<pattern id="3Zb6g_pattern0" patternContentUnits="objectBoundingBox" width="1" height="1">
<use xlink:href="#3Zb6g_image0_14_19272" transform="scale(0.0138889 0.0120482)"></use>
</pattern>
<pattern id="3Zb6g_pattern1" patternContentUnits="objectBoundingBox" width="1" height="1">
<use xlink:href="#3Zb6g_image1_14_19272" transform="scale(0.0147059 0.0126582)"></use>
</pattern>
<pattern id="3Zb6g_pattern2" patternContentUnits="objectBoundingBox" width="1" height="1">
<use xlink:href="#3Zb6g_image2_14_19272" transform="scale(0.0166667)"></use>
</pattern>
<pattern id="3Zb6g_pattern3" patternContentUnits="objectBoundingBox" width="1" height="1">
<use xlink:href="#3Zb6g_image3_14_19272" transform="scale(0.0217391)"></use>
</pattern>
<linearGradient id="3Zb6g_paint0_linear_14_19272" x1="3.52761" y1="5.89678" x2="10.3231" y2="17.6606" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.5"></stop>
<stop offset="1" stop-opacity="0.7"></stop>
</linearGradient>
<clipPath id="3Zb6g_clip0_14_19272">
<rect width="24" height="24" fill="white"></rect>
</clipPath>
<image id="3Zb6g_image0_14_19272" width="72" height="83" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEgAAABTCAYAAAA8/EEfAAAACXBIWXMAAC4jAAAuIwF4pT92AAAB4ElEQVR4Xu3cwWrUUABG4XPbUMGNCnYhOM/hpjtfXRDtYziioFRcNqZeF/dmJh3aHnCZ/GeTdiab+5HJ8i+***************************************************+BF2wDacb5DeyBH8DtEmlYXC+Bd8AVsKM9SVtoBL4AH4BPwHfgz/zl0J+eC9qTcwW8B95wxFt7E/Ct//0VuCmlHH5qM8I57We1o+G8BM7YRn/7dcfx1XJoBlq+oAcaztrfP3Nn3D/7vXNv5Sn57wIkBUgKkBQgKUBSgKQASQGSAiQFSAqQFCApQFKApABJAZICJAVICpAUIClAUoCkAEkBkgIkBUgKkBQgKUBSgKQASQGSAiQFSAqQFCApQFKApABJAZICJAVICpAUIClAUoCkAEkBkgIkBUgKkBQgKUBSgKQASQGSAiQFSAqQFCApQFKApABJAZICJAVICpA0A1XaXN7Yrw/Odq60J88+r+At1yh/As9py5xrX8KrwC3tzHuawd3yhqHWWkspY7/hI/Cqf/ea9Y9NTjScz7Sz74HxoanSibZjet3//0Vb5lz7XOnywbimGUzLG07XgLc0eKtDt7AAgk1OJj85lQwnQIcPNza6/djYNjwClI79A/7Gf1JmIOGZAAAAAElFTkSuQmCC"></image>
<image id="3Zb6g_image1_14_19272" width="68" height="79" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEQAAABPCAYAAABS1GNxAAAACXBIWXMAAC4jAAAuIwF4pT92AAABY0lEQVR4Xu3cvW3bYAAG4aNggEuo8xLp0nmhdB7AnQdy5zITuIo6DSFWXwqK/jkkQWrxHoCFBEoAD59UvtMYg3y4+/ximqYZOALzn2+/SQtwHmMsANN2Qq4xvgGPrFH24gw8AT/HGAtjDK5R7oEX4AKMHV0X4BX4DsxbjBl4AH79xxfc4nVhPQz3B1ZH4Af7+ql89v7fefAbf/3IThzIFwWRgkhBpCBSECmIFEQKIgWRgkhBpCBSECmIFEQKIgWRgkhBpCBSECmIFEQKIgWRgkhBpCBSECmIFEQKIgWRgkhBpCBSECmIFEQKIgWRgkhBpCBSECmIFEQKIgWRgkhBpCBSECmIFEQKIgWRgkhBpCBSECmIFEQKIgWRgsgWZGHd81r+ce8t+3j+ZrsYrM/9AMx3AGOMZZqmE/DGai+LVdvJeANOY4zF039H1sW7vWyanYFn4MR1D/E9yGZnA5FfhiEBfgO20QT8sY3jjAAAAABJRU5ErkJggg=="></image>
<image id="3Zb6g_image2_14_19272" width="60" height="60" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADwAAAA8CAYAAAA6/NlyAAAACXBIWXMAAC4jAAAuIwF4pT92AAADoUlEQVRoQ+2b61LbMBBGj3IhoUAphbbT93+6Ti8DoZCEXNwfq43WshyHUizi+pvZ8SWO2ONd2WH82RVFwf+kUdMBKTnnXNMxr63iLyvl9n3PgFnA7LBGhV0echJqgT2sAwZ+add3hyW++tqykBpbu70PPAnsnBsgcANgGIU9AbmANTaJ2ALbOujKHDawI2AMnJgY+dBjoF1ohdgicGtgBTyZWAE455LQJWDfxgo7AabAO+AMOPX7TqhWui3ZFl4jgHPg0YTz+5PQO+Bozo4RwAvg0sd7BH7qPx/S3ny2SWt1Vwjsb2AG3CI56fE2dopbWqt7gsBdAp+AG+Cj3z6lXOU2pQBrYAk8AHfATwJLaT4755ytsgXWCg+R1j0DPiDAX4HPwBVwTpjPOVpa23kJ3AO/kHwx+5dIW6/jAVIVHiJAp0gbXwNfEOhrpM0nhJbOAbwBFkgrT/1nWvF7v9QO1FsWUAXWCo+Rgc4Q6CukrW/ID6wtvSBUdoG09jlSqNQ1BvDA5oKlFy2tsl6lz31c8DaAN0h+IJW9INxJdLol80v9lrbQ9l48iSI3MMg8nRJyGpOAtReuVEvr0oLbGJn1NmFVOh9tLiOzrj+KkrnVVTgVdiAbOVSXlwVN5nfofTT+Yi5QVV1RiNYragLODfbP1QR8bGosUNeAG9UDd109cNfVA3ddPXDX1QN3XT1w19UDd109cNfVA3ddPXDX1QMfuWrdO6om4MYBjk1NwKoYPPeJiD0c1stR8XVY1VkP4wHsvvgPtK04hy3hKb992p/MMQauGyxlAIP2nyBqXprD2ix13Z6AilIVtoOq8UvNX0vCk/ccz4dtbgsTamRZEcB3Fa64eIqiKLyPVKuqsEs/4IMPNZA8kRdYPR4zH2pkmRPcOyVola2wbeMVAvvoB7tF/BMgf0h9WjmAYxfPD8S6dIeY1OZI/gpcUmoOq8tNYa0Pao6YR9QlA+1CK3Ds0/qGmNNmSN5PhPm8t8J69rS6d5TdMrcE66G1F7Ql29KxE+87kp8FPqjCatp8RAbAb88QC9OE4KhVvTa4TXyf1zKucMU7vQP2F664pUFOgFZbYXNYlqB8nalz0y6Q/JO3ppJB3BjU1I1nPVq6bWFzAEOzX3pDjUm84oiPbMSxL2tAmLttw6oKE6kfRHsd8XWvAMR2oEG0jVm2rcIsbYvvtp/9zsPuw+pbLbkg62ThX/ZWS53ewjtLcBhcSs8GPnYd+v9wZ/QHTbqvfA0XAmsAAAAASUVORK5CYII="></image>
<image id="3Zb6g_image3_14_19272" width="46" height="46" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAC4AAAAuCAYAAABXuSs3AAAACXBIWXMAAC4jAAAuIwF4pT92AAAAwUlEQVRoQ+3ZsQkCMRyF8S8iZAm765zAzr0cwJ3sLG8BN8gQSfW3CIdeZ/VM4P0gTSDwcXDVSxFBSikDJyAzpgaUiGjbRaLHXoAbPX5EBbgD63f8AjyACsSgpwJP4ArkiADgDLx+ePzvU+kfeIkIDsxj9x/OFL7jcDWHqzlczeFqDldzuJrD1Ryu5nA1h6s5XM3hag5Xc7iaw9UcruZwNYcLNPre2aCH7y4G1YCVPtIWgCOf1RYmWpbTtFv+Ni/P5g1xTodLg4W2uAAAAABJRU5ErkJggg=="></image>
</defs>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 40 40" id="3i3Hf">
    <path fill="#F28A30" d="m21.5,24c-1.293,0 -3,0 -3,0l0,6l-4,0l0,-16l-3,0l0,-4c0,0 6.551,0 10,0c3.866,0 7,3.134 7,7s-3.134,7 -7,7zm0,-10c-0.797,0 -3,0 -3,0l0,6c0,0 2.203,0 3,0c1.657,0 3,-1.343 3,-3s-1.343,-3 -3,-3z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 40 40" id="1WWDM">
    <path fill="#4489A7" d="m28,30l-4,0l-4,-13.333l-4,13.333l-4,0l-6,-20l4,0l4,13.333l4,-13.333l4,0l4,13.333l2.8,-9.333l-2.8,0l0,-4l8,0l-6,20z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" id="3vA2u">
    <path d="M15.2426 8.91077C15.6568 8.91077 15.9926 8.57498 15.9926 8.16077C15.9926 7.74655 15.6568 7.41077 15.2426 7.41077H8.75739C8.34317 7.41077 8.00739 7.74655 8.00739 8.16077C8.00739 8.57498 8.34317 8.91077 8.75739 8.91077H15.2426Z"></path>
    <path d="M15.9926 11.9101C15.9926 12.3243 15.6568 12.6601 15.2426 12.6601H8.75739C8.34317 12.6601 8.00739 12.3243 8.00739 11.9101C8.00739 11.4959 8.34317 11.1601 8.75739 11.1601H15.2426C15.6568 11.1601 15.9926 11.4959 15.9926 11.9101Z"></path>
    <path d="M10.7574 16.4095C11.1716 16.4095 11.5074 16.0738 11.5074 15.6595C11.5074 15.2453 11.1716 14.9095 10.7574 14.9095H8.75739C8.34317 14.9095 8.00739 15.2453 8.00739 15.6595C8.00739 16.0738 8.34317 16.4095 8.75739 16.4095H10.7574Z"></path>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M4.5 5.75C4.5 4.23122 5.73122 3 7.25 3H16.75C18.2688 3 19.5 4.23122 19.5 5.75V15.11C19.5 15.7313 19.2896 16.3342 18.9032 16.8206L16.4086 19.9606C15.8869 20.6173 15.094 21 14.2554 21H7.25C5.73121 21 4.5 19.7688 4.5 18.25V5.75ZM7.25 4.5C6.55964 4.5 6 5.05964 6 5.75V18.25C6 18.9404 6.55964 19.5 7.25 19.5H13.5947V15.6655C13.5947 15.2513 13.9305 14.9155 14.3447 14.9155H18V5.75C18 5.05964 17.4404 4.5 16.75 4.5H7.25ZM15.2341 19.0276C15.1914 19.0813 15.1448 19.1309 15.0947 19.1763V16.4155H17.3093L15.2341 19.0276Z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" id="2RlRl">
    <rect x="7.5" y="7.5" width="9" height="9" fill="white"></rect>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M19.0325 6.03862C19.8069 6.24695 20.4168 6.86079 20.6238 7.64031C21 9.05311 21 12.0009 21 12.0009C21 12.0009 21 14.9486 20.6238 16.3615C20.4168 17.141 19.8069 17.7549 19.0325 17.9633C17.6288 18.3418 12 18.3418 12 18.3418C12 18.3418 6.37122 18.3418 4.96752 17.9633C4.19311 17.7549 3.58316 17.141 3.37616 16.3615C3 14.9486 3 12.0009 3 12.0009C3 12.0009 3 9.05311 3.37616 7.64031C3.58316 6.86079 4.19311 6.24695 4.96752 6.03862C6.37122 5.66 12 5.66 12 5.66C12 5.66 17.6288 5.66 19.0325 6.03862ZM10.1591 9.32454V14.6773L14.8636 12.001L10.1591 9.32454Z" fill="#E64E40"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" id="1Mmox">
    <path d="M2.25011 15.1239C2.24657 15.4385 2.32611 15.7538 2.48875 16.0358L4.66472 19.8082C4.98343 20.3608 5.57282 20.7012 6.21071 20.7012H17.7891C18.427 20.7012 19.0164 20.3608 19.3351 19.8082L21.5111 16.0358C21.6737 15.7538 21.7533 15.4385 21.7497 15.1239H2.25011Z" fill="#4285F4"></path>
    <path d="M18.6886 20.4578L8.92798 3.55189C9.19986 3.38975 9.5146 3.3 9.84212 3.3H14.157C14.7946 3.3 15.3838 3.64017 15.7026 4.19237L21.5104 14.2517C21.8291 14.8036 21.8292 15.4837 21.5107 16.0358L19.3348 19.8082C19.1768 20.082 18.9524 20.3038 18.6886 20.4578Z" fill="#FBBC04"></path>
    <path d="M15.0715 3.55189C14.7996 3.38975 14.4849 3.3 14.1574 3.3H9.84248C9.20485 3.3 8.61566 3.64017 8.29685 4.19237L2.48911 14.2517C2.17043 14.8036 2.17029 15.4837 2.48875 16.0358L4.66472 19.8082C4.82265 20.082 5.04705 20.3038 5.31088 20.4578L15.0715 3.55189Z" fill="#34A853"></path>
    <path d="M21.7499 15.1239C21.7535 15.4385 21.6739 15.7538 21.5113 16.0358L19.3353 19.8082C19.1774 20.082 18.953 20.3038 18.6891 20.4578L15.6096 15.1239H21.7499Z" fill="#EA4335"></path>
    <path d="M15.0711 3.55189C14.7993 3.38975 14.4845 3.3 14.157 3.3H9.84212C9.5146 3.3 9.19986 3.38975 8.92798 3.55189L11.9996 8.87203L15.0711 3.55189Z" fill="#188038"></path>
    <path d="M2.25011 15.1239C2.24657 15.4385 2.32611 15.7538 2.48875 16.0358L4.66472 19.8082C4.82265 20.082 5.04705 20.3038 5.31088 20.4578L8.3904 15.1239H2.25011Z" fill="#1967D2"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 14 14" id="5jqQY">
  <path d="M5.90909 9.54545C5.90909 8.74219 5.25781 8.09091 4.45455 8.09091C3.65128 8.09091 3 8.74219 3 9.54545C3 10.3487 3.65128 11 4.45455 11C5.25781 11 5.90909 10.3487 5.90909 9.54545Z"></path>
  <path d="M4.45455 3C5.25781 3 5.90909 3.65128 5.90909 4.45454C5.90909 5.25781 5.25781 5.90909 4.45455 5.90909C3.65128 5.90909 3 5.25781 3 4.45455C3 3.65128 3.65128 3 4.45455 3Z"></path>
  <path d="M11 9.54545C11 8.74219 10.3487 8.09091 9.54543 8.09091C8.74216 8.09091 8.09088 8.74219 8.09088 9.54545C8.09088 10.3487 8.74216 11 9.54543 11C10.3487 11 11 10.3487 11 9.54545Z"></path>
  <path d="M9.54543 3C10.3487 3 11 3.65128 11 4.45454C11 5.25781 10.3487 5.90909 9.54543 5.90909C8.74216 5.90909 8.09088 5.25781 8.09088 4.45455C8.09088 3.65128 8.74216 3 9.54543 3Z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" fill="none" id="3UXhH">
  <circle cx="14" cy="14" r="14" fill="#CCCCCC"></circle>
  <mask id="3UXhH_avatar_default" mask-type="alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="28" height="28">
    <circle cx="14" cy="14" r="14" fill="#CCCCCC"></circle>
  </mask>
  <g mask="url(#3UXhH_avatar_default)">
    <path d="M20.9703 21.1425C23.587 21.9946 24 22.3461 24 23.7793V28.8095C24 29.3618 23.5523 29.8095 23 29.8095H5.54913C4.69357 29.8095 4 29.1114 4 28.2503V23.7793C4 22.3461 4.41302 21.9946 7.02974 21.1425C9.46314 20.3501 10.821 19.4989 11.452 18.9271C11.8311 18.5837 11.6957 18.1198 11.3483 17.7441C10.5114 16.839 9.26241 15.2028 8.88272 12.7265C8.40433 9.60662 10.2137 6 14 6C17.7863 6 19.5957 9.60662 19.1173 12.7265C18.7376 15.2028 17.4886 16.839 16.6517 17.7441C16.3043 18.1198 16.1689 18.5837 16.548 18.9271C17.179 19.4989 18.5369 20.3501 20.9703 21.1425Z" fill="#737373"></path>
  </g>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" id="1fzNC">
<path d="M16.033 9.62529C16.2416 9.37251 16.2016 9.00193 15.9436 8.79756C15.6856 8.59318 15.3074 8.63242 15.0988 8.8852L10.9721 13.8865L8.84097 11.9627C8.59699 11.7424 8.21699 11.7577 7.9922 11.9967C7.76742 12.2358 7.78298 12.6081 8.02697 12.8284L10.6293 15.1776C10.7514 15.2879 10.9146 15.3436 11.0802 15.3318C11.2458 15.3199 11.399 15.2414 11.5034 15.1148L16.033 9.62529Z" fill="currentColor"></path>
<path fill-rule="evenodd" clip-rule="evenodd" d="M20.3333 12C20.3333 16.6024 16.6023 20.3333 12 20.3333C7.39759 20.3333 3.66663 16.6024 3.66663 12C3.66663 7.39762 7.39759 3.66666 12 3.66666C16.6023 3.66666 20.3333 7.39762 20.3333 12ZM19.2916 12C19.2916 16.0271 16.027 19.2917 12 19.2917C7.97288 19.2917 4.70829 16.0271 4.70829 12C4.70829 7.97291 7.97288 4.70832 12 4.70832C16.027 4.70832 19.2916 7.97291 19.2916 12Z" fill="currentColor"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" id="Y-nUC">
  <circle cx="11.9243" cy="12" r="8" style="fill-opacity: var(--fill-opacity, 0); fill: var(--fill-color, transparent)"></circle>
  <circle cx="11.9243" cy="12" r="8.5" style="stroke-opacity: var(--stroke-opacity, 0); stroke: var(--stroke-color, transparent)"></circle>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M12 19.2917C16.027 19.2917 19.2916 16.0271 19.2916 12C19.2916 7.97291 16.027 4.70832 12 4.70832C7.97288 4.70832 4.70829 7.97291 4.70829 12C4.70829 16.0271 7.97288 19.2917 12 19.2917ZM12 20.3333C16.6023 20.3333 20.3333 16.6024 20.3333 12C20.3333 7.39762 16.6023 3.66666 12 3.66666C7.39759 3.66666 3.66663 7.39762 3.66663 12C3.66663 16.6024 7.39759 20.3333 12 20.3333Z" fill="currentColor"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" id="3wr7H">
<path d="M16.8396 9.15036C17.09 8.84702 17.0419 8.40233 16.7324 8.15709C16.4227 7.91183 15.9689 7.95892 15.7186 8.26225L10.7665 14.2638L8.20921 11.9552C7.91644 11.6909 7.46043 11.7092 7.19069 11.9961C6.92096 12.283 6.93963 12.7298 7.23241 12.9941L10.3552 15.8131C10.5017 15.9455 10.6976 16.0124 10.8963 15.9981C11.095 15.9839 11.2788 15.8896 11.4041 15.7378L16.8396 9.15036Z" fill="currentColor"></path>
<path fill-rule="evenodd" clip-rule="evenodd" d="M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12ZM20.75 12C20.75 16.8325 16.8325 20.75 12 20.75C7.16751 20.75 3.25 16.8325 3.25 12C3.25 7.16751 7.16751 3.25 12 3.25C16.8325 3.25 20.75 7.16751 20.75 12Z" fill="currentColor"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" id="nvjjv">
  <circle cx="11.9243" cy="12" r="8" style="fill-opacity: var(--fill-opacity, 0); fill: var(--fill-color, transparent)"></circle>
  <circle cx="11.9243" cy="12" r="8.5" style="stroke-opacity: var(--stroke-opacity, 0); stroke: var(--stroke-color, transparent)"></circle>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M12 20.75C16.8325 20.75 20.75 16.8325 20.75 12C20.75 7.16751 16.8325 3.25 12 3.25C7.16751 3.25 3.25 7.16751 3.25 12C3.25 16.8325 7.16751 20.75 12 20.75ZM12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" fill="currentColor"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" id="34nKL">
  <circle style="fill-opacity: var(--fill-opacity, 0); fill: var(--fill-color, transparent)" cx="12" cy="12" r="8"></circle>
  <path d="M16.0331 9.62529C16.2418 9.37251 16.2017 9.00193 15.9437 8.79756C15.6857 8.59318 15.3075 8.63242 15.0989 8.8852L10.9722 13.8865L8.84109 11.9627C8.59712 11.7424 8.21711 11.7577 7.99232 11.9967C7.76755 12.2358 7.78311 12.6081 8.02709 12.8284L10.6294 15.1776C10.7515 15.2879 10.9148 15.3436 11.0804 15.3318C11.2459 15.3199 11.3991 15.2414 11.5035 15.1148L16.0331 9.62529Z" fill="currentColor"></path>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M20.3334 12C20.3334 16.6024 16.6025 20.3333 12.0001 20.3333C7.39771 20.3333 3.66675 16.6024 3.66675 12C3.66675 7.39762 7.39771 3.66666 12.0001 3.66666C16.6025 3.66666 20.3334 7.39762 20.3334 12ZM19.2917 12C19.2917 16.0271 16.0272 19.2917 12.0001 19.2917C7.973 19.2917 4.70841 16.0271 4.70841 12C4.70841 7.97291 7.973 4.70832 12.0001 4.70832C16.0272 4.70832 19.2917 7.97291 19.2917 12Z" fill="currentColor"></path>
  <path style="fill-opacity: 100; fill: var(--obsolete-stroke, #ffffff)" d="M14.52 11.9677C14.8243 11.4393 15.3876 11.1111 16 11.1111C16.612 11.1111 17.1762 11.4391 17.4802 11.969L21.7717 19.4344C22.0758 19.9632 22.076 20.6147 21.7717 21.1434C21.4676 21.6715 20.9049 22 20.2917 22H11.7078C11.0957 22 10.5315 21.672 10.2275 21.142L11.095 20.6445L10.2283 21.1434C9.924 20.6147 9.92389 19.9637 10.228 19.435L14.52 11.9677Z"></path>
  <path style="fill-opacity: 100; fill: var(--obsolete-fill, #F0A00D)" fill-rule="evenodd" clip-rule="evenodd" d="M20.905 20.6445C20.7784 20.8645 20.545 21 20.2917 21H11.7078C11.455 21 11.2212 20.8645 11.095 20.6445C10.9683 20.4245 10.9683 20.1533 11.095 19.9333L15.3867 12.4667C15.5133 12.2467 15.7471 12.1111 16 12.1111C16.2529 12.1111 16.4867 12.2467 16.6128 12.4667L20.905 19.9333C21.0317 20.1533 21.0317 20.4245 20.905 20.6445ZM16 14.7512C15.6548 14.7512 15.375 15.0311 15.375 15.3762V17.4861C15.375 17.8313 15.6548 18.1111 16 18.1111C16.3452 18.1111 16.625 17.8313 16.625 17.4861V15.3762C16.625 15.0311 16.3452 14.7512 16 14.7512ZM16 18.8103C15.6548 18.8103 15.375 19.0901 15.375 19.4353C15.375 19.7805 15.6548 20.0603 16 20.0603C16.3452 20.0603 16.625 19.7805 16.625 19.4353C16.625 19.0901 16.3452 18.8103 16 18.8103Z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" id="3lqcY">
  <circle style="fill-opacity: var(--fill-opacity, 0); fill: var(--fill-color, transparent)" cx="12" cy="12" r="8"></circle>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M12.0001 19.2917C16.0272 19.2917 19.2917 16.0271 19.2917 12C19.2917 7.97291 16.0272 4.70832 12.0001 4.70832C7.973 4.70832 4.70841 7.97291 4.70841 12C4.70841 16.0271 7.973 19.2917 12.0001 19.2917ZM12.0001 20.3333C16.6025 20.3333 20.3334 16.6024 20.3334 12C20.3334 7.39762 16.6025 3.66666 12.0001 3.66666C7.39771 3.66666 3.66675 7.39762 3.66675 12C3.66675 16.6024 7.39771 20.3333 12.0001 20.3333Z" fill="currentColor"></path>
  <path style="fill-opacity: 100; fill: var(--obsolete-stroke, #ffffff)" d="M14.52 11.9677C14.8243 11.4393 15.3876 11.1111 16 11.1111C16.612 11.1111 17.1762 11.4391 17.4802 11.969L21.7717 19.4344C22.0758 19.9632 22.076 20.6147 21.7717 21.1434C21.4676 21.6715 20.9049 22 20.2917 22H11.7078C11.0957 22 10.5315 21.672 10.2275 21.142L11.095 20.6445L10.2283 21.1434C9.924 20.6147 9.92389 19.9637 10.228 19.435L14.52 11.9677Z"></path>
  <path style="fill-opacity: 100; fill: var(--obsolete-fill, #F0A00D)" fill-rule="evenodd" clip-rule="evenodd" d="M20.905 20.6445C20.7784 20.8645 20.545 21 20.2917 21H11.7078C11.455 21 11.2212 20.8645 11.095 20.6445C10.9683 20.4245 10.9683 20.1533 11.095 19.9333L15.3867 12.4667C15.5133 12.2467 15.7471 12.1111 16 12.1111C16.2529 12.1111 16.4867 12.2467 16.6128 12.4667L20.905 19.9333C21.0317 20.1533 21.0317 20.4245 20.905 20.6445ZM16 14.7512C15.6548 14.7512 15.375 15.0311 15.375 15.3762V17.4861C15.375 17.8313 15.6548 18.1111 16 18.1111C16.3452 18.1111 16.625 17.8313 16.625 17.4861V15.3762C16.625 15.0311 16.3452 14.7512 16 14.7512ZM16 18.8103C15.6548 18.8103 15.375 19.0901 15.375 19.4353C15.375 19.7805 15.6548 20.0603 16 20.0603C16.3452 20.0603 16.625 19.7805 16.625 19.4353C16.625 19.0901 16.3452 18.8103 16 18.8103Z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" id="1CKP3">
<path fill-rule="evenodd" clip-rule="evenodd" d="M7.17146 9.09126H16.8271L16.3722 17.3424C16.3052 18.5572 15.3006 19.5079 14.084 19.5079H9.93283C8.71815 19.5079 7.71447 18.5602 7.64493 17.3475L7.17146 9.09126ZM8.4952 10.3413L8.89288 17.2759C8.92449 17.8271 9.3807 18.2579 9.93283 18.2579H14.084C14.637 18.2579 15.0937 17.8258 15.1241 17.2736L15.5063 10.3413H8.4952Z" fill="currentColor"></path>
<path fill-rule="evenodd" clip-rule="evenodd" d="M11.1666 4.08673C9.90091 4.08673 8.8749 5.11275 8.8749 6.3784V6.58348H5.75C5.40482 6.58348 5.125 6.8633 5.125 7.20848C5.125 7.55365 5.40482 7.83348 5.75 7.83348H18.25C18.5952 7.83348 18.875 7.55365 18.875 7.20848C18.875 6.8633 18.5952 6.58348 18.25 6.58348H15.1249V6.3784C15.1249 5.11275 14.0989 4.08673 12.8332 4.08673H11.1666ZM13.8749 6.58348V6.3784C13.8749 5.8031 13.4085 5.33673 12.8332 5.33673H11.1666C10.5913 5.33673 10.1249 5.8031 10.1249 6.3784V6.58348H13.8749Z" fill="currentColor"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" id="1wsrF">
<path fill-rule="evenodd" clip-rule="evenodd" d="M6.20576 8.50952H17.7925L17.2466 18.4109C17.1663 19.8686 15.9608 21.0095 14.5008 21.0095H9.5194C8.06178 21.0095 6.85736 19.8722 6.77391 18.417L6.20576 8.50952ZM7.79424 10.0095L8.27145 18.3311C8.30938 18.9926 8.85684 19.5095 9.5194 19.5095H14.5008C15.1644 19.5095 15.7124 18.9909 15.7489 18.3283L16.2075 10.0095H7.79424Z" fill="currentColor"></path>
<path fill-rule="evenodd" clip-rule="evenodd" d="M10.9999 2.50409C9.48109 2.50409 8.24988 3.73531 8.24988 5.25409V5.50018H4.5C4.08579 5.50018 3.75 5.83597 3.75 6.25018C3.75 6.6644 4.08579 7.00018 4.5 7.00018H19.5C19.9142 7.00018 20.25 6.6644 20.25 6.25018C20.25 5.83597 19.9142 5.50018 19.5 5.50018H15.7499V5.25409C15.7499 3.73531 14.5187 2.50409 12.9999 2.50409H10.9999ZM14.2499 5.50018V5.25409C14.2499 4.56373 13.6902 4.00409 12.9999 4.00409H10.9999C10.3095 4.00409 9.74988 4.56373 9.74988 5.25409V5.50018H14.2499Z" fill="currentColor"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" id="axKZZ">
<path d="M9.68835 14.8227L9.61063 14.4167H9.19727H6.47083H5.64097L6.02878 15.1503C6.88975 16.7791 8.39772 18.0135 10.2124 18.5107L11.5036 18.8645L10.7604 17.7509C10.3086 17.0739 9.92566 16.0624 9.68835 14.8227ZM10.4725 14.4167H9.85505L9.98342 15.0206C10.1224 15.6747 10.3047 16.2646 10.5199 16.7668C10.7737 17.359 11.0566 17.7903 11.3326 18.0627C11.5948 18.3215 11.82 18.4095 12.0002 18.4091C12.1806 18.4095 12.4056 18.3213 12.6676 18.0627C12.9436 17.7903 13.2265 17.359 13.4803 16.7668C13.6955 16.2647 13.8779 15.6748 14.017 15.0206L14.1454 14.4167H13.5279H10.4725ZM9.77577 13.722L9.82528 14.1667H10.2727H13.7277H14.1751L14.2246 13.722C14.2853 13.1768 14.3184 12.5999 14.3184 12C14.3184 11.5559 14.3003 11.1245 14.2663 10.7092L14.2287 10.25H13.768H10.2324H9.7717L9.73409 10.7092C9.70007 11.1245 9.68197 11.5559 9.68197 12C9.68197 12.5999 9.71508 13.1768 9.77577 13.722ZM9.47714 10.7876L9.51771 10.25H8.97856H5.87512H5.46642L5.38512 10.6505C5.29653 11.0869 5.25 11.5383 5.25 12C5.25 12.6225 5.33433 13.2261 5.49263 13.7997L5.59391 14.1667H5.97461H9.01558H9.56928L9.51299 13.6158C9.46009 13.0982 9.43197 12.5571 9.43197 12C9.43197 11.586 9.44746 11.1809 9.47714 10.7876ZM13.6087 10H14.2043L14.1011 9.41337C13.9552 8.58383 13.7417 7.84324 13.4803 7.23318C13.2265 6.64101 12.9436 6.20968 12.6676 5.93729C12.4056 5.67876 12.1807 5.59048 12.0002 5.59087C11.82 5.59047 11.5948 5.67854 11.3326 5.93729C11.0566 6.20968 10.7737 6.64101 10.5199 7.23318C10.2584 7.84332 10.0452 8.58392 9.89929 9.41337L9.79608 10H10.3917H13.6087ZM15.0216 10.25H14.4825L14.5231 10.7876C14.5528 11.1811 14.5684 11.5862 14.5684 12C14.5684 12.5571 14.5403 13.0982 14.4874 13.6158L14.4311 14.1667H14.9848H18.0254H18.4061L18.5074 13.7997C18.6657 13.2261 18.75 12.6225 18.75 12C18.75 11.5384 18.7037 11.0871 18.6151 10.6505L18.5338 10.25H18.1251H15.0216ZM14.3824 9.57887L14.4497 10H14.8762H17.73H18.4941L18.1882 9.29981C17.3797 7.44937 15.7659 6.03151 13.788 5.48948L12.4968 5.13562L13.24 6.24925C13.7409 6.99976 14.1557 8.15914 14.3824 9.57887ZM5.81184 9.29981L5.50592 10H6.27002H9.12423H9.5507L9.61797 9.57887C9.84475 8.15907 10.2595 6.99967 10.7604 6.24917L11.5036 5.13561L10.2124 5.48938C8.23438 6.03135 6.62039 7.44925 5.81184 9.29981ZM14.8029 14.4167H14.3896L14.3119 14.8227C14.0745 16.0624 13.6918 17.0739 13.24 17.7508L12.4968 18.8644L13.788 18.5106C15.6026 18.0133 17.1103 16.779 17.9712 15.1503L18.359 14.4167H17.5292H14.8029ZM19 12C19 15.866 15.866 19.0001 12 19.0001C8.13399 19.0001 5 15.866 5 12C5 8.13402 8.13399 5 12 5C15.866 5 19 8.13402 19 12Z" fill="currentColor" stroke="currentColor"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" fill="none" id="1-2HI">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M10.1652 2.48911L10.05 2.36714C9.45593 1.73783 8.71834 1.25114 7.8918 0.953674C6.61912 0.49564 5.22303 0.517718 3.96546 1.01576C2.70789 1.51381 1.67526 2.4536 1.06129 3.65882C0.447319 4.86405 0.2942 6.25188 0.630662 7.56197C0.967125 8.87205 1.77005 10.0144 2.88879 10.7746C4.00754 11.5348 5.36523 11.8607 6.70716 11.6911C8.04909 11.5215 9.28304 10.8682 10.1775 9.85356C10.7819 9.168 11.0165 8.65828 11.3508 7.68294C11.4186 7.48538 11.4208 7.29978 11.3516 7.14583C11.2819 6.99081 11.1443 6.87807 10.9532 6.81892C10.8063 6.77343 10.6518 6.78139 10.5105 6.86449C10.3707 6.9468 10.2516 7.09824 10.164 7.32511C9.92771 7.93661 9.68386 8.52184 9.23914 9.02629C8.54611 9.81241 7.59005 10.3186 6.55034 10.45C5.51062 10.5814 4.45868 10.3289 3.59189 9.73989C2.72509 9.15088 2.10299 8.26583 1.84231 7.25079C1.58162 6.23574 1.70025 5.16046 2.17595 4.22666C2.65165 3.29286 3.45173 2.56472 4.42609 2.17884C5.40044 1.79295 6.48212 1.77585 7.46819 2.13073C8.16322 2.38087 8.7771 2.8037 9.25575 3.35308L9.35199 3.46354H7.56916C7.22371 3.46354 6.94368 3.74358 6.94368 4.08903C6.94368 4.43447 7.22371 4.71451 7.56916 4.71451H10.7907C11.1361 4.71451 11.4161 4.43447 11.4161 4.08903V1.143C11.4161 0.797555 11.1361 0.517517 10.7907 0.517517C10.4452 0.517517 10.1652 0.797555 10.1652 1.143V2.48911Z" fill="currentColor"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" id="1yn7O">
<path fill-rule="evenodd" clip-rule="evenodd" d="M6.75 4.91666C6.19772 4.91666 5.75 5.36437 5.75 5.91666V6.37499V14.0833V18.875C5.75 19.2202 6.02982 19.5 6.375 19.5C6.72018 19.5 7 19.2202 7 18.875V14.0833H17.6148C18.32 14.0833 18.8036 13.3731 18.5453 12.7169L17.2942 9.53979C17.2707 9.48029 17.271 9.41407 17.2949 9.35475L18.5297 6.29042C18.7946 5.63322 18.3108 4.91666 17.6022 4.91666H6.75Z" fill="currentColor"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" id="1pKdJ">
<path fill-rule="evenodd" clip-rule="evenodd" d="M5.75 5.91666C5.75 5.36437 6.19772 4.91666 6.75 4.91666H17.6022C18.3108 4.91666 18.7946 5.63322 18.5297 6.29042L17.2949 9.35475C17.271 9.41407 17.2707 9.48029 17.2942 9.53979L18.5453 12.7169C18.8036 13.3731 18.32 14.0833 17.6148 14.0833H7V18.875C7 19.2202 6.72018 19.5 6.375 19.5C6.02982 19.5 5.75 19.2202 5.75 18.875V14.0833V6.37499V5.91666ZM7 6.37499V12.8333H16.7892C17.0017 12.8333 17.1468 12.6185 17.0675 12.4213L15.9668 9.68668C15.9186 9.56689 15.9186 9.43309 15.9668 9.3133L17.0675 6.57867C17.1468 6.38153 17.0017 6.16666 16.7892 6.16666H7V6.37499Z" fill="currentColor"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" id="1OSoW">
<path fill-rule="evenodd" clip-rule="evenodd" d="M5.5 3.5C4.94772 3.5 4.5 3.94771 4.5 4.5V5.25V14.5V20.25C4.5 20.6642 4.83579 21 5.25 21C5.66421 21 6 20.6642 6 20.25V14.5H19.0315C19.7367 14.5 20.2203 13.7897 19.9619 13.1336L18.3457 9.02923C18.3223 8.96973 18.3225 8.90351 18.3464 8.84419L19.9464 4.87377C20.2112 4.21656 19.7274 3.5 19.0189 3.5H5.5Z" fill="currentColor"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" id="2kUM4">
<path fill-rule="evenodd" clip-rule="evenodd" d="M4.5 4.5C4.5 3.94771 4.94772 3.5 5.5 3.5H19.0189C19.7274 3.5 20.2112 4.21656 19.9464 4.87377L18.3464 8.84419C18.3225 8.90351 18.3223 8.96973 18.3457 9.02923L19.9619 13.1336C20.2203 13.7897 19.7367 14.5 19.0315 14.5H6V20.25C6 20.6642 5.66421 21 5.25 21C4.83579 21 4.5 20.6642 4.5 20.25V14.5V5.25V4.5ZM6 5.25V13H17.8359C18.0484 13 18.1935 12.7851 18.1142 12.588L16.7451 9.18669C16.6969 9.0669 16.6969 8.9331 16.7451 8.81331L18.1142 5.41202C18.1935 5.21487 18.0484 5 17.8359 5H6V5.25Z" fill="currentColor"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" id="UALsK">
<path d="M11.1278 6.50264L11.1294 5.39004C11.1493 4.9444 11.5096 4.59564 12.0001 4.59564C12.4909 4.59564 12.8515 4.94467 12.8717 5.38965V6.50248L13.3221 6.63351C14.8552 7.07947 15.9799 8.50632 15.9802 10.1996L15.9802 10.2093L15.9802 12.9454C15.9802 13.2544 16.0376 13.5598 16.1484 13.846C16.5275 13.6769 16.9338 13.5576 17.359 13.4968C17.2745 13.326 17.2302 13.1372 17.2302 12.9453L17.2302 10.2093L17.2302 10.1996C17.2302 8.10879 15.9444 6.31826 14.1217 5.58853V5.36667L14.1213 5.35591C14.0811 4.18982 13.1236 3.34564 12.0001 3.34564C10.8761 3.34564 9.91992 4.19029 9.87975 5.35591L9.87939 5.36623L9.87908 5.58821C8.05598 6.31774 6.76979 8.10848 6.76979 10.1996L6.76981 12.9454C6.76981 13.1598 6.71451 13.3702 6.60964 13.5562L6.60894 13.557L6.60662 13.56C6.59581 13.5746 6.55592 13.6285 6.4687 13.7314C6.37481 13.8423 6.25431 13.9775 6.12372 14.1206C5.86307 14.4064 5.5755 14.7094 5.40906 14.8847L5.40804 14.8858C5.36818 14.9278 5.33454 14.9632 5.31049 14.9887C5.30041 14.9994 5.2801 15.0214 5.26862 15.0339L5.26314 15.0398L5.2591 15.0448C5.24783 15.0587 5.21776 15.0958 5.20556 15.1142C5.18199 15.1498 5.1598 15.1862 5.139 15.2233C4.99352 15.4827 4.91669 15.7759 4.91669 16.0746C4.91669 16.1933 4.92863 16.3097 4.95158 16.4227C5.11171 17.2107 5.80491 17.8091 6.64332 17.8091H13.4367C13.4576 17.3737 13.5393 16.9543 13.6735 16.5591H6.64332C6.41701 16.5591 6.22187 16.3968 6.17655 16.1738C6.17016 16.1423 6.16669 16.1092 6.16669 16.0746C6.16669 15.9917 6.18765 15.9105 6.22701 15.8388L6.31722 15.7436C6.48297 15.569 6.77757 15.2587 7.04724 14.963C7.18209 14.8152 7.31421 14.6672 7.42239 14.5395C7.51651 14.4284 7.63261 14.2869 7.69838 14.1703C7.90915 13.7966 8.01981 13.3746 8.01981 12.9454L8.01979 10.1996C8.01979 8.50597 9.14462 7.07943 10.678 6.63346L11.1278 6.50264Z" fill="currentColor"></path>
<path d="M13.5359 19.0113H9.92026C10.1573 19.9454 11.0147 20.6373 12.0361 20.6373C12.7473 20.6373 13.379 20.3018 13.7771 19.7827C13.675 19.5357 13.5939 19.2778 13.5359 19.0113Z" fill="currentColor"></path>
<path d="M18.6391 15.5396C18.6391 15.1944 18.3593 14.9146 18.0141 14.9146C17.6689 14.9146 17.3891 15.1944 17.3891 15.5396V17.4434H15.5428C15.1976 17.4434 14.9178 17.7232 14.9178 18.0684C14.9178 18.4136 15.1976 18.6934 15.5428 18.6934H17.3891V20.5396C17.3891 20.8848 17.6689 21.1646 18.0141 21.1646C18.3593 21.1646 18.6391 20.8848 18.6391 20.5396V18.6934H20.5428C20.888 18.6934 21.1678 18.4136 21.1678 18.0684C21.1678 17.7232 20.888 17.4434 20.5428 17.4434H18.6391V15.5396Z" fill="currentColor"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" id="1bIDe">
<path d="M9.81583 18.6646C10.0605 19.6221 10.9456 20.3313 11.9999 20.3313C13.0542 20.3313 13.9393 19.6221 14.184 18.6646H9.81583Z" fill="currentColor"></path>
<path d="M10.5714 5.50999C10.5714 4.72101 11.211 4.08142 12 4.08142C12.7889 4.08142 13.4285 4.72101 13.4285 5.50999C13.4285 5.57498 13.4242 5.63895 13.4158 5.70164C15.335 6.19344 16.7537 7.93466 16.7537 10.0072L16.7537 10.0172V12.2034C16.7552 12.4802 16.8279 12.7521 16.9649 12.9927L18.4756 15.6466C18.5067 15.6933 18.5342 15.7421 18.558 15.7925C18.5863 15.8524 18.6092 15.9146 18.6266 15.9784C18.653 16.0754 18.6666 16.1759 18.6666 16.2775C18.6666 16.9055 18.1575 17.4147 17.5294 17.4147H6.47045C5.98346 17.4147 5.56797 17.1085 5.4059 16.6782C5.35894 16.5536 5.33325 16.4185 5.33325 16.2774C5.33325 16.0529 5.39971 15.8334 5.52424 15.6466L7.03494 12.9927C7.17336 12.7495 7.24615 12.4745 7.24615 12.1947L7.24618 10.0072C7.24618 7.93463 8.66487 6.19339 10.5841 5.70162C10.5757 5.63894 10.5714 5.57497 10.5714 5.50999Z" fill="currentColor"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" id="vEaL7">
<path d="M10.9533 5.40315L10.9552 4.06802C10.9791 3.53326 11.4115 3.11475 12.0001 3.11475C12.5891 3.11475 13.0218 3.53358 13.046 4.06755V5.40295L13.5865 5.56018C15.4262 6.09534 16.7758 7.80756 16.7762 9.83949L16.7762 9.85115L16.7762 13.1344C16.7762 13.5053 16.8451 13.8717 16.978 14.2152C17.433 14.0122 17.9206 13.8691 18.4307 13.7961C18.3294 13.5912 18.2762 13.3647 18.2762 13.1344L18.2762 9.85114L18.2762 9.83949C18.2762 7.33052 16.7332 5.18189 14.546 4.30621V4.03998L14.5456 4.02707C14.4973 2.62775 13.3482 1.61475 12.0001 1.61475C10.6513 1.61475 9.50388 2.62833 9.45567 4.02707L9.45525 4.03945L9.45487 4.30582C7.26715 5.18126 5.72372 7.33015 5.72372 9.83949L5.72375 13.1344C5.72375 13.3917 5.65739 13.6443 5.53155 13.8674L5.53071 13.8684L5.52792 13.872C5.51495 13.8895 5.46708 13.9542 5.36242 14.0777C5.24974 14.2107 5.10515 14.3729 4.94844 14.5447C4.63566 14.8877 4.29058 15.2512 4.09085 15.4616L4.08962 15.4629C4.04179 15.5133 4.00142 15.5558 3.97256 15.5864C3.96046 15.5993 3.93609 15.6257 3.92232 15.6406L3.91574 15.6477L3.91089 15.6537C3.89737 15.6704 3.86129 15.7149 3.84664 15.7371C3.81836 15.7798 3.79173 15.8234 3.76678 15.8679C3.5922 16.1792 3.5 16.531 3.5 16.8895C3.5 17.0319 3.51433 17.1716 3.54188 17.3072C3.73403 18.2528 4.56587 18.9709 5.57196 18.9709H13.724C13.7491 18.4484 13.8472 17.9451 14.0082 17.4709H5.57196C5.30038 17.4709 5.06622 17.2762 5.01184 17.0085C5.00416 16.9707 5 16.931 5 16.8895C5 16.79 5.02515 16.6926 5.07239 16.6065L5.18064 16.4923C5.37954 16.2828 5.73306 15.9104 6.05667 15.5556C6.21849 15.3782 6.37703 15.2006 6.50685 15.0474C6.61979 14.9141 6.75911 14.7442 6.83803 14.6043C7.09096 14.1559 7.22375 13.6494 7.22375 13.1344L7.22372 9.83949C7.22372 7.80714 8.57352 6.0953 10.4136 5.56013L10.9533 5.40315Z" fill="currentColor"></path>
<path d="M13.8431 20.4136H9.50429C9.78877 21.5345 10.8176 22.3647 12.0433 22.3647C12.8967 22.3647 13.6548 21.9622 14.1325 21.3392C14.01 21.0428 13.9126 20.7334 13.8431 20.4136Z" fill="currentColor"></path>
<path d="M19.9669 16.2475C19.9669 15.8333 19.6311 15.4975 19.2169 15.4975C18.8027 15.4975 18.4669 15.8333 18.4669 16.2475V18.5321H16.2514C15.8371 18.5321 15.5014 18.8679 15.5014 19.2821C15.5014 19.6963 15.8371 20.0321 16.2514 20.0321H18.4669V22.2475C18.4669 22.6617 18.8027 22.9975 19.2169 22.9975C19.6311 22.9975 19.9669 22.6617 19.9669 22.2475V20.0321H22.2514C22.6656 20.0321 23.0014 19.6963 23.0014 19.2821C23.0014 18.8679 22.6656 18.5321 22.2514 18.5321H19.9669V16.2475Z" fill="currentColor"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" id="3cKqc">
<path d="M9.54345 12.4093C9.54345 12.9155 9.13304 13.3259 8.62678 13.3259C8.12052 13.3259 7.71011 12.9155 7.71011 12.4093C7.71011 11.903 8.12052 11.4926 8.62678 11.4926C9.13304 11.4926 9.54345 11.903 9.54345 12.4093Z" fill="currentColor"></path>
<path d="M12.9167 12.4093C12.9167 12.9155 12.5063 13.3259 12 13.3259C11.4938 13.3259 11.0834 12.9155 11.0834 12.4093C11.0834 11.903 11.4938 11.4926 12 11.4926C12.5063 11.4926 12.9167 11.903 12.9167 12.4093Z" fill="currentColor"></path>
<path d="M16.2916 12.4093C16.2916 12.9155 15.8812 13.3259 15.375 13.3259C14.8687 13.3259 14.4583 12.9155 14.4583 12.4093C14.4583 11.903 14.8687 11.4926 15.375 11.4926C15.8812 11.4926 16.2916 11.903 16.2916 12.4093Z" fill="currentColor"></path>
<path d="M9.5434 15.7843C9.5434 16.2905 9.13299 16.7009 8.62673 16.7009C8.12047 16.7009 7.71006 16.2905 7.71006 15.7843C7.71006 15.278 8.12047 14.8676 8.62673 14.8676C9.13299 14.8676 9.5434 15.278 9.5434 15.7843Z" fill="currentColor"></path>
<path d="M12.9167 15.7843C12.9167 16.2905 12.5063 16.7009 12 16.7009C11.4937 16.7009 11.0833 16.2905 11.0833 15.7843C11.0833 15.278 11.4937 14.8676 12 14.8676C12.5063 14.8676 12.9167 15.278 12.9167 15.7843Z" fill="currentColor"></path>
<path d="M16.2917 15.7843C16.2917 16.2905 15.8813 16.7009 15.375 16.7009C14.8688 16.7009 14.4584 16.2905 14.4584 15.7843C14.4584 15.278 14.8688 14.8676 15.375 14.8676C15.8813 14.8676 16.2917 15.278 16.2917 15.7843Z" fill="currentColor"></path>
<path fill-rule="evenodd" clip-rule="evenodd" d="M15.375 3.63959C15.0298 3.63959 14.75 3.91941 14.75 4.26459V5.74225H9.25176V4.26469C9.25176 3.91951 8.97194 3.63969 8.62676 3.63969C8.28158 3.63969 8.00176 3.91951 8.00176 4.26469V5.74225H7.20835C5.9427 5.74225 4.91669 6.76826 4.91669 8.03392V17.2006C4.91669 18.4662 5.9427 19.4922 7.20835 19.4922H16.7917C18.0573 19.4922 19.0834 18.4662 19.0834 17.2006V8.03392C19.0834 6.76826 18.0573 5.74225 16.7917 5.74225H16V4.26459C16 3.91941 15.7202 3.63959 15.375 3.63959ZM14.75 6.99225V8.84792C14.75 9.1931 15.0298 9.47292 15.375 9.47292C15.7202 9.47292 16 9.1931 16 8.84792V6.99225H16.7917C17.367 6.99225 17.8334 7.45862 17.8334 8.03392V17.2006C17.8334 17.7759 17.367 18.2422 16.7917 18.2422H7.20835C6.63306 18.2422 6.16669 17.7759 6.16669 17.2006V8.03392C6.16669 7.45862 6.63306 6.99225 7.20835 6.99225H8.00176V8.84802C8.00176 9.1932 8.28158 9.47302 8.62676 9.47302C8.97194 9.47302 9.25176 9.1932 9.25176 8.84802V6.99225H14.75Z" fill="currentColor"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" id="1jjtR">
<path d="M14.75 4.26459C14.75 3.91941 15.0298 3.63959 15.375 3.63959C15.7202 3.63959 16 3.91941 16 4.26459V5.74225H16.7917C18.0573 5.74225 19.0834 6.76826 19.0834 8.03391V13.5644C18.7392 13.4818 18.3799 13.4381 18.0104 13.4381C17.9511 13.4381 17.8921 13.4392 17.8334 13.4414V8.03391C17.8334 7.45862 17.367 6.99225 16.7917 6.99225H16V8.84792C16 9.1931 15.7202 9.47292 15.375 9.47292C15.0298 9.47292 14.75 9.1931 14.75 8.84792V6.99225H9.25176V8.84802C9.25176 9.1932 8.97194 9.47302 8.62676 9.47302C8.28158 9.47302 8.00176 9.1932 8.00176 8.84802V6.99225H7.20835C6.63306 6.99225 6.16669 7.45862 6.16669 8.03391V17.2006C6.16669 17.7759 6.63306 18.2422 7.20835 18.2422H13.4328C13.4535 18.6776 13.535 19.097 13.6689 19.4922H7.20835C5.9427 19.4922 4.91669 18.4662 4.91669 17.2006V8.03391C4.91669 6.76826 5.9427 5.74225 7.20835 5.74225H8.00176V4.26469C8.00176 3.91951 8.28158 3.63969 8.62676 3.63969C8.97194 3.63969 9.25176 3.91951 9.25176 4.26469V5.74225H14.75V4.26459Z" fill="currentColor"></path>
<path d="M9.54345 12.4093C9.54345 12.9155 9.13304 13.3259 8.62678 13.3259C8.12052 13.3259 7.71011 12.9155 7.71011 12.4093C7.71011 11.903 8.12052 11.4926 8.62678 11.4926C9.13304 11.4926 9.54345 11.903 9.54345 12.4093Z" fill="currentColor"></path>
<path d="M12.9167 12.4093C12.9167 12.9155 12.5063 13.3259 12 13.3259C11.4938 13.3259 11.0834 12.9155 11.0834 12.4093C11.0834 11.903 11.4938 11.4926 12 11.4926C12.5063 11.4926 12.9167 11.903 12.9167 12.4093Z" fill="currentColor"></path>
<path d="M15.375 13.3259C15.8812 13.3259 16.2916 12.9155 16.2916 12.4093C16.2916 11.903 15.8812 11.4926 15.375 11.4926C14.8687 11.4926 14.4583 11.903 14.4583 12.4093C14.4583 12.9155 14.8687 13.3259 15.375 13.3259Z" fill="currentColor"></path>
<path d="M9.5434 15.7843C9.5434 16.2905 9.13299 16.7009 8.62673 16.7009C8.12047 16.7009 7.71006 16.2905 7.71006 15.7843C7.71006 15.278 8.12047 14.8676 8.62673 14.8676C9.13299 14.8676 9.5434 15.278 9.5434 15.7843Z" fill="currentColor"></path>
<path d="M12 16.7009C12.5063 16.7009 12.9167 16.2905 12.9167 15.7843C12.9167 15.278 12.5063 14.8676 12 14.8676C11.4937 14.8676 11.0833 15.278 11.0833 15.7843C11.0833 16.2905 11.4937 16.7009 12 16.7009Z" fill="currentColor"></path>
<path d="M18.6354 15.5274C18.6354 15.1822 18.3555 14.9024 18.0104 14.9024C17.6652 14.9024 17.3854 15.1822 17.3854 15.5274V17.4312H15.5391C15.1939 17.4312 14.9141 17.711 14.9141 18.0562C14.9141 18.4013 15.1939 18.6812 15.5391 18.6812H17.3854V20.5274C17.3854 20.8726 17.6652 21.1524 18.0104 21.1524C18.3555 21.1524 18.6354 20.8726 18.6354 20.5274V18.6812H20.5391C20.8843 18.6812 21.1641 18.4013 21.1641 18.0562C21.1641 17.711 20.8843 17.4312 20.5391 17.4312H18.6354V15.5274Z" fill="currentColor"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" id="3R4jw">
<path d="M9.05211 12.4911C9.05211 13.0987 8.55962 13.5911 7.95211 13.5911C7.3446 13.5911 6.85211 13.0987 6.85211 12.4911C6.85211 11.8836 7.3446 11.3911 7.95211 11.3911C8.55962 11.3911 9.05211 11.8836 9.05211 12.4911Z" fill="currentColor"></path>
<path d="M13.1 12.4911C13.1 13.0987 12.6075 13.5911 12 13.5911C11.3925 13.5911 10.9 13.0987 10.9 12.4911C10.9 11.8836 11.3925 11.3911 12 11.3911C12.6075 11.3911 13.1 11.8836 13.1 12.4911Z" fill="currentColor"></path>
<path d="M17.15 12.4911C17.15 13.0987 16.6575 13.5911 16.05 13.5911C15.4424 13.5911 14.95 13.0987 14.95 12.4911C14.95 11.8836 15.4424 11.3911 16.05 11.3911C16.6575 11.3911 17.15 11.8836 17.15 12.4911Z" fill="currentColor"></path>
<path d="M9.05205 16.5411C9.05205 17.1486 8.55956 17.6411 7.95205 17.6411C7.34454 17.6411 6.85205 17.1486 6.85205 16.5411C6.85205 15.9336 7.34454 15.4411 7.95205 15.4411C8.55956 15.4411 9.05205 15.9336 9.05205 16.5411Z" fill="currentColor"></path>
<path d="M13.1 16.5411C13.1 17.1486 12.6075 17.6411 12 17.6411C11.3925 17.6411 10.9 17.1486 10.9 16.5411C10.9 15.9336 11.3925 15.4411 12 15.4411C12.6075 15.4411 13.1 15.9336 13.1 16.5411Z" fill="currentColor"></path>
<path d="M17.15 16.5411C17.15 17.1486 16.6575 17.6411 16.05 17.6411C15.4425 17.6411 14.95 17.1486 14.95 16.5411C14.95 15.9336 15.4425 15.4411 16.05 15.4411C16.6575 15.4411 17.15 15.9336 17.15 16.5411Z" fill="currentColor"></path>
<path fill-rule="evenodd" clip-rule="evenodd" d="M16.05 1.96753C15.6358 1.96753 15.3 2.30332 15.3 2.71753V4.49072H8.70209V2.71765C8.70209 2.30344 8.3663 1.96765 7.95209 1.96765C7.53787 1.96765 7.20209 2.30344 7.20209 2.71765V4.49072H6.25C4.73122 4.49072 3.5 5.72194 3.5 7.24072V18.2407C3.5 19.7595 4.73122 20.9907 6.25 20.9907H17.75C19.2688 20.9907 20.5 19.7595 20.5 18.2407V7.24072C20.5 5.72194 19.2688 4.49072 17.75 4.49072H16.8V2.71753C16.8 2.30332 16.4642 1.96753 16.05 1.96753ZM15.3 5.99072V8.21753C15.3 8.63174 15.6358 8.96753 16.05 8.96753C16.4642 8.96753 16.8 8.63174 16.8 8.21753V5.99072H17.75C18.4404 5.99072 19 6.55037 19 7.24072V18.2407C19 18.9311 18.4404 19.4907 17.75 19.4907H6.25C5.55964 19.4907 5 18.9311 5 18.2407V7.24072C5 6.55037 5.55964 5.99072 6.25 5.99072H7.20209V8.21765C7.20209 8.63187 7.53787 8.96765 7.95209 8.96765C8.3663 8.96765 8.70209 8.63187 8.70209 8.21765V5.99072H15.3Z" fill="currentColor"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" id="3Lvyr">
<path d="M15.3 2.71753C15.3 2.30332 15.6358 1.96753 16.05 1.96753C16.4642 1.96753 16.8 2.30332 16.8 2.71753V4.49072H17.75C19.2688 4.49072 20.5 5.72194 20.5 7.24072V13.8773C20.087 13.7782 19.6558 13.7257 19.2124 13.7257C19.1413 13.7257 19.0705 13.7271 19 13.7297V7.24072C19 6.55037 18.4404 5.99072 17.75 5.99072H16.8V8.21753C16.8 8.63174 16.4642 8.96753 16.05 8.96753C15.6358 8.96753 15.3 8.63174 15.3 8.21753V5.99072H8.70209V8.21765C8.70209 8.63187 8.3663 8.96765 7.95209 8.96765C7.53787 8.96765 7.20209 8.63187 7.20209 8.21765V5.99072H6.25C5.55964 5.99072 5 6.55037 5 7.24072V18.2407C5 18.9311 5.55964 19.4907 6.25 19.4907H13.7194C13.7442 20.0131 13.842 20.5164 14.0026 20.9907H6.25C4.73122 20.9907 3.5 19.7595 3.5 18.2407V7.24072C3.5 5.72194 4.73122 4.49072 6.25 4.49072H7.20209V2.71765C7.20209 2.30344 7.53787 1.96765 7.95209 1.96765C8.3663 1.96765 8.70209 2.30344 8.70209 2.71765V4.49072H15.3V2.71753Z" fill="currentColor"></path>
<path d="M9.05211 12.4911C9.05211 13.0987 8.55962 13.5911 7.95211 13.5911C7.3446 13.5911 6.85211 13.0987 6.85211 12.4911C6.85211 11.8836 7.3446 11.3911 7.95211 11.3911C8.55962 11.3911 9.05211 11.8836 9.05211 12.4911Z" fill="currentColor"></path>
<path d="M13.1 12.4911C13.1 13.0987 12.6075 13.5911 12 13.5911C11.3925 13.5911 10.9 13.0987 10.9 12.4911C10.9 11.8836 11.3925 11.3911 12 11.3911C12.6075 11.3911 13.1 11.8836 13.1 12.4911Z" fill="currentColor"></path>
<path d="M16.05 13.5911C16.6575 13.5911 17.15 13.0987 17.15 12.4911C17.15 11.8836 16.6575 11.3911 16.05 11.3911C15.4424 11.3911 14.95 11.8836 14.95 12.4911C14.95 13.0987 15.4424 13.5911 16.05 13.5911Z" fill="currentColor"></path>
<path d="M9.05205 16.5411C9.05205 17.1486 8.55956 17.6411 7.95205 17.6411C7.34454 17.6411 6.85205 17.1486 6.85205 16.5411C6.85205 15.9336 7.34454 15.4411 7.95205 15.4411C8.55956 15.4411 9.05205 15.9336 9.05205 16.5411Z" fill="currentColor"></path>
<path d="M12 17.6411C12.6075 17.6411 13.1 17.1486 13.1 16.5411C13.1 15.9336 12.6075 15.4411 12 15.4411C11.3925 15.4411 10.9 15.9336 10.9 16.5411C10.9 17.1486 11.3925 17.6411 12 17.6411Z" fill="currentColor"></path>
<path d="M19.9624 16.2329C19.9624 15.8186 19.6266 15.4829 19.2124 15.4829C18.7982 15.4829 18.4624 15.8186 18.4624 16.2329V18.5174H16.2469C15.8327 18.5174 15.4969 18.8532 15.4969 19.2674C15.4969 19.6816 15.8327 20.0174 16.2469 20.0174H18.4624V22.2329C18.4624 22.6471 18.7982 22.9829 19.2124 22.9829C19.6266 22.9829 19.9624 22.6471 19.9624 22.2329V20.0174H22.2469C22.6611 20.0174 22.9969 19.6816 22.9969 19.2674C22.9969 18.8532 22.6611 18.5174 22.2469 18.5174H19.9624V16.2329Z" fill="currentColor"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" id="2Cfep">
<circle cx="12" cy="12" r="8.325" stroke="currentColor" stroke-width="1.35"></circle>
<path d="M14.3914 15.7656C14.5228 15.8979 14.6956 15.9636 14.8684 15.9636C15.0412 15.9636 15.214 15.8979 15.3454 15.7656C15.6091 15.5019 15.6091 15.0753 15.3454 14.8116L12.4492 11.9154V8.175C12.4492 7.8024 12.1477 7.5 11.7742 7.5C11.4016 7.5 11.1001 7.8024 11.1001 8.175V12.1953C11.1001 12.3744 11.1712 12.5454 11.2972 12.6723L14.3914 15.7656Z" fill="currentColor"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 25 24" fill="none" id="3w0Ej">
<path d="M18.5114 6.54356L19.1002 7.16729V4.71574C19.1002 4.3292 19.4135 4.01584 19.8001 4.01584C20.1866 4.01584 20.5 4.3292 20.5 4.71574V8.96765C20.5 9.35419 20.1866 9.66755 19.8001 9.66755H15.3263C14.9398 9.66755 14.6264 9.3542 14.6264 8.96765C14.6264 8.58111 14.9398 8.26775 15.3263 8.26775H18.1656L17.6734 7.70287C16.9269 6.84602 15.9694 6.18653 14.8854 5.79639C13.3474 5.24288 11.6604 5.26956 10.1407 5.87142C8.621 6.47327 7.37313 7.60894 6.63119 9.06537C5.88925 10.5218 5.70422 12.1989 6.11081 13.782C6.5174 15.3652 7.48767 16.7456 8.8396 17.6643C10.1915 18.5829 11.8322 18.9768 13.4538 18.7718C15.0755 18.5669 16.5542 17.7579 17.6351 16.5318C18.3353 15.7377 18.7337 14.9812 19.0759 13.9165C19.1731 13.6249 19.4979 13.1535 20.0266 13.3376C20.5553 13.5217 20.504 14.0263 20.4148 14.294C19.9173 15.7454 19.5773 16.4791 18.6975 17.477C17.3912 18.9587 15.5891 19.913 13.6293 20.1606C11.6695 20.4083 9.68671 19.9323 8.05285 18.822C6.419 17.7118 5.24638 16.0435 4.755 14.1303C4.26362 12.217 4.48724 10.1901 5.38391 8.42998C6.28057 6.66983 7.78866 5.29733 9.62526 4.56996C11.4619 3.8426 13.5007 3.81036 15.3594 4.47928C16.5665 4.91371 17.6437 5.62448 18.5114 6.54356Z" fill="currentColor"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" id="15tL3">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M19.2387 18.8994C20.9496 17.1049 22 14.6751 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 14.7145 3.08155 17.1763 4.83724 18.9782C5.14667 19.2957 5.47705 19.5928 5.82613 19.8671C5.8427 19.8802 5.8593 19.8931 5.87596 19.906C7.56801 21.2186 9.69275 22 12 22C14.357 22 16.5235 21.1846 18.2328 19.8204C18.5887 19.5364 18.9248 19.2286 19.2387 18.8994ZM17.6537 18.592C16.1344 19.8962 14.1592 20.6842 12 20.6842C9.88754 20.6842 7.95125 19.9299 6.44574 18.676C6.59688 18.3992 6.75937 18.1388 6.93117 17.912C7.24862 17.493 7.54234 17.2628 7.78709 17.1865L7.78849 17.1861C7.90137 17.1507 8.01577 17.1152 8.1302 17.0798C8.43443 16.9857 8.73886 16.8914 9.01501 16.7963C9.39363 16.666 9.77203 16.5168 10.0811 16.3295C10.3806 16.1479 10.717 15.8696 10.847 15.4366C10.9852 14.9763 10.8358 14.5344 10.586 14.1606C10.1137 13.4556 9.57037 12.3929 9.4246 11.3183C9.28356 10.2787 9.51177 9.27013 10.5286 8.48406L10.5322 8.48123C11.001 8.11356 11.731 7.98787 12.0703 7.98787C12.4156 7.98787 13.1404 8.11282 13.6182 8.48343C14.6312 9.26903 14.8585 10.2777 14.7175 11.3184C14.5719 12.3934 14.0304 13.4564 13.5614 14.1621C13.3125 14.5349 13.1632 14.9751 13.2997 15.4346C13.4285 15.8683 13.7642 16.1472 14.0637 16.3292C14.3726 16.5168 14.7511 16.666 15.1304 16.7964C15.4117 16.8931 15.7219 16.9887 16.0323 17.0844C16.1426 17.1184 16.2532 17.1525 16.3622 17.1865C16.6064 17.2626 16.8993 17.4925 17.2158 17.9113C17.3698 18.1152 17.5162 18.3462 17.6537 18.592ZM18.6134 17.6287C18.504 17.4518 18.3881 17.2802 18.2656 17.1182C17.8853 16.6147 17.3854 16.1274 16.7541 15.9305C16.6331 15.8926 16.5155 15.8564 16.4013 15.8212C16.0982 15.7278 15.8178 15.6414 15.5583 15.5521C15.199 15.4286 14.9304 15.3161 14.7469 15.2046C14.6321 15.1349 14.5849 15.0863 14.5675 15.0647C14.5744 15.0377 14.5955 14.9828 14.656 14.8923L14.657 14.8909C15.1818 14.1012 15.8384 12.846 16.0214 11.4951C16.2091 10.1097 15.8956 8.58447 14.4246 7.44367C13.6315 6.82854 12.5787 6.67208 12.0703 6.67208C11.572 6.67208 10.5099 6.82755 9.72202 7.44446C8.24851 8.58463 7.93274 10.1093 8.12075 11.4952C8.30409 12.8466 8.96315 14.1021 9.49269 14.8926C9.55232 14.9819 9.57329 15.0362 9.58027 15.0631C9.56272 15.0848 9.51515 15.1339 9.39895 15.2043C9.21462 15.3161 8.94555 15.4286 8.58663 15.5522C8.33139 15.6401 8.05748 15.7249 7.76127 15.8166L7.75942 15.8172C7.64153 15.8537 7.52012 15.8912 7.39494 15.9305C6.76396 16.1274 6.26354 16.6143 5.88237 17.1175C5.73658 17.3099 5.59994 17.5158 5.47255 17.728C4.12988 16.1991 3.31579 14.1946 3.31579 12C3.31579 7.20384 7.20384 3.31579 12 3.31579C16.7962 3.31579 20.6842 7.20384 20.6842 12C20.6842 14.1474 19.9048 16.1128 18.6134 17.6287ZM14.5645 15.0855C14.5645 15.0855 14.564 15.0832 14.5647 15.078C14.5652 15.0828 14.5645 15.0855 14.5645 15.0855ZM9.58326 15.084C9.58326 15.084 9.58258 15.0812 9.58303 15.0763C9.58378 15.0816 9.58326 15.084 9.58326 15.084Z" fill="currentColor"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" id="1QFMo">
<path d="M11.9997 5.33301C11.5394 5.33301 11.1663 5.7061 11.1663 6.16634V11.1663H6.16634C5.7061 11.1663 5.33301 11.5394 5.33301 11.9997C5.33301 12.4599 5.7061 12.833 6.16634 12.833H11.1663V17.833C11.1663 18.2932 11.5394 18.6663 11.9997 18.6663C12.4599 18.6663 12.833 18.2932 12.833 17.833V12.833H17.833C18.2932 12.833 18.6663 12.4599 18.6663 11.9997C18.6663 11.5394 18.2932 11.1663 17.833 11.1663H12.833V6.16634C12.833 5.7061 12.4599 5.33301 11.9997 5.33301Z" fill="currentColor"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" id="cEyUh">
<path d="M8.1772 8.1772C7.93312 8.42128 7.93312 8.817 8.1772 9.06108L11.2708 12.1547L8.1772 15.2483C7.93312 15.4923 7.93312 15.8881 8.1772 16.1321C8.42128 16.3762 8.817 16.3762 9.06108 16.1321L12.1547 13.0386L15.2483 16.1321C15.4923 16.3762 15.8881 16.3762 16.1321 16.1321C16.3762 15.8881 16.3762 15.4923 16.1321 15.2483L13.0386 12.1547L16.1321 9.06108C16.3762 8.817 16.3762 8.42128 16.1321 8.1772C15.8881 7.93312 15.4923 7.93312 15.2483 8.1772L12.1547 11.2708L9.06108 8.1772C8.817 7.93312 8.42128 7.93312 8.1772 8.1772Z" fill="currentColor"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" id="1_R9K">
<path fill-rule="evenodd" clip-rule="evenodd" d="M7 13.5C6.17157 13.5 5.5 12.8284 5.5 12C5.5 11.1716 6.17157 10.5 7 10.5C7.82843 10.5 8.5 11.1716 8.5 12C8.5 12.8284 7.82843 13.5 7 13.5ZM12 13.5C11.1716 13.5 10.5 12.8284 10.5 12C10.5 11.1716 11.1716 10.5 12 10.5C12.8284 10.5 13.5 11.1716 13.5 12C13.5 12.8284 12.8284 13.5 12 13.5ZM17 13.5C16.1716 13.5 15.5 12.8284 15.5 12C15.5 11.1716 16.1716 10.5 17 10.5C17.8284 10.5 18.5 11.1716 18.5 12C18.5 12.8284 17.8284 13.5 17 13.5Z" fill="currentColor"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" id="j0285">
<path d="M19.7251 20C20.181 20 20.6011 19.756 20.829 19.36C21.057 18.964 21.057 18.476 20.829 18.08L13.1031 4.63999C12.876 4.24399 12.4551 4 12 4C11.5449 4 11.124 4.24399 10.8961 4.63999L3.17098 18.08C2.94301 18.476 2.94301 18.964 3.17098 19.36C3.3981 19.756 3.81893 20 4.27408 20H19.7251ZM4.86489 18.4L12 5.98798L19.1351 18.4H4.86489Z"></path>
<path d="M12.75 10.5752C12.75 10.161 12.4142 9.82523 12 9.82523C11.5858 9.82523 11.25 10.161 11.25 10.5752V13.2979C11.25 13.7121 11.5858 14.0479 12 14.0479C12.4142 14.0479 12.75 13.7121 12.75 13.2979V10.5752Z"></path>
<path d="M12.875 15.9511C12.875 16.4344 12.4832 16.8261 12 16.8261C11.5168 16.8261 11.125 16.4344 11.125 15.9511C11.125 15.4679 11.5168 15.0761 12 15.0761C12.4832 15.0761 12.875 15.4679 12.875 15.9511Z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 14 14" fill="none" id="1yJHf">
<path fill-rule="evenodd" clip-rule="evenodd" d="M3.625 2.04163C3.07272 2.04163 2.625 2.48934 2.625 3.04163V3.06246V8.45829V11.8125C2.625 12.0541 2.82088 12.25 3.0625 12.25C3.30412 12.25 3.5 12.0541 3.5 11.8125V8.45829H10.4898C11.195 8.45829 11.6786 7.74803 11.4203 7.0919L10.7169 5.30562C10.6934 5.24612 10.6937 5.1799 10.7176 5.12058L11.4047 3.41539C11.6696 2.75819 11.1858 2.04163 10.4772 2.04163H3.625Z" fill="currentColor"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 14 14" fill="none" id="1qb1C">
<path d="M5.32705 10.9748C5.51429 11.7074 6.19149 12.25 6.9982 12.25C7.80492 12.25 8.48211 11.7074 8.66936 10.9748H5.32705Z" fill="currentColor"></path>
<path d="M6.9982 1.75C6.50377 1.75 6.10295 2.15082 6.10295 2.64526C6.10295 2.68598 6.10566 2.72607 6.11093 2.76535C4.90816 3.07353 4.01909 4.16474 4.01909 5.46359V6.54166C4.01909 6.90892 3.92356 7.26987 3.74187 7.58904L2.94 8.99774C2.86196 9.1148 2.82031 9.25235 2.82031 9.39305C2.82031 9.78663 3.13938 10.1057 3.53296 10.1057H10.4634C10.857 10.1057 11.1761 9.78663 11.1761 9.39305C11.1761 9.25235 11.1344 9.1148 11.0564 8.99774L10.2545 7.58905C10.0728 7.26987 9.97726 6.90891 9.97726 6.54164V5.46983L9.97726 5.46359C9.97726 4.16475 9.08822 3.07356 7.88548 2.76536C7.89074 2.72608 7.89346 2.68599 7.89346 2.64526C7.89346 2.15082 7.49264 1.75 6.9982 1.75Z" fill="currentColor"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" fill="none" id="dZUo_">
<circle cx="9" cy="9" r="9" fill="#262626"></circle>
<path fill-rule="evenodd" clip-rule="evenodd" d="M10.9697 5.96967C11.2626 5.67678 11.7374 5.67678 12.0303 5.96967C12.3232 6.26256 12.3232 6.73744 12.0303 7.03033L10.0607 9L12.0303 10.9697C12.3232 11.2626 12.3232 11.7374 12.0303 12.0303C11.7374 12.3232 11.2626 12.3232 10.9697 12.0303L9 10.0607L7.03033 12.0303C6.73744 12.3232 6.26256 12.3232 5.96967 12.0303C5.67678 11.7374 5.67678 11.2626 5.96967 10.9697L7.93934 9L5.96967 7.03033C5.67678 6.73744 5.67678 6.26256 5.96967 5.96967C6.26256 5.67678 6.73744 5.67678 7.03033 5.96967L9 7.93934L10.9697 5.96967Z" fill="#A6A6A6"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" fill="none" id="2pTRO">
<circle cx="9" cy="9" r="9" fill="#A6A6A6"></circle>
<path fill-rule="evenodd" clip-rule="evenodd" d="M10.9697 5.96967C11.2626 5.67678 11.7374 5.67678 12.0303 5.96967C12.3232 6.26256 12.3232 6.73744 12.0303 7.03033L10.0607 9L12.0303 10.9697C12.3232 11.2626 12.3232 11.7374 12.0303 12.0303C11.7374 12.3232 11.2626 12.3232 10.9697 12.0303L9 10.0607L7.03033 12.0303C6.73744 12.3232 6.26256 12.3232 5.96967 12.0303C5.67678 11.7374 5.67678 11.2626 5.96967 10.9697L7.93934 9L5.96967 7.03033C5.67678 6.73744 5.67678 6.26256 5.96967 5.96967C6.26256 5.67678 6.73744 5.67678 7.03033 5.96967L9 7.93934L10.9697 5.96967Z" fill="#F2F2F2"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" id="2GZYH">
<path fill-rule="evenodd" clip-rule="evenodd" d="M7.35714 5.32393C7.35714 4.8637 7.73023 4.4906 8.19047 4.4906C8.65071 4.4906 9.0238 4.8637 9.0238 5.32393V7.00001H14.9063V5.32393C14.9063 4.8637 15.2794 4.4906 15.7396 4.4906C16.1998 4.4906 16.5729 4.8637 16.5729 5.32393V7.00001H16.7315C17.8002 7.00001 18.6666 7.86641 18.6666 8.93517V17.5648C18.6666 18.6336 17.8002 19.5 16.7315 19.5H7.26848C6.19972 19.5 5.33331 18.6336 5.33331 17.5648V8.93517C5.33331 7.86641 6.19972 7.00001 7.26848 7.00001H7.35714V5.32393ZM9.25136 12.5842C9.25136 13.0904 8.84064 13.5009 8.334 13.5009C7.82736 13.5009 7.41665 13.0904 7.41665 12.5842C7.41665 12.0779 7.82736 11.6675 8.334 11.6675C8.84064 11.6675 9.25136 12.0779 9.25136 12.5842ZM12 13.5009C12.5066 13.5009 12.9173 13.0904 12.9173 12.5842C12.9173 12.0779 12.5066 11.6675 12 11.6675C11.4933 11.6675 11.0826 12.0779 11.0826 12.5842C11.0826 13.0904 11.4933 13.5009 12 13.5009ZM16.5833 12.5842C16.5833 13.0904 16.1726 13.5009 15.666 13.5009C15.1593 13.5009 14.7486 13.0904 14.7486 12.5842C14.7486 12.0779 15.1593 11.6675 15.666 11.6675C16.1726 11.6675 16.5833 12.0779 16.5833 12.5842ZM8.33402 17.4164C8.84029 17.4164 9.25069 17.0063 9.25069 16.5004C9.25069 15.9945 8.84029 15.5845 8.33402 15.5845C7.82776 15.5845 7.41736 15.9945 7.41736 16.5004C7.41736 17.0063 7.82776 17.4164 8.33402 17.4164ZM12.916 16.5004C12.916 17.0063 12.5055 17.4164 11.9993 17.4164C11.493 17.4164 11.0826 17.0063 11.0826 16.5004C11.0826 15.9945 11.493 15.5845 11.9993 15.5845C12.5055 15.5845 12.916 15.9945 12.916 16.5004ZM15.666 17.4164C16.1722 17.4164 16.5826 17.0063 16.5826 16.5004C16.5826 15.9945 16.1722 15.5845 15.666 15.5845C15.1597 15.5845 14.7493 15.9945 14.7493 16.5004C14.7493 17.0063 15.1597 17.4164 15.666 17.4164Z" fill="currentColor"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" id="21xb-">
<path fill-rule="evenodd" clip-rule="evenodd" d="M6.42859 3.98871C6.42859 3.43642 6.8763 2.98871 7.42859 2.98871C7.98087 2.98871 8.42859 3.43642 8.42859 3.98871V6H15.4875V3.98871C15.4875 3.43642 15.9353 2.98871 16.4875 2.98871C17.0398 2.98871 17.4875 3.43642 17.4875 3.98871V6H17.6778C18.9603 6 20 7.03968 20 8.3222V18.6778C20 19.9603 18.9603 21 17.6778 21H6.3222C5.03968 21 4 19.9603 4 18.6778V8.3222C4 7.03968 5.03968 6 6.3222 6H6.42859V3.98871ZM8.70166 12.701C8.70166 13.3085 8.2088 13.801 7.60083 13.801C6.99286 13.801 6.5 13.3085 6.5 12.701C6.5 12.0935 6.99286 11.601 7.60083 11.601C8.2088 11.601 8.70166 12.0935 8.70166 12.701ZM12 13.801C12.608 13.801 13.1008 13.3085 13.1008 12.701C13.1008 12.0935 12.608 11.601 12 11.601C11.392 11.601 10.8992 12.0935 10.8992 12.701C10.8992 13.3085 11.392 13.801 12 13.801ZM17.5 12.701C17.5 13.3085 17.0071 13.801 16.3992 13.801C15.7912 13.801 15.2983 13.3085 15.2983 12.701C15.2983 12.0935 15.7912 11.601 16.3992 11.601C17.0071 11.601 17.5 12.0935 17.5 12.701ZM7.60085 18.4997C8.20837 18.4997 8.70085 18.0076 8.70085 17.4005C8.70085 16.7934 8.20837 16.3013 7.60085 16.3013C6.99334 16.3013 6.50085 16.7934 6.50085 17.4005C6.50085 18.0076 6.99334 18.4997 7.60085 18.4997ZM13.0992 17.4005C13.0992 18.0076 12.6067 18.4997 11.9992 18.4997C11.3917 18.4997 10.8992 18.0076 10.8992 17.4005C10.8992 16.7934 11.3917 16.3013 11.9992 16.3013C12.6067 16.3013 13.0992 16.7934 13.0992 17.4005ZM16.3992 18.4997C17.0067 18.4997 17.4992 18.0076 17.4992 17.4005C17.4992 16.7934 17.0067 16.3013 16.3992 16.3013C15.7917 16.3013 15.2992 16.7934 15.2992 17.4005C15.2992 18.0076 15.7917 18.4997 16.3992 18.4997Z" fill="currentColor"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" id="HK8YQ">
<path d="M9.37903 19.9976C9.67269 21.1465 10.7347 21.9975 11.9999 21.9975C13.2651 21.9975 14.3272 21.1465 14.6208 19.9976H9.37903Z" fill="currentColor"></path>
<path d="M10.2857 4.21197C10.2857 3.26519 11.0532 2.49768 12 2.49768C12.9468 2.49768 13.7143 3.26519 13.7143 4.21197C13.7143 4.28995 13.7091 4.36672 13.699 4.44194C16.0021 5.0321 17.7044 7.12157 17.7044 9.60865L17.7044 9.62059V12.2441C17.7062 12.5763 17.7935 12.9024 17.9579 13.1912L19.7708 16.3759C19.8081 16.432 19.8411 16.4905 19.8696 16.551C19.9035 16.6229 19.9311 16.6975 19.9519 16.7741C19.9836 16.8904 20 17.0111 20 17.133C20 17.8866 19.389 18.4976 18.6354 18.4976H5.36458C4.78019 18.4976 4.2816 18.1302 4.08711 17.6138C4.03077 17.4643 3.99994 17.3022 3.99994 17.1329C3.99994 16.8635 4.07968 16.6001 4.22912 16.3759L6.04196 13.1912C6.20807 12.8994 6.29542 12.5694 6.29542 12.2336L6.29545 9.60865C6.29545 7.12153 7.99788 5.03204 10.301 4.44192C10.2909 4.3667 10.2857 4.28994 10.2857 4.21197Z" fill="currentColor"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" id="jTja2">
  <path d="M15.456 14.2337C15.04 14.1267 14.6486 14.0076 14.3254 13.843C14.1615 13.7594 14.0617 13.5937 14.0348 13.4117C14.0043 13.2049 14.0759 12.998 14.2069 12.8352C14.8105 12.0853 15.5713 10.5876 15.8222 9.08744C16.1784 6.9577 14.8311 4.49573 12.0119 4.49573H12.0024C9.18319 4.49573 7.83597 6.9577 8.19217 9.08744C8.44287 10.5864 9.20258 12.0829 9.80595 12.8334C9.93798 12.9976 10.0111 13.2056 9.98237 13.4144C9.95751 13.595 9.86232 13.7608 9.7031 13.8497C9.2257 14.1161 8.59433 14.2868 7.95351 14.46C6.56155 14.8364 5.125 15.2248 5.125 16.6324V18.6199C5.125 19.1036 5.54993 19.4957 6.07411 19.4957H13.6648C13.5114 19.038 13.4283 18.548 13.4283 18.0387C13.4283 16.4537 14.233 15.0567 15.456 14.2337Z"></path>
  <path d="M18.6361 15.5452C18.6361 15.2 18.3563 14.9202 18.0111 14.9202C17.6659 14.9202 17.3861 15.2 17.3861 15.5452V17.449H15.5398C15.1947 17.449 14.9148 17.7288 14.9148 18.074C14.9148 18.4192 15.1947 18.699 15.5398 18.699H17.3861V20.5452C17.3861 20.8904 17.6659 21.1702 18.0111 21.1702C18.3563 21.1702 18.6361 20.8904 18.6361 20.5452V18.699H20.5398C20.885 18.699 21.1648 18.4192 21.1648 18.074C21.1648 17.7288 20.885 17.449 20.5398 17.449H18.6361V15.5452Z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 6" id="1Bxk7">
	<path fill-rule="evenodd" clip-rule="evenodd" d="M11.0303 5.78033C10.7374 6.07322 10.2626 6.07322 9.96967 5.78033L6 1.81066L2.03033 5.78033C1.73744 6.07322 1.26256 6.07322 0.96967 5.78033C0.676777 5.48744 0.676777 5.01256 0.96967 4.71967L5.46967 0.21967C5.76256 -0.0732232 6.23744 -0.0732232 6.53033 0.21967L11.0303 4.71967C11.3232 5.01256 11.3232 5.48744 11.0303 5.78033Z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 25" id="3wZK7">
<path d="M9.54345 12.7975C9.54345 13.3038 9.13304 13.7142 8.62678 13.7142C8.12052 13.7142 7.71011 13.3038 7.71011 12.7975C7.71011 12.2912 8.12052 11.8808 8.62678 11.8808C9.13304 11.8808 9.54345 12.2912 9.54345 12.7975Z"></path>
<path d="M12.9167 12.7975C12.9167 13.3038 12.5063 13.7142 12 13.7142C11.4938 13.7142 11.0834 13.3038 11.0834 12.7975C11.0834 12.2912 11.4938 11.8808 12 11.8808C12.5063 11.8808 12.9167 12.2912 12.9167 12.7975Z"></path>
<path d="M16.2916 12.7975C16.2916 13.3038 15.8812 13.7142 15.375 13.7142C14.8687 13.7142 14.4583 13.3038 14.4583 12.7975C14.4583 12.2912 14.8687 11.8808 15.375 11.8808C15.8812 11.8808 16.2916 12.2912 16.2916 12.7975Z"></path>
<path d="M9.5434 16.1725C9.5434 16.6788 9.13299 17.0892 8.62673 17.0892C8.12047 17.0892 7.71006 16.6788 7.71006 16.1725C7.71006 15.6662 8.12047 15.2558 8.62673 15.2558C9.13299 15.2558 9.5434 15.6662 9.5434 16.1725Z"></path>
<path d="M12.9167 16.1725C12.9167 16.6788 12.5063 17.0892 12 17.0892C11.4937 17.0892 11.0833 16.6788 11.0833 16.1725C11.0833 15.6662 11.4937 15.2558 12 15.2558C12.5063 15.2558 12.9167 15.6662 12.9167 16.1725Z"></path>
<path d="M16.2917 16.1725C16.2917 16.6788 15.8813 17.0892 15.375 17.0892C14.8688 17.0892 14.4584 16.6788 14.4584 16.1725C14.4584 15.6662 14.8688 15.2558 15.375 15.2558C15.8813 15.2558 16.2917 15.6662 16.2917 16.1725Z"></path>
<path fill-rule="evenodd" clip-rule="evenodd" d="M15.375 4.02783C15.0298 4.02783 14.75 4.30765 14.75 4.65283V6.13049H9.25176V4.65293C9.25176 4.30776 8.97194 4.02793 8.62676 4.02793C8.28158 4.02793 8.00176 4.30776 8.00176 4.65293V6.13049H7.20835C5.9427 6.13049 4.91669 7.15651 4.91669 8.42216V17.5888C4.91669 18.8545 5.9427 19.8805 7.20835 19.8805H16.7917C18.0573 19.8805 19.0834 18.8545 19.0834 17.5888V8.42216C19.0834 7.15651 18.0573 6.13049 16.7917 6.13049H16V4.65283C16 4.30765 15.7202 4.02783 15.375 4.02783ZM14.75 7.38049V9.23617C14.75 9.58134 15.0298 9.86117 15.375 9.86117C15.7202 9.86117 16 9.58134 16 9.23617V7.38049H16.7917C17.367 7.38049 17.8334 7.84686 17.8334 8.42216V17.5888C17.8334 18.1641 17.367 18.6305 16.7917 18.6305H7.20835C6.63306 18.6305 6.16669 18.1641 6.16669 17.5888V8.42216C6.16669 7.84686 6.63306 7.38049 7.20835 7.38049H8.00176V9.23627C8.00176 9.58145 8.28158 9.86127 8.62676 9.86127C8.97194 9.86127 9.25176 9.58145 9.25176 9.23627V7.38049H14.75Z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 25" fill="currentColor" id="1WLQE">
<path fill-rule="evenodd" clip-rule="evenodd" d="M19.7251 20.5C20.181 20.5 20.6011 20.256 20.829 19.86C21.057 19.464 21.057 18.976 20.829 18.58L13.1031 5.13999C12.876 4.74399 12.4551 4.5 12 4.5C11.5449 4.5 11.124 4.74399 10.8961 5.13999L3.17098 18.58C2.94301 18.976 2.94301 19.464 3.17098 19.86C3.3981 20.256 3.81893 20.5 4.27408 20.5H19.7251ZM4.86489 18.9L12 6.48798L19.1351 18.9H4.86489Z"></path>
<path fill-rule="evenodd" clip-rule="evenodd" d="M12.75 11.0752C12.75 10.661 12.4142 10.3252 12 10.3252C11.5858 10.3252 11.25 10.661 11.25 11.0752V13.7979C11.25 14.2121 11.5858 14.5479 12 14.5479C12.4142 14.5479 12.75 14.2121 12.75 13.7979V11.0752Z"></path>
<path d="M12.875 16.4511C12.875 16.9344 12.4832 17.3261 12 17.3261C11.5168 17.3261 11.125 16.9344 11.125 16.4511C11.125 15.9679 11.5168 15.5761 12 15.5761C12.4832 15.5761 12.875 15.9679 12.875 16.4511Z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" id="b4R4w">
<path d="M10.0247 16.1738C10.0247 15.7596 10.3604 15.4238 10.7747 15.4238H11.2689V11.7158H11.0132C10.599 11.7158 10.2632 11.38 10.2632 10.9658C10.2632 10.5516 10.599 10.2158 11.0132 10.2158H12.0189C12.4331 10.2158 12.7689 10.5516 12.7689 10.9658V15.4238H13.2519C13.6661 15.4238 14.0019 15.7596 14.0019 16.1738C14.0019 16.588 13.6661 16.9238 13.2519 16.9238H10.7747C10.3604 16.9238 10.0247 16.588 10.0247 16.1738Z"></path>
<path d="M11.0132 7.8479C11.0132 8.40019 11.461 8.8479 12.0132 8.8479C12.5655 8.8479 13.0132 8.40019 13.0132 7.8479C13.0132 7.29562 12.5655 6.8479 12.0132 6.8479C11.461 6.8479 11.0132 7.29562 11.0132 7.8479Z"></path>
<path fill-rule="evenodd" clip-rule="evenodd" d="M12.0181 19.5C16.1602 19.5 19.5181 16.1421 19.5181 12C19.5181 7.85786 16.1602 4.5 12.0181 4.5C7.87593 4.5 4.51807 7.85786 4.51807 12C4.51807 16.1421 7.87593 19.5 12.0181 19.5ZM12.0181 21C16.9886 21 21.0181 16.9706 21.0181 12C21.0181 7.02944 16.9886 3 12.0181 3C7.0475 3 3.01807 7.02944 3.01807 12C3.01807 16.9706 7.0475 21 12.0181 21Z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" id="1gUIY">
  <g clip-path="url(#1gUIY_clip0)">
    <path style="fill:var(--default-img-bg, #CCCCCC);" d="M0 12C0 5.37258 5.37258 0 12 0C18.6274 0 24 5.37258 24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12Z" fill="#CCC"></path>
    <path style="fill:var(--default-img-head, #737373);" d="M18.8956 24.8939C19.5055 24.8939 20 24.4376 20 23.8748V20.6227C20 18.0061 16.4741 17.2777 14.706 16.3768C14.5152 16.2796 14.3991 16.0867 14.3678 15.8749C14.3323 15.6343 14.4156 15.3935 14.5681 15.2041C15.2705 14.3315 16.1556 12.5887 16.4476 10.8431C16.8621 8.36485 15.2944 5.5 12.0139 5.5H12.0028C8.72225 5.5 7.15458 8.36485 7.56907 10.8431C7.8608 12.5873 8.74482 14.3287 9.44693 15.202C9.60056 15.3931 9.68565 15.6351 9.65222 15.878C9.62329 16.0882 9.51252 16.2812 9.32724 16.3846C7.56507 17.368 4 18.2306 4 20.6227V23.8748C4 24.4376 4.49447 24.8939 5.10442 24.8939H18.8956Z" fill="#737373"></path>
  </g>
  <defs>
    <clipPath id="1gUIY_clip0">
    <path d="M0 12C0 5.37258 5.37258 0 12 0C18.6274 0 24 5.37258 24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12Z" fill="white"></path>
    </clipPath>
  </defs>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" id="2M_D0">
<path d="M17.6254 5.34204C18.1995 5.34204 18.6648 5.80737 18.6648 6.38138L18.6648 9.29302C18.6648 9.63743 18.3856 9.91663 18.0412 9.91663C17.6968 9.91663 17.4176 9.63743 17.4176 9.29302L17.4176 7.47059L11.7668 13.1184C11.5639 13.3214 11.2019 13.2885 10.9584 13.0449C10.7149 12.8014 10.682 12.4394 10.8849 12.2365L16.5357 6.58868L14.7168 6.57778C14.3724 6.57778 14.0932 6.29858 14.0932 5.95418C14.0932 5.60977 14.3724 5.33057 14.7168 5.33057L17.6254 5.34204Z"></path>
<path fill-rule="evenodd" clip-rule="evenodd" d="M6.79728 8.65938H11.5118L12.7683 7.40938H6.79728C5.53163 7.40938 4.50562 8.4354 4.50562 9.70105V17.2042C4.50562 18.4698 5.53163 19.4958 6.79728 19.4958H14.3013C15.567 19.4958 16.593 18.4698 16.593 17.2042V11.2415L15.343 12.4818V17.2042C15.343 17.7795 14.8766 18.2458 14.3013 18.2458H6.79728C6.22198 18.2458 5.75562 17.7795 5.75562 17.2042V9.70105C5.75562 9.12575 6.22198 8.65938 6.79728 8.65938Z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 25" id="fy4F8">
<path fill-rule="evenodd" clip-rule="evenodd" d="M7.17146 9.47962H16.8271L16.3722 17.7308C16.3052 18.9456 15.3006 19.8963 14.084 19.8963H9.93283C8.71815 19.8963 7.71447 18.9485 7.64493 17.7358L7.17146 9.47962ZM8.4952 10.7296L8.89288 17.6643C8.92449 18.2155 9.3807 18.6463 9.93283 18.6463H14.084C14.637 18.6463 15.0937 18.2141 15.1241 17.662L15.5063 10.7296H8.4952Z"></path>
<path fill-rule="evenodd" clip-rule="evenodd" d="M11.1666 4.4751C9.90091 4.4751 8.8749 5.50111 8.8749 6.76677V6.97184H5.75C5.40482 6.97184 5.125 7.25166 5.125 7.59684C5.125 7.94202 5.40482 8.22184 5.75 8.22184H18.25C18.5952 8.22184 18.875 7.94202 18.875 7.59684C18.875 7.25166 18.5952 6.97184 18.25 6.97184H15.1249V6.76676C15.1249 5.50111 14.0989 4.4751 12.8332 4.4751H11.1666ZM13.8749 6.97184V6.76676C13.8749 6.19147 13.4085 5.7251 12.8332 5.7251H11.1666C10.5913 5.7251 10.1249 6.19147 10.1249 6.76677V6.97184H13.8749Z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 8 24" id="2Xfxe">
  <path d="M4.25976 12.1436L2.18306 14.2204C1.93898 14.4644 1.93898 14.8602 2.18306 15.1042C2.42714 15.3483 2.82286 15.3483 3.06694 15.1042L5.57852 12.5927C5.8265 12.3447 5.8265 11.9426 5.57852 11.6946L3.06694 9.18306C2.82286 8.93898 2.42714 8.93898 2.18306 9.18306C1.93898 9.42714 1.93898 9.82286 2.18306 10.0669L4.25976 12.1436Z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 14 14" fill="none" id="2U-tm">
<path d="M10 5H6V6H10V5Z" fill="#A6A6A6"></path>
<path fill-rule="evenodd" clip-rule="evenodd" d="M10 1H2V13H10C11.1046 13 12 12.1046 12 11V3C12 1.89543 11.1046 1 10 1ZM3 12V2H4V12H3ZM5 12V2H10C10.5523 2 11 2.44771 11 3V11C11 11.5523 10.5523 12 10 12H5Z" fill="#A6A6A6"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 14 14" fill="none" id="vsvVj">
<path fill-rule="evenodd" clip-rule="evenodd" d="M7.24921 0.816547L12.2492 3.69155C12.4044 3.78075 12.5 3.94605 12.5 4.125V9.875C12.5 10.054 12.4044 10.2193 12.2492 10.3085L7.24921 13.1835C7.09491 13.2722 6.90509 13.2722 6.75079 13.1835L1.75079 10.3085C1.59564 10.2193 1.5 10.054 1.5 9.875V4.125C1.5 3.94605 1.59564 3.78075 1.75079 3.69155L6.75079 0.816547C6.90509 0.727818 7.09491 0.727818 7.24921 0.816547ZM3.02521 4.11228L7 6.68978L10.9748 4.11226L7 1.82677L3.02521 4.11228ZM6.5 7.55742L2.5 4.96357V9.58574L6.5 11.8857V7.55742ZM7.5 11.8857L11.5 9.58574V4.96352L7.5 7.55741V11.8857Z" fill="#A6A6A6"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" id="3YVIW">
<path fill-rule="evenodd" clip-rule="evenodd" d="M5.49963 7.99612C5.22369 7.99612 5 7.77246 5 7.49652C5 7.22043 5.22394 6.99667 5.5 6.99689L8.99591 6.99963V5.25C8.99591 4.55939 9.30115 4 10.0043 4H13.9985C14.7017 4 14.999 4.55624 14.999 5.24686V6.99649H18.5002C18.7762 6.99649 19 7.22028 19 7.49631C19 7.77237 18.7762 7.99612 18.5002 7.99612H5.49963ZM10 5.74475C10 5.60669 10.1119 5.49475 10.25 5.49475H13.75C13.8881 5.49475 14 5.60669 14 5.74475V6.74686C14 6.88492 13.8881 6.99686 13.75 6.99686H10.25C10.1119 6.99686 10 6.88492 10 6.74686V5.74475ZM15.1818 18.9969C15.885 18.9969 16.4546 18.7525 16.4546 18.0618L17.0009 8.99933H6.99658L7.54547 17.7469C7.54547 18.4375 8.11505 18.9969 8.81824 18.9969H15.1818Z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 14 14" fill="none" id="3u-UD">
<path fill-rule="evenodd" clip-rule="evenodd" d="M2 1H10C11.1046 1 12 1.89543 12 3V7H11V3C11 2.44771 10.5523 2 10 2H5V12H7.5V13H2V1ZM3 12V2H4V12H3Z" fill="#A6A6A6"></path>
<path d="M11.4485 8.63869C11.3751 8.45377 11.1249 8.45377 11.0515 8.63869L10.4474 10.1597C10.4165 10.2376 10.3464 10.2909 10.2661 10.2976L8.69812 10.4293C8.50749 10.4453 8.43019 10.6944 8.57543 10.8247L9.77005 11.8963C9.83128 11.9513 9.85802 12.0375 9.83932 12.1196L9.47434 13.7219C9.42997 13.9167 9.63233 14.0707 9.79554 13.9663L11.1379 13.1076C11.2067 13.0636 11.2933 13.0636 11.3621 13.1076L12.7045 13.9663C12.8677 14.0707 13.07 13.9167 13.0257 13.7219L12.6607 12.1196C12.642 12.0375 12.6687 11.9513 12.7299 11.8963L13.9246 10.8247C14.0698 10.6944 13.9925 10.4453 13.8019 10.4293L12.2339 10.2976C12.1536 10.2909 12.0836 10.2376 12.0526 10.1597L11.4485 8.63869Z" fill="#A6A6A6"></path>
<path d="M10 5.5C10 5.22386 9.77614 5 9.5 5H6.5C6.22386 5 6 5.22386 6 5.5C6 5.77614 6.22386 6 6.5 6H9.5C9.77614 6 10 5.77614 10 5.5Z" fill="#A6A6A6"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" id="2OE5W">
<path fill-rule="evenodd" clip-rule="evenodd" d="M8.85901 20L8.85986 15.4672H6.42859V20H4V4.32205C4 3.59161 4.58521 3 5.30768 3H13.6173C14.3398 3 14.925 3.59161 14.925 4.32205L14.9206 20H8.85901ZM16.1429 8.66809H19.9535C20.5317 8.66809 21 9.23154 21 9.92719V20H16.1429V8.66809ZM17.3571 10.9323H19.7857V13.1987H17.3571V10.9323ZM19.7857 15.4657H17.3571V17.7321H19.7857V15.4657ZM6.42859 8.66812V6.40173H8.85718V8.66812H6.42859ZM6.42859 10.9345V13.2009H8.85718V10.9345H6.42859ZM10.0714 8.66812V6.40173H12.5V8.66812H10.0714ZM10.0714 10.9345V13.2009H12.5V10.9345H10.0714ZM10.0714 17.7336V15.4672H12.5V17.7336H10.0714Z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 10 12" id="3d180">
    <path d="M3.32673 9.97482C3.51398 10.7074 4.19117 11.25 4.99789 11.25C5.80461 11.25 6.4818 10.7074 6.66904 9.97482H3.32673Z"></path>
    <path d="M4.99789 0.75C4.50345 0.75 4.10263 1.15082 4.10263 1.64526C4.10263 1.68598 4.10535 1.72607 4.11062 1.76535C2.90785 2.07353 2.01878 3.16474 2.01878 4.46359V5.54166C2.01878 5.90892 1.92324 6.26987 1.74156 6.58904L0.939686 7.99774C0.861643 8.1148 0.819998 8.25235 0.819998 8.39305C0.819998 8.78663 1.13906 9.10569 1.53264 9.10569H8.4631C8.85669 9.10569 9.17575 8.78663 9.17575 8.39305C9.17575 8.25235 9.1341 8.1148 9.05606 7.99774L8.25417 6.58905C8.07248 6.26987 7.97694 5.90891 7.97694 5.54164V4.46983L7.97695 4.46359C7.97695 3.16475 7.08791 2.07356 5.88516 1.76536C5.89043 1.72608 5.89315 1.68599 5.89315 1.64526C5.89315 1.15082 5.49233 0.75 4.99789 0.75Z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="-2 -2 14 14" fill="none" id="2Ap2A">
<path fill-rule="evenodd" clip-rule="evenodd" d="M5 10C7.76142 10 10 7.76142 10 5C10 2.23858 7.76142 0 5 0C2.23858 0 0 2.23858 0 5C0 7.76142 2.23858 10 5 10ZM4.49911 5.7754L6.79191 3.18013C6.9844 2.96224 7.31832 2.93925 7.53963 3.12695C7.76332 3.31667 7.78849 3.65063 7.59389 3.8709L4.90009 6.92008C4.79853 7.03503 4.65205 7.10011 4.49909 7.1001C4.34612 7.1001 4.19964 7.03502 4.09809 6.92007L2.83114 5.48587C2.63655 5.26559 2.66173 4.93164 2.88543 4.74192C3.10675 4.55423 3.44066 4.57723 3.63314 4.79512L4.49911 5.7754Z" fill="currentColor"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 13 14" fill="none" id="1MVIf">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M8.53023 0.996582H1.53023C0.793853 0.996582 0.196899 1.59354 0.196899 2.32992V11.6605C0.196899 12.3969 0.793854 12.9939 1.53023 12.9939H6.3127C6.19295 12.6316 6.12813 12.2444 6.12813 11.842C6.12813 9.817 7.76976 8.17537 9.7948 8.17537C9.81777 8.17537 9.84069 8.17559 9.86357 8.17601V2.32992C9.86357 1.59354 9.26661 0.996582 8.53023 0.996582ZM7.65991 4.16353C7.65991 4.43968 7.43605 4.66353 7.15991 4.66353H2.83647C2.56032 4.66353 2.33647 4.43968 2.33647 4.16353C2.33647 3.88739 2.56032 3.66353 2.83647 3.66353H7.15991C7.43605 3.66353 7.65991 3.88739 7.65991 4.16353ZM5.6935 7.49783C5.96965 7.49783 6.1935 7.27397 6.1935 6.99783C6.1935 6.72169 5.96965 6.49783 5.6935 6.49783H2.83647C2.56033 6.49783 2.33647 6.72169 2.33647 6.99783C2.33647 7.27397 2.56033 7.49783 2.83647 7.49783H5.6935ZM4.6698 9.82458C4.6698 10.1007 4.44594 10.3246 4.1698 10.3246H2.83647C2.56032 10.3246 2.33647 10.1007 2.33647 9.82458C2.33647 9.54844 2.56032 9.32458 2.83647 9.32458H4.1698C4.44594 9.32458 4.6698 9.54844 4.6698 9.82458Z" fill="currentcolor"></path>
    <path d="M9.83471 9.32601C9.88959 9.32601 9.9348 9.35449 9.97365 9.39101C10.0128 9.42691 10.0483 9.47365 10.0844 9.52751C10.1558 9.63584 10.2261 9.77482 10.2949 9.91751C10.3636 10.0604 10.4305 10.2066 10.4907 10.3238C10.5514 10.4411 10.6135 10.5308 10.6352 10.5474C10.6566 10.5635 10.7579 10.598 10.8836 10.6217C11.009 10.6457 11.1625 10.6672 11.3134 10.6913C11.4645 10.7156 11.6127 10.7424 11.7329 10.7798C11.7927 10.7986 11.8456 10.8196 11.8903 10.8475C11.9351 10.8753 11.9758 10.9114 11.9932 10.9676C12.0105 11.0236 11.9979 11.0785 11.9773 11.1288C11.9565 11.1793 11.9254 11.2291 11.8872 11.2811C11.811 11.3854 11.7062 11.4987 11.5978 11.6111C11.4891 11.7236 11.3769 11.835 11.2887 11.9314C11.2009 12.0274 11.1381 12.1184 11.1302 12.1434C11.1224 12.1685 11.1221 12.2799 11.1391 12.4119C11.1562 12.5438 11.1846 12.7029 11.2092 12.8608C11.2337 13.0185 11.2551 13.1745 11.258 13.306C11.2594 13.3718 11.2565 13.4314 11.245 13.4848C11.2333 13.5383 11.2123 13.5903 11.1676 13.6243C11.1228 13.6584 11.0708 13.6626 11.0188 13.6575C10.9672 13.6528 10.9128 13.6373 10.8536 13.6157C10.7353 13.5722 10.6002 13.5027 10.4644 13.4295C10.3287 13.3561 10.1925 13.2794 10.0778 13.2215C9.96282 13.1636 9.86172 13.1286 9.83427 13.1286C9.80697 13.1286 9.70558 13.1636 9.59075 13.2215C9.47607 13.2794 9.33986 13.3563 9.20409 13.4295C9.06832 13.5029 8.93342 13.5722 8.81498 13.6158C8.75591 13.6375 8.70131 13.6527 8.64989 13.6576C8.59789 13.6626 8.5459 13.6584 8.50112 13.6243C8.4562 13.5903 8.4354 13.5383 8.42385 13.4846C8.41229 13.431 8.4094 13.3715 8.41085 13.3057C8.41374 13.1741 8.43497 13.0181 8.45952 12.8603C8.48422 12.7024 8.51253 12.5435 8.52957 12.4115C8.54662 12.2796 8.54633 12.1682 8.53867 12.143C8.53087 12.1182 8.46819 12.0272 8.38037 11.9309C8.29226 11.8347 8.17989 11.7233 8.07128 11.6109C7.96252 11.4984 7.85794 11.3851 7.78197 11.2806C7.74398 11.2285 7.71264 11.1787 7.69199 11.1282C7.67133 11.0779 7.65877 11.023 7.67639 10.9669C7.69372 10.9108 7.73416 10.8747 7.77894 10.8468C7.82357 10.819 7.87658 10.7979 7.93666 10.7791C8.05669 10.7415 8.20488 10.7147 8.35596 10.6904C8.50704 10.6662 8.66043 10.6447 8.7858 10.6211C8.91132 10.5972 9.01286 10.5627 9.03452 10.5465C9.0559 10.5301 9.1183 10.4403 9.17867 10.323C9.2389 10.2058 9.30607 10.0596 9.37467 9.91673C9.44343 9.77404 9.51377 9.63491 9.58497 9.52704C9.62079 9.47303 9.65661 9.42629 9.69561 9.39039C9.73475 9.35449 9.77982 9.32601 9.83471 9.32601Z" fill="currentcolor"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 6" id="2sv-J">
<path fill-rule="evenodd" clip-rule="evenodd" d="M6.53033 5.78033C6.23744 6.07322 5.76256 6.07322 5.46967 5.78033L0.96967 1.28033C0.676777 0.987437 0.676777 0.512563 0.96967 0.21967C1.26256 -0.0732232 1.73744 -0.0732232 2.03033 0.21967L6 4.18934L9.96967 0.21967C10.2626 -0.0732232 10.7374 -0.0732232 11.0303 0.21967C11.3232 0.512563 11.3232 0.987437 11.0303 1.28033L6.53033 5.78033Z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 8 8" id="1RGLf">
<path d="M4.25399 3.76885C4.10429 3.61915 4.10429 3.37644 4.25399 3.22674C4.40369 3.07703 4.6464 3.07703 4.7961 3.22673L4.80816 3.23879C5.6151 4.04572 5.6151 5.35402 4.80817 6.16095L3.88521 7.08391C3.07161 7.89751 1.75252 7.89751 0.938929 7.08391C0.125338 6.27032 0.125337 4.95123 0.938929 4.13764L1.47372 3.60285C1.62342 3.45315 1.86613 3.45315 2.01583 3.60285C2.16553 3.75255 2.16553 3.99526 2.01583 4.14496L1.48104 4.67975C0.966854 5.19394 0.966854 6.02761 1.48104 6.5418C1.99523 7.05599 2.8289 7.05599 3.34309 6.5418L4.26605 5.61884C4.77358 5.11131 4.77358 4.28844 4.26605 3.78091L4.25399 3.76885Z"></path>
<path d="M6.51914 1.45822C6.00495 0.944027 5.17128 0.944027 4.65709 1.45822L3.73413 2.38118C3.2266 2.88871 3.2266 3.71158 3.73413 4.21911L3.74619 4.23117C3.89589 4.38087 3.89589 4.62358 3.74619 4.77328C3.59649 4.92298 3.35378 4.92298 3.20408 4.77328L3.19202 4.76122C2.38509 3.95429 2.38509 2.64599 3.19202 1.83906L4.11498 0.916102C4.92857 0.10251 6.24766 0.10251 7.06126 0.916103C7.87485 1.72969 7.87485 3.04879 7.06126 3.86238L6.52647 4.39717C6.37677 4.54687 6.13405 4.54687 5.98435 4.39717C5.83465 4.24747 5.83465 4.00475 5.98435 3.85505L6.51914 3.32027C7.03333 2.80608 7.03333 1.97241 6.51914 1.45822Z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" id="2k0JH">
<path fill-rule="evenodd" clip-rule="evenodd" d="M7.66508 4.5H16.4151C17.3356 4.5 18.0817 5.24619 18.0817 6.16667V14.8846C18.0817 14.8946 18.0817 14.9045 18.0815 14.9144H14.7484C14.4032 14.9144 14.1234 15.1943 14.1234 15.5394V19.4966L14.1107 19.4966H7.66508C6.74461 19.4966 5.99841 18.7504 5.99841 17.8299V6.16667C5.99841 5.24619 6.74461 4.5 7.66508 4.5ZM14.7022 9.08369C15.0474 9.08369 15.3272 8.80387 15.3272 8.45869C15.3272 8.11351 15.0474 7.83369 14.7022 7.83369H9.29787C8.95269 7.83369 8.67287 8.11351 8.67287 8.45869C8.67287 8.80387 8.95269 9.08369 9.29787 9.08369H14.7022ZM15.3272 12.0016C15.3272 12.3467 15.0474 12.6266 14.7022 12.6266H9.29787C8.9527 12.6266 8.67287 12.3467 8.67287 12.0016C8.67287 11.6564 8.9527 11.3766 9.29787 11.3766H14.7022C15.0474 11.3766 15.3272 11.6564 15.3272 12.0016ZM10.9645 16.16C11.3097 16.16 11.5895 15.8802 11.5895 15.535C11.5895 15.1898 11.3097 14.91 10.9645 14.91H9.29787C8.95269 14.91 8.67287 15.1898 8.67287 15.535C8.67287 15.8802 8.95269 16.16 9.29787 16.16H10.9645Z"></path>
<path d="M15.3734 16.1644H17.5299L15.4233 18.8569C15.4071 18.8777 15.3905 18.8979 15.3734 18.9178V16.1644Z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" id="1MJwX">
    <path d="M14.7021 9.42564C15.0473 9.42564 15.3271 9.14582 15.3271 8.80064C15.3271 8.45546 15.0473 8.17564 14.7021 8.17564H9.29782C8.95265 8.17564 8.67282 8.45546 8.67282 8.80064C8.67282 9.14582 8.95265 9.42564 9.29782 9.42564H14.7021Z"></path>
    <path d="M15.3271 11.9251C15.3271 12.2703 15.0473 12.5501 14.7021 12.5501H9.29782C8.95265 12.5501 8.67282 12.2703 8.67282 11.9251C8.67282 11.5799 8.95265 11.3001 9.29782 11.3001H14.7021C15.0473 11.3001 15.3271 11.5799 15.3271 11.9251Z"></path>
    <path d="M10.9645 15.6746C11.3097 15.6746 11.5895 15.3948 11.5895 15.0496C11.5895 14.7044 11.3097 14.4246 10.9645 14.4246H9.29782C8.95264 14.4246 8.67282 14.7044 8.67282 15.0496C8.67282 15.3948 8.95264 15.6746 9.29782 15.6746H10.9645Z"></path>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M5.75 6.79167C5.75 5.52601 6.77601 4.5 8.04167 4.5H15.9583C17.224 4.5 18.25 5.52601 18.25 6.79167V14.5917C18.25 15.1094 18.0747 15.6118 17.7527 16.0172L15.6738 18.6339C15.239 19.1811 14.5784 19.5 13.8795 19.5H8.04166C6.77601 19.5 5.75 18.474 5.75 17.2083V6.79167ZM8.04167 5.75C7.46637 5.75 7 6.21637 7 6.79167V17.2083C7 17.7836 7.46637 18.25 8.04166 18.25H13.3289V15.0546C13.3289 14.7094 13.6088 14.4296 13.9539 14.4296H17V6.79167C17 6.21637 16.5336 5.75 15.9583 5.75H8.04167ZM14.6951 17.8563C14.6595 17.9011 14.6206 17.9424 14.5789 17.9802V15.6796H16.4244L14.6951 17.8563Z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" id="2cT3u">
<path fill-rule="evenodd" clip-rule="evenodd" d="M21 12C21 7.02936 16.9707 3 12 3C7.0293 3 3 7.02936 3 12C3 16.9706 7.0293 21 12 21C16.9707 21 21 16.9706 21 12ZM10.4272 13.5427V16.6306C10.4272 16.8637 10.5895 17.0341 10.8142 17.1118C11.0386 17.189 11.2886 17.119 11.4371 16.9368L15.3491 12.2169C15.4568 12.0847 15.4967 11.9112 15.4568 11.7465C15.4174 11.5818 15.3027 11.4439 15.1456 11.3734L13.4583 10.4547V7.36676C13.4583 7.13312 13.2825 6.96533 13.0587 6.88812C12.8345 6.81097 12.5844 6.88092 12.4353 7.06311L8.5238 11.783C8.41565 11.9153 8.37573 12.0887 8.41565 12.2534C8.45508 12.4182 8.5697 12.5555 8.72681 12.626L10.4272 13.5427Z" fill="currentColor"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" id="QR_h5">
    <g stroke="none" fill-rule="evenodd" transform="translate(3.5, 7.0)">
        <path d="M2.73076923,6.46153846 C1.77489178,6.46153846 1,5.68664668 1,4.73076923 C1,3.77489178 1.77489178,3 2.73076923,3 C3.68664668,3 4.46153846,3.77489178 4.46153846,4.73076923 C4.46153846,5.68664668 3.68664668,6.46153846 2.73076923,6.46153846 Z M8.5,6.46153846 C7.54412255,6.46153846 6.76923077,5.68664668 6.76923077,4.73076923 C6.76923077,3.77489178 7.54412255,3 8.5,3 C9.45587745,3 10.2307692,3.77489178 10.2307692,4.73076923 C10.2307692,5.68664668 9.45587745,6.46153846 8.5,6.46153846 Z M14.2692308,6.46153846 C13.3133533,6.46153846 12.5384615,5.68664668 12.5384615,4.73076923 C12.5384615,3.77489178 13.3133533,3 14.2692308,3 C15.2251082,3 16,3.77489178 16,4.73076923 C16,5.68664668 15.2251082,6.46153846 14.2692308,6.46153846 Z"></path>
    </g>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" id="2iOG5">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M13.2967 4.01096C12.9135 3.12216 12.0294 2.5 11 2.5H8C6.97061 2.5 6.08652 3.12216 5.70327 4.01096H5C3.89543 4.01096 3 4.90638 3 6.01095V17.0155C3 18.1201 3.89543 19.0155 5 19.0155H9.86477C10.2586 20.166 11.3494 20.9931 12.6334 20.9931H15.6244C16.4831 20.9931 17.2984 20.6159 17.8543 19.9614L20.3022 17.0791C20.7514 16.5503 20.998 15.8791 20.998 15.1852L20.998 9.92844C20.998 8.31268 19.6881 7.00284 18.0724 7.00284H15.4961V6.01096C15.4961 4.90639 14.6007 4.01096 13.4961 4.01096H13.2967ZM7.85146 4.01096C7.36962 4.08271 7 4.49819 7 5C7 5.55228 7.44772 6 8 6H11C11.5523 6 12 5.55228 12 5C12 4.51675 11.6572 4.11356 11.2015 4.02032C11.184 4.01673 11.1664 4.01361 11.1485 4.01096C11.1001 4.00374 11.0505 4 11 4H8C7.94953 4 7.89993 4.00374 7.85146 4.01096ZM12.6334 8.50284C11.846 8.50284 11.2078 9.1411 11.2078 9.92844V18.0675C11.2078 18.8548 11.846 19.4931 12.6334 19.4931H14.84V15.2C14.84 14.7858 15.1758 14.45 15.59 14.45H19.498L19.498 9.92844C19.498 9.1411 18.8597 8.50284 18.0724 8.50284H12.6334ZM16.711 18.9904C16.6047 19.1155 16.4789 19.2199 16.34 19.3005V15.95H19.2755C19.2405 16.0051 19.2016 16.0579 19.159 16.1081L16.711 18.9904Z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" id="h2Ihv">
    <path d="M9.88135 19.7164L11.1726 20.0702L10.4294 18.9566C9.87736 18.1295 9.41416 16.9017 9.12785 15.406L9.05013 15H8.63677H5.36502H4.53517L4.92298 15.7337C5.94345 17.6642 7.73082 19.1272 9.88135 19.7164ZM10.167 15H9.54951L9.67788 15.604C9.84361 16.3837 10.0606 17.0852 10.3159 17.6808C10.6173 18.384 10.9504 18.8892 11.2695 19.2041C11.5723 19.5029 11.8185 19.5914 12.0003 19.591C12.1822 19.5914 12.4282 19.5028 12.7309 19.2041C13.0499 18.8892 13.3831 18.384 13.6845 17.6808C13.9398 17.0852 14.1568 16.3837 14.3225 15.604L14.4509 15H13.8334H10.167ZM9.43021 14.0553L9.47971 14.5H9.92714H14.0732H14.5207L14.5702 14.0553C14.6426 13.4047 14.682 12.7161 14.682 12C14.682 11.4698 14.6604 10.9548 14.6198 10.4592L14.5822 10H14.1215H9.87889H9.41817L9.38056 10.4592C9.33996 10.9548 9.31836 11.4698 9.31836 12C9.31836 12.7161 9.35777 13.4047 9.43021 14.0553ZM8.87287 10.5376L8.91344 10H8.37429H4.65003H4.24133L4.16003 10.4005C4.05503 10.9178 4 11.4527 4 12C4 12.738 4.10005 13.4534 4.28763 14.133L4.38891 14.5H4.76961H8.41866H8.97236L8.91607 13.9492C8.85224 13.3246 8.81836 12.6719 8.81836 12C8.81836 11.5008 8.83707 11.0121 8.87287 10.5376ZM9.57755 8.91337L9.47435 9.5H10.07H13.9304H14.526L14.4228 8.91337C14.2488 7.92397 13.9947 7.04291 13.6845 6.31921C13.3831 5.61599 13.0499 5.11079 12.7309 4.79592C12.4283 4.49727 12.1822 4.40862 12.0003 4.40904C11.8185 4.40861 11.5722 4.49713 11.2695 4.79592C10.9504 5.11079 10.6173 5.61599 10.3159 6.31921C10.0057 7.04291 9.75161 7.92397 9.57755 8.91337ZM15.6261 10H15.0869L15.1275 10.5376C15.1633 11.0121 15.182 11.5008 15.182 12C15.182 12.6719 15.1481 13.3246 15.0843 13.9492L15.028 14.5H15.5817H19.2305H19.6112L19.7124 14.133C19.9 13.4534 20.0001 12.738 20.0001 12C20.0001 11.4527 19.945 10.9178 19.84 10.4005L19.7587 10H19.35H15.6261ZM18.876 9.5H19.6401L19.3342 8.79981C18.3759 6.60663 16.4631 4.92618 14.1192 4.28382L12.828 3.92996L13.5711 5.04359C14.1829 5.96024 14.6841 7.36677 14.9576 9.07887L15.0248 9.5H15.4513H18.876ZM8.54908 9.5H8.97555L9.04282 9.07887C9.3163 7.36671 9.81754 5.96014 10.4293 5.04349L11.1725 3.92993L9.88129 4.28371C7.53715 4.92598 5.62421 6.60651 4.6659 8.79981L4.35997 9.5H5.12408H8.54908ZM13.5711 18.9565L12.8279 20.0701L14.1191 19.7163C16.2695 19.127 18.0567 17.6641 19.0771 15.7337L19.4649 15H18.635H15.3636H14.9502L14.8725 15.406C14.5862 16.9016 14.1231 18.1294 13.5711 18.9565ZM20.5001 12C20.5001 16.6945 16.6945 20.5001 12 20.5001C7.30559 20.5001 3.5 16.6945 3.5 12C3.5 7.3056 7.3056 3.5 12 3.5C16.6945 3.5 20.5001 7.30559 20.5001 12Z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 23 22" id="6c18L">
<path d="M10.9907 0.514761C10.8856 -0.0242175 10.1144 -0.0242177 10.0093 0.514761L9.75964 1.79392C9.72217 1.98591 9.57618 2.13843 9.386 2.18424L8.01765 2.51391C7.50713 2.6369 7.50713 3.36309 8.01765 3.48609L9.386 3.81575C9.57618 3.86157 9.72217 4.01408 9.75964 4.20608L10.0093 5.48523C10.1144 6.02421 10.8856 6.02421 10.9907 5.48523L11.2404 4.20608C11.2778 4.01408 11.4238 3.86157 11.614 3.81575L12.9823 3.48609C13.4929 3.36309 13.4929 2.6369 12.9823 2.51391L11.614 2.18424C11.4238 2.13843 11.2778 1.98591 11.2404 1.79392L10.9907 0.514761Z"></path>
<path d="M19.0093 3.51476C19.1144 2.97578 19.8856 2.97578 19.9907 3.51476L20.2404 4.79392C20.2778 4.98591 20.4238 5.13843 20.614 5.18424L21.9823 5.51391C22.4929 5.6369 22.4929 6.36309 21.9823 6.48609L20.614 6.81575C20.4238 6.86157 20.2778 7.01408 20.2404 7.20608L19.9907 8.48523C19.8856 9.02421 19.1144 9.02421 19.0093 8.48523L18.7596 7.20608C18.7222 7.01408 18.5762 6.86157 18.386 6.81575L17.0177 6.48609C16.5071 6.36309 16.5071 5.6369 17.0177 5.51391L18.386 5.18424C18.5762 5.13843 18.7222 4.98591 18.7596 4.79392L19.0093 3.51476Z"></path>
<path fill-rule="evenodd" clip-rule="evenodd" d="M13.3658 6.70742C13.1705 6.51216 12.8539 6.51216 12.6587 6.70742L0.637871 18.7282C0.442609 18.9235 0.442609 19.2401 0.637871 19.4353L2.40564 21.2031C2.6009 21.3984 2.91748 21.3984 3.11274 21.2031L15.1336 9.18229C15.3288 8.98703 15.3288 8.67045 15.1336 8.47519L13.3658 6.70742ZM13.1768 7.61427C13.0791 7.51664 12.9209 7.51664 12.8232 7.61427L10.3484 10.0891C10.2507 10.1868 10.2507 10.3451 10.3484 10.4427L11.409 11.5034C11.5066 11.601 11.6649 11.601 11.7626 11.5034L14.2374 9.02849C14.3351 8.93086 14.3351 8.77257 14.2374 8.67494L13.1768 7.61427Z"></path>
<path d="M16.5114 12.2536C16.6253 11.7282 17.3747 11.7282 17.4886 12.2536L17.6446 12.9727C17.6861 13.1643 17.8357 13.3139 18.0273 13.3554L18.7464 13.5114C19.2718 13.6253 19.2718 14.3747 18.7464 14.4886L18.0273 14.6446C17.8357 14.6861 17.6861 14.8357 17.6446 15.0273L17.4886 15.7464C17.3747 16.2718 16.6253 16.2718 16.5114 15.7464L16.3554 15.0273C16.3139 14.8357 16.1643 14.6861 15.9727 14.6446L15.2536 14.4886C14.7282 14.3747 14.7282 13.6253 15.2536 13.5114L15.9727 13.3554C16.1643 13.3139 16.3139 13.1643 16.3554 12.9727L16.5114 12.2536Z"></path>
<path d="M4.48865 6.25361C4.37472 5.72821 3.62528 5.72821 3.51135 6.25361L3.35543 6.97273C3.3139 7.16428 3.16428 7.31389 2.97274 7.35543L2.25361 7.51135C1.72821 7.62527 1.72821 8.37472 2.25361 8.48864L2.97274 8.64457C3.16428 8.6861 3.3139 8.83572 3.35543 9.02726L3.51135 9.74639C3.62528 10.2718 4.37472 10.2718 4.48865 9.74639L4.64457 9.02726C4.6861 8.83572 4.83572 8.6861 5.02726 8.64457L5.74639 8.48864C6.27179 8.37472 6.27179 7.62527 5.74639 7.51135L5.02726 7.35543C4.83572 7.31389 4.6861 7.16428 4.64457 6.97273L4.48865 6.25361Z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" id="2Hk1a">
    <path d="M18.7504 4.01048C19.4392 4.01048 19.9976 4.56887 19.9976 5.25769L19.9976 8.75165C19.9976 9.16494 19.6626 9.49998 19.2493 9.49998C18.836 9.49998 18.5009 9.16494 18.5009 8.75165L18.5009 6.56473L11.7201 13.3421C11.4765 13.5857 11.0422 13.5462 10.75 13.2539C10.4577 12.9617 10.4182 12.5274 10.6618 12.2838L17.4427 5.50644L15.26 5.49336C14.8467 5.49336 14.5117 5.15832 14.5117 4.74503C14.5117 4.33175 14.8467 3.9967 15.26 3.9967L18.7504 4.01048Z"></path>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M5.75659 7.99128H11.414L12.9218 6.49128H5.75659C4.23781 6.49128 3.00659 7.7225 3.00659 9.24128V18.245C3.00659 19.7638 4.23781 20.995 5.75659 20.995H14.7615C16.2802 20.995 17.5115 19.7638 17.5115 18.245V11.0899L16.0115 12.5781V18.245C16.0115 18.9354 15.4518 19.495 14.7615 19.495H5.75659C5.06624 19.495 4.50659 18.9354 4.50659 18.245V9.24128C4.50659 8.55093 5.06624 7.99128 5.75659 7.99128Z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" id="fE8-e">
<path d="M19.6255 7.34204C20.1996 7.34204 20.6649 7.80737 20.6649 8.38138L20.6649 11.293C20.6649 11.6374 20.3857 11.9166 20.0413 11.9166C19.6969 11.9166 19.4177 11.6374 19.4177 11.293L19.4177 9.47059L13.7669 15.1184C13.564 15.3214 13.202 15.2885 12.9585 15.0449C12.715 14.8014 12.6821 14.4394 12.885 14.2365L18.5358 8.58868L16.7169 8.57778C16.3725 8.57778 16.0933 8.29858 16.0933 7.95418C16.0933 7.60977 16.3725 7.33057 16.7169 7.33057L19.6255 7.34204Z"></path>
<path fill-rule="evenodd" clip-rule="evenodd" d="M8.79737 10.6594H13.5119L14.7684 9.40938H8.79737C7.53172 9.40938 6.50571 10.4354 6.50571 11.701V19.2042C6.50571 20.4698 7.53172 21.4958 8.79737 21.4958H16.3014C17.5671 21.4958 18.5931 20.4698 18.5931 19.2042V13.2415L17.3431 14.4818V19.2042C17.3431 19.7795 16.8767 20.2458 16.3014 20.2458H8.79737C8.22208 20.2458 7.75571 19.7795 7.75571 19.2042V11.701C7.75571 11.1258 8.22208 10.6594 8.79737 10.6594Z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" id="2b7T6">
<path fill-rule="evenodd" clip-rule="evenodd" d="M17.75 10.251H19.8333C20.7538 10.251 21.5 10.9972 21.5 11.9176V19.8343C21.5 20.7548 20.7538 21.501 19.8333 21.501H11.9167C10.9962 21.501 10.25 20.7548 10.25 19.8343V17.751H8.16667C7.24619 17.751 6.5 17.0048 6.5 16.0843V8.16764C6.5 7.24717 7.24619 6.50098 8.16667 6.50098H16.0833C17.0038 6.50098 17.75 7.24717 17.75 8.16764V10.251ZM16.0833 7.75098H8.16667C7.93655 7.75098 7.75 7.93752 7.75 8.16764V16.0843C7.75 16.3144 7.93655 16.501 8.16667 16.501H10.25V11.9176C10.25 10.9972 10.9962 10.251 11.9167 10.251H16.5V8.16764C16.5 7.93752 16.3135 7.75098 16.0833 7.75098ZM11.9167 11.501H19.8333C20.0635 11.501 20.25 11.6875 20.25 11.9176V19.8343C20.25 20.0644 20.0635 20.251 19.8333 20.251H11.9167C11.6865 20.251 11.5 20.0644 11.5 19.8343V11.9176C11.5 11.6875 11.6865 11.501 11.9167 11.501Z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" id="1Jn5H"><path fill-rule="evenodd" clip-rule="evenodd" d="M4.5 21V3h12.25a2.75 2.75 0 0 1 2.75 2.75v7.822a5.61 5.61 0 0 0-1.5.12V5.75c0-.69-.56-1.25-1.25-1.25H8.997v15h4.9c.022.522.117 1.025.275 1.5H4.5ZM6 4.5v15h1.497v-15H6Z" fill="currentColor"></path><path d="M15.305 10.137a.9.9 0 1 0 0-1.8h-3.6a.9.9 0 0 0 0 1.8h3.6ZM18.98 15.43a.288.288 0 0 1 .54 0l.825 2.073a.294.294 0 0 0 .247.188l2.138.18c.26.022.365.361.167.539l-1.629 1.461a.316.316 0 0 0-.094.305l.498 2.185c.06.265-.216.475-.439.333l-1.83-1.17a.282.282 0 0 0-.306 0l-1.83 1.17c-.223.142-.499-.068-.438-.333l.497-2.185a.316.316 0 0 0-.094-.305l-1.629-1.461c-.198-.178-.093-.517.167-.54l2.138-.179a.295.295 0 0 0 .248-.188l.823-2.074Z" fill="currentColor"></path></symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" id="3_1CY">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M4.5 3V21H16.75C18.2688 21 19.5 19.7688 19.5 18.25V5.75C19.5 4.23122 18.2688 3 16.75 3H4.5ZM8.99731 4.5V19.5H16.75C17.4404 19.5 18 18.9404 18 18.25V5.75C18 5.05964 17.4404 4.5 16.75 4.5H8.99731ZM6 19.5V4.5H7.49731V19.5H6Z"></path>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M16.2054 9.23665C16.2054 9.7337 15.8025 10.1366 15.3054 10.1366H11.7054C11.2084 10.1366 10.8054 9.7337 10.8054 9.23665C10.8054 8.73959 11.2084 8.33665 11.7054 8.33665H15.3054C15.8025 8.33665 16.2054 8.73959 16.2054 9.23665Z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" id="30RFg">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M12.9931 21.7072C12.384 22.0499 11.6403 22.0497 11.0313 21.7069L4.03466 17.7676C3.40529 17.4133 3.01587 16.7471 3.01587 16.0249V8.06914C3.01587 7.34678 3.4054 6.68055 4.0349 6.32624L11.0278 2.3904C11.6381 2.04688 12.3837 2.04765 12.9933 2.39243L20.0004 6.35533C20.6279 6.71021 21.0159 7.37531 21.0159 8.0962V16.0244C21.0159 16.7469 20.6262 17.4132 19.9965 17.7675L12.9931 21.7072ZM11.7635 3.69758L5.2564 7.36001L12.0056 11.1527L18.7327 7.36164L12.2549 3.69808C12.1025 3.61189 11.9161 3.6117 11.7635 3.69758ZM4.51587 16.0249V8.66448L11.2611 12.4549V20.1148L4.77057 16.4605C4.61323 16.372 4.51587 16.2054 4.51587 16.0249ZM12.7611 20.1167L19.261 16.4602C19.4184 16.3716 19.5159 16.205 19.5159 16.0244V8.6421L12.7611 12.4487V20.1167Z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" id="3midh">
<path style="fill: var(--c-background)" d="M12 2C17.523 2 22 6.477 22 12C22 17.523 17.523 22 12 22C6.477 22 2 17.523 2 12C2 6.477 6.477 2 12 2Z"></path>
<path style="fill: var(--c-foreground)" d="M17.722 12.947L13.889 6H10.111L13.944 12.947H17.722ZM10.722 13.526L8.833 17H16.111L18 13.526H10.722ZM9.611 6.868L6 13.526L7.889 17L11.556 10.342L9.611 6.868Z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" id="2GuEX">
<path fill="#A6A6A6" style="fill: var(--c-background)" d="M12 2C17.523 2 22 6.477 22 12C22 12.269 21.9894 12.5355 21.9685 12.7991C21.004 12.2678 19.8955 11.9655 18.7165 11.9655C18.2188 11.9655 17.7336 12.0194 17.2666 12.1216L13.889 6H10.111L13.944 12.947H15.2093C14.925 13.1202 14.6545 13.3139 14.3998 13.526H10.722L8.833 17H12.1864C12.0429 17.5478 11.9665 18.1227 11.9665 18.7155C11.9665 19.8949 12.269 21.0037 12.8006 21.9684C12.5365 21.9893 12.2695 22 12 22C6.477 22 2 17.523 2 12C2 6.477 6.477 2 12 2ZM9.611 6.868L6 13.526L7.889 17L11.556 10.342L9.611 6.868Z"></path>
<path fill="#666666" style="fill: var(--c-badge)" d="M22.9848 20.5477L21.6353 19.9452L21.6348 19.9463C21.3252 20.6391 20.7891 21.206 20.1146 21.5539C19.4398 21.9019 18.6666 22.0099 17.9222 21.8602C17.1778 21.7104 16.5065 21.3118 16.0188 20.7298C15.5311 20.1479 15.2559 19.4172 15.2386 18.6581C15.2212 17.899 15.4627 17.1566 15.9234 16.553C16.384 15.9494 17.0364 15.5205 17.7732 15.3369C18.51 15.1533 19.2873 15.2259 19.9773 15.5428C20.3647 15.7207 20.7116 15.9696 21.0017 16.2737L20.3187 16.1533C20.0022 16.0975 19.7003 16.3088 19.6445 16.6253C19.5887 16.9419 19.8001 17.2437 20.1166 17.2995L22.4091 17.7037C22.7256 17.7596 23.0274 17.5482 23.0832 17.2317L23.4874 14.9392C23.5433 14.6227 23.3319 14.3209 23.0154 14.265C22.6989 14.2092 22.397 14.4206 22.3412 14.7371L22.2211 15.4184C22.1511 15.3378 22.0784 15.2596 22.0033 15.184C21.5991 14.777 21.1227 14.4425 20.5941 14.1997C19.5991 13.7428 18.4782 13.6381 17.4158 13.9029C16.3534 14.1676 15.4128 14.786 14.7485 15.6564C14.0843 16.5268 13.736 17.5973 13.761 18.6919C13.786 19.7865 14.1828 20.84 14.8861 21.6791C15.5894 22.5183 16.5573 23.0931 17.6307 23.3091C18.704 23.525 19.8189 23.3693 20.792 22.8674C21.3091 22.6008 21.7697 22.2448 22.1548 21.8198C22.4942 21.4454 22.775 21.0173 22.9841 20.5493L22.9848 20.5477Z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" id="27cOH">
<path fill="#A6A6A6" style="fill: var(--c-background)" d="M12 2C17.523 2 22 6.477 22 12C22 12.0586 21.9995 12.117 21.9985 12.1754C21.6321 11.7612 21.0965 11.5 20.5 11.5H19.5C18.6478 11.5 17.92 12.033 17.632 12.7839L13.889 6H10.111L13.944 12.947H17.5774C17.527 13.1226 17.5 13.3082 17.5 13.5V13.526H10.722L8.833 17H16.111L17.5 14.4455V20.3531C15.9221 21.3941 14.0318 22 12 22C6.477 22 2 17.523 2 12C2 6.477 6.477 2 12 2ZM9.611 6.868L6 13.526L7.889 17L11.556 10.342L9.611 6.868Z"></path>
<path fill="#666666" style="fill: var(--c-badge)" d="M20 13C19.4477 13 19 13.4477 19 14V19C19 19.5523 19.4477 20 20 20C20.5523 20 21 19.5523 21 19V14C21 13.4477 20.5523 13 20 13ZM20 23C20.5523 23 21 22.5523 21 22C21 21.4477 20.5523 21 20 21C19.4477 21 19 21.4477 19 22C19 22.5523 19.4477 23 20 23Z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" id="2Un9B">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M16.9419 9.51051C17.186 9.75459 17.186 10.1503 16.9419 10.3944L12.4419 14.8944C12.1979 15.1385 11.8021 15.1385 11.5581 14.8944L7.05806 10.3944C6.81398 10.1503 6.81398 9.75459 7.05806 9.51051C7.30214 9.26643 7.69786 9.26643 7.94194 9.51051L11.375 12.9436L11.375 4.45245C11.375 4.10728 11.6548 3.82745 12 3.82745C12.3452 3.82745 12.625 4.10728 12.625 4.45245L12.625 12.9436L16.0581 9.51051C16.3021 9.26643 16.6979 9.26643 16.9419 9.51051Z"></path>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M6 18.3275C6 17.9823 6.27982 17.7025 6.625 17.7025H17.375C17.7202 17.7025 18 17.9823 18 18.3275C18 18.6726 17.7202 18.9525 17.375 18.9525H6.625C6.27982 18.9525 6 18.6726 6 18.3275Z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" id="3UgAP">
    <path d="M5.976 16.51c.22.072.429.***********.17.287.38.359.6l1.786-.447a2.899 2.899 0 0 0-2.259-2.29l-.04-.009-.446 1.787zM18.68 8.597L9.487 17.79l-4.512 1.04a.3.3 0 0 1-.36-.36l1.042-4.51 9.192-9.193c.423-.423 1.623.091 2.681 1.15 1.058 1.057 1.572 2.257 1.15 2.68z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" id="3ZS2s">
  <path d="M11.2861 20.1723C10.5811 20.1723 9.86709 19.9153 9.38109 19.5283C8.29009 18.6613 2.93209 12.9693 2.70409 12.7273C2.42009 12.4253 2.43409 11.9503 2.73609 11.6673C3.03709 11.3833 3.51309 11.3973 3.79609 11.6993C5.32609 13.3253 9.47809 17.6903 10.3141 18.3553C10.7191 18.6773 11.4231 18.7753 11.7621 18.5593C12.0111 18.4003 12.2451 17.8663 11.9581 17.2263C11.6631 16.5673 10.6311 14.6933 9.72009 13.0393C8.90609 11.5633 8.20409 10.2863 7.97009 9.78533C7.36409 8.48633 7.67709 7.13133 8.71309 6.56133C9.75109 5.98833 10.9121 6.24433 11.8121 7.24433C12.6031 8.12433 14.3481 10.6233 15.3901 12.1163L15.9721 12.9463C16.3171 13.4353 16.7221 13.5923 17.0881 13.3763C17.3011 13.2503 17.3521 12.7813 17.2111 12.2613C16.8731 11.0223 15.7191 7.57833 15.4541 6.81033C15.0581 5.66433 15.4541 4.55033 16.3941 4.16233C17.0591 3.88833 18.0571 3.82533 19.0271 5.09233C19.9261 6.26733 20.6951 7.38333 20.7271 7.43033C20.9621 7.77133 20.8761 8.23733 20.5341 8.47333C20.1931 8.70733 19.7261 8.62133 19.4911 8.27933C19.4831 8.26933 18.7111 7.14733 17.8361 6.00433C17.3621 5.38533 17.1261 5.48433 16.9681 5.54933C16.8111 5.61333 16.7331 5.91933 16.8721 6.32133C17.1401 7.09933 18.3101 10.5923 18.6581 11.8663C18.9951 13.1013 18.6851 14.1743 17.8511 14.6673C16.9741 15.1873 15.6761 15.1213 14.7491 13.8153L14.1601 12.9753C13.2041 11.6063 11.4271 9.05933 10.6981 8.24733C10.2711 7.77533 9.84509 7.64833 9.43609 7.87533C9.07309 8.07433 9.11009 8.68133 9.32909 9.15133C9.54209 9.60733 10.2661 10.9233 11.0341 12.3163C12.0041 14.0763 13.0061 15.8973 13.3271 16.6123C13.9441 17.9893 13.3971 19.2953 12.5681 19.8233C12.1861 20.0673 11.7381 20.1723 11.2861 20.1723Z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" id="33S4b">
  <path d="M8.84436 8.94709C9.48109 8.947 9.99728 9.46317 9.99736 10.0999C9.99745 10.7366 9.48096 11.2529 8.84423 11.253C8.20751 11.2531 7.69132 10.7369 7.69115 10.0999C7.69138 9.46304 8.20755 8.94685 8.84436 8.94709Z"></path>
  <path d="M15.1873 8.94704C15.824 8.94695 16.3402 9.46312 16.34 10.0999C16.3401 10.7367 15.8239 11.2529 15.1872 11.2529C14.5504 11.253 14.0342 10.7368 14.0341 10.0998C14.034 9.46308 14.5502 8.94689 15.1873 8.94704Z"></path>
  <path fill-rule="evenodd" d="M12 19.75C16.2802 19.75 19.75 16.2802 19.75 12C19.75 7.71979 16.2802 4.25 12 4.25C7.71979 4.25 4.25 7.71979 4.25 12C4.25 16.2802 7.71979 19.75 12 19.75ZM12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21Z"></path>
  <path d="M12 14.625C11.1453 14.625 10.3098 14.878 9.59862 15.3521L9.34669 15.52C9.05949 15.7115 8.67144 15.6339 8.47997 15.3467C8.2885 15.0595 8.36611 14.6714 8.65332 14.48L8.90524 14.312C9.82172 13.701 10.8985 13.375 12 13.375C13.1015 13.375 14.1783 13.701 15.0948 14.312L15.3467 14.48C15.6339 14.6714 15.7115 15.0595 15.52 15.3467C15.3286 15.6339 14.9405 15.7115 14.6533 15.52L14.4014 15.3521C13.6903 14.878 12.8547 14.625 12 14.625Z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" id="3TpOT">
    <path d="M13.2979 7.22912C11.9591 6.90185 10.4864 7.36642 9.68814 8.89757C9.55222 9.15829 9.23916 9.27134 8.96802 9.15761C8.15808 8.81788 7.56248 8.99191 7.23721 9.31808C6.90478 9.65142 6.71436 10.2838 7.01794 11.14C7.09825 11.3665 7.0266 11.6191 6.8393 11.7696C6.34067 12.1705 6.05661 12.5357 5.89144 12.8905C5.72641 13.245 5.66092 13.6301 5.66092 14.1031C5.66092 15.4072 6.68535 16.5475 8.25824 16.5533V16.5545C8.57053 16.5545 8.82368 16.8076 8.82368 17.1199C8.82368 17.4322 8.57053 17.6854 8.25824 17.6854C8.24445 17.6854 8.23078 17.6849 8.21724 17.6839C6.05562 17.6577 4.53003 16.0338 4.53003 14.1031C4.53003 13.5229 4.61075 12.9619 4.8662 12.4132C5.07756 11.9592 5.39541 11.5416 5.83814 11.1382C5.58659 10.1139 5.80368 9.15403 6.43645 8.51952C7.05402 7.90025 7.96465 7.69354 8.93191 7.95219C10.0426 6.24635 11.9066 5.72484 13.5665 6.13058C15.257 6.54382 16.7602 7.92201 17.086 9.96214C18.2178 10.5587 18.8484 11.3067 19.1825 12.0703C19.5302 12.865 19.5301 13.6233 19.53 14.085L19.53 14.1031C19.53 15.7154 18.2184 17.6016 15.8632 17.6816C15.8416 17.6841 15.8197 17.6854 15.7975 17.6854C15.4852 17.6854 15.232 17.4322 15.232 17.1199C15.232 16.8076 15.4852 16.5545 15.7975 16.5545V16.5521C17.5068 16.5066 18.3991 15.1697 18.3991 14.1031C18.3991 13.6418 18.3944 13.0903 18.1464 12.5236C17.907 11.9764 17.4133 11.3587 16.3227 10.845C16.1423 10.76 16.02 10.5863 16.0009 10.3878C15.8375 8.69427 14.6499 7.5596 13.2979 7.22912Z"></path>
    <path d="M12.5933 14.2474V10.3351C12.5933 10.0228 12.3401 9.76962 12.0279 9.76962C11.7156 9.76962 11.4624 10.0228 11.4624 10.3351V14.2474L10.1659 12.9509C9.9451 12.7301 9.58708 12.7301 9.36626 12.9509C9.2559 13.0613 9.2007 13.2063 9.20065 13.351C9.2007 13.4956 9.2559 13.6402 9.36626 13.7506L11.628 16.0124C11.8489 16.2332 12.2069 16.2332 12.4277 16.0124L14.6895 13.7506C14.9103 13.5298 14.9103 13.1718 14.6895 12.9509C14.4686 12.7301 14.1106 12.7301 13.8898 12.9509L12.5933 14.2474Z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" id="2wsgR">
	<path d="M14.9892 4.11976L14.2682 4.84076L18.5082 9.05076L19.2182 8.35076C20.3782 7.18076 20.3782 5.28976 19.2182 4.11976C18.0482 2.95976 16.1592 2.95976 14.9892 4.11976Z"></path>
	<path d="M3.56802 19.7704L3.81802 19.9404C4.08902 20.0904 4.38802 20.0904 4.50802 20.0904C5.62902 20.0904 8.30802 19.2604 9.25802 18.3004L17.348 10.2104L13.119 6.00036L5.03902 14.0804C4.08902 15.0304 3.24902 17.7104 3.24902 18.8304C3.24902 18.9904 3.24902 19.4504 3.56802 19.7704Z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 30 30" fill="none" id="2RxxT">
  <circle cx="15" cy="15" r="14" fill="white" stroke="#B2D9EC" stroke-width="2"></circle>
  <circle cx="14.9999" cy="14.9999" r="11.2499" fill="#0081C2"></circle>
  <path d="M18.9928 14.6199L19.4195 14.0031L18.9928 14.6199ZM12.5773 19.8252L13.0004 20.4444L12.5773 19.8252ZM12.5453 19.4123V10.5944H11.0453V19.4123H12.5453ZM12.1531 10.8L18.5661 15.2367L19.4195 14.0031L13.0065 9.56643L12.1531 10.8ZM18.5673 14.8247L12.1543 19.2059L13.0004 20.4444L19.4134 16.0632L18.5673 14.8247ZM18.5661 15.2367C18.4219 15.1369 18.4225 14.9236 18.5673 14.8247L19.4134 16.0632C20.1374 15.5687 20.1405 14.5019 19.4195 14.0031L18.5661 15.2367ZM12.5453 10.5944C12.5453 10.796 12.3189 10.9147 12.1531 10.8L13.0065 9.56643C12.1774 8.99286 11.0453 9.58627 11.0453 10.5944H12.5453ZM11.0453 19.4123C11.0453 20.4171 12.1707 21.0112 13.0004 20.4444L12.1543 19.2059C12.3202 19.0925 12.5453 19.2113 12.5453 19.4123H11.0453Z" fill="white"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 30 30" fill="none" id="2TZv-">
  <circle cx="15" cy="15" r="14" fill="white" stroke="#E6E6E6" stroke-width="2"></circle>
  <circle cx="14.9999" cy="14.9999" r="11.2499" fill="#E6E6E6"></circle>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white" id="32eGb">
    <path d="M17.0837 5.10566C17.3766 4.81276 17.8515 4.81276 18.1443 5.10566C21.952 8.91327 21.952 15.0866 18.1443 18.8942C17.8515 19.1871 17.3766 19.1871 17.0837 18.8942C16.7908 18.6013 16.7908 18.1265 17.0837 17.8336C20.3055 14.6118 20.3055 9.38814 17.0837 6.16632C16.7908 5.87342 16.7908 5.39855 17.0837 5.10566Z"></path>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M11.6075 6.34068C11.8493 6.47182 12 6.72487 12 7.00001V17C12 17.2752 11.8493 17.5282 11.6075 17.6593C11.3656 17.7905 11.0713 17.7786 10.8407 17.6285L6.83902 15.0227H3.75C3.33579 15.0227 3 14.6869 3 14.2727V9.72723C3 9.31301 3.33579 8.97723 3.75 8.97723H6.83903L10.8408 6.37151C11.0713 6.22137 11.3656 6.20954 11.6075 6.34068ZM10.5 8.38336L7.47094 10.3557C7.34917 10.435 7.20699 10.4772 7.06169 10.4772H4.5V13.5227H7.06169C7.207 13.5227 7.34918 13.5649 7.47095 13.6442L10.5 15.6166V8.38336Z"></path>
    <path d="M15.6623 7.58771C15.3694 7.29482 14.8945 7.29482 14.6016 7.58771C14.3087 7.88061 14.3087 8.35548 14.6016 8.64837C16.4527 10.4995 16.4527 13.5006 14.6016 15.3517C14.3087 15.6446 14.3087 16.1195 14.6016 16.4124C14.8945 16.7053 15.3694 16.7053 15.6623 16.4124C18.0992 13.9755 18.0992 10.0246 15.6623 7.58771Z"></path>
    </symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" id="i_IPC">
	<path fill-rule="evenodd" d="M14.5295 12.7543C14.0389 12.7543 13.5856 12.679 13.1509 12.5276L7.52181 18.1567C6.82321 18.8367 5.70858 18.8367 5.00997 18.1567C4.33001 17.4573 4.33001 16.3435 5.00997 15.6441L10.6383 10.0158C10.4877 9.58109 10.4117 9.12778 10.4117 8.63645C10.4117 6.35126 12.2443 4.5 14.5295 4.5C15.0961 4.5 15.6255 4.61333 16.0974 4.8027L13.7928 7.10653C13.5662 7.33318 13.5662 7.71119 13.7928 7.93785L15.2288 9.37308C15.4555 9.59973 15.8327 9.59973 16.0594 9.37308L18.364 7.06925C18.5533 7.5412 18.6667 8.06981 18.6667 8.63645C18.6667 10.9224 16.8154 12.7543 14.5295 12.7543Z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" id="3WnEk">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M12 3.66669C9.69877 3.66669 7.83329 5.53217 7.83329 7.83335V9.51146C6.8958 9.6151 6.16663 10.4099 6.16663 11.375V17.625C6.16663 18.6606 7.00609 19.5 8.04163 19.5H15.9583C16.9938 19.5 17.8333 18.6606 17.8333 17.625V11.375C17.8333 10.4099 17.1041 9.6151 16.1666 9.51146V7.83335C16.1666 5.53217 14.3011 3.66669 12 3.66669ZM14.9166 9.50002V7.83335C14.9166 6.22252 13.6108 4.91669 12 4.91669C10.3891 4.91669 9.08329 6.22252 9.08329 7.83335V9.50002H14.9166Z" fill="currentColor"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 10 10" fill="none" id="1Qzqr">
    <circle cx="5" cy="5" r="5" fill="#737373"></circle>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" id="2ng5H">
<path d="M13.6677 14.014C13.6677 13.5998 14.0035 13.264 14.4177 13.264H17.639C18.0532 13.264 18.389 13.5998 18.389 14.014C18.389 14.4282 18.0532 14.764 17.639 14.764H14.4177C14.0035 14.764 13.6677 14.4282 13.6677 14.014Z"></path>
<path d="M14.4177 9.23615C14.0035 9.23615 13.6677 9.57193 13.6677 9.98615C13.6677 10.4004 14.0035 10.7361 14.4177 10.7361H17.639C18.0532 10.7361 18.389 10.4004 18.389 9.98615C18.389 9.57193 18.0532 9.23615 17.639 9.23615H14.4177Z"></path>
<path fill-rule="evenodd" clip-rule="evenodd" d="M10.7118 11.3966C10.9652 11.0204 11.113 10.5672 11.113 10.0794C11.113 8.77543 10.0559 7.71832 8.75194 7.71832C7.44793 7.71832 6.39082 8.77543 6.39082 10.0794C6.39082 10.5775 6.54506 11.0396 6.80837 11.4205C6.0854 11.916 5.6111 12.7478 5.6111 13.6905V14.9127C5.6111 15.3269 5.94689 15.6627 6.3611 15.6627H11.1944C11.6086 15.6627 11.9444 15.3269 11.9444 14.9127V13.6905C11.9444 12.7325 11.4546 11.889 10.7118 11.3966ZM8.76167 10.9405H8.7422C8.27111 10.9353 7.89082 10.5518 7.89082 10.0794C7.89082 9.60386 8.27636 9.21832 8.75194 9.21832C9.22751 9.21832 9.61305 9.60386 9.61305 10.0794C9.61305 10.5518 9.23276 10.9353 8.76167 10.9405ZM8.73574 12.4405C8.74114 12.4405 8.74653 12.4405 8.75194 12.4405C8.75734 12.4405 8.76273 12.4405 8.76813 12.4405H9.19443C9.88479 12.4405 10.4444 13.0001 10.4444 13.6905V14.1627H7.1111V13.6905C7.1111 13.0001 7.67074 12.4405 8.3611 12.4405H8.73574Z"></path>
<path fill-rule="evenodd" clip-rule="evenodd" d="M5.13889 20.0001C3.6201 20.0001 2.38889 18.7688 2.38889 17.2501V6.75006C2.38889 5.23128 3.6201 4.00006 5.13889 4.00006H18.8611C20.3799 4.00006 21.6111 5.23128 21.6111 6.75006V17.2501C21.6111 18.7688 20.3799 20.0001 18.8611 20.0001L5.13889 20.0001ZM3.88889 17.2501C3.88889 17.9404 4.44853 18.5001 5.13889 18.5001L18.8611 18.5001C19.5515 18.5001 20.1111 17.9404 20.1111 17.2501V6.75006C20.1111 6.05971 19.5515 5.50006 18.8611 5.50006L5.13889 5.50006C4.44853 5.50006 3.88889 6.05971 3.88889 6.75006L3.88889 17.2501Z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" id="3v0JY">
<path d="M20.9533 9.25006C20.9969 9.66089 20.6578 9.99693 20.2446 9.99693C19.8315 9.99693 19.502 9.66014 19.445 9.25095C19.1082 6.83121 17.1606 4.88577 14.7406 4.54868C14.3303 4.49153 13.9927 4.16114 13.9927 3.74693C13.9927 3.33272 14.3296 2.99271 14.7415 3.03653C18.0097 3.38419 20.6065 5.98162 20.9533 9.25006Z"></path>
<path d="M17.8098 9.25024C17.8891 9.65741 17.5453 9.99693 17.1305 9.99693C16.7157 9.99693 16.3902 9.65204 16.2573 9.25908C16.0164 8.5465 15.4407 7.96953 14.7286 7.72832C14.3367 7.59557 13.9927 7.27099 13.9927 6.85722C13.9927 6.44346 14.3313 6.10051 14.7375 6.17939C16.2866 6.48024 17.5083 7.70146 17.8098 9.25024Z"></path>
<path d="M7.8447 3.88401C7.26576 3.31111 6.33544 3.30488 5.74889 3.86998L4.73263 4.84905C4.52057 5.05335 4.32527 5.27435 4.14859 5.50992L3.84271 5.91776C3.42271 6.47776 3.28596 7.20079 3.47242 7.8755L3.55955 8.19078C5.23778 14.2632 10.0448 18.9685 16.1518 20.5165C16.7632 20.6714 17.4117 20.5569 17.9331 20.202L18.2011 20.0196C18.5496 19.7823 18.8697 19.5057 19.155 19.1954L20.2117 18.0459C20.6732 17.5439 20.6684 16.7707 20.2007 16.2745L19.0202 15.0219L17.6731 13.7128C17.1054 13.1612 16.2002 13.1667 15.6393 13.7253L14.2298 15.1291C14.1625 15.1962 14.0587 15.2101 13.9761 15.1632C12.0041 14.0435 10.3401 12.4529 9.13267 10.5333L8.8501 10.0841C8.77237 9.96047 8.78909 9.79978 8.8906 9.69485L10.2198 8.32078C10.7912 7.73013 10.781 6.78969 10.1968 6.21165L7.8447 3.88401Z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" id="3OnfN">
<path fill-rule="evenodd" clip-rule="evenodd" d="M21 5.75562C21 4.23683 19.7688 3.00562 18.25 3.00562H5.75C4.23122 3.00562 3 4.23683 3 5.75561V14.8843C3 16.4031 4.23122 17.6343 5.75 17.6343H6.46599C6.62743 17.6343 6.75829 17.7652 6.75829 17.9266V20.3099C6.75829 21.2377 7.87245 21.7105 8.53972 21.066L8.56036 21.0461L11.6973 17.9891C11.9308 17.7616 12.2438 17.6343 12.5697 17.6343L18.25 17.6343C19.7688 17.6343 21 16.4031 21 14.8843V5.75562ZM16.2565 9.32214C16.6707 9.32214 17.0065 8.98636 17.0065 8.57214C17.0065 8.15793 16.6707 7.82214 16.2565 7.82214H7.74341C7.3292 7.82214 6.99341 8.15793 6.99341 8.57214C6.99341 8.98636 7.3292 9.32214 7.74341 9.32214H16.2565ZM11.2323 12.8279C11.6465 12.8279 11.9823 12.4921 11.9823 12.0779C11.9823 11.6637 11.6465 11.3279 11.2323 11.3279H7.74344C7.32923 11.3279 6.99344 11.6637 6.99344 12.0779C6.99344 12.4921 7.32923 12.8279 7.74344 12.8279H11.2323Z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" id="a7t8v">
<path d="M12.6095 11.4453C12.2502 11.086 12.2502 10.5035 12.6095 10.1442C12.9687 9.78492 13.5512 9.78491 13.9105 10.1442L13.9395 10.1731C15.8761 12.1098 15.8761 15.2497 13.9395 17.1863L11.7244 19.4014C9.77175 21.354 6.60593 21.354 4.65331 19.4014C2.70069 17.4488 2.70069 14.283 4.65331 12.3304L5.9368 11.0469C6.29608 10.6876 6.87859 10.6876 7.23787 11.0469C7.59716 11.4062 7.59716 11.9887 7.23787 12.3479L5.95438 13.6314C4.72033 14.8655 4.72033 16.8663 5.95439 18.1004C7.18844 19.3344 9.18924 19.3344 10.4233 18.1004L12.6384 15.8852C13.8565 14.6672 13.8565 12.6923 12.6384 11.4742L12.6095 11.4453Z"></path>
<path d="M18.0458 5.89976C16.8118 4.6657 14.811 4.6657 13.5769 5.89976L11.3618 8.11486C10.1437 9.33293 10.1437 11.3078 11.3618 12.5259L11.3907 12.5548C11.75 12.9141 11.75 13.4966 11.3907 13.8559C11.0315 14.2152 10.4489 14.2152 10.0897 13.8559L10.0607 13.827C8.12409 11.8903 8.12409 8.75042 10.0607 6.81378L12.2758 4.59868C14.2284 2.64606 17.3943 2.64606 19.3469 4.59868C21.2995 6.5513 21.2995 9.71713 19.3469 11.6697L18.0634 12.9532C17.7041 13.3125 17.1216 13.3125 16.7623 12.9532C16.403 12.594 16.403 12.0114 16.7623 11.6522L18.0458 10.3687C19.2799 9.13462 19.2799 7.13382 18.0458 5.89976Z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" id="1mhqZ">
<path d="M20.9092 6.72847L15.9827 11.6571L20.9198 17.2458C21.0125 17.0153 21.0635 16.7636 21.0635 16.5V7.5C21.0635 7.22653 21.0086 6.96588 20.9092 6.72847Z"></path>
<path d="M19.854 5.66233L12.903 12.6163C12.4116 13.1079 11.6137 13.1042 11.1268 12.6082L4.29859 5.65147C4.53422 5.55387 4.79256 5.5 5.06348 5.5H19.0635C19.3443 5.5 19.6116 5.55787 19.854 5.66233Z"></path>
<path d="M3.22868 6.7028L8.08736 11.6529L3.18775 17.1956C3.10738 16.9789 3.06348 16.7446 3.06348 16.5V7.5C3.06348 7.21664 3.1224 6.94704 3.22868 6.7028Z"></path>
<path d="M4.20669 18.3077C4.46639 18.431 4.75688 18.5 5.06348 18.5H19.0635C19.3523 18.5 19.6269 18.4388 19.8748 18.3286L14.9202 12.72L13.9639 13.6767C12.8829 14.7582 11.1274 14.7502 10.0563 13.6589L9.14073 12.7261L4.20669 18.3077Z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" id="1tLgd">
<path d="M18.7504 4.01048C19.4393 4.01048 19.9977 4.56887 19.9977 5.25769L19.9977 8.75165C19.9977 9.16494 19.6626 9.49998 19.2493 9.49998C18.836 9.49998 18.501 9.16494 18.501 8.75165L18.501 6.56473L11.7201 13.3421C11.4766 13.5857 11.0423 13.5462 10.75 13.2539C10.4578 12.9617 10.4183 12.5274 10.6618 12.2838L17.4427 5.50644L15.2601 5.49336C14.8468 5.49336 14.5117 5.15832 14.5117 4.74503C14.5117 4.33175 14.8468 3.9967 15.2601 3.9967L18.7504 4.01048Z"></path>
<path fill-rule="evenodd" clip-rule="evenodd" d="M5.75665 7.99128H11.4141L12.9219 6.49128H5.75665C4.23787 6.49128 3.00665 7.7225 3.00665 9.24128V18.245C3.00665 19.7638 4.23787 20.995 5.75665 20.995H14.7615C16.2803 20.995 17.5115 19.7638 17.5115 18.245V11.0899L16.0115 12.5781V18.245C16.0115 18.9354 15.4519 19.495 14.7615 19.495H5.75665C5.0663 19.495 4.50665 18.9354 4.50665 18.245V9.24128C4.50665 8.55093 5.0663 7.99128 5.75665 7.99128Z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" id="3bABf">
<path fill-rule="evenodd" clip-rule="evenodd" d="M9.38996 17.1299H6.91992V9.69861H9.38996V17.1299V17.1299Z"></path>
<path fill-rule="evenodd" clip-rule="evenodd" d="M8.15552 8.68346H8.13897C7.31044 8.68346 6.77441 8.11269 6.77441 7.39964C6.77441 6.6707 7.32699 6.11581 8.17173 6.11581C9.0168 6.11581 9.53662 6.6707 9.55316 7.39964C9.55316 8.11269 9.01647 8.68346 8.15552 8.68346V8.68346Z"></path>
<path fill-rule="evenodd" clip-rule="evenodd" d="M18.2976 17.1299H15.8276V13.1534C15.8276 12.1548 15.4702 11.4731 14.5765 11.4731C13.8942 11.4731 13.4879 11.9327 13.3089 12.3768C13.2437 12.5353 13.2278 12.757 13.2278 12.9793V17.1296H10.7578C10.7578 17.1296 10.7902 10.3948 10.7578 9.69829H13.2278V10.7502C13.5561 10.2439 14.1431 9.52292 15.4544 9.52292C17.0793 9.52292 18.298 10.5854 18.298 12.8681V17.1299H18.2976Z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" id="euDMJ">
<path fill-rule="evenodd" clip-rule="evenodd" d="M18.0001 8.6544C17.5586 8.85024 17.084 8.98254 16.5861 9.04206C17.0944 8.73738 17.4848 8.25492 17.6686 7.68C17.1928 7.96218 16.666 8.16702 16.1051 8.27742C15.6561 7.79892 15.0163 7.5 14.3081 7.5C12.9485 7.5 11.8462 8.60226 11.8462 9.96186C11.8462 10.1548 11.8679 10.3427 11.9099 10.5229C9.86376 10.4203 8.04972 9.4401 6.8355 7.9506C6.62358 8.3142 6.50214 8.73708 6.50214 9.18828C6.50214 10.0424 6.93678 10.796 7.59738 11.2375C7.19376 11.2247 6.8142 11.1139 6.48228 10.9295C6.48204 10.9398 6.48204 10.9501 6.48204 10.9605C6.48204 12.1533 7.33068 13.1483 8.45694 13.3745C8.25036 13.4308 8.03286 13.4609 7.80828 13.4609C7.64958 13.4609 7.49538 13.4454 7.34502 13.4167C7.6584 14.3948 8.56752 15.1066 9.64488 15.1264C8.8023 15.7868 7.74078 16.1803 6.58728 16.1803C6.38856 16.1803 6.1926 16.1687 6 16.1459C7.08954 16.8445 8.38368 17.2521 9.77394 17.2521C14.3023 17.2521 16.7786 13.5007 16.7786 10.2473C16.7786 10.1405 16.7762 10.0344 16.7715 9.92874C17.2526 9.5817 17.67 9.14808 18.0001 8.6544V8.6544Z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" id="27g8q">
<path fill-rule="evenodd" clip-rule="evenodd" d="M10.4888 12.3692H9V10.3418H10.4888C10.4888 10.3418 10.4888 9.51098 10.4888 8.61018C10.4888 7.24946 11.3683 6 13.3947 6C14.2152 6 14.8221 6.07866 14.8221 6.07866L14.7741 7.97183C14.7741 7.97183 14.1554 7.96581 13.4802 7.96581C12.7494 7.96581 12.6323 8.30255 12.6323 8.86158C12.6323 9.30345 12.6323 7.91975 12.6323 10.3419H14.8322L14.7365 12.3693H12.6323V18H10.4888V12.3692Z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" id="1Nsu8">
  <g clip-path="url(#1Nsu8_clip0)" id="1Nsu8_contact-default-avatar">
    <path style="fill:var(--contact-default-img-bg, #CCCCCC);" d="M0 12C0 5.37258 5.37258 0 12 0C18.6274 0 24 5.37258 24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12Z" fill="#CCC"></path>
    <path style="fill:var(--contact-default-img-head, #737373);" d="M18.8956 24.8939C19.5055 24.8939 20 24.4376 20 23.8748V20.6227C20 18.0061 16.4741 17.2777 14.706 16.3768C14.5152 16.2796 14.3991 16.0867 14.3678 15.8749C14.3323 15.6343 14.4156 15.3935 14.5681 15.2041C15.2705 14.3315 16.1556 12.5887 16.4476 10.8431C16.8621 8.36485 15.2944 5.5 12.0139 5.5H12.0028C8.72225 5.5 7.15458 8.36485 7.56907 10.8431C7.8608 12.5873 8.74482 14.3287 9.44693 15.202C9.60056 15.3931 9.68565 15.6351 9.65222 15.878C9.62329 16.0882 9.51252 16.2812 9.32724 16.3846C7.56507 17.368 4 18.2306 4 20.6227V23.8748C4 24.4376 4.49447 24.8939 5.10442 24.8939H18.8956Z" fill="#737373"></path>
  </g>
  <defs>
    <clipPath id="1Nsu8_clip0">
    <path d="M0 12C0 5.37258 5.37258 0 12 0C18.6274 0 24 5.37258 24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12Z" fill="white"></path>
    </clipPath>
  </defs>
  <svg>
    <use xlink:href="#1Nsu8_contact-default-avatar"></use>
  </svg>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" id="3KYli">
    <path d="M4.64645 7.14645C4.84171 6.95118 5.15829 6.95118 5.35355 7.14645L9 10.7929L12.6464 7.14645C12.8417 6.95118 13.1583 6.95118 13.3536 7.14645C13.5488 7.34171 13.5488 7.65829 13.3536 7.85355L9.35355 11.8536C9.15829 12.0488 8.84171 12.0488 8.64645 11.8536L4.64645 7.85355C4.45118 7.65829 4.45118 7.34171 4.64645 7.14645Z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" id="34oy1">
    <path fill-rule="evenodd" d="M20.829 19.36C20.6011 19.756 20.181 20 19.7251 20H4.27408C3.81893 20 3.3981 19.756 3.17098 19.36C2.94301 18.964 2.94301 18.476 3.17098 18.08L10.8961 4.63999C11.124 4.24399 11.5449 4 12 4C12.4551 4 12.876 4.24399 13.1031 4.63999L20.829 18.08C21.057 18.476 21.057 18.964 20.829 19.36ZM12 9.59998C12.4416 9.59998 12.8 9.95837 12.8 10.4V12C12.8 13.3256 12.4416 15.2 12 15.2C11.5584 15.2 11.2 13.3256 11.2 12V10.4C11.2 9.95837 11.5584 9.59998 12 9.59998ZM12.8 16.8C12.8 16.3584 12.4416 16 12 16C11.5584 16 11.2 16.3584 11.2 16.8C11.2 17.2416 11.5584 17.6 12 17.6C12.4416 17.6 12.8 17.2416 12.8 16.8Z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" id="3XG2L">
    <path d="M11.75,10.25 L13.5,10.25 C13.9142136,10.25 14.25,10.5857864 14.25,11 C14.25,11.4142136 13.9142136,11.75 13.5,11.75 L11.75,11.75 L11.75,13.5 C11.75,13.9142136 11.4142136,14.25 11,14.25 C10.5857864,14.25 10.25,13.9142136 10.25,13.5 L10.25,11.75 L8.5,11.75 C8.08578644,11.75 7.75,11.4142136 7.75,11 C7.75,10.5857864 8.08578644,10.25 8.5,10.25 L10.25,10.25 L10.25,8.5 C10.25,8.08578644 10.5857864,7.75 11,7.75 C11.4142136,7.75 11.75,8.08578644 11.75,8.5 L11.75,10.25 Z M16.451757,15.3910968 L19.5229708,18.4623106 C19.815864,18.7552038 19.815864,19.2300776 19.5229708,19.5229708 C19.2300776,19.815864 18.7552038,19.815864 18.4623106,19.5229708 L15.3910968,16.451757 C14.1902373,17.4202081 12.6628275,18 11,18 C7.13400675,18 4,14.8659932 4,11 C4,7.13400675 7.13400675,4 11,4 C14.8659932,4 18,7.13400675 18,11 C18,12.6628275 17.4202081,14.1902373 16.451757,15.3910968 Z M11,16.5 C14.0375661,16.5 16.5,14.0375661 16.5,11 C16.5,7.96243388 14.0375661,5.5 11,5.5 C7.96243388,5.5 5.5,7.96243388 5.5,11 C5.5,14.0375661 7.96243388,16.5 11,16.5 Z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" id="1pYEU">
<path fill-rule="evenodd" clip-rule="evenodd" d="M5.125 5.43343C4.77982 5.43343 4.5 5.71325 4.5 6.05843C4.5 6.40361 4.77982 6.68343 5.125 6.68343H18.875C19.2202 6.68343 19.5 6.40361 19.5 6.05843C19.5 5.71325 19.2202 5.43343 18.875 5.43343H5.125ZM5.125 17.3084C4.77982 17.3084 4.5 17.5883 4.5 17.9334C4.5 18.2786 4.77982 18.5584 5.125 18.5584H18.875C19.2202 18.5584 19.5 18.2786 19.5 17.9334C19.5 17.5883 19.2202 17.3084 18.875 17.3084H5.125ZM5.75 8.9751C5.05964 8.9751 4.5 9.53474 4.5 10.2251V13.7668C4.5 14.4571 5.05964 15.0168 5.75 15.0168H9.91667C10.607 15.0168 11.1667 14.4571 11.1667 13.7668V10.2251C11.1667 9.53474 10.607 8.9751 9.91667 8.9751H5.75ZM6.375 10.3333C6.02982 10.3333 5.75 10.6421 5.75 11.023V12.977C5.75 13.3579 6.02982 13.6667 6.375 13.6667H9.29167C9.63684 13.6667 9.91667 13.3579 9.91667 12.977V11.023C9.91667 10.6421 9.63684 10.3333 9.29167 10.3333H6.375Z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" id="2nRto">
<g clip-path="url(#2nRto_clip0_16_757)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M18.9 5.4H5.1C4.8 5.4 4.5 5.7 4.5 6C4.5 6.3 4.8 6.6 5.1 6.6H18.9C19.2 6.6 19.5 6.3 19.5 6C19.5 5.7 19.2 5.4 18.9 5.4ZM18.9 17.2H5.1C4.8 17.2 4.5 17.5 4.5 17.8C4.5 18.1 4.8 18.4 5.1 18.4H18.9C19.2 18.4 19.5 18.1 19.5 17.8C19.5 17.5 19.2 17.2 18.9 17.2ZM14.0008 8.9H18.2008C18.9008 8.9 19.5008 9.5 19.4008 10.2V13.7C19.4008 14.3 18.9008 14.9 18.2008 14.9H14.0008C13.4008 14.9 12.8008 14.4 12.8008 13.7V10.2C12.8008 9.5 13.3008 8.9 14.0008 8.9ZM17.6008 13.6C17.9008 13.6 18.2008 13.3 18.2008 12.9V10.9C18.2008 10.5 17.9008 10.2 17.6008 10.2H14.7008C14.4008 10.2 14.1008 10.5 14.1008 10.9V12.9C14.1008 13.3 14.4008 13.6 14.7008 13.6H17.6008Z"></path>
</g>
<defs>
<clipPath id="2nRto_clip0_16_757">
<rect width="24" height="24"></rect>
</clipPath>
</defs>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" id="1jgu4">
<path fill-rule="evenodd" clip-rule="evenodd" d="M5.125 5.375C4.77982 5.375 4.5 5.65482 4.5 6C4.5 6.34518 4.77982 6.625 5.125 6.625H18.875C19.2202 6.625 19.5 6.34518 19.5 6C19.5 5.65482 19.2202 5.375 18.875 5.375H5.125ZM5.125 17.25C4.77982 17.25 4.5 17.5298 4.5 17.875C4.5 18.2202 4.77982 18.5 5.125 18.5H18.875C19.2202 18.5 19.5 18.2202 19.5 17.875C19.5 17.5298 19.2202 17.25 18.875 17.25H5.125ZM9.91602 8.91667C9.22566 8.91667 8.66602 9.47631 8.66602 10.1667V13.7083C8.66602 14.3987 9.22566 14.9583 9.91602 14.9583H14.0826C14.773 14.9583 15.3326 14.3987 15.3326 13.7083V10.1667C15.3326 9.47631 14.773 8.91667 14.0826 8.91667H9.91602ZM10.541 10.2749C10.1958 10.2749 9.91602 10.5837 9.91602 10.9646V12.9186C9.91602 13.2995 10.1958 13.6082 10.541 13.6082H13.4576C13.8028 13.6082 14.0826 13.2995 14.0826 12.9186V10.9646C14.0826 10.5837 13.8028 10.2749 13.4576 10.2749H10.541Z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" id="2Xr9L">
<path fill-rule="evenodd" clip-rule="evenodd" d="M4.5 6.05835C4.5 5.71317 4.77982 5.43335 5.125 5.43335H18.875C19.2202 5.43335 19.5 5.71317 19.5 6.05835C19.5 6.40353 19.2202 6.68335 18.875 6.68335H5.125C4.77982 6.68335 4.5 6.40353 4.5 6.05835ZM4.5 17.9333C4.5 17.5882 4.77982 17.3083 5.125 17.3083H18.875C19.2202 17.3083 19.5 17.5882 19.5 17.9333C19.5 18.2785 19.2202 18.5583 18.875 18.5583H5.125C4.77982 18.5583 4.5 18.2785 4.5 17.9333ZM5.75 8.97512C5.05964 8.97512 4.5 9.53476 4.5 10.2251V13.7668C4.5 14.4571 5.05964 15.0168 5.75 15.0168H18.25C18.9404 15.0168 19.5 14.4571 19.5 13.7668V10.2251C19.5 9.53476 18.9404 8.97512 18.25 8.97512H5.75ZM6.375 10.3334C6.02982 10.3334 5.75 10.6421 5.75 11.023V12.977C5.75 13.3579 6.02982 13.6667 6.375 13.6667H17.625C17.9702 13.6667 18.25 13.3579 18.25 12.977V11.023C18.25 10.6421 17.9702 10.3334 17.625 10.3334H6.375Z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" id="xrK3w">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M5.63604 5.63604C2.12132 9.15076 2.12132 14.8492 5.63604 18.364C9.15076 21.8787 14.8492 21.8787 18.364 18.364C21.8787 14.8492 21.8787 9.15076 18.364 5.63604C14.8492 2.12132 9.15076 2.12132 5.63604 5.63604Z"></path>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M13.9685 8.96949C14.2615 8.6767 14.7364 8.67686 15.0292 8.96985C15.322 9.26284 15.3218 9.73772 15.0288 10.0305L13.0604 11.9976L15.0289 13.9648C15.3219 14.2576 15.3221 14.7324 15.0293 15.0254C14.7365 15.3184 14.2616 15.3186 13.9686 15.0258L11.9994 13.0579L10.0302 15.0258C9.73716 15.3186 9.26228 15.3184 8.96949 15.0254C8.6767 14.7324 8.67686 14.2576 8.96985 13.9648L10.9384 11.9976L8.96998 10.0305C8.67699 9.73772 8.67682 9.26284 8.96962 8.96985C9.26241 8.67686 9.73729 8.6767 10.0303 8.96949L11.9994 10.9373L13.9685 8.96949Z" fill="white"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" id="1g6NF">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M5.63604 5.63604C2.12132 9.15076 2.12132 14.8492 5.63604 18.364C9.15076 21.8787 14.8492 21.8787 18.364 18.364C21.8787 14.8492 21.8787 9.15076 18.364 5.63604C14.8492 2.12132 9.15076 2.12132 5.63604 5.63604Z"></path>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M13.9685 8.96949C14.2615 8.6767 14.7364 8.67686 15.0292 8.96985C15.322 9.26284 15.3218 9.73772 15.0288 10.0305L13.0604 11.9976L15.0289 13.9648C15.3219 14.2576 15.3221 14.7324 15.0293 15.0254C14.7365 15.3184 14.2616 15.3186 13.9686 15.0258L11.9994 13.0579L10.0302 15.0258C9.73716 15.3186 9.26228 15.3184 8.96949 15.0254C8.6767 14.7324 8.67686 14.2576 8.96985 13.9648L10.9384 11.9976L8.96998 10.0305C8.67699 9.73772 8.67682 9.26284 8.96962 8.96985C9.26241 8.67686 9.73729 8.6767 10.0303 8.96949L11.9994 10.9373L13.9685 8.96949Z" fill="black"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" id="1k_rY">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M6.7981 3H17.2981C18.4027 3 19.2981 3.89543 19.2981 5V15.4615C19.2981 15.4735 19.298 15.4854 19.2978 15.4973H15.2981C14.8839 15.4973 14.5481 15.8331 14.5481 16.2473V20.9959L14.5328 20.9959H6.7981C5.69353 20.9959 4.7981 20.1005 4.7981 18.9959V5C4.7981 3.89543 5.69353 3 6.7981 3ZM15.2426 8.50043C15.6568 8.50043 15.9926 8.16464 15.9926 7.75043C15.9926 7.33621 15.6568 7.00043 15.2426 7.00043H8.75745C8.34323 7.00043 8.00745 7.33621 8.00745 7.75043C8.00745 8.16464 8.34323 8.50043 8.75745 8.50043H15.2426ZM15.9926 12.0019C15.9926 12.4161 15.6568 12.7519 15.2426 12.7519H8.75745C8.34324 12.7519 8.00745 12.4161 8.00745 12.0019C8.00745 11.5877 8.34324 11.2519 8.75745 11.2519H15.2426C15.6568 11.2519 15.9926 11.5877 15.9926 12.0019ZM10.7574 16.992C11.1717 16.992 11.5074 16.6562 11.5074 16.242C11.5074 15.8278 11.1717 15.492 10.7574 15.492H8.75745C8.34323 15.492 8.00745 15.8278 8.00745 16.242C8.00745 16.6562 8.34323 16.992 8.75745 16.992H10.7574Z"></path>
    <path d="M16.0481 16.9973H18.6359L16.108 20.2283C16.0886 20.2532 16.0686 20.2775 16.0481 20.3013V16.9973Z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" id="2DJ1t">
    <path d="M20.9534 9.25006C20.997 9.66089 20.6579 9.99693 20.2447 9.99693C19.8316 9.99693 19.5021 9.66014 19.4451 9.25095C19.1084 6.83121 17.1607 4.88577 14.7407 4.54868C14.3305 4.49153 13.9928 4.16114 13.9928 3.74693C13.9928 3.33272 14.3297 2.99271 14.7416 3.03653C18.0098 3.38419 20.6066 5.98162 20.9534 9.25006Z"></path>
    <path d="M17.81 9.25024C17.8892 9.65741 17.5454 9.99693 17.1306 9.99693C16.7158 9.99693 16.3903 9.65204 16.2575 9.25908C16.0165 8.5465 15.4408 7.96953 14.7287 7.72832C14.3368 7.59557 13.9928 7.27099 13.9928 6.85722C13.9928 6.44346 14.3314 6.10051 14.7376 6.17939C16.2867 6.48024 17.5084 7.70146 17.81 9.25024Z"></path>
    <path d="M7.84481 3.88401C7.26588 3.31111 6.33556 3.30488 5.749 3.86998L4.73275 4.84905C4.52069 5.05335 4.32538 5.27435 4.1487 5.50992L3.84282 5.91776C3.42282 6.47776 3.28607 7.20079 3.47254 7.8755L3.55967 8.19078C5.23789 14.2632 10.0449 18.9685 16.1519 20.5165C16.7633 20.6714 17.4118 20.5569 17.9332 20.202L18.2012 20.0196C18.5497 19.7823 18.8698 19.5057 19.1551 19.1954L20.2118 18.0459C20.6733 17.5439 20.6685 16.7707 20.2008 16.2745L19.0203 15.0219L17.6732 13.7128C17.1055 13.1612 16.2003 13.1667 15.6394 13.7253L14.2299 15.1291C14.1626 15.1962 14.0588 15.2101 13.9762 15.1632C12.0042 14.0435 10.3402 12.4529 9.13278 10.5333L8.85021 10.0841C8.77248 9.96047 8.7892 9.79978 8.89071 9.69485L10.22 8.32078C10.7913 7.73013 10.7811 6.78969 10.197 6.21165L7.84481 3.88401Z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" id="2We1D">
    <path d="M20.9092 6.72847L15.9827 11.6571L20.9198 17.2458C21.0125 17.0153 21.0635 16.7636 21.0635 16.5V7.5C21.0635 7.22653 21.0086 6.96588 20.9092 6.72847Z"></path>
    <path d="M19.854 5.66233L12.903 12.6163C12.4116 13.1079 11.6137 13.1042 11.1268 12.6082L4.29859 5.65147C4.53422 5.55387 4.79256 5.5 5.06348 5.5H19.0635C19.3443 5.5 19.6116 5.55787 19.854 5.66233Z"></path>
    <path d="M3.22868 6.7028L8.08736 11.6529L3.18775 17.1956C3.10738 16.9789 3.06348 16.7446 3.06348 16.5V7.5C3.06348 7.21664 3.1224 6.94704 3.22868 6.7028Z"></path>
    <path d="M4.20669 18.3077C4.46639 18.431 4.75688 18.5 5.06348 18.5H19.0635C19.3523 18.5 19.6269 18.4388 19.8748 18.3286L14.9202 12.72L13.9639 13.6767C12.8829 14.7582 11.1274 14.7502 10.0563 13.6589L9.14073 12.7261L4.20669 18.3077Z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 5 7" fill="none" id="2Dwvp">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M4.00155 4.42302C4.63192 3.91364 4.631 3.08705 4.00155 2.57841L1.14139 0.267199C0.511018 -0.24218 0 -0.00291869 0 0.811704V6.18971C0 6.99981 0.51193 7.24287 1.14139 6.73423L4.00155 4.42302Z" fill="#737373"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 12" id="3K9pC">
<path d="M12.572 0.349859C12.938 -0.0729335 13.5774 -0.118993 14.0002 0.246983C14.423 0.612958 14.469 1.25238 14.1031 1.67517L5.89057 11.1626C5.69824 11.3848 5.41888 11.5125 5.12502 11.5125C4.83116 11.5125 4.5518 11.3848 4.35948 11.1626L0.49696 6.70014C0.130999 6.27733 0.17708 5.63791 0.599886 5.27195C1.02269 4.90599 1.66211 4.95207 2.02807 5.37488L5.12506 8.95292L12.572 0.349859Z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" id="3J-9P">
    <g stroke="none" fill-rule="nonzero">
        <path d="M6,3 L18,3 C18.5522847,3 19,3.44771525 19,4 L19,19 C19,19.5522847 18.5522847,20 18,20 L6,20 C5.44771525,20 5,19.5522847 5,19 L5,4 C5,3.44771525 5.44771525,3 6,3 Z M6,4 L6,19 L18,19 L18,4 L6,4 Z M8,12 L16,12 L16,13 L8,13 L8,12 Z M8,15 L16,15 L16,16 L8,16 L8,15 Z M8,6 L12,6 L12,10 L8,10 L8,6 Z M9,7 L9,9 L11,9 L11,7 L9,7 Z"></path>
    </g>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" id="11Qdl">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M16.25 4.5h-8.5A1.5 1.5 0 006.25 6v12a1.5 1.5 0 001.5 1.5h8.5a1.5 1.5 0 001.5-1.5V6a1.5 1.5 0 00-1.5-1.5zM7.75 3a3 3 0 00-3 3v12a3 3 0 003 3h8.5a3 3 0 003-3V6a3 3 0 00-3-3h-8.5z" fill="currentColor"></path>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M16 16.75a.75.75 0 01-.751.75H8.75a.75.75 0 110-1.5h6.498a.75.75 0 01.751.75zM15.249 13.876a.75.75 0 100-1.5H8.75a.75.75 0 100 1.5h6.498z" fill="currentColor"></path>
  <path d="M8 7.25a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 01-1 1H9a1 1 0 01-1-1v-2z" fill="currentColor"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" id="19hYJ">
<path d="M8.30466 4C7.03183 4 6 5.03183 6 6.30466V16.7804C6 18.0532 7.03183 19.085 8.30466 19.085H13.9589C13.8148 18.6564 13.732 18.1995 13.7209 17.7249L13.6219 17.828H8.30466C7.7261 17.828 7.25709 17.3589 7.25709 16.7804V6.30466C7.25709 5.7261 7.7261 5.25709 8.30466 5.25709H16.2662C16.8448 5.25709 17.3138 5.7261 17.3138 6.30466V13.1187C17.6402 13.0454 17.9798 13.0067 18.3284 13.0067C18.4098 13.0067 18.4906 13.0088 18.5709 13.0129V6.30466C18.5709 5.03183 17.539 4 16.2662 4H8.30466Z" fill="currentColor"></path>
<path d="M15.0029 12.9338C15.35 12.9338 15.6314 12.6524 15.6314 12.3052C15.6314 11.9581 15.35 11.6767 15.0029 11.6767H9.56794C9.2208 11.6767 8.93939 11.9581 8.93939 12.3052C8.93939 12.6524 9.2208 12.9338 9.56794 12.9338H15.0029Z" fill="currentColor"></path>
<path d="M11.244 16.076C11.5912 16.076 11.8726 15.7946 11.8726 15.4475C11.8726 15.1004 11.5912 14.8189 11.244 14.8189H9.56793C9.2208 14.8189 8.93939 15.1004 8.93939 15.4475C8.93939 15.7946 9.2208 16.076 9.56793 16.076H11.244Z" fill="currentColor"></path>
<path d="M18.957 15.1078C18.957 14.7607 18.6756 14.4793 18.3284 14.4793C17.9813 14.4793 17.6999 14.7607 17.6999 15.1078V17.0224H15.8431C15.496 17.0224 15.2146 17.3038 15.2146 17.6509C15.2146 17.9981 15.496 18.2795 15.8431 18.2795H17.6999V20.1362C17.6999 20.4833 17.9813 20.7647 18.3284 20.7647C18.6756 20.7647 18.957 20.4833 18.957 20.1362V18.2795H20.8715C21.2186 18.2795 21.5 17.9981 21.5 17.6509C21.5 17.3038 21.2186 17.0224 20.8715 17.0224H18.957V15.1078Z" fill="currentColor"></path>
<rect x="8.76758" y="6.37305" width="3.16667" height="3.16667" rx="1" fill="currentColor"></rect>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" id="1f-rb">
  <path d="M4.5 12a1.5 1.5 0 103 0 1.5 1.5 0 00-3 0zM12.001 13.5a1.5 1.5 0 110-3 1.5 1.5 0 010 3zM18.001 13.5a1.5 1.5 0 110-3 1.5 1.5 0 010 3z"></path>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 20 34" id="1g5_5">
    <g transform="translate(-19.000000, -13.000000)">
        <polyline fill="none" stroke-width="3" transform="translate(30.000000, 30.000000) rotate(90.000000) translate(-30.000000, -30.000000) " points="14 38 30.0000003 22 46 38"></polyline>
    </g>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 20 34" id="2EPJc">
    <g transform="translate(-19.000000, -13.000000)">
        <polyline fill="none" stroke-width="3" transform="translate(30.000000, 30.000000) rotate(-90.000000) translate(-30.000000, -30.000000) " points="14 38 30.0000003 22 46 38"></polyline>
    </g>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 18 18" id="3TWGQ">
    <g transform="translate(-11.000000, -11.000000)">
        <g transform="translate(20.000000, 20.000000) rotate(-135.000000) translate(-20.000000, -20.000000) translate(9.000000, 9.000000)">
            <rect x="10" y="-9.05941988e-14" width="2" height="22"></rect>
            <rect transform="translate(11.000000, 11.000000) rotate(-90.000000) translate(-11.000000, -11.000000) " x="10" y="1.72306613e-13" width="2" height="22"></rect>
        </g>
    </g>
</symbol><symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" id="1z6Lu">
<path fill-rule="evenodd" clip-rule="evenodd" d="M3 12C3 16.9705 7.02954 21 12 21C16.9705 21 21 16.9705 21 12C21 7.02954 16.9705 3 12 3C7.02954 3 3 7.02954 3 12ZM4.63635 12C4.63635 7.9328 7.9328 4.63635 12 4.63635C16.0672 4.63635 19.3636 7.9328 19.3636 12C19.3636 16.0672 16.0672 19.3636 12 19.3636C7.9328 19.3636 4.63635 16.0672 4.63635 12ZM11.9999 17.7272C11.5483 17.7272 11.1818 17.3607 11.1818 16.9091C11.1818 16.4574 11.5483 16.0909 11.9999 16.0909C12.4516 16.0909 12.8181 16.4574 12.8181 16.9091C12.8181 17.3607 12.4516 17.7272 11.9999 17.7272ZM11.9999 14.4545C11.5483 14.4545 11.1818 10.9011 11.1818 9.54543V7.09089C11.1818 6.63925 11.5483 6.27271 11.9999 6.27271C12.4516 6.27271 12.8181 6.63925 12.8181 7.09089V9.54543C12.8181 10.9011 12.4516 14.4545 11.9999 14.4545Z" fill="#A6A6A6"></path>
</symbol></svg>
      </icons>
      <meta itemprop="title" content="Trading - Fine tuning">
      <meta itemprop="created" content="20240226T000348Z">
      <meta itemprop="updated" content="20240302T221707Z">
      <note-attributes>
      </note-attributes>
      
    <h1 class="noteTitle html-note" style="
font-family: Source Sans Pro,-apple-system,system-ui,Segoe UI,Roboto,
Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif;
margin-top: 21px;
margin-bottom: 21px;
font-size: 32px;
">
     <b> Trading - Fine tuning </b>
    </h1>      <div class="para"><span style="font-size: 14px;" data-fontsize="true"><span data-markholder="true"></span></span></div><en-table><div class="container" style="overflow-x: auto;"><table width="1172px" style="grid-template-columns:610px 562px;width:1175px;"><tbody><tr><td data-colwidth="[610]" style="--border-color-lightmode:#ccc;--border-color-darkmode:#737373;--text-color-lightmode:#333;--text-color-darkmode:#ffffff;"><div class="para"><b><span style="font-size: 14px;" data-fontsize="true"><span style="--darkmode-color: rgb(87, 231, 128); --lightmode-color: rgb(24, 168, 65);" class="UrtAp">Candles 1, 4, 6 hour</span></span></b></div><ul role="list"><li><div class="list-bullet-todo-container" contenteditable="false"><input type="checkbox" class="list-bullet-todo" tabindex="-1" aria-label="Checkbox"></div><div class="list-content"><div class="para"><span style="font-size: 14px;" data-fontsize="true">Price breaking highs and lows of previous candle</span></div></div></li><li><div class="list-bullet-todo-container" contenteditable="false"><input type="checkbox" class="list-bullet-todo" tabindex="-1" aria-label="Checkbox"></div><div class="list-content"><div class="para"><span style="font-size: 14px;" data-fontsize="true">Close or open higher or lower - counter trend?</span></div></div></li><li><div class="list-bullet-todo-container" contenteditable="false"><input type="checkbox" class="list-bullet-todo" tabindex="-1" aria-label="Checkbox"></div><div class="list-content"><div class="para"><span style="font-size: 14px;" data-fontsize="true">Wick higher or lower than previous candles's wick</span></div></div></li><li><div class="list-bullet-todo-container" contenteditable="false"><input type="checkbox" class="list-bullet-todo" tabindex="-1" aria-label="Checkbox"></div><div class="list-content"><div class="para"><span style="font-size: 14px;" data-fontsize="true">4 or 6 hour - Up or Down candle?</span></div></div></li><li><div class="list-bullet-todo-container" contenteditable="false"><input type="checkbox" class="list-bullet-todo" tabindex="-1" aria-label="Checkbox"></div><div class="list-content"><div class="para"><span style="font-size: 14px;" data-fontsize="true">Is 1 hour trending red but 4 hour still green candle</span></div></div></li><li><div class="list-bullet-todo-container" contenteditable="false"><input type="checkbox" class="list-bullet-todo" tabindex="-1" aria-label="Checkbox"></div><div class="list-content"><div class="para"><span style="font-size: 14px;" data-fontsize="true">1 hour green candle higher than 4 hour green candle</span></div></div></li><li><div class="list-bullet-todo-container" contenteditable="false"><input type="checkbox" class="list-bullet-todo" tabindex="-1" aria-label="Checkbox"></div><div class="list-content"><div class="para"><span style="font-size: 14px;" data-fontsize="true">heikin without a wick - Strong Candle?</span></div></div></li><li><div class="list-bullet-todo-container" contenteditable="false"><input type="checkbox" class="list-bullet-todo" tabindex="-1" aria-label="Checkbox"></div><div class="list-content"><div class="para"><span style="font-size: 14px;" data-fontsize="true">Ratios - Body to high and low wicks</span></div></div></li><li><div class="list-bullet-todo-container" contenteditable="false"><input type="checkbox" class="list-bullet-todo" tabindex="-1" aria-label="Checkbox"></div><div class="list-content"><div class="para"><span style="font-size: 14px;" data-fontsize="true">Dojis?</span></div></div></li><ul role="list"><li><div class="list-bullet-todo-container" contenteditable="false"><input type="checkbox" class="list-bullet-todo" tabindex="-1" aria-label="Checkbox"></div><div class="list-content"><div class="para"><span style="font-size: 14px;" data-fontsize="true">Doji looking for extremes only?</span></div></div></li></ul><li><div class="list-bullet-todo-container" contenteditable="false"><input type="checkbox" class="list-bullet-todo" tabindex="-1" aria-label="Checkbox"></div><div class="list-content"><div class="para"><span style="font-size: 14px;" data-fontsize="true">End of Strong Trend</span></div></div></li><ul role="list"><li><div class="list-bullet-todo-container" contenteditable="false"><input type="checkbox" class="list-bullet-todo" tabindex="-1" aria-label="Checkbox"></div><div class="list-content"><div class="para"><span style="font-size: 14px;" data-fontsize="true">Full bodied 4 hour candle with small wick</span></div></div></li></ul></ul><div class="para"><span style="font-size: 14px;" data-fontsize="true"><span data-markholder="true"></span></span></div><div class="para"><b><span style="font-size: 14px;" data-fontsize="true"><span style="--darkmode-color: rgb(87, 231, 128); --lightmode-color: rgb(24, 168, 65);" class="UrtAp">EMA 30 min</span></span></b></div><ul role="list"><li><div class="list-bullet-todo-container" contenteditable="false"><input type="checkbox" class="list-bullet-todo" tabindex="-1" aria-label="Checkbox"></div><div class="list-content"><div class="para"><span style="font-size: 14px;" data-fontsize="true">Are 20, 50, 100, 200 levels now support or resistance?</span></div></div></li><li><div class="list-bullet-todo-container" contenteditable="false"><input type="checkbox" class="list-bullet-todo" tabindex="-1" aria-label="Checkbox"></div><div class="list-content"><div class="para"><span style="font-size: 14px;" data-fontsize="true">200 angle color, increasing or decreasing</span></div></div></li></ul><div class="para"><span style="font-size: 14px;" data-fontsize="true"><span data-markholder="true"></span></span></div><div class="para"><b><span style="font-size: 14px;" data-fontsize="true"><span style="--darkmode-color: rgb(87, 231, 128); --lightmode-color: rgb(24, 168, 65);" class="UrtAp">MA&nbsp;- HMA 1 min</span></span></b></div><ul role="list"><li><div class="list-bullet-todo-container" contenteditable="false"><input type="checkbox" class="list-bullet-todo" tabindex="-1" aria-label="Checkbox"></div><div class="list-content"><div class="para">If price is breaking candle high or low (4 hour) wait until M9 is also passed the high low</div></div></li><li><div class="list-bullet-todo-container" contenteditable="false"><input type="checkbox" class="list-bullet-todo" tabindex="-1" aria-label="Checkbox"></div><div class="list-content"><div class="para"><span style="font-size: 14px;" data-fontsize="true">Red and&nbsp; Green when entering or exiting trades</span></div></div></li><ul role="list"><li><div class="list-bullet-todo-container" contenteditable="false"><input type="checkbox" class="list-bullet-todo" tabindex="-1" aria-label="Checkbox"></div><div class="list-content"><div class="para"><span style="font-size: 14px;" data-fontsize="true">When looking for tops and bottoms must have a good cross</span></div></div></li></ul><li><div class="list-bullet-todo-container" contenteditable="false"><input type="checkbox" class="list-bullet-todo" tabindex="-1" aria-label="Checkbox"></div><div class="list-content"><div class="para"><span style="font-size: 14px;" data-fontsize="true">M9 angle is red and angled down don't take buy wait until angle is orange at least</span></div></div></li><li><div class="list-bullet-todo-container" contenteditable="false"><input type="checkbox" class="list-bullet-todo" tabindex="-1" aria-label="Checkbox"></div><div class="list-content"><div class="para"><span style="font-size: 14px;" data-fontsize="true">M9 (1000) above or below 4 hour candle</span></div></div></li></ul><div class="para"><span style="font-size: 14px;" data-fontsize="true"><span data-markholder="true"></span></span></div><div class="para"><b><span style="font-size: 14px;" data-fontsize="true"><span style="--darkmode-color: rgb(87, 231, 128); --lightmode-color: rgb(24, 168, 65);" class="UrtAp">Angles MA - HMA 1 min</span></span></b></div><ul role="list"><li><div class="list-bullet-todo-container" contenteditable="false"><input type="checkbox" class="list-bullet-todo" tabindex="-1" aria-label="Checkbox"></div><div class="list-content"><div class="para"><span style="font-size: 14px;" data-fontsize="true">M5 &gt; M8 or M5&lt; M8</span></div></div></li><li><div class="list-bullet-todo-container" contenteditable="false"><input type="checkbox" class="list-bullet-todo" tabindex="-1" aria-label="Checkbox"></div><div class="list-content"><div class="para"><span style="font-size: 14px;" data-fontsize="true">M8 color white?</span></div></div></li><li><div class="list-bullet-todo-container" contenteditable="false"><input type="checkbox" class="list-bullet-todo" tabindex="-1" aria-label="Checkbox"></div><div class="list-content"><div class="para"><span style="font-size: 14px;" data-fontsize="true">M2 distance to price. Wait until less than 1 pip</span></div></div></li></ul><div class="para"><span style="font-size: 14px;" data-fontsize="true"><span data-markholder="true"></span></span></div><div class="para"><b><span style="font-size: 14px;" data-fontsize="true"><span style="--darkmode-color: rgb(87, 231, 128); --lightmode-color: rgb(24, 168, 65);" class="UrtAp">BB Bands&nbsp; </span></span></b></div><div class="para"><b><span style="font-size: 14px;" data-fontsize="true"><span style="--darkmode-color: rgb(87, 231, 128); --lightmode-color: rgb(24, 168, 65);" class="UrtAp">&nbsp; &nbsp; &nbsp; 1 min (Length 50, 250; SMA and WMA)</span></span></b></div><ul role="list"><li><div class="list-bullet-todo-container" contenteditable="false"><input type="checkbox" class="list-bullet-todo" tabindex="-1" aria-label="Checkbox"></div><div class="list-content"><div class="para"><span style="font-size: 14px;" data-fontsize="true">Is 50 above / below 250</span></div></div></li><li><div class="list-bullet-todo-container" contenteditable="false"><input type="checkbox" class="list-bullet-todo" tabindex="-1" aria-label="Checkbox"></div><div class="list-content"><div class="para"><span style="font-size: 14px;" data-fontsize="true">What zone color for 50 &amp; 250</span></div></div></li></ul><div class="para"><span style="font-size: 14px;" data-fontsize="true"><span data-markholder="true"></span></span></div><div class="para"><b><span style="font-size: 14px;" data-fontsize="true"><span style="--darkmode-color: rgb(87, 231, 128); --lightmode-color: rgb(24, 168, 65);" class="UrtAp">&nbsp; &nbsp;&nbsp; 30 Min - Length 15 WMA</span></span></b></div><ul role="list"><li><div class="list-bullet-todo-container" contenteditable="false"><input type="checkbox" class="list-bullet-todo" tabindex="-1" aria-label="Checkbox"></div><div class="list-content"><div class="para"><span style="font-size: 14px;" data-fontsize="true">stepping up or down?</span></div></div></li><li><div class="list-bullet-todo-container" contenteditable="false"><input type="checkbox" class="list-bullet-todo" tabindex="-1" aria-label="Checkbox"></div><div class="list-content"><div class="para"><span style="font-size: 14px;" data-fontsize="true">zone color?</span></div></div></li></ul><div class="para"><span style="font-size: 14px;" data-fontsize="true"><span data-markholder="true"></span></span></div><div class="para"><b><span style="font-size: 14px;" data-fontsize="true"><span style="--darkmode-color: rgb(87, 231, 128); --lightmode-color: rgb(24, 168, 65);" class="UrtAp">Predictive Channels -&nbsp; (4,50)</span></span></b></div><ul role="list"><li><div class="list-bullet-todo-container" contenteditable="false"><input type="checkbox" class="list-bullet-todo" tabindex="-1" aria-label="Checkbox"></div><div class="list-content"><div class="para"><span style="font-size: 14px;" data-fontsize="true">Price Breaking bounds?</span></div></div></li><li><div class="list-bullet-todo-container" contenteditable="false"><input type="checkbox" class="list-bullet-todo" tabindex="-1" aria-label="Checkbox"></div><div class="list-content"><div class="para"><span style="font-size: 14px;" data-fontsize="true">Box higher or lower than previous box</span></div></div></li><li><div class="list-bullet-todo-container" contenteditable="false"><input type="checkbox" class="list-bullet-todo" tabindex="-1" aria-label="Checkbox"></div><div class="list-content"><div class="para"><span style="font-size: 14px;" data-fontsize="true">Is Box above Green 4 hour candle or Box below Red candle</span></div></div></li><li><div class="list-bullet-todo-container" contenteditable="false"><input type="checkbox" class="list-bullet-todo" tabindex="-1" aria-label="Checkbox"></div><div class="list-content"><div class="para"><span style="font-size: 14px;" data-fontsize="true">Direction of box - Angled up or down?</span></div></div></li></ul><div class="para"><span style="font-size: 14px;" data-fontsize="true"><span data-markholder="true"></span></span></div><div class="para"><b><span style="font-size: 14px;" data-fontsize="true"><span style="--darkmode-color: rgb(87, 231, 128); --lightmode-color: rgb(24, 168, 65);" class="UrtAp">Parabolic SAR -&nbsp; (15 min)</span></span></b></div><ul role="list"><li><div class="list-bullet-todo-container" contenteditable="false"><input type="checkbox" class="list-bullet-todo" tabindex="-1" aria-label="Checkbox"></div><div class="list-content"><div class="para"><span style="font-size: 14px;" data-fontsize="true">When looking for tops or bottom wait until SAR at least enters the bottom of a Predictive Channel box. Especially if SAR began below Highers Lowest midline</span></div></div></li><li><div class="list-bullet-todo-container" contenteditable="false"><input type="checkbox" class="list-bullet-todo" tabindex="-1" aria-label="Checkbox"></div><div class="list-content"><div class="para"><span style="font-size: 14px;" data-fontsize="true">If price is above green candle high point and above Highest/Lowest wait until SAR is above</span></div></div></li></ul><div class="para"><span style="font-size: 14px;" data-fontsize="true"><span data-markholder="true"></span></span></div><div class="para"><b><span style="font-size: 14px;" data-fontsize="true"><span style="--darkmode-color: rgb(87, 231, 128); --lightmode-color: rgb(24, 168, 65);" class="UrtAp">Consolidation Zones - 1 min (50,[2-5])</span></span></b></div><ul role="list"><li><div class="list-bullet-todo-container" contenteditable="false"><input type="checkbox" class="list-bullet-todo" tabindex="-1" aria-label="Checkbox"></div><div class="list-content"><div class="para"><span style="font-size: 14px;" data-fontsize="true">Distance between price and mid line. Is is consolidating</span></div></div></li></ul><div class="para"><span style="font-size: 14px;" data-fontsize="true"><span data-markholder="true"></span></span></div><div class="para"><b><span style="font-size: 14px;" data-fontsize="true"><span style="--darkmode-color: rgb(87, 231, 128); --lightmode-color: rgb(24, 168, 65);" class="UrtAp">Trendilo -&nbsp; 1 min (Lookback 50)</span></span></b></div><ul role="list"><li><div class="list-bullet-todo-container" contenteditable="false"><input type="checkbox" class="list-bullet-todo" tabindex="-1" aria-label="Checkbox"></div><div class="list-content"><div class="para"><span style="font-size: 14px;" data-fontsize="true">Is it Red or Green</span></div></div></li><li><div class="list-bullet-todo-container" contenteditable="false"><input type="checkbox" class="list-bullet-todo" tabindex="-1" aria-label="Checkbox"></div><div class="list-content"><div class="para"><span style="font-size: 14px;" data-fontsize="true">Starting to change direction?</span></div></div></li></ul><div class="para"><span style="font-size: 14px;" data-fontsize="true"><span data-markholder="true"></span></span></div></td><td data-colwidth="[562]" style="--border-color-lightmode:#ccc;--border-color-darkmode:#737373;--text-color-lightmode:#333;--text-color-darkmode:#ffffff;"><div class="para"><br></div><en-codeblock spellcheck="false"><div data-plaintext="true">// Breaking candle how and low</div><div data-plaintext="true">close&gt;ch or close&lt;cl</div><div data-plaintext="true">// Is strong Heikin candle (no wick)</div><div data-plaintext="true">co_h == ch_h or co_h == cl_h</div><div data-plaintext="true">// Higher or lower previous candle</div><div data-plaintext="true">ch&gt;ch[1] or ch&lt;ch[1] or cl&gt;cl[1] or cl&lt;cl[1]</div><div data-plaintext="true">// Candle Size</div><div data-plaintext="true">candle_dist = get_pip_distance(cc,co)</div><div data-plaintext="true">candle_dist = get_pip_distance(ch,cl)</div><div data-plaintext="true">Ratio//</div><div data-plaintext="true">[C_hl,C_hratio,C_lratio,C_body,C_color] = f_candle_ratios(co,ch,cl,cc)</div></en-codeblock><div class="para"><br></div><div class="para"><br></div><en-codeblock spellcheck="false"><div data-plaintext="true"><br></div></en-codeblock><div class="para"><br></div><div class="para"><br></div><div class="para"><br></div><en-codeblock spellcheck="false"><div data-plaintext="true"><br></div></en-codeblock><div class="para"><br></div><div class="para"><br></div><div class="para"><br></div><div class="para"><br></div><div class="para"><br></div><en-codeblock spellcheck="false"><div data-plaintext="true"><br></div></en-codeblock><div class="para"><br></div><en-codeblock spellcheck="false"><div data-plaintext="true"><br></div></en-codeblock><div class="para"><br></div><en-codeblock spellcheck="false"><div data-plaintext="true"><br></div></en-codeblock><div class="para"><br></div><div class="para"><br></div><div class="para"><br></div><div class="para"><br></div><en-codeblock spellcheck="false"><div data-plaintext="true"><br></div></en-codeblock><div class="para"><br></div><div class="para"><br></div><div class="para"><br></div><div class="para"><br></div><div class="para"><br></div><div class="para"><br></div><en-codeblock spellcheck="false"><div data-plaintext="true"><br></div></en-codeblock></td></tr></tbody></table></div></en-table><div class="para"><br></div>    </en-note>
  </body>
</html>
