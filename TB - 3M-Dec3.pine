//@version=4
study(title="TB - 3M-Dec 3", shorttitle="TB - 3M-Dec 3",overlay=true,max_labels_count=500)

g_main_inputs = "Global Settings -------------------------------------------------------------"
inl_global = "inl_global"
use_logic = input(title="Use Logic", type=input.bool, defval=false,group=g_main_inputs,inline=inl_global)
show_days = input(title="Show Days", type=input.bool, defval=false,group=g_main_inputs,inline=inl_global)
show_trades = input(title="Show Trades", type=input.bool, defval=true,group=g_main_inputs,inline=inl_global)
candle_close = input(title="Candle Close", type=input.resolution, defval="15",group=g_main_inputs)


g_bar = "Bar color ---------------------------------------------------"
inl_bar = "inl_bar"
bar_s1 = input(title="s1", type=input.bool, defval=false,inline=inl_bar,group=g_bar) 
bar_s2 = input(title="s2", type=input.bool, defval=false,inline=inl_bar,group=g_bar) 
bar_s1_m = input(title="s1 M", type=input.bool, defval=false,inline=inl_bar,group=g_bar) 
bar_s2_m = input(title="s2 M", type=input.bool, defval=false,inline=inl_bar,group=g_bar) 

bar_ema = input(title="EMA", type=input.bool, defval=false,inline=inl_bar,group=g_bar) 
bar_macd = input(title="MACD", type=input.bool, defval=false,inline=inl_bar,group=g_bar)
bar_close = input(title="Close", type=input.bool, defval=false,inline=inl_bar,group=g_bar)
bar_res = input(title="RES", type=input.bool, defval=false,inline=inl_bar,group=g_bar) 
bar_bb = input(title="BB", type=input.bool, defval=false,inline=inl_bar,group=g_bar) 
bb_candles1 = input(title="Show Candles", type=input.bool, defval=false,inline=inl_bar,group=g_bar) 
bb_candles2 = input(title="Show Candles2", type=input.bool, defval=false,inline=inl_bar,group=g_bar)
bb_candles_m = input(title="Candles Multi", type=input.bool, defval=false,inline=inl_bar,group=g_bar)
bar_bb_zone = input(title="BB Zone", type=input.bool, defval=false,inline=inl_bar,group=g_bar)

bar_trend_up = input(title="Trend Up", type=input.bool, defval=false,inline=inl_bar,group=g_bar)  
bar_trend_down = input(title="Trend Down", type=input.bool, defval=false,inline=inl_bar,group=g_bar) 
bar_trending_up = input(title="Trans Up", type=input.bool, defval=false,inline=inl_bar,group=g_bar)  
bar_trending_down = input(title="Trans Down", type=input.bool, defval=false,inline=inl_bar,group=g_bar) 
bar_conv = input(title="Conv", type=input.bool, defval=false,inline=inl_bar,group=g_bar)  
bar_conv_m = input(title="Conv Multi", type=input.bool, defval=false,inline=inl_bar,group=g_bar) 
bar_days = input(title="Show Days", type=input.bool, defval=false,inline=inl_bar,group=g_bar) 
day_of_week = input(title="Day", defval=6,options=[1,2,3,4,5,6])

g_filters = "Filters -------------------------------------------------------------"
inl_ftr1 = "inl_ftr1"
inl_ftr2 = "inl_ftr2"
filter_perc_input = input(title="Perc",type=input.bool,defval=true,group=g_filters,inline=inl_ftr1) 
filter_adx_input = input(title="ADX",type=input.bool,defval=true,group=g_filters,inline=inl_ftr1) 
filter_s5_input = input(title="S5",type=input.bool,defval=true,group=g_filters,inline=inl_ftr1)
filter_ema_input = input(title="EMA",type=input.bool,defval=true,group=g_filters,inline=inl_ftr1)
filter_s4_input = input(title="S4",type=input.bool,defval=true,group=g_filters,inline=inl_ftr2)
filter_aroon_input = input(title="Aroon",type=input.bool,defval=true,group=g_filters,inline=inl_ftr2)
use_ny_filter = input(title="NY close",type=input.bool,defval=true,group=g_filters,inline=inl_ftr2)
//show_sessions = input(title="Sessions",type=input.bool,defval=true)

// Colors
red = #ff0000
aqua = #00c3ff
yellow = #FFFF00
orange = #ff9800
green = #4caf50
lime = #50ff00
white = #ffffff
blue = #2962ff
d_blue = #0053ff
violet = #814dff
magenta = #ff0062
purple = #0045b3
gray = #707070
black = #000000
sell_color = color.new(#ff0062,20)
buy_color = color.new(#00c3ff,20)
c_hide = color.new(#ffffff,100)


// Global Functions
angle(_src, len) =>
    rad2degree=180/3.14159265359  //pi 
    ang=rad2degree*atan((_src[0] - _src[1])/atr(len))

// Change
perc_change() =>
    perc = abs( (1 - (close[1] / close)) * 10000 )


g_days = "Days -------------------------------------------------------------"
inl_days = "inl_dats"
// Europe/London - Pacific/Auckland - Asia/Tokyo
timeinrange(res, sess, loc) => not na(time(res, sess, loc)) ? 1 : 0
ny_open = timeinrange("2", "0800-0900", "America/New_York")
ny_close = timeinrange("2", "1045-1230", "Pacific/Honolulu")
day_colors = color.new(sell_color,92)

// bgcolor(show_days and ny_close == 1 and use_ny_filter ? color.new(gray,92) : na)
// bgcolor(show_days and ny_open == 1 and use_ny_filter ? color.new(aqua,92) : na)

high_m = security(syminfo.tickerid, candle_close, high)
close_m = security(syminfo.tickerid, candle_close, close)
open_m = security(syminfo.tickerid, candle_close, open)
low_m = security(syminfo.tickerid, candle_close, low)

g_cb = "SSL -------------------------------------------------------------"
inl_cb = "cb"
show_ssl = input(title="Show SSL", type=input.bool, defval=false,group=g_cb)
show_s1 = input(title="S1", type=input.bool, defval=false,group=g_cb,inline=inl_cb)
show_s2 = input(title="s2", type=input.bool, defval=true,group=g_cb,inline=inl_cb)
show_s3 = input(title="s3", type=input.bool, defval=false,group=g_cb,inline=inl_cb)
show_s4 = input(title="S4", type=input.bool, defval=true,group=g_cb,inline=inl_cb)
show_s5 = input(title="S5", type=input.bool, defval=true,group=g_cb,inline=inl_cb)
show_s6 = input(title="S6", type=input.bool, defval=true,group=g_cb,inline=inl_cb)
show_s7 = input(title="S7", type=input.bool, defval=false,group=g_cb,inline=inl_cb)
show_s8 = input(title="S8", type=input.bool, defval=true,group=g_cb,inline=inl_cb)

inl_len = "len1"
s_len1 = input(8,title="S1",inline=inl_len) // 8
s_len2 = input(20,title="S2",inline=inl_len) // 20
s_len3 = input(50,title="S3",inline=inl_len) // 50
s_len4 = input(75,title="S4",inline=inl_len) // 75 
s_len5 = input(100,title="S5",inline=inl_len) // 100
s_len6 = input(200,title="S6",inline=inl_len) // 200
s_len7 = input(300,title="S7",inline=inl_len) // 300
s_len8 = input(500,title="S8",inline=inl_len) // 500

g_fill = ""
inl_fill = "fill"
inl_conv = "conv"
show_fill = input(title="Show Fill", type=input.bool, defval=true,inline=inl_fill,group=g_fill)
show_conv = input(title="Show Conv", type=input.bool, defval=true,inline=inl_fill,group=g_fill)
// NZD looks like 2.5 is a good amount
conv_amount = input(defval=14, title="Conv Amount", type=input.float, step=1,inline=inl_conv,group=g_fill ) 
c_type = input(title="Type", defval="USD", options=["USD", "JPY" ],inline=inl_conv,group=g_fill)
line_input = 1 //input(1, title="Line width", type=input.integer,inline=inl_fill )

g_multi = ""
inl_multi = "multi"
ssl_curr = input(title="Show Current", type=input.bool, defval=true,inline=inl_multi,group=g_multi)
use_ssl_multi = input(title="Show Multi", type=input.bool, defval=false,inline=inl_multi,group=g_multi)
ssl_res = input(title="Timeframe", type=input.resolution, defval="30",group=g_multi)
//useCurrentRes = input(true, title="Chart Resolution?",inline=inl_multi,group=g_multi)
angle_input = 14 // input(title="Angle Amount",type=input.integer, defval=14)

s_lines(len) =>
    s = wma(2*wma(close, len/2)-wma(close, len), round(sqrt(len)))
    sa = angle(s,angle_input)
    [s,sa]

[s1,s1_a] = s_lines(s_len1)
[s2,s2_a] = s_lines(s_len2)
[s3,s3_a] = s_lines(s_len3)
[s4,s4_a] = s_lines(s_len4)
[s5,s5_a] = s_lines(s_len5)
[s6,s6_a] = s_lines(s_len6)
[s7,s7_a] = s_lines(s_len7)
[s8,s8_a] = s_lines(s_len8)

// Diff and Convergence
s_conv(t1, t2) =>
    boost = c_type=="JPY" ? 100 : 1000
    diff = (t1 - t2) * boost
    conv = show_conv and diff<conv_amount and diff>(conv_amount * -1) ? true : false
    [diff,conv]

[s5_s6_diff,s5_s6_conv] = s_conv(s5,s6)
[s7_s8_diff,s7_s8_conv] = s_conv(s7,s8)
ssl_conv    = s5_s6_diff<conv_amount and s5_s6_diff>(conv_amount * -1) ? 1 : 0
ssl_conv2   = s7_s8_diff<conv_amount and s7_s8_diff>(conv_amount * -1) ? 1 : 0

// S5/S6
mo  = ssl_conv == false ? true: false
mr   = s5>s6 ? true : false
mr_u = mr and ssl_conv and s5_a>0
mr_d = mr and ssl_conv and s5_a<0
// Green
mg   = s5<s6 ? true : false
mg_u = mg and ssl_conv and s5_a>0
mg_d = mg and ssl_conv and s5_a<0

// S7/S8
mo2  = ssl_conv2 == false ? true: false
mr2   = s7>s8? true : false
mr2_u = mr2 and ssl_conv2 and s7_a>0
mr2_d = mr2 and ssl_conv2 and s7_a<0
// Green
mg2   = s7<s8? true : false
mg2_u = mg2 and ssl_conv2 and s7_a>0
mg2_d = mg2 and ssl_conv2 and s7_a<0


// Plot
s1_f = plot(show_ssl and ssl_curr and show_s1?s1:na, color=aqua , title="S1", linewidth=line_input)
s2_f = plot(show_ssl and ssl_curr and show_s2?s2:na, color=white , title="S2")
s3_f = plot(show_ssl and ssl_curr and show_s3?s3:na, color=aqua , title="S3")
s4_f = plot(show_ssl and ssl_curr and show_s4?s4:na, color=s4_a>0?yellow:orange , title="S4", linewidth=2)
s5_f = plot(show_ssl and ssl_curr and show_s5?s5:na, color=s5_a>0?orange : s5_a<0 and s5>s6 ? red : green, title="S5",linewidth=line_input)
s6_f = plot(show_ssl and ssl_curr and show_s6?s6:na, color=s6_a>0?red:lime , title="S6",linewidth=line_input)
s7_f = plot(show_ssl and ssl_curr and show_s7?s7:na, color=s7_a>0?orange : s7_a<0 and s7>s8 ? red : green , title="S7",linewidth=line_input)
s8_f = plot(show_ssl and ssl_curr and show_s8?s8:na, color=s8_a>0?red:lime, title="S8",linewidth=line_input)

// Fills
fill(s5_f,s6_f,title="S5/S6 Fill", color=show_fill and s5_s6_diff<1?color.new(green,90): show_fill ? color.new(red,90):na)
fill(s7_f,s8_f,title="S7/S8 Fill", color=show_fill and s7_s8_diff<1?color.new(green,90): show_fill ? color.new(red,90):na)
// Conv
fill(s5_f,s6_f,title="S5/S6 Conv", color=s5_s6_conv and s5>s6?color.new(red,70): s5_s6_conv and s5<s6?color.new(green,70) : na)
fill(s7_f,s8_f,title="S7/S8 Conv", color=s7_s8_conv and s7>s8?color.new(red,70): s7_s8_conv and s7<s8?color.new(green,70) : na)
  

// === MULTI  ===
// ==============
s1_m = security(syminfo.tickerid, ssl_res, s1)
s1_a_m = security(syminfo.tickerid, ssl_res, s1_a)
s2_m = security(syminfo.tickerid, ssl_res, s2)
s2_a_m = security(syminfo.tickerid, ssl_res, s2_a)
s3_m = security(syminfo.tickerid, ssl_res, s3)
s3_a_m = security(syminfo.tickerid, ssl_res, s3_a)
s4_m = security(syminfo.tickerid, ssl_res, s4)
s4_a_m = security(syminfo.tickerid, ssl_res, s4_a)
s5_m = security(syminfo.tickerid, ssl_res, s5)
s5_a_m = security(syminfo.tickerid, ssl_res, s5_a)
s6_m = security(syminfo.tickerid, ssl_res, s6)
s6_a_m = security(syminfo.tickerid, ssl_res, s6_a)
s7_m = security(syminfo.tickerid, ssl_res, s7)
s7_a_m = security(syminfo.tickerid, ssl_res, s7_a)
s8_m = security(syminfo.tickerid, ssl_res, s8)
s8_a_m = security(syminfo.tickerid, ssl_res, s8_a)


// Diff
s5_s6_diff_m = security(syminfo.tickerid, ssl_res, s5_s6_diff)
s5_s6_conv_m = security(syminfo.tickerid, ssl_res, s5_s6_conv)
s7_s8_diff_m = security(syminfo.tickerid, ssl_res, s7_s8_diff)
s7_s8_conv_m = security(syminfo.tickerid, ssl_res, s7_s8_conv)

// S5/S6
mr_m   = s5_m>s6_m ? true : false
mo_m   = s5_s6_conv_m == false ? true: false
mr_u_m = mr_m and s5_s6_conv_m and s5_a_m>0
mr_d_m = mr_m and s5_s6_conv_m and s5_a_m<0
// Green
mg_m   = s5_m<s6_m ? true : false
mg_u_m = mg_m and s5_s6_conv_m and s5_a_m>0
mg_d_m = mg_m and s5_s6_conv_m and s5_a_m<0

// S7/S8
mr2_m   = s7_m>s8_m ? true : false
mo2_m   = s7_s8_conv_m == false ? true : false
mr2_u_m = mr2_m and s7_s8_conv_m and s7_a_m>0
mr2_d_m = mr2_m and s7_s8_conv_m and s7_a_m<0
// Green
mg2_m   = s7_m<s8_m ? true : false
mg2_u_m = mg2_m and s7_s8_conv_m and s7_a_m>0
mg2_d_m = mg2_m and s7_s8_conv_m and s7_a_m<0


// Plot
s1_f_m =plot(show_ssl and use_ssl_multi and show_s1?s1_m:na, title="S1 Multi",linewidth=line_input, color=aqua)
s2_f_m =plot(show_ssl and use_ssl_multi and show_s2?s2_m:na, title="S2 Multi",linewidth=line_input, color=white)
s3_f_m =plot(show_ssl and use_ssl_multi and show_s3?s3_m:na, title="S3 Multi",linewidth=line_input, color=blue)
s4_f_m =plot(show_ssl and use_ssl_multi and show_s4?s4_m:na, title="S4 Multi",linewidth=line_input, color=s4_a_m>0?yellow:orange)
s5_f_m =plot(show_ssl and use_ssl_multi and show_s5?s5_m:na, title="S5 Multi",linewidth=line_input, color=s5_a_m>0 ? orange : s5_a_m<0 and s5_m>s6_m ? red : green)
s6_f_m =plot(show_ssl and use_ssl_multi and show_s6?s6_m:na, title="S6 Multi",linewidth=line_input, color=s6_a_m>0 ? red : lime)
s7_f_m =plot(show_ssl and use_ssl_multi and show_s7?s7_m:na, title="S7 Multi",linewidth=line_input, color=s7_a_m>0 ? orange : s7_a_m<0 and s7_m>s8_m ? red : green)
s8_f_m =plot(show_ssl and use_ssl_multi and show_s8?s8_m:na, title="S8 Multi",linewidth=line_input, color=s8_a_m>0 ? red : lime)
plot(show_ssl and use_ssl_multi and show_s8?s8_a_m:na, title="S8 Multi A", color=s8_a_m>0 ? color.new(red,100) : color.new(lime,100) )

// Fills
fill(s5_f_m,s6_f_m,title="S5/S6 Fill Multi", color=show_fill and s5_s6_diff_m<1?color.new(green,90): show_fill ? color.new(red,90):na)
fill(s7_f_m,s8_f_m,title="S7/S8 Fill Multi", color=show_fill and s7_s8_diff_m<1?color.new(green,90): show_fill ? color.new(red,90):na)
// Conv
fill(s5_f_m,s6_f_m,title="S5/S6 Conv Multi", color=s5_s6_conv_m and s5_m>s6_m?color.new(red,70): s5_s6_conv_m and s5_m<s6_m?color.new(green,70) : na)
fill(s7_f_m,s8_f_m,title="S7/S8 Conv Multi", color=s7_s8_conv_m and s7_m>s8_m?color.new(red,70): s7_s8_conv_m and s7_m<s8_m?color.new(green,70) : na)


g_bb = "Bollinger Bands -------------------------------------------------------------"
inl_bb1 = "inl_bb1"
inl_bb2 = "inl_bb2"
inl_bb3 = "inl_bb3"
show_bb = input(title="Show Bands", type=input.bool, defval=false,group=g_bb)
BB_length = input(15, title="BB 1",group=g_bb, inline=inl_bb1) //   20 15  75  20 12 8 20
BB_length2 = input(100, title="BB 2",group=g_bb, inline=inl_bb1) // 350 100 150 300 100 20 40
bb_length_multi = input(20, title="BB Multi",group=g_bb, inline=inl_bb1)
sqz_length = input(80, title="Sqz Len",group=g_bb, inline=inl_bb1) // 100

bb_smooth_type = input(title="Smooth Type", defval="SMA", options=["SMA", "EMA", "WMA", "HULL" ])
bb_bias = input(title="Bias 1", type=input.float, defval=89.7,step=0.1,group=g_bb, inline=inl_bb3) 
bb_bias2 = input(title="Bias 2", type=input.float, defval=89.7,step=0.1,group=g_bb, inline=inl_bb3) // 89.70 // 88.85
bb_bias_m = input(title="Bias Multi", type=input.float, defval=89.7,step=0.1,group=g_bb, inline=inl_bb3)

bb_smoothing(len1,type) =>
    float v = na
    if type=="SMA"
        v := sma(close, len1)
    if type=="EMA"
        v := ema(close, len1)
    if type=="WMA"
        v := wma(close, len1)
    if type=="HULL"
        v := wma(2*wma(close, len1/2)-wma(close, len1), round(sqrt(len1)))
    [v]

// Timeframe
g_bb_time = ""
inl_bb4 = "inl_bb4"
use_bb_curr1 = input(title="BB1", type=input.bool, defval=false,inline=inl_bb4,group=g_bb_time)
use_bb_curr2 = input(title="BB2", type=input.bool, defval=false,inline=inl_bb4,group=g_bb_time)
use_bb_multi = input(title="BB Multi", type=input.bool, defval=true,inline=inl_bb4,group=g_bb_time)
bb_time = input(title="Timeframe", type=input.resolution, defval="30", group=g_bb_time)

BB_stdDev = 2 //input(2, minval=2.0, maxval=3)
bb_smooth_input = 1 // input(title="Smoothing",defval=1,type=input.integer,group=g_bb, inline=inl_bb1) 

// === BB 1 ===
[basis] = bb_smoothing(BB_length,bb_smooth_type)
dev = BB_stdDev * stdev(close, BB_length)
bb_upper = basis + dev
bb_lower = basis - dev
bb_spread = bb_upper - bb_lower
avgspread = sma(bb_spread, sqz_length)

bb_squeeze = 0.00
bb_squeeze := bb_spread / avgspread * 100
bb_zone = bb_squeeze < 53 ? 0 : 
 bb_squeeze < sqz_length ? 1 : 
 bb_squeeze < 120 ? 2 :
 bb_squeeze < 160 ? 3 :
 bb_squeeze > 160 ? 4 :
 bb_squeeze > 200 ? 5 : na
sqz_color = bb_zone == 0 ? #0045b3 :
 bb_zone == 1 ? #ff0062 : 
 bb_zone == 2 ?  gray : 
 bb_zone == 3 ?  #00c3ff : 
 bb_zone == 4 ? white:
 bb_zone == 5 ? yellow: na

bb_zones_color =  sqz_color

// === BB 2 ===
[basis2] = bb_smoothing(BB_length2,bb_smooth_type)
dev2 = BB_stdDev * stdev(close, BB_length2)
bb_upper2 = basis2 + dev2
bb_lower2 = basis2 - dev2
bb_spread2 = bb_upper2 - bb_lower2
avgspread2 = sma(bb_spread2, sqz_length)
bb_squeeze2 = 0.00
bb_squeeze2 := bb_spread2 / avgspread2 * 100
bb_smooth2 = sma((basis2 + dev2), bb_smooth_input)

bb_zone2 = bb_squeeze2 < 53 ? 0 : 
 bb_squeeze2 < sqz_length ? 1 : 
 bb_squeeze2 < 120 ? 2 :
 bb_squeeze2 < 160 ? 3 :
 bb_squeeze2 > 160 ? 4 : na
sqz_color2 = bb_zone2 == 0 ? #0045b3 :
 bb_zone2 == 1 ? #ff0062 : 
 bb_zone2 == 2 ?  gray : 
 bb_zone2 == 3 ?  #00c3ff : 
 bb_zone2 == 4 ? white: na

bb_zones_color2 =  sqz_color2

basis_angle = angle(basis,3)
basis_angle2 = angle(basis2,3)
// BB Diff Angles
bb_diff = (bb_upper - bb_lower) * 1000
bb_diff2 = (bb_upper2 - bb_lower2) * 1000
bb_diff_a = angle(bb_diff,3)
bb_diff_a2 = angle(bb_diff2,3)


// plot(show_bb?basis:na, title="Basis", color=bb_zones_color,linewidth=3)
p1 = plot(show_bb and use_bb_curr1 ?bb_upper:na, "BB Upper ",color=bb_zones_color,linewidth=2)
p2 = plot(show_bb and use_bb_curr1 ?bb_lower:na, "BB Lower ",color=bb_zones_color,linewidth=2)
p3 = plot(show_bb and use_bb_curr2 ?bb_upper2:na, "BB Upper 2 ",color=bb_zones_color2,linewidth=2)
p4 = plot(show_bb and use_bb_curr2 ?bb_lower2:na, "BB Lower 2 ",color=bb_zones_color2,linewidth=2)


// Temp plots
// plot(show_bb?bb_smooth2:na, "BB Smooth 2 ",color=bb_zones_color2,linewidth=2)
// plot(show_bb?bb_upper2:na, "BB Upper 2 ",color=bb_zones_color2,linewidth=2)
// plot(show_bb?bb_lower2:na, "BB Lower 2 ",color=bb_zones_color2,linewidth=2)


// === BB Multi ===
bb_basis_m  = security(syminfo.tickerid, bb_time, sma(close, bb_length_multi) )
dev_m = security(syminfo.tickerid, bb_time, 2 * stdev(close, bb_length_multi) ) 
bb_upper_m = security(syminfo.tickerid, bb_time, bb_basis_m + dev_m )
bb_lower_m = security(syminfo.tickerid, bb_time, bb_basis_m - dev_m ) 
//bb_upper_m  = security(syminfo.tickerid, bb_time, bb_upper)
//bb_lower_m  = security(syminfo.tickerid, bb_time, bb_lower)


bb_spread_m = security(syminfo.tickerid, bb_time, (bb_upper_m - bb_lower_m) ) 
avgspread_m = security(syminfo.tickerid, bb_time, sma(bb_spread_m, sqz_length) )  
bb_sqz_m = security(syminfo.tickerid, bb_time, ( bb_spread_m / avgspread_m * 100) ) 
bb_diff_m = security(syminfo.tickerid, bb_time, bb_spread_m * 1000 ) 
bb_diff_a_m = security(syminfo.tickerid, bb_time, angle(bb_diff_m,3) ) 

bb_zone_m = bb_sqz_m < 53 ? 0 : 
 bb_sqz_m < sqz_length ? 1 : 
 bb_sqz_m < 120 ? 2 :
 bb_sqz_m < 160 ? 3 :
 bb_sqz_m < 200 ? 4 :
 bb_sqz_m < 250 ? 5 :
 bb_sqz_m > 250 ? 6 : na
sqz_color_m = bb_zone_m == 0 ? #0045b3 :
 bb_zone_m == 1 ? #ff0062 : 
 bb_zone_m == 2 ?  gray : 
 bb_zone_m == 3 ?  #00c3ff : 
 bb_zone_m == 4 ? white:
 bb_zone_m == 5 ? yellow:
 bb_zone_m == 6 ? white: na

bb_zones_color_m =  sqz_color_m

// Special s4 distance to bb
s4_bb_up = (bb_upper2 - s4_m) * 100
s4_bb_down = (s4_m - bb_lower2) * 100

//plot(s4_bb_up, "S4 BB Dist up", color=color.new(blue,100))
//plot(s4_bb_down, "S4 BB Dist Down", color=color.new(blue,100))

//plot(show_bb and use_bb_multi?bb_basis_m:na, "BB Basis Multi ",color=bb_zones_color_m)
plot(show_bb and use_bb_multi?bb_upper_m:na, "BB Upper Multi ",color=bb_zones_color_m)
plot(show_bb and use_bb_multi?bb_lower_m:na, "BB Lower Multi ",color=bb_zones_color_m)




g_atr = "ATR -------------------------------------------------------------"
show_atr = false //input(title="Show ATR", type=input.bool, defval=false)
atrlen = 14  // input(14, "ATR Period")
atr_mult = 1 // input(1, "ATR Mult", step = 0.1) // 1.35 1.15
atr_stop = 0.0003 //input(0.0003, "ATR Stop", step = 0.0001) // 0.001
atr_slen = wma(tr(true), atrlen)
atr_upper = atr_slen * atr_mult + close
atr_lower = close - atr_slen * atr_mult
sl_upper = atr_slen * 2 + close
sl_lower = close - atr_slen * 2

// atr_new_len = 14
// atr_smoothing = input(title="Smoothing", defval="RMA", options=["RMA", "SMA", "EMA", "WMA"])
// atr_new = ta.rma(ta.tr(true), atr_new_len)
//plot(atr(14) * 10000, title = "ATR", color=color.new(#B71C1C, 100))

// ATR
// plot(show_atr ? atr_upper : na, "+ATR Upper", color=show_atr?color.new(#ffffff,70):color.new(#ffffff,100))
// plot(show_atr ? atr_lower : na, "-ATR Lower", color=show_atr?color.new(#ffffff,70):color.new(#ffffff,100))
// atr_pips = close>open? abs(close - atr_upper) : abs(close - atr_lower)
// plot(atr_pips, title="ATR", color=color.new(blue,100))






g_ema= "EMA's -------------------------------------------------------------"
inl_ema= "inl_ema"
inl_ema1 = "inl_ema1"
show_ema = input(title="Show EMA 200", type=input.bool, defval=true,group=g_ema)
show_zones = input(title="Show Zones", type=input.bool, defval=true,group=g_ema)
ea_input = input(title="E Bias", type=input.float, defval=1,step=0.01,group=g_ema,inline=inl_ema) 
e200_input = input(title="EMA 200",type=input.integer, defval=2200,group=g_ema,inline=inl_ema) // 200
ez_candles = input(title="EZ Candles", type=input.bool, defval=true,inline=inl_ema,group=g_ema)
g_ema_time = ""
inl_ema2 = "inl_ema2"
use_ema_curr = input(title="Show Current", type=input.bool, defval=false,inline=inl_ema2,group=g_ema_time)
use_ema_multi = input(title="Show Multi", type=input.bool, defval=true,inline=inl_ema2,group=g_ema_time)
ema_time = input(title="Timeframe", type=input.resolution, defval="30", group=g_ema_time)


//ea_sc = e200_a<5 ? color.new(orange,50): e200_a>5 ? color.new(#ff0000,50) : na
//ea_bc = e200_a>-5 ? color.new(#0000ff,50): e200_a<-5 ? color.new(#00ff00,50) : na

// e_low
e_low = ema(close, 8)
e_low_a = angle(e_low,3)
e_low_m  = security(syminfo.tickerid, ema_time, e_low)
// e20
e20 = ema(close, 20)
e20_a = angle(e20,3)
e20_m  = security(syminfo.tickerid, ema_time, e20)
// e50
e50 = ema(close, 50)
e50_a = angle(e50,3)
e50_m  = security(syminfo.tickerid, ema_time, e50)
e50_a_m  = security(syminfo.tickerid, ema_time, e50_a)
// e200
e200 = ema(close, 200)
e200_a = angle(e200,3)
ea_zone = abs(e200_a)
e200_m    = security(syminfo.tickerid, ema_time, e200)
e200_a_m  = security(syminfo.tickerid, ema_time, e200_a)
// E Dist
//e_dist = (e50 - e20) * 10000
// plot(e_dist,title="E Dist",color=e50>e20?color.new(red,100):color.new(green,100),style=plot.style_circles)

e_zones = ea_zone < ea_input ? 0 : 
 ea_zone < 2 ? 1 : 
 ea_zone < 3 ? 2 :
 ea_zone < 4 ? 3 :
 ea_zone < 5 ? 4 :
 ea_zone < 6 ? 5 :
 ea_zone > 6 ? 6 : na
e_color = e_zones == 0 ? purple :
 e_zones == 1 ? magenta : 
 e_zones == 2 ?  gray : 
 e_zones == 3 ?  #00c3ff : 
 e_zones == 4 ? white:
 e_zones == 5 ? yellow:
 e_zones == 6 ? red: na

// plot(show_ema and use_ema_curr?e200:na, title="EMA 200", color=show_zones ? e_color : s8_a_m>0 ? red : lime,linewidth=2)
// plot(show_ema and e200_a?e200_a:na, color=c_hide, title="EMA Angle",style=plot.style_circles)


// Multi
//ea_zone_m   = security(syminfo.tickerid, ema_time, ea_zone)
//ea_zone_m = abs(e200_a_m)
ea_zone_m = abs(e50_a_m)
e_zones_m = ea_zone_m < ea_input ? 0 : 
 ea_zone_m < 2 ? 1 : 
 ea_zone_m < 3 ? 2 :
 ea_zone_m < 4 ? 3 :
 ea_zone_m < 5 ? 4 :
 ea_zone_m < 6 ? 5 :
 ea_zone_m > 6 ? 6 : na
e_color_m = e_zones_m == 0 ? red :
 e_zones_m == 1 ? orange : 
 e_zones_m == 2 ? yellow : 
 e_zones_m == 3 ? gray : 
 e_zones_m == 4 ? blue:
 e_zones_m == 5 ? lime:
 e_zones_m == 6 ? white: na

// Plot
plot(show_ema and use_ema_multi?e_low_m:na, title="EMA Low Multi", color=white)
plot(show_ema and use_ema_multi?e20_m:na, title="EMA 20 Multi", color=blue)
plot(show_ema and use_ema_multi?e50_m:na, title="EMA 50 Multi", color=magenta)
plot(show_ema and use_ema_multi?e200_m:na, title="EMA 200 Multi", color=show_zones ? e_color_m : s8_a_m>0 ? red : lime )


g_ch = "SSL Channels -------------------------------------------------------------"
inl_ch = "inl_ch"
show_ch = input(title="SSL Channels",type=input.bool,defval=false,inline=inl_ch,group=g_ch)
ch_len=input(title="Period", defval=100,inline=inl_ch,group=g_ch) // 100, 60, 25
f_trans = 85
channel_line_width = 1
ch_fill = true
ch1_angles = false // input(title="Color based on angle",type=input.bool,defval=false,inline=inl_ch,group=g_ch)

g_ch_time = ""
use_ch_curr = input(title="Show Current", type=input.bool, defval=true,inline=inl_ch,group=g_ch_time)
use_ch_multi = input(title="Show Multi", type=input.bool, defval=true,inline=inl_ch,group=g_ch_time)
ch_time = input(title="Timeframe", type=input.resolution, defval="15", group=g_ch_time)

// ch2_input = 40 
// ch3_input = 13 

c1_a = lime 
c1_b = red
c2_a = lime
c2_b = red

ssl_ch1() => 
    smaHigh=sma(high, ch_len)
    smaLow=sma(low, ch_len)
    ch_Hlv = close>smaHigh ? 1 : -1
    //ch_Hlv := close>smaHigh ? 1 : close<smaLow ? -1 : ch_Hlv[1]
    ch1_d = ch_Hlv < 0 ? smaHigh: smaLow
    ch1_u   = ch_Hlv < 0 ? smaLow : smaHigh
    ch1_mid = (ch1_d + ch1_u) * 0.5
    ch1_a = angle(ch1_mid,3)
    [ch1_d,ch1_u,ch1_mid,ch1_a]

[ch1_d,ch1_u,ch1_mid,ch1_a] = ssl_ch1()

// Channel Conditions
ch1_color = ch1_d>ch1_u ? color.new(#33ae42,50) : color.new(#ff350e,50)

// Color
color1 = ch_len<25?c2_a : c1_a
color2 = ch_len<25?c2_b : c1_b
f_c1 = ch1_d>ch1_u and ch_fill?color.new(color1,f_trans):ch1_u>ch1_d and ch_fill?color.new(color2,f_trans):na

// Plot
ch_sdplot= plot(show_ch and use_ch_curr ? ch1_d:na, title="SSL Down ",linewidth=channel_line_width) //color=color.new(color1,50))
ch_suplot=plot(show_ch and use_ch_curr ? ch1_u:na, title="SSL Up",linewidth=channel_line_width) //color=color.new(color2,50))
plot(show_ch and use_ch_curr ? ch1_mid:na, title="Mid Point",color=color.new(color.white,80))
plot(show_ch and use_ch_curr ? ch1_a : na, title="Ch Angle", color=color.new(color.white,100))
fill(ch_sdplot,ch_suplot, color= f_c1)

// Multi
ch1_d_m = security(syminfo.tickerid, ch_time, ch1_d)
ch1_u_m = security(syminfo.tickerid, ch_time, ch1_u)
ch1_mid_m = security(syminfo.tickerid, ch_time, ch1_mid)
ch1_a_m = security(syminfo.tickerid, ch_time, ch1_a)

//ch_sdplot_m = plot(show_ch and use_ch_multi ? ch1_d_m : na, title="SSL Down Multi", linewidth=channel_line_width, color=color.new(color1,50))
//ch_suplot_m = plot(show_ch and use_ch_multi ? ch1_u_m : na, title="SSL Up Multi", linewidth=channel_line_width, color=color.new(color2,50))
//plot(show_ch and use_ch_multi ? ch1_mid_m : na, title="Mid Point Multi",color=color.new(color.white,80))
//plot(show_ch and use_ch_multi ? ch1_a_m : na, title="Angle Multi", color=color.new(color.white,100))

f_c1_m = ch1_d_m>ch1_u_m and ch_fill?color.new(color1,f_trans):ch1_u_m>ch1_d_m and ch_fill?color.new(color2,f_trans):na

f_c2 = 
 ch1_a>0 and ch1_a<1 ? color.new(gray,f_trans) 
 : ch1_a>1 and ch1_a<2 ? color.new(yellow,f_trans) 
 : ch1_a>2 and ch1_a<4 ? color.new(orange,f_trans) 
 : ch1_a>4 and ch1_a<20 ? color.new(red,f_trans) : na

f_c3 = 
 ch1_a<0 and ch1_a>-1 ? color.new(gray,f_trans) 
 : ch1_a<-1 and ch1_a>-2 ? color.new(aqua,f_trans) 
 : ch1_a<-2 and ch1_a>-4 ? color.new(green,f_trans) 
 : ch1_a<-4 and ch1_a<20 ? color.new(lime,f_trans) : na

ch_color_m = ch1_angles==false ? f_c1_m : ch1_a>0 ? f_c2 : f_c3
//fill(ch_sdplot_m,ch_suplot_m, color= ch_color_m)



g_macd = "MACD - Chris Moody -----------------------------------------------------"
inl_macd1 = "inl_macd1"
show_macd = input(title="Show MACD", type=input.bool, defval=false,inline=inl_macd1,group=g_macd)

g_macd_time = ""
inl_macd2 = "inl_macd1"
use_macd_curr = input(title="Show Current", type=input.bool, defval=false,inline=inl_macd2,group=g_macd_time)
use_macd_multi = input(title="Show Multi", type=input.bool, defval=true,inline=inl_macd2,group=g_macd_time)
macd_time = input(title="Timeframe", type=input.resolution, defval="5", group=g_macd_time)

cm_fast_len = 12 //input(12, minval=1), 
cm_slow_len = 26 //input(26,minval=1)
ch_sig_len  = 9 //input(9,minval=1)

cm_fastMA   = ema(close, cm_fast_len)
cm_slowMA   = ema(close, cm_slow_len)
cm_macd     = cm_fastMA - cm_slowMA
cm_signal   = sma(cm_macd, ch_sig_len)
cm_hist     = cm_macd - cm_signal

cm_macd_m = security(syminfo.tickerid, macd_time, cm_macd)
cm_sig_m = security(syminfo.tickerid, macd_time, cm_signal)
cm_hist_m = security(syminfo.tickerid, macd_time, cm_hist)

// p_color =  cm_hist>0 and open<close ? red : cm_hist<0 and close<open ? lime : gray
// barcolor(show_macd and use_macd_curr ? p_color : na)

p_color_m = cm_macd_m>cm_sig_m ? green : red


// === STOCH ===
// ==================================================

a = input(10, "Percent K Length")
b = input(2, "Percent D Length")
ob = input(40, "Overbought")
os = input(-40, "Oversold")
smooth = input(1, "Smoothing")
stoch_time = input(title="Stochastic Timeframe", type=input.resolution, defval="15")

stoch() =>
    // Range Calculation
    ll = lowest (low, a)
    hh = highest (high, a)
    diff = hh - ll
    rdiff = close - (hh+ll)/2

    avgrel = ema(ema(rdiff,b),b)
    avgdiff = ema(ema(diff,b),b)
    // SMI calculations
    SMI = avgdiff != 0 ? (avgrel/(avgdiff/2)*100) : 0

stoch_m = security(syminfo.tickerid, stoch_time, stoch() )
stoch_SMI = security(syminfo.tickerid, stoch_time, ema(stoch_m,b) ) 
stoch_EMA = security(syminfo.tickerid, stoch_time, ema(stoch_m, 10) )  

// === WAE ===
// ==================================================
sensitivity = 150
wae_fast= 20 //8
wae_slow= 40 // 50
wae_ch_len = 10 // 20 45
wae_mult = 2 // 1.75 2 1.85
wae_dz = nz(rma(tr(true),100)) * 3.7
calc_macd(source, wae_fast, wae_slow) =>
	a = ema(source, wae_fast)
	b = ema(source, wae_slow)
	a - b
calc_BBUpper(source, length, wae_mult) => 
	wae_basis = wma(source, length)
	wae_dev = wae_mult * stdev(source, length)
	t = wae_basis + wae_dev
	[t]
calc_BBLower(source, length, wae_mult) => 
	wae_basis = wma(source, length)
	wae_dev = wae_mult * stdev(source, length)
	t = wae_basis - wae_dev
	[t]

t1 = (calc_macd(close, wae_fast, wae_slow) - calc_macd(close[1], wae_fast, wae_slow))*sensitivity
t2 = (calc_macd(close[2], wae_fast, wae_slow) - calc_macd(close[3], wae_fast, wae_slow))*sensitivity 
[e1a] = calc_BBUpper(close, wae_ch_len , wae_mult)
[e1b] = calc_BBLower(close, wae_ch_len , wae_mult)
wae_line = (e1a - e1b)
wae_diff = (wae_line - wae_dz) * 100
//wae_perc = (wae_line / wae_dz) * 100
trendUp = (t1 >= 0) ? t1 : 0
trendDown = (t1 < 0) ? (-1*t1) : 0
wae_color = #000000
if t1 >= 0
	wae_color := trendUp<trendUp[1] ? lime : green
if t1 < 0
	wae_color := trendDown<trendDown[1] ? orange : red
wae_a = angle(wae_line / 10, 14)
// plot(trendUp,title="WAE Up",color=color.new(wae_color,100))
// plot(trendDown,title="WAE Down",color=color.new(wae_color,100))
// plot(wae_a,title="WAE angle",color=wae_a>5?color.new(red,100):color.new(green,100))


// === ADX + DI with SMA ===
// ==================================================
adx_len = 9 // input(9,title="ADX len") // 14
adx_line = 20 // input(title="threshold", defval=20)
adx_avg = 8 // input(8,title="ADX SMA") // 10
var float adx_top = 54 // input(55,title="High")
var float adx_high = 38 // 39.5
var float adx_mid = 33
var float adx_center = 20
var float adx_low = 12
var float smooth_tr = 0
var float smooth_di_plus = 0
var float smooth_di_minus = 0
TrueRange = max(max(high-low, abs(high-nz(close[1]))), abs(low-nz(close[1])))
DI_plus = high-nz(high[1]) > nz(low[1])-low ? max(high-nz(high[1]), 0): 0
DI_minus = nz(low[1])-low > high-nz(high[1]) ? max(nz(low[1])-low, 0): 0
smooth_tr := nz(smooth_tr[1]) - (nz(smooth_tr[1])/adx_len) + TrueRange
smooth_di_plus := nz(smooth_di_plus[1]) - (nz(smooth_di_plus[1])/adx_len) + DI_plus
smooth_di_minus := nz(smooth_di_minus[1]) - (nz(smooth_di_minus[1])/adx_len) + DI_minus

di_sell = smooth_di_plus / smooth_tr * 100
di_buy = smooth_di_minus / smooth_tr * 100
DX = abs(di_sell-di_buy) / (di_sell+di_buy)*100
adx = sma(DX, adx_len)
adx_sma = sma(adx, adx_avg)
adx_angle = (angle(adx,2))
//hline(adx_line, color=black, linestyle=dashed)




g_res = "RES - Ranging EMA Spread ---------------------------------------------------"
inl_res1 = "inl_res1"
ema1length = 12 // 40 12
ema2length = 50 // 100 43
ranginglength = 3
rangingmaxvalue = 0.12// 0.14 0.1
rangingminvalue = -0.1
show_res = input(title="Show RES", type=input.bool, defval=false,group=g_res) 

// Rangin EMA spread
ema1 = ema(close, ema1length)
ema2 = ema(close, ema2length)
spread = ((ema2 / ema1) -1) * 100
r1 = (spread >= rangingminvalue and spread <= rangingmaxvalue) 
r2 = (spread[1] >= rangingminvalue and spread[1] <= rangingmaxvalue)
r3 = (spread[2] >= rangingminvalue and spread[2] <= rangingmaxvalue)
r4 = (spread[3] >= rangingminvalue and spread[3] <= rangingmaxvalue)
r5 = (spread[4] >= rangingminvalue and spread[4] <= rangingmaxvalue)

ranging = false
if (ranginglength == 1) 
    ranging := r1
if (ranginglength == 2) 
    ranging := r1 and r2
if (ranginglength == 3) 
    ranging := r1 and r2 and r3
if (ranginglength == 4) 
    ranging := r1 and r2 and r3 and r4
if (ranginglength == 5) 
    ranging := r1 and r2 and r3 and r4 and r5

res = spread > 0 ? 0-spread : abs(spread)
res := res * 100
res_weak = gray // grey
res_mid = d_blue // navy
res_strong = aqua // aqua
res_color = ranging ? res_weak : spread > spread[1] ? res_mid : res_strong
// Targets
res_top = 15
res_high = 6
res_low = -6
res_bottom = -15

//plot(show_res and res? res : na, title="RES", style=plot.style_circles, color=res_color)



g_aroon = "AROON -------------------------------------------------------------"
aroon_len = input(14, title="Aroon Length") // 9
arn_u = 100 * (highestbars(high, aroon_len+1) + aroon_len)/aroon_len
arn_l = 100 * (lowestbars(low, aroon_len+1) + aroon_len)/aroon_len
//plot(arn_u, "Aroon Up", color=#FB8C00)
//plot(arn_l, "Aroon Down", color=#2962FF)
//plotshape(arn_u, "Aroon Up", color=#FB8C00), style=shape.circle,location=location.top


g_tdfi = "TDFI -------------------------------------------------------------"
td_lb = input(26, title = "Lookback") // 20 10 25
td_fh = input(0.25, title = "Filter", step=0.01) // 0.13 0.05
td_max = input(0.9, title = "Max Line", step=0.1) // 0.8
td_fl =  td_fh * -1 //input(-0.05, title = "Filter Low") 
price = input(close, "Period")


mma = ema(price * 1000, td_lb)
smma = ema(mma, td_lb)

impetmma = mma - mma[1]
impetsmma= smma - smma[1]
divma = abs(mma - smma)
averimpet = (impetmma + impetsmma) / 2

number = averimpet
var int pow = 3 
var float result = na

for i = 1 to pow - 1
    if i == 1
        result := number
    result := result * number

tdf = divma * result
ntdf = tdf / highest(abs(tdf), td_lb * 3)

c = ntdf > td_fh ? #008000 : ntdf < td_fl ? #ff0000 : #505050
plot(ntdf, linewidth = 2, color = c)



// === Plot === 
// ==================================================
//plot(perc_change(close), title='Change',color=color.new(red,100),style=plot.style_circles)


var bool enter_exit = false
//var int trade_dir = 0
var int state = 0
var int bar_num = 0
var float lastPrice = 0.0
var mult_diff = 10000
var int num_pips = 20
var int num_bars = 4


// Up Trend
// If price is higher than s8_m and s7_a_m>0
// while s8_m<e200_m the counter trades
// are safer to take?
t_up = s7>e200_m and not(low<e200_m and s8_m>e200_m) // s8_a_m>0 and low>e200_m and 
t_down = t_up==false ? true : false
trending_up = s8_a_m>0
trending_down = trending_up==false
//trending_up = mg2_m and s7_a_m>0 and s7_m<e200_m and not(s7_s8_conv_m)
//trending_down = t_up and mr2_m and s8_a_m<0 and s7_m>e200_m and not(s7_s8_conv_m)
//s7_s8_conv_m 

barcolor(bar_ema ? e_color:na, title="E zone color")
barcolor(bar_macd ? p_color_m : na, title="MACD")
barcolor(bar_res ? res_color : na, title="RES")
//barcolor(bb_candles1 and bb_diff_a>bb_bias ? aqua:na, title="Candles 1")
//barcolor(bb_candles2 and bb_diff_a2>bb_bias2 ? yellow:na, title="Candles 2")
//barcolor(bb_candles_m and bb_diff_a_m>bb_bias_m ? yellow:na, title="Candles Multi")

//barcolor(bar_bb_zone and bb_zone<1 and low<bb_lower ? white:na, title="BB Zone")
// Trend
//barcolor(bar_trend_up and t_up ? purple : na, title="Trend Up")
//barcolor(bar_trend_down and t_down ? aqua : na, title="Trend Down")
//barcolor(bar_trending_up and trending_up ? white : na, title="Trending Up")
//barcolor(bar_trending_down and trending_down ? white : na, title="Trending Down")
// SSL
conv_color = s7>s8 ? magenta : lime 
conv_color_m= s7_m>s8_m ? magenta : lime 
// s5_s6_conv_m and s5_m>s6_m
// s7_s8_conv_m and s7_m>s8_m

col_56 = mr and mo==false ? red :
 mr ? #6f0707 : 
 mg and mo==false ? lime :
 mg ? #026506 : na
col_78 = mr2 and mo2==false? red :
 mr2 ? #6f0707 : 
 mg2 and mo2==false ? lime :
 mg2 ? #026506 : na

col_56_m = mr_m and mo_m==false ? red :
 mr_m ? #6f0707 : 
 mg_m and mo_m==false ? lime :
 mg_m ? #026506 : na
col_78_m = mr2_m and mo2_m==false ? red :
 mr2_m ? #6f0707 : 
 mg2_m and mo2_m==false ? lime :
 mg2_m ? #026506 : na

// barcolor(bar_s1 ? col_56 : na, title="S5/S6")
// barcolor(bar_s2 ? col_78 : na, title="S7/S8")
// barcolor(bar_s1_m ? col_56_m : na, title="Multi S5/S6")
// barcolor(bar_s2_m ? col_78_m : na, title="Multi S7/S8")

// close_color = change(close_m) ? lime : purple
// barcolor(bar_close ? close_color : na, title="Candle Close")

//barcolor(stoch_EMA > 40 ? blue : stoch_EMA < -40 ? yellow : na, title="Stoch Up")


//barcolor(bar_conv and s7_s8_conv ? conv_color : na, title="Conv")
//barcolor(bar_conv_m and s7_s8_conv_m ? conv_color_m : na, title="Conv Multi")

// Days of the week
//bgcolor(dayofweek == day_of_week and bar_days ? day_colors : na)
//plotshape(change(close_m),title="Close Multi",color=#ff0000,style=shape.triangleup,location=location.top)

//barcolor(bar_trend and trending_down ? white : na)
entry_signal() =>
	candle = close > open ? 1 : 0
    dir = 0
    counter = 0
    cond = ''
    allow = false
    ea = e200_a
    ea_m = e200_a_m
    ba = basis_angle
    ba2 = basis_angle2
    ea_cnt = e200_a<5?true:false
    cm_color = cm_macd_m>cm_sig_m ? green : red 

    // Hull
    hull_sell = s1_m>s2_m and s2_m>s3_m and s2_m>s5_m and s6_m>s8_m?true:false

    // S5 / S6 cross
    cross_red = s5>s6?true:false
    cross_green = s5<s6?true:false

    adx_sell = adx_sma>di_sell and adx>adx_sma and di_sell>di_buy?true:false
    adx_buy = adx>adx_sma and adx>adx_mid and adx>di_buy and di_buy>di_buy[1]?true:false

    wae_sell = wae_line>wae_dz and wae_color==green
    wae_buy = wae_line>wae_dz and wae_color==red

    tdfi = ntdf>0.9 ? 1 : ntdf<-0.9 ? -1 : 0

    ma_2 = security(syminfo.tickerid, "180", s2)
    ma_2a = angle(ma_2,3)
    ma_3 = security(syminfo.tickerid, "180", s3)

    // // Normal
    // mr and mo==false
    // mr
    // mg and mo==false
    // mg 
    // mr2 and mo2==false
    // mr2 
    // mg2 and mo2==false
    // mg2

    // // Multi
    // mr_m and mo_m==false
    // mr_m
    // mg_m and mo_m==false
    // mg_m

    // mr2_m and mo2_m==false
    // mr2_m
    // mg2_m and mo2_m==false
    // mg2_m



    // === SELL Exit ===
    if s8_a_m>0

        if mr_m and close>s5_m and close>s2_m and s1_m>s2_m and 
         (s5_a_m>0 and s2_m>s5_m)
         and not(mo_m==false)
            dir := -1
            cond := "s2"
            counter:= 0

        // ADX sell
        // if candle==1 and adx_sell
        //  and not(mg_m and mo_m==true)
        //  and not(s2_m<s3_m)
        //  and not(tdfi<1)
        //     dir := -1
        //     cond := "ex\nadx"
        //     counter:= 0

        // 10 min
        // if candle==0 and hull_sell and perc_change()<4
        //  and not(mg_m and s6_a_m<0)
        //  and not(mg_m and s2_m<s6_m)
        //  and not(ma_3<)
        //     dir := -1
        //     cond := "ex\nred"
        //     counter:= 0

        // Red - Strongest
        // if candle==1 and close>s5_m and mr_m and mo_m==true
        //  and not(bb_basis_m<s8_m)
        //  and not(s1_m<s5_m)
        //  and not(s1_m<s2_m)
        //  and not(s2_m<s3_m)
        //  and not(s3_m<s5_m)
        //  and not(di_sell<adx_high)
        //     dir := -1
        //     cond := "ex\ns5"
        //     counter:= 0

        // S3 at the top strong trend weakening
        //if candle==1 and s6_m>s8_m

        // Weakening strong trend
        // if mr_m and close>basis and bb_zone<3 and  bb_upper>bb_upper2 and
        //  bb_zone_m>3
        //  and not(s6_m<s8_m)
        //  and not(mr_m and mo_m==false)
        //     dir := -1
        //     cond := "en\nbb1"
        //     counter:= 1

    // === SELL Entry ===
    if s8_a_m>0

        if candle==0 and bb_zone_m<2 and mr_m and s2_m<bb_basis_m
         and not(low>bb_lower_m or close>bb_lower)
         and not(s1_m>s2_m)
            dir := 1
            cond := "en\nz1"

        // Red below S8
        // if candle==0 and close<s8_m and s2_m<s8_m
        //  and not(s6_m<s8_m)
        //  and not(bb_zone_m>2)
        //  and not(open>s2_m)
        //     dir := 1
        //     cond := "en\ns8-bb"

        // Red waning
        if candle==1 and mr_m and tdfi==1 and s5_a_m<0 and close>s5_m
         and not(s6_m<s8_m)
            dir := -1
            cond := "en\ns5"

        // Green above S8
        // if candle==1 and close>s5_m and mg_m and s5_a_m<0 and tdfi==1
        //  and not(s5_m<s8_m)
        //     dir := -1
        //     cond := "en\ns4"

        if candle==0 and mr_m and mo_m==true and
         low<e20_m and bb_zone<2
         and not(s1_m>s2_m or s2_m>s3_m)
         and not(bb_zone_m==0 and close>bb_lower_m)
            dir := 1
            cond := "e20"

        if candle==0 and s5_a_m>0 and close<s3_m and s6_a_m<0
         and not(high>bb_basis_m)
         and not(s2_a_m<2)
            dir := 1
            cond := "en\ns3"

        if candle==0 and close<bb_lower2 and tdfi==-1
            dir := 1
            cond := "en\nbb"


        // if ma_3>ma_2
        //     dir := 0

   
    // === BUY Exit  ===
    // if s8_a_m<0

    // === BUY Entry ===
    if s8_a_m<0

        if candle==1 and mg_m and mo_m==true and 
         close<s2_m and s2_a<10
         and not(s5_a_m>0)
            dir := -1
            cond := "en\ns2"

        if candle==0 and mg_m and mo_m==true and s2_a_m>5 and 
         close<s2_m and close<s6_m and high<bb_basis_m
            dir := 1
            cond := "en\ns2"


    // if candle==1
    //     // rsc_w,rsc_m,rsc_s,rbc_w,rbc_m,rbc_s,rsc_close,rsc_high,rsc_low
    //     if s7_a<0 and (rsc_close>70)
    //         dir := -1
    //         cond := "r"



    // Filter out same direction trades, wait 4 candles
    // and price higher than 20 pips before allowing another trade
    if dir == state and use_logic and counter==0
        allow := (bar_index - bar_num) >= num_bars ? true : false
        // sell
        if state == -1
            dir := ((close - lastPrice) * mult_diff) > num_pips and allow ? dir : 0
        // buy
        if state == 1
            dir := ((close - lastPrice) * mult_diff) < (num_pips * -1) and allow ? dir : 0

    // Do not trade during New York close
    if ny_close==1
        cond := cond  + '-NY'

    if ny_close==1 and use_ny_filter
        dir := 0



    //var int enter_exit = basis_angle > 0 and dir == -1 ? 0 : basis_angle < 0 and dir
    // if counter==1 and state==dir
    //     dir := 0
        
    type = dir > 0 ? 1 : dir < 0 ? -1 : 0
    [type,counter,cond]


[trade_dir,counter,condition] = entry_signal() 



if trade_dir != 0 and use_logic
    state := trade_dir

    enter_exit := counter == 1 ? 1 : 0
    // Sell
    // if basis_angle > 0 and trade_dir == -1
    //     enter_exit := -1
    // // Counter
    // if basis_angle > 0 and trade_dir == 1
    //     enter_exit := 0
    // // Buy
    // if basis_angle < 0 and trade_dir == 1
    //     enter_exit := -1
    // // Counter
    // if basis_angle < 0 and trade_dir == -1
    //     enter_exit := 0
    // if basis>e200
    //     enter_exit := trade_dir == -1 ? false : trade_dir == 1 or counter ? true : false
    // if basis<e200
    //     enter_exit := trade_dir == 1 ? false : trade_dir == -1 or counter ? true : false
    //enter_exit := counter == 1 ? true : enter_exit == false ? true : false
    lastPrice := close
    bar_num   := bar_index

// if enter_exit != trade_dir and trade_dir != 0
//     enter_exit := enter_exit == false ? true : false
labelText = tostring(condition)
trade_color = trade_dir > 0  ? buy_color : sell_color
if trade_dir != 0 and show_trades // and show_cond
	if trade_dir > 0
        buy = label.new(x=time,y=high,text=labelText,xloc=xloc.bar_time, yloc=yloc.belowbar,color=trade_color,textcolor=color.white, size=size.normal,style=label.style_label_up)
    else
        sell = label.new(x=time,y=high,text=labelText,xloc=xloc.bar_time, yloc=yloc.abovebar,color=trade_color,textcolor=color.white, size=size.normal)

//plot(counter,title="Counter Trade", style=plot.style_circles)
//plot(state,"State",style=plot.style_circles)

// plotshape(show_entry and trade_dir == 1 and enter_exit == 0 ? 1 : na, title="Entry Buy", color=trade_color, location = location.belowbar, style=shape.labelup, text="B", textcolor=color.white, size=size.small)
// plotshape(show_entry and trade_dir == 1 and enter_exit == -1 ? 1 : na, title="Counter Buy", color=trade_color, location = location.belowbar, style=shape.labelup, text="Exit", textcolor=color.white, size=size.small)
// plotshape(show_entry and trade_dir == -1 and enter_exit == 0 ? -1: na, title="Entry Sell", color=trade_color, location = location.abovebar, style=shape.labeldown, text="S", textcolor=color.white, size=size.small)
// plotshape(show_entry and trade_dir == -1 and enter_exit == -1 ? -1 : na, title="Exit Sell", color=trade_color, location = location.abovebar, style=shape.labeldown, text="Exit", textcolor=color.white, size=size.small)
