//@version=5
strategy('TB - Backtesting Lookahead 5 min - Stoch',
 overlay=true,
 precision=4,
 currency=currency.USD,
 initial_capital=10000,
 default_qty_value=10,
 commission_type=strategy.commission.percent,
 commission_value = 0.004,
 process_orders_on_close=true,
 pyramiding=1,
 max_labels_count=500)

red = #ff0062
orange = #ff9800
yellow = #FFFF00
green = #4caf50
lime = #50e600
aqua = #00bcd4
blue = #2962ff
dark_blue = #2962ff
violet = #814dff
white = #ffffff
gray = #707070
black = #000000

// Angle
angle(_src, len) =>
    rad2degree = 180 / 3.14159265359  //pi 
    ang = rad2degree * math.atan((_src[0] - _src[1]) / ta.atr(len))
    ang

// Change
perc_change() =>
    perc = math.abs((1 - close[1] / close) * 10000)
    perc

get_point_value(point1, point2, abs_value) =>
    float diff_points = 0.0
    float point_value = 0.0
    diff_points := abs_value ? math.abs( (point1 - point2) ) : point1 - point2
    point_value := diff_points / syminfo.mintick / 10

is_between(p1, p2) =>
    is_inside = p1>p2 ? (close<p1) and (open<p1) and (close>p2) and (open>p2) : (close>p1) and (open>p1) and (close<p2) and (open<p2)
    is_inside
    

    
//
// ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ //
// Moving Averages
// ------------------------------------------------------------------------------------------------------------------

g_ma = 'MA\' s ----------------------------------------------------'
i_show_ma = input.bool(false, title='Show MAs', group=g_ma)
i_ma_time = input.timeframe(title='Timeframe', defval='5', group=g_ma) // chart
i_bars_merge = input.bool(true, title='Lookahead On', group=g_ma)
i_ma_offset = input.int(0, title='Offset MAs', group=g_ma)
ma_inl1 = 'len1'
ma_type = input.string(title='Type', defval='ema', options=['sma', 'ema', 'zema', 'dema', 'tema', 'wma', 'vwma', 'smma', 'rma', 'hma', 'lsma', 'McGinley'], group=g_ma)
show_m1 = input.bool(title='m1', defval=true, group=g_ma, inline=ma_inl1)
show_m2 = input.bool(title='m2', defval=true, group=g_ma, inline=ma_inl1)
show_m3 = input.bool(title='m3', defval=false, group=g_ma, inline=ma_inl1)
show_m4 = input.bool(title='m4', defval=true, group=g_ma, inline=ma_inl1)
show_m5 = input.bool(title='m5', defval=false, group=g_ma, inline=ma_inl1)
show_m6 = input.bool(title='m6', defval=false, group=g_ma, inline=ma_inl1)
show_m7 = input.bool(title='m7', defval=false, group=g_ma, inline=ma_inl1)
show_m8 = input.bool(title='m8', defval=true, group=g_ma, inline=ma_inl1)

ma_inl2 = 'len2'
m_len1 = input.int(5, minval=1, title='M1', inline=ma_inl2)  // 8
m_len2 = input.int(10, minval=1, title='M2', inline=ma_inl2)  // 20
m_len3 = input.int(50, minval=1, title='M3', inline=ma_inl2)  // 50
m_len4 = input.int(75, minval=1, title='M4', inline=ma_inl2)  // 75 
m_len5 = input.int(100, minval=1, title='M5', inline=ma_inl2)  // 100
m_len6 = input.int(200, minval=1, title='M6', inline=ma_inl2)  // 200
m_len7 = input.int(300, minval=1, title='M7', inline=ma_inl2)  // 300
m_len8 = input.int(500, minval=1, title='M8', inline=ma_inl2)  // 500

ma_inl3 = 'len3'
ma_inl4 = 'len4'
ma_inl5 = 'len5'
i_use_smooth  = input.bool(false, title="", inline=ma_inl3)
i_smooth      = input.int(10, title="Smooth", inline=ma_inl3)
i_use_angle  = input.bool(true, title="", inline=ma_inl4)
i_angle =  input.int(6,title="Angle Amount", inline=ma_inl4) // 14 25
i_ma_multiple = input.bool(false, title="", inline=ma_inl5)
i_ma_multi_value = input.int(9, title="Multiply Value", inline=ma_inl5) // 10


g_ma_plot = 'Plot'
i_ma_select = input.int(6, title="Colorized", options=[1,2,3,4,5,6,7,8],group=g_ma_plot)
i_ma_candles = input.bool(false,title='MA Candles',group=g_ma_plot)
inl_fill = 'fill'
inl_conv = 'conv'
show_fill = input.bool(title='Show Fill', defval=true, inline=inl_fill, group=g_ma_plot)
show_conv = input.bool(title='Show Conv', defval=true, inline=inl_fill, group=g_ma_plot)
conv_amount = input.float(defval=25, title='Conv Amount', step=1, inline=inl_conv, group=g_ma_plot) //4 
c_type = input.string(title='Type', defval='NAS', options=['NAS', 'USD', 'JPY'], inline=inl_conv, group=g_ma_plot)
line_input = 1  //input(1, title="Line width", type=input.integer,inline=inl_fill )
l_width = 2


// Lines and Angles
ma_types(len, type, src) =>
    obj = 0.0
    this_src = src
    length = len
    if i_ma_multiple 
        length := i_ma_multi_value * len 
    if type == 'ema' // Exponential
        obj := ta.ema(this_src,length)
    if type == 'zema' // Zero Lag Exponential
        e1 = ta.ema(close,length)
        e2 = ta.ema(e1,length)
        diff = e1 - e2
        obj := e1 + diff 
    if type == 'sma' // Simple Moving Average
        obj := ta.sma(this_src,length)
    if type=="dema" // Double Exponential
        e = ta.ema(this_src, length)
        obj := 2 * e - ta.ema(e, length)
    if type == 'tema' // Triple Exponential
        ema1 = ta.ema(this_src, length)
        ema2 = ta.ema(ema1, length)
        ema3 = ta.ema(ema2, length)
        obj := 3 * (ema1 - ema2) + ema3
    if type == 'wma' // Weighted
        obj := ta.wma(this_src,length)
    if type == 'vwma' // Volume Weighted
        obj := ta.vwma(this_src,length)
    // if type=="smma" // Smoothed
    //     w = ta.wma(src, length)
    //     ma := na(w[1]) ? ta.sma(src, length) : (w[1] * (length - 1) + src) / length
    if type == "rma"
        obj := ta.rma(this_src, length)
    if type == 'hma' // Hull
        obj := ta.wma(2*ta.wma(this_src, length/2)-ta.wma(this_src, length), math.floor(math.sqrt(length) ))
    if type=="lsma" // Least Squares
        obj := ta.linreg(this_src, length, 0)
    // if type=="McGinley"
    //     mg = 0.0
    //     mg := na(mg[1]) ? ta.ema(this_src, length) : mg[1] + (this_src - mg[1]) / (length * math.pow(this_src/mg[1], 4))
    //     obj :=mg

    // if i_use_smooth
    //     ma := ta.sma(ma,i_smooth)

    obj

ma_angles(obj) =>
    this_ma = obj
    if i_use_smooth
        this_ma := ta.sma(this_ma,i_smooth)

    ma_angle = angle(this_ma,i_use_angle ? i_angle : 1)

    ma_angle

float m1 = 0.0
ma_change = request.security(syminfo.tickerid, i_ma_time, close )
m1   := request.security(syminfo.tickerid, i_ma_time, ma_types(m_len1, ma_type, close) )
m1_a = request.security(syminfo.tickerid, i_ma_time, ma_angles(m1) )
m2   = request.security(syminfo.tickerid, i_ma_time, ma_types(m_len2, ma_type, close) )
m2_a = request.security(syminfo.tickerid, i_ma_time, ma_angles(m2) )
m3   = request.security(syminfo.tickerid, i_ma_time, ma_types(m_len3, ma_type, close) )
m3_a = request.security(syminfo.tickerid, i_ma_time, ma_angles(m3) )
m4   = request.security(syminfo.tickerid, i_ma_time, ma_types(m_len4, ma_type, close) )
m4_a = request.security(syminfo.tickerid, i_ma_time, ma_angles(m4) )
m5   = request.security(syminfo.tickerid, i_ma_time, ma_types(m_len5, ma_type, close) )
m5_a = request.security(syminfo.tickerid, i_ma_time, ma_angles(m5) )
m6   = request.security(syminfo.tickerid, i_ma_time, ma_types(m_len6, ma_type, close) )
m6_a = request.security(syminfo.tickerid, i_ma_time, ma_angles(m6) )
m7   = request.security(syminfo.tickerid, i_ma_time, ma_types(m_len7, ma_type, close) )
m7_a = request.security(syminfo.tickerid, i_ma_time, ma_angles(m7) )
m8   = request.security(syminfo.tickerid, i_ma_time, ma_types(m_len8, ma_type, close) )
m8_a = request.security(syminfo.tickerid, i_ma_time, ma_angles(m8) )
ema_m = request.security(syminfo.tickerid, i_ma_time, ta.ema(close, 1500) )
ema_m_a = request.security(syminfo.tickerid, i_ma_time, ma_angles(ema_m) )


// Diff and Convergence
ma_conv(t1, t2) =>
    
    float boost = switch syminfo.ticker
        "GBPUSD" => 10000
        "EURUSD" => 10000
        "GBPJPY" => 100
        "BTCUSD" => 0.1
        "NAS100" => 1
        "NDQ100" => 1
        "GOLD"   => 10
        "SILVER" => 100
        => 1

    diff = (t1 - t2) * boost
    conv = show_conv and diff < conv_amount and diff > conv_amount * -1 ? true : false
    [diff, conv]

[m2_m4_diff, m2_m4_conv] = ma_conv(m2, m4)
[m4_m5_diff, m4_m5_conv] = ma_conv(m4, m5)
[m5_m6_diff, m5_m6_conv] = ma_conv(m5, m6)
[m7_m8_diff, m7_m8_conv] = ma_conv(m7, m8)

// Colorized MA angles
ma_select()=>
    float select = switch i_ma_select
        2 => m2_a
        3 => m3_a
        4 => m4_a
        5 => m5_a
        6 => m6_a
        7 => m7_a
        8 => m8_a
        => m6_a

    ma_a = math.abs(select)
    ma_zone =  
     ma_a   < 1 ? 0 
     : ma_a < 2 ? 1 
     : ma_a < 3 ? 2 
     : ma_a < 4 ? 3 
     : ma_a < 5 ? 4 
     : ma_a < 6 ? 5 
     : ma_a > 6 ? 6 
     : na

    ma_color = 
     ma_zone   == 0 ? red 
     : ma_zone == 1 ? orange 
     : ma_zone == 2 ? yellow 
     : ma_zone == 3 ? gray 
     : ma_zone == 4 ? blue 
     : ma_zone == 5 ? lime 
     : ma_zone == 6 ? white : na

    [ma_color]

[ma_color] = ma_select()


// Candles change
// ma_close = request.security(syminfo.tickerid, i_ma_time, close )
// plotshape( ta.change(ma_close) ? 1 : na,title="MA Candle Close",color=red ,style=shape.circle,location=location.top)


var int flag_u = 0
var int flag_d = 0
flag_u := m2_a>20 and m2_a>m2_a[1] ? 1 : flag_u==1 and m2_a<m2_a[1] ? 0 : flag_u==1 and m2_a>0 ? 1 : na
flag_d := m2_a<-10 and m2_a<m2_a[1] ? -1 : flag_d==-1 and m2_a>m2_a[1] ? 0 : flag_d==-1 and m2_a<0 ? -1 : na
//plotshape(flag_u==0 ? 1 : na,title="Flag Up",color=red ,style=shape.circle,location=location.top)
//plotshape(flag_d==0?1:na,title="Flag Down",color=green ,style=shape.circle,location=location.bottom)


// Colorize candles based on MA angles
//barcolor(i_ma_candles? ma_color : na)

// Plot Angles
//plot(show_m1 ? m1_a : na, color=m1_a>0?color.new(green,100):color.new(red,100), title='M1 A', linewidth=0)
//plot(show_m2 ? m2_a: na, title='M2 A', color=m2_a> 0 ? color.new(red, 0) : color.new(lime, 0))
//m8_f_a = plot(show_m8 ? m8_a : na, title='m8 Multi A', color=m8_a > 0 ? color.new(red, 100) : color.new(lime, 100))

// Plot
//plot(m8_a, title='M8 A', color=color.new(blue,100))
//ma_cond = i_show_ma
p_m1 = plot(i_show_ma and show_m1 and ta.change(ma_change) ? m1 : i_show_ma and show_m1 ? m1[1] : na,color=m1_a>0?green:orange,title="M1" )
p_m2 = plot(i_show_ma and show_m2 and ta.change(ma_change) ? m2 : i_show_ma and show_m1 ? m2[1] : na,color=m2_a>0?green:red,title="M2")
// p_m3 = plot(i_show_ma and show_m3 and ta.change(ma_change) ? m3 : i_show_ma and show_m1 ? m3[1] : na,color=m2_a>0?blue:aqua,title="M3")
// //p_m4 = plot(i_show_ma and show_m4 and ta.change(ma_change) ? m4 : i_show_ma and show_m1 ? m4[1] : na,color=yellow,title="M4")
// p_m5 = plot(i_show_ma and show_m5 and ta.change(ma_change) ? m5 : i_show_ma and show_m1 ? m5[1] : na,color=m5_a > 0 ? orange : m5_a < 0 and m5 > m6 ? red : green,title="M5",linewidth=i_ma_select==5?l_width:1)
// p_m6 = plot(i_show_ma and show_m6 and ta.change(ma_change) ? m6 : i_show_ma and show_m1 ? m6[1] : na,color=m6_a>0 ? red : lime, title="M6",linewidth=2)
// p_m7 = plot(i_show_ma and show_m7 and ta.change(ma_change) ? m7 : i_show_ma and show_m1 ? m7[1] : na,color=m7_a>0 ? orange : m7_a<0 and m7>m8 ? red : green,title="M7",linewidth=i_ma_select==7?l_width:1)
// p_m8 = plot(i_show_ma and show_m8 and ta.change(ma_change) ? m8 : i_show_ma and show_m1 ? m8[1] : na,color=i_ma_select==8? ma_color : m8_a>0 ? red : lime,title="M8",linewidth=i_ma_select==8?l_width:1)

// Fills
//fill(p_m7, p_m8, title='m7/m8 Conv Multi', color=m7_m8_conv and m7 > m8 ? color.new(red, 70) : m7_m8_conv and m7 < m8 ? color.new(green, 70) : na)

// fill(p_m5, p_m6, title='m5/m6 Fill Multi', color=show_fill and m5_m6_diff < 1 ? color.new(green, 90) : show_fill ? color.new(red, 90) : na)
// fill(p_m7, p_m8, title='m7/m8 Fill Multi', color=show_fill and m7_m8_diff < 1 ? color.new(green, 90) : show_fill ? color.new(red, 90) : na)
// Conv
// fill(p_m2, p_m4, title='m2/m4 Conv Multi', color=m2_m4_conv and m2 > m4 ? color.new(red, 70) : m2_m4_conv and m2 < m4 ? color.new(green, 70) : na)
// fill(p_m5, p_m6, title='m5/m6 Conv Multi', color=m5_m6_conv and m5 > m6 ? color.new(red, 70) : m5_m6_conv and m5 < m6 ? color.new(green, 70) : na)

//plot(m5_m6_diff, title='m5/m6 Diff Multi', linewidth=0, color=color.new(blue, 100))
// 
// Previous State
var state_change = blue
ss_red2 = m5 > m6 and m5_a > 0
ss_orange2 = m5 > m6 and m5_a < 0
ss_lime2 = m5 < m6 and m6_a > 0
ss_green2 = m5 < m6 and m6_a < 0
m5_m6_c = ss_red2 ? red : ss_orange2 ? orange : ss_lime2 ? lime : ss_green2 ? green : na

// Distance to M8
m8_dist = get_point_value(close, m8, true)
//plot(m8_dist, title='M8 Dist', color= m8_dist>20 ? color.new(red,100) : color.new(green,100) )


g_super = 'Supertrend ----------------------------------------------------'
i_show_sup = input.bool(false, title='Show Supertrend', group=g_super)
i_time_sup = input.timeframe(title='Timeframe', defval='60')
inl_sup1 = "inl_sup1"
i_sup_show1= input.bool(false,title='Sup 1',inline=inl_sup1)
i_sup1_a = input.int(10,title='',inline=inl_sup1)
i_sup1_b = input.int(1,title='',inline=inl_sup1)
inl_sup2 = "inl_sup2"
i_sup_show2= input.bool(true,title='Sup 2',inline=inl_sup2)
i_sup2_a = input.int(11,title='',inline=inl_sup2)
i_sup2_b = input.int(2,title='',inline=inl_sup2)
inl_sup3 = "inl_sup3"
i_sup_show3= input.bool(false,title='Sup 3',inline=inl_sup3)
i_sup3_a = input.int(12,title='',inline=inl_sup3)
i_sup3_b = input.int(3,title='',inline=inl_sup3)
inl_sup4 = "inl_sup4"
i_sup_show4 = input.bool(true,title='Sup 4',inline=inl_sup4) 
i_sup4_a = input.int(5,title='',inline=inl_sup4) 
i_sup4_b = input.int(5,title='',inline=inl_sup4) 
i_show_close = input.bool(false,title='Show close line', group=g_super)
sup_src = input(hl2, title='Source', group=g_super)
//Multiplier = input.float(title='ATR Multiplier', step=0.1, defval=3.0)
changeATR = true //input(title='Change ATR Calculation Method?', defval=true, group=g_super)
showsignals = false //input(title='Show Buy/Sell Signals ?', defval=false, group=g_super)

supertend(p, m) =>
    atr2 = ta.sma(ta.tr, p)
    atr = changeATR ? ta.atr(p) : atr2
    up = sup_src - m * atr
    up1 = nz(up[1], up)
    up := close[1] > up1 ? math.max(up, up1) : up
    dn = sup_src + m * atr
    dn1 = nz(dn[1], dn)
    dn := close[1] < dn1 ? math.min(dn, dn1) : dn
    trend = 1
    trend := nz(trend[1], trend)
    trend := trend == -1 and close > dn1 ? 1 : trend == 1 and close < up1 ? -1 : trend
    b_sig = trend == 1 and trend[1] == -1

    s_sig = trend == -1 and trend[1] == 1

    [trend, up, dn, b_sig, s_sig]

[trend1, up1, dn1, b_sig1, s_sig1]  = request.security(syminfo.tickerid, i_time_sup, supertend(i_sup1_a, i_sup1_b), lookahead=barmerge.lookahead_on )
[trend2, up2, dn2, b_sig2, s_sig2]  = request.security(syminfo.tickerid, i_time_sup, supertend(i_sup2_a, i_sup2_b), lookahead=barmerge.lookahead_on )
[trend3, up3, dn3, b_sig3, s_sig3]  = request.security(syminfo.tickerid, i_time_sup, supertend(i_sup3_a, i_sup3_b), lookahead=barmerge.lookahead_on )
[trend4, up4, dn4, b_sig4, s_sig4]  = request.security(syminfo.tickerid, i_time_sup, supertend(i_sup4_a, i_sup4_b), lookahead=barmerge.lookahead_on )

newbar(res) => ta.change(time(res)) == 0 ? 0 : 1
change = newbar(i_time_sup)
//plot(change)

// [10, 1]
up1 := change ? up1 : up1[1]
trend1  := change ? trend1 : trend1[1]
upPlot1 = plot(i_show_sup and trend1 == 1 ? up1 : up1, title='Up Trend 1', style=plot.style_linebr, linewidth=1, 
 color=i_sup_show1 and trend1 == 1 ? aqua : i_sup_show1 and trend1 == -1 ? color.new(aqua,80) : na  )
dn1 := change ? dn1 : dn1[1]
dnPlot1 = plot(i_show_sup and trend1 == -1 ? dn1 : dn1 , title='Down Trend 1', style=plot.style_linebr, linewidth=1, 
 color=i_sup_show1 and trend1 == -1 ? yellow : i_sup_show1 and trend1 == 1 ? color.new(yellow,80) : na )
dir_chng1 = trend1[1]==-1 and trend1==1 ? dn1[1] : trend1[1]==1 and trend1==-1 ? up1[1] : 0

// Supertrend 2
up2 := change ? up2 : up2[1]
trend2  := change ? trend2 : trend2[1]
upPlot2 = plot(i_show_sup and trend2 == 1 ? up2 : na, title='Up Trend 2', style=plot.style_linebr, linewidth=1, color=i_sup_show2 ? blue:na )
dn2 := change ? dn2 : dn2[1]
dnPlot2 = plot(i_show_sup and trend2 == -1 ? dn2 : na , title='Down Trend 2', style=plot.style_linebr, linewidth=1, color=i_sup_show2 ? orange : na )

// Supertrend 3
up3     := change ? up3 : up3[1]
trend3  := change ? trend3 : trend3[1]
upPlot3 = plot(i_show_sup and trend3==1 ? up3 : na, title='Up Trend 3', style=plot.style_linebr, linewidth=1, color=i_sup_show3 ? lime:na )
dn3 := change ? dn3 : dn3[1]
dnPlot3 = plot(i_show_sup and trend3==-1 ? dn3 : na , title='Down Trend 3', style=plot.style_linebr, linewidth=1, color=i_sup_show3 ? violet :na )

// Supertrend 4
up4     := change ? up4 : up4[1]
trend4  := change ? trend4 : trend4[1]
upPlot4 = plot(trend4==1 ? up4 : na, title='Up Trend 4', style=plot.style_linebr, linewidth=1, color=i_sup_show4 ? green:na )
dn4 := change ? dn4 : dn4[1]
dnPlot4 = plot(trend4==-1 ? dn4 : na , title='Down Trend 4', style=plot.style_linebr, linewidth=1, color=i_sup_show4 ?red :na )






g_bb = 'BB Bands ----------------------------------------------------'
i_show_bb_bands = input.bool(false,title='Show BB Bands', group=g_bb)
i_showbb_fast = input.bool(false, title="Show Fast")
i_showbb = input.bool(false, title="Show BB")
i_bb_len_fast = input(20, title='BB Len Fast', group=g_bb)
i_bb_len = input(350, title='BB Len', group=g_bb)
i_show_back = input.bool(false, title='Show Background')
sqz_length = 80
// Fast
bb_basis_f = ta.sma(close, i_bb_len_fast)
dev_f = 2 * ta.stdev(close, i_bb_len_fast)
bb_upper_f = bb_basis_f + dev_f
bb_lower_f = bb_basis_f - dev_f
bb_spread_f = bb_upper_f - bb_lower_f
bb_angle_f = angle(bb_basis_f,1)
// plot(i_show_bb_bands and i_showbb_fast ? bb_basis_f : na,title="Basis")
// plot(i_show_bb_bands and i_showbb_fast ? bb_upper_f : na,title="bb_upper")
// plot(i_show_bb_bands and i_showbb_fast ? bb_lower_f : na,title="bb_lower")

bb_basis = ta.sma(close, i_bb_len)
dev = 2 * ta.stdev(close, i_bb_len)
bb_upper = bb_basis + dev
bb_lower = bb_basis - dev
bb_spread = bb_upper - bb_lower
bb_angle = angle(bb_basis,1)
//avgspread = sma(bb_spread, sqz_length)
// plot(i_show_bb_bands and i_showbb ? bb_basis : na,title="Basis")
// plot(i_show_bb_bands and i_showbb ? bb_upper : na,title="bb_upper")
// plot(i_show_bb_bands and i_showbb ? bb_lower : na,title="bb_lower")
bb_cond = m1 < bb_lower ? 1 : m1 > bb_upper ? -1 : na
//bgcolor(i_show_back and bb_cond == 1 ? aqua : i_show_back and bb_cond == -1 ? orange : na)
//barcolor(bb_cond == 1 ? aqua : bb_cond == -1 ? orange : na)





g_rsi_band = 'RSI + Bands ----------------------------------------------------'
rsi_time = input.timeframe("",title="Timeframe", group=g_rsi_band)
isBB = true
rsiLengthInput = input.int(14, minval=1, title="RSI Length", group=g_rsi_band) // 21
rsiSourceInput = input.source(close, "Source", group=g_rsi_band)
maLengthInput = input.int(20, title="MA Length", group=g_rsi_band) // 14
bbMultInput = input.float(1.5, minval=0.001, maxval=50, title="BB StdDev", group=g_rsi_band) // 2.0

rsi_bb()=>
    rsi_up = ta.rma(math.max(ta.change(rsiSourceInput), 0), rsiLengthInput)
    rsi_down = ta.rma(-math.min(ta.change(rsiSourceInput), 0), rsiLengthInput)
    rsi = rsi_down == 0 ? 100 : rsi_up == 0 ? 0 : 100 - (100 / (1 + rsi_up / rsi_down))
    rsiMA = ta.sma(rsi, maLengthInput)
    bbUpperBand = rsiMA + ta.stdev(rsi, maLengthInput) * bbMultInput
    bbLowerBand = rsiMA - ta.stdev(rsi, maLengthInput) * bbMultInput

    [rsi,rsiMA,bbUpperBand,bbLowerBand]

[rsi,rsiMA,bbUpperBand,bbLowerBand] = rsi_bb()

rsi_m = request.security(syminfo.tickerid, rsi_time, rsi )
rsiMA_m = request.security(syminfo.tickerid, rsi_time, rsiMA )
bbUpperBand_m = request.security(syminfo.tickerid, rsi_time, bbUpperBand )
bbLowerBand_m = request.security(syminfo.tickerid, rsi_time, bbLowerBand )

// Plot
rsi_up = rsi_m>bbUpperBand_m and rsiMA_m>50 ? rsiMA_m : na
//plot(rsi_up,title="RSI Up", style=plot.style_circles,color=color.red)
rsi_down = rsi_m<bbLowerBand_m and rsiMA_m<50 ? rsiMA_m : na
//plot(rsi_down,title="RSI Down", style=plot.style_circles,color=color.green)
// Bands
//plot(bbUpperBand_m ,title="BB up", style=plot.style_circles,color=color.red)
//plot(bbLowerBand_m ,title="BB down", style=plot.style_circles,color=color.green)





// ===  Fibo Trend ===
// ==================================================
g_fibo_trend = 'G Fibo Trend ----------------------------------------------------'
inl_fibo = 'inl-fib'
fibo_trend_time = input.timeframe("",title="Timeframe", group=g_fibo_trend)
show_gfibo = false //input.bool(false,"Show G Fibo Trend",group=g_fibo_trend)
show_candles = input.bool(true,"Fibo Candles",group=g_fibo_trend)
ma_val = input.int(10, title="MA",group=g_fibo_trend ) // 6
fibo_period = input.int(25,"Analysis Period",group=g_fibo_trend) // 50
lowerValue = input.float(0.382,"Lower Fibonacci Level",options=[0.236, 0.382, 0.50, 0.618, 0.786],group=g_fibo_trend)
upperValue = input.float(0.618,"Upper Fibonacci Level",options=[0.236, 0.382, 0.50, 0.618, 0.786],group=g_fibo_trend)
showFill = input.bool(true,"Show Filling",group=g_fibo_trend)
changeCandle = input.bool(true,"Change Candle Color",group=g_fibo_trend)




f_fibo_trend() =>
    ma = ta.wma(close,ma_val)
    max = ta.highest(close, fibo_period)
    min = ta.lowest(close, fibo_period)
    lowerFib = min + (max-min)*lowerValue
    upperFib = min + (max-min)*upperValue
    [ma, lowerFib, upperFib]

[ma, lowerFib, upperFib] = request.security(syminfo.tickerid, fibo_trend_time, f_fibo_trend() )

float closeVal = ma
float openVal = ma
color clrToUse = closeVal>upperFib and openVal>upperFib?green:closeVal<lowerFib and openVal<lowerFib?red:yellow

// maxLine = plot(max and show_gfibo?max : na,color=color.green,title="Max")
// minLine = plot(min and show_gfibo?min : na,color=color.red,title="Min")
// LowerFibLine = plot(lowerFib and show_gfibo?lowerFib : na,color=color.rgb(228, 255, 75, 20),title="Lower Fib")
// UpperFibLine = plot(upperFib and show_gfibo?upperFib : na,color=color.rgb(228, 255, 75, 20),title="Upper Fib")
// fill(maxLine,UpperFibLine,color=showFill?color.rgb(0,255,0,changeCandle?95:70):na)
// fill(UpperFibLine,LowerFibLine,color=showFill?color.rgb(228, 255, 75, changeCandle?95:70):na)
// fill(LowerFibLine,minLine,color=showFill?color.rgb(255,0,0,changeCandle?95:70):na)
barcolor(show_candles? clrToUse : na)
//plotcandle(open,high,low,close,"Bar",color=changeCandle?clrToUse:na,wickcolor=changeCandle?clrToUse:na,bordercolor=changeCandle?clrToUse:na)





// ===  RSI Wicks ===
// ==================================================
g_rsi = 'RSI Wicks -------------------------------------------------------------'
inl_rsi1 = '1'
inl_rsi2 = '2'
i_show_rsi  = input.bool(false, title='Show RSI', inline=inl_rsi1, group=g_rsi)
rsi_strong  = input.bool(title='Strongest', defval=false, inline=inl_rsi1, group=g_rsi)
rsi_len     = input.int(6, minval=1, title='Length', inline=inl_rsi2, group=g_rsi) // 14
rsi_len_m   = input.int(6, minval=1, title='Length', inline=inl_rsi2, group=g_rsi) // 14
rsi_pos     = 'tb' //input.string(title='Position', defval='tb', options=['tb', 't', 'b'], inline=inl_rsi2, group=g_rsi)
i_use_rsi_candles = input.bool(false, title='Use RSI Candles', inline=inl_rsi1, group=g_rsi)


g_rsi_time = ''
inl_rsi3 = '3'
use_rsi_curr = input.bool(title='Show Current', defval=true, inline=inl_rsi3, group=g_rsi_time)
use_rsi_multi = input.bool(title='Show Multi', defval=false, inline=inl_rsi3, group=g_rsi_time)
i_rsi_time = input.timeframe(title='Timeframe', defval='15', group=g_rsi_time)

wicks = input(true, title="Wicks based on stand-alone RSI")
target_up = 70  // input(70, minval=1, title="RSI Up")
target_down = 30  // input(30, minval=1, title="RSI Down")
src_close = close
src_open = open
src_high = high
src_low = low
rsi_l_up = 70
rsi_l_dn = 30


rsi_wicks(rsi_len) =>
    norm_close = math.avg(src_close, src_close[1])
    gain_loss_close = ta.change(src_close) / norm_close
    RSI_close = 50 + 50 * ta.rma(gain_loss_close, rsi_len) / ta.rma(math.abs(gain_loss_close), rsi_len)

    norm_open = if wicks == true
        math.avg(src_open, src_open[1])
    else
        math.avg(src_close, src_close[1])
    gain_loss_open = ta.change(src_open) / norm_open
    RSI_open = 50 + 50 * ta.rma(gain_loss_open, rsi_len) / ta.rma(math.abs(gain_loss_open), rsi_len)

    norm_high = if wicks == true
        math.avg(src_high, src_high[1])
    else
        math.avg(src_close, src_close[1])
    gain_loss_high = ta.change(src_high) / norm_high
    RSI_high = 50 + 50 * ta.rma(gain_loss_high, rsi_len) / ta.rma(math.abs(gain_loss_high), rsi_len)

    norm_low = if wicks == true
        math.avg(src_low, src_low[1])
    else
        math.avg(src_close, src_close[1])
    gain_loss_low = ta.change(src_low) / norm_low
    RSI_low = 50 + 50 * ta.rma(gain_loss_low, rsi_len) / ta.rma(math.abs(gain_loss_low), rsi_len)

    [RSI_open, RSI_close, RSI_high, RSI_low]

//[RSI_open, RSI_close, RSI_high, RSI_low] = rsi_wicks(rsi_len1)

// Multi
// RSI_open_m = request.security(syminfo.tickerid, rsi_time, RSI_open)
// RSI_high_m = request.security(syminfo.tickerid, rsi_time, RSI_high)
// RSI_low_m = request.security(syminfo.tickerid, rsi_time, RSI_low)
[RSI_open, RSI_close, RSI_high, RSI_low]  = rsi_wicks(rsi_len)


// Normal
rsi_color_up = RSI_close > rsi_l_up and RSI_open > rsi_l_up ? red 
 : RSI_close > rsi_l_up or RSI_open > rsi_l_up ? orange 
 : RSI_high > rsi_l_up and RSI_close < rsi_l_up ? yellow : na

rsi_color_dn = RSI_close < rsi_l_dn and RSI_open < rsi_l_dn ? lime 
 : RSI_close < rsi_l_dn or RSI_open < rsi_l_dn  ? blue
 : RSI_low < rsi_l_dn and (RSI_close > rsi_l_dn or RSI_open > rsi_l_dn) ? violet : na

rsi_cond_up = i_use_rsi_candles and RSI_close> RSI_open? 1 : 0
rsi_cond_dn = i_use_rsi_candles and RSI_close< RSI_open? 1 : 0
// plot(i_show_rsi and use_rsi_curr ? RSI_high: na, title='RSI High', color=color.new(yellow, 100))
// plot(i_show_rsi and use_rsi_curr ? RSI_close: na, title='RSI Close', color=color.new(green, 100))
// plot(i_show_rsi and use_rsi_curr ? RSI_open: na, title='RSI Open', color=color.new(green, 100))
// plot(i_show_rsi and use_rsi_curr ? RSI_low: na, title='RSI Low', color=color.new(violet, 100))
// plot(i_show_rsi and use_rsi_curr ? rsi_cond_up : na, title='RSI Up Condition', color=color.new(blue, 100))
// plot(i_show_rsi and use_rsi_curr ? rsi_cond_dn : na, title='RSI Down Condition', color=color.new(blue, 100))
//plotshape(i_show_rsi and use_rsi_curr and (rsi_cond_up) ? 1 : i_show_rsi and use_rsi_curr ? 1 : na, title='RSI Wicks Up', color=rsi_color_up, style=shape.circle, location=location.top)
//plotshape(i_show_rsi and use_rsi_curr and (rsi_cond_dn) ? 1 : i_show_rsi and use_rsi_curr ? 1 : na, title='RSI Wicks Down', color=rsi_color_dn, style=shape.circle, location=location.bottom)


// Multi
// var float RSI_open_m = 0.0
// var float RSI_high_m = 0.0
// var float RSI_low_m = 0.0
// var float RSI_close_m = 0.0
[RSI_open_m, RSI_close_m, RSI_high_m, RSI_low_m]  = rsi_wicks(rsi_len_m)
rsi_close_m = request.security(syminfo.tickerid, i_rsi_time, close)
rsi_open_m = request.security(syminfo.tickerid, i_rsi_time, open)
RSI_open_m :=  ta.change(rsi_close_m) ? request.security(syminfo.tickerid, i_rsi_time, RSI_open_m) : RSI_open_m[1]
RSI_high_m := ta.change(rsi_close_m) ? request.security(syminfo.tickerid, i_rsi_time, RSI_high_m) : RSI_high_m[1]
RSI_low_m := ta.change(rsi_close_m) ? request.security(syminfo.tickerid, i_rsi_time, RSI_low_m) : RSI_low_m[1]
RSI_close_m := ta.change(rsi_close_m) ? request.security(syminfo.tickerid, i_rsi_time, RSI_close_m) : RSI_close_m[1]

rsi_color_up_m = RSI_close_m > rsi_l_up and RSI_open_m > rsi_l_up ? red 
 : RSI_close_m > rsi_l_up or RSI_open_m > rsi_l_up ? orange 
 : RSI_high_m > rsi_l_up and RSI_close_m < rsi_l_up ? yellow : na

rsi_color_dn_m = RSI_close_m < rsi_l_dn and RSI_open_m < rsi_l_dn ? lime 
 : RSI_close_m < rsi_l_dn or RSI_open_m < rsi_l_dn  ? blue
 : RSI_low_m < rsi_l_dn and (RSI_close_m > rsi_l_dn or RSI_open_m > rsi_l_dn) ? violet : na

rsi_cond_up_m = i_use_rsi_candles and rsi_close_m > rsi_open_m ? 1 : 0
rsi_cond_dn_m = i_use_rsi_candles and rsi_close_m < rsi_open_m ? 1 : 0
// plot(i_show_rsi and use_rsi_multi ? RSI_high_m : na, title='RSI High', color=color.new(yellow, 100))
// plot(i_show_rsi and use_rsi_multi ? RSI_close_m : na, title='RSI Close', color=color.new(green, 100))
// plot(i_show_rsi and use_rsi_multi ? RSI_open_m : na, title='RSI Open', color=color.new(green, 100))
// plot(i_show_rsi and use_rsi_multi ? RSI_low_m : na, title='RSI Low', color=color.new(violet, 100))
// plot(i_show_rsi and use_rsi_multi ? rsi_cond_up_m : na, title='RSI Up Condition', color=color.new(blue, 100))
// plot(i_show_rsi and use_rsi_multi ? rsi_cond_dn_m : na, title='RSI Down Condition', color=color.new(blue, 100))
plotshape(i_show_rsi and use_rsi_multi and (rsi_cond_up_m) ? 1 : i_show_rsi and use_rsi_multi ? 1 : na, title='RSI Wicks Up', color=rsi_color_up_m, style=shape.circle, location=location.top)
plotshape(i_show_rsi and use_rsi_multi and (rsi_cond_dn_m) ? 1 : i_show_rsi and use_rsi_multi ? 1 : na, title='RSI Wicks Down', color=rsi_color_dn_m, style=shape.circle, location=location.bottom)





// ===  Stochastic ===
// ==================================================
g_rstoch = 'Stochastic -------------------------------------------------------------'
a = input.int(10, "Percent K Length", group=g_rstoch) // 10
b = input.int(3, "Percent D Length", group=g_rstoch)
ob = input.int(40, "Overbought", group=g_rstoch)
os = input.int(-40, "Oversold", group=g_rstoch)
smooth = input.int(1, "Smoothing", group=g_rstoch)
stoch_time = input.timeframe('5', "Stoch Time", group=g_rstoch)
show_bars = input.bool(false, title="Show Bars", group=g_rstoch)

stoch() =>
    // Range Calculation
    ll = ta.lowest (low, a)
    hh = ta.highest (high, a)
    diff = hh - ll
    rdiff = close - (hh+ll)/2

    avgrel = ta.ema(ta.ema(rdiff,b),b)
    avgdiff = ta.ema(ta.ema(diff,b),b)
    // SMI calculations
    SMI = avgdiff != 0 ? (avgrel/(avgdiff/2)*100) : 0

stoch_m = request.security(syminfo.tickerid, stoch_time, stoch() )
stoch_SMI = request.security(syminfo.tickerid, stoch_time, ta.ema(stoch_m,b) ) 
stoch_EMA = request.security(syminfo.tickerid, stoch_time, ta.ema(stoch_m, 10) )  
 
//plot(stoch_SMI, title="Stochastic", color=color.new(blue,100) )
//plot(stoch_EMA, title="stoch_EMA", color=color.new(yellow,100))

// level_40 = ob
// level_40smi = stoch_SMI > ob ? stoch_SMI : level_40
// level_m40 = os
// level_m40smi = stoch_SMI < os ? stoch_SMI : level_m40

stoch_color = stoch_SMI<stoch_EMA and stoch_EMA>ob ? yellow : stoch_EMA>ob ? red : stoch_SMI>ob ? orange : 
 stoch_SMI>stoch_EMA and stoch_EMA<os ? aqua : stoch_EMA<os ? green : stoch_SMI<os ? lime : na

barcolor(show_bars?stoch_color:na)
 



// ===  PD Retrace ===
// ==================================================
g_retrace = 'PD Retrace -------------------------------------------------------------'
i_pd_time = input.timeframe('D', "Resolution", group=g_retrace) // 1 hour 4 hour
i_pd_lookback = input.int(1, title='Lookback', group=g_retrace)
i_pd_fibo = input.bool(false, title='Show Fibo', group=g_retrace)
i_pd_labels = input.bool(false, title="Show Labels", group=g_retrace)
i_pd_fills = input.bool(false, title='Fill in Fibo Levels', group=g_retrace)
i_pd_fills_trans = input.int(90, title='Fill Transparency', group=g_retrace)

// Fibo
close_m = request.security(syminfo.tickerid, i_pd_time, close, lookahead=barmerge.lookahead_on)
fibo0   = request.security(syminfo.tickerid, i_pd_time, high[i_pd_lookback], lookahead=barmerge.lookahead_on)
fibo100 = request.security(syminfo.tickerid, i_pd_time, low[i_pd_lookback], lookahead=barmerge.lookahead_on)
fibo23  = (fibo100-fibo0)*0.786+fibo0
fibo38  = (fibo100-fibo0)*0.618+fibo0
fibo50  = (fibo100-fibo0)/2+fibo0
fibo62  = (fibo100-fibo0)*0.382+fibo0
fibo78  = (fibo100-fibo0)*0.236+fibo0

get_retrace_level(obj) =>
    var int level = 0
    // Above Red
    if obj>fibo0 
        level := 8
    // Red 
    if obj<fibo0 and obj>fibo78
        level := 7
    // Orange 
    if obj<fibo78 and obj>fibo62
        level := 6
    // Yellow
    if obj<fibo62 and obj>fibo50
        level := 5
    // Aqua
    if obj<fibo50 and obj>fibo38
        level := 4
    // Green
    if obj<fibo38 and obj>fibo23
        level := 3
    // Lime
    if obj<fibo23 and obj>fibo100
        level := 2
    // Below Lime
    if obj<fibo100
        level := 1

    level



// Fibo Plots
p_fib0 = plot(i_pd_fibo ? fibo0 : na ,title='Fibo 0',color=color.new(color.red,50),linewidth=2)
p_fibo100 = plot(i_pd_fibo ? fibo100 : na ,title='Fibo 100',color=color.new(color.green,50),linewidth=2)
p_fibo23  = plot(i_pd_fibo ? fibo23 : na,title='Fibo 23',color=color.new(color.gray,50) )
p_fibo38  = plot(i_pd_fibo ? fibo38 : na,title='Fibo 38',color=color.new(color.gray,50) )
p_fibo50  = plot(i_pd_fibo ? fibo50 : na,title='Fibo 50',color=color.new(color.gray,50) )
p_fibo62  = plot(i_pd_fibo ? fibo62 : na,title='Fibo 62',color=color.new(color.gray,50) )
p_fibo78  = plot(i_pd_fibo ? fibo78 : na,title='Fibo 78',color=color.new(color.gray,50) )

fill(p_fibo78, p_fib0,title='Fill 100',color=i_pd_fills? color.new(red,i_pd_fills_trans) : na )
fill(p_fibo62, p_fibo78,title='Fill 78',color=i_pd_fills? color.new(orange,i_pd_fills_trans) : na )
fill(p_fibo50, p_fibo62,title='Fill 62',color=i_pd_fills? color.new(yellow,i_pd_fills_trans) : na )
fill(p_fibo50, p_fibo38,title='Fill 38',color=i_pd_fills? color.new(aqua,i_pd_fills_trans) : na )
fill(p_fibo38, p_fibo23,title='Fill 23',color=i_pd_fills? color.new(green,i_pd_fills_trans) : na )
fill(p_fibo23, p_fibo100,title='Fill 0',color=i_pd_fills? color.new(lime,i_pd_fills_trans) : na )

diff_fibo0 = ta.change(fibo0,1) 
diff_fibo100 = ta.change(fibo100,1)  

if ta.change(fibo0) and i_pd_labels
    higher = fibo0 > fibo0[1] ? "Higher: " + str.tostring(diff_fibo0)  : fibo0 < fibo0[1] ? "Lower: " + str.tostring(diff_fibo0) : "No Change: " + str.tostring(diff_fibo0)

    
    txt1 = str.tostring(get_point_value(fibo0, fibo100, false))
    info1 = label.new(x=time,y=fibo0,xloc=xloc.bar_time, text=txt1, textcolor=#ffffff)

    lower = fibo100 > fibo100[1] ? "Higher: " + str.tostring(diff_fibo100)  : fibo100 < fibo100[1] ? "Lower: " + str.tostring(diff_fibo100) : "No Change: " + str.tostring(diff_fibo100)
    txt2 = str.tostring(lower)
    info2 = label.new(x=time,y=fibo100,xloc=xloc.bar_time, text=txt2, textcolor=#ffffff, style=label.style_label_up)


retrace_level = get_retrace_level(up1)
plot(retrace_level, title="Retrace", color=color.new(blue, 100))


// ===  Momentum ===
// ==================================================
g_mom = 'Momentum -------------------------------------------------------------'
i_show_mom = input.bool(false, title='Show Momentum', group=g_mom)
mom_len = input(12, title='Momentum Length', group=g_mom)
momentum(seria, len) =>
	mom = seria - seria[mom_len]
	mom
mom0 = momentum(close, mom_len)
mom1 = momentum( mom0, 1)

mom_up = mom0 > 0 and mom1 > 0
mom_down = mom0 < 0 and mom1 < 0

mom_plot_sell = i_show_mom and mom_down
mom_plot_buy = i_show_mom and mom_up
// plotshape(mom_plot_sell, title='Momentum Sell', color=red, style=shape.arrowdown, location=location.abovebar, size=size.large)
// plotshape(mom_plot_buy, title='Momentum Buy', color=green, style=shape.arrowup, location=location.belowbar, size=size.large)


//
// ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ //
// ADX
// ------------------------------------------------------------------------------------------------------------------
g_adx = 'ADX -------------------------------------------------------------'
g_adx_time = ''
i_adx_showbars = input.bool(false, title="ADX bars", group=g_adx)
inl_adx = '1'
use_adx_curr = input.bool(title='Show Current', defval=true, inline=inl_adx, group=g_adx)
use_adx_multi = input.bool(title='Show Multi', defval=true, inline=inl_adx, group=g_adx)
adx_time = input.timeframe(title='Timeframe', defval='30', group=g_adx)

adx_len = input(9, title='Length', group=g_adx)  // 14
adx_line = input(20, title='threshold', group=g_adx)  // 20
adx_avg = input(8, title='SMA', group=g_adx)  // 10
adx_top = input(50, title='High', group=g_adx)  // 41
adx_high = input(39.5, title='High', group=g_adx)  // 41
adx_mid = input(title='Mid', defval=33, group=g_adx)
adx_center = input(title='Center', defval=20, group=g_adx)
adx_low = input(title='Low', defval=12, group=g_adx)

// Show adx
// show_di_plus = input(title='Di Plus', defval=true,group=g_adx)
// show_di_minus = input(title='Di Minus', defval=true,group=g_adx)
// show_adx = input(title='ADX', defval=true,group=g_adx)
// show_adx_sma = input(title='ADX SMA', defval=true,group=g_adx)


f_adx() =>
    smooth_tr = 0.0
    smooth_di_plus = 0.0
    smooth_di_minus = 0.0
    TrueRange = math.max(math.max(high - low, math.abs(high - nz(close[1]))), math.abs(low - nz(close[1])))
    DI_plus = high - nz(high[1]) > nz(low[1]) - low ? math.max(high - nz(high[1]), 0) : 0
    DI_minus = nz(low[1]) - low > high - nz(high[1]) ? math.max(nz(low[1]) - low, 0) : 0
    smooth_tr := nz(smooth_tr[1]) - nz(smooth_tr[1]) / adx_len + TrueRange
    smooth_di_plus := nz(smooth_di_plus[1]) - nz(smooth_di_plus[1]) / adx_len + DI_plus
    smooth_di_minus := nz(smooth_di_minus[1]) - nz(smooth_di_minus[1]) / adx_len + DI_minus

    di_plus = smooth_di_plus / smooth_tr * 100
    di_minus = smooth_di_minus / smooth_tr * 100
    DX = math.abs(di_plus - di_minus) / (di_plus + di_minus) * 100
    adx = ta.sma(DX, adx_len)
    adx_sma = ta.sma(adx, adx_avg)

    [adx, adx_sma, di_plus, di_minus]

[adx, adx_sma, di_plus, di_minus] = f_adx()

adx_m = request.security(syminfo.tickerid, adx_time, adx)
adx_sma_m = request.security(syminfo.tickerid, adx_time, adx_sma)
di_plus_m = request.security(syminfo.tickerid, adx_time, di_plus)
di_minus_m = request.security(syminfo.tickerid, adx_time, di_minus)

barcolor(i_adx_showbars and di_plus_m>di_minus_m ? red : green)




//
// ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ //
// STOCH RSI
// ------------------------------------------------------------------------------------------------------------------
g_stoch_rsi = 'STOCH RSI -------------------------------------------------------------'
stoch_rsi_time = input.timeframe(title='Timeframe', defval='', group=g_stoch_rsi)
smoothK = input.int(3, "K", minval=1, group=g_stoch_rsi)
smoothD = input.int(3, "D", minval=1, group=g_stoch_rsi)
lengthRSI = input.int(14, "RSI Length", minval=1, group=g_stoch_rsi)
lengthStoch = input.int(14, "Stochastic Length", minval=1, group=g_stoch_rsi)
stoch_src = input(close, title="RSI Source", group=g_stoch_rsi)
i_stoch_smooth = input.bool(true, title="Use Smoothing")
i_sto_smooth_amount = input.int(3, title="Smooth Amount")

stoch_rsi()=>
    stoch_rsi = ta.rsi(stoch_src, lengthRSI )
    stoch = ta.stoch(stoch_rsi, stoch_rsi, stoch_rsi, lengthStoch)
    k = ta.sma(stoch, smoothK)
    d = ta.sma(k, smoothD)

    if i_stoch_smooth
        stoch_rsi := ta.sma(stoch_rsi, i_sto_smooth_amount)
        stoch := ta.sma(stoch, i_sto_smooth_amount)
        k := ta.sma(k, i_sto_smooth_amount)
        d := ta.sma(d, i_sto_smooth_amount)

    [stoch_rsi,stoch,k,d]

[stoch_rsi,stoch,stoch_k,stoch_d] = request.security(syminfo.tickerid, stoch_rsi_time, stoch_rsi() ) 

stoch_ch = ta.change(bar_index % str.tonumber(stoch_rsi_time) == 0 ) ? 1 : 0
stoch_rsi := stoch_ch ? stoch_rsi : stoch_rsi[1]
stoch := stoch_ch ? stoch : stoch[1]
stoch_k := stoch_ch ? stoch_k : stoch_k[1]
stoch_d := stoch_ch ? stoch_d : stoch_d[1]
//diff = math.abs(stoch_k - stoch_d)
stoch_rsi_angle = request.security(syminfo.tickerid, stoch_rsi_time, angle(stoch_k,1) ) 
//plot(stoch_rsi, "RSI", color=color.new(color.yellow, 100) )
//plot(stoch, "Stoch RSI", color=color.new(color.green, 100) )
//pk = plot(stoch_k, "K", color=color.new(blue,100))
//pd = plot(stoch_d, "D", color=color.new(orange,100))




//
// ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ //
// Nadaraya–Watson Regression using a Rational Quadratic Kernel
rqk_group = 'Nadaraya Watson -------------------------------------------------------------'

// Settings
i_rqk_show_rqk = input.bool(false, title='Show Ma\'s ', group=rqk_group)
i_rqk_show_ma = input.bool(false, title='Show Ma\'s ', group=rqk_group)
i_rqk_use_multi = input.bool(true, title="Use Multi Timeframe", group=rqk_group)
i_rqk_resCustom = input.timeframe(title='Timeframe', defval='', group=rqk_group)

rqk_h = input.float(20.0, 'Lookback Window', tooltip='The number of bars used for the estimation. This is a sliding value that represents the most recent historical bars. Recommended range: 3-50', group=rqk_group) // 8
rqk_r = input.float(20.0, 'Relative Weighting', step=0.25, tooltip='Relative weighting of time frames. As this value approaches zero, the longer time frames will exert more influence on the estimation. As this value approaches infinity, the behavior of the Rational Quadratic Kernel will become identical to the Gaussian kernel. Recommended range: 0.25-25', group=rqk_group) // 8
rqk_x_0 = input.int(100, "Start Regression at Bar", tooltip='Bar index on which to start regression. The first bars of a chart are often highly volatile, and omission of these initial bars often leads to a better overall fit. Recommended range: 5-25', group=rqk_group) // 4 6 25
rqk_smoothColors = input.bool(false, "Smooth Colors", tooltip="Uses a crossover based mechanism to determine colors. This often results in less color transitions overall.", inline='1', group='Colors', group=rqk_group) // false
rqk_lag = input.int(1, "Lag", tooltip="Lag for crossover detection. Lower values result in earlier crossovers. Recommended range: 1-2", inline='1', group='Colors', group=rqk_group) // 2

//i_bars_merge = input.bool(false, title='Lookahead On')
rqk_src = close //input.source(close, 'Source', group=rqk_group)
rqk_size = array.size(array.from(rqk_src) ) // size of the data series

//MA
i_rqk_angle_amount = input.int(3, title='Angle Amount', group=rqk_group)
i_rqk_conv = input.int(30, title='Convergence Amount', group=rqk_group)
i_rqk_show_between = input.bool(false, title='Show Between ', group=rqk_group)

rqk_m2 = ta.hma(close,20)
rqk_m2_a = angle(rqk_m2,i_rqk_angle_amount)
rqk_m4 = ta.hma(close,75)
rqk_m4_a = angle(rqk_m4,i_rqk_angle_amount)
rqk_m8 = ta.hma(close,1000)
rqk_m8_a = angle(rqk_m8,i_rqk_angle_amount)

//plot(size,title="array size",color=color.new(blue,100))

// Further Reading:
// The Kernel Cookbook: Advice on Covariance functions. David Duvenaud. Published June 2014.
// Estimation of the bandwidth parameter in Nadaraya-Watson kernel non-parametric regression based on universal threshold level. Ali T, Heyam Abd Al-Majeed Hayawi, Botani I. Published February 26, 2021.
kernel_regression(_src, _size, _h) =>
    float _currentWeight = 0.
    float _cumulativeWeight = 0.
    for i = 0 to _size + rqk_x_0
        y = _src[i] 
        w = math.pow(1 + (math.pow(i, 2) / ((math.pow(_h, 2) * 2 * rqk_r))), -rqk_r)
        _currentWeight += y*w
        _cumulativeWeight += w
    _currentWeight / _cumulativeWeight

bull_bear() =>
    // Estimations
    yhat1 = kernel_regression(rqk_src, rqk_size, rqk_h)
    yhat2 = kernel_regression(rqk_src, rqk_size, rqk_h - rqk_lag)

    // Rates of Change
    bool wasBearish = yhat1[2] > yhat1[1]
    bool wasBullish = yhat1[2] < yhat1[1]
    bool isBearish = yhat1[1] > yhat1
    bool isBullish = yhat1[1] < yhat1
    bool isBearishChange = isBearish and wasBullish
    bool isBullishChange = isBullish and wasBearish

    // Crossovers
    bool isBullishCross = ta.crossover(yhat2, yhat1)
    bool isBearishCross = ta.crossunder(yhat2, yhat1) 
    bool isBullishSmooth = yhat2 > yhat1
    bool isBearishSmooth = yhat2 < yhat1

    [yhat1,isBullish,isBearish,isBearishChange,isBullishChange,isBullishCross,isBearishCross,isBullishSmooth,isBearishSmooth, wasBearish, wasBullish]

[yhat1,isBullish,isBearish,isBearishChange,isBullishChange,isBullishCross,isBearishCross,isBullishSmooth,isBearishSmooth, wasBearish, wasBullish] = bull_bear()


rqk = request.security(syminfo.tickerid, i_rqk_use_multi? i_rqk_resCustom : "", yhat1, lookahead=i_bars_merge ? barmerge.lookahead_on : barmerge.lookahead_off)
rqk_a = request.security(syminfo.tickerid, i_rqk_use_multi? i_rqk_resCustom : "", angle(rqk,i_rqk_angle_amount), lookahead=i_bars_merge ? barmerge.lookahead_on : barmerge.lookahead_off)

rqk_bwt =  is_between(rqk, rqk_m8)
barcolor(i_rqk_show_between and rqk_bwt and i_rqk_show_rqk ? green : (i_rqk_show_between and i_rqk_show_rqk) and not rqk_bwt ? red : na, title='Is between RQK and M8')

// Colors
rqk_buy_col = #3AFF17
rqk_sell_col = #FD1707
color c_bullish = input.color(rqk_buy_col, 'Bullish Color', group='Colors')
color c_bearish = input.color(rqk_sell_col, 'Bearish Color', group='Colors')
color colorByCross = isBullishSmooth ? c_bullish : c_bearish
color colorByRate = isBullish ? c_bullish : c_bearish
color plotColor = rqk_smoothColors ? colorByCross : colorByRate

// RQK and M8 Conv
rqk_conv = get_point_value(rqk,rqk_m8, true)
// Price and M8 Distance
rqk_price_dist = get_point_value(close,rqk_m8, true)

//plot(rqk_conv,title="Convergence",color=color.new(blue,100) )
//plot(rqk_price_dist,title="Price Distance",color=color.new(blue,100) )
//p_m2 = plot(i_rqk_show_ma ? rqk_m2 : na, title="M2", color=rqk_m2_a>0?green:red)
//p_m4 = plot(i_rqk_show_ma ? rqk_m4 : na, title="M4", color=rqk_m4_a>0?yellow:orange)
p_rqk_m8 = plot(i_rqk_show_rqk and i_rqk_show_ma ? rqk_m8 : na, title="M8", color=rqk_m8_a>0?green:red)
p_rqk = plot(i_rqk_show_rqk ? rqk : na, "RQK", color=plotColor, linewidth=2)
//plot(rqk_a,title="RQK Angle",color=color.new(blue,100) )
fill(p_rqk_m8, p_rqk, title="Fill Convergence", color=rqk_conv<i_rqk_conv ? color.new(red,80) : na)


//
// ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ //
// TRADING
// ------------------------------------------------------------------------------------------------------------------
g_trading = 'Trading ----------------------------------------------------'
i_live_trading  = input.bool(false, title='Live Trading Only',group=g_trading)
isLive          = barstate.isrealtime
i_equity        = input.string("Equity", options=["Initial", "Equity"], title="Initial or Equity",group=g_trading)
i_long_trades   = input.bool(true, title='Long Trades',group=g_trading)
i_short_trades  = input.bool(true, title='Short Trades',group=g_trading)
i_use_pos       = input.bool(true,title="Use Percentage based Position Size",group=g_trading)
i_pctStop       = input(1.0, '% of Risk to Starting Equity Use to Size Positions',group=g_trading) / 100


// ▒▒▒▒▒ Sessions ▒▒▒▒▒ 
session_StartDate = input.time(timestamp("5 March 2023 00:00 -1000"), title="Start Date", group=g_trading )
show_sessions = input.bool(false,title='Sessions', group=g_trading)
As  = input.session(title="Asia", defval="1800-0300")
Lon = input.session(title="London", defval="0300-1200")
Ny  = input.session(title="New York", defval="0800-1800")
Dz  = input.session(title="Deadzone - High Spreads", defval="1645-1830")


inl_color = "inl_color"
inl_color2 = "inl_color2"
c1_on = false //input.bool(true,title="", group=g_trading,inline=inl_color)
c1 = color.new(#00bcd4,90) //input.color(title="Asia", defval=#00bcd4,group=g_trading,inline=inl_color)
c2_on = true //input.bool(true,title="",group=g_trading,inline=inl_color)
c2 = color.new(#00796b,90) //input.color(title="London", defval=#00796b,group=g_trading,inline=inl_color)
c3_on = true //input.bool(true,title="",group=g_trading,inline=inl_color2)
c3 = color.new(#b71c1c,90) //input.color(title="New York", defval=#b71c1c,group=g_trading,inline=inl_color2)
c4_on = true //input.bool(true,title="", group=g_trading,inline=inl_color2)
c4 = color.new(color.purple,90) //input.color(title="Deadzone", defval=#b71c1c,group=g_trading,inline=inl_color2)

Session(sess) => na(time("2",sess)) == false
Asia = Session(As) and c1_on and show_sessions? c1 : na
London = Session(Lon) and c2_on and show_sessions ? c2 : na
NewYork = Session(Ny) and c3_on and show_sessions ? c3 : na
Deadzone = Session(Dz) and c4_on and show_sessions ? c4 : na
// bgcolor(Asia)
// bgcolor(London)
// bgcolor(NewYork)
// bgcolor(Deadzone)


// ▒▒▒▒▒ FILTERS ▒▒▒▒▒ 
g_filters = 'Filters'
i_use_filters = input.bool(true, title='Enable Filters',group=g_filters)
i_use_rsi_filter = input.bool(false, title='RSI Filter',group=g_filters)
i_use_time_filter = input.bool(false, title='Time Restraint',group=g_filters)
i_deadzone = input.bool(true, title='Do not take trades during',group=g_filters)

// var int flag_d = na
// flag_d := m2_a<-10 and m2_a<m2_a[1] ? -1 : flag_d==-1 and m2_a>m2_a[1] ? 0 : flag_d==-1 and m2_a<0 ? -1 : na



// ▒▒▒▒▒ TRADE LOGIC ▒▒▒▒▒ 
trade_dir() =>
    c      = close>open ? 1 : 0
    dir         = 0
    entryLong   = 0
    entryShort  = 0
    exitLong    = 0
    exitShort   = 0
    closeAll    = 0
    longSL      = 0.0
    cond        = ''

    red_s       = m5>m6
    green_s     = m5<m6
    red_s2      = m7>m8
    green_s2    = m7<m8
    red_conv    = m5_m6_conv==1
    red_conv2   = m7_m8_conv==1
    green_conv  = m5_m6_conv==1
    green_conv2  = m7_m8_conv==1
    btw_m7_m8 = ( m7>m8 and (close<m7) and (close>m8) ) or ( m7<m8 and (close>m7) and (close<m8) )? true : false
    rsi_bb_cond1 = rsi_m<bbLowerBand_m and rsiMA_m<50 ? 1 : 0
    
    // Time and Live Trading
    allow_trades = i_live_trading==0 and time>session_StartDate ? true : i_live_trading==1 and isLive ? 1 : 0


    // Uptrend
    if trend3==1


        // rsi_m 
        // rsiMA_m
        // bbUpperBand_m
        // bbLowerBand_m

        if trend1==1 and close<up1 and c==0
         and rsi_m<bbLowerBand_m
            entryLong := 1
            cond := 't1'

        if trend1==0 and up1<close and c==0
         and rsi_m<bbLowerBand_m
            entryLong := 1
            cond := 't1-b'

        if trend3==1 and close<up3 and c==0
         and rsi_m<bbLowerBand_m
            entryLong := 1
            cond := 't3'

        if trend3==1 and c==0
         and rsi_m<bbLowerBand_m and rsiMA_m<50
            entryLong := 1
            cond := 'rsi-bb'


        // Reversal
        if close>m5 and m3_a<m3_a[1] and m3<m5 and m5_a<1 and c==1
            entryShort := 1
            cond := 'M3'


        // if m8_a>0 and c==0 and close<m8 
        //  and di_minus_m>di_plus_m 
        //  and trend2==-1
        //  and m3_a>-5 and clrToUse==red
        //  and not(trend4==-1)
        //     entryLong := 1
        //     cond := 'adx'

        // if m8_a>-1 and c==0
        //  and (stoch_k<20 and stoch_d<20)
        //  and (stoch_k>stoch_d)
        //  and (stoch_k>stoch_k[1] and stoch_d>stoch_d[1])
        //  and not(close>m8)
        //  //and not(m5<m6)
        //     entryLong := 1
        //     cond := 'rsi'

        // if m8_a>0 and close<bb_lower and c==0 
        //  and close<m1 and m1_a<-25 and m4_a<5 and m2_a<m4_a
        //  and not(m6>m5 and m6_a<0)
        //  and not(adx>adx_high)
        //     entryLong := 1
        //     cond := 'm1-bb'

        // if m8_a>0 and c==0
        //  and trend4==-1 and trend1==1
        //  and close<bb_lower
        //  and not (high>rqk)
        //  and not(clrToUse==green )
        //  and not(m1_a<m1_a[1] and m1_a>m2_a)
        //     entryLong := 1
        //     cond := 'sup-a'

        // if m8_a>0 and close<bb_lower and bb_lower_f<bb_lower
        //  and rqk<bb_basis
        //     entryLong := 1
        //     cond := 'bb'

        // if m8_a>0 and close<m8 and c==0
        //  and m1_a>1 and m2_a<0 and close<m1
        //     entryLong := 1
        //     cond := 'm8-m1'

        // if m8_a>-1 and trend2==1 and trend4==-1
        //  and not(m3_a<-3 )
        //  and not(m7<m8 and close>m7)
        //  and not(close>m8)
        //  and not(clrToUse==green and m3_a<-1  )
        //  and not(m7_a<0 and green_conv2)
        //  and not(ema_m_a<0)
        //     entryLong := 1
        //     cond := 'sup-b'

        // if m4>m5 or bb_angle<-1.5
        //     entryLong := 0
        //     cond := 'no-trade'

        // if close<bb_basis and close<m3
        //  and bb_basis>bb_basis[1]
        //  and not(rqk_m8>rqk)
        //     entryLong := 1
        //     cond := 'bb'

        // if m8_a>-1 and c==0 and close<m2 and close<m6
        //  and m1<m6 and trend2==1
        //  and not(m5<m6)
        //  and not(m4_a<0 and m4>m5)
        //     entryLong := 1
        //     cond := 'sup'

        // if (m8_a>-1 ) and m2<m6
        //  and m1_a>0 and m2_a>-15
        //  and clrToUse==red
        //  and not(close<m8)
        //     entryLong := 1
        //     cond := 'rsi'

        // Exits

        if close>m8 and close>m7 and close>m2
         and m4>m5 and m2>m4 and di_plus_m>di_minus_m
            exitLong := 1
            cond := 'Exit Long'

        // if close>m8 and close>m7 
        //  and adx_sma_m>di_plus_m and adx_m>adx_high and clrToUse==green 
        //  and not(red_conv2) 
        //  and not(trend4==-1)
        //     exitLong := 1
        //     cond := 'Exit Long'

        // if c==1 and clrToUse==green 
        //  and RSI_close_m > rsi_l_up or RSI_open_m > rsi_l_up
        //  and close>m4 and m4>m5
        //     exitLong := 1
        //     cond := 'Exit Long'



        // if (m8_a>-1 ) and m2<m4
        //  and RSI_close_m < rsi_l_dn and RSI_open_m < rsi_l_dn
        //  and not(m4>m5)
        //     entryLong := 1
        //     cond := 'rsi'

        // if m8_a>-2 and c==0
        //  and low<m3 and m5_a>0 and m3<m8
        //  and trend2==-1 and trend4==1
        //     entryLong := 1
        //     cond := 'm3'

        // if m8_a<-1 and trend2==1 and trend4==-1
        //  and not(m3_a>-3 )
        //  and not(m7>m8 and close<m7)
        //  and not(close<m8)
        //  //and not(clrToUse==green and m3_a<-1  )
        //     entryShort := 1
        //     cond := 'sup-s'


        // if m8_a>-1 and m3<m8 and trend4==1 and close<up4
        //     entryLong := 1
        //     cond := 'up4'

        // if (m8_a>-2 and m7_a>-2) and trend2==-1 and m3<m8 and c==0 and clrToUse==red
        //  and not(m3_a<-10)
        //  and not(m2_a<-6)
        //     entryLong := 1
        //     cond := 'm3'

        // if (m8_a>-1 and m7_a>-1) and trend2==1 and m3_a>12 and close<m3 and c==0
        //  and not(red_conv2)
        //  and not(m3<m7)
        //  and not(m8_a<5)
        //     entryLong := 1
        //     cond := 'm3_fast'





        // if close<m8 and close<m7 
        //  and adx_sma_m>di_minus_m and adx_m>adx_high and clrToUse==red 
        //  and not(green_conv2) 
        //     exitShort := 1
        //     cond := 'Exit Short'

        // if m8_a>5 and close>m4 and m2>m4 and m6>m8
        //     exitLong := 1
        //     cond := 'Exit Long'




        // Deadzone
        // if i_deadzone and Session(Dz)
        //     entryLong := 0


        // Reversals

        // Exits
        // if RSI_close>70 and i_use_rsi_filter
        //     exitLong := 1

        // if Session(Dz) and i_deadzone
        //     exitLong := 1



        // rsi_m 
        // rsiMA_m
        // bbUpperBand_m
        // bbLowerBand_m
            
    // Downtrend
    if trend3==-1

        if trend1==-1 and close>dn1 and c==1
         //and rsi_m<bbLowerBand_m
            entryShort := 1
            cond := 't1'

        if trend3==-1 and close>dn3 and c==1
         //and rsi_m<bbLowerBand_m
            entryShort := 1
            cond := 't3'

        if trend4==-1 and close>dn4 and c==1
         //and rsi_m<bbLowerBand_m
            entryShort := 1
            cond := 't4'

        if m3_a>0 and m6_a<0
            entryShort := 0

        // M3
        // if m8_a<0 and trend2==1 and trend4==-1 and clrToUse == green
        //  and not(m3_a>10)
        //     entryShort := 1
        //     cond := 'm3'




        //Exits
        if RSI_open<30 and RSI_close<30 and i_use_rsi_filter
            exitShort := 1

        if Session(Dz) and i_deadzone
            exitShort := 1



    [entryLong,entryShort,exitLong,exitShort,closeAll,cond]

[entryLong,entryShort,exitLong,exitShort,closeAll,cond] = trade_dir()

// check if live trading or market closed
//plotshape(barstate.islastconfirmedhistory, title='Real Time', color=red, style=shape.circle, location=location.top)


//
// ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ //
// STOP LOSS
// ------------------------------------------------------------------------------------------------------------------
g_sl = 'Stop Loss ----------------------------------------------------'
i_plot_trades = input.bool(false, title="Display Trades",group=g_sl)
i_sl_type   = input.string("ATR", title="SL Type", options=["ATR", "Pips","MA","SUP","Lowest"],group=g_sl) // ATR
i_ma_atr    = input.string("M6", title="Select MA ATR", options=["M6","M7","M8"],group=g_sl) // ATR
Multip      = input.float(3, title='Stop Loss',group=g_sl) // 4 1.5
atr_src     = input.string('close', title='Close or Wicks', options=['close', 'wicks'],group=g_sl) // close


atr_group   = 'ATR'
show_sl     = input.bool(false,title="Stop Loss",group=atr_group)
atr_type    = input.string(title='Type', defval='ema', options=['sma', 'ema', 'zema', 'dema', 'tema', 'wma', 'vwma', 'smma', 'rma', 'hma', 'lsma', 'McGinley'], group=atr_group)
atr_len     = input.int(14, title='ATR number ',group=atr_group)

// Take Profit
g_tp = 'Take Profit ----------------------------------------------------'
i_tpFactor  = input(2.0, 'Target Profit',group=g_tp) // 3.5 3
i_qty_mult  = input.float(1.0, title='Quantity Multiplier',group=g_tp) // 5.0 2
i_tsFactor  = input(1.0, 'Trailing Stop',group=g_tp) // 1.25
i_ts        = input.bool(true, title="Use Trailing Stop",group=g_tp)
i_ticks     = input.float(10, title='Min Ticks',group=g_tp) // 100 for Forex 1000 for NAS
show_ts     = input.bool(true,title="Trailing Stop",group=g_tp)
i_bkcandles = 11 //input.int(11, title="Lowerest range - Number of candles",group=g_sl)


float qty_value = switch syminfo.type
    "forex" => 100000.0
    "futures" => 10.0
    "index" => 10.0
    => 10.0
    
stop_loss()=>

    float sl_short  = na
    float sl_long   = na
    float quantity  = syminfo.currency == 'JPY' ? i_pctStop * 100 : i_pctStop

    if i_sl_type == "ATR"
        atr_len = 14
        ATR = ta.atr(atr_len)
        sl_long     := (atr_src =='close' ? close : low)  - ATR * Multip
        sl_short    := (atr_src =='close' ? close : high) + ATR * Multip

    if i_sl_type == "MA"
        atr_len = 14
        ATR = ta.atr(atr_len)
        float ma_select = switch i_ma_atr
            "M6" => m6
            "M7" => m7
            "M8" => m8
            => m6
        
        sl_long     := ma_select
        sl_short    := ma_select


    if i_sl_type == "SUP"
        sl_long     := up4
        sl_short    := dn4

    if i_sl_type == "Lowest"
        sl_short    = ta.highest(high, i_bkcandles)[1]
        sl_long     = ta.lowest(low, i_bkcandles)[1]

    // Long
    longDiff  = math.abs(close - sl_long)
    longTS    = close + (i_tsFactor * longDiff)
    longTP    = close + (i_tpFactor * longDiff)
    plValue   = (i_equity=="Initial" ? strategy.initial_capital : strategy.equity) * quantity / (longDiff / close)
    pl_size   = i_use_pos ? math.ceil( plValue ) / close : math.ceil( qty_value * i_qty_mult )
    // Short
    shortDiff = math.abs(close - sl_short)
    shortTS   = close - (i_tsFactor * shortDiff)
    shortTP   = close - (i_tpFactor * shortDiff)
    psValue   = (i_equity=="Initial" ? strategy.initial_capital : strategy.equity) * quantity / (shortDiff / close)
    ps_size   = i_use_pos ? math.ceil( psValue / close ) : math.ceil( qty_value * i_qty_mult )


    [sl_short,sl_long, close, close, shortTP, longTP, longTS, shortTS, pl_size, ps_size, longDiff ]

float shortSL = 0.0
float longSL  = 0.0
float ratio_l = 0.0
float short_ticks = 0.0
float long_ticks = 0.0

[atr_short, atr_long, long_close, short_close, shortTP, longTP, longTS, shortTS, pl_size, ps_size, longDiff] = stop_loss()

// ATR
atr_sm_up = ma_types(atr_len, atr_type, atr_long)
atr_sm_dn = ma_types(atr_len, atr_type, atr_short)
plot(show_sl ? atr_sm_up  : na,"ATR Smoothed ", color=lime )
plot(show_sl ? atr_sm_dn  : na,"ATR Smoothed ", color=red )
plot(show_sl ? atr_long  : na,"ATR + ", color=color.new(green,70) )
plot(show_sl ? atr_short : na,"ATR - ", color=color.new(red,70) )


// longDiff := entryLong and strategy.opentrades == 0 ? longDiff * 100 : strategy.opentrades > 0 ? longDiff[1] : 0
// plot(longDiff,"Long Diff ", color=color.new(red,100) )

// Short Plots
plot_trans = 90

short_close := entryShort and strategy.opentrades == 0 ? short_close : strategy.opentrades > 0 ? short_close[1] : 0
short_ticks := short_close - (syminfo.mintick * i_ticks)
shortSL     := entryShort and strategy.opentrades == 0 ? atr_short : strategy.opentrades > 0 ? shortSL[1] : 0
shortTS     := entryShort and strategy.opentrades == 0 ? shortTS : strategy.opentrades > 0 ? shortTS[1] : 0
shortTP     := entryShort and strategy.opentrades == 0 ? shortTP : strategy.opentrades > 0 ? shortTP[1] : 0
p_s_c       = plot( strategy.opentrades > 0 and i_plot_trades ? short_close : na, title="Short Close", color=color.new(green,plot_trans), linewidth=1, style=plot.style_linebr)
p_s_ticks   = plot( strategy.opentrades > 0 and i_plot_trades ? short_ticks : na, title="Short Ticks", color=lime, style=plot.style_linebr)
p_s_sl      = plot( strategy.opentrades > 0 and i_plot_trades ? shortSL : na, title="Short SL", color=color.new(red,plot_trans), linewidth=1, style=plot.style_linebr)
p_s_ts      = plot( strategy.opentrades > 0 and i_plot_trades ? shortTS : na, title="Short TS", color=color.new(green,plot_trans), linewidth=1, style=plot.style_linebr)
p_s_tp      = plot( strategy.opentrades > 0 and i_plot_trades ? shortTP : na, title="Short TP", color=color.new(green,plot_trans), linewidth=1, style=plot.style_linebr)
fill(p_s_sl, p_s_c,title='Fill Short SL',color=color.new(red,plot_trans) )
fill(p_s_ts, p_s_c,title='Fill Short TS',color=color.new(lime,plot_trans) )
fill(p_s_ts, p_s_tp,title='Fill Short TP',color=color.new(green,plot_trans) )

// Long Plots
long_close  := entryLong and strategy.opentrades == 0 ? long_close : strategy.opentrades > 0 ? long_close[1] : 0
long_ticks  := long_close + (syminfo.mintick * i_ticks)
longSL      := entryLong and strategy.opentrades == 0 ? atr_long : strategy.opentrades > 0 ? longSL[1] : 0
longTS      := entryLong and strategy.opentrades == 0 ? longTS : strategy.opentrades > 0 ? longTS[1] : 0
longTP      := entryLong and strategy.opentrades == 0 ? longTP : strategy.opentrades > 0 ? longTP[1] : 0
ratio_l     := ( close  - long_close )
p_l_dist    = plot( strategy.opentrades > 0 and i_plot_trades ? get_point_value(long_close,longSL,true) : na, title="SL in Pips", color=color.new(blue,100) )
p_l_c       = plot( strategy.opentrades > 0 and i_plot_trades ? long_close : na, title="Long Close", color=color.new(red,plot_trans), linewidth=1, style=plot.style_linebr)
p_l_ticks   = plot( strategy.opentrades > 0 and i_plot_trades ? long_ticks : na, title="Long Ticks", color=color.new(lime,plot_trans), style=plot.style_linebr)
p_l_sl      = plot( strategy.opentrades > 0 and i_plot_trades ? longSL : na, title="Long SL", color=color.new(red,plot_trans), linewidth=1, style=plot.style_linebr)
p_l_ts      = plot( strategy.opentrades > 0 and i_plot_trades ? longTS : na, title="Trailing Stop", color=color.new(yellow,plot_trans), linewidth=1, style=plot.style_linebr)
p_l_tp      = plot( strategy.opentrades > 0 and i_plot_trades ? longTP : na, title="Long TP", color=color.new(green,plot_trans), linewidth=1, style=plot.style_linebr)

//psize       = plot(pl_size, title="Position Size")
//p_l_ratio   = plot( ratio_l, title="Ratio", color=color.new(red,75), linewidth=1, style=plot.style_linebr)
fill(p_l_sl,p_l_c, title='Fill Long SL', color=color.new(red,plot_trans))
fill(p_l_ts,p_l_c, title='Fill Long Trailing Stop', color=color.new(lime,plot_trans) )
fill(p_l_tp,p_l_ts, title='Fill Long Take Profit', color=color.new(green,plot_trans))



cd=close>open?1:0

if Session(Dz) and i_deadzone
    entryShort := 0
    entryLong := 0

// Long
if (entryLong )
	strategy.entry("L", strategy.long, stop = long_ticks, comment=cond, qty = pl_size)
    //strategy.exit('EXIT L', 'L', stop = longSL)
        
else
	strategy.cancel("S")

if (exitLong)
	strategy.close("L", comment = "Close L")

// Short
if (entryShort)
	strategy.entry("S", strategy.short, qty = ps_size, comment=cond)
    //strategy.exit('EXIT S', 'S', stop=shortSL)
else
	strategy.cancel("S")

if (exitShort)
	strategy.close("S", comment = "Close S")

// Filters
if Session(Dz) and i_deadzone
    strategy.close_all(comment = "close all entries")

in_profit() =>
    if strategy.position_size > 0 and close > longTS
        true
    else if strategy.position_size < 0 and close < shortTS
        true
    else
        false

getCurrentStage() =>
    var stage = 0
    if strategy.position_size == 0
        stage := 0
        stage
    if stage == 0 and strategy.position_size != 0
        stage := 1
        stage
    else if stage == 1 
        if strategy.position_size > 0 and close > longTS
            stage := 2
        if strategy.position_size < 0 and close < shortTS
            stage := 2
    else if stage == 2
        if strategy.position_size > 0 and close > longTP
            stage := 3
        if strategy.position_size < 0 and close < shortTP
            stage := 3
            stage
    stage

curStage = getCurrentStage()


float stopLevel = na
string comment  = ''
float limit     = na //strategy.position_size > 0 and close>longTP and m2>m4 ? close : strategy.position_size < 0 and close<shortTP and m2<m4 ? close : na
string win     = 'Take Profit'
string loss    = 'Loss'
string bkeven  = 'Break Even'
string ts      = 'Trailing Stop'

if curStage == 1
    // Comment Strings

    if strategy.position_size > 0
        stopLevel := longSL
        comment   := close < long_close ? loss : win
        limit     := high>longTP ? high : na
    else
        stopLevel := shortSL
        comment   := close > short_close ? loss : win
        limit     := low<shortTP ? low : na
        
    strategy.exit('x', stop=stopLevel, limit=limit, comment=comment)

else if curStage == 2

    if strategy.position_size > 0
        stopLevel := long_close + (syminfo.mintick * i_ticks)
        comment   := close <= longTS ? bkeven : win
        limit     := high>longTP ? high : na
    else
        stopLevel := short_close - (syminfo.mintick * i_ticks)
        comment   := close >= shortTS ? bkeven : win
        limit     := low<shortTP ? low : na

    strategy.exit('x', stop=stopLevel, limit=limit, comment=comment)

else if curStage == 3

    if strategy.position_size > 0
        stopLevel := longTS
        comment   := close >= longTS and close<longTP ? ts : win
        limit     := high>longTP ? high : na
    else
        stopLevel := shortTS
        comment   := close <= shortTS and close>shortTP ? ts : win
        limit     := low<shortTP ? low : na

    strategy.exit('x', stop=stopLevel, limit=limit, comment=comment)

else
    strategy.cancel('x')
