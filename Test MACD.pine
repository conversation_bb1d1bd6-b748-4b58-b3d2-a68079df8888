//@version=5
strategy('TEST MACD DEFAULT', shorttitle='MACD', overlay=false, precision=4, initial_capital=10000, default_qty_type=strategy.cash, default_qty_value=10, currency=currency.USD, process_orders_on_close=true, pyramiding=0,  max_labels_count=500)
i_pctStop = input(1., '% of Risk to Starting Equity Use to Size Positions') / 100
i_tpFactor = input(1.5, 'Factor of stop determining target profit')
i_equity = input.string("Initial", options=["Initial", "Equity"], title="Initial or Equity")
i_bkcandles = input.int(11, title="Lowerest range - Number of candles")

// Save the strat's equity on the first bar, which is equal to initial capital.
var initialCapital = strategy.equity

// MACD
[macdLine, signalLine, _] = ta.macd(close, 12, 26, 9)
plot(macdLine,color=color.blue)
plot(signalLine,color=color.yellow)

// EMA 200
ema = ta.ema(close, 200)
plot(ema, title='EMA 200', color=color.new(color.yellow, 0), linewidth=2)

// LONG CONDITIONS
longCheckCondition = ta.barssince(ta.crossover(macdLine, signalLine))
longCondition1 = longCheckCondition <= 3 ? true : false
longCondition2 = macdLine < 0 and signalLine < 0
longCondition3 = close > ema
longCondition = longCondition1 and longCondition2 and longCondition3 and strategy.opentrades == 0

// STOP LOSS
atr_src = input.string('wicks', title='Close or Wicks', options=['close', 'wicks'])
Multip = input.float(defval=1.5, title='Multiplier')
show_sl = input.bool(true,title="Show SL")
atr_stop()=>
    atr_len = 14
    ATR = ta.atr(atr_len)
    Shortstop = (atr_src=='close' ? close : high) + ATR * Multip
    Longstop = (atr_src=='close' ? close : low) - ATR * Multip
    [Shortstop, Longstop]

[Shortstop, Longstop] = atr_stop()


float longSL = na
i_sl_type = input.string("Lowest", options=["Lowest","ATR"])
if i_sl_type == "Lowest"
    longSL := longCondition ? ta.lowest(low, i_bkcandles)[1] : longSL[1]
if i_sl_type == "ATR"

    longSL := longCondition ? Longstop : longSL[1]

plot(show_sl ? Shortstop : na, title="Stop Short")
plot(show_sl ? Longstop : na, title="Long Short")

// TAKE PROFIT
longEntryPrice = close
longDiffSL = math.abs(longEntryPrice - longSL)
float longTP = na
longTP := longCondition ? close + i_tpFactor * longDiffSL : longTP[1]

positionValue = (i_equity=="Initial" ? strategy.initial_capital : strategy.equity) * i_pctStop / (longDiffSL / longEntryPrice)
positionSize = positionValue / longEntryPrice

// ENTRY/EXIT
if longCondition
    strategy.entry('LONG', strategy.long, qty=positionSize)
    strategy.exit('EXIT LONG', 'LONG', stop=longSL, limit=longTP)
    buy = label.new(x=time,y=longTP,text=str.tostring(strategy.equity) ,xloc=xloc.bar_time,color=color.green,textcolor=color.white, size=size.normal,style=label.style_label_down)


// PLOT STOP LOSS
longPlotSL = strategy.opentrades > 0 and strategy.position_size > 0 ? longSL : na
plot(longPlotSL, title='LONG STOP LOSS', linewidth=2, style=plot.style_linebr, color=color.new(color.red, 0))

// PLOT TAKE PROFIT
longPlotTP = strategy.opentrades > 0 and strategy.position_size > 0 ? longTP : na
plot(longPlotTP, title='LONG TAKE PROFIT', linewidth=2, style=plot.style_linebr, color=color.new(color.green, 0))

