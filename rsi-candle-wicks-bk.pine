//@version=4
//@author=smileBTC
study("RSI Candles with Wicks - func", shorttitle="RSIC - func", precision=0)
////based on RSIC [cI8DH]
////added wicks

std         = input(false, title="Show Standard RSI")
rsi_candles     = input(true,  title="Show Candles")
wicks       = input(true,  title="Wicks based on stand-alone RSI")
src_close   = close
src_open    = open
src_high    = high
src_low     = low 
len1         = input(25, minval=1, title="Length")
len2         = input(5, minval=1, title="Length")
orange      = #ff9800
yellow      = #ffee58
violet      = #814dff
aqua        = #43d3ff
teal        = #36fcf6
blue        = #0053ff
lime        = #50ff00

rsi_wicks(rsi_len) =>
    norm_close  = avg(src_close,src_close[1])
    gain_loss_close   = change(src_close)/norm_close
    RSI_close         = 50+50*rma(gain_loss_close, rsi_len)/rma(abs(gain_loss_close), rsi_len)

    norm_open = if wicks==true 
        avg(src_open,src_open[1])
    else 
        avg(src_close,src_close[1])
    gain_loss_open   = change(src_open)/norm_open
    RSI_open         = 50+50*rma(gain_loss_open, rsi_len)/rma(abs(gain_loss_open), rsi_len)
            
    norm_high = if wicks==true 
        avg(src_high,src_high[1])
    else 
        avg(src_close,src_close[1])
    gain_loss_high   = change(src_high)/norm_high
    RSI_high         = 50+50*rma(gain_loss_high, rsi_len)/rma(abs(gain_loss_high), rsi_len)
            
    norm_low  = if wicks==true
        avg(src_low,src_low[1])
    else 
        avg(src_close,src_close[1])
    gain_loss_low   = change(src_low)/norm_low
    RSI_low         = 50+50*rma(gain_loss_low, rsi_len)/rma(abs(gain_loss_low), rsi_len)

    [RSI_open,RSI_close,RSI_high,RSI_low]

[RSI_open,RSI_close,RSI_high,RSI_low] = rsi_wicks(len1)
[RSI_open_c,RSI_close_c,RSI_high_c,RSI_low_c] = rsi_wicks(len2)

rsi_color = RSI_close > RSI_close[1] ? #65D25Bff : #FE6B69ff
plotcandle(rsi_candles?RSI_open:na, rsi_candles?RSI_high:na, rsi_candles?RSI_low:na, rsi_candles?RSI_close:na, title='RSI', color = rsi_color)
plot(RSI_open,title="RSI open",color=color.new(color.blue,100))
plot(RSI_high,title="RSI high",color=color.new(color.blue,100))
plot(RSI_low,title="RSI low",color=color.new(color.blue,100))
plot(RSI_close,title="RSI close",color=color.new(color.blue,100))

// Mid
plotshape(RSI_close>70 and RSI_open<70 and close>open?1:na ,title="Up Mid",color=orange,style=shape.circle,location=location.top)
// Strong
plotshape(RSI_open>69 and close>open?1:na ,title="Up High",color=#ff0000,style=shape.circle,location=location.top)
// Weak
plotshape(RSI_close<70 and RSI_high>70 and close>open?1:na ,title="Up Weak",color=yellow,style=shape.circle,location=location.top)

// Weak
plotshape(RSI_close>30 and RSI_low<30 and close<open?1:na ,title="Low weak",color=violet ,style=shape.circle,location=location.bottom)
// Mid
plotshape(RSI_close<30 and RSI_open>30 and close<open?1:na ,title="Low Mid",color=blue,style=shape.circle,location=location.bottom)
// Strong
plotshape(RSI_open<30 and close<open?1:na ,title="Low strong",color=lime,style=shape.circle,location=location.bottom)

plot(std ? RSI_close : na, color=#a64d79ff, title="RSI", join=true)
b1 = hline(30, color=#a64d7933, linestyle=hline.style_dotted, title = "Oversold")
c = hline(50, color=color.new(#ffffff,50), linestyle=hline.style_dotted, title = "Center")
b2 = hline(70, color=#a64d7933, linestyle=hline.style_dotted, title = "Overbought")
fill(b1,b2,color=#5b9cf6, title="RSI Band")
