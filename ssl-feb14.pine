//@version=4
study("SSL - Feb 14 ", overlay=true)

show_fill = input(title="Show Fill", type=input.bool, defval=true)
show_conv = input(title="Show Conv", type=input.bool, defval=true)
conv_amount = input(defval=7, title="Convergence Amount", type=input.float, step=1 )
line_input = input(1, title="Line width", type=input.integer )
c_type = input(title="Type", defval="NZD", options=
 ["NZD", "JPY" ])

red = #ff0062
orange = #ff9800
yellow = #FFFF00
green = #4caf50
lime = #00E676
aqua = #00bcd4
blue = #42a5f5
dark_blue = #2962ff
violet = #814dff
white = #ffffff

angle(_src, len) =>
    rad2degree=180/3.14159265359  //pi 
    ang=rad2degree*atan((_src[0] - _src[1])/atr(len))

angle_input = input(title="Angle Amount",type=input.integer, defval=14)

e200 = ema(close, 230)
e200_a = angle(e200,14)


// ===  SSL ===
// ==================================================
show_ema = input(title="EMA", type=input.bool, defval=false)
show_s1 = input(title="S1", type=input.bool, defval=true)
show_s2 = input(title="S2", type=input.bool, defval=false)
show_s3 = input(title="S3", type=input.bool, defval=false)
show_s4 = input(title="S4", type=input.bool, defval=false)
show_s5 = input(title="S5", type=input.bool, defval=true)
show_s6 = input(title="S6", type=input.bool, defval=true)
show_s7 = input(title="S7", type=input.bool, defval=true)
show_s8 = input(title="S8", type=input.bool, defval=true)

ssl_len1 = input(20, minval=1,title="SSL 1") // 45
ssl_len2 = input(38, minval=1,title="SSL 2") //75
ssl_len3 = input(8, minval=1,title="SSL 3") //
ssl_len3_b = input(15, minval=1,title="SSL 3 b") // 8 7
ssl_len4 = input(75, minval=1,title="SSL 4") // 75 100
ssl_len5 = input(100, minval=1,title="SSL 5") // 150
ssl_len6 = input(200, minval=1,title="SSL 6") // 150
ssl_len7 = input(300, minval=1,title="SSL 7") // 150
ssl_len8 = input(500, minval=1,title="SSL 8") // 150

trans = input(50,title="Transparency")

s_lines(len) =>
    result = wma(2*wma(close, len/2)-wma(close, len), round(sqrt(len)))

//s1 = wma(2*wma(close, ssl_len1/2)-wma(close, ssl_len1), round(sqrt(ssl_len1)))
s1 = s_lines(ssl_len1)
s2 = s_lines(ssl_len2)
s3 = s_lines(ssl_len3)
s3b = s_lines(ssl_len3_b)
s4 = s_lines(ssl_len4)
s5 = s_lines(ssl_len5)
s6 = s_lines(ssl_len6)
s7 = s_lines(ssl_len7)
s8 = s_lines(ssl_len8)
// Angles
s1_a = angle(s1,angle_input)
s3_a = angle(s3,angle_input)
s4_a = angle(s4,angle_input)
s5_a = angle(s5,angle_input)
s6_a = angle(s6,angle_input)
s7_a = angle(s7,angle_input)
s8_a = angle(s8,angle_input)

s1_f = plot(show_s1?s1:na, color=white , title="SSL 1", linewidth=line_input)
//s2_f = plot(show_s2?s2:na, color=orange , title="SSL 2")
s3_f = plot(show_s3?s3:na, color=aqua , title="SSL 3")
//s3b_f = plot(show_s3?s3b:na, color=#2962ff , title="SSL 3B")
s4_f = plot(show_s4?s4:na, color=s4_a>0?yellow:orange , title="SSL 4",linewidth=line_input)
s5_f =plot(show_s5?s5:na, color=s5_a<0?red:orange , title="SSL 5",linewidth=line_input)
s6_f =plot(show_s6?s6:na, color=s6_a>0?#2962ff:aqua , title="SSL 6",linewidth=line_input)
s7_f =plot(show_s7?s7:na, color=s7_a<0?green:red , title="SSL 7",linewidth=line_input)
s8_f =plot(show_s8?s8:na, color=s8_a<0?dark_blue:aqua , title="SSL 8",linewidth=line_input)

// Angles
plot(show_ema?e200:na,title="E200",color=e200_a>0?color.new(aqua,0):color.new(red,0))
plot(e200_a,title="EA",color=e200_a>0?color.new(aqua,100):color.new(red,100),style=plot.style_circles)
plot(show_s1?s1_a:na,title="S1 A",color=s1_a>0?color.new(aqua,100):color.new(red,100),style=plot.style_circles)
plot(show_s3?s3_a:na,title="S3 A",color=s3_a>0?color.new(aqua,100):color.new(red,100),style=plot.style_circles)
plot(show_s4?s4_a:na,title="S4 A",color=s4_a>0?color.new(aqua,100):color.new(red,100),style=plot.style_circles)
plot(show_s5?s5_a:na,title="S5 A",color=s5_a>0?color.new(aqua,100):color.new(red,100),style=plot.style_circles)
plot(show_s6?s6_a:na,title="S6 A",color=s6_a>0?color.new(aqua,100):color.new(red,100),style=plot.style_circles)
plot(show_s7?s7_a:na,title="S7 A",color=s7_a>0?color.new(aqua,100):color.new(red,100),style=plot.style_circles)
plot(show_s8?s8_a:na,title="S8 A",color=s8_a>0?color.new(aqua,100):color.new(red,100),style=plot.style_circles)


// Diff
boost = c_type=="JPY"?100 : 1000
s1_s3_diff = (s1 - s3) * boost
s1_s4_diff = (s1 - s4) * boost
s4_s5_diff = (s4 - s5) * boost
s5_s6_diff = (s5 - s6) * boost
//fill(s1_f,s3_f,title="S1/S3 Fill", color=show_fill and s1_s3_diff<1?color.new(blue,70) : show_fill?  color.new(aqua,70):na)
//fill(s1_f,s4_f,title="S1/S4 Fill", color=show_fill and s1_s4_diff<1?color.new(blue,70) : show_fill ? color.new(aqua,70):na)
//fill(s4_f,s5_f,title="S4/S5 Fill", color=show_fill and s4_s5_diff<1?color.new(blue,70) : show_fill?  color.new(aqua,70):na)
fill(s5_f,s6_f,title="S5/S6 Fill", color=show_fill and s5_s6_diff<1?color.new(green,80): show_fill ? color.new(red,80):na)

//s1 s4 Conv
s1_s4_conv = show_conv and s1_s4_diff<5 and s1_s4_diff>(5 * -1) ? true : false
//fill(s1_f,s4_f,title="S1/S4 Conv", color=s1_s4_conv and s1>s4?color.new(red,70): s1_s4_conv and s1<s4?color.new(green,70) : na)
//s4 s5 Conv
s4_s5_conv = show_conv and s4_s5_diff<2 and s4_s5_diff>(2 * -1) ? true : false
//fill(s4_f,s5_f,title="S4/S5 Conv", color=s4_s5_conv and s4>s5?color.new(red,70): s4_s5_conv and s4<s5?color.new(green,70) : na)
//s5 s6 Conv
s5_s6_conv = show_conv and s5_s6_diff<conv_amount and s5_s6_diff>(conv_amount * -1) ? true : false
fill(s5_f,s6_f,title="S5/S6 Conv", color=s5_s6_conv and s5>s6?color.new(red,70): s5_s6_conv and s5<s6?color.new(green,70) : na)
plot(s5_s6_diff,title="S5/S6 Conv",color=color.new(orange, 100), style=plot.style_circles)


//plot(s1_s4_diff,title='S1 S4 Diff Diff', color=s4_a>0?color.new(red,100):color.new(green,100),style=plot.style_circles)
//plot(s4_s5_diff,title='S4 S5 Diff', color=s5_a>0?color.new(red,100):color.new(green,100),style=plot.style_circles)
plot(s5_s6_diff,title='S5 S6 Diff', color=s5_a>0?color.new(red,100):color.new(green,100),style=plot.style_circles)
// s5_col = s5>bb_upper?#ff0000 : s5<bb_lower ? #00ddff : green