//@version=5
strategy('Marks Strategy', overlay=true, precision=4, initial_capital=10000, default_qty_type=strategy.fixed, default_qty_value=10, currency=currency.USD, process_orders_on_close=true, pyramiding=1,  max_labels_count=500)

red = #ff0062
orange = #ff9800
yellow = #FFFF00
green = #4caf50
lime = #00E676
aqua = #00bcd4
blue = #2962ff
dark_blue = #2962ff
violet = #814dff
white = #ffffff
gray = #707070

// Angle
angle(_src, len) =>
    rad2degree = 180 / 3.14159265359  //pi 
    ang = rad2degree * math.atan((_src[0] - _src[1]) / ta.atr(len))
    ang

// Change
perc_change() =>
    perc = math.abs((1 - close[1] / close) * 10000)
    perc


g_1min = '1 Minute MA\'s ----------------------------------------------------'
inl_1min = '1-min'
inl_1min_len = '1-min-len'
show_ma_1min = input.bool(title='Show', defval=false, group=g_1min, inline=inl_1min)
m_1min_len1 = input.int(8, minval=1, title='M1', inline=inl_1min_len) 
m_1min_len2 = input.int(20, minval=1, title='M2', inline=inl_1min_len) 
m_1min_len3 = input.int(50, minval=1, title='SMA', inline=inl_1min_len) 

ma_1min() =>
    src = close
    m1 = ta.hma(src,m_1min_len1)
    m2 = ta.hma(src,m_1min_len2)
    m3 = ta.sma(src,m_1min_len3)

    [m1,m2,m3]

[m1f,m2f,m3f] = ma_1min()

plot(m1f and show_ma_1min?m1f:na,title="m1",color=#53bad1,linewidth=2)
plot(m2f and show_ma_1min?m2f:na,title="m2",color=#c04045,linewidth=2)
plot(m3f and show_ma_1min?m3f:na,title="m3",color=#fdea60,linewidth=2)



g_mom = 'Momentum ----------------------------------------------------'
length = input.int(1,group=g_mom) // 5 NAS 1 BTC
price = close
momentum(seria, length) =>
	mom = seria - seria[length]
	mom
mom0 = momentum(price, length)
mom1 = momentum( mom0, 1)




// ===  Fibo Trend ===
// ==================================================
g_fibo_trend = 'G Fibo Trend ----------------------------------------------------'
inl_fibo = 'inl-fib'
show_gfibo = false //input.bool(false,"Show G Fibo Trend",group=g_fibo_trend)
show_candles = input.bool(false,"Fibo Candles",group=g_fibo_trend)
BackStep = input.int(25,"Analysis Period") // 50
lowerValue = input.float(0.382,"Lower Fibonacci Level",options=[0.236, 0.382, 0.50, 0.618, 0.786])
upperValue = input.float(0.618,"Upper Fibonacci Level",options=[0.236, 0.382, 0.50, 0.618, 0.786])
showFill = input.bool(true,"Show Filling")
changeCandle = input.bool(true,"Change Candle Color")
atr = ta.atr(200)
max = ta.highest(close,BackStep)
min = ta.lowest(close,BackStep)

lowerFib = min + (max-min)*lowerValue
upperFib = min + (max-min)*upperValue
ma_val = input.int(10, title="MA" ) // 6
ma = ta.wma(close,ma_val)

float closeVal = ma
float openVal = ma
color clrToUse = closeVal>upperFib and openVal>upperFib?green:closeVal<lowerFib and openVal<lowerFib?red:yellow

// maxLine = plot(max and show_gfibo?max : na,color=color.green,title="Max")
// minLine = plot(min and show_gfibo?min : na,color=color.red,title="Min")
// LowerFibLine = plot(lowerFib and show_gfibo?lowerFib : na,color=color.rgb(228, 255, 75, 20),title="Lower Fib")
// UpperFibLine = plot(upperFib and show_gfibo?upperFib : na,color=color.rgb(228, 255, 75, 20),title="Upper Fib")
// fill(maxLine,UpperFibLine,color=showFill?color.rgb(0,255,0,changeCandle?95:70):na)
// fill(UpperFibLine,LowerFibLine,color=showFill?color.rgb(228, 255, 75, changeCandle?95:70):na)
// fill(LowerFibLine,minLine,color=showFill?color.rgb(255,0,0,changeCandle?95:70):na)
barcolor(show_candles? clrToUse : na)
//plotcandle(open,high,low,close,"Bar",color=changeCandle?clrToUse:na,wickcolor=changeCandle?clrToUse:na,bordercolor=changeCandle?clrToUse:na)

float LowerRetracement = (max-min)*0.318
float UpperRetracement = (max-min)*0.618




// ===  GMMA ===
// ==================================================
g_GMMA_OSC = 'GMMA OSC ----------------------------------------------------'
inl_g_GMMA_OSC = 'inl-g_GMMA_OSC'

// Use Alternate Anchor TF for MAs 
anchor = 0 //input.int(0, minval=0, maxval=1440, title='Use Alternate Anchor TimeFrame (0=none, max=1440 (mins,D,W)')
gmmaType = 'Guppy' //input.string('Guppy', title='Calculate Oscillator From Which GMMA Sets', options=['Guppy', 'SuperGuppy'])
smoothLen = input.int(3, minval=1, title='Oscillator Smoothing Length (1=none)', group=g_GMMA_OSC)  // 1
signalLen = input.int(13, minval=1, title='GMMA Oscillator Signal Length', group=g_GMMA_OSC)
showZones = input(false, title='Show Bullish/Bearish Zones', group=g_GMMA_OSC)
//
src = close //input(close, title='Source')
angle_amount = 14 //input.int(14, minval=1, title='Angle Len')
show_angles = input(title='Show Angles', defval=false)
use_zero_line = true //input(title='Zero Line', defval=true)

//Fast Guppy Avg EMA
GMMAFast(src, mult) =>
    ema1 = ta.ema(src, 3 * mult)
    ema2 = ta.ema(src, 5 * mult)
    ema3 = ta.ema(src, 8 * mult)
    ema4 = ta.ema(src, 10 * mult)
    ema5 = ta.ema(src, 12 * mult)
    ema6 = ta.ema(src, 15 * mult)
    return_1 = ema1 + ema2 + ema3 + ema4 + ema5 + ema6
    return_1

//Slow Guppy Avg EMA
GMMASlow(src, mult) =>
    ema7 = ta.ema(src, 30 * mult)
    ema8 = ta.ema(src, 35 * mult)
    ema9 = ta.ema(src, 40 * mult)
    ema10 = ta.ema(src, 45 * mult)
    ema11 = ta.ema(src, 50 * mult)
    ema12 = ta.ema(src, 60 * mult)
    return_2 = ema7 + ema8 + ema9 + ema10 + ema11 + ema12
    return_2

//Fast SuperGuppy Avg EMA
superGMMAFast(src, mult) =>
    emaF1 = ta.ema(src, 3 * mult)
    emaF2 = ta.ema(src, 5 * mult)
    emaF3 = ta.ema(src, 7 * mult)
    emaF4 = ta.ema(src, 9 * mult)
    emaF5 = ta.ema(src, 11 * mult)
    emaF6 = ta.ema(src, 13 * mult)
    emaF7 = ta.ema(src, 15 * mult)
    emaF8 = ta.ema(src, 17 * mult)
    emaF9 = ta.ema(src, 19 * mult)
    emaF10 = ta.ema(src, 21 * mult)
    emaF11 = ta.ema(src, 23 * mult)
    return_3 = (emaF1 + emaF2 + emaF3 + emaF4 + emaF5 + emaF6 + emaF7 + emaF8 + emaF9 + emaF10 + emaF11) / 11
    return_3

//Slow SuperGuppy Avg EMA
superGMMASlow(src, mult) =>
    emaS1 = ta.ema(src, 25 * mult)
    emaS2 = ta.ema(src, 28 * mult)
    emaS3 = ta.ema(src, 31 * mult)
    emaS4 = ta.ema(src, 34 * mult)
    emaS5 = ta.ema(src, 37 * mult)
    emaS6 = ta.ema(src, 40 * mult)
    emaS7 = ta.ema(src, 43 * mult)
    emaS8 = ta.ema(src, 46 * mult)
    emaS9 = ta.ema(src, 49 * mult)
    emaS10 = ta.ema(src, 52 * mult)
    emaS11 = ta.ema(src, 55 * mult)
    emaS12 = ta.ema(src, 58 * mult)
    emaS13 = ta.ema(src, 61 * mult)
    emaS14 = ta.ema(src, 64 * mult)
    emaS15 = ta.ema(src, 67 * mult)
    emaS16 = ta.ema(src, 70 * mult)
    // average
    return_4 = (emaS1 + emaS2 + emaS3 + emaS4 + emaS5 + emaS6 + emaS7 + emaS8 + emaS9 + emaS10 + emaS11 + emaS12 + emaS13 + emaS14 + emaS15 + emaS16) / 16
    return_4


// Calculate the Multiplier for Anchor MAs.
mult = not timeframe.isintraday or anchor == 0 or timeframe.multiplier <= 0 or timeframe.multiplier >= anchor or anchor > 1440 ? 1 : math.round(anchor / timeframe.multiplier) > 1 ? math.round(anchor / timeframe.multiplier) : 1
mult := timeframe.isintraday or anchor == 0 or timeframe.multiplier <= 0 or timeframe.multiplier >= anchor or anchor > 52 ? mult : math.round(anchor / timeframe.multiplier) > 1 ? math.round(anchor / timeframe.multiplier) : 1

// Select type of Oscillator calculation
gmmaFast = gmmaType == 'Guppy' ? GMMAFast(src, mult) : superGMMAFast(src, mult)
gmmaSlow = gmmaType == 'Guppy' ? GMMASlow(src, mult) : superGMMASlow(src, mult)

// Calculate Oscillator, Smoothed Osc and signal line
gmmaOscRaw = (gmmaFast - gmmaSlow) / gmmaSlow * 100
gmmaOsc = ta.sma(gmmaOscRaw, smoothLen)
gmmaSignal = ta.ema(gmmaOscRaw, signalLen)
gmmaClr = gmmaOsc < gmmaSignal ? red : gmmaOsc > gmmaSignal ? green : gray

// Angles 
gmmaOsc_a = show_angles ? angle(gmmaOsc, angle_amount) * 0.001 : na
gmmaSignal_a = show_angles ? angle(gmmaSignal, angle_amount) * 0.001 : na

// bullish signal rule: 
bullishRule = ta.crossover(gmmaOsc, gmmaSignal)
// bearish signal rule: 
bearishRule = ta.crossunder(gmmaOsc, gmmaSignal)
// current trading State
ruleState = 0
ruleState := bullishRule ? 1 : bearishRule ? -1 : nz(ruleState[1])


// === PLOTTING === 
// plot(gmmaOsc, title='GMMA OSC Smooth', style=plot.style_line, linewidth=2, color=color.new(gmmaClr,10) )
// plot(gmmaOsc_a, title='GMMA OSC Angle', style=plot.style_line, linewidth=0, color=color.new(gmmaClr,65) )
// plot(gmmaSignal, title='GMMA Signal', style=plot.style_line, linewidth=1, color=color.new(orange, 10))
// plot(gmmaSignal_a, title='GMMA Signa Angle', style=plot.style_line, linewidth=0, color=color.new(orange, 65))


//color.new(hline,90)(0, title='Zero line', linestyle=hline.style_dotted, linewidth=2, color=gray)
//color.new(hline,90)(0.23, title='High', linestyle=hline.style_dotted, linewidth=2, color=color.new(red, 50))
//color.new(hline,90)(-0.23, title='Low', linestyle=hline.style_dotted, linewidth=2, color=color.new(green, 50))
// === /PLOTTING === 

//
// ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ //

// === ALERTS === 

bgcolor(showZones ? ruleState == 1 ? color.new(green,90) : ruleState == -1 ? color.new(red,90) : color.new(gray,90) : na, title='Guppy Bullish/Bearish Zones')


// ===  MA's ===
// ==================================================

g_cb = 'MA\' s ----------------------------------------------------'
inl_cb = 'cb'
ma_type = input.string(title='Type', defval='ema', options=['sma', 'ema', 'dema', 'tema', 'wma', 'vwma', 'smma', 'rma', 'hma', 'lsma', 'McGinley'], group=g_cb)
show_m1 = input.bool(title='m1', defval=true, group=g_cb, inline=inl_cb)
show_m2 = input.bool(title='m2', defval=true, group=g_cb, inline=inl_cb)
show_m3 = input.bool(title='m3', defval=true, group=g_cb, inline=inl_cb)
show_m4 = input.bool(title='m4', defval=false, group=g_cb, inline=inl_cb)
show_m5 = input.bool(title='m5', defval=true, group=g_cb, inline=inl_cb)
show_m6 = input.bool(title='m6', defval=true, group=g_cb, inline=inl_cb)
show_m7 = input.bool(title='m7', defval=false, group=g_cb, inline=inl_cb)
show_m8 = input.bool(title='m8', defval=true, group=g_cb, inline=inl_cb)

inl_len = 'len1'
m_len1 = input.int(5, minval=1, title='M1', inline=inl_len)  // 8
m_len2 = input.int(20, minval=1, title='M2', inline=inl_len)  // 20
m_len3 = input.int(50, minval=1, title='M3', inline=inl_len)  // 50
m_len4 = input.int(75, minval=1, title='M4', inline=inl_len)  // 75 
m_len5 = input.int(100, minval=1, title='M5', inline=inl_len)  // 100
m_len6 = input.int(200, minval=1, title='M6', inline=inl_len)  // 200
m_len7 = input.int(300, minval=1, title='M7', inline=inl_len)  // 300
m_len8 = input.int(500, minval=1, title='M8', inline=inl_len)  // 500

g_fill = 'Plot'
inl_fill = 'fill'
inl_conv = 'conv'
show_fill = input.bool(title='Show Fill', defval=true, inline=inl_fill, group=g_fill)
show_conv = input.bool(title='Show Conv', defval=true, inline=inl_fill, group=g_fill)
conv_amount = input.float(defval=4, title='Conv Amount', step=1, inline=inl_conv, group=g_fill)
c_type = input.string(title='Type', defval='NAS', options=['NAS', 'USD', 'JPY'], inline=inl_conv, group=g_fill)
line_input = 1  //input(1, title="Line width", type=input.integer,inline=inl_fill )

g_multi = 'Multi Settings'
inl_multi = 'multi'
show_current = input.bool(title='Show Current', defval=false, inline=inl_multi, group=g_multi)
show_multi = input.bool(title='Show Multi', defval=true, inline=inl_multi, group=g_multi)
resCustom = input.timeframe(title='Timeframe', defval='15', group=g_multi)
candle_close = input.timeframe(title='Candle Close', defval='15', group=g_multi)

//useCurrentRes = input(true, title="Chart Resolution?",inline=inl_multi,group=g_multi)
angle_input =  input.int(1,title="Angle Amount") // 14
i_use_smooth = input.bool(false, title="Use Smooth")
i_smooth    = input.int(1, title="Smooth")

close_m = request.security(syminfo.tickerid, candle_close, close)


// Lines and Angles
ma_types(len) =>
    ma =0.0
    src = close
    if ma_type == 'sma' // Simple Moving Average
        ma := ta.sma(src,len)
    if ma_type == 'ema' // Exponential
        ma := ta.ema(src,len)
    if ma_type=="dema" // Double Exponential
        e = ta.ema(src, len)
        ma := 2 * e - ta.ema(e, len)
    if ma_type == 'tema' // Triple Exponential
        ema1 = ta.ema(src, len)
        ema2 = ta.ema(ema1, len)
        ema3 = ta.ema(ema2, len)
        ma := 3 * (ema1 - ema2) + ema3
    if ma_type == 'wma' // Weighted
        ma := ta.wma(src,len)
    if ma_type == 'vwma' // Volume Weighted
        ma := ta.vwma(src,len)
    if ma_type=="smma" // Smoothed
        w = ta.wma(src, len)
        ma := na(w[1]) ? ta.sma(src, len) : (w[1] * (len - 1) + src) / len
    if ma_type == "rma"
        ma := ta.rma(src, len)
    if ma_type == 'hma' // Hull
        ma := ta.wma(2*ta.wma(src, len/2)-ta.wma(src, len), math.floor(math.sqrt(len) ))
    if ma_type=="lsma" // Least Squares
        ma := ta.linreg(src, len, 0)
    if ma_type=="McGinley"
        mg = 0.0
        mg := na(mg[1]) ? ta.ema(src, len) : mg[1] + (src - mg[1]) / (len * math.pow(src/mg[1], 4))
        ma :=mg

    if i_use_smooth
        ma := ta.sma(ma,i_smooth)

    ma


m1   = request.security(syminfo.tickerid, resCustom, ma_types(m_len1),gaps=barmerge.gaps_on )
m1_a = request.security(syminfo.tickerid, resCustom, angle(m1,1) )
m2   = request.security(syminfo.tickerid, resCustom, ma_types(m_len2) )
m2_a = request.security(syminfo.tickerid, resCustom, angle(m2,1) )
m3   = request.security(syminfo.tickerid, resCustom, ma_types(m_len3) )
m3_a = request.security(syminfo.tickerid, resCustom, angle(m3,1) )
m4   = request.security(syminfo.tickerid, resCustom, ma_types(m_len4) )
m4_a = request.security(syminfo.tickerid, resCustom, angle(m4,1) )
m5   = request.security(syminfo.tickerid, resCustom, ma_types(m_len5) )
m5_a = request.security(syminfo.tickerid, resCustom, angle(m5,1) )
m6   = request.security(syminfo.tickerid, resCustom, ma_types(m_len6) )
m6_a = request.security(syminfo.tickerid, resCustom, angle(m6,1) )
m7   = request.security(syminfo.tickerid, resCustom, ma_types(m_len7) )
m7_a = request.security(syminfo.tickerid, resCustom, angle(m7,1) )
m8   = request.security(syminfo.tickerid, resCustom, ma_types(m_len8) )
m8_a = request.security(syminfo.tickerid, resCustom, angle(m8,1) )


// Diff and Convergence
ma_conv(t1, t2) =>
    
    float boost = switch syminfo.ticker
        "GBPUSD" => 10000
        "EURUSD" => 10000
        "GBPJPY" => 100
        "BTCUSD" => 0.1
        "NAS100" => 1
        "NDQ100" => 1
        "GOLD"   => 10
        "SILVER" => 100
        => 1

    diff = (t1 - t2) * boost
    conv = show_conv and diff < conv_amount and diff > conv_amount * -1 ? true : false
    [diff, conv]

[m2_m4_diff, m2_m4_conv] = ma_conv(m2, m4)
[m4_m5_diff, m4_m5_conv] = ma_conv(m4, m5)
[m5_m6_diff, m5_m6_conv] = ma_conv(m5, m6)
[m7_m8_diff, m7_m8_conv] = ma_conv(m7, m8)


// Plot

if ta.change(close_m)
    m1_a_text = label.new(x=bar_index, y=close,yloc=yloc.abovebar, style=label.style_label_center, color=color.white,textcolor=color.red, size=size.normal,text=str.tostring(m1_a, '#.##') )

//plotshape(ta.change(close_m),title="Close Multi", color=#ff0000, style=shape.triangleup, location=location.top)
m1_f = plot(show_current and show_m1 ? m1: na, color=m1_a>0?green:red, title='M1', linewidth=line_input)
m2_f = plot(show_current and show_m2 ? m2: na, color=color.new(white, 0), title='M2', linewidth=2)
m3_f = plot(show_current and show_m3 ? m3: na, color=color.new(blue, 0), title='M3')
m4_f = plot(show_current and show_m4 ? m4: na, color=m4_a > 0 ? yellow : orange, title='M4', linewidth=line_input)
m5_f = plot(show_current and show_m5 ? m5: na, title='M5', linewidth=line_input, color=m5_a > 0 ? orange : m5_a < 0 and m5 > m6 ? red : green)
m6_f = plot(show_current and show_m6 ? m6: na, title='M6', linewidth=line_input, color=m6_a > 0 ? red : lime)
m7_f = plot(show_current and show_m7 ? m7: na, title='M7', linewidth=line_input, color=m7_a > 0 ? orange : m7_a < 0 and m7 > m8 ? red : green)
m8_f = plot(show_current and show_m8 ? m8: na, title='M8', linewidth=line_input, color=m8_a > 0 ? red : lime)

// Plot Angles
plot(show_m1 ? m1_a : na, color=m1_a>0?color.new(green,100):color.new(red,100), title='M1 A', linewidth=0)
plot(show_m2 ? m2_a: na, title='M2 A', color=m2_a> 0 ? color.new(red, 100) : color.new(lime, 100))
m8_f_a = plot(show_m8 ? m8_a : na, title='m8 Multi A', color=m8_a > 0 ? color.new(red, 100) : color.new(lime, 100))

// Fills
fill(m5_f, m6_f, title='m5/m6 Fill Multi', color=show_fill and m5_m6_diff < 1 ? color.new(green, 90) : show_fill ? color.new(red, 90) : na)
fill(m7_f, m8_f, title='m7/m8 Fill Multi', color=show_fill and m7_m8_diff < 1 ? color.new(green, 90) : show_fill ? color.new(red, 90) : na)
// Conv
fill(m2_f, m4_f, title='m2/m4 Conv Multi', color=m2_m4_conv and m2 > m4 ? color.new(red, 70) : m2_m4_conv and m2 < m4 ? color.new(green, 70) : na)
fill(m5_f, m6_f, title='m5/m6 Conv Multi', color=m5_m6_conv and m5 > m6 ? color.new(red, 70) : m5_m6_conv and m5 < m6 ? color.new(green, 70) : na)
fill(m7_f, m8_f, title='m7/m8 Conv Multi', color=m7_m8_conv and m7 > m8 ? color.new(red, 70) : m7_m8_conv and m7 < m8 ? color.new(green, 70) : na)
plot(m5_m6_diff, title='m5/m6 Diff Multi', linewidth=0, color=color.new(blue, 100))

// Previous State
var state_change = blue
ss_red2 = m5 > m6 and m5_a > 0
ss_orange2 = m5 > m6 and m5_a < 0
ss_lime2 = m5 < m6 and m6_a > 0
ss_green2 = m5 < m6 and m6_a < 0
m5_m6_c = ss_red2 ? red : ss_orange2 ? orange : ss_lime2 ? lime : ss_green2 ? green : na




// ===  Bollinger Bands ===
// ==================================================
// 
BB_length = 20 //input(250, title='BB Len')
basis = ta.sma(close, BB_length)
dev = 2 * ta.stdev(close, BB_length)
bb_upper = basis + dev
bb_lower = basis - dev
bb_spread = bb_upper - bb_lower
//avgspread = sma(bb_spread, sqz_length)
// plot(basis,title="Basis")
// plot(bb_upper,title="bb_upper")
// plot(bb_lower,title="bb_lower")
bb_cond = m1 < bb_lower ? 1 : m1 > bb_upper ? -1 : na
barcolor(bb_cond == 1 ? aqua : bb_cond == -1 ? orange : na)




g_trading = 'Trading ----------------------------------------------------'
inl_stoploss = 'inl-stoploss'
inl_sessions = 'inl-sessions'
account_size = input.int(50000, minval=1000, title='Account Size',group=g_trading)
show_st = input.bool(title='Show Stop Loss', defval=false, group=g_trading)
atr_mult = input.float(0.85, 'ATR Mult', step=0.01, group=g_trading, inline=inl_stoploss) // 1.5
atr_src = input.string('close', title='Close or Wicks', options=['close', 'wicks'],group=g_trading, inline=inl_stoploss)


// Sessions
show_sessions = input.bool(false,title='Sessions', group=g_trading)
As = "1800-0300" //input.session(title="Asia", defval="1800-0300")
Lon = "0300-1200" //input.session(title="London", defval="0300-1200")
Ny = "0800-1700" //input.session(title="New York", defval="0800-1800")
Dz = "1715-2030" //input.session(title="Deadzone", defval="1715-2030")

inl_color = "inl_color"
inl_color2 = "inl_color2"
c1_on = false //input.bool(true,title="", group=g_trading,inline=inl_color)
c1 = color.new(#00bcd4,90) //input.color(title="Asia", defval=#00bcd4,group=g_trading,inline=inl_color)
c2_on = true //input.bool(true,title="",group=g_trading,inline=inl_color)
c2 = color.new(#00796b,90) //input.color(title="London", defval=#00796b,group=g_trading,inline=inl_color)
c3_on = true //input.bool(true,title="",group=g_trading,inline=inl_color2)
c3 = color.new(#b71c1c,90) //input.color(title="New York", defval=#b71c1c,group=g_trading,inline=inl_color2)
c4_on = false //input.bool(true,title="", group=g_trading,inline=inl_color2)
c4 = color.new(color.purple,90) //input.color(title="Deadzone", defval=#b71c1c,group=g_trading,inline=inl_color2)

Session(sess) => na(time("2",sess)) == false
Asia = Session(As) and c1_on and show_sessions? c1 : na
London = Session(Lon) and c2_on and show_sessions ? c2 : na
NewYork = Session(Ny) and c3_on and show_sessions ? c3 : na
Deadzone = Session(Dz) and c4_on and show_sessions ? c4 : na
bgcolor(Asia)
bgcolor(London)
bgcolor(NewYork)
//bgcolor(Deadzone)





// ===  Trailing Stop ===
// ==================================================
atrlen = 14  //input(14, "ATR Period")
atr_slen = ta.atr(atrlen)
atr_upper = atr_slen * atr_mult + (atr_src=='close' ? close : high)
atr_lower = (atr_src=='close' ? close : low) - atr_slen * atr_mult

// ATR
plot(show_st ? atr_upper : na, '+ATR Upper', color=color.new(#ffffff, 80))
plot(show_st ? atr_lower : na, '-ATR Lower', color=color.new(#ffffff, 80))


// ===  Position Size ===
// ==================================================
upper_band = atr_slen * atr_mult + close
lower_band = close - atr_slen * atr_mult
calc_trade_values() =>
    p = 0.00
    rp = 0.00
    sl = 0.00
    pips = 0.00
    p_value = 0.00
    trade_dir = 0

    // Sell
    if trade_dir < 0
        sl := upper_band
        rp := (1 - upper_band / close) * 100
        p := 2 / rp * account_size
        p
    else
    // Buy
        sl := lower_band
        rp := (1 - lower_band / close) * 100
        p := 2 / rp * account_size
        p

    p := math.round(math.abs(p))
    rp := math.abs(rp)
    pips := math.abs((close - sl) * 10000)
    p_value := account_size * rp / pips

    [p, rp, sl, pips, p_value]


trade_dir() =>
    candle  = close>open ? 1 : 0
    dir = 0
    //flag_up = 0
    //flag_down = 0

    if mom0 > 0 and mom1 > 0
        dir := 1
    if mom0 < 0 and mom1 < 0
        dir := -1

    //flag_amount = m6_a>20 ? // maybe use BB bands below or above 0 line
    // flag_up := m1_a>20 ? 1  : flag_up==1  and m1_a>0 ? 1  : flag_up==1  and m1_a<0 ? 0 : na
    // flag_down := m1_a<-10 ? -1 : flag_down==-1 and m1_a<0 ? -1 : flag_down==-1 and m1_a>0 ? 0 : na

    // if flag_up == 0
    //     dir := -1

    // if flag_down == 0
    //     dir := 1

    dir

direction = trade_dir()

// var int flag_u = 0
// var int flag_d = 0
// flag_u := m1_a[1]>20 ? 1  : flag_u==1  and m1_a[1]>0 ? 1  : flag_u==1  and m1_a[1]<0 ? 0 : na
// flag_d := m1_a[1]<-10 ? -1 : flag_d==-1 and m1_a[1]<0 ? -1 : flag_d==-1 and m1_a[1]>0 ? 0 : na
// plotshape(flag_u==0 ? 1 : na,title="Flag Up",color=red ,style=shape.circle,location=location.top)
// plotshape(flag_d==0 ? 1 : na,title="Flag Down",color=green ,style=shape.circle,location=location.bottom)


cd=close>open?1:0
if (direction==1)
	strategy.entry("MomLE", strategy.long,stop= cd==1 ?high+syminfo.mintick:low-syminfo.mintick, comment="MomLE")
else
	strategy.cancel("MomLE")
if (direction==-1)
	//strategy.close(id="MomLE",comment="Close \n MomLE ")
	strategy.entry("MomSE", strategy.short, stop=low-syminfo.mintick, comment="MomSE")
else
	strategy.cancel("MomSE")

plotshape(direction==-1, title='Sell 1', color=red, style=shape.circle, location=location.top)
plotshape(direction==1, title='Buy', color=lime, style=shape.circle, location=location.bottom)
plot(direction==-1?1:na, title="Flag Up")
plot(direction==1?1:na, title="Flag Down")

// === Labels ===
// label_txt = "Equity: " + str.tostring(account_size)
// if barstate.islastconfirmedhistory
//     buy = label.new(x=bar_index + 20, y=close, style=label.style_label_left,color=color.blue, textcolor=color.white, size=size.normal,
//      text="Exchange: " + str.tostring(syminfo.prefix)+ "\nSymbol: " + str.tostring(syminfo.ticker) )

// === Alerts ===

// Entries

sell_cond3 = m3_a < 0 and m4_a < 0 and m7 > m8 and close > m3 and not(m2_a > 0) and not(m7_a < 0) ? 1 : na
sell_cond4 = m3_a < 0 and m4_a < 0 and m7 > m8 and close > m4 and not(m7_a > 0) ? 1 : na
//plotshape(sell_cond, title='Sell 1', color=color.new(red, 0), style=shape.circle, location=location.top)
//plotshape(sell_cond2, title='Sell 1', color=color.new(orange, 0), style=shape.circle, location=location.top)

buy_cond = m1_a>-10 and m1_a > m1_a[1] and m1_a<5
//plotshape(buy_cond, title='Buy 1', color=green, style=shape.circle, location=location.bottom)


buy1_cond3 = m5_a>0 and m5>m6 and close<m2 and clrToUse!=green
 and not(m2_a<0)
 and not(close>m3) ? 1 : na


buy2_cond4 = m5_a>0 and m5>m6 and close<m6 and clrToUse!=green 
 and not(m2>m3) ? 1 : na
//plotshape(buy2_cond, title='Buy 2', color=lime, style=shape.circle, location=location.bottom)

buy3_cond5 = m5>m6 and mom0 > 0 and mom1 > 0 and clrToUse!=green and close<m2
//plotshape(buy3_cond, title='Buy 3', color=aqua, style=shape.circle, location=location.bottom)

