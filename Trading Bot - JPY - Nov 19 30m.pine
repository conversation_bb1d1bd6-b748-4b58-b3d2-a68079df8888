//@version=4
study(title = "Trading Bot - JPY - Nov 19 30m", shorttitle="TB - JPY - 11_19 - 30M",overlay=true)

show_entry = input(title="Show Entry", type=input.bool, defval=true)
show_atr = input(title="Show ATR", type=input.bool, defval=true)
show_cond = input(title="Show Conditions", type=input.bool, defval=true)
use_logic = input(title="Use Logic", type=input.bool, defval=false)
show_friday= input(title="Show Friday", type=input.bool, defval=true)
rsi_strong= input(title="Show only RSI strongest", type=input.bool, defval=false)
rsi1_show = input(title="Show RSI1", type=input.bool, defval=false)
rsi2_show = input(title="Show RSI2", type=input.bool, defval=true)
rsi3_show = input(title="Show RSI3", type=input.bool, defval=false)
switch_bars = input(title="RSI candles switch",type=input.bool,defval=false)
show_r3 = input(title="Show R3 down",type=input.bool,defval=false)

red = #ff0062
aqua = #00bcd4
yellow = #FFFF00
orange = #ff9800
green = #4caf50
lime = #00E676
white = #ffffff
c_hide = color.new(#ffffff,100)
blue = #42a5f5
violet = #814dff
gray = #707070
black = #000000
sell_color = color.new(#ff0062,20)
buy_color = color.new(#00c3ff,20)

friday = color.new(sell_color,92)
bgcolor(show_friday and dayofweek == 1 ? friday : na)

angle(_src, len) =>
    rad2degree=180/3.14159265359  //pi 
    ang=rad2degree*atan((_src[0] - _src[1])/atr(len))

// Change
perc_change() =>
    perc = abs( (1 - (close[1] / close)) * 10000 )


// ===  EMA's ===
// ==================================================
ema_200 = ema(close, 200)
ema_200_angle = angle(ema_200,2)


// ===  ATR ===
// ==================================================
atrlen = 14 //input(14, "ATR Period")
atr_mult = input(2.0, "ATR Mult", step = 0.1) // 1.35 1.15
atr_stop = 0.0003 //input(0.0003, "ATR Stop", step = 0.0001) // 0.001
atr_slen = wma(tr(true), atrlen)
atr_upper = atr_slen * atr_mult + close
atr_lower = close - atr_slen * atr_mult


// ===  Kijun ===
// ==================================================
middleDonchian(Length) =>
    lower = lowest(Length)
    upper = highest(Length)
    avg(upper, lower)

basePeriods = input(5, title="kijun Len",minval=1) // 14 26
kijun =  middleDonchian(basePeriods)
k_angle = angle(kijun,3)
xChikou = close
xPrice = close


// ===  BB ===
// ==================================================
BB_length = input(20) // 45 100 125 45 23
BB_stdDev = 2 //input(2, minval=2.0, maxval=3)
sqz_length = 100 //input(100, minval=2, maxval=200) // 100
sqz_threshold = 76 
bb_s = kijun
basis = sma(close, BB_length)
dev = BB_stdDev * stdev(close, BB_length)
bb_upper = basis + dev
bb_lower = basis - dev
bb_spread = bb_upper - bb_lower
avgspread = sma(bb_spread, sqz_length)
bb_squeeze = 0.00
bb_squeeze := bb_spread / avgspread * 100
bb_diff = (bb_upper - bb_lower) * 10
basis_angle = angle(basis,3)
bbu_angle = angle(bb_upper,3)
bbl_a = angle(bb_lower,3)
// BB Zones
bb_zone = bb_squeeze < 53 ? 0 : 
 bb_squeeze < sqz_threshold ? 1 : 
 bb_squeeze < 120 ? 2 :
 bb_squeeze < 181 ? 3 :
 bb_squeeze > 181 ? 4 : na
sqz_color = bb_zone == 0 ? #0045b3 :
 bb_zone == 1 ? #ff0062 : 
 bb_zone == 2 ?  gray : 
 bb_zone == 3 ?  #00c3ff : 
 bb_zone == 4 ? white: na

bb_zones_color =  sqz_color


// ===  SSL ===
// ==================================================
ssl_len1 = input(45, minval=1,title="SSL 1") // 45
ssl_len2 = input(60, minval=1,title="SSL 2") //75
ssl_len3 = input(15, minval=1,title="SSL 3") // 8 7
ssl_len4 = input(75, minval=1,title="SSL 4") // 75 100
ssl_len5 = input(150, minval=1,title="SSL 5") //7
s1 = wma(2*wma(close, ssl_len1/2)-wma(close, ssl_len1), round(sqrt(ssl_len1)))
s2 = wma(2*wma(close, ssl_len2/2)-wma(close, ssl_len2), round(sqrt(ssl_len2)))
s3 = wma(2*wma(close, ssl_len3/2)-wma(close, ssl_len3), round(sqrt(ssl_len3)))
s4 = wma(2*wma(close, ssl_len4/2)-wma(close, ssl_len4), round(sqrt(ssl_len4)))
s5 = wma(2*wma(close, ssl_len5/2)-wma(close, ssl_len5), round(sqrt(ssl_len5)))
//ssl_angle = angle(SSL,3)
s1_a  = angle(s1,2)
s2_a = angle(s2,2)
s3_a = angle(s3,2)
s4_a = angle(s4,2)
s5_a = angle(s5,2)
s4_dist = basis_angle>0 ? (bb_upper-s4)*100 : (s4 - bb_lower)*100 
basis_dist = basis_angle>0 ? (ema_200-basis)*100 : (basis - ema_200)*100 
ssl_color  = s1_a > 0 ? blue : red



// === WAE ===
// ==================================================
sensitivity = 150
wae_fast= 20 //8
wae_slow= 40 // 50
channelLength= 20 // 45
wae_mult = 2.0//1.85
wae_dz = nz(rma(tr(true),100)) * 3.7
calc_macd(source, wae_fast, wae_slow) =>
	a = ema(source, wae_fast)
	b = ema(source, wae_slow)
	a - b
calc_BBUpper(source, length, wae_mult) => 
	wae_basis = wma(source, length)
	wae_dev = wae_mult * stdev(source, length)
	t = wae_basis + wae_dev
	[t]
calc_BBLower(source, length, wae_mult) => 
	wae_basis = wma(source, length)
	wae_dev = wae_mult * stdev(source, length)
	t = wae_basis - wae_dev
	[t]

t1 = (calc_macd(close, wae_fast, wae_slow) - calc_macd(close[1], wae_fast, wae_slow))*sensitivity * 100
t2 = (calc_macd(close[2], wae_fast, wae_slow) - calc_macd(close[3], wae_fast, wae_slow))*sensitivity * 100
[e1a] = calc_BBUpper(close, channelLength, wae_mult)
[e1b] = calc_BBLower(close, channelLength, wae_mult)
wae_line = (e1a - e1b)
wae_diff = (wae_line - wae_dz) * 100
//wae_perc = (wae_line / wae_dz) * 100
trendUp = (t1 >= 0) ? t1 : 0
trendDown = (t1 < 0) ? (-1*t1) : 0
wae_color = #000000
if t1 >= 0
	wae_color := trendUp<trendUp[1] ? lime : green
if t1 < 0
	wae_color := trendDown<trendDown[1] ? orange : red
wae_angle = angle(wae_line,3)



// === ADX + DI with SMA ===
// ==================================================
adx_len = input(9,title="ADX len") // 14
adx_line = 20 // input(title="threshold", defval=20)
adx_avg = input(8,title="ADX SMA") // 10
var float adx_top = 54 // input(55,title="High")
var float adx_high = 38 // 39
var float adx_mid = 33
var float adx_center = 20
var float adx_low = 12
var float smooth_tr = 0
var float smooth_di_plus = 0
var float smooth_di_minus = 0
TrueRange = max(max(high-low, abs(high-nz(close[1]))), abs(low-nz(close[1])))
DI_plus = high-nz(high[1]) > nz(low[1])-low ? max(high-nz(high[1]), 0): 0
DI_minus = nz(low[1])-low > high-nz(high[1]) ? max(nz(low[1])-low, 0): 0
smooth_tr := nz(smooth_tr[1]) - (nz(smooth_tr[1])/adx_len) + TrueRange
smooth_di_plus := nz(smooth_di_plus[1]) - (nz(smooth_di_plus[1])/adx_len) + DI_plus
smooth_di_minus := nz(smooth_di_minus[1]) - (nz(smooth_di_minus[1])/adx_len) + DI_minus

di_plus = smooth_di_plus / smooth_tr * 100
di_minus = smooth_di_minus / smooth_tr * 100
DX = abs(di_plus-di_minus) / (di_plus+di_minus)*100
adx = sma(DX, adx_len)
adx_sma = sma(adx, adx_avg)
adx_angle = (angle(adx,2))
//hline(adx_line, color=black, linestyle=dashed)


// === RSI Wicks ===
// ==================================================
std         = false //input(false, title="Show Standard RSI")
rsi_candles = false //input(true,  title="Show Candles")
wicks       = true  //input(true,  title="Wicks based on stand-alone RSI")
src_close   = close
src_open    = open
src_high    = high
src_low     = low 

rsiw_len1    = input(23, title="RSIW len1") // 25 28 8 10 14
rsiw_len2    = input(12, title="RSIW len2") // 8 10 14
rsiw_len3    = input(4, title="RSIW len3") // 8 10 14

rsi_wicks(rsi_len) =>
    norm_close  = avg(src_close,src_close[1])
    gain_loss_close   = change(src_close)/norm_close
    RSI_close         = 50+50*rma(gain_loss_close, rsi_len)/rma(abs(gain_loss_close), rsi_len)

    norm_open = if wicks==true 
        avg(src_open,src_open[1])
    else 
        avg(src_close,src_close[1])
    gain_loss_open   = change(src_open)/norm_open
    RSI_open         = 50+50*rma(gain_loss_open, rsi_len)/rma(abs(gain_loss_open), rsi_len)
            
    norm_high = if wicks==true 
        avg(src_high,src_high[1])
    else 
        avg(src_close,src_close[1])
    gain_loss_high   = change(src_high)/norm_high
    RSI_high         = 50+50*rma(gain_loss_high, rsi_len)/rma(abs(gain_loss_high), rsi_len)
            
    norm_low  = if wicks==true
        avg(src_low,src_low[1])
    else 
        avg(src_close,src_close[1])
    gain_loss_low   = change(src_low)/norm_low
    RSI_low         = 50+50*rma(gain_loss_low, rsi_len)/rma(abs(gain_loss_low), rsi_len)

    // Sell
    rws_weak = RSI_close<70 and RSI_high>70 and rsi_strong==0? 1 : 0
    rws_mid = RSI_close>70 and RSI_open<70 and rsi_strong==0? 1 : 0 
    rws_strong = RSI_open>70 ? 1 : 0

    // Buy
    rwb_weak = RSI_close>30 and RSI_low<30 and rsi_strong==0? 1 : 0
    rwb_mid = RSI_close<30 and RSI_open>30 and rsi_strong==0? 1 : 0
    rwb_strong = RSI_open<30 ? 1 : 0

    [rws_mid,rws_strong,rwb_mid,rwb_strong,RSI_close,RSI_high,RSI_low]

[rws_mid1,rws_strong1,rwb_mid1,rwb_strong1,rsi_close1,rsi_high1,rsi_low1] = rsi_wicks(rsiw_len1)
[rws_mid2,rws_strong2,rwb_mid2,rwb_strong2,rsi_close2,rsi_high2,rsi_low2] = rsi_wicks(rsiw_len2)
[rws_mid3,rws_strong3,rwb_mid3,rwb_strong3,rsi_close3,rsi_high3,rsi_low3] = rsi_wicks(rsiw_len3)




// === Bollinger Bands %B ===
// ==================================================
bbr_len = 14 //input(45, minval=1)
bbr_multi = 2 //input(2.0, minval=0.001, maxval=50, title="StdDev")
bbr_basis = sma(close, bbr_len)
deviation = bbr_multi * stdev(close, bbr_len)
bbr_upper = bbr_basis + deviation
bbr_lower = bbr_basis - deviation
bbr = (close - bbr_lower)/(bbr_upper - bbr_lower)

// bbr_fun(bbr_len) =>
//     bbr_multi = 2 //input(2.0, minval=0.001, maxval=50, title="StdDev")
//     bbr_basis = sma(close, bbr_len)
//     deviation = bbr_multi * stdev(close, bbr_len)
//     bbr_upper = bbr_basis + deviation
//     lower = bbr_basis - deviation
//     bbr_lower = bbr_basis - deviation
//     bbr = (close - bbr_lower)/(bbr_upper - bbr_lower)

//     [bbr]

// [bbr] = bbr_fun(14)
// [bbr2] = bbr_fun(50)

// === RSI Candles - glaz ===
// ==================================================
var int pos = 0
var rsi_color = #000000
rsi_len = input(14, minval=1, title="RSI bar length")
rsi_up = rma(max(change(close), 0), rsi_len)
rsi_down = rma(-min(change(close), 0), rsi_len)
rsi = rsi_down == 0 ? 100 : rsi_up == 0 ? 0 : 100 - (100 / (1 + rsi_up / rsi_down))
isup() => rsi > 70
isdown() => rsi < 30 
// plot(rsi,title="RSI",color=color.blue)
rsi_color := isup() ? color.new(#ff0000, 0) : isdown() ? color.new(color.purple, 0)  : na




// === MACD ===
// ==================================================
fast_length = 8 // 12
slow_length = 25 // 26
md_src = close
signal_length = 9
sma_source = true
sma_signal = true
// Plot colors
col_grow_above = #26A69A
col_fall_above = #B2DFDB
col_grow_below = #FFCDD2
col_fall_below = #EF5350
col_macd = #0094ff
col_signal = #ff6a00
// Calculating
md_fast_ma = sma_source ? sma(md_src, fast_length) : ema(md_src, fast_length)
slow_ma = sma_source ? sma(md_src, slow_length) : ema(md_src, slow_length)
macd = md_fast_ma - slow_ma
macd_s = sma_signal ? sma(macd, signal_length) : ema(macd, signal_length)
m_histo = macd - macd_s
// plot(hist, title="Histogram", style=plot.style_columns, color=(hist>=0 ? (hist[1] < hist ? col_grow_above : col_fall_above) : (hist[1] < hist ? col_grow_below : col_fall_below) ), transp=0 )
// plot(macd, title="MACD", color=col_macd, transp=0)
// plot(signal, title="Signal", color=col_signal, transp=0)

// === Schaff Trend Cycle (STC) ===
// ==================================================
stc_signal = 12 // input(12,"Length")
stc_fast = 26 //input(26,"FastL/ength")
stc_slow = 50 //input(55,"SlowLength") //55
stc_line = 50 //(stc_high + stc_low) * 0.5
stc_bias = 0.5 // input(0.203,"STC Sensitivity") // 0.203

stc_high = 92
stc_up = 80 //  85 75
stc_center = 50
stc_down = 12 //12 25
stc_low = 5
stc_macd(BBB, stc_fast, stc_slow) =>
    stc_fastMA = ema(BBB, stc_fast)
    stc_slowMA = ema(BBB, stc_slow)
    stc_macd = stc_fastMA - stc_slowMA
    stc_macd
    
AAAAA(stc_signal, stc_fast, stc_slow) => 
    AAA= stc_bias
    var CCCCC = 0.0
    var DDD = 0.0
    var DDDDDD = 0.0
    var EEEEE = 0.0
    tmp_macd = stc_macd(close,stc_fast,stc_slow)     
    lowest = lowest(tmp_macd, stc_signal)
    highest = highest(tmp_macd, stc_signal) - lowest    
    CCCCC := (highest > 0 ? ((tmp_macd - lowest) / highest) * 100 : nz(CCCCC[1])) 
    DDD := (na(DDD[1]) ? CCCCC : DDD[1] + (AAA * (CCCCC - DDD[1]))) 
    DDDD = lowest(DDD, stc_signal) 
    DDDDD = highest(DDD, stc_signal) - DDDD     
    DDDDDD := (DDDDD > 0 ? ((DDD - DDDD) / DDDDD) * 100 : nz(DDDDDD[1])) 
    EEEEE := (na(EEEEE[1]) ? DDDDDD : EEEEE[1] + (AAA * (DDDDDD - EEEEE[1])))
    EEEEE

stc = AAAAA(stc_signal,stc_fast,stc_slow)
stc_color = stc > stc[1] ? buy_color : sell_color

// === Plot === 
// ==================================================
// Angles
//plot(bb_squeeze, color=bb_zones_color, title="BB Squeeze",style=plot.style_circles)
plot(bbr,title='BBR', style=plot.style_circles)
plot(stc, color= c_hide , title="STC",style=plot.style_circles)
plot(m_histo,title='MACD Histo', style=plot.style_circles)
plot( bb_diff,title="BB diff", style=plot.style_circles)
plot(ema_200_angle, color= c_hide , title="EMA 200 Angle",style=plot.style_circles)
plot(basis_angle, color= c_hide , title="Basis angle",style=plot.style_circles)
plot(s1_a, color=c_hide , title="SSL angle",style=plot.style_circles)
plot(s2_a, color=c_hide , title="SSL angle 2",style=plot.style_circles)
plot(s4_a, color=c_hide , title="SSL angle 4",style=plot.style_circles)
plot(s5_a, color=c_hide , title="SSL angle 5",style=plot.style_circles)
//plot(bbu_angle, color= c_hide , title="BB Upper Angle",style=plot.style_circles)
plot(bbl_a, color= c_hide , title="BB Lower Angle",style=plot.style_circles)
plot(s4_dist,title="s4 Dist",style=plot.style_circles,color=color.new(color.black,100) )
plot(basis_dist,title="Basis Dist",style=plot.style_circles,color=color.new(color.black,100) )
//plot(rsi_close1,title="RSI close1",style=plot.style_circles)
//plot(rsi_close2,title="RSI close2",style=plot.style_circles)
//plot(rsi_close3,title="RSI close3",style=plot.style_circles)
////plot(res, color=res_c, title="RES",style=plot.style_circles)

// plot(be_dist, color= c_hide , title="BE Dist",style=plot.style_circles)
// plot(mom_stoch, color= c_hide , title="MOM smi",style=plot.style_circles)
// plot(mom_ema, color= c_hide , title="MOM ema",style=plot.style_circles)
plot(di_plus, color=green, title="DI+",style=plot.style_circles)
plot(di_minus, color=red, title="DI-",style=plot.style_circles)
plot(adx, color=yellow, title="ADX",style=plot.style_circles)
plot(adx_sma, color=c_hide, title="ADX SMA",style=plot.style_circles)
plot(wae_line,title="Explosion Line",style=plot.style_circles,color=wae_color)
plot(wae_dz,title="Dead Zone",style=plot.style_circles)
plot(wae_angle,title="WAE angle",style=plot.style_circles)

plot(macd_s,title='MACD Sig', style=plot.style_circles)


// Kijun
plot(kijun, color=#f44336, title="Kijun",linewidth=2)
// SSL
plot(s1, color=ssl_color , title="SSL 1", linewidth=2)
plot(s2, color=orange , title="SSL 2")
plot(s3, color=aqua , title="SSL 3")
plot(s4, color=yellow , title="SSL 4",linewidth=2)
plot(s5, color=green , title="SSL 5",linewidth=2)

// BB
plot(basis, title="Basis", color=bb_zones_color,linewidth=3)
p1 = plot(bb_upper, "BB Upper ",color=bb_zones_color,linewidth=2)
p2 = plot(bb_lower, "BB Lower ",color=bb_zones_color,linewidth=2)
//fill(p1,p2, bb_zones_color)
// EMA 200 bands large
plot(ema_200, "EMA 200", color=color.new(yellow,50),linewidth=2 )
//plot(ema_fast, color=color.blue, title="EMA Fast")
// ATR
//plot(show_atr ? atr_upper : na, "+ATR Upper", color=color.new(#ffffff,95))
//plot(show_atr ? atr_lower : na, "-ATR Lower", color=color.new(#ffffff,95))
// plot(show_atr ? atr_upper + atr_stop : na, "+ATR stop", color=color.new(#ffff00,85))
// plot(show_atr ? atr_lower - atr_stop : na, "-ATR stop", color=color.new(#ffff00,85))


// RSI close
plot(rsi_close1,title="RSI Close 1",style=plot.style_circles)
plotshape(rsi1_show and rsi_close1>70?1:na,title="RSI 1",color=rws_mid1==1?orange:rws_strong1==1?#ff0000:na,style=shape.circle,location=location.top)
plotshape(rsi2_show and rsi_close2>70 and close>open?1:na,title="RSI 2",color=rws_mid2==1?orange:rws_strong2==1?#ff0000:na,style=shape.circle,location=location.top)
plotshape(rsi3_show and rsi_close3>70?1:na,title="RSI 3",color=rws_mid3==1?orange:rws_strong3==1?#ff0000:na,style=shape.circle,location=location.top)

// Down
plotshape(rsi1_show and rsi_close1<30?1:na,title="RSI 1",color=rwb_mid1==1?#0053ff:rwb_strong1==1?lime:na,style=shape.circle,location=location.bottom)

plotshape(rsi2_show and rsi_low2<30 and open>close?1:na,title="RSI 2 Open",color=rwb_mid2==1?#0053ff:rwb_strong2==1?lime:na,style=shape.circle,location=location.bottom)
plotshape(rsi2_show and rsi_close2<30 and close<open?1:na,title="RSI 2 Close",color=rwb_mid2==1?#0053ff:rwb_strong2==1?lime:na,style=shape.circle,location=location.bottom)

plotshape(rsi3_show and rsi_close3<30?1:na,title="RSI 3",color=rwb_mid3==1?#0053ff:rwb_strong3==1?lime:na,style=shape.circle,location=location.bottom)

// Bar color
barcolor(switch_bars==false ? rsi_color:na, title="Rsi Candles")
bbr_color = bbr>1 ? color.new(#ff0000, 0) : bbr<0 ? color.new(color.purple, 0)  : na
barcolor(switch_bars?bbr_color:na, title="Switch bars")
//barcolor(pos == -1 ? #ff0000: pos == 1 ? #00a000 : gray)


var bool enter_exit = false
//var int trade_dir = 0
var int state = 0
var int bar_num = 0
var float lastPrice = 0.0
var mult_diff = 10000
var int num_pips = 17
var int num_bars = 4

entry_signal() =>
	candle = close > open ? 1 : 0
    dir = 0
    counter = 0
    cond = ''
    allow = false
    ea = ema_200_angle
    ba = basis_angle

    // Uptrend - Oct 6-20


    // === SELL ===
    // ===========


    // Sell

    if candle==1 and rws_strong1 and bbr<1 and
     close<bb_upper and di_plus>di_plus[1]
        dir := -1
        cond := 'rsi1-close'

    if bb_zone<3 and candle==1 and rsi_high1>70 and rsi_close1<70
        dir := -1
        cond := 'rsi1-high'

    if candle==1 and rws_strong2 and bbr>1 and ba<7
     and not(bb_zone>2 and s1<s4)
        dir := -1
        cond := 'rsi2'

    if candle==1 and rsi_close2>70 and bbr<1 and
     close<bb_upper and di_plus>di_plus[1]
     and not(s1<kijun and bb_lower>ema_200)
     and not(bb_upper<ema_200)
        dir := -1
        cond := 'rsi2-close'

    if candle==1 and s5>s4 and rws_strong2
     and not(rsi_high1>70)
        dir := -1
        cond := 's5'    

    // if candle==1 and bbr>1 and m_histo>=0 and stc>stc_center and
    //  ba<7 and ba>-1 and s3>s4 and s3>kijun
    //  and not(bb_upper<ema_200 and rwb_strong3==0)
    //     dir := -1
    //     cond := 'bbr'


    // Counter
    if candle==0 and rsi_close3<30 and bbr<0.5 
     and not(bb_zone==4)
     and not(bb_upper<ema_200)
     and not(adx<adx_sma)
     and not(low<bb_lower)
     and not(s4>bb_upper)
     and not(bb_diff>6)
        dir := 1
        cond := 'rsi3-cnt'

    if candle==0 and bb_lower>ema_200 and rwb_strong3 and wae_line<wae_dz
     //and not(s4>s5)
     and not(bbl_a<-15)
     and not(s2>bb_upper)
        dir := 1
        cond := 'rsi3'

    if candle==1 and bbr>1 and di_plus>di_minus and macd_s>0 and
     adx>adx_sma and adx_sma>adx_low and (s4>s1 and s5>s1)
     and not(rsi_close1>70)
        dir := -1
        cond := 'bbr-cnt-up'

    if dir==-1 and bb_lower>ema_200 and s1<s4
        dir := 0

    


    // === Buy ===
    // ===========
    if candle==0 and rwb_strong1 and s4<basis
     and not(bb_upper<ema_200 and basis<s5)
     and not(bb_upper<ema_200 and di_minus<di_minus[1])
        dir := 1
        cond := 'rsi1'

    // if candle==0 and rwb_strong1 and bbr2>0
    //     dir := 1
    //     cond := 'rsi1-bbr2'

    if candle==0 and rwb_strong2 and s4<s5
     and not(s4>basis and low<bb_lower)
     and not(low<bb_lower and wae_color==orange)
        dir := 1
        cond := 'rsi2'
        counter := close>bb_lower? 1 : 0

    if candle==0 and bbr<0 and m_histo<=0 and stc<stc_center and s3<kijun and ba>-7
     and not(s4<bb_lower)
     and not(bb_upper>ema_200 and s4>s5)
     and not(s5<s1 and s4>s1)
        dir := 1
        cond := bb_zone<2? 'bbr\ncnt' : 'bbr'

    if candle==0 and bb_upper<ema_200 and rsi_close2<30
        dir := 1
        cond := 'r2'

    if candle==0 and rwb_strong3 and stc<stc_down and s5>bb_lower and bb_lower<ema_200
     and not(rsi_low2<30 or rsi_close2<30)
        dir := 1
        cond := 'r3-stc'

    if show_r3 and candle==0 and bb_zone<3 and rwb_strong3 and bb_upper<ema_200 and 
     s3<kijun and s1<s4
     and not(s4_a<-6)
        dir := 1
        cond := 'r3'

    if show_r3 and candle==0 and bb_zone<3 and close<bb_lower and rsi_close3<30
     and s5_a>0 and s4<basis
        dir := 1
        cond := 'r3-z1'

    // Counter
    if candle==1 and bb_lower<ema_200 and 
     rws_strong3 and s5_a<1 and di_plus>di_minus and wae_line>wae_dz
     and not(s5<s4)
        dir := -1
        cond := 'r3'

    if candle==1 and bbr>1 and di_plus>di_minus and macd_s>0 and
     adx>adx_sma and adx_sma>adx_low
     and not(s5>bb_upper or close>bb_upper)
     and not(basis>ema_200)
        dir := -1
        cond := 'bbr-cnt'

    // if candle==1 and bb_zone>1  and s5_a<0 and bb_lower<ema_200 and 
    //  bbr>1 and rsi_close3>70 and m_histo>0 and di_plus>di_minus
    //     dir := -1
    //     cond := 'r3-bbr'


    // Filter out same direction trades, wait 4 candles
    // and price higher than 20 pips before allowing another trade
    if dir == state and use_logic and counter==0
        allow := (bar_index - bar_num) >= num_bars ? true : false
        // sell
        if state == -1
            dir := ((close - lastPrice) * mult_diff) > num_pips and allow ? dir : 0
        // buy
        if state == 1
            dir := ((close - lastPrice) * mult_diff) < (num_pips * -1) and allow ? dir : 0

    //var int enter_exit = basis_angle > 0 and dir == -1 ? 0 : basis_angle < 0 and dir
    if counter==1 and state==dir
        dir := 0
        
    type = dir > 0 ? 1 : dir < 0 ? -1 : 0
    [type,counter,cond]

[trade_dir,counter,condition] = entry_signal() 

if trade_dir != 0 and use_logic
    state := trade_dir

    enter_exit := counter == 1 ? 1 : 0
    // Sell
    // if basis_angle > 0 and trade_dir == -1
    //     enter_exit := -1
    // // Counter
    // if basis_angle > 0 and trade_dir == 1
    //     enter_exit := 0
    // // Buy
    // if basis_angle < 0 and trade_dir == 1
    //     enter_exit := -1
    // // Counter
    // if basis_angle < 0 and trade_dir == -1
    //     enter_exit := 0
    // if basis>ema_200
    //     enter_exit := trade_dir == -1 ? false : trade_dir == 1 or counter ? true : false
    // if basis<ema_200
    //     enter_exit := trade_dir == 1 ? false : trade_dir == -1 or counter ? true : false
    //enter_exit := counter == 1 ? true : enter_exit == false ? true : false
    lastPrice := close
    bar_num   := bar_index

// if enter_exit != trade_dir and trade_dir != 0
//     enter_exit := enter_exit == false ? true : false
labelText = tostring(condition)
trade_color = trade_dir > 0  ? buy_color : sell_color
if trade_dir != 0 and show_cond
	if trade_dir > 0
        buy = label.new(x=time,y=high,text=labelText,xloc=xloc.bar_time, yloc=yloc.belowbar,color=trade_color,textcolor=color.white, size=size.normal,style=label.style_label_up)
    else
        sell = label.new(x=time,y=high,text=labelText,xloc=xloc.bar_time, yloc=yloc.abovebar,color=trade_color,textcolor=color.white, size=size.normal)

plot(counter,title="Counter Trade", style=plot.style_circles)
plot(state,"State",style=plot.style_circles)

plotshape(show_entry and trade_dir == 1 and enter_exit == 0 ? 1 : na, title="Entry Buy", color=trade_color, location = location.belowbar, style=shape.labelup, text="B", textcolor=color.white, size=size.small)
plotshape(show_entry and trade_dir == 1 and enter_exit == -1 ? 1 : na, title="Counter Buy", color=trade_color, location = location.belowbar, style=shape.labelup, text="Exit", textcolor=color.white, size=size.small)
plotshape(show_entry and trade_dir == -1 and enter_exit == 0 ? -1: na, title="Entry Sell", color=trade_color, location = location.abovebar, style=shape.labeldown, text="S", textcolor=color.white, size=size.small)
plotshape(show_entry and trade_dir == -1 and enter_exit == -1 ? -1 : na, title="Exit Sell", color=trade_color, location = location.abovebar, style=shape.labeldown, text="Exit", textcolor=color.white, size=size.small)


