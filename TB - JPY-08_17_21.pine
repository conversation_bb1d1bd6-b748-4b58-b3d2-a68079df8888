//@version=4
study(title = "Trading Bot - JPY", shorttitle="TB - JPY",overlay=true)

show_entry = input(title="Show Entry", type=input.bool, defval=true)
show_atr = input(title="Show ATR", type=input.bool, defval=true)
show_plots = input(title="Show Plots", type=input.bool, defval=false)
show_bb= input(title="Show BB", type=input.bool, defval=true)
use_barcolor = input(title="Use Bar Color", type=input.bool, defval=false)
show_candles = input(title="Show Candlesticks", type=input.bool, defval=false)
use_rsi = input(title="Use RSI", type=input.bool, defval=true)
show_lastprice = input(title="Show Last Price", type=input.bool, defval=false)
use_logic = input(title="Use Logic", type=input.bool, defval=false)
show_cond = input(title="Use Cond", type=input.bool, defval=true)

red = #ff0062
aqua = #00bcd4
yellow = #FFFF00
orange = #ff9800
green = #4caf50
lime = #00E676
white = #ffffff
blue = #42a5f5
gray = #707070
black = #000000
sell_color = color.new(#ff0062,20)
buy_color = color.new(#00c3ff,20)

angle(_src, len) =>
    rad2degree=180/3.14159265359  //pi 
    ang=rad2degree*atan((_src[0] - _src[1])/atr(len))

// Change
perc_change() =>
    perc = abs( (1 - (close[1] / close)) * 10000 )


// ===  EMA's ===
// ==================================================
ema_200 = ema(close, 200)
ema_200_angle = angle(ema_200,2)
// ema_len_f = 5//input(5, minval=1, title="EMA Length") //6
// ema_fast = ema(close, ema_len_f)
// ema_angle = angle(ema_fast,2)



// === Bollinger Bands %B ===
// ==================================================
length = input(20, title="BBR",minval=1) // 20, 29, 43
multi = 1.8 //input(2.0, minval=0.001, maxval=50, title="StdDev")
bbr_basis = sma(close, length)
deviation = multi * stdev(close, length)
bbr_upper = bbr_basis + deviation
bbr_lower = bbr_basis - deviation
bbr = (close - bbr_lower)/(bbr_upper - bbr_lower)
bbr_color = bbr[1]>1 ? #ff0062 : bbr[1]<0 ? #00ff64 : #00c3ff



// ===  ATR ===
// ==================================================
atrlen = 14 //input(14, "ATR Period")
atr_mult = 1.8 //input(1.8, "ATR Mult", step = 0.1) // 1.15
atr_stop = 0.0003 //input(0.0003, "ATR Stop", step = 0.0001) // 0.001
atr_slen = wma(tr(true), atrlen)
atr_upper = atr_slen * atr_mult + close
atr_lower = close - atr_slen * atr_mult

atr_offset = input(5, "ATR Offset") 
atr_s = sma(atr_upper,atrlen+atr_offset)
//plot(angle(atr_s,10),"ATR AVG - Short")
atr_b = sma(atr_lower,atrlen+atr_offset)
//plot(angle(atr_b,10),"ATR AVG - Short")


// ===  Kijun ===
// ==================================================
middleDonchian(Length) =>
    lower = lowest(Length)
    upper = highest(Length)
    avg(upper, lower)

basePeriods = 14 //input(14, minval=1) //26
kijun =  middleDonchian(basePeriods)
k_angle = angle(kijun,3)


// ===  BB ===
// ==================================================
var float bb_squeeze = 0.00
var float bb_diff = 0.00
BB_length = input(20, minval=1, maxval=150) // 43 45
BB_stdDev = 2 //input(2, minval=2.0, maxval=3)
sqz_length = 100 //input(100, minval=2, maxval=200) // 100
sqz_threshold = 76 
bb_s = close //kijun //close
basis = sma(close, BB_length)
dev = BB_stdDev * stdev(bb_s, BB_length)
bb_upper = basis + dev
bb_lower = basis - dev
bb_spread = bb_upper - bb_lower
avgspread = sma(bb_spread, sqz_length)
bb_squeeze := bb_spread / avgspread * 100
diffspread = sma(bb_spread, 14)
bb_diff := bb_spread / diffspread * 100
basis_angle = angle(basis,3)
basis_angle_h = angle(sma(close, 43),2)
bbu_angle = angle(bb_upper,3)
bbl_angle = angle(bb_lower,3)
bbu_diff_ema = abs(bb_upper-ema_200)
bbl_diff_ema = abs(bb_lower-ema_200)
// BB Zones
bb_zone = bb_squeeze < 53 ? 0 : 
 bb_squeeze < sqz_threshold ? 1 : 
 bb_squeeze < 120 ? 2 :
 bb_squeeze < 181 ? 3 :
 bb_squeeze > 181 ? 4 : na
sqz_color = bb_zone == 0 ? #0045b3 :
 bb_zone == 1 ? #ff0062 : 
 bb_zone == 2 ?  gray : 
 bb_zone == 3 ?  #00c3ff : 
 bb_zone == 4 ? white: na

bb_zones_color =  sqz_color



// ===  SSL ===
// ==================================================
ssl_len = input(60, minval=1,title="SSL Len") //75
ssl_len2 = BB_length //input(45, minval=1,title="SSL 2")
ssl_len3 = 8 //input(8, minval=1,title="SSL 3") //7
SSL = wma(2*wma(close, ssl_len/2)-wma(close, ssl_len), round(sqrt(ssl_len)))
SSL2 = wma(2*wma(close, ssl_len2/2)-wma(close, ssl_len2), round(sqrt(ssl_len2)))
SSL3 = wma(2*wma(close, ssl_len3/2)-wma(close, ssl_len3), round(sqrt(ssl_len3)))
//ssl_angle = angle(SSL,3)
ssl_angle = angle(SSL,2)
ssl_angle2 = angle(SSL2,2)
ssl_angle3 = angle(SSL3,2)
ssl_color = ssl_angle2 > 0 ? blue : red



// === Basis to ema 200 distance ===
// ==================================================
be_length = 20 // 35 43
be_high = 50
be_up = 10
be_down = -10
be_low = -50

be_mult = syminfo.currency == 'JPY' ? 100 : syminfo.currency == 'NZD' ? 10000 : 10000
be_basis = sma(close, be_length) 
be_dist = (be_basis - ema_200) * 100
be_close_ema = (close - ema_200) * be_mult
be_close_lower = (close - bb_lower) * be_mult
be_u = (bb_upper - ema_200) * be_mult
be_l = (bb_lower - ema_200) * be_mult
be_angle = angle(be_dist,2)
be_color = be_dist>0?color.green  : color.red



// === MACD Crossover === 
// ==================================================
fastLength = 8 //input(8, minval=1) // 8 //8
slowLength = 25 //input(25,minval=1) // 16 // 21
signalLength= 9 //input(9,minval=1) // 11 // 5
fastMA = ema(close, fastLength)
slowMA = ema(close, slowLength)
macd_c = fastMA - slowMA
sig_c = sma(macd_c, signalLength)
pos = 0
pos := iff(sig_c < macd_c , 1,iff(sig_c > macd_c, -1, nz(pos[1], 0))) 
mc_color = pos == -1 ? red: pos == 1 ? green : blue
mc_angle = angle(macd_c,2)
s_angle = angle(sig_c,2)
mc_diff = macd_c / sig_c

macd_high = syminfo.currency == 'NZD' ? 0.00170 : syminfo.currency == 'JPY' ? 0.395 : 0.0012
macd_up = 0.160
macd_down = -0.160
macd_low = syminfo.currency == 'NZD' ? -0.00170 : syminfo.currency == 'JPY' ? -0.395 : -0.0012



// === WAE ===
// ==================================================
sensitivity = 150
wae_fast= 20 //8
wae_slow= 40 // 50
channelLength= 20 // 45
wae_mult = 2.0//1.85
var float wae_columns = 0.000
wae_dz = nz(rma(tr(true),100)) * 3.7
calc_macd(source, wae_fast, wae_slow) =>
	a = ema(source, wae_fast)
	b = ema(source, wae_slow)
	a - b
calc_BBUpper(source, length, wae_mult) => 
	wae_basis = wma(source, length)
	wae_dev = wae_mult * stdev(source, length)
	t = wae_basis + wae_dev
	[t]
calc_BBLower(source, length, wae_mult) => 
	wae_basis = wma(source, length)
	wae_dev = wae_mult * stdev(source, length)
	t = wae_basis - wae_dev
	[t]

t1 = (calc_macd(close, wae_fast, wae_slow) - calc_macd(close[1], wae_fast, wae_slow))*sensitivity * 100
t2 = (calc_macd(close[2], wae_fast, wae_slow) - calc_macd(close[3], wae_fast, wae_slow))*sensitivity * 100
[e1a] = calc_BBUpper(close, channelLength, wae_mult)
[e1b] = calc_BBLower(close, channelLength, wae_mult)
wae_line = (e1a - e1b)
wae_diff = (wae_line - wae_dz) * 100
//wae_perc = (wae_line / wae_dz) * 100
trendUp = (t1 >= 0) ? t1 : 0
trendDown = (t1 < 0) ? (-1*t1) : 0
wae_columns := abs(t1/100)
wae_color = #000000
wae_angle = angle(wae_line,2)
if t1 >= 0
	wae_color := trendUp<trendUp[1] ? lime : green
if t1 < 0
	wae_color := trendDown<trendDown[1] ? orange : red


// === ADX + DI with SMA ===
// ==================================================
adx_len = input(title="Adx Length",defval=13)
adx_line = 20// input(title="threshold", type=integer, defval=20)
adx_avg = 10// input(title="SMA", type=integer, defval=10)
var float adx_high = 40
var float adx_mid = 34
var float adx_center = 20
var float adx_low = 12
var float smooth_tr = 0
var float smooth_di_plus = 0
var float smooth_di_minus = 0
TrueRange = max(max(high-low, abs(high-nz(close[1]))), abs(low-nz(close[1])))
DI_plus = high-nz(high[1]) > nz(low[1])-low ? max(high-nz(high[1]), 0): 0
DI_minus = nz(low[1])-low > high-nz(high[1]) ? max(nz(low[1])-low, 0): 0
smooth_tr := nz(smooth_tr[1]) - (nz(smooth_tr[1])/adx_len) + TrueRange
smooth_di_plus := nz(smooth_di_plus[1]) - (nz(smooth_di_plus[1])/adx_len) + DI_plus
smooth_di_minus := nz(smooth_di_minus[1]) - (nz(smooth_di_minus[1])/adx_len) + DI_minus

di_plus = smooth_di_plus / smooth_tr * 100
di_minus = smooth_di_minus / smooth_tr * 100
DX = abs(di_plus-di_minus) / (di_plus+di_minus)*100
adx = sma(DX, adx_len)
adx_sma = sma(adx, adx_avg)
adx_angle = (angle(adx,2))



// === Stochastics Momentum Index ===
// ==================================================
stc_mom_a = 10 //input(10, "Percent K Length")
stc_mom_b = 3//input(3, "Percent D Length")
// Range Calculation
ll = lowest (low, stc_mom_a)
hh = highest (high, stc_mom_a)
diff = hh - ll
rdiff = close - (hh+ll)/2
avgrel = ema(ema(rdiff,stc_mom_b),stc_mom_b)
avgdiff = ema(ema(diff,stc_mom_b),stc_mom_b)
// SMI calculations
SMI = avgdiff != 0 ? (avgrel/(avgdiff/2)*100) : 0
mom_sig = ema(SMI,stc_mom_b)
mom_ema = ema(SMI, 10)
mom_angle = angle(mom_ema,2)
mom_high = 40
mom_low = -40



// === MACD Dema === 
// ==================================================
var float dema_histo = 0
dema_sma = input(12,title='DEMA Short') // 12
dema_lma = input(26,title='DEMA Long') // 26
dema_signal_in = input(9,title='Signal') // 7 // 9 
dema_high= 27//input(27,title='Dema high')
dema_mid= 11//input(11,title='Dema mid')
dema_down = -10//input(-10,title='Dema down') 
dema_low = -24//input(-24,title='Dema Low') 
dema_mult = 100 // GBP/USD 10000

MMEslowa = ema(close,dema_lma)
MMEslowb = ema(MMEslowa,dema_lma)
DEMAslow = ((2 * MMEslowa) - MMEslowb )
MMEfasta = ema(close,dema_sma)
MMEfastb = ema(MMEfasta,dema_sma)
DEMAfast = ((2 * MMEfasta) - MMEfastb)
dema_line = (DEMAfast - DEMAslow)*dema_mult
MMEsignala = ema(dema_line, dema_signal_in)
MMEsignalb = ema(MMEsignala, dema_signal_in)
dema_sig = ((2 * MMEsignala) - MMEsignalb )
dema_histo := (dema_line - dema_sig)
dema_color = dema_histo>0?green:red



// === RSI Chart Bars ===
// ==================================================
var rsi_color = #000000
rsi_len = 14 //input(14, minval=1, title="Length")
rsi_up = rma(max(change(close), 0), rsi_len)
rsi_down = rma(-min(change(close), 0), rsi_len)
rsi = rsi_down == 0 ? 100 : rsi_up == 0 ? 0 : 100 - (100 / (1 + rsi_up / rsi_down))
isup() => rsi > 70
isdown() => rsi < 30
// plot(rsi,title="RSI",color=color.blue)
rsi_color := isup() ? color.new(buy_color, 10) : isdown() ? color.new(#ff0000, 10)  : na
barcolor(rsi_color, title="Rsi Candles")


// === RSI Wicks ===
// ==================================================
std         = false //input(false, title="Show Standard RSI")
rsi_candles = false//input(true,  title="Show Candles")
wicks       = true //input(true,  title="Wicks based on stand-alone RSI")
src_close   = close
src_open    = open
src_high    = high
src_low     = low 
len         = 14 //input(14, minval=1, title="Length")

norm_close  = avg(src_close,src_close[1])
gain_loss_close   = change(src_close)/norm_close
RSI_close         = 50+50*rma(gain_loss_close, len)/rma(abs(gain_loss_close), len)

norm_open = if wicks==true 
    avg(src_open,src_open[1])
else 
    avg(src_close,src_close[1])
gain_loss_open   = change(src_open)/norm_open
RSI_open         = 50+50*rma(gain_loss_open, len)/rma(abs(gain_loss_open), len)
        
norm_high = if wicks==true 
    avg(src_high,src_high[1])
else 
    avg(src_close,src_close[1])
gain_loss_high   = change(src_high)/norm_high
RSI_high         = 50+50*rma(gain_loss_high, len)/rma(abs(gain_loss_high), len)
        
norm_low  = if wicks==true
    avg(src_low,src_low[1])
else 
    avg(src_close,src_close[1])
gain_loss_low   = change(src_low)/norm_low
RSI_low         = 50+50*rma(gain_loss_low, len)/rma(abs(gain_loss_low), len)
rws_strong = RSI_open>69 and close>open
rws_mid = RSI_close>70 and RSI_open<70 and close>open
rws_weak = RSI_close<70 and RSI_high>70 and close>open
rwb_weak = RSI_close>30 and RSI_low<30 and close<open
rwb_mid = RSI_close<30 and RSI_open>30 and close<open
rwb_strong = RSI_open<30 and close<open



// === RES - Ranging EMA Spread  ===
// ==================================================
ema1length = 12
ema2length = 43
ranginglength = 3
rangingmaxvalue = 0.14//0.1
rangingminvalue = -0.1
enablebarcolors = false


// EMA spread
ema1 = ema(close, ema1length)
ema2 = ema(close, ema2length)
spread = ((ema2 / ema1) -1) * 100
r1 = (spread >= rangingminvalue and spread <= rangingmaxvalue) 
r2 = (spread[1] >= rangingminvalue and spread[1] <= rangingmaxvalue)
r3 = (spread[2] >= rangingminvalue and spread[2] <= rangingmaxvalue)
r4 = (spread[3] >= rangingminvalue and spread[3] <= rangingmaxvalue)
r5 = (spread[4] >= rangingminvalue and spread[4] <= rangingmaxvalue)

ranging = false

if (ranginglength == 1) 
    ranging := r1
if (ranginglength == 2) 
    ranging := r1 and r2
if (ranginglength == 3) 
    ranging := r1 and r2 and r3
if (ranginglength == 4) 
    ranging := r1 and r2 and r3 and r4
if (ranginglength == 5) 
    ranging := r1 and r2 and r3 and r4 and r5

res = spread > 0 ? 0-spread : abs(spread)
res := res * 100
col_low = gray // grey
col_mid = blue// navy
col_high = aqua // aqua
res_c = ranging ? col_low : spread > spread[1] ? col_mid : col_high




// === Plot === 
// ==================================================

plot(ema_200_angle, color= white , title="Ema 200 angle",style=plot.style_circles)
plot(basis_angle, color= white , title="Basis angle",style=plot.style_circles)
plot(basis_angle_h, color= white , title="Basis angle High",style=plot.style_circles)

plot(bbu_angle, color= white , title="BB Upper Angle",style=plot.style_circles)
plot(bbl_angle, color= white , title="BB Lower Angle",style=plot.style_circles)
plot(adx, color=yellow, title="ADX",style=plot.style_circles)
plot(adx_sma, color=white, title="ADX SMA",style=plot.style_circles)
plot(di_plus, color=green, title="DI+",style=plot.style_circles)
plot(di_minus, color=red, title="DI-",style=plot.style_circles)
plot(bbr, color=bbr_color, title="BBR",style=plot.style_circles)
// BE Distance
plot(be_dist, color=be_color, title="BE distance",style=plot.style_circles)
plot(be_close_ema,title="BE close ema",color=be_close_ema<9 ? red : gray, style=plot.style_circles)
plot(be_close_lower,title="BE close basis",color=be_close_ema<9 ? red : gray, style=plot.style_circles)

plot(be_angle, color=be_color, title="BE angle",style=plot.style_circles)
plot(be_u, color=be_color, title="BE Up",style=plot.style_circles)
plot(be_l, color=be_color, title="BE Down",style=plot.style_circles)
plot(res,"RES", style=plot.style_circles, linewidth=0, color=res_c)
// MOM and dema
plot(mom_ema, title="Mom EMA", style=plot.style_circles, color=yellow)
plot(mom_sig, title="Mom SMA", style=plot.style_circles, color=red)
plot(dema_line, title="Dema Line", style=plot.style_circles, color=green)
plot(dema_sig, title="Dema Sig", style=plot.style_circles, color=red)
plot(dema_histo, title="Dema Histo", style=plot.style_circles, color=dema_color)
// SSL angles
plot(ssl_angle, color=white , title="SSL angle",style=plot.style_circles)
plot(ssl_angle2, color= white , title="SSL2 angle",style=plot.style_circles)
plot(ssl_angle3, color= white , title="SSL3 angle",style=plot.style_circles)
plot(adx_angle, color=white, title="ADX Angle",style=plot.style_circles)

atr_color = color.new(#55d51a,70)
atr_s_upper = plot(atr_s,"ATR AVG - Short",color=atr_color)
atr_b_lower = plot(atr_b,"ATR AVG - Long",color=atr_color)
fill(atr_s_upper,atr_b_lower,title='ATR Fill',color=color.new(atr_color,93))

// Kijun
plot(kijun, color=red, title="Kijun",linewidth=2)
// SSL
plot(SSL, color=white , title="SSL")
plot(SSL2, color=ssl_color , title="SSL 2")
plot(SSL3, color= green , title="SSL 3")
// BB

plot(bbu_diff_ema, title="Diff BBU ema 200", color=bb_zones_color,linewidth=3)
plot(basis, title="Basis", color=bb_zones_color,linewidth=3)
p1 = plot(show_bb ? bb_upper : na, "BB Upper ",color=bb_zones_color,linewidth=2)
p2 = plot(show_bb ? bb_lower : na, "BB Lower ",color=bb_zones_color,linewidth=2)

// EMA 200 bands large
plot(ema_200, "EMA 200", color=color.new(yellow,50),linewidth=2 )

// ATR
//plot(show_plots ? atr_upper : na, "+ATR Upper", color=color.new(#ffffff,85))
//plot(show_plots ? atr_lower : na, "-ATR Lower", color=color.new(#ffffff,85))
plot(show_atr ? atr_upper + atr_stop : na, "+ATR stop", color=color.new(#ffff00,75))
plot(show_atr ? atr_lower - atr_stop : na, "-ATR stop", color=color.new(#ffff00,75))
//barcolor(pos == -1 and use_barcolor? #ff0000: pos == 1 and use_barcolor ? #00a000 : na)

var bool enter_exit = false
//var int trade_dir = 0
var int state = 0
var int bar_num = 0
var float lastPrice = 0.0
var mult_diff = 10000
var int num_pips = 25
var int num_bars = 4

entry_signal() =>
	candle = close > open ? 1 : 0
    dir = 0
    counter = 0
    allow = false
    cond = ''

    // New Aug 12

    // basis-7-cnt
    if basis_angle<7 and basis_angle>-7
        // Sell
        if close>atr_s and be_angle>be_u and close>SSL3 and
         be_dist<be_high and be_dist>be_low and 
         res_c == gray and bbr>1 
         and not(bb_lower>ema_200 and res<0)
         and not(bb_lower>ema_200 and adx<adx_center)
         and not(bb_upper<ema_200 and res>0)
         and not(di_plus[1]>di_plus or di_plus>adx_mid)
            dir := -1
            counter := 1
            cond := 's-basis-7-cnt'

        // Buy
        // if candle==0 and close<atr_b and be_angle<be_l and close<SSL3 and
        //  be_dist<be_high and be_dist>be_low and 
        //  res<0 and res_c==gray and bbr<0
        //  and not(bb_upper<ema_200 and res<0)
        //  and not(bb_upper<ema_200 and adx<adx_center)
        //  and not(bb_lower>ema_200 and res<0)
        //  and not(di_minus[1]>di_minus)
        //     dir := 1
        //     counter := 1
        //     cond := 'b-basis-7-cnt'


        
    // up-0-1-2-s
    if bb_zone<3 and candle==0 and close>atr_s and isup() and
     be_dist>be_high and RSI_close>70 and di_plus>adx_mid
        dir := -1
        cond := 'up-0-1-2-s'


    // Counter - 4-cnt
    if bb_zone==4 and candle==1 and 
     SSL>close and sig_c>macd_c and macd_c>0 and be_angle>be_l
        dir := -1
        counter := 1
        cond := '4-cnt'

    // === Above ema ===
    // =================
    if bb_lower>ema_200


        if bb_zone>2 and candle==1 and 
         wae_color==green and be_dist>be_high and rws_strong==1 and
         atr_s<bb_upper and adx>adx_mid and di_minus<adx_low and
         di_plus>di_plus[1] and adx>adx_sma
            dir := -1
            cond := 'up-3-4-strong'

        // Sell - Uptrend zone 2 & 3
        // if (bb_zone==2 or bb_zone==3) and candle==1 and 
        //  wae_color==green and 
        //  be_dist>be_high and rws_strong==1
        //  and not(isup() and di_plus>adx_mid and adx<adx_high)
        //     dir := -1
        //     cond := 'up-2-3'

        // weakening zone 2
        if bb_zone==2 and be_angle>be_u and be_dist>be_high and
         di_plus<adx_center and di_minus<adx_low and bbr>1
            dir := -1
            counter := 1
            cond := 'up-2-weak'

        // Counter 
        // up-3-4-cnt
        if bb_zone>2 and candle==0 and close<bb_lower and
         rwb_strong==1 and di_minus>adx_mid and 
         wae_color==red and di_minus>di_minus[1] 
            dir := 1
            counter := 1
            cond := 'up-3-4-cnt'

        // atr below ema 200
        if candle==0 and low<ema_200 and close>ema_200 and 
         (atr_lower<ema_200) and wae_line<wae_dz and
         res_c==gray and res<0 and bbl_angle>-1
            dir := 1
            counter := 1
            cond := 'up-atr-\nema-cnt'

        // atr below bb lower
        // if candle==0 and close<bb_lower and close>atr_b and 
        //  atr_lower<atr_b and res_c==gray and wae_line<wae_dz and
        //  macd_c<0
        //     dir := 1
        //     counter := 1
        //     cond := 'up-atr-\nbblower-cnt'


    // === Up Down ema ===
    // ===================
    if bb_lower<ema_200 and bb_upper>ema_200

        // Sell - 3-4-strong-01
        if bb_zone>2 and candle==1 and 
         rws_strong==1 and di_plus>adx_high and adx>di_plus and be_u>0
         and not(wae_line<wae_dz or wae_color!=green) 
         and not(di_plus>adx_high and adx_sma<adx_mid)
         and not(di_plus>adx_high and adx>adx_high and adx_sma<adx_high)
            dir := -1
            cond := '3-4-strong-01'

        // // Sell - 3-4-strong-02
        if bb_zone>2 and candle==1 and 
         rws_strong==1 and be_u>be_angle and adx>di_plus and be_u>0
         and not(wae_line<wae_dz or wae_color!=green)
         and not(di_plus>adx_high and adx_sma<adx_mid)
         and not(di_plus>adx_high and adx>adx_high and adx_sma<adx_high)
            dir := -1
            cond := '3-4-strong-02'

        // Strongest low
        if  bb_zone>2 and candle==0 and be_dist<be_down and
         rwb_strong==1 and be_l<0 and di_minus>adx_mid and
         wae_line>wae_dz and wae_color==red
            dir := 1
            cond := 'up-down-3-4'

        if  bb_zone<3 and candle==0 and close<atr_b and
         di_minus>adx_mid and adx_sma<adx_low and
         wae_line<wae_dz and wae_color==red
            dir := 1
            cond := 'b-up-down-0-1-2'


    // === Below ema ===
    // =================
    if bb_upper<ema_200

        // Strongest downtrend
        if bb_zone>2 and candle==0 and 
         rwb_strong==1 and di_minus>adx_high and
         be_l<0 and
         wae_line>wae_dz and wae_color==red
            dir := 1
            cond := 'down-3-4'


        //zone 2 - strongest
        if bb_zone==2 and candle==0 and close<atr_b and
         rwb_strong==1 and di_minus>adx_high and di_plus<adx_low and
         (adx>adx_high or adx_sma>adx)
            dir := 1
            cond := 'down-2-strong'
            
        //zone 2 - adx
        if bb_zone==2 and candle==0 and close<atr_b and
         bbr<0 and adx>adx_high and di_plus<adx_low and
         be_dist<be_low
            dir := 1
            cond := 'down-2-adx'

        // down-0-1-2-cnt - maybe kijun < bb_lower
        if bb_zone<3 and bbu_angle<0 and isdown() and 
         di_minus>adx_high and adx>di_minus and RSI_low<30 and bbr<0
            dir := 1
            counter := 1
            cond := 'down-0-1-2-cnt'

        if bb_zone<3 and bbu_angle<1 and isdown() and 
         di_minus>adx_mid and adx<adx_center and RSI_low<32 and bbr<0
            dir := 1
            counter := 1
            cond := 'down-0-1-2-cnt02'

        // Counter - zone 4
        // if bb_zone==4 and candle==0 and 
        //  adx_sma>adx and adx_sma>adx_high
        //     dir := 1
        //     counter := 1

        // == Counter ==

        // Below atr_s
        if candle==1 and basis_angle<0 and close>SSL3 and 
         (atr_upper>atr_s and high<atr_s and basis_angle<-2) and 
         di_plus>adx_center and be_angle>0 // res_c==aqua  and atr_upper<bb_upper
            dir := -1
            counter := 1
            cond := 'down-atr-s-cnt'

        // atr above ema 200
        if candle==1 and close>atr_s and 
         (atr_upper>ema_200 and high<ema_200 and bbl_angle<5 and bbu_angle<5 and basis_angle<0) and 
         di_plus>adx_center and di_plus>adx_sma and res_c==gray
            dir := -1
            counter := 1
            cond := 'down-atr_ema-cnt'

    // Filter out same direction trades, wait 4 candles
    // and price higher than 20 pips before allowing another trade
    if dir == state and use_logic
        allow := (bar_index - bar_num) > num_bars ? true : false
        // sell
        if dir == -1
            dir := ((close - lastPrice) * mult_diff) > num_pips and allow ? dir : 0
            //counter := dir!=0 ? 1 : 0
        // buy
        if dir == 1
            dir := ((close - lastPrice) * mult_diff) < (num_pips * -1) and allow ? dir : 0

            //counter := dir!=0 ? 1 : 0

    // if dir!=state and dir==1
    //     if abs( ((lastPrice - close) * mult_diff) ) < 50
    //         dir==0

    // Filter out high percentage trades
    // if wae_columns<4.7
    //     if counter == 0 and abs(perc_change())>20
    //         dir := 0
    //     if counter == 1 and abs(perc_change())>20
    //         dir := 0

    //var int enter_exit = basis_angle > 0 and dir == -1 ? 0 : basis_angle < 0 and dir
    type = dir > 0 ? 1 : dir < 0 ? -1 : 0
    [type,counter,cond]

[trade_dir,counter,condition] = entry_signal() 
    
// if trade_dir[1]==0 and trade_dir != 0
//     dir == 0

if trade_dir != 0 and use_logic
    state := trade_dir

    //enter_exit := counter == 1 ? 1 : 0
    // Sell
    // if basis_angle > 0 and trade_dir == -1
    //     enter_exit := -1
    // // Counter
    // if basis_angle > 0 and trade_dir == 1
    //     enter_exit := 0
    // // Buy
    // if basis_angle < 0 and trade_dir == 1
    //     enter_exit := -1
    // // Counter
    // if basis_angle < 0 and trade_dir == -1
    //     enter_exit := 0
    // if basis>ema_200
    //     enter_exit := trade_dir == -1 ? false : trade_dir == 1 or counter ? true : false
    // if basis<ema_200
    //     enter_exit := trade_dir == 1 ? false : trade_dir == -1 or counter ? true : false
    //enter_exit := counter == 1 ? true : enter_exit == false ? true : false
    lastPrice := close
    bar_num   := bar_index

// if enter_exit != trade_dir and trade_dir != 0
//     enter_exit := enter_exit == false ? true : false



// === Labels ===
show_exit = trade_dir == 1 and counter == 0? '\nExit': ''
labelText = tostring(condition) + show_exit
trade_color = trade_dir > 0 ? buy_color : sell_color
if trade_dir != 0 and show_cond
	if trade_dir > 0
        buy = label.new(x=time,y=high,text=labelText,xloc=xloc.bar_time, yloc=yloc.belowbar,color=trade_color,textcolor=color.white, size=size.normal,style=label.style_label_up)
    else
        sell = label.new(x=time,y=high,text=labelText,xloc=xloc.bar_time, yloc=yloc.abovebar,color=trade_color,textcolor=color.white, size=size.normal)

plot(trade_dir!=0?lastPrice:na,title="Last Price", style=plot.style_circles)
//plot(show_lastprice and lastPrice?lastPrice:na,title="Last Price", style=plot.style_circles)
plot(counter,title="Counter Trade", style=plot.style_circles)
//plot(state,"State",style=plot.style_circles)

// Buy
tmp_text = counter==1? "B":"Exit"
plotshape(show_entry and trade_dir == 1 and counter == 1 ? 1 : na, title="Entry Buy", color=trade_color, location = location.belowbar, style=shape.labelup, text="B", textcolor=color.white, size=size.small)
plotshape(show_entry and trade_dir == 1 and counter == 0 ? 1 : na, title="Exit Buy", color=trade_color, location = location.belowbar, style=shape.labelup, text="Exit", textcolor=color.white, size=size.small)
// Sell
plotshape(show_entry and trade_dir == -1 and counter == 1 ? -1: na, title="Entry Sell", color=trade_color, location = location.abovebar, style=shape.labeldown, text="S", textcolor=color.white, size=size.small)
plotshape(show_entry and trade_dir == -1 and counter == 0 ? -1 : na, title="Exit Sell", color=trade_color, location = location.abovebar, style=shape.labeldown, text="Exit", textcolor=color.white, size=size.small)
