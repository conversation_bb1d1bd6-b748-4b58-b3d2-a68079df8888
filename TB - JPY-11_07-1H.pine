//@version=4
study(title = "Trading Bot - JPY - Nov 7", shorttitle="TB - JPY - 11_07 - 1H",overlay=true)

show_entry = input(title="Show Entry", type=input.bool, defval=true)
show_atr = input(title="Show ATR", type=input.bool, defval=true)
show_cond = input(title="Show Conditions", type=input.bool, defval=true)
use_logic = input(title="Use Logic", type=input.bool, defval=false)
show_friday= input(title="Show Friday", type=input.bool, defval=true)
rsi_strong= input(title="Show only RSI strongest", type=input.bool, defval=false)
rsi1_show = input(title="Show RSI1", type=input.bool, defval=false)
rsi2_show = input(title="Show RSI2", type=input.bool, defval=true)
rsi3_show = input(title="Show RSI2", type=input.bool, defval=false)

red = #ff0062
aqua = #00bcd4
yellow = #FFFF00
orange = #ff9800
green = #4caf50
lime = #00E676
white = #ffffff
c_hide = color.new(#ffffff,100)
blue = #42a5f5
violet = #814dff
gray = #707070
black = #000000
sell_color = color.new(#ff0062,20)
buy_color = color.new(#00c3ff,20)

friday = color.new(sell_color,92)
bgcolor(show_friday and dayofweek == 1 ? friday : na)

angle(_src, len) =>
    rad2degree=180/3.14159265359  //pi 
    ang=rad2degree*atan((_src[0] - _src[1])/atr(len))

// Change
perc_change() =>
    perc = abs( (1 - (close[1] / close)) * 10000 )


// ===  EMA's ===
// ==================================================
ema_200 = ema(close, 200)
ema_200_angle = angle(ema_200,2)
ema_len_f = 5 //input(5, minval=1, title="EMA Length") //6
ema_fast = ema(close, ema_len_f)
ema_angle = angle(ema_fast,2)


// ===  ATR ===
// ==================================================
atrlen = 14 //input(14, "ATR Period")
atr_mult = input(2.0, "ATR Mult", step = 0.1) // 1.35 1.15
atr_stop = 0.0003 //input(0.0003, "ATR Stop", step = 0.0001) // 0.001
atr_slen = wma(tr(true), atrlen)
atr_upper = atr_slen * atr_mult + close
atr_lower = close - atr_slen * atr_mult


// ===  Kijun ===
// ==================================================
middleDonchian(Length) =>
    lower = lowest(Length)
    upper = highest(Length)
    avg(upper, lower)

basePeriods = input(5, title="kijun Len",minval=1) // 14 26
kijun =  middleDonchian(basePeriods)
k_angle = angle(kijun,3)
xChikou = close
xPrice = close




// ===  BB ===
// ==================================================
BB_length = input(45) // 45 100 125 45 23
BB_stdDev = 2 //input(2, minval=2.0, maxval=3)
sqz_length = 100 //input(100, minval=2, maxval=200) // 100
sqz_threshold = 76 
bb_s = kijun
basis = sma(close, BB_length)
dev = BB_stdDev * stdev(close, BB_length)
bb_upper = basis + dev
bb_lower = basis - dev
bb_spread = bb_upper - bb_lower
avgspread = sma(bb_spread, sqz_length)
bb_squeeze = 0.00
bb_squeeze := bb_spread / avgspread * 100
basis_angle = angle(basis,3)
bbu_angle = angle(bb_upper,3)
bbl_angle = angle(bb_lower,3)
// BB Zones
bb_zone = bb_squeeze < 53 ? 0 : 
 bb_squeeze < sqz_threshold ? 1 : 
 bb_squeeze < 120 ? 2 :
 bb_squeeze < 181 ? 3 :
 bb_squeeze > 181 ? 4 : na
sqz_color = bb_zone == 0 ? #0045b3 :
 bb_zone == 1 ? #ff0062 : 
 bb_zone == 2 ?  gray : 
 bb_zone == 3 ?  #00c3ff : 
 bb_zone == 4 ? white: na

bb_zones_color =  sqz_color


// ===  SSL ===
// ==================================================
ssl_len1 = input(45, minval=1,title="SSL 1")
ssl_len2 = input(60, minval=1,title="SSL 2") //75
ssl_len3 = input(15, minval=1,title="SSL 3") // 8 7
ssl_len4 = input(75, minval=1,title="SSL 4") // 75 100
ssl_len5 = input(150, minval=1,title="SSL 5") //7
SSL1 = wma(2*wma(close, ssl_len1/2)-wma(close, ssl_len1), round(sqrt(ssl_len1)))
SSL2 = wma(2*wma(close, ssl_len2/2)-wma(close, ssl_len2), round(sqrt(ssl_len2)))
SSL3 = wma(2*wma(close, ssl_len3/2)-wma(close, ssl_len3), round(sqrt(ssl_len3)))
SSL4 = wma(2*wma(close, ssl_len4/2)-wma(close, ssl_len4), round(sqrt(ssl_len4)))
SSL5 = wma(2*wma(close, ssl_len5/2)-wma(close, ssl_len5), round(sqrt(ssl_len5)))
//ssl_angle = angle(SSL,3)
ssl_angle  = angle(SSL1,2)
ssl_angle2 = angle(SSL2,2)
ssl_angle3 = angle(SSL3,2)
ssl_angle4 = angle(SSL4,2)
ssl_angle5 = angle(SSL5,2)
ssl4_dist = basis_angle>0 ? (bb_upper-SSL4)*100 : (SSL4 - bb_lower)*100 
basis_dist = basis_angle>0 ? (ema_200-basis)*100 : (basis - ema_200)*100 
ssl_color  = ssl_angle > 0 ? blue : red




// === Bollinger Bands %B ===
// ==================================================
bbr_len = 20 //input(45, minval=1)
bbr_multi = 2 //input(2.0, minval=0.001, maxval=50, title="StdDev")
bbr_basis = sma(close, bbr_len)
deviation = bbr_multi * stdev(close, bbr_len)
bbr_upper = bbr_basis + deviation
bbr_lower = bbr_basis - deviation
bbr = (close - bbr_lower)/(bbr_upper - bbr_lower)



// === WAE ===
// ==================================================
sensitivity = 150
wae_fast= 20 //8
wae_slow= 40 // 50
channelLength= 20 // 45
wae_mult = 2.0//1.85
wae_dz = nz(rma(tr(true),100)) * 3.7
calc_macd(source, wae_fast, wae_slow) =>
	a = ema(source, wae_fast)
	b = ema(source, wae_slow)
	a - b
calc_BBUpper(source, length, wae_mult) => 
	wae_basis = wma(source, length)
	wae_dev = wae_mult * stdev(source, length)
	t = wae_basis + wae_dev
	[t]
calc_BBLower(source, length, wae_mult) => 
	wae_basis = wma(source, length)
	wae_dev = wae_mult * stdev(source, length)
	t = wae_basis - wae_dev
	[t]

t1 = (calc_macd(close, wae_fast, wae_slow) - calc_macd(close[1], wae_fast, wae_slow))*sensitivity * 100
t2 = (calc_macd(close[2], wae_fast, wae_slow) - calc_macd(close[3], wae_fast, wae_slow))*sensitivity * 100
[e1a] = calc_BBUpper(close, channelLength, wae_mult)
[e1b] = calc_BBLower(close, channelLength, wae_mult)
wae_line = (e1a - e1b)
wae_diff = (wae_line - wae_dz) * 100
//wae_perc = (wae_line / wae_dz) * 100
trendUp = (t1 >= 0) ? t1 : 0
trendDown = (t1 < 0) ? (-1*t1) : 0
wae_color = #000000
if t1 >= 0
	wae_color := trendUp<trendUp[1] ? lime : green
if t1 < 0
	wae_color := trendDown<trendDown[1] ? orange : red
wae_angle = angle(wae_line,3)



// === ADX + DI with SMA ===
// ==================================================
adx_len = input(9,title="ADX len") // 14
adx_line = 20 // input(title="threshold", defval=20)
adx_avg = input(8,title="ADX SMA") // 10
var float adx_top = 54 // input(55,title="High")
var float adx_high = 38 // 39
var float adx_mid = 33
var float adx_center = 20
var float adx_low = 12
var float smooth_tr = 0
var float smooth_di_plus = 0
var float smooth_di_minus = 0
TrueRange = max(max(high-low, abs(high-nz(close[1]))), abs(low-nz(close[1])))
DI_plus = high-nz(high[1]) > nz(low[1])-low ? max(high-nz(high[1]), 0): 0
DI_minus = nz(low[1])-low > high-nz(high[1]) ? max(nz(low[1])-low, 0): 0
smooth_tr := nz(smooth_tr[1]) - (nz(smooth_tr[1])/adx_len) + TrueRange
smooth_di_plus := nz(smooth_di_plus[1]) - (nz(smooth_di_plus[1])/adx_len) + DI_plus
smooth_di_minus := nz(smooth_di_minus[1]) - (nz(smooth_di_minus[1])/adx_len) + DI_minus

di_plus = smooth_di_plus / smooth_tr * 100
di_minus = smooth_di_minus / smooth_tr * 100
DX = abs(di_plus-di_minus) / (di_plus+di_minus)*100
adx = sma(DX, adx_len)
adx_sma = sma(adx, adx_avg)
adx_angle = (angle(adx,2))
//hline(adx_line, color=black, linestyle=dashed)



// === RSI Wicks ===
// ==================================================
std         = false //input(false, title="Show Standard RSI")
rsi_candles = false //input(true,  title="Show Candles")
wicks       = true  //input(true,  title="Wicks based on stand-alone RSI")
src_close   = close
src_open    = open
src_high    = high
src_low     = low 

rsiw_len1    = input(28, title="RSIW len1") // 8 10 14
rsiw_len2    = input(14, title="RSIW len2") // 8 10 14
rsiw_len3    = input(5, title="RSIW len3") // 8 10 14

rsi_wicks(rsi_len) =>
    norm_close  = avg(src_close,src_close[1])
    gain_loss_close   = change(src_close)/norm_close
    RSI_close         = 50+50*rma(gain_loss_close, rsi_len)/rma(abs(gain_loss_close), rsi_len)

    norm_open = if wicks==true 
        avg(src_open,src_open[1])
    else 
        avg(src_close,src_close[1])
    gain_loss_open   = change(src_open)/norm_open
    RSI_open         = 50+50*rma(gain_loss_open, rsi_len)/rma(abs(gain_loss_open), rsi_len)
            
    norm_high = if wicks==true 
        avg(src_high,src_high[1])
    else 
        avg(src_close,src_close[1])
    gain_loss_high   = change(src_high)/norm_high
    RSI_high         = 50+50*rma(gain_loss_high, rsi_len)/rma(abs(gain_loss_high), rsi_len)
            
    norm_low  = if wicks==true
        avg(src_low,src_low[1])
    else 
        avg(src_close,src_close[1])
    gain_loss_low   = change(src_low)/norm_low
    RSI_low         = 50+50*rma(gain_loss_low, rsi_len)/rma(abs(gain_loss_low), rsi_len)

    // Sell
    rws_weak = RSI_close<70 and RSI_high>70 and rsi_strong==0? 1 : 0
    rws_mid = RSI_close>70 and RSI_open<70 and rsi_strong==0? 1 : 0 
    rws_strong = RSI_open>70 ? 1 : 0

    // Buy
    rwb_weak = RSI_close>30 and RSI_low<30 and rsi_strong==0? 1 : 0
    rwb_mid = RSI_close<30 and RSI_open>30 and rsi_strong==0? 1 : 0
    rwb_strong = RSI_open<30 ? 1 : 0

    [rws_mid,rws_strong,rwb_mid,rwb_strong,RSI_close]

[rws_mid1,rws_strong1,rwb_mid1,rwb_strong1,rsi_close1] = rsi_wicks(rsiw_len1)
[rws_mid2,rws_strong2,rwb_mid2,rwb_strong2,rsi_close2] = rsi_wicks(rsiw_len2)
[rws_mid3,rws_strong3,rwb_mid3,rwb_strong3,rsi_close3] = rsi_wicks(rsiw_len3)




// === RSI Candles - glaz ===
// ==================================================
var int pos = 0
var rsi_color = #000000
rsi_len = 14 //input(14, minval=1, title="Length")
rsi_up = rma(max(change(close), 0), rsi_len)
rsi_down = rma(-min(change(close), 0), rsi_len)
rsi = rsi_down == 0 ? 100 : rsi_up == 0 ? 0 : 100 - (100 / (1 + rsi_up / rsi_down))
isup() => rsi > 70
isdown() => rsi < 30 
// plot(rsi,title="RSI",color=color.blue)
rsi_color := isup() ? color.new(#ff0000, 0) : isdown() ? color.new(color.purple, 0)  : na




// === MACD ===
// ==================================================
fast_length = 8 // 12
slow_length = 25 // 26
md_src = close
signal_length = 9
sma_source = true
sma_signal = true
// Plot colors
col_grow_above = #26A69A
col_fall_above = #B2DFDB
col_grow_below = #FFCDD2
col_fall_below = #EF5350
col_macd = #0094ff
col_signal = #ff6a00
// Calculating
md_fast_ma = sma_source ? sma(md_src, fast_length) : ema(md_src, fast_length)
slow_ma = sma_source ? sma(md_src, slow_length) : ema(md_src, slow_length)
macd = md_fast_ma - slow_ma
macd_s = sma_signal ? sma(macd, signal_length) : ema(macd, signal_length)
m_histo = macd - macd_s
// plot(hist, title="Histogram", style=plot.style_columns, color=(hist>=0 ? (hist[1] < hist ? col_grow_above : col_fall_above) : (hist[1] < hist ? col_grow_below : col_fall_below) ), transp=0 )
// plot(macd, title="MACD", color=col_macd, transp=0)
// plot(signal, title="Signal", color=col_signal, transp=0)



// === Plot === 
// ==================================================
// Angles
//plot(bb_squeeze, color=bb_zones_color, title="BB Squeeze",style=plot.style_circles)
//plot(bbr,title='BBR', style=plot.style_circles)
plot(bb_upper - bb_lower,title="BB diff", style=plot.style_circles)
plot(ema_200_angle, color= c_hide , title="EMA 200 Angle",style=plot.style_circles)
plot(basis_angle, color= c_hide , title="Basis angle",style=plot.style_circles)
plot(ssl_angle, color=c_hide , title="SSL angle",style=plot.style_circles)
plot(ssl_angle2, color=c_hide , title="SSL angle 2",style=plot.style_circles)
plot(ssl_angle4, color=c_hide , title="SSL angle 4",style=plot.style_circles)
plot(ssl_angle5, color=c_hide , title="SSL angle 5",style=plot.style_circles)
plot(bbu_angle, color= c_hide , title="BB Upper Angle",style=plot.style_circles)
plot(bbl_angle, color= c_hide , title="BB Lower Angle",style=plot.style_circles)
plot(ssl4_dist,title="SSL4 Dist",style=plot.style_circles,color=color.new(color.black,100) )
plot(basis_dist,title="Basis Dist",style=plot.style_circles,color=color.new(color.black,100) )
//plot(rsi_close1,title="RSI close1",style=plot.style_circles)
//plot(rsi_close2,title="RSI close2",style=plot.style_circles)
//plot(rsi_close3,title="RSI close3",style=plot.style_circles)
////plot(res, color=res_c, title="RES",style=plot.style_circles)

// plot(be_dist, color= c_hide , title="BE Dist",style=plot.style_circles)
// plot(mom_stoch, color= c_hide , title="MOM smi",style=plot.style_circles)
// plot(mom_ema, color= c_hide , title="MOM ema",style=plot.style_circles)
plot(di_plus, color=green, title="DI+",style=plot.style_circles)
plot(di_minus, color=red, title="DI-",style=plot.style_circles)
plot(adx, color=yellow, title="ADX",style=plot.style_circles)
plot(adx_sma, color=c_hide, title="ADX SMA",style=plot.style_circles)
plot(wae_line,title="Explosion Line",style=plot.style_circles,color=wae_color)
plot(wae_dz,title="Dead Zone",style=plot.style_circles)
plot(wae_angle,title="WAE angle",style=plot.style_circles)

plot(macd_s,title='MACD Sig', style=plot.style_circles)
plot(m_histo,title='MACD Histo', style=plot.style_circles)

//plot(stc, color= c_hide , title="STC",style=plot.style_circles)
plot(bbr, "Bollinger Bands %B", color=color.teal,linewidth=0)
//plot(rsi, "RSI", color=#8E1599)

// Kijun
plot(kijun, color=#f44336, title="Kijun",linewidth=2)
// SSL
plot(SSL1, color=ssl_color , title="SSL 1", linewidth=2)
plot(SSL2, color=orange , title="SSL 2")
plot(SSL3, color=aqua , title="SSL 3")
plot(SSL4, color=yellow , title="SSL 4",linewidth=2)
plot(SSL5, color=green , title="SSL 5",linewidth=2)

// BB
plot(basis, title="Basis", color=bb_zones_color,linewidth=3)
p1 = plot(bb_upper, "BB Upper ",color=bb_zones_color,linewidth=2)
p2 = plot(bb_lower, "BB Lower ",color=bb_zones_color,linewidth=2)
//fill(p1,p2, bb_zones_color)
// EMA 200 bands large
plot(ema_200, "EMA 200", color=color.new(yellow,50),linewidth=2 )
//plot(ema_fast, color=color.blue, title="EMA Fast")
// ATR
plot(show_atr ? atr_upper : na, "+ATR Upper", color=color.new(#ffffff,95))
plot(show_atr ? atr_lower : na, "-ATR Lower", color=color.new(#ffffff,95))
// plot(show_atr ? atr_upper + atr_stop : na, "+ATR stop", color=color.new(#ffff00,85))
// plot(show_atr ? atr_lower - atr_stop : na, "-ATR stop", color=color.new(#ffff00,85))


// RSI close

plotshape(rsi1_show and rsi_close1>70?1:na,title="RSI 1",color=rws_mid1==1?orange:rws_strong1==1?#ff0000:na,style=shape.circle,location=location.top)
plotshape(rsi1_show and rsi_close1<30?1:na,title="RSI 1",color=rwb_mid1==1?#0053ff:rwb_strong1==1?lime:na,style=shape.circle,location=location.bottom)
plotshape(rsi2_show and rsi_close2>70 and close>open?1:na,title="RSI 2",color=rws_mid2==1?orange:rws_strong2==1?#ff0000:na,style=shape.circle,location=location.top)
plotshape(rsi2_show and rsi_close2<30 and close<open?1:na,title="RSI 2",color=rwb_mid2==1?#0053ff:rwb_strong2==1?lime:na,style=shape.circle,location=location.bottom)
plotshape(rsi3_show and rsi_close3>70?1:na,title="RSI 3",color=rws_mid3==1?orange:rws_strong3==1?#ff0000:na,style=shape.circle,location=location.top)
plotshape(rsi3_show and rsi_close3<30?1:na,title="RSI 3",color=rwb_mid3==1?#0053ff:rwb_strong3==1?lime:na,style=shape.circle,location=location.bottom)

// Bar color
barcolor(rsi_color, title="Rsi Candles")
//barcolor(res_c, title="Res Color")
//barcolor(pos == -1 ? #ff0000: pos == 1 ? #00a000 : gray)


var bool enter_exit = false
//var int trade_dir = 0
var int state = 0
var int bar_num = 0
var float lastPrice = 0.0
var mult_diff = 10000
var int num_pips = 17
var int num_bars = 4

entry_signal() =>
	candle = close > open ? 1 : 0
    dir = 0
    counter = 0
    cond = ''
    allow = false
    ea = ema_200_angle
    ba = basis_angle


    // === Sell ===
    // ===========

    // Notes
    // if open outside bbands filter out if SSL5 is lower or above basis
    // when to trigger strongest trend maybe when SSL3 changes direction against the trend direction

    // extreme high - Oct 6
    // extreme low - Jun 3, Jul 6, Oct 11, 14
    
    // Sell
    if ea<2

        if candle==1 and rsi_close1>70 and wae_line>wae_dz and wae_color==green and 
         SSL5>basis
            dir := -1
            cond := 'ea-rsi1'

        if candle==1 and rsi_close2>70 and wae_line>wae_dz and wae_color==green
         and not(rsi_close1>70) and SSL5>basis
            dir := -1
            cond := 'ea-rsi2'

        if candle==1 and bb_zone<3 and rsi_close1>70 and wae_line>wae_dz and wae_color==green and 
         SSL5>basis
            dir := -1
            cond := 'ea-z1-rsi2'

    if candle==1 and rws_strong1==1 and SSL3>bb_upper and 
     wae_color==green and di_plus>di_plus[1]
     and not(adx_sma<di_plus)
        dir := -1
        cond := 'rws1'

    if candle==1 and bb_zone>2 and close<bb_upper and atr_upper>bb_upper and
     adx_sma>adx and adx>adx_high and rsi_close2>70
        dir := -1
        cond := 'adx-close'

    // if candle==1 and rsi_close1>70 and close<bb_upper
    //     dir := -1
    //     cond := 'rws1-close'

    if candle==1 and ea>2 and open>ema_200 and ba<7 and 
     rws_strong2==1 and wae_line>wae_dz and wae_color==green
        dir := -1
        cond := 'rws2'

    // need to fix Oct 7th
    if candle==1 and close<bb_upper and atr_upper>bb_upper and 
     rws_strong3==1 and SSL5>SSL4 and SSL3>SSL5 and SSL4>basis and
     bb_lower>ema_200
        dir := -1
        cond := 'rws3-ssl5'

    // Counter
    if ba>3 and candle==0 and basis>ema_200 and 
     rsi_close2<30 and SSL5<SSL4
        dir := 1
        cond := 'rwb2-cnt'

    if ssl_angle5>0 and close<basis and candle==0 and basis>ema_200 and 
     rwb_strong3==1 and adx>di_minus and di_minus>di_minus[1] and
     wae_line>wae_dz
        dir := 1
        cond := 'rwb3-strong-cnt'

    if ba>3 and low>basis and candle==0 and basis>ema_200 and 
     rsi_close3<30 and SSL5<SSL4 and m_histo<0
        dir := 1
        cond := 'rwb3-cnt'



    // Buy
    if candle==0 and rsi_close1<30 and SSL3<bb_lower and wae_color==red
        dir := 1
        cond := 'rwb1'

    if candle==0 and rsi_close1<30 and close>bb_lower
        dir := 1
        cond := 'rwb1-close'

    if candle==0 and ba>-7.5 and 
     rwb_strong2==1 and wae_line>wae_dz and wae_color==red
     and not(bb_zone==3 and SSL4>SSL5)
     and not(SSL3>kijun)
        dir := 1
        cond := 'rwb2'

    if candle==0 and ba>-10 and SSL3<bb_lower and
     rwb_strong2==1 and wae_line>wae_dz 
        dir := 1
        cond := 'rwb2-strong'

    if candle==0 and close>bb_upper and ba>-10 and 
     adx_sma>adx and adx_sma>adx_high
        dir := -1
        cond := 'adx-close'


    // Filter out same direction trades, wait 4 candles
    // and price higher than 20 pips before allowing another trade
    if dir == state and use_logic and counter==0
        allow := (bar_index - bar_num) >= num_bars ? true : false
        // sell
        if state == -1
            dir := ((close - lastPrice) * mult_diff) > num_pips and allow ? dir : 0
        // buy
        if state == 1
            dir := ((close - lastPrice) * mult_diff) < (num_pips * -1) and allow ? dir : 0

    //var int enter_exit = basis_angle > 0 and dir == -1 ? 0 : basis_angle < 0 and dir
    type = dir > 0 ? 1 : dir < 0 ? -1 : 0
    [type,counter,cond]

[trade_dir,counter,condition] = entry_signal() 

if trade_dir != 0 and use_logic
    state := trade_dir

    enter_exit := counter == 1 ? 1 : 0
    // Sell
    // if basis_angle > 0 and trade_dir == -1
    //     enter_exit := -1
    // // Counter
    // if basis_angle > 0 and trade_dir == 1
    //     enter_exit := 0
    // // Buy
    // if basis_angle < 0 and trade_dir == 1
    //     enter_exit := -1
    // // Counter
    // if basis_angle < 0 and trade_dir == -1
    //     enter_exit := 0
    // if basis>ema_200
    //     enter_exit := trade_dir == -1 ? false : trade_dir == 1 or counter ? true : false
    // if basis<ema_200
    //     enter_exit := trade_dir == 1 ? false : trade_dir == -1 or counter ? true : false
    //enter_exit := counter == 1 ? true : enter_exit == false ? true : false
    lastPrice := close
    bar_num   := bar_index

// if enter_exit != trade_dir and trade_dir != 0
//     enter_exit := enter_exit == false ? true : false
labelText = tostring(condition)
trade_color = trade_dir > 0  ? buy_color : sell_color
if trade_dir != 0 and show_cond
	if trade_dir > 0
        buy = label.new(x=time,y=high,text=labelText,xloc=xloc.bar_time, yloc=yloc.belowbar,color=trade_color,textcolor=color.white, size=size.normal,style=label.style_label_up)
    else
        sell = label.new(x=time,y=high,text=labelText,xloc=xloc.bar_time, yloc=yloc.abovebar,color=trade_color,textcolor=color.white, size=size.normal)

plot(counter,title="Counter Trade", style=plot.style_circles)
plot(state,"State",style=plot.style_circles)

plotshape(show_entry and trade_dir == 1 and enter_exit == 0 ? 1 : na, title="Entry Buy", color=trade_color, location = location.belowbar, style=shape.labelup, text="B", textcolor=color.white, size=size.small)
plotshape(show_entry and trade_dir == 1 and enter_exit == -1 ? 1 : na, title="Exit Buy", color=trade_color, location = location.belowbar, style=shape.labelup, text="Exit", textcolor=color.white, size=size.small)
plotshape(show_entry and trade_dir == -1 and enter_exit == 0 ? -1: na, title="Entry Sell", color=trade_color, location = location.abovebar, style=shape.labeldown, text="S", textcolor=color.white, size=size.small)
plotshape(show_entry and trade_dir == -1 and enter_exit == -1 ? -1 : na, title="Exit Sell", color=trade_color, location = location.abovebar, style=shape.labeldown, text="Exit", textcolor=color.white, size=size.small)


