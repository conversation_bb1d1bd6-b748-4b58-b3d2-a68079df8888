//@version=5
indicator("PD Retrace - New", overlay=true, max_labels_count = 500)

i_res = input.timeframe('240', "Resolution")
i_lookback = input.int(1, title='Lookback')
i_show_labels = input.bool(false, title="Show Labels")
i_bars_merge = input.bool(true, title='Lookahead On')
i_bars_lookback = input.int(60, title='Bars Back')

change = ta.change(time(i_res))
allow_plot = 0
var float fibo0   = na
var float fibo100 = na
var float fibo38  = na
var float fibo50  = na
var float fibo62  = na
if change
    fibo0   := high
    fibo100 := low
    fibo38  := (fibo100-fibo0)*0.628+fibo0
    fibo50  := (fibo100-fibo0)/2+fibo0
    fibo62  := (fibo100-fibo0)*0.382+fibo0
    allow_plot := 1

plot( allow_plot )
plotshape(change?1:na,title="Change",color=#00ff00 ,style=shape.circle,location=location.top)
p_fib0 = plot( allow_plot==1 ? fibo0 : fibo0[1] ,color=color.red,linewidth=2)
p_fibo100 = plot( allow_plot==1 ? fibo100 : fibo100[1] ,color=color.green,linewidth=2)
p_fibo38 = plot( allow_plot==1 ? fibo38 : fibo38[1],linewidth=0 )
p_fibo50 = plot( allow_plot==1 ? fibo50 : fibo50[1],linewidth=0 )
p_fibo62 = plot( allow_plot==1 ? fibo62 : fibo62[1],linewidth=0 )

allow_plot := 0


if change and i_show_labels
    f0ch = ta.change(fibo0)
    f1000ch = ta.change(fibo100)

    higher = fibo0 > fibo0[1] ? "H:\n " + str.tostring(f0ch) + '%' : fibo0 < fibo0[1] ? "L:\n " + str.tostring(f0ch) + '%' : "No Change: " + str.tostring(f0ch)
    txt1 = str.tostring(higher)
    info1 = label.new(x=time,y=fibo0,xloc=xloc.bar_time, text=txt1, textcolor=#ffffff,size=size.normal, color=color.black)

    yval = fibo0 + high
    lower = fibo100 > fibo100[1] ? "H:\n " + str.tostring(f1000ch) + '%'  : fibo100 < fibo100[1] ? "L:\n " + str.tostring(f1000ch) + '%' : "No Change: " + str.tostring(f1000ch)
    txt2 = str.tostring(lower)
    info2 = label.new(x=time,y=fibo100,xloc=xloc.bar_time, text=txt2, textcolor=#ffffff, style=label.style_label_up,size=size.normal, color=color.black)

// fill(p_fibo38,p_fibo50, color=color.aqua)
// fill(p_fibo50,p_fibo62, color=color.yellow)