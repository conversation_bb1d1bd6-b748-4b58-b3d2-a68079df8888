//@version=4
study(title="BB Bands - Angels", overlay=false, shorttitle="BB Bands - Angels")

bb_trans = input(50,title="Bands transparency")
white = #ffffff
gray = #707070
red = #ff0000
green = #00ff00

angle(_src, len) =>
    rad2degree=180/3.14159265359  //pi 
    ang=rad2degree*atan((_src[0] - _src[1])/atr(len))

// ===  Bollinger Bands ===
// ==================================================
BB_length = input(20) // 45 100 25
BB_stdDev = 2 //input(2, minval=2.0, maxval=3)
sqz_length = 100 //input(100, minval=2, maxval=200) // 100
sqz_threshold = 76 
basis = sma(close, BB_length)
dev = BB_stdDev * stdev(close, BB_length)
bb_upper = basis + dev
bb_lower = basis - dev
bb_spread = bb_upper - bb_lower
avgspread = sma(bb_spread, sqz_length)
bb_squeeze = 0.00
bb_squeeze := bb_spread / avgspread * 100
bb_diff = (bb_upper - bb_lower) * 1000
basis_angle = angle(basis,3)
bbu_angle = angle(bb_upper,3)
bbl_a = angle(bb_lower,3)

bb_zone = bb_squeeze < 53 ? 0 : 
 bb_squeeze < sqz_threshold ? 1 : 
 bb_squeeze < 120 ? 2 :
 bb_squeeze < 160 ? 3 :
 bb_squeeze > 160 ? 4 : na
sqz_color = bb_zone == 0 ? #0045b3 :
 bb_zone == 1 ? #ff0062 : 
 bb_zone == 2 ?  gray : 
 bb_zone == 3 ?  #00c3ff : 
 bb_zone == 4 ? white: na

bb_zones_color =  color.new(sqz_color,bb_trans)

p1 = plot(bb_upper, "BB Upper ",color=bb_zones_color,linewidth=2)
p2 = plot(bb_lower, "BB Lower ",color=bb_zones_color,linewidth=2)
plot(basis, "Basis ",color=bb_zones_color,linewidth=2)
plot(bb_diff,title="BB Diff")
plot(bbu_angle,title="BB Upper Angle",color=red)
plot(bbl_a,title="BB Lower Angle",color=green)
plotshape(bbu_angle>basis?1:na, title='Sell signal', color=green, style=shape.circle,location=location.bottom)
