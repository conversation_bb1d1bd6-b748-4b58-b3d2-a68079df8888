//@version=4
study(title="MACD Crossover", shorttitle="MACD Crossover")
blue = #00c3ff
red = #ff0000
aqua = #00bcd4
yellow = #FFFF00
orange = #ff9800
green = #00a000
white = #ffffff
gray = #707070

angle(_src, len) =>
    rad2degree=180/3.14159265359  //pi 
    ang=rad2degree*atan((_src[0] - _src[1])/atr(len))

pos = 0
fastLength = input(8, minval=1) // 8
slowLength = input(25,minval=1) // 16
signalLength=input(9,minval=1) // 11

macd_mult = syminfo.currency == 'JPY' ? 100 : syminfo.currency == 'NZD' ? 10000 : 10000
fastMA = ema(close, fastLength)
slowMA = ema(close, slowLength)
macd = (fastMA - slowMA) * macd_mult
signal = (sma(macd, signalLength) )
pre_angle = (macd*1000) - 900
macd_angle=angle((fastMA - slowMA) ,2)

macd_high = 39
macd_up = 16
macd_down = -16
macd_low = -39

// macd_high = syminfo.currency == 'NZD' ? 0.00170 : syminfo.currency == 'JPY' ? 0.395 : 0.0012
// macd_up = 0.160
// macd_down = -0.160
// macd_low = syminfo.currency == 'NZD' ? -0.00170 : syminfo.currency == 'JPY' ? -0.395 : -0.0012




pos := iff(signal < macd , 1,iff(signal > macd, -1, nz(pos[1], 0))) 
barcolor(pos == -1 ? red: pos == 1 ? green : blue)
plot(signal, color=red, title="SIGNAL")
plot(macd_high, color=blue, title="Line Up")
plot(macd_low, color=blue, title="Line down")
plot(macd, color=blue, title="MACD")
//plot(macd_angle,title="Angle Macd",style=plot.style_circles)
//plotshape(down, style=shape.circle,location=location.bottom,color=#008800,text="3",textcolor=#ffffff)

down = macd<macd_down and macd_angle>-2 ?1:na
low = signal<macd_low ?1:na
// Cross macd to
plot(cross(signal, macd) ? signal : na,color=macd>signal?#50ff00:#ff0000 ,style = plot.style_circles, linewidth = 2)
// Cross Signal to MACD
plot(cross(signal, macd) ? signal : na,color=macd>signal?#50ff00:#ff0000 ,style = plot.style_circles, linewidth = 2)
plotshape(low, style=shape.circle,location=location.bottom,color=#008800,text="",textcolor=#ffffff)

hline(0, color=white, linestyle=hline.style_dashed)
hline(macd_up, color=color.new(#ff0000,50), linestyle=hline.style_dashed)
hline(macd_down, color=color.new(#ff0000,50), linestyle=hline.style_dashed)
hline(macd_high, color=color.new(#ff0000,50), linestyle=hline.style_dashed)
hline(macd_low, color=color.new(#ff0000,50), linestyle=hline.style_dashed)

// hline(mcu_line, "Hi Line")
// hline(mcd_line, "Down Line")

