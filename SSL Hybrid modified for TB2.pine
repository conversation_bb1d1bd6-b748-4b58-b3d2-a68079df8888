//@version=4
// === SSL Hybrid - Mihkel00 ===
// ==================================================
study("SSL Hybrid modified for TB", overlay=true)

show_atr = input(title="Show ATR", type=input.bool, defval=true)

red = #ff0000
red_light = #ff0062
aqua = #00bcd4
yellow = #FFFF00
orange = #ff9800
green = #4caf50
green_light = #4caf50
lime = #00E676
white = #ffffff
blue = #42a5f5
gray = #707070
black = #000000

angle(_src, len) =>
    rad2degree=180/3.14159265359  //pi 
    ang=rad2degree*atan((_src[0] - _src[1])/atr(len))

candle = close > open ? 1 : 0

// ===  EMA 200 ===
// ==================================================
ema_200 = ema(close, 200)
ema_angle = angle(ema_200,2)

// ===  ATR ===
// ==================================================
atrlen = 14 //input(14, "ATR Period")
atr_mult = 1.8 //input(1.8, "ATR Mult", step = 0.1) // 1.15
atr_stop = 0.0003 //input(0.0003, "ATR Stop", step = 0.0001) // 0.001
atr_slen = wma(tr(true), atrlen)
atr_upper = atr_slen * atr_mult + close
atr_lower = close - atr_slen * atr_mult

atr_offset = input(5, "ATR Offset") 
atr_s = sma(atr_upper,atrlen+atr_offset)
atr_b = sma(atr_lower,atrlen+atr_offset)

// SSL Long
ssl_long_input = input(100, minval=1,title="SSL Long")
ssl_long = wma(2*wma(close, ssl_long_input/2)-wma(close, ssl_long_input), round(sqrt(ssl_long_input)))
ssl_l_angle = angle(ssl_long,2)

// === SSL Hybrid ===
// ==================================================
ssl_h_len = input(225,title = "SSL1 / Baseline Length") // 200

//ATR
ssl_atr_len = input(14, "ATR Period")
mult = input(1, "ATR Multi", step = 0.1)
smoothing = input(title = "ATR Smoothing", defval = "WMA", options = ["RMA", "SMA", "EMA", "WMA"])

ma_function(source, ssl_atr_len) =>
	if smoothing == "RMA"
		rma(source, ssl_atr_len)
	else
		if smoothing == "SMA"
			sma(source, ssl_atr_len)
		else
			if smoothing == "EMA"
				ema(source, ssl_atr_len)
			else
				wma(source, ssl_atr_len)

ssl_atr_slen = ma_function(tr(true), ssl_atr_len)

// ATR Up/Low Bands
upper_band = ssl_atr_slen * mult + close
lower_band = close - ssl_atr_slen * mult

// SSL 1 and SSL2
emaHigh = wma(2 * wma(high, ssl_h_len / 2) - wma(high, ssl_h_len), round(sqrt(ssl_h_len)))
emaLow = wma(2 * wma(low, ssl_h_len / 2) - wma(low, ssl_h_len), round(sqrt(ssl_h_len)))

// BASELINE VALUES
ssl_baseline = wma(2 * wma(close, ssl_h_len / 2) - wma(close, ssl_h_len), round(sqrt(ssl_h_len)))
multy = 0.2
range = tr
rangema = ema(range, ssl_h_len)
upperk =ssl_baseline + rangema * multy
lowerk = ssl_baseline - rangema * multy

//SSL1 VALUES
Hlv = int(na)
Hlv := close > emaHigh ? 1 : close < emaLow ? -1 : Hlv[1]
sslDown = Hlv < 0 ? emaHigh : emaLow

//COLORS
color_ssl1 = close > sslDown ? #00c3ff : close < sslDown ? #ff0062 : na
ssl_color = close > upperk ? aqua : close < lowerk ? red_light : color.gray

trend = ssl_baseline > ema_200 ? 1 : -1
ssl_angle = angle(ssl_baseline,3)


// ===  MACD Crossover ===
// ==================================================
pos = 0
macd_mult = syminfo.currency == 'JPY' ? 100 : syminfo.currency == 'NZD' ? 10000 : 10000
fastLength = input(8, minval=1) // 8
slowLength = input(25,minval=1) // 16
signalLength=input(9,minval=1) // 11
fastMA = ema(close, fastLength)
slowMA = ema(close, slowLength)
macd = (fastMA - slowMA) * macd_mult
signal = (sma(macd, signalLength) )
pos := iff(signal < macd , 1,iff(signal > macd, -1, nz(pos[1], 0))) 
barcolor(pos == -1 ? red: pos == 1 ? green : na)


plot(ssl_angle,title='SSL Angle', color=ssl_color, linewidth=4, style=plot.style_circles)
plot(ssl_l_angle,title='Long Angle', color=ssl_color, linewidth=4, style=plot.style_circles)
plot(ema_angle,title="EMA Angle",color=color.yellow,linewidth=2)
plot(ssl_baseline,title='SSL Baseline', color=ssl_color, linewidth=4)
plot(ssl_long, color= orange , title="SSL Long",linewidth=2)
plot(show_atr ? atr_upper + atr_stop : na, "+ATR stop", color=color.new(#ffff00,75))
plot(show_atr ? atr_lower - atr_stop : na, "-ATR stop", color=color.new(#ffff00,75))
plot(ema_200,title="EMA",color=color.yellow,linewidth=2)
atr_color = color.new(#55d51a,70)
atr_s_upper = plot(atr_s,"ATR Sell",color=atr_color)
atr_b_lower = plot(atr_b,"ATR Buy",color=atr_color)
fill(atr_s_upper,atr_b_lower,title='ATR Fill',color=color.new(atr_color,93))

b_cnt() =>
	mc_color = pos == -1 ? red: pos == 1 ? green : na

	b_cond1 = candle==2 and open<ema_200 and ssl_angle>0 and atr_lower<ssl_baseline
	b_cond2 = candle==2 and ssl_angle<0 and ema_angle>0 and atr_lower<ema_200 and  low<ema_200 and close>ema_200

	b_cond3 = candle==2 and ssl_angle>0 and (high<atr_b or (high>atr_b and open<atr_b))
	// and res==dark blue and only when rcis low is above bottom line and wae_line>wae_dz and
	// ema_200<ssl_baseline and di_minus>di_minus[1] 
	b_cond4 = candle==2 and close>ema_200  and ssl_angle<0 and high<atr_b and atr_lower<ema_200
	// and di_minus>di_minus[1]
	b_cond5 = candle==2 and ssl_angle>0 and atr_lower<atr_b and low<atr_b and
	 close>atr_b // wae_line>wae_dz or di_mins>di_minus[1] and (adx<di_minus unless adx_sma>adx)
	b_cond6 = candle==2 and ssl_angle>0 and atr_lower<ema_200 and low>ema_200 and mc_color==red
	// di_minus>adx

	b_cond7 = candle==2 and ssl_angle>0 and ssl_l_angle>12 and mc_color==red and
	 ssl_long>ssl_baseline and atr_lower>ssl_long and ssl_angle>8
	// close<SSL3 

	b_cond8 = candle==0 and close>ema_200 and ssl_baseline>ema_200 and 
	 mc_color==red and close<atr_b and
	 (
	 (atr_lower<ssl_long and ssl_l_angle>0 and close>ssl_long and close<ssl_baseline) or
	 (atr_lower<ema_200 and ssl_l_angle<0)
	 )
	 // bbr<bbr_center

	// remember this is for counter trades. Overall always be checking the atr_bottom is lower 
	// than the next line down whether ssl_baseline, ssl_long or ema_200

	[b_cond1,b_cond2,b_cond3,b_cond4,b_cond5,b_cond6,b_cond7,b_cond8]

[b_cond1,b_cond2,b_cond3,b_cond4,b_cond5,b_cond6,b_cond7,b_cond8] = b_cnt() 

plotshape(b_cond1, style=shape.circle,location=location.bottom,color=green,text="1",textcolor=color.white)
plotshape(b_cond2, style=shape.circle,location=location.bottom,color=green,text="2",textcolor=color.white)
plotshape(b_cond3, style=shape.circle,location=location.belowbar,color=lime,text="3",textcolor=color.white)
plotshape(b_cond4, style=shape.circle,location=location.bottom,color=lime,text="4",textcolor=color.gray)
plotshape(b_cond5, style=shape.circle,location=location.bottom,color=lime,text="5",textcolor=color.gray)
plotshape(b_cond6, style=shape.circle,location=location.bottom,color=lime,text="6",textcolor=color.gray)
plotshape(b_cond7, style=shape.circle,location=location.bottom,color=lime,text="7",textcolor=color.gray)
plotshape(b_cond8, style=shape.circle,location=location.bottom,color=lime,text="8",textcolor=color.gray)

s_cnt() =>
	atr_up = ssl_angle<0 and ema_angle<0 and candle==1 and ssl_color==red_light
	mc_color = pos == -1 ? red: pos == 1 ? green : na
	s_cond1 = atr_up and atr_upper>ssl_baseline and 
	 (ssl_baseline>ema_200 or (ssl_long>ssl_baseline and atr_upper>ssl_long) ) 

	s_cond2 = candle==1 and ssl_baseline>ema_200 and
	 ssl_angle<0 and mc_color==green and
	 ssl_long<ema_200 and ssl_angle<-9
	 and not(atr_upper>ssl_long and close>ssl_long)
	 // and wae_color == green and di_plus>di_minus

	// s_cond2 = atr_up and high<ssl_baseline and open>ssl_long and ssl_baseline<ema_200 and ssl_baseline>atr_s and
	//  (atr_upper>ssl_baseline and atr_upper>atr_s )

	s_cond3 = candle==1 and close<ema_200 and ssl_color==aqua and 
	 atr_upper>ema_200 and mc_color==green
	s_cond4 = candle==1 and close>ssl_long and atr_lower>atr_s and
	 atr_s>ema_200 
	 // and only if high is lower than bb_upper and rsic==red and low>ssl_baseline
	 // and di_plus>di_plus[1]

	[s_cond1,s_cond2,s_cond3,s_cond4]

[s_cond1,s_cond2,s_cond3,s_cond4] = s_cnt() 

// b_cond3 = candle==1 and ssl_angle<0 and ema_angle<0 and open>ssl_long and
//  atr_upper<ssl_baseline and ssl_long<close and ssl_color==red_light and ssl_baseline < ema_200

plotshape(s_cond1, style=shape.circle,location=location.top,color=#ff0000,text="1",textcolor=color.white)
plotshape(s_cond2, style=shape.circle,location=location.top,color=#ff0000,text="2",textcolor=color.gray)
plotshape(s_cond3, style=shape.circle,location=location.top,color=#ff0000,text="3",textcolor=color.gray)
plotshape(s_cond4, style=shape.circle,location=location.top,color=orange,text="4",textcolor=color.gray)
