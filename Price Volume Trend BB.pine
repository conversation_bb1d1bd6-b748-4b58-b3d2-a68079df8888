//@version=4
study(title="Price Volume Trend BB", shorttitle="PV BB", format=format.volume)
srcc = close
vt = cum(change(srcc)/srcc[1]*volume)
plot(vt, color=color.white, linewidth=2, transp=0, title="PVT")
//////////
price = close
///////////
length = input(30, minval=1)
mult = input(0.5, minval=0.001, maxval=5, step=0.1)
basis = sma(vt, length)
dev = mult * stdev(vt, length)
upper = basis + dev
lower = basis - dev
color_bar = vt > upper ? #00c3ff : vt < lower ? #ff0062 : color.yellow
color_bar1 = upper > vt ? #ff0062 : #00c3ff
color_bar2 = lower > vt ? #ff0062 : #00c3ff

p1 = plot(upper, color=color_bar1, transp=0, title="BB Upper")
p2 = plot(lower, color=color_bar2, transp=0, title="BB Lower")
fill(p1, p2, color=color_bar, transp=70)

