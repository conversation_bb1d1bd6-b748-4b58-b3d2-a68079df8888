//@version=5
//Stochastic Momentum Index
// Author: Surjith <PERSON> (India)
// Copyright: CC 3.0 
//Thanks UCSgears, lonestar108
indicator('Stoch - Multi', shorttitle='Stoch - Multi')

// Colors
red = #ff0000
aqua = #00c3ff
yellow = #FFFF00
orange = #ff9800
green = #4caf50
lime = #50ff00
white = #ffffff
blue = #42a5f5
d_blue = #0053ff
violet = #814dff
magenta = #ff0062
purple = #0045b3
gray = #707070
black = #000000


a = input(10, 'Percent K Length')
b = input(3, 'Percent D Length')
ob = input(40, 'Overbought')
os = input(-40, 'Oversold')
smooth = input(1, 'Smoothing')
stoch_time = input.timeframe(title='Timeframe', defval='240')
show_bars = input(title='Show Bars', defval=true)

angle(_src, len) =>
    rad2degree = 180 / 3.14159265359  //pi 
    ang = rad2degree * math.atan((_src[0] - _src[1]) / ta.atr(len))
    ang

ma(source, length, type) =>
    switch type
        "SMA" => ta.sma(source, length)
        "Bollinger Bands" => ta.sma(source, length)
        "EMA" => ta.ema(source, length)
        "SMMA (RMA)" => ta.rma(source, length)
        "WMA" => ta.wma(source, length)
        "VWMA" => ta.vwma(source, length)

f_stoch() =>
    // Range Calculation
    ll = ta.lowest(low, a)
    hh = ta.highest(high, a)
    diff = hh - ll
    rdiff = close - (hh + ll) / 2

    avgrel = ta.ema(ta.ema(rdiff, b), b)
    avgdiff = ta.ema(ta.ema(diff, b), b)
    // SMI calculations
    SMI = avgdiff != 0 ? avgrel / (avgdiff / 2) * 100 : 0
    SMI



stoch_m = request.security(syminfo.tickerid, stoch_time, f_stoch())
SMI = request.security(syminfo.tickerid, stoch_time, ta.ema(stoch_m, b))
EMA = request.security(syminfo.tickerid, stoch_time, ta.ema(stoch_m, 10))

h0 = hline(0)
h1 = hline(40)
h2 = hline(-40)
h3 = hline(60)
h4 = hline(-60)

//Color Definition for Stochastic Line
//col = SMI >= 40 ? green : SMI <= -40 ? red : black

plot(SMI, title='Stochastic', color=color.new(gray, 0))
plot(EMA, title='EMA', color=color.new(yellow, 0))

level_40 = ob
level_40smi = SMI > ob ? SMI : level_40
level_m40 = os
level_m40smi = SMI < os ? SMI : level_m40

p1 = plot(level_40)
p2 = plot(level_40smi)
p3 = plot(level_m40)
p4 = plot(level_m40smi)

color_cond = SMI < EMA and EMA > ob ? yellow : EMA > ob ? red : SMI > ob ? orange : SMI > EMA and EMA < os ? aqua : EMA < os ? green : SMI < os ? lime : na

fill(p1, p2, color=EMA > level_40 ? color.new(color_cond, 80) : color.new(color_cond, 80), title='OverSold', transp=90)
fill(p3, p4, color=EMA < level_m40 ? color.new(color_cond, 80) : color.new(color_cond, 80), title='OverBought', transp=90)
barcolor(show_bars ? color_cond : na)



// RSI + Bollinger
g_time = "TimeFrame"
inl_adx = "1"
rsi_time = input.timeframe("",title="Timeframe", group=g_time)
isBB = true
i_rsi_angle = input.int(3, title='Angle Amount')
i_smooth = input.int(1, title='Smooth Amount')
rsiLengthInput = input.int(14, minval=1, title="RSI Length", group="RSI Settings")
rsiSourceInput = input.source(close, "Source", group="RSI Settings")
maTypeInput = input.string("Bollinger Bands", title="MA Type", options=["SMA", "Bollinger Bands", "EMA", "SMMA (RMA)", "WMA", "VWMA"], group="MA Settings")
maLengthInput = input.int(40, title="MA Length", group="MA Settings") // 14
bbMultInput = input.float(1.0, minval=0.001, maxval=50, title="BB StdDev", group="MA Settings") // 2.0


f_rsi_bb()=>
    up = ta.rma(math.max(ta.change(rsiSourceInput), 0), rsiLengthInput)
    down = ta.rma(-math.min(ta.change(rsiSourceInput), 0), rsiLengthInput)
    rsi = down == 0 ? 100 : up == 0 ? 0 : 100 - (100 / (1 + up / down))
    rsiMA = ma(rsi, maLengthInput, maTypeInput)
    isBB = maTypeInput == "Bollinger Bands"
    bbUpperBand = rsiMA + ta.stdev(rsi, maLengthInput) * bbMultInput
    bbLowerBand = rsiMA - ta.stdev(rsi, maLengthInput) * bbMultInput

    [rsi,rsiMA,bbUpperBand,bbLowerBand]


[rsi,rsiMA,bbUpperBand,bbLowerBand] = f_rsi_bb()

rsi_m = request.security(syminfo.tickerid, rsi_time, rsi )
rsiMA_m = request.security(syminfo.tickerid, rsi_time, ta.sma(rsiMA,i_smooth) )
rsiMA_a_m = request.security(syminfo.tickerid, rsi_time, angle(rsiMA,i_rsi_angle) )
bbUpperBand_m = request.security(syminfo.tickerid, rsi_time, bbUpperBand )
bbLowerBand_m = request.security(syminfo.tickerid, rsi_time, bbLowerBand )
// Plots
// change_m = ta.change(rsi_m)
// candle_c = change_m and rsi_m>rsi_m[1] ? color.green : change_m and rsi_m<rsi_m[1] ? color.red : color.gray
// plot(rsi_m, "RSI", color=candle_c)
// plot(rsiMA_m, "RSI-based MA", color=rsiMA_m>50 and rsiMA_a_m<0 ? color.red : rsiMA_m>50 ? color.yellow : color.green)
// rsiUpperBand = hline(70, "RSI Upper Band", color=#787B86)
// hline(50, "RSI Middle Band", color=color.new(#787B86, 50))
// rsiLowerBand = hline(30, "RSI Lower Band", color=#787B86)
// fill(rsiUpperBand, rsiLowerBand, color=color.rgb(126, 87, 194, 90), title="RSI Background Fill")
// fill_bbup = plot(isBB ? bbUpperBand_m : na, title = "Upper Bollinger Band", color=color.green)
// fill_bbdown = plot(isBB ? bbLowerBand_m : na, title = "Lower Bollinger Band", color=color.green)
// fill(fill_bbup, fill_bbdown, color= isBB ? color.new(color.green, 90) : na, title="Bollinger Bands Background Fill")


// Stoch Conditions
stoch_sell = SMI < EMA and EMA > ob ? 1 : 0
stoch_buy  = SMI > EMA and EMA < os ? 1 : 0
// RSI + BB conditions
rsi_sell    = rsi_m>bbUpperBand_m and rsiMA_m>50 ? 90 : na
rsi_buy     = rsi_m<bbLowerBand_m and  rsiMA_m<50 ? 10 : na
sell_colors = stoch_sell and rsi_sell ? red : stoch_sell and not rsi_sell ? orange : rsi_sell and not stoch_sell ? yellow : na
buy_colors  = stoch_buy and rsi_buy ? lime : stoch_buy and not rsi_buy ? blue : rsi_buy and not stoch_buy ? aqua : na
plotshape(stoch_sell or rsi_sell ? 1 : 0, title="Sell Condition", color=sell_colors ,style=shape.circle,location=location.top)
plotshape(stoch_buy or rsi_buy ? 1 : 0, title="Buy Condition", color=buy_colors ,style=shape.circle,location=location.bottom)
// plotshape(cond2, title="Sell Condition", color=color.new(red,50) ,style=shape.circle,location=location.top)
// plotshape(cond, title="Buy Condition", color=color.new(green,50) ,style=shape.circle,location=location.bottom)
