//@version=4
study(title = "Bollinger Bands %B - Feb 12", shorttitle = "BB %B - Feb 12", format=format.price, precision=3, resolution="")
red = color.new(#ff0062,50)
aqua = #00bcd4
yellow = color.new(#FFFF00,30) 
orange = #ff9800
green = color.new(#00ff64,50) 
violet = #814dff
lime = #00E676
white = color.new(#ffffff,50)  
blue = #42a5f5
gray = #707070
black = #000000

src = input(close, title="Source")
mult = 2 //input(2.0, minval=0.001, maxval=50, title="StdDev")

l1 = input(14)
basis = sma(src, l1)
dev= mult * stdev(src, l1)
upper = basis + dev
lower = basis - dev
bbr = (src - lower)/(upper - lower)
bbr_color = bbr>1 ? red : bbr<0 ? green : blue
plot(bbr, "BBR 1", color=bbr_color)

l2 = input(40) // 50
basis2 = sma(src, l2)
dev2 = mult * stdev(src, l2)
upper2 = basis2 + dev2
lower2 = basis2 - dev2
bbr2 = (src - lower2)/(upper2 - lower2)
bbr_color2 = bbr2>1 ? orange : bbr2<0 ? green : yellow
plot(bbr2, "BBR 2", color=bbr_color2)

l3 = input(100)
basis3 = sma(src, l3)
dev3 = mult * stdev(src, l3)
upper3 = basis3 + dev3
lower3 = basis3 - dev3
bbr3 = (src - lower3)/(upper3 - lower3)
bbr_color3 = bbr3>1 ? red : bbr3<0 ? green : white
plot(bbr3, "BBR 3", color=gray)
//length = input(100)

// b2 > b1
//plotshape(bbr2>bbr and bbr2>1?1:na,title="B2 above",color=bbr3>0.5?#ff0000:orange,style=shape.circle,location=location.top)
bbr_u = bbr>1 and bbr2<1?yellow : bbr2>1 and bbr3<1?orange : bbr3>1?red:na
plotshape(bbr>1?1 : bbr2>1?1 : bbr3>1?1:na,title="Tops",color=bbr_u,style=shape.circle,location=location.top)

bbr_d = bbr<0 and bbr2>0?green : bbr2<0 and bbr3>0?violet : bbr3<0?aqua:na
plotshape(bbr<0 and bbr2>0?1 : bbr2<0 and bbr3>0?1 : bbr3<0?1:na,title="Bottoms",color=bbr_d,style=shape.circle,location=location.bottom)

//plotshape(bbr3>0.5 and bbr>1, style=shape.circle,location=location.top,color=red)
//plotshape(bbr3<0.5 and bbr<0, style=shape.circle,location=location.bottom,color=green)
band1 = hline(1, "Overbought", color=#787B86, linestyle=hline.style_dashed)
band0 = hline(0, "Oversold", color=#787B86, linestyle=hline.style_dashed) // -0.080
fill(band1, band0, color=#3179f5, title="Background")
hline(0.5,linestyle=hline.style_dashed,color=color.new(color.white,50))