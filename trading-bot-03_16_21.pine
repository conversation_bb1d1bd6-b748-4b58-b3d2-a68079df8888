//@version=4
study(title = "Trading Bot", shorttitle = "Trading Bot", overlay=true, format = format.price, precision = 2)


// Custom inputs
account_size = input(10000, minval=1000)
trending = input(title="Show Trending/Ranging", type=input.bool, defval=true)
show_entry = input(title="Show Entry", type=input.bool, defval=true)
show_label = input(title="Show Labels", type=input.bool, defval=false)
show_cross = input(title="Show Crossover", type=input.bool, defval=false)
show_BB = input(title="Show BB", type=input.bool, defval=true)
use_res = input(title="RES Filter", type=input.bool, defval=true)



// === Bollinger Bands %B ===
// ==================================================
length = input(20, minval=1)
src = input(close, title="Source")
multi = input(2.0, minval=0.001, maxval=50, title="StdDev")
basis = sma(src, length)
deviation = multi * stdev(src, length)
upper = basis + deviation
lower = basis - deviation
bbr = (src - lower)/(upper - lower)
//band1 = hline(1, "Overbought", color=#606060, linestyle=hline.style_dashed)
//band0 = hline(0, "Oversold", color=#606060, linestyle=hline.style_dashed)
//fill(band1, band0, color=color.teal, title="Background")
plot(bbr, "Bollinger Bands %B", color=color.teal,linewidth=0)



// === BBand width ratio - kapipara180@ === 
// ==================================================
BB_Period = input(20, title="BBand period")
Deviation = input(2.0, "Deviation")
sko = stdev(close,BB_Period)
BBandwr = 2*(Deviation*sko)/sma(close,BB_Period)*1000 //length from UPband to Downband / SMA
BBandwr2 = 2*(Deviation*sko)/sma(close,BB_Period)*100
plot(BBandwr,"BBand width ratio", linewidth=0, color= #ff0000,transp=100)



// === Bollinger Awesome Alert R1.1 by JustUncleL ===
// ==================================================
bb_use_ema = input(false, title="Use EMA for Bollinger Band")
bb_source = input(close, title="Bollinger Source")
bb_length = input(20, minval=1, title="Bollinger Length")
bb_mult = input(2.0, title="Base Multiplier", minval=0.5, maxval=10)
sqz_length = 100
sqz_threshold = input(75, minval=60, title="Squeeze Threshold")

// EMA inputs
fast_ma_len = input(3, title="Fast EMA length", minval=2)

// Breakout Indicator Inputs
ema_1 = ema(bb_source, bb_length)
sma_1 = sma(bb_source, bb_length)
bb_basis = bb_use_ema ? ema_1 : sma_1
fast_ma = ema(bb_source, fast_ma_len)

// Deviation
// * I'm sure there's a way I could write some of this cleaner, but meh.
dev = stdev(bb_source, bb_length)
bb_dev = bb_mult * dev
// Bands
bb_upper = bb_basis + bb_dev
bb_lower = bb_basis - bb_dev
// Calculate BB spread and average spread
bb_spread = bb_upper - bb_lower
avgspread = sma(bb_spread, sqz_length)
// Calculate BB relative %width for Squeeze indication
bb_squeeze = 0.00
bb_squeeze := bb_spread / avgspread * 100
// Calculate Upper and Lower band painting offsets based on 50% of atr.
bb_offset = atr(14) * 0.5
bb_sqz_upper = bb_upper + bb_offset
bb_sqz_lower = bb_lower - bb_offset

// plot 
plot(fast_ma, title="Fast EMA", color=color.yellow, transp=10, linewidth=2)
plot(bb_basis, title="Basis Line", color=color.red, transp=10, linewidth=2)
plot(bb_squeeze[0], title="BB Squeeze", color=bb_squeeze < sqz_threshold ? color.blue : color.gray, transp=10, linewidth=0)
//bands squeeze
// plot BB upper and lower bands
ubi = plot(show_BB ? bb_upper: na, title="Upper Band Inner", color=color.blue, transp=10, linewidth=1)
lbi = plot(show_BB ? bb_lower:na, title="Lower Band Inner", color=color.blue, transp=10, linewidth=1)
// center BB channel fill
fill(ubi, lbi, title="Center Channel Fill", color=color.silver, transp=90)

//Indicate BB squeeze based on threshold.
usqzi = plot(show_BB ? bb_sqz_upper: na, "Hide Sqz Upper", transp=100)
lsqzi = plot(show_BB ? bb_sqz_lower: na, "Hide Sqz Lower", transp=100)
fill(ubi, usqzi, color=bb_squeeze > sqz_threshold ? color.white : color.blue, transp=50)
fill(lbi, lsqzi, color=bb_squeeze > sqz_threshold ? color.white : color.blue, transp=50)


// === SSL Hybrid - Mihkel00 ===
// ==================================================
len = input(title = "SSL1 / Baseline Length", defval = 60)
show_Baseline = input(title="Show Baseline", type=input.bool, defval=true)
show_SSL1 = input(title = "Show SSL1", type = input.bool, defval = false)
show_atr = input(title = "Show ATR bands", type = input.bool, defval = true)
//ATR
atrlen = input(14, "ATR Period")
mult = input(1, "ATR Multi", step = 0.1)
smoothing = input(title = "ATR Smoothing", defval = "WMA", options = ["RMA", "SMA", "EMA", "WMA"])

ma_function(source, atrlen) =>
	if smoothing == "RMA"
		rma(source, atrlen)
	else
		if smoothing == "SMA"
			sma(source, atrlen)
		else
			if smoothing == "EMA"
				ema(source, atrlen)
			else
				wma(source, atrlen)

atr_slen = ma_function(tr(true), atrlen)

////ATR Up/Low Bands
upper_band = atr_slen * mult + close
lower_band = close - atr_slen * mult

//SSL 1 and SSL2
emaHigh = wma(2 * wma(high, len / 2) - wma(high, len), round(sqrt(len)))
emaLow = wma(2 * wma(low, len / 2) - wma(low, len), round(sqrt(len)))

// BASELINE VALUES
BBMC = wma(2 * wma(close, len / 2) - wma(close, len), round(sqrt(len)))
multy = 0.2
range = tr
rangema = ema(range, len)
upperk =BBMC + rangema * multy
lowerk = BBMC - rangema * multy

//SSL1 VALUES
Hlv = int(na)
Hlv := close > emaHigh ? 1 : close < emaLow ? -1 : Hlv[1]
sslDown = Hlv < 0 ? emaHigh : emaLow

//COLORS
color_ssl1 = close > sslDown ? #00c3ff : close < sslDown ? #ff0062 : na
color_bar = close > upperk ? #00c3ff : close < lowerk ? #ff0062 : color.gray

//Plot Baseline & SSL1
p1 = plot(show_Baseline ? BBMC : na, color=color_bar, linewidth=4,transp=0, title='MA Baseline')
DownPlot = plot( show_SSL1 ? sslDown : na, title="SSL1", linewidth=3, color=color_ssl1, transp=10)

// ATR plot
atr_upper = plot(show_atr ? upper_band : na, "+ATR", color=color.white, transp=80)
atr_lower = plot(show_atr ? lower_band : na, "-ATR", color=color.white, transp=80)



// === RES - Ranging EMA Spread  ===
// ==================================================
ema1length = 12
ema2length = 50
ranginglength = 3
rangingmaxvalue = 0.1
rangingminvalue = -0.1
enablebarcolors = false

// EMA spread
ema1 = ema(close, ema1length)
ema2 = ema(close, ema2length)
spread = ((ema2 / ema1) -1) * 100
r1 = (spread >= rangingminvalue and spread <= rangingmaxvalue) 
r2 = (spread[1] >= rangingminvalue and spread[1] <= rangingmaxvalue)
r3 = (spread[2] >= rangingminvalue and spread[2] <= rangingmaxvalue)
r4 = (spread[3] >= rangingminvalue and spread[3] <= rangingmaxvalue)
r5 = (spread[4] >= rangingminvalue and spread[4] <= rangingmaxvalue)

ranging = false

if (ranginglength == 1) 
    ranging := r1
if (ranginglength == 2) 
    ranging := r1 and r2
if (ranginglength == 3) 
    ranging := r1 and r2 and r3
if (ranginglength == 4) 
    ranging := r1 and r2 and r3 and r4
if (ranginglength == 5) 
    ranging := r1 and r2 and r3 and r4 and r5

color_res = ranging ? #FFFF00 : spread > spread[1] ? #007600 : #18f918
res = spread > 0 ? 0-spread : abs(spread)
res := res * 100
plot(res,"RES", style=plot.style_circles, linewidth=0, color=color_res, transp=0)



// === EMA 200 ===
// ==================================================
//@version=4
// len_200 = 200
// offset = input(title="Offset", type=input.integer, defval=0, minval=-500, maxval=500)
// ema_200 = ema(close, len_200)
// plot(ema_200, title="EMA 200", color=#fff176, offset=offset, linewidth=3)


 
// === Angle of Moving Average  ===
// ==================================================
i_lookback   = input(2,     "Angle Period", input.integer, minval = 1)
i_atrPeriod  = input(14,    "ATR Period",   input.integer, minval = 1)
i_angleLevel = input(6,     "Angle Level",  input.integer, minval = 1)

i_maLength   = input(20,    "MA Length",    input.integer, minval = 1)
i_maType     = input("WMA", "MA Type",      input.string, options=["ALMA", "EMA", "DEMA", "TEMA", "WMA", "VWMA", "SMA", "SMMA", "HMA", "LSMA", "Kijun", "McGinley"])
i_maSource   = input(close, "MA Source",    input.source)

i_lsmaOffset = input(0,   "* Least Squares (LSMA) Only - Offset Value", minval=0)
i_almaOffset = input(0.85,"* Arnaud Legoux (ALMA) Only - Offset Value", minval=0, step=0.01)
i_almaSigma  = input(6,   "* Arnaud Legoux (ALMA) Only - Sigma Value",  minval=0)

i_barColor   = input(true, "Bar Color ?")
i_noTZone    = input(true,  "No Trade Zone")

// ————— Determine Angle by KyJ
f_angle(_src, _lookback, _atrPeriod) =>
    rad2degree = 180 / 3.141592653589793238462643  //pi 
    ang = rad2degree * atan((_src[0] - _src[_lookback]) / atr(_atrPeriod)/_lookback)
    ang

// ————— Moving Averages
f_ma(type, _src, _len) =>
    float result = 0
    if type=="SMA" // Simple
        result := sma(_src, _len)
    if type=="EMA" // Exponential
        result := ema(_src, _len)
    if type=="DEMA" // Double Exponential
        e = ema(_src, _len)
        result := 2 * e - ema(e, _len)
    if type=="TEMA" // Triple Exponential
        e = ema(_src, _len)
        result := 3 * (e - ema(e, _len)) + ema(ema(e, _len), _len)
    if type=="WMA" // Weighted
        result := wma(_src, _len)
    if type=="VWMA" // Volume Weighted
        result := vwma(_src, _len) 
   //  if type=="SMMA" // Smoothed
   //      w = wma(_src, _len)
   //      result := na(w[1]) ? sma(_src, _len) : (w[1] * (_len - 1) + _src) / _len
    if type=="HMA" // Hull
        result := wma(2 * wma(_src, _len / 2) - wma(_src, _len), round(sqrt(_len)))
    if type=="LSMA" // Least Squares
        result := linreg(_src, _len, i_lsmaOffset)
    if type=="ALMA" // Arnaud Legoux
        result := alma(_src, _len, i_almaOffset, i_almaSigma)
    if type=="Kijun" //Kijun-sen
        kijun = avg(lowest(_len), highest(_len))
        result :=kijun
   //  if type=="McGinley"
   //      mg = 0.0
   //      mg := na(mg[1]) ? ema(_src, _len) : mg[1] + (_src - mg[1]) / (_len * pow(_src/mg[1], 4))
   //      result :=mg
    result
    
_ma    = f_ma(i_maType, i_maSource, i_maLength)
_angle = f_angle(_ma, i_lookback, i_atrPeriod)
color_H = _angle > 0 ? color.lime : _angle < 0 ? color.red : color.gray
color_L = 
   _angle > _angle[1] and (_angle > i_angleLevel or _angle < -i_angleLevel) ? color.lime :
   _angle < _angle[1] and (_angle > i_angleLevel or _angle < -i_angleLevel) ? color.red : color.gray

c_ntz = i_noTZone ? color_L : color_H
plot(_angle,"Angle MA Line", c_ntz, 3, plot.style_line,transp=100)
barcolor(i_barColor ? c_ntz : na)



// === WaveTrend with Crosses [LazyBear]  ===
// ==================================================
n1 = input(10, "Channel Length")
n2 = input(21, "Average Length")
obLevel1 = input(60, "Over Bought Level 1")
obLevel2 = input(53, "Over Bought Level 2")
osLevel1 = input(-60, "Over Sold Level 1")
osLevel2 = input(-53, "Over Sold Level 2")
 
ap = hlc3 
esa = ema(ap, n1)
d = ema(abs(ap - esa), n1)
ci = (ap - esa) / (0.015 * d)
tci = wma(ci, n2)
 
wt1 = tci
wt2 = sma(wt1,4)



// === Trading Properties ===
// ==================================================
var bb_count = 0
allow_trade = 0
entry_price = 0.00
exit_price = 0.00
// Initialize an empty array to store variables
props = array.new_float(4)


// === Trading Methods ===
// ==================================================
entry_signal() =>
	dir = 0
	trading = 0
	// Type of candle - Long / Short
	candle = close > open ? 1 : 0

	// === Ranging ===
	if bb_squeeze < sqz_threshold == 1
		// Sell signal
		if (bbr > 0.97 and candle == 1) or (fast_ma < open and high > bb_upper and candle == 1)
			dir := -1
			trading := 1
			
			//if bb_squeeze > sqz_threshold
				// Angle of Moving Average
				// if c_ntz == color.gray
				// 	dir := na
				// 	trading := 0

				// Wave Trend - Filter
				// if wt2 < 60 
				// 	dir := na
				// 	trading := 0

			//Above Fast MA line
			// if fast_ma < open
			// 	dir := -1
			// 	trading := 1
			// else 
			// 	dir := na
			// 	trading := 0

			// Crossdown - SSL and Bollinger Band basis line
			// if sslDown > bb_basis
			// 	dir := -1
			// 	trading := 1
			// else 
			// 	dir := na
			// 	trading := 0

			// RES FILTER 1
			// if res > 0
			// 	dir := -1
			// 	trading := 1
			// else 
			// 	dir := na
			// 	trading := 0

		// Buy signal
		else if (bbr < 0.03 and candle == 0) or (fast_ma > open and low < bb_lower and candle == 0)
		// else if (bbr < 0.01 and fast_ma > open) or (fast_ma > open and low < bb_lower and candle == 0)
			dir := 1
			trading := 1

			// Wave Trend - Filter
			// if wt2 > -60 
			// 	dir := na
			// 	trading := 0

			// Angle of Moving Average
			// if c_ntz == color.gray
			// 	dir := na
			// 	trading := 0

			// Below Fast MA line
			// if fast_ma > open
			// 	dir := -1
			// 	trading := 1
			// else 
			// 	dir := na
			// 	trading := 0

			// EMA 200 FILTER 1
			// if  close < ema_200 and open < ema_200 
			// 	dir := 1
			// 	trading := 1
			// else 
			// 	dir := na
			// 	trading := 0

			
			// RES FILTER 1
			// if res < 0
			// 	dir := 1
			// 	trading := 1
			// else 
			// 	dir := na
			// 	trading := 0

			// Crossdown - SSL and Bollinger Band basis line
			// if sslDown < bb_basis
			// 	dir := 1
			// 	trading := 1
			// else 
			// 	dir := na
			// 	trading := 0

		// Don't trade
		else
			dir := na
			trading := 0

	// === Trending ===	
	else if trending == 1 and bb_squeeze < sqz_threshold == 0
		// Sell signal
		if (bbr >= 0.98 and candle == 1)
			dir := -1
			trading := 1

			// RES FILTER 1
			// if res > 0 and use_res == 1
			// 	dir := -1
			// 	trading := 1
			// else 
			// 	dir := na
			// 	trading := 0
			
		// Buy signal
		else if (bbr <= 0.02 and candle == 0)
			dir := 1
			trading := 1

			// RES FILTER 1
			// if res < 0 and use_res == 1
			// 	dir := 1
			// 	trading := 1
			// else 
			// 	dir := na
			// 	trading := 0

		// Don't trade
		else
			dir := na
			trading := 0

	[dir, trading]

[trade_dir, trading] = entry_signal() 

calc_trade_values() =>
	p = 0.00
	rp = 0.00
	sl = 0.00
	pips = 0.00
	p_value = 0.00

	// Sell
	if trade_dir < 0
		sl := upper_band
		rp := (1 - (upper_band / close)) * 100
		p  := (1 / rp) * account_size
	// Buy
	else
		sl := lower_band
		rp := (1 - (lower_band / close)) * 100
		p  := (1 / rp) * account_size
    
    p := round(abs(p))
    rp := abs(rp)
    pips := abs( (close - sl) * 10000 )
    p_value := (account_size * rp) / pips
	 
	[p,rp,sl,pips,p_value]
	
[pos_size,risk_perc, stop_loss,pips, pip_value] = calc_trade_values()

// === Labels ===
//l_offset = 2 * (time - time[1])
labelText = "Lot Size: " + tostring(pos_size) 
 + "\nRisk: 0" + tostring(risk_perc, "#.00") 
 + "% \nStop Loss: " + tostring(stop_loss, "#.0000") 
 + "\nPips: " + tostring(pips, "#.00")
 + "\nPip Value: " + tostring(pip_value, "#.0000")

if show_label and trading
	if trade_dir > 0
        buy = label.new(x=time,y=high,text=labelText,xloc=xloc.bar_time, yloc=yloc.belowbar,color=color.green,textcolor=color.white, size=size.normal)
    else
        sell = label.new(x=time,y=high,text=labelText,xloc=xloc.bar_time, yloc=yloc.abovebar,color=color.red,textcolor=color.white, size=size.normal)



// BB count
if trading > 0
	bb_count := bb_count+1
else 
	bb_count := 0

if bb_count > 2
	allow_trade := trade_dir
if bb_count > 4
	bb_count := 0

// BB count
bb_color = bb_count < 2 ? #555555 : #00ff00
plot(bb_count,title="BB Count", color=bb_color, transp=100)

// Buy / Sell indicators
plotshape(trade_dir > 0 and show_entry ? 1 : na, title="Entry Buy", color=res < 0 and trade_dir > 0 ? color.orange : color.green, location = location.belowbar, style=shape.labelup, text="B", textcolor=color.white, size=size.small)
plotshape(trade_dir < 0 and show_entry ? -1 : na, title="Entry Sell", color= res > 0 and trade_dir < 0 ? color.orange : color.red, location = location.abovebar, style=shape.labeldown, text="S", textcolor=color.white, size=size.small)
plot(trading ,title="Trading",color= trading ? color.green : color.gray)
// Cross: Basis line & SSL baseline
co = crossover(BBMC,bb_basis) 
cu = crossunder(BBMC,bb_basis)
plotshape(co and show_cross ? 1 : na, title="Cross Up", color=color.white, location = location.belowbar, style=shape.labelup, text="x", textcolor=color.black, size=size.small)
plotshape(cu and show_cross ? 1 : na, title="Cross Up", color=color.white, location = location.abovebar, style=shape.labeldown, text="x", textcolor=color.black, size=size.small)




