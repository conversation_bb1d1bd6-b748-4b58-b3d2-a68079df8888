// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © Mango2Juice

//@version=4
study("Angle of Moving Average")

// ————————————————————————————————————————————————————————————
// >>>>>>>>>>>>>>>>>>>>>>>>  Inputs  <<<<<<<<<<<<<<<<<<<<<<<<<<
// ————————————————————————————————————————————————————————————

i_lookback   = input(2,     "Angle Period", input.integer, minval = 1)
i_atrPeriod  = input(10,    "ATR Period",   input.integer, minval = 1)
i_angleLevel = input(6,     "Angle Level",  input.integer, minval = 1)

i_maLength   = input(20,    "MA Length",    input.integer, minval = 1)
i_maType     = input("EMA", "MA Type",      input.string, options=["ALMA", "EMA", "DEMA", "TEMA", "WMA", "VWMA", "SMA", "SMMA", "HMA", "LSMA", "Kijun", "McGinley"])
i_maSource   = input(close, "MA Source",    input.source)

i_lsmaOffset = input(0,   "* Least Squares (LSMA) Only - Offset Value", minval=0)
i_almaOffset = input(0.85,"* Arnaud Legoux (ALMA) Only - Offset Value", minval=0, step=0.01)
i_almaSigma  = input(6,   "* Arnaud Legoux (ALMA) Only - Sigma Value",  minval=0)

i_barColor   = input(false, "Bar Color ?")
i_noTZone    = input(true,  "No Trade Zone")

// ————————————————————————————————————————————————————————————
// >>>>>>>>>>>>>>>>>>>>>>  Functions  <<<<<<<<<<<<<<<<<<<<<<<<<
// ————————————————————————————————————————————————————————————

// ————— Determine Angle by KyJ
f_angle(_src, _lookback, _atrPeriod) =>
    rad2degree = 180 / 3.141592653589793238462643  //pi 
    ang = rad2degree * atan((_src[0] - _src[_lookback]) / atr(_atrPeriod)/_lookback)
    ang

// ————— Moving Averages
f_ma(type, _src, _len) =>
    float result = 0
    if type=="SMA" // Simple
        result := sma(_src, _len)
    if type=="EMA" // Exponential
        result := ema(_src, _len)
    if type=="DEMA" // Double Exponential
        e = ema(_src, _len)
        result := 2 * e - ema(e, _len)
    if type=="TEMA" // Triple Exponential
        e = ema(_src, _len)
        result := 3 * (e - ema(e, _len)) + ema(ema(e, _len), _len)
    if type=="WMA" // Weighted
        result := wma(_src, _len)
    if type=="VWMA" // Volume Weighted
        result := vwma(_src, _len) 
    if type=="SMMA" // Smoothed
        w = wma(_src, _len)
        result := na(w[1]) ? sma(_src, _len) : (w[1] * (_len - 1) + _src) / _len
    if type=="HMA" // Hull
        result := wma(2 * wma(_src, _len / 2) - wma(_src, _len), round(sqrt(_len)))
    if type=="LSMA" // Least Squares
        result := linreg(_src, _len, i_lsmaOffset)
    if type=="ALMA" // Arnaud Legoux
        result := alma(_src, _len, i_almaOffset, i_almaSigma)
    if type=="Kijun" //Kijun-sen
        kijun = avg(lowest(_len), highest(_len))
        result :=kijun
    if type=="McGinley"
        mg = 0.0
        mg := na(mg[1]) ? ema(_src, _len) : mg[1] + (_src - mg[1]) / (_len * pow(_src/mg[1], 4))
        result :=mg
    result
    
_ma    = f_ma(i_maType, i_maSource, i_maLength)
_angle = f_angle(_ma, i_lookback, i_atrPeriod)

// ————————————————————————————————————————————————————————————
// >>>>>>>>>>>>>>>>>>>>>>    Plots    <<<<<<<<<<<<<<<<<<<<<<<<<
// ————————————————————————————————————————————————————————————
color_H = _angle > 0 ? color.lime : _angle < 0 ? color.red : color.gray
color_L = 
   _angle > _angle[1] and (_angle > i_angleLevel or _angle < -i_angleLevel) ? color.lime :
   _angle < _angle[1] and (_angle > i_angleLevel or _angle < -i_angleLevel) ? color.red : color.gray

c_ntz    = i_noTZone ? color_L : color_H
plot(_angle,"Angle MA Line", c_ntz, 3, plot.style_line)
plot(_angle,"Angle MA Histogram", c_ntz, 4, plot.style_histogram)
hline(0)
barcolor(i_barColor ? c_ntz : na)

// >>>>>>>>>>>>>>>>>>>>>>    End of Script    <<<<<<<<<<<<<<<<<<<<<<<<<