//@version=4
study(title="Trading Bot NZD-15M-Mar 15", shorttitle="TB NZD-15M-03_15",overlay=true,max_labels_count=500)

show_ema = true // input(title="Show EMA 200", type=input.bool, defval=true)
show_atr = input(title="Show ATR", type=input.bool, defval=false)
//show_cond = input(title="Show Conditions", type=input.bool, defval=true)
use_logic = input(title="Use Logic", type=input.bool, defval=false)
show_friday= input(title="Show Friday", type=input.bool, defval=true)
rsi_strong= input(title="Show only RSI strongest", type=input.bool, defval=false)
rsi1_show = input(title="Show RSI1", type=input.bool, defval=false)
rsi2_show = input(title="Show RSI2", type=input.bool, defval=true)
rsi3_show = input(title="Show RSI3", type=input.bool, defval=false)
rsi4_show = input(title="Show RSI4", type=input.bool, defval=false)
rsi5_show = input(title="Show RSI5", type=input.bool, defval=false)
show_bbr = input(title="Show BBR candles",type=input.bool,defval=false)
show_bb = input(title="Show BB Bands",type=input.bool,defval=false)
show_channels = input(title="SSL Channels",type=input.bool,defval=false)
show_channel_fill = input(title="Show Fill",type=input.bool,defval=false)
channel_line_width = input(title="Show Fill",type=input.integer, defval=2)
show_ich = input(title="Show Ichimoku",type=input.bool,defval=false)
use_ny_filter = input(title="Filter NY close",type=input.bool,defval=true)
//show_r3 = input(title="Show R3 down",type=input.bool,defval=false)


red = #ff0062
aqua = #00bcd4
yellow = #FFFF00
orange = #ff9800
green = #4caf50
lime = #00E676
white = #ffffff
c_hide = color.new(#ffffff,100)
blue = #42a5f5
violet = #814dff
gray = #707070
black = #000000
sell_color = color.new(#ff0062,20)
buy_color = color.new(#00c3ff,20)


friday = color.new(sell_color,92)
bgcolor(dayofweek == 1 and show_friday ? friday : na)

// Europe/London - Pacific/Auckland - Asia/Tokyo
timeinrange(res, sess) => not na(time(res, sess, "America/New_York")) ? 1 : 0
ny_close = timeinrange("2", "1630-1900")
bgcolor(ny_close == 1 ? color.new(gray,92) : na)

angle(_src, len) =>
    rad2degree=180/3.14159265359  //pi 
    ang=rad2degree*atan((_src[0] - _src[1])/atr(len))

// Change
perc_change(obj) =>
    perc = abs( (1 - (obj[1] / obj)) * 10000 )


// ===  EMA's ===
// ==================================================
e200_input = input(title="EMA 200",type=input.integer, defval=230) // 200
e200 = ema(close, e200_input)
e200_a = angle(e200,3)
ea_sc = e200_a<5 ? color.new(orange,50): e200_a>5 ? color.new(#ff0000,50) : na
ea_bc = e200_a>-5 ? color.new(#0000ff,50): e200_a<-5 ? color.new(#00ff00,50) : na
e50 = ema(close, 50)
e50_a = angle(e50,3)

e20 = ema(close, 20)
e20_a = angle(e20,3)


// ===  ATR ===
// ==================================================
atrlen = 14 //input(14, "ATR Period")
atr_mult = input(1, "ATR Mult", step = 0.1) // 1.35 1.15
atr_stop = 0.0003 //input(0.0003, "ATR Stop", step = 0.0001) // 0.001
atr_slen = wma(tr(true), atrlen)
atr_upper = atr_slen * atr_mult + close
atr_lower = close - atr_slen * atr_mult
sl_upper = atr_slen * 2 + close
sl_lower = close - atr_slen * 2

// atr_new_len = 14
// atr_smoothing = input(title="Smoothing", defval="RMA", options=["RMA", "SMA", "EMA", "WMA"])
// atr_new = ta.rma(ta.tr(true), atr_new_len)
//plot(atr(14) * 10000, title = "ATR", color=color.new(#B71C1C, 100))

// ===  Kijun ===
// ==================================================
middleDonchian(Length) =>
    lower = lowest(Length)
    upper = highest(Length)
    avg(upper, lower)

basePeriods = input(5, title="kijun Len",minval=1) // 14 26
kijun =  middleDonchian(basePeriods)
k_angle = angle(kijun,3)
xChikou = close
xPrice = close



// ===  SSL ===
// ==================================================
ssl_len1 = 30 // input(30, minval=1,title="SSL 1") // 45
ssl_len2 = 60 // input(60, minval=1,title="SSL 2") //75
ssl_len3 = 8 // input(8, minval=1,title="SSL 3") // 8
ssl_len3_b = 15//input(15, minval=1,title="SSL 3 b") // 8 7
ssl_len4 = 70 // input(70, minval=1,title="SSL 4") // 75 100
ssl_len5 = 125 // input(125, minval=1,title="SSL 5") // 150

s_type = 'SSL' // input(title="S Line Type", type=input.string, defval="SSL", options=["SSL","SMA","EMA","WMA"])
s_lines(type, len) =>
    float result = 0
    if type=="SMA" // Simple
        result := sma(close, len)
    if type=="EMA" // Simple
        result := ema(close, len)
    if type=="WMA" // Simple
        result := wma(close, len)
    if type=="SSL" // Simple
        result := wma(2*wma(close, len/2)-wma(close, len), round(sqrt(len)))
    result

//s1 = wma(2*wma(close, ssl_len1/2)-wma(close, ssl_len1), round(sqrt(ssl_len1)))
s1 = s_lines(s_type, ssl_len1)
s2 = s_lines(s_type, ssl_len2)
s3 = s_lines(s_type, ssl_len3)
s3b = s_lines(s_type, ssl_len3_b)
s4 = s_lines(s_type, ssl_len4)
s5 = s_lines(s_type, ssl_len5)
// angles
s_angle = 3 //input(3)
s1_a  = angle(s1,s_angle)
s2_a = angle(s2,s_angle)
s3_a = angle(s3,s_angle)
s3b_a = angle(s3b,s_angle)
s4_a = angle(s4,s_angle)
s5_a = angle(s5,s_angle)
s3_high = 23
s3_low = -23
ssl_color  = s1_a > 0 ? blue : red
s1_dist = abs(open - s1) * 1000
plot(s1_dist,title="s1 dist", color=s1_a>0?color.new(red,100):color.new(green,100), style=plot.style_circles)



// ===  Bollinger Bands ===
// ==================================================
BB_length = input(10, title="BB Length 1") // 20 45 100 25
BB_length2 = input(40, title="BB Length 2") // 53
BB_stdDev = 2 //input(2, minval=2.0, maxval=3)
sqz_length = 100 //input(100, minval=2, maxval=200) // 100
sqz_threshold = 76 
bb_s = kijun
// === BB 1 ===
basis = sma(close, BB_length)
dev = BB_stdDev * stdev(close, BB_length)
bb_upper = basis + dev
bb_lower = basis - dev
bb_spread = bb_upper - bb_lower
avgspread = sma(bb_spread, sqz_length)
bb_squeeze = 0.00
bb_squeeze := bb_spread / avgspread * 100
bb_zone = bb_squeeze < 53 ? 0 : 
 bb_squeeze < sqz_threshold ? 1 : 
 bb_squeeze < 120 ? 2 :
 bb_squeeze < 160 ? 3 :
 bb_squeeze > 160 ? 4 :
 bb_squeeze > 200 ? 5 : na
sqz_color = bb_zone == 0 ? #0045b3 :
 bb_zone == 1 ? #ff0062 : 
 bb_zone == 2 ?  gray : 
 bb_zone == 3 ?  #00c3ff : 
 bb_zone == 4 ? white:
 bb_zone == 5 ? yellow: na

bb_zones_color =  sqz_color

// === BB 2 ===
basis2 = sma(close, BB_length2)
dev2 = BB_stdDev * stdev(close, BB_length2)
bb_upper2 = basis2 + dev2
bb_lower2 = basis2 - dev2
bb_spread2 = bb_upper2 - bb_lower2
avgspread2 = sma(bb_spread2, sqz_length)
bb_squeeze2 = 0.00
bb_squeeze2 := bb_spread2 / avgspread2 * 100


bb_zone2 = bb_squeeze2 < 53 ? 0 : 
 bb_squeeze2 < sqz_threshold ? 1 : 
 bb_squeeze2 < 120 ? 2 :
 bb_squeeze2 < 160 ? 3 :
 bb_squeeze2 > 160 ? 4 : na
sqz_color2 = bb_zone2 == 0 ? #0045b3 :
 bb_zone2 == 1 ? #ff0062 : 
 bb_zone2 == 2 ?  gray : 
 bb_zone2 == 3 ?  #00c3ff : 
 bb_zone2 == 4 ? white: na

bb_zones_color2 =  sqz_color2


bb_diff = (bb_upper - bb_lower) * 10
basis_angle = angle(basis,3)
basis_angle2 = angle(basis2,3)
bbu_angle = angle(bb_upper,3)
bbl_a = angle(bb_lower,3)


// === WAE ===
// ==================================================
sensitivity = 150
wae_fast= 20 //8
wae_slow= 40 // 50
channelLength= 20 // 45
wae_mult = 2.0 //1.85
wae_dz = nz(rma(tr(true),100)) * 3.7
calc_macd(source, wae_fast, wae_slow) =>
	a = ema(source, wae_fast)
	b = ema(source, wae_slow)
	a - b
calc_BBUpper(source, length, wae_mult) => 
	wae_basis = wma(source, length)
	wae_dev = wae_mult * stdev(source, length)
	t = wae_basis + wae_dev
	[t]
calc_BBLower(source, length, wae_mult) => 
	wae_basis = wma(source, length)
	wae_dev = wae_mult * stdev(source, length)
	t = wae_basis - wae_dev
	[t]

t1 = (calc_macd(close, wae_fast, wae_slow) - calc_macd(close[1], wae_fast, wae_slow))*sensitivity * 100
t2 = (calc_macd(close[2], wae_fast, wae_slow) - calc_macd(close[3], wae_fast, wae_slow))*sensitivity * 100
[e1a] = calc_BBUpper(close, channelLength, wae_mult)
[e1b] = calc_BBLower(close, channelLength, wae_mult)
wae_line = (e1a - e1b)
wae_diff = (wae_line - wae_dz) * 100
//wae_perc = (wae_line / wae_dz) * 100
trendUp = (t1 >= 0) ? t1 : 0
trendDown = (t1 < 0) ? (-1*t1) : 0
wae_color = #000000
if t1 >= 0
	wae_color := trendUp<trendUp[1] ? lime : green
if t1 < 0
	wae_color := trendDown<trendDown[1] ? orange : red
wae_angle = angle(wae_line,3)



// === ADX + DI with SMA ===
// ==================================================
adx_len = 9 // input(9,title="ADX len") // 14
adx_line = 20 // input(title="threshold", defval=20)
adx_avg = 8 // input(8,title="ADX SMA") // 10
var float adx_top = 54 // input(55,title="High")
var float adx_high = 38 // 39.5
var float adx_mid = 33
var float adx_center = 20
var float adx_low = 12
var float smooth_tr = 0
var float smooth_di_plus = 0
var float smooth_di_minus = 0
TrueRange = max(max(high-low, abs(high-nz(close[1]))), abs(low-nz(close[1])))
DI_plus = high-nz(high[1]) > nz(low[1])-low ? max(high-nz(high[1]), 0): 0
DI_minus = nz(low[1])-low > high-nz(high[1]) ? max(nz(low[1])-low, 0): 0
smooth_tr := nz(smooth_tr[1]) - (nz(smooth_tr[1])/adx_len) + TrueRange
smooth_di_plus := nz(smooth_di_plus[1]) - (nz(smooth_di_plus[1])/adx_len) + DI_plus
smooth_di_minus := nz(smooth_di_minus[1]) - (nz(smooth_di_minus[1])/adx_len) + DI_minus

di_plus = smooth_di_plus / smooth_tr * 100
di_minus = smooth_di_minus / smooth_tr * 100
DX = abs(di_plus-di_minus) / (di_plus+di_minus)*100
adx = sma(DX, adx_len)
adx_sma = sma(adx, adx_avg)
adx_angle = (angle(adx,2))
//hline(adx_line, color=black, linestyle=dashed)


// === RSI Wicks ===
// ==================================================
std         = false //input(false, title="Show Standard RSI")
rsi_candles = false //input(true,  title="Show Candles")
wicks       = true  //input(true,  title="Wicks based on stand-alone RSI")
src_close   = close
src_open    = open
src_high    = high
src_low     = low 

rsiw_len1    = 19 // input(19, title="RSI 1") // 20
rsiw_len2    = 14 // input(14, title="RSI 2") // 15 10
rsiw_len3    = 10 // input(10, title="RSI 3") // 9 
rsiw_len4    = 7 // input(7, title="RSI 4") // 6
rsiw_len5    = input(3, title="RSI 5") // 3

rsi_wicks(rsi_len) =>
    norm_close  = avg(src_close,src_close[1])
    gain_loss_close   = change(src_close)/norm_close
    RSI_close         = 50+50*rma(gain_loss_close, rsi_len)/rma(abs(gain_loss_close), rsi_len)

    norm_open = if wicks==true 
        avg(src_open,src_open[1])
    else 
        avg(src_close,src_close[1])
    gain_loss_open   = change(src_open)/norm_open
    RSI_open         = 50+50*rma(gain_loss_open, rsi_len)/rma(abs(gain_loss_open), rsi_len)
            
    norm_high = if wicks==true 
        avg(src_high,src_high[1])
    else 
        avg(src_close,src_close[1])
    gain_loss_high   = change(src_high)/norm_high
    RSI_high         = 50+50*rma(gain_loss_high, rsi_len)/rma(abs(gain_loss_high), rsi_len)
            
    norm_low  = if wicks==true
        avg(src_low,src_low[1])
    else 
        avg(src_close,src_close[1])
    gain_loss_low   = change(src_low)/norm_low
    RSI_low         = 50+50*rma(gain_loss_low, rsi_len)/rma(abs(gain_loss_low), rsi_len)

    // Sell
    rws_weak = RSI_close<70 and RSI_high>70 and rsi_strong==0? 1 : 0
    rws_mid = RSI_close>70 and RSI_open<70 and rsi_strong==0? 1 : 0 
    rws_strong = RSI_open>70 ? 1 : 0

    // Buy
    rwb_weak = RSI_close>30 and RSI_low<30 and rsi_strong==0? 1 : 0
    rwb_mid = RSI_close<30 and RSI_open>30 and rsi_strong==0? 1 : 0
    rwb_strong = RSI_open<30 ? 1 : 0

    [rws_weak,rws_mid,rws_strong,rwb_weak,rwb_mid,rwb_strong,RSI_close,RSI_high,RSI_low]

[rws_weak1,rws_mid1,rws_strong1,rwb_weak1,rwb_mid1,rwb_strong1,rsi_close1,rsi_high1,rsi_low1] = rsi_wicks(rsiw_len1)
[rws_weak2,rws_mid2,rws_strong2,rwb_weak2,rwb_mid2,rwb_strong2,rsi_close2,rsi_high2,rsi_low2] = rsi_wicks(rsiw_len2)
[rws_weak3,rws_mid3,rws_strong3,rwb_weak3,rwb_mid3,rwb_strong3,rsi_close3,rsi_high3,rsi_low3] = rsi_wicks(rsiw_len3)
[rws_weak4,rws_mid4,rws_strong4,rwb_weak4,rwb_mid4,rwb_strong4,rsi_close4,rsi_high4,rsi_low4] = rsi_wicks(rsiw_len4)
[rws_weak5,rws_mid5,rws_strong5,rwb_weak5,rwb_mid5,rwb_strong5,rsi_close5,rsi_high5,rsi_low5] = rsi_wicks(rsiw_len5)




// === Bollinger Bands %B ===
// ==================================================
bbr_fun(len) =>
    multi = 2 
    b = sma(close, len)
    d = multi * stdev(close, len)
    upper = b + d
    lower = b - d
    bbr = (close - lower)/(upper - lower)
    [bbr]

[bbr] = bbr_fun(14)
[bbr2] = bbr_fun(40)
[bbr3] = bbr_fun(100)


// === RSI Candles - glaz ===
// ==================================================
var int pos = 0
var rsi_color = #000000
rsi_len = input(14, minval=1, title="RSI bar length")
rsi_up = rma(max(change(close), 0), rsi_len)
rsi_down = rma(-min(change(close), 0), rsi_len)
rsi = rsi_down == 0 ? 100 : rsi_up == 0 ? 0 : 100 - (100 / (1 + rsi_up / rsi_down))
isup() => rsi > 70
isdown() => rsi < 30 
// plot(rsi,title="RSI",color=color.blue)
rsi_color := isup() ? color.new(#ff0000, 0) : isdown() ? color.new(color.purple, 0)  : na


// === RES - Ranging EMA Spread  ===
// ==================================================
ema1length = 12 // 40 12
ema2length = 50 // 100 43
ranginglength = 3
rangingmaxvalue = 0.12// 0.14 0.1
rangingminvalue = -0.1
enablebarcolors = false


// Rangin EMA spread
ema1 = ema(close, ema1length)
ema2 = ema(close, ema2length)
spread = ((ema2 / ema1) -1) * 100
r1 = (spread >= rangingminvalue and spread <= rangingmaxvalue) 
r2 = (spread[1] >= rangingminvalue and spread[1] <= rangingmaxvalue)
r3 = (spread[2] >= rangingminvalue and spread[2] <= rangingmaxvalue)
r4 = (spread[3] >= rangingminvalue and spread[3] <= rangingmaxvalue)
r5 = (spread[4] >= rangingminvalue and spread[4] <= rangingmaxvalue)

ranging = false

if (ranginglength == 1) 
    ranging := r1
if (ranginglength == 2) 
    ranging := r1 and r2
if (ranginglength == 3) 
    ranging := r1 and r2 and r3
if (ranginglength == 4) 
    ranging := r1 and r2 and r3 and r4
if (ranginglength == 5) 
    ranging := r1 and r2 and r3 and r4 and r5

res = spread > 0 ? 0-spread : abs(spread)
res := res * 100
res_weak = gray // grey
res_mid = blue // navy
res_strong = aqua // aqua
res_color = ranging ? res_weak : spread > spread[1] ? res_mid : res_strong
// Targets
res_top = 15
res_high = 6
res_low = -6
res_bottom = -15



// === SSL channel by ErwinBeckers  ===
// ==================================================
// Channel 1
ch1_input = input(33,title="Ch1 length")
smaHigh1=sma(high, ch1_input) // 30
smaLow1=sma(low, ch1_input)
var Hlv1 = 0
Hlv1 := close > smaHigh1 ? 1 : close < smaLow1 ? -1 : Hlv1[1]
ch1_t   = Hlv1 < 0 ? smaLow1 : smaHigh1
ch1_b = Hlv1 < 0 ? smaHigh1: smaLow1

// Channel 2
ch2_input = input(15,title="Ch2 length")
smaHigh2=sma(high, ch2_input) // 20 12 15 17
smaLow2=sma(low, ch2_input)
var Hlv2 = 0
Hlv2 := close > smaHigh2 ? 1 : close < smaLow2 ? -1 : Hlv2[1]
ch2_t   = Hlv2 < 0 ? smaLow2 : smaHigh2
ch2_b = Hlv2 < 0 ? smaHigh2: smaLow2


// Channel Conditions
ch1_state = ch1_t>ch1_b?1:ch1_t<ch1_b?-1:0
ch2_state = ch2_t>ch2_b?1:ch2_t<ch2_b?-1:0
ch_inter_up = ch2_t>ch2_b and (ch2_t<ch1_t)?1:0
ch_inter_down = ch1_state==-1 and  ch2_state==-1 and (ch2_b>ch1_b)?1:0
plot(ch1_state,title="Ch1 State",color=ch1_state==1?red:ch1_state==-1?green:gray, style=plot.style_circles)
plot(ch2_state,title="Ch2 State",color=ch2_state==1?red:ch2_state==-1?green:gray, style=plot.style_circles)
plot(ch_inter_up,title="C Intersection Up",color=ch_inter_up==1?red:gray, style=plot.style_circles)
plot(ch_inter_down,title="C Intersection Down",color=ch_inter_down==1?green:gray, style=plot.style_circles)

//sup1=plot(ch1_t,title="Channel Top 1", linewidth=1, color=show_channels?#e91e63:na)
//sdp1= plot(ch1_b,title="Channel Bottom 1", linewidth=1, color=show_channels?#00bcd4:na)
s1_mid = 0.5*(ch1_t + ch1_b)
//plot(s1_mid, title="Channel 1 Mid point", linewidth=channel_line_width, color=show_channels?bb_zones_color:na)
//fill(sdp1,sup1, color=ch1_b>ch1_t and show_channel_fill?#00bcd4:ch1_t>ch1_b and show_channel_fill? #e91e63:na)
sup2=plot(ch2_t,title="Channel Top 2", linewidth=channel_line_width, color=show_channels?color.red:na)
sdp2= plot(ch2_b,title="Channel Bottom 2", linewidth=channel_line_width, color=show_channels?color.lime:na)
//fill(sdp2,sup2, color=ch2_b>ch2_t and show_channel_fill?color.lime:ch2_t>ch2_b and show_channel_fill? color.red:na)



// === Ichimoku  ===
// ==================================================
donchian(len) => avg(lowest(len), highest(len))
c_input = input(9,title="Conversion length")
periods_input = input(30,title="Periods length")
ich_converson = c_input // 15 7  input(9, minval=1, title="Conversion Line Length")
ich_peroids = periods_input // 33 24 25 - input(26, minval=1, title="Base Line Length")
ich_lagging = 52 // input(52, minval=1, title="Lagging Span 2 Length")
displacement = 26 // input(26, minval=1, title="Displacement")
c_line = donchian(ich_converson)
b_line = donchian(ich_peroids)
ich_l1 = avg(c_line, b_line)
ich_l2 = donchian(ich_lagging)


plot(show_ich?c_line:0, color=blue,title="C Line")
plot(show_ich?b_line:0, color=red,title="B Line")

// SSL - Ichimoku
ich_buy = close<open and ch2_state==-1 and c_line<b_line and s3_a<-15
 //and not(bb_zone<2 and isdown() and c_line>ch2_t) 
 and not(c_line>ch2_t) 
 and not((b_line<ch2_t or b_line<ch2_b ) )
 //and not(bb_zone>2 and c_line>ch2_t) 
 //and not(bb_zone>3 and (b_line<ch2_t or b_line<ch2_b ) )
 and not(wae_color!=red)

ich_sell = close>open and ch2_state==1 and c_line>b_line and s3_a>15
//  and not(bb_zone<2 and isdown() and c_line>ch2_t) 
//  and not(bb_zone>2 and c_line>ch2_t) 
//  and not(bb_zone>3 and (b_line<ch2_t or b_line<ch2_b ) )
//  and not(wae_color!=red)

//plot(b_line,title="B Line", color=color.new(red,50))
// plotshape(show_ich and ich_buy?1:0,title="Ichimoku",color=green,style=shape.circle,location=location.bottom)
// plotshape(show_ich and ich_sell?1:0,title="Ichimoku Sell",color=red,style=shape.circle,location=location.top)
ich_c1 = plot(show_ich and ich_l1?1:0, offset = displacement - 1, color=color.yellow,title="Lead 1")
ich_c2 = plot(show_ich and ich_l2?1:0, offset = displacement - 1, color=color.red,title="Lead 2")
//ich_cloud = ich_c1>ich_c2?1:-1
ich_cond2 = ich_l1[26]>ich_l2[26] and ch2_state==-1 and s3_a<-15 and ich_buy==false?1:0
plotshape(show_ich and ich_cond2?1:0,title="Cloud Buy",color=yellow,style=shape.circle,location=location.bottom)



// === Plot === 
// ==================================================
plot(perc_change(close), title='Change',color=color.new(red,100),style=plot.style_circles)
plot(e200, title="EMA 200", color=e200_a>0 and show_ema?orange:e200_a<0 and show_ema?violet:na,linewidth=2 )
plot(e200_a, color=c_hide, title="EMA 200 Angle",style=plot.style_circles)
//plot(basis_angle, color=c_hide, title="Basis Angle",style=plot.style_circles)

// plot(s1_a, color=c_hide , title="SSL angle",style=plot.style_circles)
//plot(s2_a, color=c_hide , title="SSL angle 2",style=plot.style_circles)
// plot(s3_a, color=c_hide , title="SSL angle 3",style=plot.style_circles)
// plot(s3b_a, color=c_hide , title="SSL angle 3 b",style=plot.style_circles)
// plot(s4_a, color=c_hide , title="SSL angle 4",style=plot.style_circles)
// plot(s5_a, color=c_hide , title="SSL angle 5",style=plot.style_circles)

// SSL
//plot(s1, color=ssl_color , title="SSL 1", linewidth=2)
//plot(s2, color=orange , title="SSL 2")
//plot(s3, color=aqua , title="SSL 3")
//plot(s4, color=yellow , title="SSL 4",linewidth=2)
s5_col = s5>bb_upper?#ff0000 : s5<bb_lower ? #00ddff : green
//plot(s5, color=s5_col , title="SSL 5",linewidth=2)

// Kijun
//plot(kijun, color=#f44336, title="Kijun",linewidth=2)

// BB
plot(show_bb?basis:na, title="Basis", color=bb_zones_color,linewidth=3)
p1 = plot(show_bb?bb_upper:na, "BB Upper ",color=bb_zones_color,linewidth=2)
p2 = plot(show_bb?bb_lower:na, "BB Lower ",color=bb_zones_color,linewidth=2)
plot(show_bb?bb_upper2:na, "BB Upper 2 ",color=bb_zones_color2,linewidth=2)
plot(show_bb?bb_lower2:na, "BB Lower 2 ",color=bb_zones_color2,linewidth=2)
//fill(p1,p2, bb_zones_color)
// EMA 200 bands large
//plot(e200, "EMA 200", color=e200_a>0?ea_sc:ea_bc,linewidth=2 )
//plot(ema_fast, color=color.blue, title="EMA Fast")
// ATR
plot(show_atr ? atr_upper : na, "+ATR Upper", color=show_atr?color.new(#ffffff,70):color.new(#ffffff,100))
plot(show_atr ? atr_lower : na, "-ATR Lower", color=show_atr?color.new(#ffffff,70):color.new(#ffffff,100))

// RSI Up
// RSI 5
rsi5_color = rws_weak5==1?yellow: rws_mid5==1?orange:rws_strong5==1?#ff0000:na
plotshape(rsi5_show and rsi_high5>70 and close>open and e200_a<-1?1:na,title="RSI 5",color=rsi5_color,style=shape.circle,location=location.top)
// RSI 4
//plot(rsi_high4, title="RSI 4", color=color.new(blue,100),style=plot.style_circles)
rsi4_color = rws_weak4==1?yellow: rws_mid4==1?orange:rws_strong4==1?#ff0000:na
plotshape(rsi4_show and rsi_high4>70 and close>open and e200_a<-1?1:na,title="RSI 4",color=rsi4_color,style=shape.circle,location=location.top)
// RSI 3
//plot(rsi_close3, title="RSI 3", color=color.new(blue,100),style=plot.style_circles)

plotshape(rsi3_show and rsi_high3>70 and close>open?1 and close>open:na,title="RSI 3",color=rws_mid3==1?orange:rws_strong3==1?#ff0000:yellow,style=shape.circle,location=location.top)
// RSI 2
plotshape(rsi2_show and (rsi_close2>70 or rsi_high2>70) and close>open ?1:na,title="RSI 2",color=rsi_high2>70 and rsi_close2<70?#e47aee:rws_mid2==1?#e116f5:rws_strong2==1?#ff008c:na,style=shape.circle,location=location.top)
//RSI 1
//plot(rsi_high1, title="RSI 1", style=plot.style_circles, color=black)
plotshape(rsi1_show and (rsi_close1>70 or rsi_high1>70) and close>open?1:na,title="RSI 1",color=rsi_high1>70 and rsi_close1<70?yellow:rws_mid1==1?orange:rws_strong1==1?#ff0000:na,style=shape.circle,location=location.top)


// RSI Down
// RSI 5
plotshape(rsi5_show and rsi_close5<30 and open>close and e200_a>0?1:na,title="RSI 5",color=rwb_mid5==1?#0053ff:rwb_strong5==1?lime:na,style=shape.circle,location=location.bottom)
// RSI 4
plotshape(rsi4_show and rsi_close4<30 and open>close and e200_a>0?1:na,title="RSI 4",color=rwb_mid4==1?#0053ff:rwb_strong4==1?lime:na,style=shape.circle,location=location.bottom)
// RSI 3
plotshape(rsi3_show and rsi_close3<30 and open>close and e200_a>0?1:na,title="RSI 3",color=rwb_mid3==1?#0053ff:rwb_strong3==1?lime:na,style=shape.circle,location=location.bottom)
//plot(rsi3_show and rsi_close3<30 and open>close?rsi_close3:na, title="RSI Close 3", color=color.new(blue,100), style=plot.style_circles )
// RSI 2
r2_d_cond = rsi2_show and rsi_low2<30 and close<open?1:na
r2_c = rsi_low2<30 and rsi_close2>30?#c77bfd:rwb_mid2==1?#9200fc:rwb_strong2==1?#00f1f2:na
plotshape(r2_d_cond,title="RSI 2 Close",color=r2_c,style=shape.circle,location=location.bottom)
// RSI 1
r1_d_cond = rsi1_show and rsi_low1<30 and close<open?1:na
r1_c = rsi_low1<30 and rsi_close1>30?#5e91ff:rwb_mid1==1?#0053ff:rwb_strong1==1?lime:na
plotshape(r1_d_cond,title="RSI 1",color=r1_c,style=shape.circle,location=location.bottom)




// Bar color
barcolor(show_bbr==false ? rsi_color:na, title="Rsi Candles")
bbr_color = bbr>1 ? color.new(#ff0000, 0) : bbr<0 ? color.new(color.purple, 0)  : na
barcolor(show_bbr?bbr_color:na, title="Show bbr")
//barcolor(s3_a<15?yellow:na, title="S1 close")

//barcolor(pos == -1 ? #ff0000: pos == 1 ? #00a000 : gray)


var bool enter_exit = false
//var int trade_dir = 0
var int state = 0
var int bar_num = 0
var float lastPrice = 0.0
var mult_diff = 10000
var int num_pips = 20
var int num_bars = 4

entry_signal() =>
	candle = close > open ? 1 : 0
    dir = 0
    counter = 0
    cond = ''
    allow = false
    ea = e200_a
    ba = basis_angle
    ba2 = basis_angle2
    ea_cnt = e200_a<5?true:false

    // Sell t is on top - Buy t is on bottom
    //ch1_state = ch1_t>ch1_b?1:ch1_t<ch1_b?-1:0
    ch1_sell = ch1_state==1?true:false
    ch1_buy = ch1_state==-1?true:false
    ch2_sell = ch2_state==1?true:false
    ch2_buy = ch2_state==-1?true:false

    // === SELL - Uptrend ===
    // ===========
    
    if candle==1 and ea>0

        if bb_zone2<2 and rsi_close5>70 and high>bb_upper2
         and not(bb_zone==4)
            dir := -1
            cond := 'r5-z1'

        if rsi_close3>70 or rws_weak3
         and not(bb_zone2<2)
         and not(s3_a>22)
         and not(bb_upper<bb_upper2)
            dir := -1
            cond := 'r3-weak'

        if rsi_close2>70 or rws_weak2
            dir := -1
            cond := 'r2'

        if rsi_close1>70 or rws_weak1
         //and not(s3_a>22 and s5>e50)
            dir := -1
            cond := 'r1'

        if 
         (s3<s1)
         or (s3_a<ba or s1_a<ba)
         or (bb_zone==0)
         or (bb_lower<bb_lower2)
            dir := 0

        if isup()==false and s1_a>ba and close<bb_upper
         and not(s1_a<0)
         and not(s4<s5)
         and not(bb_zone<2)
            dir := -1
            cond := 'z3'

        // if ch1_sell and s5>ch1_t and s5>s4 and s3>s5 and close>s5
        //     dir := -1
        //     cond := 's5'


    // === BUY Counter ===
    // ===========
    if candle==0 and ea>-1
        // === RSI 5 ===
        if rsi_close5<30
         and not(rsi_close4<30 or rwb_weak4) 
            dir := 1
            cond := 'r5'

        // === RSI 4 ===
        if rsi_close4<30 or rwb_weak4
         and not(rsi_close3<30 or rwb_weak3) 
            dir := 1
            cond := 'r4'

        // === RSI 3 ===
        if rsi_close3<30 or rwb_weak3 
            dir := 1
            cond := 'r3'



        if (ch1_buy==false)
         or (s5>ch1_b and open>ch1_t)
         or (ba<0 and c_line>ch1_t)
            dir := 0

        //if s3>s1 or s3>ch1_t 
         //or (ch1_buy==false)
         //or (c_line>b_line and close>bb_lower2)
         //and not(b_line<ch1_b)
         //and not(bb_zone==1 and open>basis)
         //(s5>s4 and s5_a<0) 
        // (bb_zone2==2 and ch1_buy==false)
        //  or ch1_buy==false
        //  or (s3>ch1_t and bb_zone>0)
        //  // s1>s5 
        //  //or (s5>ch1_b and s4>ch1_b)
            //dir := 0

        // if ch1_sell and close<ch1_t and res_color==blue
        //  and not(bb_zone<3)
        //     dir := 1
        //     cond := 'ch1-sell'

        if ba<-20
            dir := 1
            cond := 'ba'

        if rsi_close1<30 or rwb_weak1
            dir := 1
            cond := 'r1'
            
        if rsi_close2<30 or rwb_weak2
         and not(rsi_close1<30)
            dir := 1
            cond := 'r2'

        if bb_zone2<2 and low<bb_lower2 and
         (c_line<ch1_t or b_line<c_line)
            dir := 1
            cond := 'bb2'

        counter := 1
        


    // === Buy - Downtrend ===
    // ======================
    if candle==0 and ea<0

        if bb_zone>2

            if rwb_strong1
             and not(high<s3)
                dir := 1
                cond := 'r1'

            if rwb_weak1 and close>bb_lower
             //and not(low<bb_lower) 
                dir := 1
                cond := 'r1-weak'
                counter := 1

            // if (b_line>ch1_b or b_line>ch1_t) or
            //  (c_line[1]>b_line and isdown())
            //     dir := 0


            // if rsi_close5<30 and bb_lower<bb_lower2 and close>bb_lower and
            //  c_line<ch2_t and open<c_line
            //  and not(bb_zone==4 and bb_lower<bb_lower[1])
            //     dir := 1
            //     cond := 'z3-below'

            // if rsi_close5<30 and low>bb_lower and
            //  s3<s1 and s3<s4
            //  and not(rsi_close4<30)
            //     dir := 1
            //     cond := 'r5-z3'

        if (bb_upper>basis2)
         or (s3b_a>s1_a)
            dir := 0


        if bb_zone==2

            if rwb_strong1 or rwb_weak1
                dir := 1
                cond := 'r1'

            // if bb_upper<e200 and di_minus>adx_mid
            //     dir := 1
            //     cond := 'adx'

            if 
             (ch2_buy==false)
             or (s1>s4 or s3>s1)
                dir := 0

            if (s1>s4)
                dir := 0

            // if s5<s4 and s5<basis and s3<s5 and s3<s1 and close<s5 and s1<s4
            //  and not(ch2_t>ch1_t or ch2_t>ch1_b)
            //     dir := 1
            //     cond := 's5-z2'

    
        if bb_zone<2

            if ch2_buy and open<ch2_t and s1<basis and s3<s5 and
             rwb_strong5 and s4<s5
                dir := 1
                cond := 'ch2-z1'

            // if rsi_close5<30 and rwb_strong5 and 
            //  s5<s4 and s5<s1
            //     dir := 1
            //     cond := 's5'

            // if bb_upper<e200 and rsi_close5<30 and s1>s5 and s4_a>0 and close<basis
            //     dir := 1
            //     cond := 's4'

            if ch2_buy==0 or c_line>ch2_b or c_line>ch2_t 
                dir := 0


         // Sell t is on top - Buy t is on bottom
         // ch1_state = ch1_t>ch1_b?1:ch1_t<ch1_b?-1:0

        if rsi_close5<30 and 
         c_line<ch2_t and open<c_line and
         b_line>=ch2_b
         and not(b_line>ch1_t and ch1_sell)
         and not(bb_zone<2)
         and not(s3_a<-18)
         and not(bb_zone>2 and s3b_a>s1_a)
         and not(bb_upper>basis2 and bb_zone>2)
            dir := 1
            cond := 'z3-below'

        // if rsi_close5>70 and 
        //  c_line>ch2_t and open>c_line and
        //  b_line<=ch2_b
        //  and not(b_line>ch1_t and ch1_sell)
        //  and not(bb_zone<2)
        //  and not(s3_a>18)
        //     dir := 1
        //     cond := 'z3'





    // SELL Counter
    if candle==1 and ea<0

        if rsi_close1>70 or rws_weak1
            dir := -1
            cond := 'r1-d'

        if rsi_close2>70
            dir := -1
            cond := 'r2-d'

        // if rsi_close5>70 and bb_zone>2 and close<s5
        //  and not(rsi_close4>70)
        //     dir := -1
        //     cond := 'r5-d'

        // if s3<s1 and s1_a<-5 and res_color==blue and high>ch2_t and close<ch2_t
        //     dir := -1
        //     cond := 'ch-blue'

        // if bb_zone==3 and res_color==aqua and ch2_sell and high<ch2_t 
        //     dir := -1
        //     cond := 'ch-aqua'


            

        // if ch2_sell and ch1_sell and bb_zone<2
        //     dir := -1
        //     cond := 'ch1-ch2'

        if ch2_sell==0
            dir := 0

        if ch1_sell and c_line>b_line and open>ch2_t and open>ch1_t
            dir := -1
            cond := 'c1'
            counter := 1

        if ch2_sell and rws_strong5 and (s1>basis or (wae_line>wae_dz and wae_color==green) )
         and not(s3_a<0)
         and not(close<ch2_t)
         and not(bb_zone==1 and open<ch2_t)
         and not(rsi_close4>70)
            dir := -1
            cond := 'ch2-5'

        if ch2_sell and rsi_close4>70 and (s1>basis or (wae_line>wae_dz and wae_color==green) )
         and not(s3_a<0)
         and not(close<ch2_t)
         and not(bb_zone==1 and open<ch2_t)
         and not(c_line<=b_line)
            dir := -1
            cond := 'ch2-4'

        // if ba<0 and
        //  (ch2_sell and c_line>b_line and res_color==aqua)
        //     dir := -1
        //     cond := 'ch2-aqua'

        // if bb_zone>1 and close<c_line and ch2_buy and 
        //  res_color==blue
        //  and not(bb_zone==4)
        //     dir := -1
        //     cond := 'ch2-blue'

        if bb_zone>1 and high<s5 and close>c_line and ch2_buy and 
         res_color==aqua and c_line<c_line[1]
            dir := -1
            cond := 'ch2-aqua'

        if bb_zone==1 and bb_zone2==3 and close>c_line and ch2_sell and 
         res_color==aqua
            dir := -1
            cond := 'ch2-aqua2'

        if (s5<s4 or s4<open) and bb_zone<2
        //b_line>bb_upper //high<bb_upper
            dir:=0

    // Channel Sell


    // Channel buy
    // if candle==0 and s5<e200 and ch2_buy and rsi_close5<30
    //  and not(s4_a<1 or s1_a<0)
    //     dir := 1
    //     cond := 'ch2-s5'

    // Filter out same direction trades, wait 4 candles
    // and price higher than 20 pips before allowing another trade
    if dir == state and use_logic and counter==0
        allow := (bar_index - bar_num) >= num_bars ? true : false
        // sell
        if state == -1
            dir := ((close - lastPrice) * mult_diff) > num_pips and allow ? dir : 0
        // buy
        if state == 1
            dir := ((close - lastPrice) * mult_diff) < (num_pips * -1) and allow ? dir : 0

    // Do not trade during New York close
    if ny_close==1 and use_ny_filter
        dir := 0

    //var int enter_exit = basis_angle > 0 and dir == -1 ? 0 : basis_angle < 0 and dir
    // if counter==1 and state==dir
    //     dir := 0
        
    type = dir > 0 ? 1 : dir < 0 ? -1 : 0
    [type,counter,cond]

[trade_dir,counter,condition] = entry_signal() 

if trade_dir != 0 and use_logic
    state := trade_dir

    enter_exit := counter == 1 ? 1 : 0
    // Sell
    // if basis_angle > 0 and trade_dir == -1
    //     enter_exit := -1
    // // Counter
    // if basis_angle > 0 and trade_dir == 1
    //     enter_exit := 0
    // // Buy
    // if basis_angle < 0 and trade_dir == 1
    //     enter_exit := -1
    // // Counter
    // if basis_angle < 0 and trade_dir == -1
    //     enter_exit := 0
    // if basis>e200
    //     enter_exit := trade_dir == -1 ? false : trade_dir == 1 or counter ? true : false
    // if basis<e200
    //     enter_exit := trade_dir == 1 ? false : trade_dir == -1 or counter ? true : false
    //enter_exit := counter == 1 ? true : enter_exit == false ? true : false
    lastPrice := close
    bar_num   := bar_index

// if enter_exit != trade_dir and trade_dir != 0
//     enter_exit := enter_exit == false ? true : false
labelText = tostring(condition)
trade_color = trade_dir > 0  ? buy_color : sell_color
if trade_dir != 0 // and show_cond
	if trade_dir > 0
        buy = label.new(x=time,y=high,text=labelText,xloc=xloc.bar_time, yloc=yloc.belowbar,color=trade_color,textcolor=color.white, size=size.normal,style=label.style_label_up)
    else
        sell = label.new(x=time,y=high,text=labelText,xloc=xloc.bar_time, yloc=yloc.abovebar,color=trade_color,textcolor=color.white, size=size.normal)

plot(counter,title="Counter Trade", style=plot.style_circles)
plot(state,"State",style=plot.style_circles)

// plotshape(show_entry and trade_dir == 1 and enter_exit == 0 ? 1 : na, title="Entry Buy", color=trade_color, location = location.belowbar, style=shape.labelup, text="B", textcolor=color.white, size=size.small)
// plotshape(show_entry and trade_dir == 1 and enter_exit == -1 ? 1 : na, title="Counter Buy", color=trade_color, location = location.belowbar, style=shape.labelup, text="Exit", textcolor=color.white, size=size.small)
// plotshape(show_entry and trade_dir == -1 and enter_exit == 0 ? -1: na, title="Entry Sell", color=trade_color, location = location.abovebar, style=shape.labeldown, text="S", textcolor=color.white, size=size.small)
// plotshape(show_entry and trade_dir == -1 and enter_exit == -1 ? -1 : na, title="Exit Sell", color=trade_color, location = location.abovebar, style=shape.labeldown, text="Exit", textcolor=color.white, size=size.small)
