//@version=5
strategy('TB - May 19',
 overlay=true,
 precision=6,
 currency=currency.USD,
 initial_capital=10000,
 default_qty_value=10,
 commission_type=strategy.commission.percent,
 commission_value = 0.0012,
 //max_bars_back = 500,
 pyramiding=1)


//strategy.risk.max_position_size(400000)

red = #ff0000
orange = #ff9800
yellow = #FFFF00
green = #55d51a
lime = #50e600
aqua = #00bcd4
blue = #2962ff
dark_blue = #2962ff
violet = #814dff
white = #ffffff
gray = #707070
black = #000000

// Angle
angle(_src, len) =>
    rad2degree = 180 / 3.14159265359  //pi 
    ang = rad2degree * math.atan((_src[0] - _src[1]) / ta.atr(len))
    ang

// SYM Info
min_tick = syminfo.mintick
var int decimals = int(math.log10(1/min_tick))
decimals := decimals == 2 ? 1 : decimals == 0 ? 1 : decimals
// plot(min_tick, title='Min Tick')
// plot(decimals, title='Decimals')
get_pip_distance(point1, point2) =>
    diff_points = math.abs( (point1 - point2) )
    pip_value = decimals<3 ? diff_points / syminfo.mintick / 100 : diff_points / syminfo.mintick / 10
    //pip_value = decimals>3 ? diff_points * (math.pow(10, decimals) * syminfo.mintick) : diff_points / syminfo.mintick / 10

// Change
perc_change() =>
    perc = math.abs((1 - close[1] / close) * 10000)
    perc

// Calculate Multi Timeframes
newbar(res) => ta.change(time(res)) == 0 ? 0 : 1

is_between(p1, p2) =>
    is_inside = p1>p2 ? (close<p1) and (open<p1) and (close>p2) and (open>p2) : (close>p1) and (open>p1) and (close<p2) and (open<p2)
    is_inside



f_ma_angle_dir(m_angle, time)=>
    ma_angle_direction = m_angle[0]>m_angle[str.tonumber(time) * 0.5] ? 1 : -1
    
//
// ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ //
// Moving Averages
// ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ //
// ------------------------------------------------------------------------------------------------------------------
g_ma = "MA ----------------------------------------------------"
inl_cb = "cb"
inl_ma = "ma"
i_ma_time = input.timeframe(title='Timeframe', defval='60',group=g_ma)
i_ma_time2 = input.timeframe(title='Timeframe 2', defval='120',group=g_ma)
ma_type1 = input.string(title="MA Type 1", defval="hma", options=["sma","ema","zema","dema","tema","wma","vwma","smma","rma","hma","lsma","McGinley"],group=g_ma)
ma_type2 = input.string(title="MA Type 2", defval="ema", options=["sma","ema","zema","dema","tema","wma","vwma","smma","rma","hma","lsma","McGinley"],group=g_ma)
i_show_ma = input.bool(false,title='Show MAs',group=g_ma)


show_h1 = input.bool(title="M1", defval=false,group=g_ma,inline=inl_cb)
show_h2 = input.bool(title="M2", defval=true,group=g_ma,inline=inl_cb)
show_h3 = input.bool(title="M3", defval=false,group=g_ma,inline=inl_cb)
show_h4 = input.bool(title="M4", defval=true,group=g_ma,inline=inl_cb)
show_h5 = input.bool(title="M5", defval=false,group=g_ma,inline=inl_cb)
show_h6 = input.bool(title="M6", defval=false,group=g_ma,inline=inl_cb)
show_h7 = input.bool(title="M7", defval=false,group=g_ma,inline=inl_cb)
show_h8 = input.bool(title="M8", defval=false,group=g_ma,inline=inl_cb)
show_h9 = input.bool(title="M9", defval=false,group=g_ma,inline=inl_cb)

l1 = input.int(5,title="M1",group=g_ma,inline=inl_ma)
l2 = input.int(15,title="M2",group=g_ma,inline=inl_ma)
l3 = input.int(50,title="M3",group=g_ma,inline=inl_ma)
l4 = input.int(75,title="M4",group=g_ma,inline=inl_ma)
l5 = input.int(100,title="M5",group=g_ma,inline=inl_ma)
l6 = input.int(200,title="M6",group=g_ma,inline=inl_ma)
l7 = input.int(300,title="M7",group=g_ma,inline=inl_ma)
l8 = input.int(500,title="M8",group=g_ma,inline=inl_ma)
l9 = input.int(1000,title="M9",group=g_ma,inline=inl_ma)

angle_amount = 14 //input.int(14, title="Angle Amount",group=g_ma)
i_gaps = input.bool(false,title='Use Gaps',group=g_ma)
show_ma_candles = false //input.bool(false,title="Show Candle Division",group=g_ma)
show_angles = input.bool(false,title='Show Angles',group=g_ma)
i_offset = 0 //input.int(0, title="Offset",group=g_ma) 
src = close //input.source(close, "Source",group=g_ma)
i_ma_use_smooth = false //input.bool(false, title="Use Smoothing",group=g_ma)
i_ma_smooth = 1 //input.int(1,title="Smoothing",group=g_ma)
use_multiple = false //input.bool(false, title="Use Multiply",group=g_ma)
multi_value = 10 //input.int(10, title="Multiply Value",group=g_ma)

g_fill = "Fills"
inl_fill = "fill"
inl_conv = "conv"
i_ma_select = input.int(3, title="Colorized", options=[1,2,3,4,5,6,7,8],group=g_fill)
show_fill = input.bool(title="Show Fill", defval=true,inline=inl_fill,group=g_fill)
show_conv = input.bool(title="Show Conv", defval=true,inline=inl_fill,group=g_fill)
conv_amount = input.float(20, title="Conv Amount", step=1,inline=inl_conv,group=g_fill ) // 4

g_angles = "MA Angles ----------------------------------------------------"
inl_angles = "angles"
i_ang_1 =  input.int(1, title='A1',group=g_angles,inline=inl_angles)
i_ang_2 =  input.int(2, title='A2',group=g_angles,inline=inl_angles)
i_ang_3 =  input.int(3, title='A3',group=g_angles,inline=inl_angles)
i_ang_4 =  input.int(4, title='A4',group=g_angles,inline=inl_angles)
i_ang_5 =  input.int(5, title='A5',group=g_angles,inline=inl_angles)
i_ang_6 =  input.int(7, title='A6',group=g_angles,inline=inl_angles)
i_ang_7 =  input.int(10, title='A7',group=g_angles,inline=inl_angles)
i_ang_8 =  input.int(12, title='A8',group=g_angles,inline=inl_angles)

use_candles = input.bool(false,title="Colorize Candles")
g_select = "Select ----------------------------------------------------"
i_sel_ma = input.int(title='MA Select', defval=3, options=[1,2,3,4,5,6,7,8,9], group=g_select)
i_sel_angle = input.int(title="Angle Select", defval=3, options=[1,2,3,4,5,7,10,12,14],group=g_select)


// Lines and Angles
ma_graph(len,type) =>
    ma =0.0
    length = 1
    if use_multiple
        length := len * multi_value
    else 
        length := len

    if type == 'sma' // Simple
        ma := ta.sma(src,length) 

    if type == 'ema' // Exponential
        ma := ta.ema(src,length)

    if type == 'zema' // Zero Lag Exponential
        e1 = ta.ema(close,length)
        e2 = ta.ema(e1,length)
        diff = e1 - e2
        ma := e1 + diff 

    if type=="dema" // Double Exponential
        e = ta.ema(src, length)
        ma := 2 * e - ta.ema(e, length)
    if type == 'tema' // Triple Exponential
        ema1 = ta.ema(src, length)
        ema2 = ta.ema(ema1, length)
        ema3 = ta.ema(ema2, length)
        ma := 3 * (ema1 - ema2) + ema3
    if type == 'wma' // Weighted
        ma := ta.wma(src,length)
    if type == 'vwma' // Volume Weighted
        ma := ta.vwma(src,length)
    if type=="smma" // Smoothed
        w = ta.wma(src, length)
        _sma = ta.sma(src, length) 
        log.warning("Warning {0} ", _sma )
        ma := na(w[1]) ? _sma : (w[1] * (length - 1) + src) / length
    if type == "rma"
        ma := ta.rma(src, length)
    if type == 'hma' // Hull
        ma := ta.hma(src, length)
       // ma := ta.wma(2*ta.wma(src, length/2)-ta.wma(src, length), math.floor(math.sqrt(length) ))
    if type=="lsma" // Least Squares
        ma := ta.linreg(src, length, 0)
    if type=="McGinley"
        mg = 0.0
        _ema = ta.ema(src, length)
        log.warning("Warning {0}", _ema )
        mg := na(mg[1]) ? _ema : mg[1] + (src - mg[1]) / (length * math.pow(src/mg[1], 4))
        ma :=mg

    if i_ma_use_smooth
        ma := ta.sma(ma,i_ma_smooth)
    ma


ma_conv(t1, t2) =>
    
    diff = get_pip_distance(t1, t2)
    conv = show_conv and diff < conv_amount and diff > conv_amount * -1 ? true : false
    [diff, conv]


// === HMA ===
// ==================================================
h1 = ma_graph(l1,ma_type1)
h2 = ma_graph(l2,ma_type1)
h3 = ma_graph(l3,ma_type1)
h4 = ma_graph(l4,ma_type1)
h5 = ma_graph(l5,ma_type1)
h6 = ma_graph(l6,ma_type1)
h7 = ma_graph(l7,ma_type1)
h8 = ma_graph(l8,ma_type1)
h9 = ma_graph(l9,ma_type1)

// Angles
h1_a = angle(h1,angle_amount)
h2_a = angle(h2,angle_amount)
h3_a = angle(h3,angle_amount)
h4_a = angle(h4,angle_amount)
h5_a = angle(h5,angle_amount)
h6_a = angle(h6,angle_amount)
h7_a = angle(h7,angle_amount)
h8_a = angle(h8,angle_amount)
h9_a = angle(h9,angle_amount)

[h2_h4_diff,h2_h4_conv] = ma_conv(h2,h4)
[h4_h5_diff,h4_h5_conv] = ma_conv(h4,h5)
[h5_h6_diff,h5_h6_conv] = ma_conv(h5,h6)
[h7_h8_diff,h7_h8_conv] = ma_conv(h7,h8)



// === Multi-Timeframe ===
[h1_m,h2_m,h3_m,h4_m,h5_m,h6_m,h7_m,h8_m,h9_m] = request.security(syminfo.tickerid, i_ma_time, 
 [ma_graph(l1,ma_type1)
 ,ma_graph(l2,ma_type1)
 ,ma_graph(l3,ma_type1)
 ,ma_graph(l4,ma_type1)
 ,ma_graph(l5,ma_type1)
 ,ma_graph(l6,ma_type1)
 ,ma_graph(l7,ma_type1)
 ,ma_graph(l8,ma_type1)
 ,ma_graph(l9,ma_type1)
 ]
 ,gaps=i_gaps?barmerge.gaps_on:barmerge.gaps_off ) 

// Angles
[h1_a_m,h2_a_m,h3_a_m,h4_a_m,h5_a_m,h6_a_m,h7_a_m,h8_a_m,h9_a_m] = request.security(syminfo.tickerid, i_ma_time, 
 [angle(h1_m,angle_amount)
 ,angle(h2_m,angle_amount)
 ,angle(h3_m,angle_amount)
 ,angle(h4_m,angle_amount)
 ,angle(h5_m,angle_amount)
 ,angle(h6_m,angle_amount)
 ,angle(h7_m,angle_amount)
 ,angle(h8_m,angle_amount)
 ,angle(h9_m,angle_amount)
 ]
 ,gaps=i_gaps?barmerge.gaps_on:barmerge.gaps_off ) 

[h2_h4_diff_m,h2_h4_conv_m] = ma_conv(h2_m,h4_m)
[h4_h5_diff_m,h4_h5_conv_m] = ma_conv(h4_m,h5_m)
[h5_h6_diff_m,h5_h6_conv_m] = ma_conv(h5_m,h6_m)
[h7_h8_diff_m,h7_h8_conv_m] = ma_conv(h7_m,h8_m)

if newbar(i_ma_time) == 0
    h1_a_m := h1_a_m[1]
    h2_a_m := h2_a_m[1]
    h3_a_m := h3_a_m[1]
    h4_a_m := h4_a_m[1]
    h5_a_m := h5_a_m[1]
    h6_a_m := h6_a_m[1]
    h7_a_m := h7_a_m[1]
    h8_a_m := h8_a_m[1]
    h9_a_m := h9_a_m[1]

// Colorized MA angles
ma_select()=>
    float select = switch i_ma_select
        2 => h2_a_m
        3 => h3_a_m
        4 => h4_a_m
        5 => h5_a_m
        6 => h6_a_m
        7 => h7_a_m
        8 => h8_a_m
        => h5_a_m

    ma_a = math.abs(select)
    ma_zone =  
     ma_a   < i_ang_1 ? 0 
     : ma_a < i_ang_2 ? 1 
     : ma_a < i_ang_3 ? 2 
     : ma_a < i_ang_4 ? 3 
     : ma_a < i_ang_5 ? 4 
     : ma_a < i_ang_6 ? 5 
     : ma_a < i_ang_7 ? 6 
     : ma_a < i_ang_8 ? 7 
     : ma_a > i_ang_7 ? 8 
     : na

    ma_color = 
     ma_zone   == 0 ? red 
     : ma_zone == 1 ? orange 
     : ma_zone == 2 ? yellow 
     : ma_zone == 3 ? gray 
     : ma_zone == 4 ? green 
     : ma_zone == 5 ? lime
     : ma_zone == 6 ? blue
     : ma_zone == 7 ? aqua
     : ma_zone == 8 ? white : na

    [ma_color]

[ma_color] = ma_select()

// plot(f_ma_angle_dir(h2_a_m,i_ma_time)==1 ? 1 : -1, title='H2 angle', color=color.new(blue,100), style=plot.style_circles )
// p_h2 = plot(h2 and show_h2?h2_m:na, color= h2_a_m>h2_a_m[30] ? green : red,title="h2")


// === EMA ===
// ==================================================
m1 = ma_graph(l1,ma_type2)
m2 = ma_graph(l2,ma_type2)
m3 = ma_graph(l3,ma_type2)
m4 = ma_graph(l4,ma_type2)
m5 = ma_graph(l5,ma_type2)
m6 = ma_graph(l6,ma_type2)
m7 = ma_graph(l7,ma_type2)
m8 = ma_graph(l8,ma_type2)
m9 = ma_graph(l9,ma_type2)

// Angles
m1_a = angle(m1,angle_amount)
m2_a = angle(m2,angle_amount)
m3_a = angle(m3,angle_amount)
m4_a = angle(m4,angle_amount)
m5_a = angle(m5,angle_amount)
m6_a = angle(m6,angle_amount)
m7_a = angle(m7,angle_amount)
m8_a = angle(m8,angle_amount)
m9_a = angle(m9,angle_amount)



[m1_m,m2_m,m3_m,m4_m,m5_m,m6_m,m7_m,m8_m,m9_m] = request.security(syminfo.tickerid, i_ma_time2, 
 [ma_graph(l1,ma_type2)
 ,ma_graph(l2,ma_type2)
 ,ma_graph(l3,ma_type2)
 ,ma_graph(l4,ma_type2)
 ,ma_graph(l5,ma_type2)
 ,ma_graph(l6,ma_type2)
 ,ma_graph(l7,ma_type2)
 ,ma_graph(l8,ma_type2)
 ,ma_graph(l9,ma_type2)
 ]
 ,gaps=i_gaps?barmerge.gaps_on:barmerge.gaps_off ) 

[m1_a_m,m2_a_m,m3_a_m,m4_a_m,m5_a_m,m6_a_m,m7_a_m,m8_a_m,m9_a_m] = request.security(syminfo.tickerid, i_ma_time2, 
 [angle(m1_m,angle_amount)
 ,angle(m2_m,angle_amount)
 ,angle(m3_m,angle_amount)
 ,angle(m4_m,angle_amount)
 ,angle(m5_m,angle_amount)
 ,angle(m6_m,angle_amount)
 ,angle(m7_m,angle_amount)
 ,angle(m8_m,angle_amount)
 ,angle(m9_m,angle_amount)
 ]
 ,gaps=i_gaps?barmerge.gaps_on:barmerge.gaps_off )

if newbar(i_ma_time) == 0
    m1_m := m1_m[1]
    m2_m := m2_m[1]
    m3_m := m3_m[1]
    m4_m := m4_m[1]
    m5_m := m5_m[1]
    m6_m := m6_m[1]
    m7_m := m7_m[1]
    m8_m := m8_m[1]
    m9_m := m9_m[1]

    // Angles
    m1_a_m := m1_a_m[1]
    m2_a_m := m2_a_m[1]
    m3_a_m := m3_a_m[1]
    m4_a_m := m4_a_m[1]
    m5_a_m := m5_a_m[1]
    m6_a_m := m6_a_m[1]
    m7_a_m := m7_a_m[1]
    m8_a_m := m8_a_m[1]
    m9_a_m := m9_a_m[1]


ma_select2()=>
    float select = switch i_ma_select
        2 => m2_a_m
        3 => m3_a_m
        4 => m4_a_m
        5 => m5_a_m
        6 => m6_a_m
        7 => m7_a_m
        8 => m8_a_m
        => m5_a_m

    ma_a = math.abs(select)
    ma_zone =  
     ma_a   < i_ang_1 ? 0 
     : ma_a < i_ang_2 ? 1 
     : ma_a < i_ang_3 ? 2 
     : ma_a < i_ang_4 ? 3 
     : ma_a < i_ang_5 ? 4 
     : ma_a < i_ang_6 ? 5 
     : ma_a < i_ang_7 ? 6 
     : ma_a < i_ang_8 ? 7 
     : ma_a > i_ang_7 ? 8 
     : na

    ma_colorize = 
     ma_zone   == 0 ? red 
     : ma_zone == 1 ? orange 
     : ma_zone == 2 ? yellow 
     : ma_zone == 3 ? gray 
     : ma_zone == 4 ? green 
     : ma_zone == 5 ? lime
     : ma_zone == 6 ? blue
     : ma_zone == 7 ? aqua
     : ma_zone == 8 ? white : na

    [ma_colorize]

[ma_color2] = ma_select2()

[m2_m4_diff_m,m2_m4_conv_m] = ma_conv(m2_m,m4_m)
[m4_m5_diff_m,m4_m5_conv_m] = ma_conv(m4_m,m5_m)
[m5_m6_diff_m,m5_m6_conv_m] = ma_conv(m5_m,m6_m)
[m7_m8_diff_m,m7_m8_conv_m] = ma_conv(m7_m,m8_m)



p_h3_m = plot(h3_m and show_h3?h3_m:na, color= i_ma_select==3 ? ma_color : h3_a_m>0 ? orange : h3_a_m<0 ? red : green,title="H3")

// HMA
//plot(show_angles and h2_a_m ? h2_a_m : na,color=h2_a_m>0 ? color.new(color.green,100): color.new(color.red,100),title="M2 A", style=plot.style_circles)
//plot(show_angles and h3_a_m ? h3_a_m : na,color=i_ma_select==3 ? color.new(ma_color,100) : color.new(color.blue,100),title="M3 A", style=plot.style_circles)
//p_h5_m = plot(h5_m and show_h5?h5_m:na, style=plot.style_line, color= i_ma_select==5 ? ma_color : h5_a_m<1 and h5_a_m>-1 ? aqua : h5_a_m>0 ? orange : h5_a_m<0 and h5_m>h6_m ? red : green,title="M5")
//p_h6_m = plot(h6_m and show_h6?h6_m:na, style=plot.style_line, color= i_ma_select==6 ? ma_color : h6_a_m > 0 ? red : lime, title="M6")
// p_h7_m = plot(h7_m and show_h7?h7_m:na, style=plot.style_line, color= i_ma_select==7 ? ma_color : h7_a_m>0 ? orange : h7_a_m<0 and h7_m>h8_m ? red : green,title="H7")
// p_h8_m = plot(h8_m and show_h8?h8_m:na, style=plot.style_line, color= i_ma_select==8 ? ma_color : h8_a_m>0 ? red : lime,title="M8")
// p_h9_m = plot(h9_m and show_h9?h9_m:na, style=plot.style_line, color= i_ma_select==9 ? ma_color : h9_a_m>0 ? red : lime,title="M9")

// EMA
// p_m3_m = plot(m3_m and show_h5?m3_m:na, style=plot.style_line, color= m3_a_m<0 ? aqua : orange,title="M5")
// p_m5_m = plot(m5_m and show_h5?m5_m:na, style=plot.style_line, color= i_ma_select==5 ? ma_color2 : m5_a_m<1 and m5_a_m>-1 ? aqua : m5_a_m>0 ? orange : m5_a_m<0 and m5_m>m6_m ? red : green,title="M5")
// p_m6_m = plot(m6_m and show_h6?m6_m:na, style=plot.style_line, color= i_ma_select==6 ? ma_color2 : m6_a_m > 0 ? red : lime, title="M6")
// p_m9_m = plot(m9_m and show_h9?m9_m:na, style=plot.style_line, color= i_ma_select==6 ? ma_color2 : m9_a_m > 0 ? red : lime, title="M9")


// p_m2_m = plot(m2_m and show_h2?m2_m:na, style=plot.style_line, color= i_ma_select==2 ? ma_color : m2_a_m>0 ? orange : m2_a_m<0 and m2_m>h8_m ? red : green,title="M2")




// WMA 4 hours
// -----------------------------------------------------
// var w1_m = 0.0
// var w1_a_m = 0.0
//i_wma_time = input.timeframe(title='WMA Timeframe', defval='239',group=g_ma)

//w1_m = request.security(syminfo.tickerid, i_wma_time, ma_graph(10,"wma")[1], lookahead=barmerge.lookahead_on)
//w1_a_m = request.security(syminfo.tickerid, i_wma_time, angle(w1_m,angle_amount)[1], lookahead=barmerge.lookahead_on)
// w1_m := request.security(syminfo.tickerid, i_wma_time, newbar(i_wma_time) ? ma_graph(10,"wma") : w1_m[1] )
// w1_a_m := request.security(syminfo.tickerid, i_wma_time, newbar(i_wma_time) ? angle(w1_m,angle_amount) : w1_a_m[1] )  
//plot(w1_m, title="W1-4hr", color=w1_a_m>0 ? aqua : orange)

f_ma_cross(m1_t,m2_t) =>
    dir = m1_t>m2_t ? 1 : -1
f_ma_stages(m1_t, m2_t, m3_t, a1, a2, a3) =>

    dir = f_ma_cross(m1_t, m2_t)
    ma_conv = get_pip_distance(m1_t ,m2_t) <= conv_amount ? 1 : 0
    stage = 0

    if dir == 1
        stage :=
         a1>0 and a2>0 and ma_conv==1 ? 1
         : a1>0 and a2>0 and ma_conv==0 and a3>0? 2
         : a1>0 and a2>0 and ma_conv==0 and a3<0 ? 3
         : a1<0 and a2>0 and ma_conv==0 and a3<0 ? 4
         : a1<0 and a2>0 and ma_conv==1 ? 5
         : a1<0 and a2<0 and ma_conv==1 ? 6 : 0

    if dir == -1
        stage := 
         a1<0 and a2>0 and a3<0 ? 1
         : a1<0 and a2<0 and a3<0 ? 2
         : a1<0 and a2<0 and a3>0 ? 3
         : a1>-1 and a1<1 ? 4
         : a1>0 and a2<0 and m3_t>m1_t ? 5
         : a1>0 and a2<0 and a3<0 ? 6
         : a1>0 and a2>0 ? 7 : 0

    [dir,stage]

[hma_dir,hma_stage] = f_ma_stages(h5_m, h6_m, h3_m, h5_a_m, h6_a_m, h3_a_m)
//[hma_dir,hma_stage] = f_ma_stages(h7_m, h8_m, h3_m, h7_a_m, h8_a_m, h3_a_m)
plot(hma_stage,'HMA Stage', style=plot.style_circles, color=hma_dir==1 ? red : green)


// === RSI Wicks ===
// ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ //
g_rsi = 'RSI Wicks -------------------------------------------------------------'
inl_rsi1 = '1'
inl_rsi2 = '2'
i_show_rsi  = input.bool(false, title='Show RSI', inline=inl_rsi1, group=g_rsi)
rsi_strong  = input.bool(title='Strongest', defval=false, inline=inl_rsi1, group=g_rsi)
rsi_len     = input.int(12, minval=1, title='Length', inline=inl_rsi2, group=g_rsi) // 12
rsi_len_m   = input.int(3, minval=1, title='Length', inline=inl_rsi2, group=g_rsi) // 12 20 14
rsi_pos     = 'tb' //input.string(title='Position', defval='tb', options=['tb', 't', 'b'], inline=inl_rsi2, group=g_rsi)
i_use_rsi_candles = input.bool(false, title='Use RSI Candles', inline=inl_rsi1, group=g_rsi)


g_rsi_time = ''
inl_rsi3 = '3'
use_rsi_curr = input.bool(title='Show Current', defval=true, inline=inl_rsi3, group=g_rsi_time)
use_rsi_multi = input.bool(title='Show Multi', defval=false, inline=inl_rsi3, group=g_rsi_time)
i_rsi_time = input.timeframe(title='Timeframe', defval='120', group=g_rsi_time) 

wicks = true // input(true, title="Wicks based on stand-alone RSI")
rsi_target_up = 70  // input(70, minval=1, title="RSI Up")
rsi_target_down = 30  // input(30, minval=1, title="RSI Down")
src_close = close
src_open = open
src_high = high
src_low = low
rsi_l_up = 70
rsi_l_dn = 30

//trend4==1 and trend1==1 and close<up1 and RSI_low<rsi_target_down

rsi_wicks(rsi_len) =>
    norm_close = math.avg(src_close, src_close[1])
    gain_loss_close = ta.change(src_close) / norm_close
    RSI_close = 50 + 50 * ta.rma(gain_loss_close, rsi_len) / ta.rma(math.abs(gain_loss_close), rsi_len)

    norm_open = if wicks == true
        math.avg(src_open, src_open[1])
    else
        math.avg(src_close, src_close[1])
    gain_loss_open = ta.change(src_open) / norm_open
    RSI_open = 50 + 50 * ta.rma(gain_loss_open, rsi_len) / ta.rma(math.abs(gain_loss_open), rsi_len)

    norm_high = if wicks == true
        math.avg(src_high, src_high[1])
    else
        math.avg(src_close, src_close[1])
    gain_loss_high = ta.change(src_high) / norm_high
    RSI_high = 50 + 50 * ta.rma(gain_loss_high, rsi_len) / ta.rma(math.abs(gain_loss_high), rsi_len)

    norm_low = if wicks == true
        math.avg(src_low, src_low[1])
    else
        math.avg(src_close, src_close[1])
    gain_loss_low = ta.change(src_low) / norm_low
    RSI_low = 50 + 50 * ta.rma(gain_loss_low, rsi_len) / ta.rma(math.abs(gain_loss_low), rsi_len)

    [RSI_open, RSI_close, RSI_high, RSI_low]

[RSI_open, RSI_close, RSI_high, RSI_low] = rsi_wicks(rsi_len)
[RSI_open_2, RSI_close_2, RSI_high_2, RSI_low_2] = rsi_wicks(rsi_len_m)
[close_m, open_m, RSI_open_m, RSI_high_m,RSI_low_m,RSI_close_m] = request.security(syminfo.tickerid, i_rsi_time, [close, open, RSI_open_2,RSI_high_2,RSI_low_2,RSI_close_2])
//open_m = request.security(syminfo.tickerid, i_rsi_time, open)
// RSI_open_m = request.security(syminfo.tickerid, i_rsi_time, RSI_open_2)
// RSI_high_m = request.security(syminfo.tickerid, i_rsi_time, RSI_high_2)
// RSI_low_m = request.security(syminfo.tickerid, i_rsi_time, RSI_low_2)
// RSI_close_m = request.security(syminfo.tickerid, i_rsi_time, RSI_close_2)
//[close_m, open_m,RSI_open_m, RSI_high_m, RSI_low_m, RSI_close_m] = request.security(syminfo.tickerid, i_rsi_time, [close, open, RSI_open,RSI_high,RSI_low,RSI_close] )

// plot(RSI_high_m, title='RSI High', color=color.new(blue,100))
// plot(RSI_open_m, title='RSI Open', color=color.new(blue,100))
// plot(RSI_close_m, title='RSI close', color=color.new(blue,100))
// plot(RSI_low_m, title='RSI Low', color=color.new(blue,100))

// Why are normal and multi different? You are using 'close_m > open_m' instead of close > open
// Sell
up_cond_m = close_m > open_m
plotshape(i_show_rsi and use_rsi_multi and RSI_high_m > rsi_target_up and RSI_close_m < rsi_target_up ? 1 : na, title='Up Weak', color=color.new(yellow, 0), style=shape.circle, location=location.top)
plotshape(i_show_rsi and use_rsi_multi and RSI_close_m > rsi_target_up ? 1 : na, title='Up Mid', color=color.new(orange, 0), style=shape.circle, location=location.top)
//plot(perc_change(RSI_close_m)*.01, title="Percent Change", color=color.new(blue,100),style=plot.style_circles)
//plotshape(i_show_rsi and use_rsi_multi and close>open and RSI_close_m > rsi_target_up ? 1 : na, title='Up High', color=color.new(#ff0000, 0), style=shape.circle, location=location.top)
// Buy
down_cond_m = close_m < open_m
plotshape(i_show_rsi and use_rsi_multi and RSI_low_m < rsi_target_down and RSI_close_m>rsi_target_down ? 1 : na, title='Low Weak', color=color.new(violet, 0), style=shape.circle, location=location.bottom)
plotshape(i_show_rsi and use_rsi_multi and RSI_close_m < rsi_target_down ? 1 : na, title='Low Mid', color=color.new(blue, 0), style=shape.circle, location=location.bottom)

//plotshape(i_show_rsi and use_rsi_multi and close<open and RSI_close_m < rsi_target_down ? 1 : na, title='Up High Multi', color=green, style=shape.circle, location=location.bottom)

ash_group = 'ASH -------------------------------------------------------------'
ash_time  = input.timeframe(title='Timeframe', defval='720', group=ash_group) // 12 hours
ash_len = input.int(9,title="Period of Evaluation", group=ash_group)
ash_smooth = input.int(3,title="Period of Smoothing", group=ash_group)
ash_show_histo = input(false, title='Show Histogam')

//----
ash_src =  close // input(close,title="Source")
Mode = input.string(title="Indicator Method", defval="STOCHASTIC", options=["RSI", "STOCHASTIC","ADX"], group=ash_group) // RSI
ma_type = input.string(title="MA", defval="ALMA", options=["ALMA", "EMA", "WMA", "SMA", "SMMA", "HMA"], group=ash_group) //WMA 
alma_offset  = input.float(0.85, title="* Arnaud Legoux (ALMA) Only - Offset Value", minval=0, step=0.01, group=ash_group)
alma_sigma   = input.float(6.0, title="* Arnaud Legoux (ALMA) Only - Sigma Value", minval=0, group=ash_group)

f_ash_ma(type, src, len) =>
    float result = 0
    if type=="SMA" // Simple
        result := ta.sma(src, len)
    if type=="EMA" // Exponential
        result := ta.ema(src, len)
    if type=="WMA" // Weighted
        result := ta.wma(src, len)
    if type=="SMMA" // Smoothed
        w = ta.wma(src, len)
        result := na(w[1]) ? ta.sma(src, len) : (w[1] * (len - 1) + src) / len
    if type=="HMA" // Hull
        result := ta.wma(2 * ta.wma(src, len / 2) - ta.wma(src, len), math.round(math.sqrt(len)))
    if type=="ALMA" // Arnaud Legoux
        result := ta.alma(src, len, alma_offset, alma_sigma)
    result


//----
f_ash() =>

    ash_price = ash_src
    ash_price1 = f_ash_ma("SMA",ash_price,1)
    ash_price2 = f_ash_ma("SMA",ash_price[1],1)

    //RSI
    ash_bulls0 = 0.5*(math.abs(ash_price1-ash_price2)+(ash_price1-ash_price2))
    ash_bears0 = 0.5*(math.abs(ash_price1-ash_price2)-(ash_price1-ash_price2))

    //STOCHASTIC
    ash_bulls1 = ash_price1 - ta.lowest(ash_price1,ash_len)
    ash_bears1 = ta.highest(ash_price1,ash_len) - ash_price1

    //ADX
    ash_bulls2 = 0.5*(math.abs(high-high[1])+(high-high[1]))
    ash_bears2 = 0.5*(math.abs(low[1]-low)+(low[1]-low))

    //
    Bulls = Mode == "RSI" ? ash_bulls0 : Mode == "STOCHASTIC" ? ash_bulls1 : ash_bulls2
    Bears = Mode == "RSI" ? ash_bears0 : Mode == "STOCHASTIC" ? ash_bears1 : ash_bears2
    AvgBulls=f_ash_ma(ma_type,Bulls,ash_len)     
    AvgBears=f_ash_ma(ma_type,Bears,ash_len)

    //----
    SmthBulls=f_ash_ma(ma_type,AvgBulls,ash_smooth)  
    SmthBears=f_ash_ma(ma_type,AvgBears,ash_smooth)

    difference = math.abs(SmthBulls - SmthBears)

    bull_color = (SmthBulls<SmthBulls[1])?orange:red
    bear_color = (SmthBears<SmthBears[1])?lime:green

    [SmthBulls, SmthBears, bull_color,bear_color]

[SmthBulls, SmthBears, bull_color, bear_color] = request.security(syminfo.tickerid, ash_time, f_ash(), lookahead=barmerge.lookahead_on )

if newbar(ash_time) == 0
    SmthBulls := SmthBulls[1]
    SmthBears := SmthBears[1]
    bull_color := bull_color[1]
    bear_color := bear_color[1]


// CANDLES MULTI-TIMEFRAME
// -----------------------------------------------------
g_candles   = 'Candles -----------------------------------------------------------'
inl_c1      = 'inl_c1'
i_cand      = input.bool(true, title="Show Candles", group=g_candles, inline=inl_c1)
i_c_heik1   = input.bool(false,title='Heikin Ashi', group=g_candles, inline=inl_c1)
i_c_time    = input.timeframe('60', "Resolution", group=g_candles) // 1 hour
i_c_trans1  = input.int(30, title='Transp', group=g_candles)
i_s_c_attr  = input.bool(false, title='Candle Attributes', group=g_candles)

g_c2 = 'Candles 2 -----------------------------------'
inl_c2     = 'inl_c2'
i_cand2    = input.bool(true, title="Show Candles 2", group=g_c2, inline=inl_c2)
i_c_heik2  = input.bool(false,title='Heikin Ashi', group=g_c2, inline=inl_c2)
i_c_time2  = input.timeframe('240', "Resolution", group=g_c2) // 4 hour
i_c_trans2 = input.int(65, title='Transp', group=g_c2)
i_s_c_attr2= input.bool(false, title='Candle Attributes', group=g_c2)

g_c3 = 'Candles 3 -----------------------------------'
inl_c3     = 'inl_c3'
i_cand3    = input.bool(true, title="Show Candles 3", group=g_c3, inline=inl_c3)
i_c_heik3  = input.bool(false,title='Heikin Ashi', group=g_c3, inline=inl_c3)
i_c_time3  = input.timeframe('D', "Resolution", group=g_c3) // Day, 12 hour
i_c_trans3 = input.int(90, title='Transp', group=g_c3)
i_s_c_attr3= input.bool(false, title='Candle Attributes', group=g_c3)
i_s_c_label3= input.bool(false, title='Candle Labels', group=g_c3)

g_c4 = 'Additional -----------------------------------'
i_candle_mid = input.bool(false, title='Show Candle Midline', group=g_c4)
i_candle_type = input.bool(false, title='Show Candle Types', group=g_c4)


i_c_lookback = 1 //input.int(1, title='Lookback', group=g_candles)
i_lookahead = true //input.bool(true,title='Use Lookahead', group=g_candles)

// Candle Ratios
f_candle_ratios(c_open,c_high,c_low,c_close) =>
    C_dir = 0.
    C_hratio = 0.
    C_lratio = 0.
    C_color = red

    C_hl    = c_high - c_low
    C_body  = math.abs( (c_close - c_open) / C_hl )
    C_bratio = C_body / C_hl

    if c_close>c_open 
        C_hratio :=  (c_high - c_close) / C_hl
        C_lratio :=  (c_open - c_low) / C_hl
        C_color  := green
        C_dir    := 1
    else 
        C_hratio := (c_high - c_open) / C_hl
        C_lratio := (c_close - c_low) / C_hl
        C_color  := red
        C_dir    := 0

    [C_hl,C_hratio,C_lratio,C_body,C_color]

// Breaking High and Lows
f_cand_breaking(o,h,l,c) =>
    var hi = false
    var lo = false
    var dist = 0.
    var type = 0

    hi := high>h ? true : false
    lo := low<l ? true : false
    dist := get_pip_distance(c,o)
    type := c>o ? 1 : -1

    [hi,lo,dist,type]

// Candle Types - Doji ect..
f_candle_type(co, ch, cl, cc, dist, c_type, show) =>

    if ta.change(cc) and i_candle_type and show

        short_sm = dist<4 and c_type == 0 ? 1 : 0
        long_sm  = dist<4 and c_type == 1 ? 1 : 0
        if short_sm
            short_sm_txt = "Doji \n Short"
            info1 = label.new(x=time,y=ch,xloc=xloc.bar_time, text=short_sm_txt, textcolor=#ffffff,color =red, style=label.style_label_down)
        if long_sm
            info2 = label.new(x=time,y=cl,xloc=xloc.bar_time, text="Doji \n Long", textcolor=#ffffff,color =green, style=label.style_label_up)




// CANDLES ATTRIBUTES 1
// -----------------------------------------------------
[co,ch,cl,cc] = request.security(syminfo.tickerid, i_c_time, 
 [ open[i_c_lookback], high[i_c_lookback], low[i_c_lookback], close[i_c_lookback] ],
 lookahead=barmerge.lookahead_on )

[co_h,ch_h,cl_h,cc_h] = request.security(ticker.heikinashi(syminfo.tickerid), i_c_time, 
 [ open[i_c_lookback], high[i_c_lookback], low[i_c_lookback], close[i_c_lookback] ] ,  
 lookahead=barmerge.lookahead_on)
cm = (cc + co) * 0.5

[C_hl,C_hratio,C_lratio,C_body,C_color] = f_candle_ratios(co,ch,cl,cc)

[c1_hi,c1_lo,c1_dist,c1_type] = f_cand_breaking(co, ch, cl, cc)
[c1h_hi,c1h_lo,c1h_dist,c1h_type] = f_cand_breaking(co_h, ch_h, cl_h, cc_h)

chl = get_pip_distance(ch,cl)
cco = get_pip_distance(cc,co)
c_type = i_c_heik1 == false and cc>co ? 1 : i_c_heik1 == true and cc_h>co_h ? 1 : 0
candle_color = c_type == 1 ? green : red
candle_dist = get_pip_distance(cc,co)



// PLOT CANDLE 1
// -----------------------------------------------------
// p_co  = plot(i_cand == false ? na : i_c_heik1 ? co_h : co, title='Candle Open',color=color.new(candle_color,i_c_trans1) )
// p_ch  = plot(i_cand == false ? na : i_c_heik1 ? ch_h : ch, title='Candle High',color=color.new(color.white,80) )
// p_cl  = plot(i_cand == false ? na : i_c_heik1 ? cl_h : cl, title='Candle Low',color=color.new(color.white,80) )
// p_cc  = plot(i_cand == false ? na : i_c_heik1 ? cc_h : cc, title='Candle Close',color=color.new(candle_color,i_c_trans1) )
// fill(p_cc, p_co, title='Fill Candle 1',color=i_cand ? color.new(candle_color,i_c_trans1) : na )

// Candle Ratios
// plot(i_show_c_attr ? C_hl : na, title='High Low Ratio', color= color.new(C_color,100) )
// plot(i_show_c_attr ? C_hratio : na, title='High Ratio', color= color.new(C_color,100) )
// plot(i_show_c_attr ? C_lratio : na, title='Low Ratio', color= color.new(C_color,100) )
// plot(i_show_c_attr ? C_body : na, title='Body Ratio', color= color.new(C_color,100) )


// CANDLES ATTRIBUTES 2
// -----------------------------------------------------
[co2,ch2,cl2,cc2] = request.security(syminfo.tickerid, i_c_time2, 
 [ open[i_c_lookback], high[i_c_lookback], low[i_c_lookback], close[i_c_lookback] ], 
 lookahead=barmerge.lookahead_on )

[co2_h,ch2_h,cl2_h,cc2_h] = request.security(ticker.heikinashi(syminfo.tickerid), i_c_time2, 
 [ open[i_c_lookback], high[i_c_lookback], low[i_c_lookback], close[i_c_lookback] ] ,  
 lookahead=barmerge.lookahead_on)

cm2   = (cc2 + co2) * 0.5
chl2 = get_pip_distance(ch2,cl2)
cco2 = get_pip_distance(cc2,co2)

c_type2 = i_c_heik2 == false and cc2>co2 ? 1 : i_c_heik2 == true and cc2_h>co2_h ? 1 : 0
candle_color2 = c_type2 == 1 ? green : red
candle_dist2 = get_pip_distance(cc2,co2)

f_candle_type(co2, ch2, cl2, cc2, candle_dist2, c_type2, i_cand2)


// PLOT CANDLE 2
// -----------------------------------------------------
// p_co2  = plot(i_cand2 == false ? na : i_c_heik2 ? co2_h : co2, title='Candle Open',color=color.new(candle_color2,i_c_trans2) )
// p_ch2  = plot(i_cand2 == false ? na : i_c_heik2 ? ch2_h : ch2, title='Candle High',color=color.new(color.white,80))
// p_cl2  = plot(i_cand2 == false ? na : i_c_heik2 ? cl2_h : cl2, title='Candle Low',color=color.new(color.white,80))
// p_cc2  = plot(i_cand2 == false ? na : i_c_heik2 ? cc2_h : cc2, title='Candle Close',color=color.new(candle_color2,i_c_trans2) )
// fill(p_cc2, p_co2, title='Fill Candle 2',color=i_cand2 ? color.new(candle_color2,i_c_trans2) : na )



// CANDLES ATTRIBUTES 3
// -----------------------------------------------------
[co3,ch3,cl3,cc3] = request.security(syminfo.tickerid, i_c_time3, 
 [ open[i_c_lookback], high[i_c_lookback], low[i_c_lookback], close[i_c_lookback] ], 
 lookahead=barmerge.lookahead_on )

[co3_h,ch3_h,cl3_h,cc3_h] = request.security(ticker.heikinashi(syminfo.tickerid), i_c_time3, 
 [ open[i_c_lookback], high[i_c_lookback], low[i_c_lookback], close[i_c_lookback] ] ,  
 lookahead=barmerge.lookahead_on)

cm3   = (cc3 + co3) * 0.5
chl3 = get_pip_distance(ch3,cl3)
cco3 = get_pip_distance(cc3,co3)

c3_type = i_c_heik3 == false and cc3>co3 ? 1 : i_c_heik3 == true and cc3_h>co3_h ? 1 : -1
candle_color3 = c3_type == 1 ? green : red
candle_dist3 = get_pip_distance(cc3,co3)

f_candle_type(co3, ch3, cl3, cc3, candle_dist3, c3_type, i_cand3)

// PLOT CANDLE 3
// -----------------------------------------------------
// p_co3  = plot(i_cand3 == false ? na : i_c_heik3 ? co3_h : co3, title='Candle Open',color=color.new(candle_color3,i_c_trans3) )
// p_ch3  = plot(i_cand3 == false ? na : i_c_heik3 ? ch3_h : ch3, title='Candle High',color=color.new(color.white,80))
// p_cl3  = plot(i_cand3 == false ? na : i_c_heik3 ? cl3_h : cl3, title='Candle Low',color=color.new(color.white,80))
// p_cc3  = plot(i_cand3 == false ? na : i_c_heik3 ? cc3_h : cc3, title='Candle Close',color=color.new(candle_color3,i_c_trans3) )
// fill(p_cc3, p_co3, title='Fill Candle 3',color=i_cand3 ? color.new(candle_color3,i_c_trans3) : na )




//
// ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ //
// HIGHEST LOWEST
// ------------------------------------------------------------------------------------------------------------------
g_hl = 'HIGHEST LOWEST -------------------------------------------------------------'
hl_res = input.timeframe(title='Resolution', defval='30', group=g_hl)
hl_res2 = input.timeframe(title='Resolution 2', defval='240', group=g_hl)
i_use_hl1 = input.bool(false,title='Display HL1', group=g_hl)
i_use_hl2 = input.bool(false,title='Display HL2', group=g_hl)
i_show_fill = input.bool(false, title='Fill Boxes', group=g_hl)
i_show_bars = input.bool(false,title='Show Bars', group=g_hl)
i_show_labels = input.bool(false,title='Show Labels', group=g_hl)
hl_len1 = input(title='Length 1', defval=20, group=g_hl)  // 20
hl_len2 = input(title='Length 2', defval=15, group=g_hl)  // 20
src_h = close // input(title='Source', defval=close, group=g_hl)
src_l = close // input(title='Source', defval=close, group=g_hl)
use_diff = input(title='Filter Diff', defval=true, group=g_hl)
diff_range = input(10, title='FDiff Range', group=g_hl)

// H1
// -----------------------------------------------------
var float hh = 0.0
var float ll = 0.0
hh := timeframe.change(hl_res) ? request.security(syminfo.tickerid, hl_res, ta.highest(src_h, hl_len1)) : hh[1]
ll := timeframe.change(hl_res) ? request.security(syminfo.tickerid, hl_res, ta.lowest(src_l, hl_len1)) : ll[1]
hl_mid = (hh + ll) * 0.5
hh_a = angle(hh, hl_len1)
ll_a = angle(ll, hl_len1)
hl_dist = get_pip_distance(hh,ll)
hl_diff = hh - ll
hh_bars = ta.barssince(ta.change(hh))
ll_bars = ta.barssince(ta.change(ll))

// H2
// -----------------------------------------------------
var float hh2 = 0.0
var float ll2 = 0.0
hh2 := timeframe.change(hl_res2) ? request.security(syminfo.tickerid, hl_res2, ta.highest(src_h, hl_len2) ) : hh2[1]
ll2 := timeframe.change(hl_res2) ? request.security(syminfo.tickerid, hl_res2, ta.lowest(src_l, hl_len2) ) : ll2[1]
hl2_mid = (hh2 + ll2) * 0.5
hh2_a = angle(hh2, hl_len2)
ll2_a = angle(ll2, hl_len2)
hl2_dist = math.round(get_pip_distance(hh2,ll2) )
diff2 = hh2 - ll2
hh2_bars = ta.barssince(ta.change(hh2))
ll2_bars = ta.barssince(ta.change(ll2))
hh2_p1 = plot(i_use_hl2 ? hh2 : na, title='HH2', linewidth=2, color=hh2_a == 0 ? #ff00ff : #ff0000)
hh2_p2 = plot(i_use_hl2 ? ll2 : na, title='LL2', linewidth=2, color=ll2_a == 0 ? #55d51a : #00FFFF)

//hh2_p1 = plot(i_use_hl2 ? hh2 : na, title='HH2', linewidth=2, color=hh2_a == 0 ? #ff00ff : #ff0000)
//hh2_p2 = plot(i_use_hl2 ? ll2 : na, title='LL2', linewidth=2, color=ll2_a == 0 ? #55d51a : #00FFFF)





//
// ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ //
// PREDICTIVE RANGES
// ------------------------------------------------------------------------------------------------------------------
g_PR = 'PREDICTIVE RANGES ------------------------------------------------------'
PR_s1          = input.bool(true, title='Show Channels',group=g_PR)
PR_tf1         = input.timeframe('5', 'Timeframe',group=g_PR)
PR_len1        = input.int(200, 'Length', minval = 2,group=g_PR)
PR_mult1       = input.float(6., 'Factor', minval = 0, step = .5,group=g_PR) // 6.0 , 10.0
PR_s_cand1     = input.bool(false, title='Show Candles',group=g_PR)
PR_trans1      = input.int(95, title='Trans',group=g_PR)

g_PR2 = 'PR 2 ------------------------------------------------------'
PR_s2          = input.bool(true, title='Show Channels',group=g_PR2)
PR_tf2         = input.timeframe('15', 'Timeframe',group=g_PR2)
PR_len2        = input.int(200, 'Length', minval = 2,group=g_PR2)
PR_mult2       = input.float(6., 'Factor', minval = 0, step = .5,group=g_PR2) // 6.0 , 10.0
PR_s_cand2     = input.bool(false, title='Show Candles',group=g_PR2)
PR_trans2      = input.int(95, title='Trans',group=g_PR2)

g_PR3 = 'PR 3 ------------------------------------------------------'
PR_s3          = input.bool(true, title='Show Channels',group=g_PR3)
PR_tf3         = input.timeframe('', 'Timeframe',group=g_PR3)
PR_len3        = input.int(200, 'Length', minval = 2,group=g_PR3)
PR_mult3       = input.float(6., 'Factor', minval = 0, step = .5,group=g_PR3) // 6.0 , 10.0
PR_s_cand3     = input.bool(false, title='Show Candles',group=g_PR3)
PR_trans3      = input.int(95, title='Trans',group=g_PR3)

//-----------------------------------------------------------------------------}
//Function
//-----------------------------------------------------------------------------{
pred_ranges(PR_len, PR_mult)=>
    var R2 = 0.
    var R1 = 0.
    var S1 = 0.
    var S2 = 0.
    var avg = close
    var hold_atr = 0.

    atr = nz(ta.atr(PR_len)) * PR_mult
        
    avg := close - avg > atr ? avg + atr : 
      avg - close > atr ? avg - atr : 
      avg
        
    hold_atr := avg != avg[1] ? atr / 2 : hold_atr

    R2 := avg + hold_atr * 2
    R1 := avg + hold_atr
    S1 := avg - hold_atr
    S2 := avg - hold_atr * 2

    //l38  = (R2-S2) * 0.382 + S2
    //l62  = (R2-S2) * 0.618 + S2
        
    [R2, 
     R1, 
     avg, 
     S1, 
     S2]

//-----------------------------------------------------------------------------}
//PR 1
//-----------------------------------------------------------------------------{
[prR2_1
  , prR1_1
  , pravg_1
  , prS1_1
  , prS2_1] = request.security(syminfo.tickerid, PR_tf1, pred_ranges(PR_len1, PR_mult1))

// Color
PR_col1 = 
 close>prR1_1 ? color.red
 : low>pravg_1 and close<prR1_1 ? color.yellow 
 : high<pravg_1 and close>prS1_1 ? color.blue 
 : high<prS1_1 ? color.green
 : na

// Bars Since Change
pr_bars1 = ta.barssince(ta.change(prR2_1))

//-----------------------------------------------------------------------------}
//PR 2
//-----------------------------------------------------------------------------{
[prR2_2
  , prR1_2
  , pravg_2
  , prS1_2
  , prS2_2] = request.security(syminfo.tickerid, PR_tf2, pred_ranges(PR_len2, PR_mult2))

// Color
PR_col2 = 
 close>prR1_2 ? color.red
 : low>pravg_2 and close<prR1_2 ? color.yellow 
 : high<pravg_2 and close>prS1_2 ? color.blue 
 : high<prS1_2 ? color.green
 : na

// Bars Since Change
pr_bars2 = ta.barssince(ta.change(prR2_2))
pr_onchange = ta.change(prR2_2)

var float PR2_state = 0
PR2_state := ta.change(prR2_2) and prR2_2 < prR2_2[1] ? -1 : ta.change(prR2_2) and prR2_2 > prR2_2[1] ? 1 : PR2_state

// Plots
//plotshape(pr_onchange and prR2_2 < prR2_2[1], title='PR Change Down', color=red, style=shape.cross, location=location.top)
//plotshape(pr_onchange and prR2_2 > prR2_2[1], title='PR Change Up', color=green, style=shape.cross, location=location.bottom)



//-----------------------------------------------------------------------------}
//PR 3
//-----------------------------------------------------------------------------{
[prR2_3
  , prR1_3
  , pravg_3
  , prS1_3
  , prS2_3] = request.security(syminfo.tickerid, PR_tf3, pred_ranges(PR_len3, PR_mult3))

// Previous
f_prev(curr) =>
  dir = curr == curr[1] ? 0 : curr > curr[1] ? 1 : -1


// var prev_box = 0.
// prev_box := ta.change(prR2_3) ? f_prev(prR2_3) : prev_box[1]
// plot(prev_box, 'Prev Box', color=prev_box==1?#089981:#f23645)

// Color
PR_col3 = 
 close>prR1_3 ? color.red
 : low>pravg_3 and close<prR1_3 ? color.yellow 
 : high<pravg_3 and close>prS1_3 ? color.blue 
 : high<prS1_3 ? color.green
 : na

// Bars Since Change
pr_bars3 = ta.barssince(ta.change(prR2_3))

//-----------------------------------------------------------------------------}




//
// ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ //
// Predictive Channels
// ------------------------------------------------------------------------------------------------------------------
g_PC = 'Predictive Channels -------------------------------------------------------------'
PC_mult  = input.float(13, 'Factor', minval = 0, group=g_PC) // 5
PC_slope = input.float(50, minval = 0, group=g_PC) * PC_mult
i_PC_show_plot = input.bool(true,title = 'Show Plots', group=g_PC)
pc_i_show_info = input.bool(true,title = 'Show Info', group=g_PC)
i_PC_show_ma = input.bool(true,title = 'Show MA', group=g_PC)

var upper_bound = 0.0
var pc_direction = 0
var pc_bars = 0

//-----------------------------------------------------------------------------}
//Calculation
//-----------------------------------------------------------------------------{
var PC_avg = close
var PC_os = 1
var PC_hold_atr = 0.0

PC_atr = nz(ta.atr(200)) * PC_mult

PC_avg := math.abs(close - PC_avg) > PC_atr ? close 
  : PC_avg + PC_os * PC_hold_atr / PC_slope

PC_hold_atr := PC_avg == close ? PC_atr / 2 : PC_hold_atr
PC_os := PC_avg > PC_avg[1] ? 1 : PC_avg < PC_avg[1] ? -1 : PC_os

PC_R2 = PC_avg + PC_hold_atr
PC_R1 = PC_avg + PC_hold_atr/2
PC_S1 = PC_avg - PC_hold_atr/2
PC_S2 = PC_avg - PC_hold_atr


PC_get_angle() =>
    PC_R2>PC_R2[1] ? 1 : -1

PC_get_level(obj) =>
  
    var int level = 0
    // Above R2
    if obj>PC_R2 
        level := 6
    // Above R1 
    if obj<PC_R2 and obj>PC_R1
        level := 5
    // Above Average 
    if obj<PC_R1 and obj>PC_avg
        level := 4
    // Below Average
    if obj<PC_avg and obj>PC_S1
        level := 3
    // Below S1
    if obj<PC_S1 and obj>PC_S2
        level := 2
    // Below S2
    if obj<PC_S2
        level := 1

    level

PC_is_between(obj) =>
    between = PC_R2>obj and PC_S2<obj ? 1 : 0

PC_barssince()=>
    bars = ta.barssince( get_pip_distance(PC_R2,PC_R2[1])>5 )

if get_pip_distance(PC_R2,PC_R2[1])>5
    upper_bound := PC_R2
    pc_direction := upper_bound>upper_bound[1] ? 1 : -1

pc_bars := PC_barssince()





//-----------------------------------------------------------------------------}
//Plot
//-----------------------------------------------------------------------------{
// plot(pc_i_show_info ? pc_bars : na, title='Bars Since', color=color.new(white,100))
// plot(pc_i_show_info ? pc_direction : na, title='direction', color=color.new(white,100))
// plot(pc_i_show_info ? PC_is_between(m2) : na, title='is between', color=color.new(white,100))
// plot(pc_i_show_info ? PC_get_angle() : na, title='Get Angle', color=color.new(white,100))

// plot_0 = plot(i_PC_show_plot ? close : na, color = na, display = display.none, editable = false)

// //SR PLots
// plot(i_PC_show_plot ? PC_R2 : na, 'Upper Resistance', close == PC_avg ? na : red)
// plot(i_PC_show_plot ? PC_R1 : na, 'Lower Resistance', close == PC_avg ? na : color.new(red, 50))
// plot_avg = plot(i_PC_show_plot ? PC_avg : na, 'Average', close == PC_avg ? na : gray)
// plot(i_PC_show_plot ? PC_S1 : na, 'Upper Support', close == PC_avg ? na : color.new(green, 50))
// plot(i_PC_show_plot ? PC_S2 : na, 'Lower Support', close == PC_avg ? na : green)

// //Fill
// topcss = close > PC_avg ? color.new(green, 80) : color.new(chart.bg_color, 100)
// btmcss = close < PC_avg ? color.new(red, 80) : color.new(chart.bg_color, 100)
// fill(plot_0, plot_avg, PC_R2, PC_S2, topcss, btmcss)







//
// ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ //
// Bollinger Bands
// ------------------------------------------------------------------------------------------------------------------
g_bb = 'BB Bands ----------------------------------------------------'
bb_res = input.timeframe(title='Resolution', defval='120', group=g_bb) // 60
i_show_bb_bands = input.bool(false,title='Show BB Bands', group=g_bb)
i_showbb_fast = input.bool(false, title="Show Fast")
i_showbb = input.bool(false, title="Show BB")
i_bb_len_fast = input(50, title='BB Len Fast', group=g_bb) 
i_bb_len = input(10, title='BB Len', group=g_bb)
i_show_back = input.bool(false, title='Show Background')
sqz_length = 80
// Fast
bb_basis_f = ta.sma(close, i_bb_len_fast)
dev_f = 2 * ta.stdev(close, i_bb_len_fast)
bb_upper_f = bb_basis_f + dev_f
bb_lower_f = bb_basis_f - dev_f
bb_spread_f = bb_upper_f - bb_lower_f
bb_angle_f = angle(bb_basis_f,1)

f_bb_slow() =>

    bb_basis = ta.sma(close, i_bb_len)
    dev = 2 * ta.stdev(close, i_bb_len)
    bb_upper = bb_basis + dev
    bb_lower = bb_basis - dev
    bb_spread = bb_upper - bb_lower
    bb_angle = angle(bb_basis,3)
    bb_avgspread = ta.sma(bb_spread, sqz_length)

    [bb_basis,bb_upper,bb_lower,bb_spread,bb_angle,bb_avgspread]

[bb_basis,bb_upper,bb_lower,bb_spread,bb_angle,bb_avgspread] = request.security(syminfo.tickerid, bb_res, f_bb_slow() )

bb_squeeze = 0.00
bb_squeeze := bb_spread / bb_avgspread * 100
// Zones
bb_zone = bb_squeeze < 53 ? 0 : 
 bb_squeeze < sqz_length ? 1 : 
 bb_squeeze < 120 ? 2 :
 bb_squeeze < 160 ? 3 :
 bb_squeeze > 160 ? 4 :
 bb_squeeze > 200 ? 5 : na
sqz_color = bb_zone == 0 ? #0045b3 :
 bb_zone == 1 ? #ff0062 : 
 bb_zone == 2 ?  gray : 
 bb_zone == 3 ?  #00c3ff : 
 bb_zone == 4 ? white:
 bb_zone == 5 ? yellow: na

bb_zones_color =  sqz_color

plot(i_show_bb_bands and i_showbb_fast ? bb_basis_f : na,title="Basis")
plot(i_show_bb_bands and i_showbb_fast ? bb_upper_f : na,title="bb_upper")
plot(i_show_bb_bands and i_showbb_fast ? bb_lower_f : na,title="bb_lower")

plot(i_show_bb_bands and i_showbb ? bb_upper : na,title="bb_upper")
plot(i_show_bb_bands and i_showbb ? bb_lower : na,title="bb_lower")
plot(i_show_bb_bands and i_showbb ? bb_basis : na,title="Basis", color=bb_zones_color)



//plot(i_show_bb_bands ? bb_angle : na,title="BB Angle 2", color=bb_angle>0?green:red)
bb_cond = h1 < bb_lower ? 1 : h1 > bb_upper ? -1 : na
//bgcolor(i_show_back and bb_cond == 1 ? color.new(aqua,80) : i_show_back and bb_cond == -1 ? color.new(orange,80) : na)
//barcolor(bb_cond == 1 ? aqua : bb_cond == -1 ? orange : na)





//
// ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ //
// SUPERTREND CHANNELS
// ------------------------------------------------------------------------------------------------------------------
g_stch = 'SUPERTREND CHANNELS -----------------------------'
i_stch_time = input.timeframe('30', "Resolution",group=g_stch) // Daily
length = input.int(18,group=g_stch) // 18, 14
mult   = input.int(5,group=g_stch)  //  5,  2
i_stch_trans = input.int(80, 'Transp',group=g_stch)
i_heikin = input.bool(false, title='Use Heikin Ashi',group=g_stch)


//------------------------------------------------------------------------------
f_st_channels()=>
    //timeframe.change(i_stch_time) ? 
    upper = 0.,lower = 0.,os = 0,max = 0.,min = 0.

    src = close
    atr = ta.atr(length)*mult
    up = hl2 + atr
    dn = hl2 - atr
    upper := src[1] < upper[1] ? math.min(up,upper[1]) : up
    lower := src[1] > lower[1] ? math.max(dn,lower[1]) : dn

    os := src > upper ? 1 : src < lower ? 0 : os[1]
    spt = os == 1 ? lower : upper

    max := ta.cross(src,spt) ? nz(math.max(max[1],src),src) : 
     os == 1 ? math.max(src,max[1]) : 
     math.min(spt,max[1])

    min := ta.cross(src,spt) ? nz(math.min(min[1],src),src) : 
     os == 0 ? math.min(src,min[1]) : 
     math.max(spt,min[1])

    avg = math.avg(max,min)
    avg_high = math.avg(max,avg)
    avg_low = math.avg(avg,min)

    [max, min, os, avg, avg_high, avg_low]

[st_max, st_min, st_os, st_avg, st_avg_high, st_avg_low] = request.security(syminfo.tickerid, i_stch_time, f_st_channels() )
[st_max_h, st_min_h, st_os_h, st_avg_h, st_avg_high_h, st_avg_low_h] = request.security(ticker.heikinashi(syminfo.tickerid), i_stch_time, f_st_channels() )

//------------------------------------------------------------------------------
var st_up_col  = color.new(#ff1100,i_stch_trans)
var st_avg_col = color.new(#ffffff , (i_stch_trans + 5) )
var st_dn_col = color.new(#0cb51a ,i_stch_trans)

stch_max = i_heikin ? st_max_h : st_max 
stch_min = i_heikin ? st_min_h : st_min   
stch_os  = i_heikin ? st_os_h : st_os 
stch_avg = i_heikin ? st_avg_h : st_avg
stch_avg_high = i_heikin ? st_avg_high_h : st_avg_high
stch_avg_low = i_heikin ? st_avg_low_h : st_avg_low  
stch_max_bar = ta.barssince(ta.change(stch_max)) 
stch_min_bar = ta.barssince(ta.change(stch_min)) 

// p_st_max = plot(stch_max,'Upper Channel',stch_max != stch_max[1] and stch_os == 1 ? na : st_up_col)
// p_st_avg = plot(stch_avg,'Average',st_avg_col)
// p_st_min = plot(stch_min,'Lower Channel',stch_min != stch_min[1] and stch_os == 0 ? na : st_dn_col)
// plot(stch_avg_high,'Avg High',st_up_col)
// plot(stch_avg_low,'Avg Low',st_dn_col)
// fill(p_st_max,p_st_avg,st_up_col,'Upper Area')
// fill(p_st_avg,p_st_min,st_dn_col,'Lower Area')



g_sar = 'Parabolic SAR -----------------------------------------------------------'
i_show_sar = input.bool(false, title='Show SAR', group=g_sar)
i_psar_timeframe = input.timeframe('60', "SAR Timeframe", group=g_sar)
i_psar_heikin = input.bool(false,title='Use Heikin Ashi Candles', group=g_sar)
psar_start = input.float(title='Start', defval=0.02, step=0.001, group=g_sar)
psar_inc = input.float(title='Increment', defval=0.02, step=0.001, group=g_sar)
psar_max = input.float(title='Max Value', defval=0.2, step=0.01, group=g_sar)
putlabel = input(title='Put Labels', defval=false, group=g_sar)
colup = input.color(title='Colors', defval=color.lime, inline='col', group=g_sar)
coldn = input.color(title='', defval=color.red, inline='col', group=g_sar)

psar_m  = 
 i_psar_heikin ? request.security(ticker.heikinashi(syminfo.tickerid), i_psar_timeframe, ta.sar(psar_start, psar_inc, psar_max) ) 
 : request.security(syminfo.tickerid, i_psar_timeframe, ta.sar(psar_start, psar_inc, psar_max) )

psar_close_m = i_psar_heikin ? request.security(ticker.heikinashi(syminfo.tickerid), i_psar_timeframe, close ) 
 : request.security(syminfo.tickerid, i_psar_timeframe, close )
psar_dir  = psar_m < psar_close_m ? 1 : -1

psar_dist = get_pip_distance(psar_m,psar_dir==1 ? close : open)
psarColor = psar_dir == 1 ? colup : coldn
plot(i_show_sar ? psar_m : na, title="PSAR", style=plot.style_circles, color=psarColor, linewidth=2)
// plot(psar_dist, title='SAR Distance', color=color.new(psarColor,100))
//plot(i_show_sar ? psar_m : na, title='Parabolic SAR', color=trend > 0 ? colup : coldn, linewidth=2, style=plot.style_circles)
// bs_sar = ta.barssince(ta.change(psar_dir))
// p_bs_sar = plot(bs_sar, 'Bars Since', color.new(psarColor,100) ) 







//
// ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ //
// TRADING
// ------------------------------------------------------------------------------------------------------------------

g_trading = 'Trading ----------------------------------------------------'
i_live_trading  = input.bool(false, title='Live Trading Only',group=g_trading)
isLive          = barstate.isrealtime
i_equity        = input.string("Equity", options=["Initial", "Equity"], title="Initial or Equity",group=g_trading)
i_long_trades   = input.bool(true, title='Long Trades',group=g_trading)
i_long_back_trades = input.bool(true, title='Long Backside Trades',group=g_trading)
i_short_trades  = input.bool(true, title='Short Trades',group=g_trading)
i_short_back_trades = input.bool(true, title='Short Backside Trades',group=g_trading)
i_use_pos       = input.bool(true,title="Use Percentage based Position Size",group=g_trading)
i_pctStop       = input(1.0, '% of Risk to Starting Equity Use to Size Positions',group=g_trading) / 100

Session(sess) => na(time("2",sess)) == false
i_show_tr = input.bool(false,title='Show Session')
i_show_nt = input.bool(true, title='Show No Trade')
i_session  = input.session(title="Session", defval="1400-1600")
i_no_trading = input.session(title="No Trading Hours", defval="1045-1300")
i_GMT = input.string(title='GMT', defval='GMT-10', options=['GMT-10', 'GMT-9', 'GMT-8', 'GMT-7', 'GMT-6', 'GMT-5', 'GMT-4', 'GMT-3', 'GMT-2', 'GMT-1', 'GMT-0', 'GMT+1', 'GMT+2', 'GMT+3', 'GMT+4', 'GMT+5', 'GMT+6', 'GMT+7', 'GMT+8', 'GMT+9', 'GMT+10', 'GMT+11', 'GMT+12', 'GMT+13'] )
// Set the start of day
start_time = Session(i_session)
timerange = time(timeframe.period, i_session, i_GMT) and i_show_tr
no_trading = time(timeframe.period, i_no_trading, i_GMT) and i_show_nt 
// ▒▒▒▒▒ Sessions ▒▒▒▒▒ 
session_StartDate = input.time(timestamp("1 January 2023 00:00 -1000"), title="Start Date", group=g_trading )
show_sessions = input.bool(false,title='Sessions', group=g_trading)
// As  = input.session(title="Asia", defval="1800-0300")
// Lon = input.session(title="London", defval="0300-1200")
// Ny  = input.session(title="New York", defval="0800-1800")
Dz  = input.session(title="Deadzone - High Spreads", defval="1645-1830")




inl_color = "inl_color"
inl_color2 = "inl_color2"
c1_on = false //input.bool(true,title="", group=g_trading,inline=inl_color)
c1 = color.new(#00bcd4,90) //input.color(title="Asia", defval=#00bcd4,group=g_trading,inline=inl_color)
c2_on = true //input.bool(true,title="",group=g_trading,inline=inl_color)
c2 = color.new(#00796b,90) //input.color(title="London", defval=#00796b,group=g_trading,inline=inl_color)
c3_on = true //input.bool(true,title="",group=g_trading,inline=inl_color2)
c3 = color.new(#b71c1c,90) //input.color(title="New York", defval=#b71c1c,group=g_trading,inline=inl_color2)
c4_on = true //input.bool(true,title="", group=g_trading,inline=inl_color2)
c4 = color.new(color.purple,90) //input.color(title="Deadzone", defval=#b71c1c,group=g_trading,inline=inl_color2)

// Session(sess) => na(time("2",sess)) == false
// Asia = Session(As) and c1_on and show_sessions? c1 : na
// London = Session(Lon) and c2_on and show_sessions ? c2 : na
// NewYork = Session(Ny) and c3_on and show_sessions ? c3 : na
Deadzone = Session(Dz) and c4_on and show_sessions ? c4 : na
// bgcolor(Asia)
// bgcolor(London)
// bgcolor(NewYork)
bgcolor(Deadzone)


//
// ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ //
// STOP LOSS
// ------------------------------------------------------------------------------------------------------------------
g_sl = 'Stop Loss ----------------------------------------------------'
i_plot_trades = input.bool(true, title="Display Trades",group=g_sl)
i_sl_type   = input.string("ATR", title="SL Type", options=["ATR", "Pips","MA","SUP","Lowest"],group=g_sl) // ATR
i_ma_atr    = input.string("M6", title="Select MA ATR", options=["M6","M7","M8"],group=g_sl) // ATR

atr_group   = 'ATR'
show_sl     = input.bool(false,title="Show Stop Loss",group=atr_group)
sl_Multip   = input.float(1.0, title='SL Amount',group=atr_group) // 0.75 1.25 1.5
atr_len     = 14 //input.int(14, title='ATR Length ',group=atr_group)
atr_src     = 'close' //input.string('close', title='Close or Wicks', options=['close', 'wicks'],group=atr_group) // close
//atr_type    = input.string(title='ATR Type', defval='ema', options=['sma', 'ema', 'zema', 'dema', 'tema', 'wma', 'vwma', 'smma', 'rma', 'hma', 'lsma', 'McGinley'], group=atr_group)
atr_smooth  = 5 //input.int(5, title="ATR Smooth", group=atr_group)
i_sl_min_max = input.bool(true, title='Use SL Min and Max',group=atr_group)
sl_min      = input.float(1.5, title='Stop Loss Minimum Pips',group=atr_group) // 2.0
sl_max      = input.float(2.0, title='Stop Loss Maximum Pips',group=atr_group) // 4.0 12.0


// Take Profit
g_tp = 'Take Profit ----------------------------------------------------'
i_tpFactor  = input(70, 'Target Profit',group=g_tp) // 100 13 3.5 3
i_qty_mult  = input.float(1.0, title='Quantity Multiplier',group=g_tp) // 5.0 2
i_tsFactor  = input(7.0, 'Trailing Stop',group=g_tp) // 1.25
i_ts        = input.bool(true, title="Use Trailing Stop",group=g_tp)
i_ticks     = input.float(10, title='Min Ticks',group=g_tp) // 100 for Forex 1000 for NAS
show_ts     = input.bool(true,title="Trailing Stop",group=g_tp)
i_bkcandles = 11 //input.int(11, title="Lowerest range - Number of candles",group=g_sl)
i_leverage = input.int(33, 'Max Leverage', group=g_tp)
i_max_leverage_perc = input.float(0.98, 'Percentage of Leverage to Use', step=0.01, group=g_tp ) 

float qty_value = switch syminfo.type
    "forex" => 100000.0
    "futures" => 10.0
    "index" => 10.0
    => 10.0
    
stop_loss()=>

    float sl_short  = na
    float sl_long   = na
    float sl_short_t   = na
    float quantity  = syminfo.currency == 'JPY' ? i_pctStop * 100 : i_pctStop

    if i_sl_type == "ATR"
        atr_len = 14
        ATR = ta.atr(atr_len)
        sl_long     := (atr_src =='close' ? close : low)  - ATR * sl_Multip 
        sl_short    := (atr_src =='close' ? close : high) + ATR * sl_Multip 
        
    if i_sl_type == "MA"
        atr_len = 14
        ATR = ta.atr(atr_len)
        float ma_select = switch i_ma_atr
            "M6" => h6
            "M7" => h7
            "M8" => h8
            => h6
        
        sl_long     := ma_select
        sl_short    := ma_select


    if i_sl_type == "Lowest"
        sl_short    = ta.highest(high, i_bkcandles)[1]
        sl_long     = ta.lowest(low, i_bkcandles)[1]

    // Calculate Position Size and Pip Value
    base = str.tostring(syminfo.basecurrency)
    currency = str.tostring(syminfo.currency)
    rate = request.currency_rate(base, currency)
    balance = (i_equity=="Initial" ? strategy.initial_capital : strategy.equity)
    p_size = (i_leverage * (balance * i_max_leverage_perc ) ) / rate
    pip_value = ((0.0001 / rate) * p_size) * rate
    acc_max = balance * quantity

    // Stop Loss - Force a min and max range for the stop loss
    min = 1 / math.pow(10, (decimals-1) ) * sl_min
    max = 1 / math.pow(10, (decimals-1) ) * sl_max

    // Long
    longRatio = math.abs(close - sl_long)
    sl_long   := i_sl_min_max==false ? sl_long 
     : longRatio < min ? close - min 
     : longRatio > max ? close - max : sl_long
    longDiff  = math.abs(close - sl_long) 
    longPips  = get_pip_distance(close,sl_long)
    longTS    = close + (i_tsFactor * longDiff)
    longTP    = close + (i_tpFactor * longDiff)

    // Max Position Size based on percentage of account balance
    plValue   = balance * quantity / (longDiff / close)
    pl_max    = i_use_pos ? math.ceil( plValue ) / close : math.ceil( qty_value * i_qty_mult )
    // If Position Size is greater than max leverage available or
    // the Positoin Size is greather than 1% of account equity
    pl_size = pip_value * longPips > acc_max ? pl_max : p_size


    // Short
    shortRatio = math.abs(close - sl_short)
    sl_short   := i_sl_min_max==false ? sl_short 
     : shortRatio < min ? close + min 
     : shortRatio > max ? close + max : sl_short
    shortDiff = math.abs(close - sl_short)
    shortPips = get_pip_distance(close,sl_short)
    shortTS   = close - (i_tsFactor * shortDiff)
    shortTP   = close - (i_tpFactor * shortDiff)


    psValue   = balance * quantity / (shortDiff / close)
    ps_max    = i_use_pos ? math.ceil( psValue / close ) : math.ceil( qty_value * i_qty_mult )
    ps_size   = pip_value * shortPips > acc_max ? ps_max : p_size
   

    [sl_short,sl_long, close, close, shortTP, longTP, longTS, shortTS, pl_size, ps_size, longDiff, shortDiff, longPips, shortPips, pip_value, pl_size, acc_max ]

[atr_short, atr_long, long_close, short_close, shortTP, longTP, longTS, shortTS, pl_size, ps_size, longDiff, shortDiff, longPips, shortPips, pip_value, tmp_psize, acc_max] = stop_loss()

plot(acc_max, 'Acc Balance', color=color.new(white,100),style=plot.style_circles)
plot(tmp_psize, 'PL Size', color=color.new(white,100),style=plot.style_circles)

// ATR
atr_upper = ta.sma( ta.ema(atr_short, atr_len), atr_smooth ) // ma_types(atr_len, atr_type, atr_short)
atr_lower = ta.sma( ta.ema(atr_long, atr_len), atr_smooth )  // ma_types(atr_len, atr_type, atr_long)
atr_mid = (atr_lower + atr_upper) * 0.5
atr_mid_a = angle(atr_mid,14)
tmp = 1 / math.pow(10, (decimals-1) )  * sl_min

// Plot ATR
// plot(show_sl ? atr_upper  : na,"ATR Upper ", color=red )
// plot(show_sl ? atr_lower  : na,"ATR Lower ", color=lime )
// plot(show_sl ? atr_mid  : na,"ATR Mid ", color=atr_mid_a > 0 ? white : gray )
// plot(show_sl ? atr_mid_a  : na,"ATR Angle ", color=atr_mid_a > 0 ? color.new(green,100) : color.new(red,100) )
// plot(show_sl ? atr_long  : na,"ATR + ", color=color.new(green,70) )
// plot(atr_long - tmp , title='Min Pip Value', color=color.new(red,100)  )
// plot(show_sl ? atr_short : na,"ATR - ", color=color.new(red,70) )


// ▒▒▒▒▒ Stages ▒▒▒▒▒ 

g_stages = 'Stages ----------------------------------------------------'
stage2= 20
stage2_p= 10
stage3= 30
stage3_p= 15



// ▒▒▒▒▒ FILTERS ▒▒▒▒▒ 

g_filters = 'Filters ----------------------------------------------------'
inl_fi1 = 'inl_fi1'
inl_fi2 = 'inl_fi2'
inl_fi3 = 'inl_fi3'
i_perc_filter = input.bool(false, title='Percent Filter',group=g_filters, inline=inl_fi1)
i_perc_filter_num = input.float(7.0, title='Percent Number',group=g_filters, inline=inl_fi1)

i_cz_filter = input.bool(false, title='Consolidation Filter',group=g_filters, inline=inl_fi2)
i_cz_filter_num = input.float(15.0, title='Consolidation Number',group=g_filters, inline=inl_fi2)

i_atr_filter = input.bool(false, title='ATR Filter',group=g_filters, inline=inl_fi3)
i_atr_filter_num = input.float(9.0, title='Percent Number', group=g_filters, inline=inl_fi3)
i_perc_hhll = input.bool(false, title='Highest Lowest Filter',group=g_filters)
i_rsi_filter = input.bool(false, title='RSI Filter',group=g_filters)
i_ssl_filter = input.bool(false, title='SSL Filter',group=g_filters)
i_fibo_filter = input.bool(false, title='Fibo Filter',group=g_filters)
i_retrace_filter = input.bool(false,title='Filter Retrace Level',group=g_filters)
//i_use_filters = input.bool(false, title='Enable Filters',group=g_filters)
//i_use_time_filter = input.bool(false, title='Time Restraint',group=g_filters)
i_deadzone = input.bool(false, title='Do not take trades during',group=g_filters)

// var int flag_d = na
// flag_d := m2_a<-10 and m2_a<m2_a[1] ? -1 : flag_d==-1 and m2_a>m2_a[1] ? 0 : flag_d==-1 and m2_a<0 ? -1 : na


float shortSL = 0.0
float longSL  = 0.0
float ratio_l = 0.0
float short_ticks = 0.0
float long_ticks = 0.0
var int counter_trade = 0


// plot(get_pip_distance(close, atr_long), title='ATR pip distance', color=color.new(blue,100), style=plot.style_circles )
// plot(get_pip_distance(close, h3_m), title='H3 Distance', color=color.new(blue,100), style=plot.style_circles )
// plot(ta.barssince(h3_a_m<0), title='H3 Bars Since', color=color.new(blue,100), style=plot.style_circles )
// plot(ta.barssince(h3_m<stch_min), title='H3/Channel', color=color.new(blue,100), style=plot.style_circles )


// plot( ll2_bars, 'll2 Bars Since', color=color.new(blue,100) )
// plot( get_pip_distance(close,ll2), 'll2 pip distance', color=color.new(blue,100) )

var int eventLong = 0

// ▒▒▒▒▒ TRADE LOGIC ▒▒▒▒▒ 
trade_dir() =>
    c           = close>open ? 1 : -1
    dir         = 0
    entryLong   = 0
    entryShort  = 0
    exitLong    = 0
    exitShort   = 0
    closeAll    = 0
    //longSL      = 0.0
    cond        = ''
    cnt         = 0 // Counter Trend
    evt         = 0
    evtExit     = 0


    // ATR
    atr_f_s = atr_upper<atr_short
    atr_f_l = atr_lower<atr_long
    

    // ▒▒▒▒▒ ENTRY LONG ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ 


    // End of Downtrend
    if c==-1 and m5_m<m6_m and i_long_back_trades==true and strategy.position_size == 0

        // EMA 15 - 2 Hour
        if h5_m<h6_m
         and m2_a_m>0 
         and (close<m2_m and m2_m<m3_m)
            entryLong := 1
            cond := 'm2-b-cnt'


        // ▒▒▒▒▒ FILTERS ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒
    
        // if m2_m>m3_m and close>m3_m
        //     entryLong := 0

        // if m2_m<m3_m and close>m2_m
        //     entryLong := 0  
            
    if c==-1 and h7_a_m>0 and i_long_trades==true

        // EMA 15 - 2 Hour
        if close<m2_m 
         and m3_a_m>0
         //and (hma_dir==1 and hma_stage<4 and h6>hl2_mid)
         and not(h7_m>high)
            entryLong := 1
            cond := 'm2-b'


            // ▒▒▒▒▒ FILTERS ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒

            // Predictive Ranges
            if low>pravg_2 or low>pravg_1 
                entryLong := 0

            // Red failing
            if hma_dir==1 
             and h3_m>h5_m and h3_a_m<0
             and close>h6_m
                entryLong := 0

            // H3 above Highest
            if hma_dir==1 
             and h3_m>h5_m and h3_m>hh2
                entryLong := 0

            // Candles
            // if ch>ch2
            //     entryLong := 0

            // SAR
            // if psar_dir==-1 and psar_m>hh2  
            //     entryLong := 0 


            // HMA - Chart
            // if h3_a < -7 and h3>m2_m
            //     entryLong := 0


    if strategy.position_size == 0 and c==-1 
     and h9<ll2 and h9_a>0
     and close<m1_m 
        entryLong := 1
        cond := 'h9-b'


        // ▒▒▒▒▒ FILTERS ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒

        // Wait for downtrend EMA (2 hour) to open NOT converging 
        // unless m5_m angle is increasing
        
        if f_ma_cross(m5_m, m6_m) == -1
         and get_pip_distance(m5_m, m6_m) < conv_amount 
            entryLong := 0

        // EMA 50 - 2 Hour
        // if close<m3_m 
        //  and m5_a_m>0 and m3_m>m5_m
        //  and get_pip_distance(h3,close) <= 2
        //  and not(h7_m>high)
        //     entryLong := 1
        //     cond := 'm3-b'





    // ▒▒▒▒▒ FILTERS ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ 
    
    // HMA Red Down
    // if hma_dir == 1 and hma_stage == 5
    //     entryLong := 0






    // ▒▒▒▒▒ LONG COUNTER ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒

    // if c==1 and h7_a_m>0

    //     if hma_dir == 1 and hma_stage == 5
    //      and close>h5_m
    //         entryShort := 1
    //         cond := 'red-down'

    //         if h3<h9
    //             entryShort := 0

        // if PR2_state==-1
        //  and close>m2_m and m2_a_m<0 and m1_m<m2_m
        //     entryShort := 1
        //     cond := 'm2-s'
        //     cnt := 1




    // ▒▒▒▒▒ EXIT LONG ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ 

    if strategy.position_size > 0


        if c==1
         and m2_m>m3_m 
         and close>h5_m and close>h6_m
         and bb_upper_f > bb_upper
         and psar_m>prS2_2
         and psar_dir==1
         and co2 > pravg_2
         and h8>h9
         //and h3_a_m>7
            exitLong := 1
            cond := 'ex 2'

        if c==1
         and h7<h8 and h8_a>0 and close>h8
         and h7>hh2
         and h3>h8
            exitLong := 1
            cond := 'h7-ex'

        // if c==1
        //  and hma_dir == 1 and hma_stage == 5
        //  and close>h5_m
        //     exitLong := 1
        //     cond := 'red-down'



    // ▒▒▒▒▒ ENTRY SHORT ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ 
    if c==1 and h7_a_m<0 and m3_a_m<0

        if close>m2_m 
         and h3>m2_m
         and not(PR2_state==1)
         and not(h7_m<low)
         and not(h6_a_m>0)
         and not(h7_a_m>-1 and h3_a_m>0)
         and not(h7<h8 and h7_a>0)
         and not(h7>h8 and close<h7)
         //and m2_m>m3_m and m3_a>0
            entryShort := 1
            cond := 'm2-s'



    // ▒▒▒▒▒ FILTERS ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ 

    // HMA - 1 hour
    if hma_dir == -1 and h3_m<h5_m
     and h3_a_m>0 and h2_m>h3_m and h2_a_m>0
        entryShort := 0




    // ▒▒▒▒▒ COUNTER SHORT ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒




    // ▒▒▒▒▒ EXIT SHORT ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒


    if strategy.position_size < 0
        
        // if close<prS1_2 and c1_lo and close<PC_S1
        //     exitShort := 1
        //     cond := 'pr2-exit'

        if c==0
         and m2_m<m3_m 
         and close<h5_m and close<h6_m
         and bb_upper_f < bb_upper
         and psar_m<prR2_2
         and psar_dir==1-1
         and co2 < pravg_2
            exitShort := 1
            cond := 'ex-base'


        // if c==0 
        //  and ll2_bars>350
        //  and get_pip_distance(close,ll2)<2
        //     exitShort := 1
        //     cond := 'll2-ex'



    // if strategy.position_size > 0 and counter_trade==-1 and 
    //  close>hl2_mid and h9_a_m<0
    //     exitLong := 1
    //     cond := 'cnt-hl2'
    //     cnt  := -1





    // ▒▒▒▒▒ GLOBAL FILTERS ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒

    // Exclude New York close
    if i_deadzone and Session(Dz)
        entryLong := 0
        entryShort := 0

    // Trading Start Date
    if time<session_StartDate
        entryLong := 0
        entryShort := 0

    // Percent Filter
    if i_perc_filter and perc_change()>i_perc_filter_num  
        entryLong := 0
        entryShort := 0

    if i_long_trades==false
        entryLong := 0

    if i_short_trades==false
        entryShort := 0

        


    [entryLong,entryShort,exitLong,exitShort,closeAll,cond, cnt, evt, evtExit]

[entryLong,entryShort,exitLong,exitShort,closeAll,trade_label, cnt, evt, evtExit] = trade_dir()

// eventLong := ta.barssince(eventLong==1)>200 ? 0 : 1
eventLong := evt==1 ? 1 : evtExit==1 ? 0 : eventLong 
counter_trade := cnt==0 ? counter_trade : cnt
plot(counter_trade,'counter trade', color=color.new(white,100), style=plot.style_circles)
plot(eventLong,'eventLong', color=eventLong==1?color.new(green,100):color.new(red,100), style=plot.style_circles)
plot(evtExit,'evtExit', color=evtExit==1?color.new(green,100):color.new(red,100), style=plot.style_circles)
// check if live trading or market closed
//plotshape(barstate.islastconfirmedhistory, title='Real Time', color=red, style=shape.circle, location=location.top)


plot(strategy.position_size < 0 ? -1 : strategy.position_size > 0  ? 1 : 0, title='Trade Direction', color=color.new(blue,100))


// longDiff := entryLong and strategy.opentrades == 0 ? longDiff * 100 : strategy.opentrades > 0 ? longDiff[1] : 0


// Short Plots
plot_trans = 90

short_close := entryShort and strategy.opentrades == 0 ? short_close : strategy.opentrades > 0 ? short_close[1] : 0
short_ticks := short_close - (syminfo.mintick * i_ticks)
shortSL     := entryShort and strategy.opentrades == 0 ? atr_short : strategy.opentrades > 0 ? shortSL[1] : 0
shortTS     := entryShort and strategy.opentrades == 0 ? shortTS : strategy.opentrades > 0 ? shortTS[1] : 0
shortTP     := entryShort and strategy.opentrades == 0 ? shortTP : strategy.opentrades > 0 ? shortTP[1] : 0
plot(strategy.opentrades > 0 and i_plot_trades ? shortPips : na, title="Short Pips", color=color.new(blue,100))
p_s_c       = plot( strategy.opentrades > 0 and i_plot_trades ? short_close : na, title="Short Close", color=color.new(green,plot_trans), linewidth=1, style=plot.style_linebr)
p_s_ticks   = plot( strategy.opentrades > 0 and i_plot_trades ? short_ticks : na, title="Short Ticks", color=lime, style=plot.style_linebr)
p_s_sl      = plot( strategy.opentrades > 0 and i_plot_trades ? shortSL : na, title="Short SL", color=color.new(red,plot_trans), linewidth=1, style=plot.style_linebr)
p_s_ts      = plot( strategy.opentrades > 0 and i_plot_trades ? shortTS : na, title="Short TS", color=color.new(green,plot_trans), linewidth=1, style=plot.style_linebr)
p_s_tp      = plot( strategy.opentrades > 0 and i_plot_trades ? shortTP : na, title="Short TP", color=color.new(green,plot_trans), linewidth=1, style=plot.style_linebr)
fill(p_s_sl, p_s_c,title='Fill Short SL',color=color.new(red,plot_trans) )
fill(p_s_ts, p_s_c,title='Fill Short TS',color=color.new(lime,plot_trans) )
fill(p_s_ts, p_s_tp,title='Fill Short TP',color=color.new(green,plot_trans) )

// Long Plots
long_close  := entryLong and strategy.opentrades == 0 ? long_close : strategy.opentrades > 0 ? long_close[1] : 0
long_ticks  := long_close + (syminfo.mintick * i_ticks)
longSL      := entryLong and strategy.opentrades == 0 ? atr_long : strategy.opentrades > 0 ? longSL[1] : 0
longTS      := entryLong and strategy.opentrades == 0 ? longTS : strategy.opentrades > 0 ? longTS[1] : 0
longTP      := entryLong and strategy.opentrades == 0 ? longTP : strategy.opentrades > 0 ? longTP[1] : 0
ratio_l     := ( close  - long_close )
//plot( sl_short  ,"sl_short ", color=color.new(red,100) )

// var int decimals = int(math.log10(1/syminfo.mintick))
// decimals := decimals == 2 ? 1 : decimals == 0 ? 1 : decimals
// plot(strategy.opentrades > 0 and i_plot_trades ? syminfo.mintick : na, title="Min Ticks", color=color.new(blue,100))
// plot(strategy.opentrades > 0 and i_plot_trades ? math.pow(10, decimals) * syminfo.mintick : na, title="Ticks Converted", color=color.new(blue,100))
// plot(strategy.opentrades > 0 and i_plot_trades ? decimals : na, title="Decimals", color=color.new(blue,100))
plot(strategy.opentrades > 0 and i_plot_trades ? longDiff : na, title="Long Diff", color=color.new(yellow,100))
plot(strategy.opentrades > 0 and i_plot_trades ? longPips : na, title="Long Pips", color=color.new(yellow,100))
//plot(strategy.opentrades > 0 and i_plot_trades ? longDiff / syminfo.mintick : na, title="Ticks to Pips")
p_l_dist    = plot( strategy.opentrades > 0 and i_plot_trades ? get_pip_distance(long_close,longSL) * (math.pow(10, decimals) * syminfo.mintick) : na, title="SL in Pips", color=color.new(blue,100) )
p_l_c       = plot( strategy.opentrades > 0 and i_plot_trades ? long_close : na, title="Long Close", color=color.new(red,plot_trans), linewidth=1, style=plot.style_linebr)
p_l_ticks   = plot( strategy.opentrades > 0 and i_plot_trades ? long_ticks : na, title="Long Ticks", color=color.new(lime,plot_trans), style=plot.style_linebr)
p_l_sl      = plot( strategy.opentrades > 0 and i_plot_trades ? longSL : na, title="Long SL", color=color.new(red,plot_trans), linewidth=1, style=plot.style_linebr)
p_l_ts      = plot( strategy.opentrades > 0 and i_plot_trades ? longTS : na, title="Trailing Stop", color=color.new(yellow,plot_trans), linewidth=1, style=plot.style_linebr)
p_l_tp      = plot( strategy.opentrades > 0 and i_plot_trades ? longTP : na, title="Long TP", color=color.new(green,plot_trans), linewidth=1, style=plot.style_linebr)

//psize       = plot(pl_size, title="Position Size")
//p_l_ratio   = plot( ratio_l, title="Ratio", color=color.new(red,75), linewidth=1, style=plot.style_linebr)
fill(p_l_sl,p_l_c, title='Fill Long SL', color=color.new(red,plot_trans))
fill(p_l_ts,p_l_c, title='Fill Long Trailing Stop', color=color.new(lime,plot_trans) )
fill(p_l_tp,p_l_ts, title='Fill Long Take Profit', color=color.new(green,plot_trans))



cd=close>open?1:0

if Session(Dz) and i_deadzone
    entryShort := 0
    entryLong := 0

// Long

// if high<atr_lower and entryLong
//     entryLong := 0

// if  atr_upper>m2 and entryLong
//     entryLong := 0

var int trade_num = 0
if (entryLong)
    //pd_level = get_retrace_level(close)
    // * decimals , "#.00"
    trade_num := strategy.opentrades == 0 ? trade_num + 1 : trade_num
	strategy.entry("L", strategy.long, qty = pl_size, comment=trade_label + ' #' 
     + str.tostring(trade_num) + ' ' 
     + '(' + str.tostring( longPips, '#.##')  + ') '
     + '(' + str.tostring( pip_value, '#.##') + ') ' 
     //+ str.tostring( math.round_to_mintick(longDiff)* 10 * decimals) + ' '
     //+ str.tostring( get_pip_distance(long_close, close) )
     //+ str.tostring( pd_level )
     )
    //strategy.exit('EXIT L', 'L', stop = longSL)
        
else
	strategy.cancel("S")

if (exitLong ) //and counter_trade==0
    exit_com = trade_label + ' ' + str.tostring( get_pip_distance(long_close, close) )
	strategy.close("L", comment = exit_com)
    eventLong := 0

// Short
if (entryShort)
    trade_num := strategy.opentrades == 0 ? trade_num + 1 : trade_num
	strategy.entry("S", strategy.short, qty = ps_size, comment=trade_label 
     + ' #' + str.tostring(trade_num) + ' '
     + '(' + str.tostring( shortPips, '#.##' ) + ') '  
     + '(' + str.tostring( pip_value, '#.##' ) + ') '  
     //+ str.tostring( math.round_to_mintick(shortDiff * 100))
     )
    //strategy.exit('EXIT S', 'S', stop=shortSL)
else
	strategy.cancel("S")

if (exitShort)
    exit_com = trade_label + ' ' + str.tostring( get_pip_distance(short_close, close) )
	strategy.close("S", comment = exit_com)

// Filters
// if Session(Dz) and i_deadzone
//     strategy.close_all(comment = "close all entries")

in_profit() =>
    if strategy.position_size > 0 and close > longTS
        true
    else if strategy.position_size < 0 and close < shortTS
        true
    else
        false

getCurrentStage() =>
    var stage = 0
    if strategy.position_size == 0
        stage := 0
        stage
    if stage == 0 and strategy.position_size != 0
        stage := 1
        stage
    else if stage == 1 
        if strategy.position_size > 0 and close > longTS
            stage := 2
        if strategy.position_size < 0 and close < shortTS
            stage := 2
    else if stage == 2
        if strategy.position_size > 0 and close > longTP
            stage := 3
        if strategy.position_size < 0 and close < shortTP
            stage := 3
            stage
    stage

curStage = getCurrentStage()
plot(curStage,title='Current Stage', color=color.new(color.blue,100))

float stopLevel = na
string comment  = ''
float limit     = na //strategy.position_size > 0 and close>longTP and m2>m4 ? close : strategy.position_size < 0 and close<shortTP and m2<m4 ? close : na
string win     = 'Take Profit'
string loss    = 'Loss'
string bkeven  = 'Break Even'
string ts      = 'Trailing Stop'

targetProfitLong = high>longTP and h3_m>h5_m and h3_m>h6_m
if curStage == 1
    // Comment Strings

    if strategy.position_size > 0
        stopLevel := longSL
        comment   := close < long_close ? loss : win
        limit     := targetProfitLong ? high : na
    else if strategy.position_size < 0
        stopLevel := shortSL
        comment   := close > short_close ? loss : win
        limit     := low<shortTP ? low : na
        
    strategy.exit('x', stop=stopLevel, limit=limit, comment=comment)

else if curStage == 2

    if strategy.position_size > 0
        stopLevel := long_close + (syminfo.mintick * i_ticks)
        pips = str.tostring( get_pip_distance(long_close, close) )  
        comment   := close <= longTS ? bkeven + ' ' + pips  : win + ' ' + pips
        limit     := targetProfitLong ? high: na
        //get_pip_distance(long_close, close)>60 ? high 
    else if strategy.position_size < 0
        stopLevel := short_close - (syminfo.mintick * i_ticks)
        pips       = str.tostring( get_pip_distance(short_close, close) )
        comment   := close >= shortTS ? bkeven + ' ' + pips : win + ' ' + pips
        limit     := low<shortTP ? low : na

    strategy.exit('x', stop=stopLevel, limit=limit, comment=comment)

else if curStage == 3

    if strategy.position_size > 0
        stopLevel := longTS
        pips      = str.tostring( get_pip_distance(long_close, close) )
        comment   := close >= longTS and close<longTP ? ts + ' ' + pips : win + ' ' + pips
        limit     := targetProfitLong ? high : na
    else if strategy.position_size < 0
        stopLevel := shortTS
        pips       = str.tostring( get_pip_distance(short_close, close) )
        comment   := close <= shortTS and close>shortTP ? ts + ' ' + pips : win  + ' ' + pips
        limit     := low<shortTP ? low : na

    strategy.exit('x', stop=stopLevel, limit=limit, comment=comment)

else
    strategy.cancel('x')


bars_long = ta.barssince(strategy.position_size > 0)
plot(bars_long, title='Bars since long', color=color.new(blue,98))
bars_short = ta.barssince(strategy.position_size < 0)
plot(bars_short, title='Bars since short', color=color.new(blue,98))