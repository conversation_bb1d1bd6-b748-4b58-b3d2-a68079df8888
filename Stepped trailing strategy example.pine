// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © adolgov

// @description
//-------------------------------------------------
// activateTrailingOnThirdStep == false (default)
//  - when tp1 is reached, sl is moved to break-even
//  - when tp2 is reached, sl is moved to tp1
//  - when tp3 is reached - exit
//-------------------------------------------------
// activateTrailingOnThirdStep == true
//  - when tp1 is reached, sl is moved to break-even
//  - when tp2 is reached, sl is moved to tp1 and trailing stop is activated with tp2 amount level and tp1 offset level
//-------------------------------------------------

//@version=5
strategy('Stepped trailing strategy example', overlay=true, precision=4, initial_capital=100000, default_qty_value=10, currency=currency.USD, process_orders_on_close=true)

red = #ff0062
orange = #ff9800
yellow = #FFFF00
green = #4caf50
lime = #00E676
aqua = #00bcd4
blue = #2962ff
dark_blue = #2962ff
violet = #814dff
white = #ffffff
gray = #707070
black = #000000

// Angle
angle(_src, len) =>
    rad2degree = 180 / 3.14159265359  //pi 
    ang = rad2degree * math.atan((_src[0] - _src[1]) / ta.atr(len))
    ang

// random entry condition
i_ma1   = input(100)  // 14
i_ma2   = input(200)  // 28
i_ma3   = input(500)  // 200
ma1     = ta.sma(close, i_ma1)
ma2     = ta.sma(close, i_ma2)
ma3     = ta.sma(close, i_ma3)
ma3_a   = angle(ma3, 3)

var float entryClose = 0.0
if ta.crossover(ma1, ma2) and ma3_a > 0
    if strategy.position_size == 0
        entryClose := close
        strategy.entry('Long', strategy.long)
        

if ta.crossunder(ma1, ma2) and ma3_a < 0
    if strategy.position_size == 0
        entryClose := close
        strategy.entry('Short', strategy.short)

plot(ma1, color=color.new(color.orange, 0))
plot(ma2, color=color.new(color.blue, 0))
plot(ma3, color=color.new(color.red, 0))
// exit logic    


i_dec = input.int(500, title='Decimal') //100
percent2points(percent) =>
    strategy.position_avg_price * percent / i_dec / syminfo.mintick



// sl & tp in %%
sl  = percent2points(input(1.0, title='stop loss %%'))
tp1 = percent2points(input(1.0, title='take profit 1 %%'))
tp2 = percent2points(input(2.0, title='take profit 2 %%'))
tp3 = percent2points(input(3.0, title='take profit 3 %%'))
activateTrailingOnThirdStep = input(false, title='activate trailing on third stage (tp3 is amount, tp2 is offset level)')

curProfitInPts() =>
    if strategy.position_size > 0
        (high - strategy.position_avg_price) / syminfo.mintick
    else if strategy.position_size < 0
        (strategy.position_avg_price - low) / syminfo.mintick
    else
        0

calcStopLossPrice(OffsetPts) =>
    if strategy.position_size > 0
        strategy.position_avg_price - OffsetPts * syminfo.mintick
    else if strategy.position_size < 0
        strategy.position_avg_price + OffsetPts * syminfo.mintick
    else
        na

calcProfitTrgtPrice(OffsetPts) =>
    calcStopLossPrice(-OffsetPts)

getCurrentStage() =>
    var stage = 0
    if strategy.position_size == 0
        stage := 0
        stage
    if stage == 0 and strategy.position_size != 0
        stage := 1
        stage
    else if stage == 1 and curProfitInPts() >= tp1
        stage := 2
        stage
    else if stage == 2 and curProfitInPts() >= tp2
        stage := 3
        stage
    stage

calcTrailingAmountLevel(points) =>
    var float level = na
    level := calcProfitTrgtPrice(points)
    if not na(level)
        if strategy.position_size > 0
            if not na(level[1])
                level := math.max(level[1], level)
                level
            if not na(level)
                level := math.max(high, level)
                level
        else if strategy.position_size < 0
            if not na(level[1])
                level := math.min(level[1], level)
                level
            if not na(level)
                level := math.min(low, level)
                level

calcTrailingOffsetLevel(points, offset) =>
    float result = na
    amountLevel = calcTrailingAmountLevel(points)
    if strategy.position_size > 0
        trailActiveDiff = amountLevel - calcProfitTrgtPrice(points)
        if trailActiveDiff > 0
            result := trailActiveDiff + calcProfitTrgtPrice(offset)
            result
    else if strategy.position_size < 0
        trailActiveDiff = calcProfitTrgtPrice(points) - amountLevel
        if trailActiveDiff > 0
            result := calcProfitTrgtPrice(offset) - trailActiveDiff
            result
    result

float stopLevel = na
float trailOffsetLevel = na
float profitLevel = activateTrailingOnThirdStep ? calcTrailingAmountLevel(tp3) : calcProfitTrgtPrice(tp3)

// note: calcTrailingOffsetLevel uses calcTrailingAmountLevel and last one has a state (level).
//       therefor we needs calculate it on every bar for correct result.
//       if we inline it the Pine compiler give us warning "The function '***' should be called on each calculation for consistency. It is recommended to extract the call from this scope."
trailOffsetLevelTmp = calcTrailingOffsetLevel(tp3, tp2)

// based on current stage set up exit
// note: we use same exit ids ("x") consciously, for MODIFY the exit's parameters
curStage = getCurrentStage()
if curStage == 1
    stopLevel := calcStopLossPrice(sl)
    strategy.exit('x', loss=sl, profit=tp3, comment='sl or tp3')
else if curStage == 2
    stopLevel := calcStopLossPrice(0)
    strategy.exit('x', stop=stopLevel, profit=tp3, comment='breakeven or tp3')
else if curStage == 3
    stopLevel := calcStopLossPrice(-tp1)
    if activateTrailingOnThirdStep
        trailOffsetLevel := trailOffsetLevelTmp
        strategy.exit('x', stop=stopLevel, trail_points=tp3, trail_offset=tp3 - tp2, comment='stop tp1 or trailing tp3 with offset tp2')
    else
        strategy.exit('x', stop=stopLevel, profit=tp3, comment='tp1 or tp3')
else
    strategy.cancel('x')

// this is debug plots for visulalize TP & SL levels
// plot(sl, title='SL', style = plot.style_linebr, color = color.red)
// plot(tp1, title='TP', style = plot.style_linebr, color = color.blue)
// plot(tp2, title='Trailing', style = plot.style_linebr, color = color.green)


p_sl = plot(strategy.position_size != 0 ? stopLevel   : na, title='SL', style=plot.style_linebr, color=color.new(color.red, 0) )
p_en = plot(strategy.position_size != 0 ? entryClose  : na, title='EN', style=plot.style_linebr, color=color.new(color.red, 0) )
p_tp = plot(strategy.position_size != 0 ? profitLevel : na, title='TP', style=plot.style_linebr, color=color.new(color.blue, 0))
p_ts = plot(strategy.position_size != 0 ? trailOffsetLevel : na, title='Trailing', style=plot.style_linebr, color=color.new(color.green, 0) )

fill(p_sl,p_en, title='Fill SL', color=color.new(red,75))
fill(p_en,p_tp, title='Fill SL', color=color.new(green,75))


