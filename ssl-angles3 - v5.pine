//@version=5
indicator(title='SSL Angles 3 - V5', shorttitle='SSL Angles 3 - V5')

// Global
g_global = 'Global'
inl_g = '1'
input_angle = input.int(14, 'Angle degree', group=g_global, inline=inl_g)  // 14
sma_input = input.int(14, 's1 SMA', minval=1, group=g_global, inline=inl_g)  // 100
plot_type_input = input.string(title='Choose Plot Type', options=['line', 'columns', 'area', 'histo', 'step'], defval='line', group=g_global, inline=inl_g)
plot_type = plot_type_input == 'line' ? plot.style_line : plot_type_input == 'columns' ? plot.style_columns : plot_type_input == 'area' ? plot.style_area : plot_type_input == 'histo' ? plot.style_histogram : plot_type_input == 'stepline' ? plot.style_linebr : na

red = #ff0062
aqua = #00bcd4
yellow = #FFFF00
orange = #ff9800
green = #4caf50
lime = #00E676
white = #ffffff
c_hide = color.new(#ffffff, 100)
blue = #42a5f5
violet = #814dff
magenta = #ff0062
purple = #0045b3
gray = #707070
black = #000000
sell_color = color.new(#ff0062, 20)
buy_color = color.new(#00c3ff, 20)

angle(_src, len) =>
    rad2degree = 180 / 3.14159265359  //pi 
    ang = rad2degree * math.atan((_src[0] - _src[1]) / ta.atr(len))
    ang

angles(len1, type) =>
    float v = na
    if type == 'SMA'
        v := ta.sma(close, len1)
        v
    if type == 'EMA'
        v := ta.ema(close, len1)
        v
    if type == 'HMA'
        v := ta.wma(2 * ta.wma(close, len1 / 2) - ta.wma(close, len1), math.round(math.sqrt(len1)))
        v

    obj_angle = ta.sma(angle(v, input_angle), sma_input)
    [obj_angle]

// Basis
g_bb = 'Basis'
inl_bb = '1'
show_ba = input(title='Show BA\'s', defval=false)
basis_len = input.int(20, 'Basis Length', group=g_bb, inline=inl_bb)  // 20
basis_len2 = input.int(150, 'Basis Length 2', group=g_bb, inline=inl_bb)  // 40
basis_len3 = input.int(300, 'Basis Length 3', group=g_bb, inline=inl_bb)  // 100
smooth_type = input.string(title='Smooth Type', defval='SMA', options=['SMA', 'EMA', 'HMA'])
BB_stdDev = 2

basis = ta.sma(close, basis_len)
dev = BB_stdDev * ta.stdev(close, basis_len)
bb_upper = basis + dev
bb_lower = basis - dev
bb_ua = angle(bb_upper, 3)
bb_la = angle(bb_lower, 3)

[ba] = angles(basis_len, smooth_type)
[ba2] = angles(basis_len2, smooth_type)
[ba3] = angles(basis_len3, smooth_type)



plot(show_ba ? bb_ua : na, title='BB Upper Angle', color=bb_ua < 0 ? green : red, style=plot_type)
plot(show_ba ? bb_la : na, title='BB Lower Angle', color=bb_la < 0 ? white : blue, style=plot_type)
plot(show_ba ? ba : na, title='Basis angle 1', color=color.new(orange, 0), style=plot_type)
plot(show_ba ? ba2 : na, title='Basis angle2', color=show_ba == true ? yellow : color.new(yellow, 100), style=plot_type)
plot(show_ba ? ba3 : na, title='Basis angle3', color=show_ba == true ? blue : color.new(blue, 100), style=plot_type)

// EMA
g_ema = ' '
inl_ema = '1'
show_ema = input(title='Show EMA\'s', defval=false)
e1_len = input.int(8, 'E1', group=g_ema, inline=inl_ema)  // 30 
e2_len = input.int(20, 'E2', group=g_ema, inline=inl_ema)
e3_len = input.int(50, 'E3', group=g_ema, inline=inl_ema)
e4_len = input.int(200, 'E 200', group=g_ema, inline=inl_ema)
[e1_a] = angles(e1_len, 'EMA')
[e2_a] = angles(e2_len, 'EMA')
[e3_a] = angles(e3_len, 'EMA')
[ea] = angles(e4_len, 'EMA')

ea_zone = math.abs(ea)
e_zones = ea_zone < 1 ? 0 : ea_zone < 2 ? 1 : ea_zone < 3 ? 2 : ea_zone < 4 ? 3 : ea_zone < 5 ? 4 : ea_zone < 6 ? 5 : ea_zone > 6 ? 6 : na
e_color = e_zones == 0 ? purple : e_zones == 1 ? red : e_zones == 2 ? orange : e_zones == 3 ? yellow : e_zones == 4 ? white : e_zones == 5 ? green : e_zones == 6 ? #00c3ff : na

plot(show_ema ? e1_a : na, title='EMA 8 angle', color=e1_a < ba ? white : gray, style=plot_type)
plot(show_ema ? e2_a : na, title='EMA 20 angle', color=e2_a < ba ? aqua : #3179f5, style=plot_type)
plot(show_ema ? e3_a : na, title='EMA 50 angle', color=e3_a > ba ? #cd1beb : e3_a < ba and e3_a > 0 ? yellow : green, style=plot_type)
plot(show_ema ? ea : na, title='EMA 200 angle', color=e_color, style=plot_type)


g_cb = 'SSL -------------------------------------------------------------'
inl_cb = 'cb'
show_ssl = input.bool(title='Show SSL', defval=true, group=g_cb)
show_s1 = input.bool(title='S1', defval=false, group=g_cb, inline=inl_cb)
show_s2 = input.bool(title='s2', defval=false, group=g_cb, inline=inl_cb)
show_s3 = input.bool(title='s3', defval=false, group=g_cb, inline=inl_cb)
show_s4 = input.bool(title='S4', defval=false, group=g_cb, inline=inl_cb)
show_s5 = input.bool(title='S5', defval=false, group=g_cb, inline=inl_cb)
show_s6 = input.bool(title='S6', defval=false, group=g_cb, inline=inl_cb)
show_s7 = input.bool(title='S7', defval=true, group=g_cb, inline=inl_cb)
show_s8 = input.bool(title='S8', defval=true, group=g_cb, inline=inl_cb)
inl_len = 'len1'
s_len1 = input.int(8, title='S1', inline=inl_len)  // 8
s_len2 = input.int(20, title='S2', inline=inl_len)  // 20
s_len3 = input.int(50, title='S3', inline=inl_len)  // 50
s_len4 = input.int(75, title='S4', inline=inl_len)  // 75 
s_len5 = input.int(100, title='S5', inline=inl_len)  // 100
s_len6 = input.int(200, title='S6', inline=inl_len)  // 200
s_len7 = input.int(300, title='S7', inline=inl_len)  // 300
s_len8 = input.int(500, title='S8', inline=inl_len)  // 500
g_multi = ''
inl_multi = 'multi'
ssl_curr = input.bool(title='Show Current', defval=true, inline=inl_multi, group=g_multi)
ssl_multi = input.bool(title='Show Multi', defval=true, inline=inl_multi, group=g_multi)
ssl_res = input.timeframe(title='Timeframe', defval='15', group=g_multi)
//useCurrentRes = input(true, title="Chart Resolution?",inline=inl_multi,group=g_multi)
angle_input = 14  // input(title="Angle Amount",type=input.integer, defval=14)

ma_type = input.string(title='MA Type', defval='HMA', options=['SMA', 'EMA', 'HMA'])
[s1_a] = angles(s_len1, ma_type)
[s2_a] = angles(s_len2, ma_type)
[s3_a] = angles(s_len3, ma_type)
[s4_a] = angles(s_len4, ma_type)
[s5_a] = angles(s_len5, ma_type)
[s6_a] = angles(s_len6, ma_type)
[s7_a] = angles(s_len7, ma_type)
[s8_a] = angles(s_len8, ma_type)


plot(show_ssl and ssl_curr and show_s1 ? s1_a : na, title='s1 angle', color=s1_a > 0 ? aqua : blue, style=plot_type)
plot(show_ssl and ssl_curr and show_s2 ? s2_a : na, title='s2 angle', color=s2_a > 0 ? red : green, linewidth=2, style=plot_type)
plot(show_ssl and ssl_curr and show_s3 ? s3_a : na, title='S3 angle', color=color.new(white, 50), style=plot_type)
plot(show_ssl and ssl_curr and show_s4 ? s4_a : na, title='S4 angle', color=s4_a > 0 ? yellow : orange, style=plot_type)
plot(show_ssl and ssl_curr and show_s5 ? s5_a : na, title='S5 angle', color=s5_a > 0 ? green : red, style=plot_type)
plot(show_ssl and ssl_curr and show_s6 ? s6_a : na, title='S6 angle', color=show_s6 == true ? green : color.new(green, 100), style=plot_type)
plot(show_ssl and ssl_curr and show_s7 ? s7_a : na, title='S7 angle', color=s7_a > 0 ? color.new(red, 60) : color.new(green, 60), style=plot_type)
plot(show_ssl and ssl_curr and show_s8 ? s8_a : na, title='S8 angle', color=s8_a > 0 ? color.new(red, 60) : color.new(green, 60), style=plot_type)


// === MULTI  ===
// ==============
s1_a_m = request.security(syminfo.tickerid, ssl_res, s1_a)
s2_a_m = request.security(syminfo.tickerid, ssl_res, s2_a)
s3_a_m = request.security(syminfo.tickerid, ssl_res, s3_a)
s4_a_m = request.security(syminfo.tickerid, ssl_res, s4_a)
s5_a_m = request.security(syminfo.tickerid, ssl_res, s5_a)
s6_a_m = request.security(syminfo.tickerid, ssl_res, s6_a)
s7_a_m = request.security(syminfo.tickerid, ssl_res, s7_a)
s8_a_m = request.security(syminfo.tickerid, ssl_res, s8_a)
// s1_m = security(syminfo.tickerid, ssl_res, s1)
// s3_m = security(syminfo.tickerid, ssl_res, s3)
// s2_m = security(syminfo.tickerid, ssl_res, s2)
// s4_m = security(syminfo.tickerid, ssl_res, s4)
// s5_m = security(syminfo.tickerid, ssl_res, s5)
// s6_m = security(syminfo.tickerid, ssl_res, s6)
// s7_m = security(syminfo.tickerid, ssl_res, s7)
// s8_m = security(syminfo.tickerid, ssl_res, s8)

plot(show_ssl and ssl_multi and show_s1 ? s1_a_m : na, title='s1 angle', color=s1_a_m > 0 ? aqua : blue, style=plot_type)
plot(show_ssl and ssl_multi and show_s2 ? s2_a_m : na, title='s2 angle', color=s2_a_m > 0 ? red : green, linewidth=2, style=plot_type)
plot(show_ssl and ssl_multi and show_s3 ? s3_a_m : na, title='S3 angle', color=color.new(white, 0), style=plot_type)
plot(show_ssl and ssl_multi and show_s4 ? s4_a_m : na, title='S4 angle', color=s4_a_m > 0 ? yellow : orange, style=plot_type)
plot(show_ssl and ssl_multi and show_s5 ? s5_a_m : na, title='S5 angle', color=s5_a_m > 0 ? green : red, style=plot_type)
plot(show_ssl and ssl_multi and show_s6 ? s6_a_m : na, title='S6 angle', color=show_s6 == true ? green : color.new(green, 100), style=plot_type)
plot(show_ssl and ssl_multi and show_s7 ? s7_a_m : na, title='S7 angle', color=s7_a_m > 0 ? color.new(red, 60) : color.new(green, 60), style=plot_type)
plot(show_ssl and ssl_multi and show_s8 ? s8_a_m : na, title='S8 angle', color=s8_a_m > 0 ? color.new(red, 60) : color.new(green, 60), style=plot_type)


plotshape(show_ba and ba > 10 ? 1 : na, title='BA top', color=color.new(#ff0000, 0), style=shape.circle, location=location.top)

ema_cond2 = e3_a > ba and e3_a > 0 and s1_a < 0 and e2_a < 0 and e2_a < e2_a[1] and s4_a > s5_a
plotshape(show_ema and ema_cond2 ? 1 : na, title='counter trade', color=color.new(blue, 0), style=shape.circle, location=location.bottom)

ema_cond3 = e3_a < ba and e3_a < 0 and s1_a < -15 and e2_a < -8 and e2_a < e2_a[1] and s4_a < ba
plotshape(show_ema and ema_cond3 ? 1 : na, title='counter trade', color=color.new(lime, 0), style=shape.circle, location=location.bottom)

s1_cond_u = s1_a > 20 and s4_a > 15 and s1_a < s1_a[1] and s1_a > s4_a
plotshape(show_ssl and s1_cond_u ? 1 : na, title='counter trade', color=color.new(red, 0), style=shape.circle, location=location.top)

s1_cond_d = s1_a < -20 and s4_a < -15 and s1_a > s1_a[1] and s1_a < s4_a and s4_a < s5_a
plotshape(show_ssl and s1_cond_d ? 1 : na, title='counter trade', color=color.new(lime, 0), style=shape.circle, location=location.bottom)
s4_cond_d = s4_a < -15 and s1_a > s4_a
plotshape(show_ssl and s4_cond_d ? 1 : na, title='counter trade', color=color.new(blue, 0), style=shape.circle, location=location.bottom)

hline(0)
hline(10, color=color.new(#ffffff, 80))
hline(-10, color=color.new(#ffffff, 80))
hline(15, color=color.new(red, 80))
hline(-15, color=color.new(green, 80))
hline(20, color=color.new(red, 80))
hline(-20, color=color.new(green, 80))

