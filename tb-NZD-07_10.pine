//@version=4
study(title = "Trading Bot - NZD", shorttitle="TB - NZD",overlay=true)

show_entry = input(title="Show Entry", type=input.bool, defval=true)
show_atr = input(title="Show ATR", type=input.bool, defval=false)
show_plots = input(title="Show Plots", type=input.bool, defval=false)
show_bb= input(title="Show BB", type=input.bool, defval=true)
use_logic = input(title="Use Logic", type=input.bool, defval=false)

red = #ff0062
aqua = #00bcd4
yellow = #FFFF00
orange = #ff9800
green = #4caf50
lime = #00E676
white = #ffffff
blue = #42a5f5
gray = #707070
black = #000000
sell_color = color.new(#ff0062,20)
buy_color = color.new(#00c3ff,20)

angle(_src, len) =>
    rad2degree=180/3.14159265359  //pi 
    ang=rad2degree*atan((_src[0] - _src[1])/atr(len))

// Change
perc_change() =>
    perc = abs( (1 - (close[1] / close)) * 10000 )


// ===  EMA's ===
// ==================================================
ema_200 = ema(close, 200)
ema_200_angle = angle(ema_200,2)
ema_len_f = input(5, minval=1, title="EMA Length") //6
ema_fast = ema(close, ema_len_f)
ema_angle = angle(ema_fast,2)


// ===  ATR ===
// ==================================================
atrlen = 14 //input(14, "ATR Period")
atr_mult = input(1.15, "ATR Mult", step = 0.1) // 1.15
atr_stop = input(0.0003, "ATR Stop", step = 0.0001) // 0.001
atr_slen = wma(tr(true), atrlen)
atr_upper = atr_slen * atr_mult + close
atr_lower = close - atr_slen * atr_mult


// ===  Kijun ===
// ==================================================
middleDonchian(Length) =>
    lower = lowest(Length)
    upper = highest(Length)
    avg(upper, lower)

basePeriods = input(14, minval=1) //26
kijun =  middleDonchian(basePeriods)
k_angle = angle(kijun,3)
xChikou = close
xPrice = close


// ===  SSL ===
// ==================================================
ssl_len = input(60, minval=1,title="SSL Len") //75
ssl_len2 = input(45, minval=1,title="SSL 2")
ssl_len3 = input(8, minval=1,title="SSL 3") //7
SSL = wma(2*wma(close, ssl_len/2)-wma(close, ssl_len), round(sqrt(ssl_len)))
SSL2 = wma(2*wma(close, ssl_len2/2)-wma(close, ssl_len2), round(sqrt(ssl_len2)))
SSL3 = wma(2*wma(close, ssl_len3/2)-wma(close, ssl_len3), round(sqrt(ssl_len3)))
//ssl_angle = angle(SSL,3)
ssl_angle = angle(SSL2,2)
ssl_angle2 = angle(SSL3,2)
ssl_color = ssl_angle > 0 ? blue : red


// ===  BB ===
// ==================================================
BB_length = input(45, minval=1, maxval=150) // 23
BB_stdDev = input(2, minval=2.0, maxval=3)
sqz_length = input(100, minval=2, maxval=200) // 100
sqz_threshold = 76 
bb_s = kijun
basis = sma(close, BB_length)
dev = BB_stdDev * stdev(bb_s, BB_length)
bb_upper = basis + dev
bb_lower = basis - dev
bb_spread = bb_upper - bb_lower
avgspread = sma(bb_spread, sqz_length)
bb_squeeze = 0.00
bb_squeeze := bb_spread / avgspread * 100
basis_angle = angle(basis,3)
bbu_angle = angle(bb_upper,3)
bbl_angle = angle(bb_lower,3)
// BB Zones
bb_zone = bb_squeeze < 53 ? 0 : 
 bb_squeeze < sqz_threshold ? 1 : 
 bb_squeeze < 120 ? 2 :
 bb_squeeze < 181 ? 3 :
 bb_squeeze > 181 ? 4 : na
sqz_color = bb_zone == 0 ? #0045b3 :
 bb_zone == 1 ? #ff0062 : 
 bb_zone == 2 ?  gray : 
 bb_zone == 3 ?  #00c3ff : 
 bb_zone == 4 ? white: na

bb_zones_color =  sqz_color



// === RSI ===
// ==================================================
rsi_len = input(14, minval=1, title="Length")
up = rma(max(change(close), 0), rsi_len)
down = rma(-min(change(close), 0), rsi_len)
rsi = down == 0 ? 100 : up == 0 ? 0 : 100 - (100 / (1 + up / down))


// === Bollinger Bands %B ===
// ==================================================
length = 20 //input(45, minval=1)
multi = 2 //input(2.0, minval=0.001, maxval=50, title="StdDev")
bbr_basis = sma(close, length)
deviation = multi * stdev(close, length)
bbr_upper = bbr_basis + deviation
bbr_lower = bbr_basis - deviation
bbr = (close - bbr_lower)/(bbr_upper - bbr_lower)


// === Stochastic RSI ===
// ==================================================
smoothK = 3 //input(3, "K", minval=1)
smoothD = 3 //input(3, "D", minval=1)
lengthRSI = 14 //input(14, "RSI Length", minval=1)
lengthStoch = 14 //input(14, "Stochastic Length", minval=1)
rsi1 = rsi(close, lengthRSI)
stoch_k = sma(stoch(rsi1, rsi1, rsi1, lengthStoch), smoothK)
stoch_d = sma(stoch_k, smoothD)
stoch_hi = 80
stoch_low = 20


// === Stochastic Slow ===
// ==================================================
slow_len_k = input(14, minval=1), 
slow_len_d = input(3, minval=1)
slow_k = sma(stoch(close, high, low, slow_len_k), 3)
slow_d = sma(slow_k, slow_len_d)
slow_hi = 80
slow_low = 20
slow_k_angle= angle(slow_k,2)



// === MACD Crossover === 
// ==================================================
fastLength = input(8, minval=1) // 8 //8
slowLength = input(25,minval=1) // 16 // 21
signalLength=input(9,minval=1) // 11 // 5
fastMA = ema(close, fastLength)
slowMA = ema(close, slowLength)
macd_c = fastMA - slowMA
sig_c = sma(macd_c, signalLength)
pos = 0
pos := iff(sig_c < macd_c , 1,iff(sig_c > macd_c, -1, nz(pos[1], 0))) 
mc_color = pos == -1 ? red: pos == 1 ? green : blue
mc_angle = angle(macd_c,2)
s_angle = angle(sig_c,2)
mc_diff = macd_c / sig_c

//mcd_line = -0.000910 //-0.00105
currency = syminfo.currency
mch_line = 0.00420
mcbyp_line = 0.00364
mcu_line = syminfo.currency == 'NZD' ? 0.00170 : syminfo.currency == 'JPY' ? 0.50 : 0.12
mcm_line =-0.000820
mcd_line = syminfo.currency == 'NZD' ? -0.00170 : -0.50
mcl_line = -0.00420


// === Breakout/Consolidation Filter ===
// ==================================================
// ATRMultiple = input(4.0)
// BreakoutLength = input(6)
// Lookback = input(7)
// ATRPeriod = input(14)
// bc_line = 50//input(50) //27
// trueRange = atr(ATRPeriod)

// xColor = yellow
// for i = Lookback to 0
//     if ((close[i] - open[i+BreakoutLength]) > trueRange[i] * ATRMultiple)
//         xColor := green
//     if ( (open[i+BreakoutLength] - close[i]) > trueRange[i]*ATRMultiple)
//         xColor := red
// bc = percentrank(trueRange, 100)
// bc_angle = angle(bc,2)



// === WAE ===
// ==================================================
sensitivity = 150
wae_fast= 20 //8
wae_slow= 40 // 50
channelLength= 20 // 45
wae_mult = 2.0//1.85
wae_dz = nz(rma(tr(true),100)) * 3.7
calc_macd(source, wae_fast, wae_slow) =>
	a = ema(source, wae_fast)
	b = ema(source, wae_slow)
	a - b
calc_BBUpper(source, length, wae_mult) => 
	wae_basis = wma(source, length)
	wae_dev = wae_mult * stdev(source, length)
	t = wae_basis + wae_dev
	[t]
calc_BBLower(source, length, wae_mult) => 
	wae_basis = wma(source, length)
	wae_dev = wae_mult * stdev(source, length)
	t = wae_basis - wae_dev
	[t]

t1 = (calc_macd(close, wae_fast, wae_slow) - calc_macd(close[1], wae_fast, wae_slow))*sensitivity * 100
t2 = (calc_macd(close[2], wae_fast, wae_slow) - calc_macd(close[3], wae_fast, wae_slow))*sensitivity * 100
[e1a] = calc_BBUpper(close, channelLength, wae_mult)
[e1b] = calc_BBLower(close, channelLength, wae_mult)
wae_line = (e1a - e1b)
wae_diff = (wae_line - wae_dz) * 100
//wae_perc = (wae_line / wae_dz) * 100
trendUp = (t1 >= 0) ? t1 : 0
trendDown = (t1 < 0) ? (-1*t1) : 0
wae_color = #000000
if t1 >= 0
	wae_color := trendUp<trendUp[1] ? lime : green
if t1 < 0
	wae_color := trendDown<trendDown[1] ? orange : red




// === ADX + DI with SMA ===
// ==================================================
adx_len = 14//input(title="Length", type=integer, defval=14)
adx_line = 20// input(title="threshold", type=integer, defval=20)
adx_avg = 10// input(title="SMA", type=integer, defval=10)
var float smooth_tr = 0
var float smooth_di_plus = 0
var float smooth_di_minus = 0
TrueRange = max(max(high-low, abs(high-nz(close[1]))), abs(low-nz(close[1])))
DI_plus = high-nz(high[1]) > nz(low[1])-low ? max(high-nz(high[1]), 0): 0
DI_minus = nz(low[1])-low > high-nz(high[1]) ? max(nz(low[1])-low, 0): 0
smooth_tr := nz(smooth_tr[1]) - (nz(smooth_tr[1])/adx_len) + TrueRange
smooth_di_plus := nz(smooth_di_plus[1]) - (nz(smooth_di_plus[1])/adx_len) + DI_plus
smooth_di_minus := nz(smooth_di_minus[1]) - (nz(smooth_di_minus[1])/adx_len) + DI_minus

di_plus = smooth_di_plus / smooth_tr * 100
di_minus = smooth_di_minus / smooth_tr * 100
DX = abs(di_plus-di_minus) / (di_plus+di_minus)*100
adx = sma(DX, adx_len)
adx_sma = sma(adx, adx_avg)
adx_angle = (angle(adx,2))




// === Schaff Trend Cycle (STC) ===
// ==================================================
stc_signal = 12 // input(12,"Length")
stc_fast = 26 //input(26,"FastL/ength")
stc_slow = 55 //input(50,"SlowLength")
stc_line = 50 //(stc_high + stc_low) * 0.5
stc_up = 85
stc_high = 99
stc_down = 12
stc_low = 2
stc_macd(BBB, stc_fast, stc_slow) =>
    stc_fastMA = ema(BBB, stc_fast)
    stc_slowMA = ema(BBB, stc_slow)
    stc_macd = stc_fastMA - stc_slowMA
    stc_macd
    
AAAAA(stc_signal, stc_fast, stc_slow) => 
    AAA= 0.203 //input(0.203,"STC Sensitivity") // 0.25
    var CCCCC = 0.0
    var DDD = 0.0
    var DDDDDD = 0.0
    var EEEEE = 0.0
    tmp_macd = stc_macd(close,stc_fast,stc_slow)     
    lowest = lowest(tmp_macd, stc_signal)
    highest = highest(tmp_macd, stc_signal) - lowest    
    CCCCC := (highest > 0 ? ((tmp_macd - lowest) / highest) * 100 : nz(CCCCC[1])) 
    DDD := (na(DDD[1]) ? CCCCC : DDD[1] + (AAA * (CCCCC - DDD[1]))) 
    DDDD = lowest(DDD, stc_signal) 
    DDDDD = highest(DDD, stc_signal) - DDDD     
    DDDDDD := (DDDDD > 0 ? ((DDD - DDDD) / DDDDD) * 100 : nz(DDDDDD[1])) 
    EEEEE := (na(EEEEE[1]) ? DDDDDD : EEEEE[1] + (AAA * (DDDDDD - EEEEE[1])))
    EEEEE

stc = AAAAA(stc_signal,stc_fast,stc_slow)
stc_color = stc > stc[1] ? buy_color : sell_color




// === Plot === 
// ==================================================
// Angles
plot(basis_angle, color= white , title="Basis angle",style=plot.style_circles)
plot(bc,title="Breakout",style=plot.style_circles)
plot(bc_angle,title="BC Angle",style=plot.style_circles)
plot(wae_line,title="Explosion Line",style=plot.style_circles,color=wae_color)
plot(wae_dz,title="Dead Zone",style=plot.style_circles)
plot(adx_angle, color=white, title="ADX Angle",style=plot.style_circles)
plot(di_plus, color=green, title="DI+",style=plot.style_circles)
plot(di_minus, color=red, title="DI-",style=plot.style_circles)
plot(adx, color=yellow, title="ADX",style=plot.style_circles)
plot(adx_sma, color=white, title="ADX SMA",style=plot.style_circles)
plot(ssl_angle, color=white , title="SSL angle",style=plot.style_circles)
plot(ssl_angle2, color= white , title="SSL angle 2",style=plot.style_circles)
plot(mc_angle, color=yellow, title="MACD Angle",style=plot.style_circles)
plot(s_angle, color=yellow, title="Signal Angle",style=plot.style_circles)
plot(bbu_angle, color= white , title="BB Upper Angle",style=plot.style_circles)
plot(bbl_angle, color= white , title="BB Lower Angle",style=plot.style_circles)
plot(slow_k_angle, color= white , title="Slow K Angle",style=plot.style_circles)

//plot(k_angle, color= white , title="Kijun Angle",style=plot.style_circles)
//plot(ema_angle, color= white , title="EMA Fast Angle",style=plot.style_circles)
//plot(ema_200_angle, color= white , title="EMA 200 Angle",style=plot.style_circles)

plot(macd_c,title="MACD Crossover",color=mc_color)
plot(sig_c, color=red, title="SIGNAL Crossover")
plot(mc_diff, color=yellow, title="MACD Diff",style=plot.style_circles)
plot(stoch_k, color=blue, title="Stoch k",style=plot.style_circles)
plot(stoch_d, color=red, title="Stoch D",style=plot.style_circles)
plot(slow_k, color=blue, title="slow k",style=plot.style_circles)
plot(slow_d, color=red, title="slow D",style=plot.style_circles)
plot(bbr, "Bollinger Bands %B", color=color.teal,linewidth=0)
plot(rsi, "RSI", color=#8E1599)

//plot(SmthBulls,title="ASH Bulls",color=bull_trend_color,linewidth=4)
//plot(SmthBears,title="ASH Bears",color=bear_trend_color,linewidth=4)

//barcolor(bbr > 1.1 ? aqua : bbr < 0 ? aqua : na )
//barcolor(rsi > 70 ? aqua : rsi < 30 ? aqua : na )
barcolor(pos == -1 ? #ff0000: pos == 1 ? #00a000 : blue)

//plot(ssl_angle2, color= white , title="SSL2 angle",style=plot.style_circles)
//plot(dema_angle, color= white , title="Dema angle",style=plot.style_circles)
//plot(show_plots ? dema_histo : na, title="Dema Histo", color=dema_up ? green : red, style=plot.style_circles)

//plot(show_plots ? bb_zone : na, title="BB Zones", color=bb_zones_color, style=plot.style_circles,transp=20)
//plot(show_plots ? dema_signal: na, title="Dema Signal", color=bb_zones_color, style=plot.style_circles)
// plot(show_plots ? m_hist: na, title="MACD Histogram", style=plot.style_columns, color = m_color )
// plot(show_plots ? macd: na, title="MACD", color=col_macd)
// plot(show_plots ? m_signal: na, title="MACD Signal", color=col_signal)
// Kijun
plot(kijun, color=red, title="Kijun",linewidth=2)
// SSL
plot(SSL, color=white , title="SSL")
plot(SSL2, color=ssl_color , title="SSL 2")
plot(SSL3, color= green , title="SSL 3")
// BB
plot(basis, title="Basis", color=bb_zones_color,linewidth=3)
p1 = plot(show_bb ? bb_upper : na, "BB Upper ",color=bb_zones_color)
p2 = plot(show_bb ? bb_lower : na, "BB Lower ",color=bb_zones_color)
//fill(p1,p2, bb_zones_color)
// EMA 200 bands large
plot(ema_200, "EMA 200", color=color.new(yellow,50),linewidth=2 )
//e200_up = plot(show_plots ? ema_200_upper : na, title="EMA 200 Upper", color=color.new(#ffe676,70))
//e200_down = plot(show_plots ? ema_200_lower : na, title="EMA 200 Lower", color=color.new(#ffe676,70))
plot(show_plots ? ema_fast : na, color=color.lime, title="EMA Fast")
// ATR
plot(show_plots ? atr_upper : na, "+ATR Upper", color=color.new(#ffffff,85))
plot(show_plots ? atr_lower : na, "-ATR Lower", color=color.new(#ffffff,85))
plot(show_atr ? atr_upper + atr_stop : na, "+ATR stop", color=color.new(#ffff00,85))
plot(show_atr ? atr_lower - atr_stop : na, "-ATR stop", color=color.new(#ffff00,85))

var bool enter_exit = false
//var int trade_dir = 0
var int state = 0
var int bar_num = 0
var float lastPrice = 0.0
var mult_diff = 10000
var int num_pips = 15
var int num_bars = 3

entry_signal() =>
	candle = close > open ? 1 : 0
    dir = 0
    counter = 0
    allow = false

    // Special just below upper BB trend reversal
    if bb_zone == 3 and candle==1 and low>SSL2 and high < bb_upper and
     mc_color == red and adx>di_plus and di_minus<adx_line and
     wae_line>wae_dz and wae_color == green and
     macd_c > mcu_line
        dir := -1
        counter := 1

    // high percentage
    if bbl_angle < -10 and perc_change() > 15 and
     stoch_k > 50 and macd_c < mcd_line
        dir := -1

    // Uptrend
    if basis_angle > 0  //if basis > ema_200
        // Zone 0
        if bb_zone == 0 
            // Sell - May 30
            if candle == 1 and close > bb_upper and sig_c > 0 and
             wae_line > wae_dz and bc>bc_line and macd_c > mcu_line and adx > di_plus
                dir := -1
            // Sell
            if basis_angle < 0 and candle == 1 and ssl_angle < 4 and
             wae_line < wae_dz and bc<50 and atr_upper > bb_upper and macd_c > 0
                dir := -1

            // Counter - Buy
            if basis_angle < 0 and candle == 0 and close < bb_lower and 
             wae_line < wae_dz and wae_color == red and bc<50 and macd_c < mcd_line
                dir := 1
                counter := 1

       
        if bb_zone > 0
             // MACD high
            if macd_c > mch_line and candle == 1 and close > bb_upper and close > SSL3 and
             sig_c > mcu_line and bc>bc_line and s_angle < 5
                dir := -1

            // MACD up 
            if candle == 1 and close > bb_upper and close > SSL3 and
             macd_c > mcu_line and SSL2>SSL and wae_line > wae_dz and bc>bc_line
             and not(bb_zone == 3 and bc[2]<bc_line) and mc_color == green
                dir := -1
            
            // Counter
            if bb_zone > 1 and candle == 0 and close < bb_lower and
             wae_line > wae_dz and macd_c < mcd_line and adx > di_plus
                dir := 1
                counter := 1

        // Zone 4 counter
        if bb_zone == 4
            // Below ema 200 - Buy Jun 22
            if candle == 0 and low < ema_200 and basis_angle > 0 and
             wae_line < wae_dz and macd_c < 0 and bc<51
                dir := 1
                counter := 1
           // Below bb_lower - Buy Jun 16
            if candle == 0 and low < bb_lower and
             wae_line > wae_dz and macd_c < 0 and bc>51
                dir := 1
                counter := 1


    if basis_angle < 0  //if basis < ema_200

        if candle == 0 and close < bb_lower and 
         di_minus > 32 and SSL2<SSL
            dir := 1


    // Filter out same direction trades, wait 4 candles
    // and price higher than 20 pips before allowing another trade
    if dir == state and use_logic
        allow := (bar_index - bar_num) > num_bars ? true : false
        // sell
        if state == -1
            dir := ((close - lastPrice) * mult_diff) > num_pips and allow ? dir : 0
        // buy
        if state == 1
            dir := ((close - lastPrice) * mult_diff) < (num_pips * -1) and allow ? dir : 0

    //var int enter_exit = basis_angle > 0 and dir == -1 ? 0 : basis_angle < 0 and dir
    type = dir > 0 ? 1 : dir < 0 ? -1 : 0
    [type,counter]

[trade_dir,counter] = entry_signal() 

if trade_dir != 0 and use_logic
    state := trade_dir

    enter_exit := counter == 1 ? 1 : 0
    // Sell
    // if basis_angle > 0 and trade_dir == -1
    //     enter_exit := -1
    // // Counter
    // if basis_angle > 0 and trade_dir == 1
    //     enter_exit := 0
    // // Buy
    // if basis_angle < 0 and trade_dir == 1
    //     enter_exit := -1
    // // Counter
    // if basis_angle < 0 and trade_dir == -1
    //     enter_exit := 0
    // if basis>ema_200
    //     enter_exit := trade_dir == -1 ? false : trade_dir == 1 or counter ? true : false
    // if basis<ema_200
    //     enter_exit := trade_dir == 1 ? false : trade_dir == -1 or counter ? true : false
    //enter_exit := counter == 1 ? true : enter_exit == false ? true : false
    lastPrice := close
    bar_num   := bar_index

// if enter_exit != trade_dir and trade_dir != 0
//     enter_exit := enter_exit == false ? true : false

trade_color = trade_dir > 0  ? buy_color : sell_color
plot(counter,title="Counter Trade", style=plot.style_circles)
plot(state,"State",style=plot.style_circles)

plotshape(show_entry and trade_dir == 1 and enter_exit == 0 ? 1 : na, title="Entry Buy", color=trade_color, location = location.belowbar, style=shape.labelup, text="B", textcolor=color.white, size=size.small)
plotshape(show_entry and trade_dir == 1 and enter_exit == -1 ? 1 : na, title="Exit Buy", color=trade_color, location = location.belowbar, style=shape.labelup, text="Exit", textcolor=color.white, size=size.small)
plotshape(show_entry and trade_dir == -1 and enter_exit == 0 ? -1: na, title="Entry Sell", color=trade_color, location = location.abovebar, style=shape.labeldown, text="S", textcolor=color.white, size=size.small)
plotshape(show_entry and trade_dir == -1 and enter_exit == -1 ? -1 : na, title="Exit Sell", color=trade_color, location = location.abovebar, style=shape.labeldown, text="Exit", textcolor=color.white, size=size.small)


