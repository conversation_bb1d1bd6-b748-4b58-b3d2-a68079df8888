//@version=4
study(title="SSL Angles", shorttitle="SSL Angles")

// ATR plot
input_angle = input(100, "Angle degree") // 14
basis_len = input(20, "Basis Length") // 20
basis_len2 = input(40, "Basis Length 2") // 40
basis_len3 = input(100, "Basis Length 3") // 100
s1_sma_input = input(14, "s1 SMA") // 100
show_ema = input(title="Show EMA's", type=input.bool, defval=true)
show_ba = input(title="Show BA's", type=input.bool, defval=false)
show_s1 = input(title="Show S1", type=input.bool, defval=true)
show_s3 = input(title="Show S3", type=input.bool, defval=false)
show_s4 = input(title="Show S4", type=input.bool, defval=false)
show_s5 = input(title="Show S5", type=input.bool, defval=false)
show_s6 = input(title="Show S6", type=input.bool, defval=false)
show_s7 = input(title="Show S7", type=input.bool, defval=false)

red = #ff0062
aqua = #00bcd4
yellow = #FFFF00
orange = #ff9800
green = #4caf50
lime = #00E676
white = #ffffff
c_hide = color.new(#ffffff,100)
blue = #42a5f5
violet = #814dff
gray = #707070
black = #000000
sell_color = color.new(#ff0062,20)
buy_color = color.new(#00c3ff,20)

// If its in the green ema20 must be lower than ema_50??

angle(_src, len) =>
    rad2degree=180/3.14159265359  //pi 
    ang=rad2degree*atan((_src[0] - _src[1])/atr(len))

// ssl_angles(len,type) =>
//     obj_angle = 

//     if smooth==true
//         obj_angle 

//     [value]

// [value] = ssl_angles(len1)

// Basis
basis = sma(close, basis_len)
ba = angle(basis,input_angle)
plot(show_ba? ba:na, title="Basis angle",color=show_ba==true?orange:color.new(orange,100))

basis2 = sma(close, basis_len2)
ba2 = angle(basis2,input_angle)
plot(show_ba? ba2:na, title="Basis angle2",color=show_ba==true?yellow:color.new(yellow,100))

basis3 = sma(close, basis_len3)
ba3 = angle(basis3,input_angle)
plot(show_ba? ba3:na, title="Basis angle2",color=show_ba==true?blue:color.new(blue,100))

// EMA
ema_20 = ema(close, 20)
e20_a = angle(ema_20,input_angle)
plot(show_ema?e20_a:na, title="EMA 20 angle",color=e20_a<ba?aqua:#3179f5)

ema_50 = ema(close, 50)
e50_a = angle(ema_50,input_angle)
plot(show_ema?e50_a:na, title="EMA 50 angle",color=e50_a>ba?#cd1beb:e50_a<ba and e50_a>0?yellow:green)

ema_200 = ema(close,200)
ea = angle(ema_200,input_angle)
plot(show_ema?ea:na, title="EMA 200 angle",color=color.new(blue,100))

// SSL
s1_len = input(20, "S1") // 30
s1 = wma(2*wma(close, s1_len/2)-wma(close, s1_len), round(sqrt(s1_len)))
s1_a = angle(s1,input_angle)
s1_sma = sma(s1_a,s1_sma_input)
s1_color  = s1_a > 0 ? red : green
plot(show_s1?s1_a:na, title="s1 angle",color=color.new(s1_color,50),linewidth=2)
plot(show_s1?s1_sma:na, title="s1 SMA",color=s1_color,linewidth=2)

s3_len = input(8, "S3")
s3 = wma(2*wma(close, s3_len/2)-wma(close, s3_len), round(sqrt(s3_len)))
s3_a = angle(s3,input_angle)
plot(show_s3?s3_a:na, title="S3 angle",color=white)

// s3b_len = input(15, "S3b")
// s3b = wma(2*wma(close, s3b_len/2)-wma(close, s3b_len), round(sqrt(s3b_len)))
// s3b_a = angle(s3b,input_angle)
// plot(show_s3?s3b_a:na, title="S3B angle",color=blue)

s4_len = 75
s4 = wma(2*wma(close, s4_len/2)-wma(close, s4_len), round(sqrt(s4_len)))
s4_a = angle(s4,input_angle)
plot(show_s4?s4_a:na, title="S4 angle",color=show_s4==true?yellow:color.new(yellow,100))

s5_len = 100
s5 = wma(2*wma(close, s5_len/2)-wma(close, s5_len), round(sqrt(s5_len)))
s5_a = angle(s5,input_angle)
plot(show_s5?s5_a:na, title="S5 angle",color=s5_a>0?green:red)

s6_len = 200
s6 = wma(2*wma(close, s6_len/2)-wma(close, s6_len), round(sqrt(s6_len)))
s6_a = angle(s6,input_angle)
plot(show_s6?s6_a:na, title="S6 angle",color=show_s6==true?green:color.new(green,100))

s7_len = 300
s7 = wma(2*wma(close, s7_len/2)-wma(close, s7_len), round(sqrt(s7_len)))
s7_a = angle(s7,input_angle)
plot(show_s7?s7_a:na, title="S7 angle",style=plot.style_columns, color=s7_a>0 ? color.new(red,60) : color.new(green,60) )
//plot(s7_a, title="S7 angle",color=show_s7==true?green:color.new(green,100))

s4_cross = abs(s4_a - s5_a)
plot(s4_cross and show_ema==false?s4_cross:na, title="S4 Cross",color=color.new(yellow,100))

s4_s5_dist = abs(s4 - s5) * 1000
plot(s4_s5_dist and show_ema==false?s4_s5_dist:na, title="S4 Dist",color=color.new(yellow,100))
// s1 higher than s5

plotshape(ba>10?1:na,title="BA top",color=#ff0000,style=shape.circle,location=location.top)

// ema_cond1 = s3_a<-10 and s3b_a<-10 and e20_a<0 and e50_a>0
// plotshape(ema_cond1?1:na, title='counter trade', color=orange, style=shape.circle,location=location.bottom)
ema_cond2 = e50_a>ba and e50_a>0 and s1_a<0 and e20_a<0 and e20_a<e20_a[1] and s4_a>s5_a
plotshape(ema_cond2?1:na, title='counter trade', color=blue, style=shape.circle,location=location.bottom)

ema_cond3 = e50_a<ba and e50_a<0 and s1_a<-15 and e20_a<-8 and e20_a<e20_a[1] and s4_a<ba
plotshape(ema_cond3?1:na, title='counter trade', color=lime, style=shape.circle,location=location.bottom)

// RSI 3
// basis = sma(close, 100)
// ba = angle(basis,input_angle)
// plot(ba, title="Basis angle",color=green)

hline(0)
hline(10,color=color.new(#ffffff,50)) 
hline(-10,color=color.new(#ffffff,50))
hline(15,color=color.new(red,50)) 
hline(-15,color=color.new(green,50))
