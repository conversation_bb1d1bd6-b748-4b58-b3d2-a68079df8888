//@version=4
study(title = "Trading Bot - JPY - Jan 23 30m", shorttitle="TB - JPY - 01_23 - 30M",overlay=true,max_labels_count=500)

show_entry = input(title="Show Entry", type=input.bool, defval=true)
show_atr = input(title="Show ATR", type=input.bool, defval=true)
show_cond = input(title="Show Conditions", type=input.bool, defval=true)
use_logic = input(title="Use Logic", type=input.bool, defval=false)
show_friday= input(title="Show Friday", type=input.bool, defval=true)
rsi_strong= input(title="Show only RSI strongest", type=input.bool, defval=false)
rsi1_show = input(title="Show RSI1", type=input.bool, defval=false)
rsi2_show = input(title="Show RSI2", type=input.bool, defval=true)
rsi3_show = input(title="Show RSI3", type=input.bool, defval=false)
show_bbr = input(title="Show BBR candles",type=input.bool,defval=false)
show_r3 = input(title="Show R3 down",type=input.bool,defval=false)
use_filter_up = input(title="Use Filter Uptrend",type=input.bool,defval=false)

red = #ff0062
aqua = #00bcd4
yellow = #FFFF00
orange = #ff9800
green = #4caf50
lime = #00E676
white = #ffffff
c_hide = color.new(#ffffff,100)
blue = #42a5f5
violet = #814dff
gray = #707070
black = #000000
sell_color = color.new(#ff0062,20)
buy_color = color.new(#00c3ff,20)

friday = color.new(sell_color,92)
bgcolor(show_friday and dayofweek == 1 ? friday : na)

angle(_src, len) =>
    rad2degree=180/3.14159265359  //pi 
    ang=rad2degree*atan((_src[0] - _src[1])/atr(len))

// Change
perc_change() =>
    perc = abs( (1 - (close[1] / close)) * 10000 )


// ===  EMA's ===
// ==================================================
ema_200 = ema(close, 200)
e200_a = angle(ema_200,3)
ea_sc = e200_a<5 ? color.new(orange,50): e200_a>5 ? color.new(#ff0000,50) : na
ea_bc = e200_a>-5 ? color.new(#0000ff,50): e200_a<-5 ? color.new(#00ff00,50) : na

// ===  ATR ===
// ==================================================
atrlen = 14 //input(14, "ATR Period")
atr_mult = input(2.0, "ATR Mult", step = 0.1) // 1.35 1.15
atr_stop = 0.0003 //input(0.0003, "ATR Stop", step = 0.0001) // 0.001
atr_slen = wma(tr(true), atrlen)
atr_upper = atr_slen * atr_mult + close
atr_lower = close - atr_slen * atr_mult


// ===  Kijun ===
// ==================================================
middleDonchian(Length) =>
    lower = lowest(Length)
    upper = highest(Length)
    avg(upper, lower)

basePeriods = input(5, title="kijun Len",minval=1) // 14 26
kijun =  middleDonchian(basePeriods)
k_angle = angle(kijun,3)
xChikou = close
xPrice = close


// ===  BB ===
// ==================================================
BB_length = input(20) // 45 100 125 45 23
BB_stdDev = 2 //input(2, minval=2.0, maxval=3)
sqz_length = 100 //input(100, minval=2, maxval=200) // 100
sqz_threshold = 76 
bb_s = kijun
basis = sma(close, BB_length)
dev = BB_stdDev * stdev(close, BB_length)
bb_upper = basis + dev
bb_lower = basis - dev
bb_spread = bb_upper - bb_lower
avgspread = sma(bb_spread, sqz_length)
bb_squeeze = 0.00
bb_squeeze := bb_spread / avgspread * 100
bb_diff = (bb_upper - bb_lower) * 10
basis_angle = angle(basis,3)
bbu_angle = angle(bb_upper,3)
bbl_a = angle(bb_lower,3)
// BB Zones
// old settings 181 z3
bb_zone = bb_squeeze < 53 ? 0 : 
 bb_squeeze < sqz_threshold ? 1 : 
 bb_squeeze < 120 ? 2 :
 bb_squeeze < 160 ? 3 :
 bb_squeeze > 160 ? 4 : na
sqz_color = bb_zone == 0 ? #0045b3 :
 bb_zone == 1 ? #ff0062 : 
 bb_zone == 2 ?  gray : 
 bb_zone == 3 ?  #00c3ff : 
 bb_zone == 4 ? white: na

bb_zones_color =  sqz_color


// == Basis to EMA
// =====================
be_mult = syminfo.currency == 'JPY' ? 100 : syminfo.currency == 'NZD' ? 10000 : 10000
bbu_dist = (bb_upper - ema_200) * be_mult
close_dist = (close - ema_200) * be_mult
be_dist = (basis - ema_200) * be_mult
bbl_dist = (bb_lower - ema_200) * be_mult
be_high = 50
be_up = 10
be_down = -10
be_low = -50
dist_angle = angle(abs(be_dist),5) // 2

be_color = be_dist>0?color.red : color.green 


// ===  SSL ===
// ==================================================
ssl_len1 = input(45, minval=1,title="SSL 1") // 45
ssl_len2 = input(60, minval=1,title="SSL 2") //75
ssl_len3 = 8//input(8, minval=1,title="SSL 3") //
ssl_len3_b = 15//input(15, minval=1,title="SSL 3 b") // 8 7
ssl_len4 = input(75, minval=1,title="SSL 4") // 75 100
ssl_len5 = input(150, minval=1,title="SSL 5") //7
s1 = wma(2*wma(close, ssl_len1/2)-wma(close, ssl_len1), round(sqrt(ssl_len1)))
s2 = wma(2*wma(close, ssl_len2/2)-wma(close, ssl_len2), round(sqrt(ssl_len2)))
s3 = wma(2*wma(close, ssl_len3/2)-wma(close, ssl_len3), round(sqrt(ssl_len3)))
s3b = wma(2*wma(close, ssl_len3_b/2)-wma(close, ssl_len3_b), round(sqrt(ssl_len3_b)))
s4 = wma(2*wma(close, ssl_len4/2)-wma(close, ssl_len4), round(sqrt(ssl_len4)))
s5 = wma(2*wma(close, ssl_len5/2)-wma(close, ssl_len5), round(sqrt(ssl_len5)))
// angles
s1_a  = angle(s1,2)
s2_a = angle(s2,2)

s3_a = angle(s3,3)
s3b_a = angle(s3b,3)
s3_high = 23
s3_low = -23

s4_a = angle(s4,3)
s5_a = angle(s5,3)
//s4_dist = basis_angle>0 ? (bb_upper-s4)*100 : (s4 - bb_lower)*100 
basis_dist = basis_angle>0 ? (ema_200-basis)*100 : (basis - ema_200)*100 
ssl_color  = s1_a > 0 ? blue : red



// === WAE ===
// ==================================================
sensitivity = 150
wae_fast= 20 //8
wae_slow= 40 // 50
channelLength= 20 // 45
wae_mult = 2.0//1.85
wae_dz = nz(rma(tr(true),100)) * 3.7
calc_macd(source, wae_fast, wae_slow) =>
	a = ema(source, wae_fast)
	b = ema(source, wae_slow)
	a - b
calc_BBUpper(source, length, wae_mult) => 
	wae_basis = wma(source, length)
	wae_dev = wae_mult * stdev(source, length)
	t = wae_basis + wae_dev
	[t]
calc_BBLower(source, length, wae_mult) => 
	wae_basis = wma(source, length)
	wae_dev = wae_mult * stdev(source, length)
	t = wae_basis - wae_dev
	[t]

t1 = (calc_macd(close, wae_fast, wae_slow) - calc_macd(close[1], wae_fast, wae_slow))*sensitivity * 100
t2 = (calc_macd(close[2], wae_fast, wae_slow) - calc_macd(close[3], wae_fast, wae_slow))*sensitivity * 100
[e1a] = calc_BBUpper(close, channelLength, wae_mult)
[e1b] = calc_BBLower(close, channelLength, wae_mult)
wae_line = (e1a - e1b)
wae_diff = (wae_line - wae_dz) * 100
//wae_perc = (wae_line / wae_dz) * 100
trendUp = (t1 >= 0) ? t1 : 0
trendDown = (t1 < 0) ? (-1*t1) : 0
wae_color = #000000
if t1 >= 0
	wae_color := trendUp<trendUp[1] ? lime : green
if t1 < 0
	wae_color := trendDown<trendDown[1] ? orange : red
wae_angle = angle(wae_line,3)



// === ADX + DI with SMA ===
// ==================================================
adx_len = input(9,title="ADX len") // 14
adx_line = 20 // input(title="threshold", defval=20)
adx_avg = input(8,title="ADX SMA") // 10
var float adx_top = 54 // input(55,title="High")
var float adx_high = 38 // 39
var float adx_mid = 33
var float adx_center = 20
var float adx_low = 12
var float smooth_tr = 0
var float smooth_di_plus = 0
var float smooth_di_minus = 0
TrueRange = max(max(high-low, abs(high-nz(close[1]))), abs(low-nz(close[1])))
DI_plus = high-nz(high[1]) > nz(low[1])-low ? max(high-nz(high[1]), 0): 0
DI_minus = nz(low[1])-low > high-nz(high[1]) ? max(nz(low[1])-low, 0): 0
smooth_tr := nz(smooth_tr[1]) - (nz(smooth_tr[1])/adx_len) + TrueRange
smooth_di_plus := nz(smooth_di_plus[1]) - (nz(smooth_di_plus[1])/adx_len) + DI_plus
smooth_di_minus := nz(smooth_di_minus[1]) - (nz(smooth_di_minus[1])/adx_len) + DI_minus

di_plus = smooth_di_plus / smooth_tr * 100
di_minus = smooth_di_minus / smooth_tr * 100
DX = abs(di_plus-di_minus) / (di_plus+di_minus)*100
adx = sma(DX, adx_len)
adx_sma = sma(adx, adx_avg)
adx_angle = (angle(adx,2))
//hline(adx_line, color=black, linestyle=dashed)


// === RSI Wicks ===
// ==================================================
std         = false //input(false, title="Show Standard RSI")
rsi_candles = false //input(true,  title="Show Candles")
wicks       = true  //input(true,  title="Wicks based on stand-alone RSI")
src_close   = close
src_open    = open
src_high    = high
src_low     = low 

rsiw_len1    = input(20, title="RSIW len1") // 23 25 28 8 10 14
rsiw_len2    = input(12, title="RSIW len2") // 8 10 14
rsiw_len3    = input(4, title="RSIW len3") // 8 10 14

rsi_wicks(rsi_len) =>
    norm_close  = avg(src_close,src_close[1])
    gain_loss_close   = change(src_close)/norm_close
    RSI_close         = 50+50*rma(gain_loss_close, rsi_len)/rma(abs(gain_loss_close), rsi_len)

    norm_open = if wicks==true 
        avg(src_open,src_open[1])
    else 
        avg(src_close,src_close[1])
    gain_loss_open   = change(src_open)/norm_open
    RSI_open         = 50+50*rma(gain_loss_open, rsi_len)/rma(abs(gain_loss_open), rsi_len)
            
    norm_high = if wicks==true 
        avg(src_high,src_high[1])
    else 
        avg(src_close,src_close[1])
    gain_loss_high   = change(src_high)/norm_high
    RSI_high         = 50+50*rma(gain_loss_high, rsi_len)/rma(abs(gain_loss_high), rsi_len)
            
    norm_low  = if wicks==true
        avg(src_low,src_low[1])
    else 
        avg(src_close,src_close[1])
    gain_loss_low   = change(src_low)/norm_low
    RSI_low         = 50+50*rma(gain_loss_low, rsi_len)/rma(abs(gain_loss_low), rsi_len)

    // Sell
    rws_weak = RSI_close<70 and RSI_high>70 and rsi_strong==0? 1 : 0
    rws_mid = RSI_close>70 and RSI_open<70 and rsi_strong==0? 1 : 0 
    rws_strong = RSI_open>70 ? 1 : 0

    // Buy
    rwb_weak = RSI_close>30 and RSI_low<30 and rsi_strong==0? 1 : 0
    rwb_mid = RSI_close<30 and RSI_open>30 and rsi_strong==0? 1 : 0
    rwb_strong = RSI_open<30 ? 1 : 0

    [rws_mid,rws_strong,rwb_mid,rwb_strong,RSI_close,RSI_high,RSI_low]

[rws_mid1,rws_strong1,rwb_mid1,rwb_strong1,rsi_close1,rsi_high1,rsi_low1] = rsi_wicks(rsiw_len1)
[rws_mid2,rws_strong2,rwb_mid2,rwb_strong2,rsi_close2,rsi_high2,rsi_low2] = rsi_wicks(rsiw_len2)
[rws_mid3,rws_strong3,rwb_mid3,rwb_strong3,rsi_close3,rsi_high3,rsi_low3] = rsi_wicks(rsiw_len3)




// === Bollinger Bands %B ===
// ==================================================
bbr_fun(len) =>
    multi = 2 
    b = sma(close, len)
    d = multi * stdev(close, len)
    upper = b + d
    lower = b - d
    bbr = (close - lower)/(upper - lower)
    [bbr]

[bbr] = bbr_fun(14)
[bbr2] = bbr_fun(40)



// === RSI Candles - glaz ===
// ==================================================
var int pos = 0
var rsi_color = #000000
rsi_len = input(14, minval=1, title="RSI bar length")
rsi_up = rma(max(change(close), 0), rsi_len)
rsi_down = rma(-min(change(close), 0), rsi_len)
rsi = rsi_down == 0 ? 100 : rsi_up == 0 ? 0 : 100 - (100 / (1 + rsi_up / rsi_down))
isup() => rsi > 70
isdown() => rsi < 30 
// plot(rsi,title="RSI",color=color.blue)
rsi_color := isup() ? color.new(#ff0000, 0) : isdown() ? color.new(color.purple, 0)  : na




// === MACD ===
// ==================================================
fast_length = 8 // 12
slow_length = 20 // 26
md_src = close
signal_length = 9
sma_source = true
sma_signal = true
// Plot colors
col_grow_above = #26A69A
col_fall_above = #B2DFDB
col_grow_below = #FFCDD2
col_fall_below = #EF5350
col_macd = #0094ff
col_signal = #ff6a00
// Calculating
md_fast_ma = sma_source ? sma(md_src, fast_length) : ema(md_src, fast_length)
slow_ma = sma_source ? sma(md_src, slow_length) : ema(md_src, slow_length)
macd = md_fast_ma - slow_ma
macd_s = sma_signal ? sma(macd, signal_length) : ema(macd, signal_length)
m_histo = macd - macd_s
// plot(hist, title="Histogram", style=plot.style_columns, color=(hist>=0 ? (hist[1] < hist ? col_grow_above : col_fall_above) : (hist[1] < hist ? col_grow_below : col_fall_below) ), transp=0 )
// plot(macd, title="MACD", color=col_macd, transp=0)
// plot(signal, title="Signal", color=col_signal, transp=0)

// === Schaff Trend Cycle (STC) ===
// ==================================================
stc_signal = 12 // input(12,"Length")
stc_fast = 26 //input(26,"FastL/ength")
stc_slow = 50 //input(55,"SlowLength") //55
stc_line = 50 //(stc_high + stc_low) * 0.5
stc_bias = 0.5 // input(0.203,"STC Sensitivity") // 0.203

stc_high = 92
stc_up = 80 //  85 75
stc_center = 50
stc_down = 12 //12 25
stc_low = 5
stc_macd(BBB, stc_fast, stc_slow) =>
    stc_fastMA = ema(BBB, stc_fast)
    stc_slowMA = ema(BBB, stc_slow)
    stc_macd = stc_fastMA - stc_slowMA
    stc_macd
    
AAAAA(stc_signal, stc_fast, stc_slow) => 
    AAA= stc_bias
    var CCCCC = 0.0
    var DDD = 0.0
    var DDDDDD = 0.0
    var EEEEE = 0.0
    tmp_macd = stc_macd(close,stc_fast,stc_slow)     
    lowest = lowest(tmp_macd, stc_signal)
    highest = highest(tmp_macd, stc_signal) - lowest    
    CCCCC := (highest > 0 ? ((tmp_macd - lowest) / highest) * 100 : nz(CCCCC[1])) 
    DDD := (na(DDD[1]) ? CCCCC : DDD[1] + (AAA * (CCCCC - DDD[1]))) 
    DDDD = lowest(DDD, stc_signal) 
    DDDDD = highest(DDD, stc_signal) - DDDD     
    DDDDDD := (DDDDD > 0 ? ((DDD - DDDD) / DDDDD) * 100 : nz(DDDDDD[1])) 
    EEEEE := (na(EEEEE[1]) ? DDDDDD : EEEEE[1] + (AAA * (DDDDDD - EEEEE[1])))
    EEEEE

stc = AAAAA(stc_signal,stc_fast,stc_slow)
stc_color = stc > stc[1] ? buy_color : sell_color



// === Stochastic Momentum Index ===
// ==================================================
k_len = 8 // input(10, "Percent K Length") // 10 12
d_len = 3 // input(3, "Percent D Length")
d_len2 = 2 //input(2, "Percent D Length 2")
mom_high = 45 //input(45, "Overbought")
mom_low = -45 //input(-45, "Oversold")
// Range Calculation
ll = lowest (low, k_len)
hh = highest (high, k_len)
diff = hh - ll
rdiff = close - (hh+ll)/2
avgrel = ema(ema(rdiff,d_len),d_len)
avgdiff = ema(ema(diff,d_len),d_len)

// SMI
SMI = avgdiff != 0 ? (avgrel/(avgdiff/2)*100) : 0
mom_sig = ema(SMI,d_len)
mom_ema = ema(SMI, k_len)
mom_angle = angle(mom_ema,2)
c_sma = mom_sig > mom_high or mom_sig < mom_low ? color.blue : na
// SMI 2
avgrel2 = ema(ema(rdiff,d_len2),d_len2)
avgdiff2 = ema(ema(diff,d_len2),d_len2)
SMI2 = avgdiff != 0 ? (avgrel2/(avgdiff2/2)*100) : 0
mom_sig2 = ema(SMI2,d_len2)
c_sma2 = mom_sig2 > mom_high or mom_sig2 < mom_low ? color.new(color.white,50) : na


// === RES - Ranging EMA Spread  ===
// ==================================================
ema1length = 12 // 40 12
ema2length = 50 // 100 43
ranginglength = 3
rangingmaxvalue = 0.12// 0.14 0.1
rangingminvalue = -0.1
enablebarcolors = false


// Rangin EMA spread
ema1 = ema(close, ema1length)
ema2 = ema(close, ema2length)
spread = ((ema2 / ema1) -1) * 100
r1 = (spread >= rangingminvalue and spread <= rangingmaxvalue) 
r2 = (spread[1] >= rangingminvalue and spread[1] <= rangingmaxvalue)
r3 = (spread[2] >= rangingminvalue and spread[2] <= rangingmaxvalue)
r4 = (spread[3] >= rangingminvalue and spread[3] <= rangingmaxvalue)
r5 = (spread[4] >= rangingminvalue and spread[4] <= rangingmaxvalue)

ranging = false

if (ranginglength == 1) 
    ranging := r1
if (ranginglength == 2) 
    ranging := r1 and r2
if (ranginglength == 3) 
    ranging := r1 and r2 and r3
if (ranginglength == 4) 
    ranging := r1 and r2 and r3 and r4
if (ranginglength == 5) 
    ranging := r1 and r2 and r3 and r4 and r5

res = spread > 0 ? 0-spread : abs(spread)
res := res * 100
res_weak = gray // grey
res_mid = blue// navy
res_strong = aqua // aqua
res_c = ranging ? res_weak : spread > spread[1] ? res_mid : res_strong
// Targets
res_top = 14
res_high = 6
res_low = -6
res_bottom = -14


// === Plot === 
// ==================================================
// Angles
//plot(bb_squeeze, color=bb_zones_color, title="BB Squeeze",style=plot.style_circles)
plot(res, color=res_c, title="RES",style=plot.style_circles)
plot(bbr,title='BBR', style=plot.style_circles)
plot(bbr2,title='BBR 2', style=plot.style_circles)
plot(mom_ema, color= #ff0000 , title="Mom",style=plot.style_circles)
//plot(stc, color= c_hide , title="STC",style=plot.style_circles)
//plot(m_histo,title='MACD Histo', style=plot.style_circles)
//plot( bb_diff,title="BB diff", style=plot.style_circles)
plot(e200_a, color= e200_a>0?ea_sc:ea_bc , title="EMA 200 Angle",style=plot.style_circles)
plot(basis_angle, color= c_hide , title="Basis angle",style=plot.style_circles)
plot(s1_a, color=c_hide , title="SSL angle",style=plot.style_circles)
plot(s2_a, color=c_hide , title="SSL angle 2",style=plot.style_circles)
plot(s3_a, color=c_hide , title="SSL angle 3",style=plot.style_circles)
plot(s3b_a, color=c_hide , title="SSL angle 3 b",style=plot.style_circles)

plot(s4_a, color=c_hide , title="SSL angle 4",style=plot.style_circles)
plot(s5_a, color=c_hide , title="SSL angle 5",style=plot.style_circles)
//plot(bbu_angle, color= c_hide , title="BB Upper Angle",style=plot.style_circles)
plot(bbl_a, color= c_hide , title="BB Lower Angle",style=plot.style_circles)
//plot(s4_dist,title="s4 Dist",style=plot.style_circles,color=color.new(color.black,100) )
plot(basis_dist,title="Basis Dist",style=plot.style_circles,color=color.new(color.black,100) )
//plot(rsi_close1,title="RSI close1",style=plot.style_circles)
//plot(rsi_close2,title="RSI close2",style=plot.style_circles)
//plot(rsi_close3,title="RSI close3",style=plot.style_circles)


// plot(be_dist, color= c_hide , title="BE Dist",style=plot.style_circles)
// plot(mom_stoch, color= c_hide , title="MOM smi",style=plot.style_circles)
// plot(mom_ema, color= c_hide , title="MOM ema",style=plot.style_circles)
plot(di_plus, color=green, title="DI+",style=plot.style_circles)
plot(di_minus, color=red, title="DI-",style=plot.style_circles)
plot(adx, color=yellow, title="ADX",style=plot.style_circles)
plot(adx_sma, color=c_hide, title="ADX SMA",style=plot.style_circles)
plot(wae_line,title="Explosion Line",style=plot.style_circles,color=wae_color)
plot(wae_dz,title="Dead Zone",style=plot.style_circles)
plot(trendUp* 0.100,title="WAE Trend Up",style=plot.style_circles)
//plot(wae_angle,title="WAE angle",style=plot.style_circles)

plot(macd_s,title='MACD Sig', style=plot.style_circles)


// Kijun
plot(kijun, color=#f44336, title="Kijun",linewidth=2)
// SSL
plot(s1, color=ssl_color , title="SSL 1", linewidth=2)
plot(s2, color=orange , title="SSL 2")
plot(s3, color=aqua , title="SSL 3")
plot(s4, color=yellow , title="SSL 4",linewidth=2)
s5_col = s5>bb_upper?#ff0000 : s5<bb_lower ? #00ddff : green
plot(s5, color=s5_col , title="SSL 5",linewidth=2)

// BB
plot(basis, title="Basis", color=bb_zones_color,linewidth=3)
p1 = plot(bb_upper, "BB Upper ",color=bb_zones_color,linewidth=2)
p2 = plot(bb_lower, "BB Lower ",color=bb_zones_color,linewidth=2)
//fill(p1,p2, bb_zones_color)
// EMA 200 bands large
plot(ema_200, "EMA 200", color=e200_a>0?ea_sc:ea_bc,linewidth=2 )
//plot(ema_fast, color=color.blue, title="EMA Fast")
// ATR
//plot(show_atr ? atr_upper : na, "+ATR Upper", color=color.new(#ffffff,95))
//plot(show_atr ? atr_lower : na, "-ATR Lower", color=color.new(#ffffff,95))
// plot(show_atr ? atr_upper + atr_stop : na, "+ATR stop", color=color.new(#ffff00,85))
// plot(show_atr ? atr_lower - atr_stop : na, "-ATR stop", color=color.new(#ffff00,85))


// RSI Up
plotshape(rsi1_show and rsi_high1>70 and close>open?1:na,title="RSI 1",color=rsi_high1>70 and rsi_close1<70?yellow:rws_mid1==1?orange:rws_strong1==1?#ff0000:na,style=shape.circle,location=location.top)
plotshape(rsi2_show and rsi_high1<70 and rsi_high2>70 and close>open?1:na,title="RSI 2",color=rsi_high2>70 and rsi_close2<70?#e47aee:rws_mid2==1?#e116f5:rws_strong2==1?#ff008c:na,style=shape.circle,location=location.top)
// plotshape(rsi3_show and rsi_close3>70?1:na,title="RSI 3",color=rws_mid3==1?orange:rws_strong3==1?#ff0000:na,style=shape.circle,location=location.top)

// RSI Down
r1_d_cond = rsi1_show and rsi_low1<30 and close<open?1:na
r1_c = rsi_low1<30 and rsi_close1>30?#5e91ff:rwb_mid1==1?#0053ff:rwb_strong1==1?lime:na
plotshape(r1_d_cond,title="RSI 1",color=r1_c,style=shape.circle,location=location.bottom)
r2_d_cond = rsi2_show and rsi_low1>30 and rsi_low2<30 and close<open?1:na
r2_c = rsi_low2<30 and rsi_close2>30?#c77bfd:rwb_mid2==1?#9200fc:rwb_strong2==1?#00f1f2:na
plotshape(r2_d_cond,title="RSI 2 Close",color=r2_c,style=shape.circle,location=location.bottom)
// plotshape(rsi3_show and rsi_close3<30?1:na,title="RSI 3",color=rwb_mid3==1?#0053ff:rwb_strong3==1?lime:na,style=shape.circle,location=location.bottom)

// Bar color
barcolor(show_bbr==false ? rsi_color:na, title="Rsi Candles")
bbr_color = bbr>1 ? color.new(#ff0000, 0) : bbr<0 ? color.new(color.purple, 0)  : na
barcolor(show_bbr?bbr_color:na, title="Show bbr")
//barcolor(pos == -1 ? #ff0000: pos == 1 ? #00a000 : gray)


var bool enter_exit = false
//var int trade_dir = 0
var int state = 0
var int bar_num = 0
var float lastPrice = 0.0
var mult_diff = 10000
var int num_pips = 17
var int num_bars = 4

entry_signal() =>
	candle = close > open ? 1 : 0
    dir = 0
    counter = 0
    cond = ''
    allow = false
    ea = e200_a
    ba = basis_angle
    ea_cnt = e200_a<5?true:false

    // Uptrend - Oct 28th


    // === SELL ===
    // ===========
    if bb_lower>ema_200 and candle==1

        if rws_strong1 and wae_color==green and
         s5<basis and s3_a<s3b_a
         //and not((close>bb_upper and di_plus>di_plus[1]))
            dir := -1
            cond := close<bb_upper ? 'rsi1\ncnt' :'rsi1'
            counter := close<bb_upper ? 1 : 0
        
        if bb_zone<4 and 
         rsi_high1>70 and rsi_close1<70 and s3b_a>s3_high
         and not( (adx>adx_sma and adx_sma<adx_high) and s3b_a<s3_a)
         and not(s5>basis)
            dir := -1
            cond := 's3-rsi1\ncnt'
            counter := 1

        if bb_zone>2 and s2>s4 and kijun<s1 and s1>s4 and
         high>s1 and s5_a>s3_a
            dir := -1
            cond := 'z3-cnt'
            counter := 1

        if bb_zone<3 and rsi_close1>70 and wae_color==green and
         s5<basis and s3_a<s3b_a
            dir := -1
            cond := close<bb_upper ? 'rsi1\ncnt' :'rsi1'
            counter := close<bb_upper ? 1 : 0

        if close>basis and close>s5 and s5>s4 and s5>s1 and wae_color==green and
         rsi_close1<70
         and not(s1<s4)
         and not(s4<basis)
            dir := -1
            cond := 'r2'

        if (rsi_low1>70 or rsi_low2>70) and wae_line<wae_dz and s3b_a>0 and 
         (s3b_a<s3b_a[1] or s3b_a>s3_high) and s3>bb_upper
         and not(s1<basis or s1<s5)
         and not(s5>basis)
            dir := -1
            cond := 'r1-r2\nwae-dz'

        // if bb_zone<2 and close>basis and close>s5 and rsi_high2>70
        //  and not(s4<basis)
        //     dir := -1
        //     cond := 'r2-z1'



    // Counter 
    // ==========
    if candle==0 and bb_lower>ema_200 

        if rsi_close2<30 and s1<s4 and s2<basis
            dir := 1
            cond := 'r2-cnt'

        if 
         (rsi_close3<30 and (wae_line<wae_dz) and di_minus>di_plus)
         and ea_cnt
         and not(mom_ema<0 and rwb_strong3==0)
         and not(s4>bb_upper)
         and not(rsi_low2<30)
         and not(bbr2<0 and bbr2<bbr2[1])
            dir := 1
            cond := 'r3-cnt-s'

        // Filter
        if (di_minus<di_plus or di_minus<adx_low) or
         (m_histo>0)
            dir := 0

        if bbr<0
         and not(s4>bb_upper)
         and not(s3b_a>s3_low)
         and not(adx_sma<adx_mid and rsi_low2<30)
            dir := 1
            cond := 'bbr-cnt'

        if rsi_close3<30 and mom_ema<mom_low
         and not(isdown() and s3_a>s3b_a)
            dir := 1
            cond := 'mom-cnt'

        if close<kijun and s4_a>0 and adx<adx_high and
         wae_line>=wae_dz and (trendUp * 0.00010)<wae_dz and trendUp>0
         and not(s4>basis)
         and not(s5_a>s3_high)
            dir := 1
            cond := 'z3-cnt'

        // Filter
        if (s5>basis and s1>s5) or (bb_zone>1 and s1>s4) or (s5>bb_upper)
            dir := 0

        if low<s5 and high>s5 and rsi_low3<30
         and not(mom_ema>0)
            dir := 1
            cond := 's5-cnt'

        if adx_sma<adx_low and di_minus>di_plus
         and not(s5>bb_upper and mom_ema>0)
         and not(s3>s1)
            dir := 1
            cond := 'adx-low\ncnt'

        if open>s4 and low<s4 and s4<basis and 
         rsi_close3<50 and s3<basis
         and not(s5>bb_upper)
            dir := 1
            cond := 's4-low\ncnt'



        // Fitler
        if (mom_ema<mom_low or isdown() ) and rsi_close2>30
            dir := 0

        // counter trades- 
        // if mom_ema<0 and s3_a<s3_low and wae_line<wae_dz, pretty safe bet
        // or stronger down trades wait till mom_ema<mom_low adx_sma>adx_high - Jan 05


    if s3<s1 and (s5_a>s3_high or s5_a[1]>s3_high)  and 
     s5<s4 and s3_a<0
        dir := 1
        cond := 's5a-low\ncnt'

    // Between 
    // ==========
    if bb_upper>ema_200 and bb_lower<ema_200

        // Sell
        if candle==1 and rsi_close1>70 and
         wae_color==green
         and not(basis<ema_200 and rws_strong1==false)
         and not(bb_zone==4 and s3_a>s3b_a)
         and not(m_histo<0)
         and not(adx_sma<di_plus)
         and not(s3_a<s3b_a and s3_a>s3_a[1])
            dir := -1
            cond := high<bb_upper?'r1-cnt' : 'r1'
            counter := high<bb_upper ? 1 : 0

        if candle==1 and bb_zone<4 and 
         rsi_high1>70 and rsi_close1<70 and adx>adx_high
            dir := -1
            cond := 'rsi1-high\ncnt'
            counter := 1

        if candle==1 and rsi_high1<70 and rsi_high2>70 and
         m_histo>0 and s1>s5 and s1>basis and
         wae_color==green
         and not(s5>basis)
         and not(rsi_close2>70 and wae_line<wae_dz)
            dir := -1
            cond := 'rsi2'
            counter := 1

        // if candle==1 and bb_zone>2 and s2>s4 and kijun<s1 and s1>s4 and
        //  high>s1 and s5_a>s3_a and s3_a>0
        //     dir := -1
        //     cond := 'z3-cnt'
        //     counter := 1

        // Buy
        // if candle==0 and bb_zone>2 and 
        //  low<s1 and s5_a<s3_a and high<basis
        //  and not(adx_sma<adx_high)
        //     dir := 1
        //     cond := 'z3-cnt'
        //     counter := 1

        if candle==0 and s4_a>5 and s4<s5 and rsi_low3<30
            dir := 1
            cond := 's4-r3-cnt'
            counter := 1

        // if candle==0 and rsi_close1<30
        //  and not(bb_zone==4 and adx_sma<adx_high)
        //     dir := 1
        //     cond := 'r1'

        if candle==0 and rsi_low2<30 and wae_line>wae_dz and wae_color==red
         and not(s1>basis or s1>s5 or s1>ema_200)
         and not(rsi_low1<30)
         //and not(s2>basis or s2>s5)
         //and not(s3<bb_lower)
         //and not(bb_zone>2 and s1>ema_200)
            dir := 1
            cond := 'r2-new'


        // Filter
        if dir==0 and (wae_line<wae_dz or wae_color!=red)
            dir := 0

        if candle==0 and open>s4 and low<s4 and s4<basis and 
         rsi_close3<50 and s3<basis and s1_a>0
            dir := 1
            cond := 's4-low\ncnt'

        // Counter Buy
        if candle==1 and s5>basis and s3b_a>0 and mom_ema<mom_low
         and not(isdown())
         and not(s3_a>s3_a[1] and s3_a<s3_high)
            dir := -1
            cond := 's3'

        if candle==1 and rsi_close3>70 and rsi_high1>7 and rsi_close1<70
         and s4_a<0 and s5_a<0 and ea<0
            dir := -1
            cond := 'rsi1-cnt'
        

        // Ideas wae_line><wae_dz, di_minus>di_plus vice versa
        // s4_a>0 and open<s4
        // always look at s3_a, s3b_a

        // You have to make sure your strategy works on the close of the candle
        // because you are getting fake-outs
        // process_orders_on_close


        // if isdown() and res>res_low
        //     dir := 0

    // === BUY ===
    // ===========
    if bb_upper<ema_200 and candle==0

        // Between April 05-16
        // For rsi_close2 trades s1 must be < or > than s5 and basis and unless s5 is < > bb_bands and is medium stength
        // wae>line>wae_dz unless rsi_close3 is strongest,lime green and not bb_zone>2
        // if bb_zone>2 di_plus or di_minus should be up angle
        // or maybe check status adx>di_minus or di_plus


        // rsi_close3 and without rsi_close2 signal
        // s3b_a must be above below lines and mom_ema<0
        // If s1 is NOT crossed with s5 but s3 is and mom_ema< > 0 than the may be a safe trade

        // Also, think about changing where between and bb_upper<ema_200
        // start based off be_dist

        if rsi_close1<30
         and not(bb_zone>2 and s3_a<s3b_a)
         and not(bb_zone>2 and di_minus<di_minus[1])
         //and not(bb_zone>2 and s2>basis)
            dir := 1
            cond := 'r1'

        if rsi_close2<30
         and not(bb_upper>ema_200 and s1>basis)
         and not(rsi_low1<30)
         and not(isdown() and res>res_bottom)
         and not(s1>s5)
            dir := 1
            cond := 'r2'

        if rsi_close3<30 and mom_ema<0
         and not(s1>basis)
         and not(rsi_close2<30)
         and not(isdown())
         and not(m_histo>0)
         and not(bb_upper<ema_200 and s2>basis)
         and not(s3_a>0)
         and not(adx<adx_sma)
            dir := 1
            cond := 'r3'

        if rsi_close2<30 and (s4<bb_lower or s5<bb_lower)
            dir := 1
            cond := 's4-s5-low\ncnt'
            counter := 1

        if rsi_low3<30 and s5<bb_lower
            dir := 1
            cond := 's5-under'
            counter := 1

    // Counter
    if bb_upper<ema_200 and candle==1

        if rsi_high2>70 and s1>s5 and s1>basis and
         wae_line>wae_dz
         and not(rsi_close2>70 and adx<di_plus)
         and not(s5<bb_lower and adx_sma<adx_mid)
            dir := -1
            cond := 'rsi2-new'

        if rws_strong3
         and not(rsi_close2>70)
         and not(s4<basis and s1<s4)
         and not(di_plus<di_minus)
         and not(s4_a>0 and s4<basis)
            dir := -1
            cond := 'rsi3-new'

        // if s5>basis and s3b_a>0 and mom_ema<mom_low
        //     dir := -1
        //     cond := 's3'

        // if rsi_high2>70
        //  and not(s5<bb_lower)
        //     dir := -1
        //     cond := 'r2'

        // if rsi_close3>70 and mom_ema>0 and s5_a<0
        //  and not(isup() )
        //  and not(s5<bb_lower)
        //  and not(ea>0)
        //  and not(adx<adx_sma)
        //     dir := -1
        //     cond := 'r3'

        // if candle==1 and s5>basis and s3b_a>0 and mom_ema<mom_low
        //     dir := -1
        //     cond := 's3'

        // ====================================================================

        

        // if rsi_close3>70 and bb_lower<ema_200 and
        //  (s5_a<-5 and s5>bb_upper)
        //     dir := -1
        //     cond := 's5-above'

        // if rsi_close3<70 and mom_ema>0 and s5_a<0 and
        //  mom_ema>mom_ema[1] and res>res_low
        //  and not(isup() )
        //  and not(ea>0)
        //     dir := -1
        //     cond := 'mom'

        // Filter out
        // if dir==1 and bb_lower>ema_200
        //     dir := 0
    


    // Filter out same direction trades, wait 4 candles
    // and price higher than 20 pips before allowing another trade
    if dir == state and use_logic and counter==0
        allow := (bar_index - bar_num) >= num_bars ? true : false
        // sell
        if state == -1
            dir := ((close - lastPrice) * mult_diff) > num_pips and allow ? dir : 0
        // buy
        if state == 1
            dir := ((close - lastPrice) * mult_diff) < (num_pips * -1) and allow ? dir : 0

    //var int enter_exit = basis_angle > 0 and dir == -1 ? 0 : basis_angle < 0 and dir
    if counter==1 and state==dir
        dir := 0
        
    type = dir > 0 ? 1 : dir < 0 ? -1 : 0
    [type,counter,cond]

[trade_dir,counter,condition] = entry_signal() 

if trade_dir != 0 and use_logic
    state := trade_dir

    enter_exit := counter == 1 ? 1 : 0
    // Sell
    // if basis_angle > 0 and trade_dir == -1
    //     enter_exit := -1
    // // Counter
    // if basis_angle > 0 and trade_dir == 1
    //     enter_exit := 0
    // // Buy
    // if basis_angle < 0 and trade_dir == 1
    //     enter_exit := -1
    // // Counter
    // if basis_angle < 0 and trade_dir == -1
    //     enter_exit := 0
    // if basis>ema_200
    //     enter_exit := trade_dir == -1 ? false : trade_dir == 1 or counter ? true : false
    // if basis<ema_200
    //     enter_exit := trade_dir == 1 ? false : trade_dir == -1 or counter ? true : false
    //enter_exit := counter == 1 ? true : enter_exit == false ? true : false
    lastPrice := close
    bar_num   := bar_index

// if enter_exit != trade_dir and trade_dir != 0
//     enter_exit := enter_exit == false ? true : false
labelText = tostring(condition)
trade_color = trade_dir > 0  ? buy_color : sell_color
if trade_dir != 0 and show_cond
	if trade_dir > 0
        buy = label.new(x=time,y=high,text=labelText,xloc=xloc.bar_time, yloc=yloc.belowbar,color=trade_color,textcolor=color.white, size=size.normal,style=label.style_label_up)
    else
        sell = label.new(x=time,y=high,text=labelText,xloc=xloc.bar_time, yloc=yloc.abovebar,color=trade_color,textcolor=color.white, size=size.normal)

plot(counter,title="Counter Trade", style=plot.style_circles)
plot(state,"State",style=plot.style_circles)

plotshape(show_entry and trade_dir == 1 and enter_exit == 0 ? 1 : na, title="Entry Buy", color=trade_color, location = location.belowbar, style=shape.labelup, text="B", textcolor=color.white, size=size.small)
plotshape(show_entry and trade_dir == 1 and enter_exit == -1 ? 1 : na, title="Counter Buy", color=trade_color, location = location.belowbar, style=shape.labelup, text="Exit", textcolor=color.white, size=size.small)
plotshape(show_entry and trade_dir == -1 and enter_exit == 0 ? -1: na, title="Entry Sell", color=trade_color, location = location.abovebar, style=shape.labeldown, text="S", textcolor=color.white, size=size.small)
plotshape(show_entry and trade_dir == -1 and enter_exit == -1 ? -1 : na, title="Exit Sell", color=trade_color, location = location.abovebar, style=shape.labeldown, text="Exit", textcolor=color.white, size=size.small)
