//@version=5
indicator(title='Price Volume Trend BB - Multi', format=format.volume)

blue = #00c3ff
red = #ff0062
gray = #7e7e7e
white = #ffffff
yellow = #ffff00

g_pvt = 'Price Volume Trend BB -------------------------------------------------------------'
inl_pvt_color = '1'
i_pvt_time = input.timeframe(title='Timeframe', defval='30', group=g_pvt) 
i_pvt_smooth = input(2, title='PVT Smoothing', group=g_pvt)
i_pvt_len = input.int(30, title='BB Length', minval=1, group=g_pvt)
i_pvt_deviation = input.float(0.5, title='Bands Stdev Multiplier', minval=0.1, maxval=5.0, step=0.1, group=g_pvt)
i_pvt_show_candles = input.bool(false, 'Show PVT Candles')
i_pvt_col1 = input.color(blue, "Up", group=g_pvt, inline=inl_pvt_color)
i_pvt_col2 = input.color(red, "Down", group=g_pvt, inline=inl_pvt_color)
i_pvt_col3 = input.color(yellow, "Between", group=g_pvt, inline=inl_pvt_color)

newbar(res) => ta.change(time(res)) == 0 ? 0 : 1

// Function to normalize the series
normalize(_src, _min, _max) =>
    var _historicMin = 10e10
    var _historicMax = -10e10
    _historicMin := math.min(nz(_src, _historicMin), _historicMin)
    _historicMax := math.max(nz(_src, _historicMax), _historicMax)
    _min + (_max - _min) * (_src - _historicMin) / math.max(_historicMax - _historicMin, 10e-10)


// Calculate the price volume trend (PVT)
f_pvt() =>
    pvt_volume = ta.cum(ta.change(close) / close[1] * volume)
    pvtb = ta.sma(pvt_volume, i_pvt_smooth)
    pvt_norm = normalize(pvtb, -50, 50)
    [pvt_norm]

[pvt_norm] = request.security(syminfo.tickerid, i_pvt_time, f_pvt() )

plot(pvt_norm, color=color.new(white, 0), linewidth=2, title='PVT')

// Calculate the Bollinger Bands

f_pvt_bb() =>
    basis = ta.sma(pvt_norm, i_pvt_len)
    dev = i_pvt_deviation * ta.stdev(pvt_norm, i_pvt_len)
    i_pvt_upper = basis + dev
    i_pvt_lower = basis - dev

    [i_pvt_upper, i_pvt_lower]

[i_pvt_upper, i_pvt_lower] = request.security(syminfo.tickerid, i_pvt_time, f_pvt_bb() )

// Plot the Bollinger Bands
color_bar = pvt_norm > i_pvt_upper ? blue : pvt_norm < i_pvt_lower ? red : gray
color_bar1 = i_pvt_upper > pvt_norm ? red : blue
color_bar2 = i_pvt_lower > pvt_norm ? red : blue
upper_band_plot = plot(i_pvt_upper, color=color.new(gray, 0), title='BB Upper')
lower_band_plot = plot(i_pvt_lower, color=color.new(gray, 0), title='BB Lower')
fill(upper_band_plot, lower_band_plot, color=color.new(gray, 70))

pvt_cross_color = pvt_norm > i_pvt_upper ? i_pvt_col1 : pvt_norm < i_pvt_lower ? i_pvt_col2 : i_pvt_col3

bgcolor(pvt_cross_color, transp=90)

barcolor(i_pvt_show_candles ? pvt_cross_color : na )


