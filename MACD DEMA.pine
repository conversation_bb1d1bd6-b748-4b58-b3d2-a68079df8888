//@version=4
study("MACD DEMA",shorttitle='MACD DEMA')

red = color.new(#FF0000,30)
pink = color.new(#ff0057,30) 
lime = color.new(#54fc06,30) 
green = color.new(#008000, 30)
blue = color.new(#00c3ff, 30)
white = color.new(#ffffff, 60)

var float dema_histo = 0
dema_sma = input(12,title='DEMA Short') // 12
dema_lma = input(26,title='DEMA Long') // 26
dema_signal_in = input(9,title='Signal') // 7 // 9 
dema_high= input(27,title='Dema high')
dema_mid= input(11,title='Dema mid')
dema_down = input(-10,title='Dema down') 
dema_low = input(-24,title='Dema Low') 

MMEslowa = ema(close,dema_lma)
MMEslowb = ema(MMEslowa,dema_lma)
DEMAslow = ((2 * MMEslowa) - MMEslowb )
MMEfasta = ema(close,dema_sma)
MMEfastb = ema(MMEfasta,dema_sma)
DEMAfast = ((2 * MMEfasta) - MMEfastb)
dema_line = (DEMAfast - DEMAslow)
MMEsignala = ema(dema_line, dema_signal_in)
MMEsignalb = ema(MMEsignala, dema_signal_in)
dema_sig = ((2 * MMEsignala) - MMEsignalb )
dema_histo := (dema_line - dema_sig)
dema_color = dema_histo>0?green:red

dema_mult = 100
plot(dema_histo*dema_mult,color=dema_color,style=plot.style_columns,title='Histo',histbase=0)
p1 = plot(dema_line*dema_mult,color=blue,title='LigneMACD')
p2 = plot(dema_sig*dema_mult,color=red,title='Signal')
//fill(p1, p2, color=blue)
hline(0)
hline(dema_high,title="Dema high",color=white,linestyle=hline.style_solid)
hline(dema_mid,title="Dema mid",color=white,linestyle=hline.style_solid)
hline(dema_down,title="Dema down",color=white,linestyle=hline.style_solid)
hline(dema_low,title="Dema Low",color=white,linestyle=hline.style_solid)