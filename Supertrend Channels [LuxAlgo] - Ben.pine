// This work is licensed under a Attribution-NonCommercial-ShareAlike 4.0 International (CC BY-NC-SA 4.0) https://creativecommons.org/licenses/by-nc-sa/4.0/
// © LuxAlgo

//@version=5
indicator("Supertrend Channels [LuxAlgo] - Ben",overlay=true,max_lines_count=500)

i_stch_time = input.timeframe('30', "Resolution") // 4 hours, Daily
length = input.int(18) // 18, 14
mult   = input.int(5)  //  5,  2
i_stch_trans = input.int(75, 'Transp')
i_heikin = input.bool(false, title='Use Heikin Ashi')
i_show_ma = input.bool(false, 'Show MA')


// Angle
angle(_src, len) =>
    rad2degree = 180 / 3.14159265359  //pi 
    ang = rad2degree * math.atan((_src[0] - _src[1]) / ta.atr(len))
    ang

newbar(res) => ta.change(time(res)) == 0 ? 0 : 1

//------------------------------------------------------------------------------
f_st_level(sup_max, sup_high, sup_avg, sup_low, sup_min) =>

    var level = 0

    if close>sup_max
        level := 6
    if close>sup_high and close<sup_max
        level := 5
    if close>sup_avg and close<sup_high
        level := 4
    if close<sup_avg and close>sup_low
        level := 3
    if close<sup_low and close>sup_min
        level := 2
    if close<sup_min
        level := 1

    [level]



f_st_channels()=>
    //timeframe.change(i_stch_time) ? 
    upper = 0.,lower = 0.,os = 0,max = 0.,min = 0.

    src = close
    atr = ta.atr(length)*mult
    up = hl2 + atr
    dn = hl2 - atr
    upper := src[1] < upper[1] ? math.min(up,upper[1]) : up
    lower := src[1] > lower[1] ? math.max(dn,lower[1]) : dn

    os := src > upper ? 1 : src < lower ? 0 : os[1]
    spt = os == 1 ? lower : upper

    max := ta.cross(src,spt) ? nz(math.max(max[1],src),src) : 
     os == 1 ? math.max(src,max[1]) : 
     math.min(spt,max[1])

    min := ta.cross(src,spt) ? nz(math.min(min[1],src),src) : 
     os == 0 ? math.min(src,min[1]) : 
     math.max(spt,min[1])

    avg = math.avg(max,min)
    avg_high = math.avg(max,avg)
    avg_low = math.avg(avg,min)

    [max, min, os, avg, avg_high, avg_low]

[st_max, st_min, st_os, st_avg, st_avg_high, st_avg_low] = request.security(syminfo.tickerid, i_stch_time, f_st_channels() )
[st_max_h, st_min_h, st_os_h, st_avg_h, st_avg_high_h, st_avg_low_h] = request.security(ticker.heikinashi(syminfo.tickerid), i_stch_time, f_st_channels() )

st_max_a = angle(st_max, 14)
st_avg_a = angle(st_avg, 14)
st_min_a = angle(st_min, 14)

if newbar(i_stch_time) == 0
    st_max := st_max[1]
    st_min := st_min[1]
    //st_os := st_os[1]
    st_avg := st_avg[1]
    st_avg_high:= st_avg_high[1]
    st_avg_low := st_avg_low[1]

    st_max_a := st_max_a[1]
    st_avg_a:= st_avg_a[1]
    st_min_a := st_min_a[1]

[level] = f_st_level(st_max, st_avg_high, st_avg, st_avg_low, st_min )
plot( level, 'Level', color=color.new(color.blue,100) ) 
//------------------------------------------------------------------------------
var st_up_col  = color.new(#ff1100,i_stch_trans)
var st_avg_col = color.new(#ffffff , (i_stch_trans + 5) )
var st_dn_col = color.new(#0cb51a ,i_stch_trans)

stch_max = i_heikin ? st_max_h : st_max 
stch_min = i_heikin ? st_min_h : st_min   
stch_os  = i_heikin ? st_os_h : st_os 
stch_avg = i_heikin ? st_avg_h : st_avg
stch_avg_high = i_heikin ? st_avg_high_h : st_avg_high
stch_avg_low = i_heikin ? st_avg_low_h : st_avg_low  
// Plots
p_st_max = plot(stch_max,'Upper Channel',stch_max != stch_max[1] and stch_os == 1 ? na : st_up_col)
p_st_avg = plot(stch_avg,'Average',st_avg_col)
p_st_min = plot(stch_min,'Lower Channel',stch_min != stch_min[1] and stch_os == 0 ? na : st_dn_col)
plot(stch_avg_high,'Avg High',st_up_col)
plot(stch_avg_low,'Avg Low',st_dn_col)
plot(st_os,'st_os',st_dn_col)


// Angles
plot(st_max_a, 'Angle Avg', color=color.new(st_up_col,100))
plot(st_avg_a, 'Angle Avg', color=color.new(st_avg_col,100))
plot(st_min_a, 'Angle Avg', color=color.new(st_dn_col,100))

fill(p_st_max,p_st_avg,st_up_col,'Upper Area')
fill(p_st_avg,p_st_min,st_dn_col,'Lower Area')

// H3
i_ma_len = input.int(50, 'MA Length')
h3_m = request.security(syminfo.tickerid, '30', ta.hma(close, i_ma_len) )
plot(i_show_ma ? h3_m : na,'H3', color=color.orange)

stch_h3_bars = ta.barssince(h3_m<stch_min)
plot(stch_h3_bars, title='H3/Channel', color=color.new(color.blue,100), style=plot.style_circles )