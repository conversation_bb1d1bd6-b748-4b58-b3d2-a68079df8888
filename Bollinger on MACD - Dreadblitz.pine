// © Dreadblitz
//@version=5
//
indicator('Bolling<PERSON> on Macd', shorttitle='BSM - v5', overlay=false)

red = #FF0000
green = #008000
sky_blue = #2196F3
aqua = #00BFFF
//
// ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ //
// INPUTS
// ------------------------------------------------------------------------------------------------------------------
i_bbmacd_time = input.timeframe('30', title="Timeframe")
fast = input.int(8, 'Fast EMA')
slow = input.int(26, 'Low EMA')
stdv = input.float(0.8, 'Stdv', step=0.1)
//
// ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ //
// VARS
// ------------------------------------------------------------------------------------------------------------------
m_fast = 0.0
m_slow = 0.0
Avg = 0.0
SDev = 0.0
bb_up = 0.0
bb_down = 0.0
bb_diff = 0.0


// ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ //
// CALCULATE
// ------------------------------------------------------------------------------------------------------------------
m_fast := request.security(syminfo.tickerid, i_bbmacd_time,  ta.ema(close, fast)[1], lookahead=barmerge.lookahead_on  ) // ta.ema(close, fast)
m_slow := request.security(syminfo.tickerid, i_bbmacd_time, ta.ema(close, slow)[1], lookahead=barmerge.lookahead_on  )  // ta.ema(close, slow)

BBMacd = m_fast - m_slow
Avg := request.security(syminfo.tickerid, i_bbmacd_time, ta.ema(BBMacd, 9)[1], lookahead=barmerge.lookahead_on ) // ta.ema(BBMacd, 9)
SDev := request.security(syminfo.tickerid, i_bbmacd_time,ta.stdev(BBMacd, 9)[1], lookahead=barmerge.lookahead_on ) //ta.stdev(BBMacd, 9)
bb_up := Avg + stdv * SDev
bb_down := Avg - stdv * SDev
bb_diff := ta.change(m_fast) ? BBMacd - BBMacd[1] : bb_diff[1]

//
// ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ //
// PLOTS
// ------------------------------------------------------------------------------------------------------------------
pcol = BBMacd < bb_down ? red: BBMacd > bb_up ? green : sky_blue
p_diff = plot(bb_diff, title='BB Diff', color=color.new(color.blue,100))
c = plot(BBMacd, title='BB MACD', color=pcol, linewidth=2, style=plot.style_line)
a = plot(bb_up, title='Upper Band', color=color.new(aqua, 30), linewidth=1, style=plot.style_line)
b = plot(bb_down, title='Lower Band', color=color.new(aqua, 30), linewidth=1, style=plot.style_line)
fill(a, b, color=color.new(aqua, 90) )
pcol1 = BBMacd < bb_down ? red: na
fill(c, b, color=color.new(pcol1, 50) )
pcol2 = BBMacd > bb_up ? green : na
fill(c, a, color=color.new(pcol2, 50) )

//
// ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ //
// ALERTS
// ------------------------------------------------------------------------------------------------------------------
buy = ta.crossover(BBMacd, bb_up)
sell = ta.crossunder(BBMacd, bb_down)
alertcondition(buy or sell, title='Buy/Sell', message='Buy/Sell')
alertcondition(buy, title='Buy', message='Buy')
alertcondition(sell, title='Sell', message='Sell')

