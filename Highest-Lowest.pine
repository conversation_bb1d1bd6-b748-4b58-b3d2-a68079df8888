//@version=5
indicator(title='Highest/Lowest - V5', overlay=true, max_labels_count = 500)

red = #ff0062
orange = #ff9800
yellow = #FFFF00
green = #4caf50
lime = #50e600
aqua = #00bcd4
blue = #2962ff
dark_blue = #2962ff
violet = #814dff
white = #ffffff
gray = #707070
black = #000000

timeinrange(hl_res, sess) => not na(time(hl_res, sess, "America/New_York")) ? 1 : 0
//plot(timeinrange("1", "1300-1400"), color=color.red)


angle(_src, len) =>
    rad2degree = 180 / 3.14159265359  //pi 
    ang = rad2degree * math.atan((_src[0] - _src[1]) / ta.atr(len))
    ang

// SYM Info
min_tick = syminfo.mintick
var int decimals = int(math.log10(1/min_tick))
decimals := decimals == 2 ? 1 : decimals == 0 ? 1 : decimals
//plot(min_tick, title='Min Tick')
//plot(decimals, title='Decimals')

get_pip_distance(point1, point2) =>
    diff_points = math.abs( (point1 - point2) )
    pip_value = decimals<3 ? diff_points / syminfo.mintick / 100 : diff_points / syminfo.mintick / 10
    //pip_value = decimals>3 ? diff_points * (math.pow(10, decimals) * syminfo.mintick) : diff_points / syminfo.mintick / 10



g_hl = 'HIGHEST LOWEST -------------------------------------------------------------'
hl_res = input.timeframe(title='Resolution', defval='30', group=g_hl)
hl_res2 = input.timeframe(title='Resolution 2', defval='120', group=g_hl)
i_use_hl1 = input.bool(true,title='Display HL1', group=g_hl)
i_use_hl2 = input.bool(true,title='Display HL2', group=g_hl)
i_show_fill = input.bool(false, title='Fill Boxes', group=g_hl)
i_show_bars = input.bool(false,title='Show Bars', group=g_hl)
i_show_labels = input.bool(false,title='Show Labels', group=g_hl)
gaps = input.bool(false,title='Bar Merge Gaps On', group=g_hl)
hl_len1 = input(title='Length', defval=20, group=g_hl)  // 20
hl_len2 = input(title='Length', defval=20, group=g_hl)  // 20
src_h = input(title='Source', defval=close, group=g_hl)
src_l = input(title='Source', defval=close, group=g_hl)
use_diff = input(title='Filter Diff', defval=true, group=g_hl)
diff_range = input(10, title='FDiff Range', group=g_hl)
offset = input(title='Offset', defval=0, group=g_hl)
i_hl_smooth = input(title='Smooth', defval=false, group=g_hl)
i_hl_smooth_len = input(title='Smooth Length', defval=14, group=g_hl)


// HL 1
var float hh = 0.0
var float ll = 0.0
hh := timeframe.change(hl_res) ? request.security(syminfo.tickerid, hl_res, ta.highest(src_h, hl_len1), gaps=gaps ? barmerge.gaps_on : barmerge.gaps_off) : hh[1]
ll := timeframe.change(hl_res) ? request.security(syminfo.tickerid, hl_res, ta.lowest(src_l, hl_len1), gaps=gaps ? barmerge.gaps_on : barmerge.gaps_off) : ll[1]
hl_mid = i_hl_smooth ? ta.sma((hh + ll) * 0.5, i_hl_smooth_len) : (hh + ll) * 0.5
hh_a = angle(hh, hl_len1)
ll_a = angle(ll, hl_len1)
diff = hh - ll
h1_p1 = plot(i_use_hl1 ? hh : na, title='HH', linewidth=2, color=hh_a == 0 ? violet : red)
h1_p2 = plot(i_use_hl1 ? ll : na, title='LL', linewidth=2, color=ll_a == 0 ? aqua : blue)
fill(h1_p1, h1_p2,title='Fill H1', color= i_show_fill ? color.new(white,90) : na )
//fill(h1_p1, h1_p2,title='Fill H1', color= i_show_fill and ll>ll[1] ? color.new(green,50) : i_show_fill and ll<ll[1] ? color.new(red,50) : na )
dist = get_pip_distance(hh,ll)

plot(math.round(dist),"Distance ", color=color.new(green,100) )
plot(i_use_hl1 ? hl_mid : na, title='HL Midline', linewidth=2)
barcolor(i_show_bars and i_use_hl1 and timeframe.change(hl_res) ? blue : na)
plot(i_use_hl1 ? ta.barssince(ta.change(hh)) : na, 'Bars Since High', style=plot.style_circles)
plot(i_use_hl1 ? ta.barssince(ta.change(ll)) : na, 'Bars Since Low', style=plot.style_circles)

if ta.change(hh) and i_use_hl1 and i_show_labels
    txt2 = "Pips: \n" + str.tostring(dist)
    info2 = label.new(x=time,y=hh + 0.0005,xloc=xloc.bar_time, text=txt2, textcolor=#ffffff, style=label.style_label_down, color=red)

if ta.change(ll) and i_use_hl1 and i_show_labels
    txt2 = "Pips: \n" + str.tostring(dist)
    info2 = label.new(x=time, y=ll - 0.0005, xloc=xloc.bar_time, text=txt2, textcolor=#ffffff, style=label.style_label_up, color=green)

// HL 2
var float hh2 = 0.0
var float ll2 = 0.0
hh2 := timeframe.change(hl_res2) ? request.security(syminfo.tickerid, hl_res2, ta.highest(src_h, hl_len2), gaps=gaps ? barmerge.gaps_on : barmerge.gaps_off) : hh2[1]
ll2 := timeframe.change(hl_res2) ? request.security(syminfo.tickerid, hl_res2, ta.lowest(src_l, hl_len2), gaps=gaps ? barmerge.gaps_on : barmerge.gaps_off) : ll2[1]
hh2_a = angle(hh2, hl_len2)
ll2_a = angle(ll2, hl_len2)
diff2 = hh2 - ll2
hl_mid2 = i_hl_smooth ? ta.sma((hh2 + ll2) * 0.5, i_hl_smooth_len) : (hh2 + ll2) * 0.5
dist2 = math.round(get_pip_distance(hh2,ll2) )
plot(dist2,"Distance 2", color=color.new(green,100),style=plot.style_circles )

if ta.change(hh2) and i_use_hl2 and i_show_labels
    txt2 = str.tostring(dist2)
    info2 = label.new(x=time,y=hh2 + 0.0005,xloc=xloc.bar_time, text=txt2, textcolor=#ffffff, style=label.style_label_down, color=red)

if ta.change(ll2) and i_use_hl2 and i_show_labels
    txt2 = str.tostring(dist2)
    info2 = label.new(x=time, y=ll2 - 0.0005, xloc=xloc.bar_time, text=txt2, textcolor=#ffffff, style=label.style_label_up, color=green)

// Plot
hh2_p1 = plot(i_use_hl2 ? hh2 : na, title='HH2', linewidth=2, color=hh2_a == 0 ? #ff00ff : #ff0000)
hh2_p2 = plot(i_use_hl2 ? ll2 : na, title='LL2', linewidth=2, color=ll2_a == 0 ? #55d51a : #00FFFF)
plot(i_use_hl2 ? hl_mid2 : na, title='HL2 Mid', linewidth=2)
plot(i_use_hl2 ? ta.barssince(ta.change(hh2)) : na, 'Bars Since High', style=plot.style_circles)
plot(i_use_hl2 ? ta.barssince(ta.change(ll2)) : na, 'Bars Since Low', style=plot.style_circles)
//fill(p1, p2, title='Fill', color=#33333365)
//plot(diff, title='Diff', color=color.new(color.blue, 100), linewidth=0)

sell_cond = high > hh ? 1 : na
buy_cond = low < ll and use_diff and diff > diff_range ? 1 : na
//plotshape(sell_cond, title='Sell 1', color=color.new(color.red, 0), style=shape.circle, location=location.top)
//plotshape(buy_cond, title='Sell 1', color=color.new(color.green, 0), style=shape.circle, location=location.bottom)

barcolor(i_show_bars and i_use_hl2 and timeframe.change(hl_res2) ? blue : na)

//
// ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ //
// STOP LOSS
// ------------------------------------------------------------------------------------------------------------------
g_sl = 'Stop Loss ----------------------------------------------------'
atr_group   = 'ATR'
show_sl     = input.bool(false,title="Display ATR",group=atr_group)
sl_Multip   = input.float(0.75, title='Stop Loss',group=g_sl) // 4 1.5
atr_len     = input.int(14, title='ATR Length ',group=atr_group)
//i_plot_trades = input.bool(true, title="Display Trades",group=g_sl)
atr_src     = input.string('close', title='Close or Wicks', options=['close', 'wicks'],group=g_sl) // close
sl_min      = input.float(0.07, title='Stop Loss Minimum Pips')
sl_max      = input.float(0.12, title='Stop Loss Maximum Pips')

atr_type    = input.string(title='ATR Type', defval='ema', options=['sma', 'ema', 'zema', 'dema', 'tema', 'wma', 'vwma', 'smma', 'rma', 'hma', 'lsma', 'McGinley'], group=atr_group)
atr_smooth  = input.int(5, title="ATR Smooth", group=atr_group)

ATR = ta.atr(atr_len)
var float sl_long = 0.0
var float sl_short = 0.0
sl_long     := (atr_src =='close' ? close : low)  - ATR * sl_Multip 
sl_short    := (atr_src =='close' ? close : high) + ATR * sl_Multip 

atr_upper = ta.sma( ta.ema(sl_short, atr_len), atr_smooth ) //ma_types(atr_len, atr_type, sl_short)
atr_lower = ta.sma( ta.ema(sl_long, atr_len), atr_smooth )  //ma_types(atr_len, atr_type, sl_long)
atr_mid = (atr_lower + atr_upper) * 0.5
atr_mid_a = angle(atr_mid,14)

plot(show_sl ? atr_lower  : na,"ATR Lower ", color=lime )
plot(show_sl ? atr_upper  : na,"ATR Upper ", color=red )
plot(show_sl ? atr_mid  : na,"ATR Mid ", color=atr_mid_a > 0 ? white : gray )
plot(show_sl ? atr_mid_a  : na,"ATR Angle ", color=atr_mid_a > 0 ? color.new(green,100) : color.new(red,100) )
plot(show_sl ? sl_short : na,"ATR - ", color=color.new(red,70) )
plot(show_sl ? sl_long  : na,"ATR + ", color=color.new(green,70) )

candle = close>open ? 1 : 0
atr_sell = sl_short>atr_upper and atr_mid_a<0 and candle==1 ? 1 : 0
atr_buy = sl_long<atr_lower and atr_mid_a>0 and candle==1 ? 1 : 0
//plotshape(show_sl and atr_sell ? 1 : 0,"Top End",style=shape.circle, location = location.top, color = color.rgb(230, 0, 0))
//plotshape(show_sl and atr_buy ? 1 : 0,"Bottom End",style=shape.circle, location = location.bottom, color =  #00be00 )

