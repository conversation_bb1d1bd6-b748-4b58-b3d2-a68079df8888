//@version=4
study("Wick Delta Buy/Sell Pressure", overlay=false)

arrows = false//input(title="Show as arrows?", type=input.bool, defval=false)

uptop = high - close
upbot = open - low
dntop = high - open
dnbot = close - low

up_delta = upbot - uptop
down_delta = dntop - dnbot
delta = up_delta ? up_delta : down_delta ? down_delta : 0  // golong ? +1 : goshort ? -1 : 0 

pos_delta = delta > 0 ? delta : 0
neg_delta = delta < 0 ? delta : 0

stddevlevel = input(title="Standard Deviation", type=input.float, defval=3.0, step=0.5)
stddevlookback = input(title="Standard Deviation Lookback", type=input.integer, defval=20)
//huntbars = input(title="Colour Candles", type=bool, defval=true)

wickdevup = stdev(pos_delta, stddevlookback) * stddevlevel
wickdevdn = stdev(neg_delta, stddevlookback) * -stddevlevel
huntbars = input(title="Highlight Outliers?", type=input.bool, defval=true)

huntingup = pos_delta >= wickdevup // and abs(upPlotValue) > abs(downPlotValue)
huntingdn = neg_delta <= wickdevdn // and abs(downPlotValue) > abs(upPlotValue)

plot(wickdevup, color=color.new(color.lime,90), style=plot.style_area, title="Deviation Up")
plot(wickdevdn, color=color.new(color.red,90), style=plot.style_area, title="Deviation Down")
hline(0, linestyle=hline.style_solid)

plot(delta > 0 ? delta : na, style=plot.style_histogram, linewidth=8, color=huntingup and huntbars ? color.green : color.new(color.lime,60), title="Delta Up")
plot(delta < 0 ? delta : na, style=plot.style_histogram, linewidth=8, color=huntingdn and huntbars ? #b33f37 : color.new(color.red,60), title="Delta Down")

//plotshape(arrows and delta > 0 ? delta : na, style=shape.triangleup, location=location.absolute, color=huntingup and huntbars ? color.green : color.new(color.lime,60))
//plotshape(arrows and delta < 0 ? delta : na, style=shape.triangledown, location=location.absolute, color=huntingdn and huntbars ? #b33f37 : color.new(color.red,60))

// DEBUG
// min(uptop, uptop)
// plotchar(uptop, title="uptop", char="", location=location.bottom)
// plotchar(upbot, title="upbot", char="", location=location.bottom)
// plotchar(dntop, title="dntop", char="", location=location.bottom)
// plotchar(dnbot, title="dnbot", char="", location=location.bottom)
// plotchar(up_delta, title="up_delta", char="", location=location.bottom)
// plotchar(down_delta, title="down_delta", char="", location=location.bottom)

