//@version=4
study("EMA - Multi", overlay=true)

ema1 = input(8, "Ema 1")
ema2 = input(20, "Ema 2")
ema3 = input(50, "Ema 3")
ema4 = input(200, "Ema 4")
e1 = input(title="EZ 1", type=input.bool, defval=false)
e2 = input(title="EZ 2", type=input.bool, defval=false)
e3 = input(title="EZ 3", type=input.bool, defval=false)
e4 = input(title="EZ 4", type=input.bool, defval=true)
e_angle = input(100, "EMA Angle")
ez_candles = input(title="Candles", type=input.bool, defval=false)
ez_candles_m = input(title="Candles Multi", type=input.bool, defval=true)
show_curr = input(title="Show Current", type=input.bool, defval=false)
show_multi = input(title="Show Multi", type=input.bool, defval=true)
ema_time = input(title="Timeframe", type=input.resolution, defval="30")


red = #ff0062
aqua = #00bcd4
yellow = #FFFF00
orange = #ff9800
green = #4caf50
lime = #50ff00
white = #ffffff
c_hide = color.new(#ffffff,100)
blue = #0053ff
magenta = #ff0062
purple = #0045b3
gray = #707070
black = #000000

angle(_src, len) =>
    rad2degree=180/3.14159265359  //pi 
    ang=rad2degree*atan((_src[0] - _src[1])/atr(len))

e100 = ema(close, 100)
e100_a = angle(e100,e_angle)
e200 = ema(close, ema4)
e200_a = angle(e200,e_angle)
ea = abs(e200_a)
e_zone = ea < 1 ? 0 : 
 ea < 2 ? 1 : 
 ea < 3 ? 2 :
 ea < 4 ? 3 :
 ea < 5 ? 4 :
 ea < 6 ? 5 :
 ea > 6 ? 6 : na
e_color = e_zone == 0 ? red :
 e_zone == 1 ? orange : 
 e_zone == 2 ? yellow : 
 e_zone == 3 ? gray : 
 e_zone == 4 ? blue:
 e_zone == 5 ? lime:
 e_zone == 6 ? white: na

plot(e1 and show_curr ? ema(close, ema1) : na, color=color.white, title='EMA 1')
plot(e2 and show_curr ? ema(close, ema2) : na, color=#0088FA , title='20 EMA')
plot(e3 and show_curr ? ema(close, ema3) : na, color=#FA00D0, title='50 EMA')
e_n= plot(e4 and show_curr ? e200 : na, color=e_color, title='200 EMA')
plot(e200_a, color=color.new(e_color,100), title='Angel ema 200',style=plot.style_circles)
plot(e100_a, color=e100_a>0?color.new(#FF7000,100):color.new(#814dff,100), title='Angel ema 100',style=plot.style_circles)
barcolor(show_curr and ez_candles ? e_color : na)

// Multi
e_1_m = security(syminfo.tickerid, ema_time, ema(close, ema1) ) 
e_2_m = security(syminfo.tickerid, ema_time, ema(close, ema2) ) 
e_3_m = security(syminfo.tickerid, ema_time, ema(close, ema3) ) 
e200_m = security(syminfo.tickerid, ema_time, ema(close, ema4) )
e200_a_m = security(syminfo.tickerid, ema_time, angle(e200_m,e_angle) ) 
ea_m = security(syminfo.tickerid, ema_time, abs(e200_a_m) ) 
e_zone_m = ea_m < 0.5 ? 0 : 
 ea_m < 2 ? 1 : 
 ea_m < 3 ? 2 :
 ea_m < 4 ? 3 :
 ea_m < 5 ? 4 :
 ea_m < 6 ? 5 :
 ea_m > 6 ? 6 : na

e_color_m = e_zone_m == 0 ? red :
 e_zone_m == 1 ? orange : 
 e_zone_m == 2 ? yellow : 
 e_zone_m == 3 ? gray : 
 e_zone_m == 4 ? blue:
 e_zone_m == 5 ? lime:
 e_zone_m == 6 ? white: na


plot(e1 and show_multi ? e_1_m : na, color=color.white, title='EMA 1')
plot(e2 and show_multi ? e_2_m : na, color=#0088FA , title='20 EMA')
plot(e3 and show_multi ? e_3_m : na, color=#FA00D0, title='50 EMA')
e_m = plot(e4 and show_multi ? e200_m : na, color=e_color_m, title='Multi 200')
barcolor(show_multi and ez_candles_m ? e_color_m : na)

e1_cond = e_1_m>e_2_m and e_1_m[1]<e_2_m[1]
plotshape(e1_cond, "e1 cross up", color=#00ff00, style=shape.circle,location=location.bottom)

// Diff
e_diff = (e200 - e200_m) * 100
plot(show_multi ? e_diff : na, title='Diff',color=color.new(blue,100))
fill(e_n,e_m,title='EMA Diff', color=e200_a>0 ? color.new(red,90) : color.new(green,90))

