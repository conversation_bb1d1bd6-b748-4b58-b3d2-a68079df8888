//@version=4
study(title = "Trading Bot - old", shorttitle = "Trading Bot - old", overlay=true, format = format.price, precision = 4)


// Custom inputs
account_size = input(2000, minval=1000,title="Account Size")
//trending = input(title="Show Trending/Ranging", type=input.bool, defval=true)
show_entry = input(title="Show Entry", type=input.bool, defval=true)
show_atr = input(title = "Show ATR", type = input.bool, defval = false)
//show_label = input(title="Show Labels", type=input.bool, defval=true)
//show_pips = input(title="Show Pips", type=input.bool, defval=true)
show_angle = input(title="Show Angles", type=input.bool, defval=false)
//show_cross = input(title="Show Crossover", type=input.bool, defval=false)
show_BB = input(title="Show BB", type=input.bool, defval=true)
use_fast_ma = input(title="Fast MA", type=input.bool, defval=false)


use_ssl_cross = input(title="SSL cross BB Basis", type=input.bool, defval=false)
use_rsi = input(title="Use RSI bars", type=input.bool, defval=true)
use_bsm = input(title="Use BSM", type=input.bool, defval=false)
//use_ema_cross = input(title="SSL cross EMA", type=input.bool, defval=false)
//use_basis_angle = input(title="Use Basis Angle", type=input.bool, defval=false)

//use_res = input(title="RES Filter", type=input.bool, defval=false)
use_spcecial = input(title="Use Special", type=input.bool, defval=true)
use_change = input(title="Use Change", type=input.bool, defval=true)
use_wae = input(title="WAE Filter", type=input.bool, defval=true)
use_trend = input(title="Trend Filter", type=input.bool, defval=false)
use_wicks = input(title="Use Wicks", type=input.bool, defval=false)



use_deadzone = input(title="Deadzone", type=input.bool, defval=true)
dz_time = input(title="DZ Timeframe", type=input.session, defval= "1715-1900")
use_counter_sell = input(title="Counter Sell", type=input.bool, defval=true)
use_counter_buy = input(title="Counter Buy", type=input.bool, defval=false)





// === Global Function ===
// ==================================================
angle(_src) =>
    rad2degree=180/3.14159265359  //pi 
    ang=rad2degree*atan((_src[0] - _src[1])/atr(3))

Session(sess) => na(time(timeframe.period, sess)) == false
timezone(tz) =>
	As = "1800-0300" //input(title="Asia", type=input.session, defval="1800-0300")
	Lon = "0300-1200" //input(title="London", type=input.session, defval="")
	Ny = "0800-1800" //input(title="New York", type=input.session, defval=)
	Dz = dz_time //input(title="Deadzone", type=input.session, defval=)
	//trans = input(title="TZ Trans", type=input.integer, defval=85)

	Asia = Session(As) ? #00bcd4 : na
	London = Session(Lon) ? #00796b : na
	NewYork = Session(Ny) ? #b71c1c : na
	Deadzone = Session(Dz) ? #b71c1c : na

	// if show
	// 	bgcolor(Asia, title="Asia",transp=trans)
	// 	bgcolor(London, title="London",transp=trans)
	// 	bgcolor(NewYork, title="New York",transp=trans)

	//bgcolor(Deadzone, title="Deadzone",transp=40)
	
// === EMA 200 ===
// ==================================================
len_200 = input(200, minval=1, title="EMA 200")
ema_200 = ema(close, len_200)

ema_56 = ema(close, 56)
ema_56_up = ema_56 > ema_56[1]
ema_56_down = ema_56 < ema_56[1]
mycolor = ema_56_up ? #55d51a : ema_56_down ? #e91e63 : #00c3ff
plot(ema_56 ? ema_56 :na, title="EMA 56", color=mycolor, linewidth=4)



// === Bollinger Bands %B ===
// ==================================================
length = input(25, minval=1, title="%B Length") // old 56
src = input(close, title="Source")
multi = input(2.0, minval=0.001, maxval=50, title="StdDev")
basis = sma(src, length)
deviation = multi * stdev(src, length)
upper = basis + deviation
lower = basis - deviation
bbr = (src - lower)/(upper - lower)




// === Bollinger Awesome Alert R1.1 by JustUncleL ===
// ==================================================
bb_use_ema = input(false, title="Use EMA for Bollinger Band")
bb_source = input(close, title="Bollinger Source")
bb_length = length //input(54, minval=1, title="Bollinger Length")
bb_mult = input(2.0, title="Base Multiplier", minval=0.5, maxval=10)
sqz_length = 100
sqz_threshold = input(50, minval=0, title="Squeeze Threshold") // old 54

// EMA inputs
fast_ma_len = input(9, title="Fast EMA length", minval=2)

// Breakout Indicator Inputs
ema_1 = ema(bb_source, bb_length)
sma_1 = sma(bb_source, bb_length)
bb_basis = bb_use_ema ? ema_1 : sma_1
//fast_ma = ema(bb_source, fast_ma_len)
fast_ma = sma(bb_source, fast_ma_len)
dev = stdev(bb_source, bb_length)
bb_dev = bb_mult * dev
// Bands
bb_upper = bb_basis + bb_dev
bb_lower = bb_basis - bb_dev
// Calculate BB spread and average spread
bb_spread = bb_upper - bb_lower
avgspread = sma(bb_spread, sqz_length)
// Calculate BB relative %width for Squeeze indication
bb_squeeze = 0.00
bb_squeeze := bb_spread / avgspread * 100
// Calculate Upper and Lower band painting offsets based on 50% of atr.
bb_offset = atr(14) * 0.5
bb_sqz_upper = bb_upper + bb_offset
bb_sqz_lower = bb_lower - bb_offset
basis_angle = angle(bb_basis)



// === SSL Hybrid - Mihkel00 ===
// ==================================================
len = input(title = "SSL1 / Baseline Length", defval = 55) // old 55
show_Baseline = input(title="Show Baseline", type=input.bool, defval=true)
show_SSL1 = input(title = "Show SSL1", type = input.bool, defval = false)

//ATR
atrlen = input(14, "ATR Period")
mult = input(1, "ATR Multi", step = 0.1)
smoothing = input(title = "ATR Smoothing", defval = "WMA", options = ["RMA", "SMA", "EMA", "WMA"])

ma_function(source, atrlen) =>
	if smoothing == "RMA"
		rma(source, atrlen)
	else
		if smoothing == "SMA"
			sma(source, atrlen)
		else
			if smoothing == "EMA"
				ema(source, atrlen)
			else
				wma(source, atrlen)

atr_slen = ma_function(tr(true), atrlen)

// ATR Up/Low Bands
upper_band = atr_slen * mult + close
lower_band = close - atr_slen * mult

// SSL 1 and SSL2
emaHigh = wma(2 * wma(high, len / 2) - wma(high, len), round(sqrt(len)))
emaLow = wma(2 * wma(low, len / 2) - wma(low, len), round(sqrt(len)))

// BASELINE VALUES
SSL_B = wma(2 * wma(close, len / 2) - wma(close, len), round(sqrt(len)))
multy = 0.2
range = tr
rangema = ema(range, len)
upperk =SSL_B + rangema * multy
lowerk = SSL_B - rangema * multy

//SSL1 VALUES
Hlv = int(na)
Hlv := close > emaHigh ? 1 : close < emaLow ? -1 : Hlv[1]
sslDown = Hlv < 0 ? emaHigh : emaLow

//COLORS
color_ssl1 = close > sslDown ? #00c3ff : close < sslDown ? #ff0062 : na
ssl_color_buy = #00c3ff
ssl_color_sell = #ff0062
ssl_color_bar = close > upperk ? ssl_color_buy : close < lowerk ? ssl_color_sell : color.gray



// === RSI Chart Bars ===
// ==================================================
rsi_len = input(14, minval=1, title="Length")
up = rma(max(change(close), 0), rsi_len)
down = rma(-min(change(close), 0), rsi_len)
rsi = down == 0 ? 100 : up == 0 ? 0 : 100 - (100 / (1 + up / down))
src1 = close, len1 = input(70, minval=1, title="UpLevel")
src2 = close, len2 = input(30, minval=1, title="DownLevel")
isup() => rsi > len1
isdown() => rsi < len2
// plot(rsi,title="RSI",color=color.blue)
barcolor(isup() ? #00ff00 : isdown() ? #ff0000 : na )
//hline(len1, title='Upper', color=color.red, linestyle=hline.style_dotted, linewidth=2)
//hline(len2, title='Lower', color=color.green, linestyle=hline.style_dotted, linewidth=2)



// === WAE - LazyBear === 
// ==================================================
sensitivity = 150
fastLength= 8 // 20
slowLength= 56 // 40
channelLength= 20
wae_mult = 2

DEAD_ZONE = nz(rma(tr(true),100)) * 3.7

calc_macd(source, fastLength, slowLength) =>
	fastMA = ema(source, fastLength)
	slowMA = ema(source, slowLength)
	fastMA - slowMA

calc_BBUpper(source, length, wae_mult) => 
	wae_basis = sma(source, length)
	wae_dev = wae_mult * stdev(source, length)
	t = wae_basis + wae_dev 
	[t]

calc_BBLower(source, length, wae_mult) => 
	wae_basis = sma(source, length)
	wae_dev = wae_mult * stdev(source, length)
	t = wae_basis - wae_dev 
	[t]

t1 = (calc_macd(close, fastLength, slowLength) - calc_macd(close[1], fastLength, slowLength))*sensitivity * 100
t2 = (calc_macd(close[2], fastLength, slowLength) - calc_macd(close[3], fastLength, slowLength))*sensitivity * 100

[e1a] = calc_BBUpper(close, channelLength, wae_mult)
[e1b] = calc_BBLower(close, channelLength, wae_mult)
e1 = (e1a - e1b)
//wae_ratio = (e1 / DEAD_ZONE) * 100
//wae_diff = (e1 - DEAD_ZONE) * 100

//trendUp = (t1 >= 0) ? t1 : 0
//trendDown = (t1 < 0) ? (-1*t1) : 0

//plot(trendUp, style=plot.style_circles, linewidth=1, color=(trendUp<trendUp[1])?color.lime:color.green, transp=45, title="UpTrend")
//plot(trendDown, style=plot.style_circles, linewidth=1, color=(trendDown<trendDown[1])?color.orange:color.red, transp=45, title="DownTrend")
//plot(e1, color=#A0522D, title="ExplosionLine")
//plot(DEAD_ZONE, color=color.blue, title="DeadZoneLine")
//plot(wae_ratio, color=color.white, title="WAE Ratio",style=plot.style_circles)
//plot(wae_diff, color=color.white, title="WAE Ratio",style=plot.style_circles)



// === Bollinger on Macd - BSM ===
// ==================================================
SDev = 0.0
bsm_upper_band = 0.0
bsm_lower_band = 0.0
fast = input(8, "Fast Average")
slow = input(26, "Slow Average")
stdv = input(0.8, "Stdv")
m_fast = ema(close,fast)
m_slow = ema(close,slow)
BBMacd = m_fast - m_slow
Avg = ema(BBMacd,9)
SDev := stdev(BBMacd,9)
bsm_upper_band := Avg + stdv * SDev
bsm_lower_band := Avg - stdv * SDev
pcol = BBMacd < bsm_lower_band ? #FF0000 : BBMacd > bsm_upper_band ? #008000 : color.blue
pcol1 = BBMacd < bsm_lower_band ? #FF0000 :  na
pcol2 = BBMacd > bsm_upper_band ? #008000  :  na
bsm_macd=plot(BBMacd, title='Line Macd BB', color=pcol, linewidth=2, style=plot.style_line, transp=0)
macd_angle = angle(BBMacd) / 10000
macd_change = change(BBMacd)
plot(macd_angle, color=pcol, title="MACD angle")
plot(macd_change, color=pcol, title="MACD change")

// a=plot(bsm_upper_band, title='Upper Band', color=#00BFFF, linewidth=1, style=plot.style_line, transp=30)
// b=plot(bsm_lower_band, title='Lower Band', color=#00BFFF, linewidth=1, style=plot.style_line, transp=30)
// fill(a, b, color=#00BFFF)

// fill(c, b, color=pcol1, transp=50)
// fill(c, a, color=pcol2, transp=50)


// === Trading Properties ===
// ==================================================
var bb_count = 0
allow_trade = 0
entry_price = 0.00
exit_price = 0.00
trend = SSL_B > ema_200 ? 1 : -1
//plot(trend, title="Trend Direction", color=color.black,style=plot.style_circles)
// plot 
plot(ema_200, title="EMA 200", color=#fff176, linewidth=3)
plot(angle(ema_200), title="EMA angle", color=#fff176, style=plot.style_circles)
plot(bbr, "Bollinger Bands %B", color=color.teal,linewidth=0)
//plot(wae_ratio, color=color.white, title="WAE Ratio",style=plot.style_circles)

ssl_angle = angle(SSL_B)
plot(fast_ma, title="Fast EMA", color=color.yellow, transp=10, linewidth=2)
fast_ma_angle = angle(fast_ma)
plot(fast_ma_angle, title="Fast Angle", color=color.yellow, transp=10,style=plot.style_circles)
p1 = plot(show_Baseline ? SSL_B : na,title='SSL Baseline', color=ssl_color_bar, linewidth=4,transp=0)
plot(ssl_angle,title='SSL Angle', color=ssl_color_bar, linewidth=4,transp=0, style=plot.style_circles)
plot(bb_squeeze[0], title="BB Squeeze", color=bb_squeeze < sqz_threshold ? color.blue : color.gray, transp=10, linewidth=0,style=plot.style_circles)
plot(bb_basis, title="Basis Line", color=color.red, transp=10, linewidth=2)
//plot(basis_angle, title="Basis Angle", color=color.red, transp=10, linewidth=2, style=plot.style_circles)
// SSL Hybrid

// ATR plot
atr_upper = plot(show_atr ? upper_band : na, "+ATR", color=color.white, transp=50)
atr_lower = plot(show_atr ? lower_band : na, "-ATR", color=color.white, transp=50)
 
ubi = plot(show_BB ? bb_upper: na, title="Upper Band Inner", color=color.blue, transp=10, linewidth=1)
usqzi = plot(show_BB ? bb_sqz_upper: na, "Upper band", transp=0, linewidth=1,color=color.red)
lbi = plot(show_BB ? bb_lower:na, title="Lower Band Inner", color=color.blue, transp=10, linewidth=1)
lsqzi = plot(show_BB ? bb_sqz_lower: na, "Lower Band", transp=0, linewidth=1,color=color.red)

//fill(ubi, lbi, title="Center Channel Fill", color=color.black, transp=80)

fill(ubi, usqzi, color=bb_squeeze > sqz_threshold ? color.black : color.blue, transp=20)
fill(lbi, lsqzi, color=bb_squeeze > sqz_threshold ? color.black : color.blue, transp=20)

perc_change = abs( (1 - (close[1] / close)) * 10000 )
//plot(perc_change, title="Change", color=perc_change > 0 ? #00ff00 : #ff0000 )
bgcolor(Session(dz_time) ? #b71c1c : na, title="Deadzone",transp=85)

//plot(show_Baseline ? emaHigh : na,title='Upperk', color=color.gray, linewidth=2,transp=0)
//plot(show_Baseline ? emaLow : na,title='Lowerk', color=color.gray, linewidth=2,transp=0)
//plot(show_Baseline ? upper_band : na,title='Upper band', color=ssl_color_bar, linewidth=2,transp=0)
//plot(show_Baseline ? lower_band : na,title='Lower band', color=ssl_color_bar, linewidth=2,transp=0)

// RES
//plot(res,"RES", style=plot.style_circles, linewidth=0, color=color_res, transp=0)
//barcolor(i_barColor ? c_ntz : na)



// === Trading Methods ===
// ==================================================
entry_signal() =>
	dir = 0
	trading = 0

	// Type of candle - Long / Short
	candle = close > open ? 1 : 0
	waiting = 0
	wae_limit = 75
	Deadzone = Session(dz_time) ? 1 : 0
	sell_counter_trend =  use_counter_sell and trend < 0 and bbr >= 0.98 and candle == 1 and SSL_B < ema_200 ? 1 : 0 
	//and bb_squeeze < sqz_threshold
	//candle_type = (abs(close - open)) (close - low) (high - open)


	// Sell signal
	if (bbr >= 0.98 and candle == 1)
		dir := -1
		trading := 1

		// Fast MA Filter
		if (use_fast_ma) and ( (fast_ma < SSL_B ) )
			dir := na
			trading := 0

		// SSL Cross Basis
		if use_ssl_cross and SSL_B < bb_basis
			dir := na
			trading := 0

		// Use RSI candles only
		if use_rsi and not isup() and bb_squeeze > 120 
			dir := na
			trading := 0

		// BSM Filter
		if use_bsm and BBMacd[2] < bsm_upper_band[2]
			dir := na
			trading := 0

		// WAE Filter 2
		if (use_wae == true and e1 < DEAD_ZONE and bb_squeeze < 90 and bb_squeeze < 50)
			dir := na
			trading := 0

		// Trend Filter
		if (use_trend and trend < 0)
			dir := na
			trading := 0

	// Buy signal
	else if (bbr <= 0.02 and candle == 0)
		dir := 1
		trading := 1

		// Fast Ma Filter
		if (use_fast_ma) and (fast_ma > SSL_B )
			dir := na
			trading := 0

		if use_ssl_cross and SSL_B > bb_basis
			dir := na
			trading := 0

		// Use RSI candles only
		if use_rsi and not isdown() and bb_squeeze > 120
			dir := na
			trading := 0

		// BSM Filter
		if use_bsm and BBMacd[2] > bsm_lower_band[2] and bb_squeeze > 80
			dir := na
			trading := 0

		// Percent Change Filter
		if use_change and perc_change > 6.5 and bb_squeeze > 80
			dir := na
			trading := 0

		// WAE Filter 2
		if (use_wae == true and e1 < DEAD_ZONE and bb_squeeze < 90 and bb_squeeze > 50)
			dir := na
			trading := 0

		// Trend Filter
		if (use_trend and trend > 0)
			dir := na
			trading := 0

	
	else
		dir := na
		trading := 0

	// Special Candles
	if use_spcecial and trend < 0 and bb_squeeze > 120 and SSL_B < bb_basis and fast_ma > SSL_B and close >= SSL_B and open < fast_ma
		dir := 1
		trading := 1

	// Counter Trend
	if (sell_counter_trend and ema_56_down) or (sell_counter_trend and ema_56_up and fast_ma > ema_56 )
		dir := -1
		trading := 1



	// Buy - long wicks 
	if (trend < 0 and bbr <= 0.20 and candle == 0) and (use_wicks and low < (bb_sqz_lower + .00010) ) and (close > bb_sqz_lower)
		dir := 1
		trading := 1

	// Sell - long wicks
	if (trend > 0 and close <= bb_sqz_upper and candle == 1) and (use_wicks and high > bb_sqz_upper )
		dir := -1
		trading := 1

	// Don't trade during high spread periods
	if use_deadzone and Deadzone == 1
		dir := na
		trading := 0


	[dir, trading,waiting]

[trade_dir, trading, waiting] = entry_signal() 

calc_trade_values() =>
	p = 0.00
	rp = 0.00
	sl = 0.00
	pips = 0.00
	p_value = 0.00

	// Sell
	if trade_dir < 0
		sl := upper_band
		rp := (1 - (upper_band / close)) * 100
		p  := (2 / rp) * account_size
	// Buy
	else
		sl := lower_band
		rp := (1 - (lower_band / close)) * 100
		p  := (2 / rp) * account_size
    
    p := round(abs(p))
    rp := abs(rp)
    pips := abs( (close - sl) * 10000 )
    p_value := (account_size * rp) / pips
	 
	[p,rp,sl,pips,p_value]
	
[pos_size,risk_perc, stop_loss,pips, pip_value] = calc_trade_values()


ma=ema(close,2)
angles=tostring( angle(fast_ma), "#.00" )
 + "\nSSL: " + tostring( angle(SSL_B), "#.00" )
 + "\nUpper: " + tostring( angle(bb_upper), "#.00" )
 + "\nLower: " + tostring( angle(bb_lower), "#.00" )
//plot(ma_slope, title="Angle", linewidth=4)


// Buy / Sell indicators
plotshape(trade_dir > 0 and show_entry ? 1 : na, title="Entry Buy", color=trade_dir > 0 ? color.orange : color.green, location = location.belowbar, style=shape.labelup, text="B", textcolor=color.white, size=size.small)
plotshape(trade_dir < 0 and show_entry ? -1 : na, title="Entry Sell", color= trade_dir < 0 ? color.orange : color.red, location = location.abovebar, style=shape.labeldown, text="S", textcolor=color.white, size=size.small)
plot(trading ,title="Trading",color= trading ? color.green : color.gray)




