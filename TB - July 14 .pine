//@version=5
strategy('TB - July 14',
 overlay=true,
 precision=6,
 currency=currency.USD,
 initial_capital=12500,
 default_qty_value=10,
 commission_type=strategy.commission.cash_per_order,
 commission_value = 30,
 process_orders_on_close=true,
 pyramiding=1,
 max_labels_count=500)

red = #ff0062
orange = #ff9800
yellow = #FFFF00
green = #4caf50
lime = #50e600
aqua = #00bcd4
blue = #2962ff
dark_blue = #2962ff
violet = #814dff
white = #ffffff
gray = #707070
black = #000000

// Angle
angle(_src, len) =>
    rad2degree = 180 / 3.14159265359  //pi 
    ang = rad2degree * math.atan((_src[0] - _src[1]) / ta.atr(len))
    ang

// SYM Info
min_tick = syminfo.mintick
var int decimals = int(math.log10(1/min_tick))
decimals := decimals == 2 ? 1 : decimals == 0 ? 1 : decimals
plot(min_tick, title='Min Tick')
plot(decimals, title='Decimals')
get_pip_value(point1, point2, abs_value) =>
    diff_points = abs_value ? math.abs( (point1 - point2) ) : point1 - point2
    pip_value = decimals<3 ? diff_points / syminfo.mintick / 100 : diff_points / syminfo.mintick / 10
    //pip_value = decimals>3 ? diff_points * (math.pow(10, decimals) * syminfo.mintick) : diff_points / syminfo.mintick / 10

// Change
perc_change() =>
    perc = math.abs((1 - close[1] / close) * 10000)
    perc


is_between(p1, p2) =>
    is_inside = p1>p2 ? (close<p1) and (open<p1) and (close>p2) and (open>p2) : (close>p1) and (open>p1) and (close<p2) and (open<p2)
    is_inside
    

    
//
// ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ //
// Moving Averages
// ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ //
// ------------------------------------------------------------------------------------------------------------------
g_ma = "MA ----------------------------------------------------"
inl_cb = "cb"
inl_ma = "ma"
i_ma_time = input.timeframe(title='Timeframe', defval='30',group=g_ma)
ma_type1 = input.string(title="MA Type 1", defval="hma", options=["sma","ema","zema","dema","tema","wma","vwma","smma","rma","hma","lsma","McGinley"],group=g_ma)
ma_type2 = input.string(title="MA Type 2", defval="McGinley", options=["sma","ema","zema","dema","tema","wma","vwma","smma","rma","hma","lsma","McGinley"],group=g_ma)
i_show_ma = input.bool(true,title='Show MAs',group=g_ma)


show_h1 = input.bool(title="M1", defval=false,group=g_ma,inline=inl_cb)
show_h2 = input.bool(title="M2", defval=true,group=g_ma,inline=inl_cb)
show_h3 = input.bool(title="M3", defval=false,group=g_ma,inline=inl_cb)
show_h4 = input.bool(title="M4", defval=true,group=g_ma,inline=inl_cb)
show_h5 = input.bool(title="M5", defval=false,group=g_ma,inline=inl_cb)
show_h6 = input.bool(title="M6", defval=false,group=g_ma,inline=inl_cb)
show_h7 = input.bool(title="M7", defval=false,group=g_ma,inline=inl_cb)
show_h8 = input.bool(title="M8", defval=false,group=g_ma,inline=inl_cb)
show_h9 = input.bool(title="M9", defval=false,group=g_ma,inline=inl_cb)

l1 = input.int(5,title="M1",group=g_ma,inline=inl_ma)
l2 = input.int(15,title="M2",group=g_ma,inline=inl_ma)
l3 = input.int(50,title="M3",group=g_ma,inline=inl_ma)
l4 = input.int(75,title="M4",group=g_ma,inline=inl_ma)
l5 = input.int(100,title="M5",group=g_ma,inline=inl_ma)
l6 = input.int(200,title="M6",group=g_ma,inline=inl_ma)
l7 = input.int(300,title="M7",group=g_ma,inline=inl_ma)
l8 = input.int(500,title="M8",group=g_ma,inline=inl_ma)
l9 = input.int(1000,title="M9",group=g_ma,inline=inl_ma)

angle_amount = 14 //input.int(14, title="Angle Amount",group=g_ma)
i_gaps = input.bool(false,title='Use Gaps',group=g_ma)
i_lookahead = input.bool(true,title='Use Lookahead',group=g_ma)
show_ma_candles = false //input.bool(false,title="Show Candle Division",group=g_ma)
show_angles = input.bool(false,title='Show Angles',group=g_ma)
i_offset = 0 //input.int(0, title="Offset",group=g_ma) 
src = close //input.source(close, "Source",group=g_ma)
i_ma_use_smooth = false //input.bool(false, title="Use Smoothing",group=g_ma)
i_ma_smooth = 1 //input.int(1,title="Smoothing",group=g_ma)
use_multiple = false //input.bool(false, title="Use Multiply",group=g_ma)
multi_value = 10 //input.int(10, title="Multiply Value",group=g_ma)

g_fill = "Fills"
inl_fill = "fill"
inl_conv = "conv"
i_ma_select = input.int(3, title="Colorized", options=[1,2,3,4,5,6,7,8],group=g_fill)
show_fill = input.bool(title="Show Fill", defval=true,inline=inl_fill,group=g_fill)
show_conv = input.bool(title="Show Conv", defval=true,inline=inl_fill,group=g_fill)
conv_amount = input.float(20, title="Conv Amount", step=1,inline=inl_conv,group=g_fill ) // 4

g_angles = "MA Angles ----------------------------------------------------"
inl_angles = "angles"
i_ang_1 =  input.int(1, title='A1',group=g_angles,inline=inl_angles)
i_ang_2 =  input.int(2, title='A2',group=g_angles,inline=inl_angles)
i_ang_3 =  input.int(3, title='A3',group=g_angles,inline=inl_angles)
i_ang_4 =  input.int(4, title='A4',group=g_angles,inline=inl_angles)
i_ang_5 =  input.int(5, title='A5',group=g_angles,inline=inl_angles)
i_ang_6 =  input.int(7, title='A6',group=g_angles,inline=inl_angles)
i_ang_7 =  input.int(10, title='A7',group=g_angles,inline=inl_angles)
i_ang_8 =  input.int(12, title='A8',group=g_angles,inline=inl_angles)

use_candles = input.bool(false,title="Colorize Candles")
g_select = "Select ----------------------------------------------------"
i_sel_ma = input.int(title='MA Select', defval=3, options=[1,2,3,4,5,6,7,8,9], group=g_select)
i_sel_angle = input.int(title="Angle Select", defval=3, options=[1,2,3,4,5,7,10,12,14],group=g_select)


// Lines and Angles
ma_graph(len,type) =>
    ma =0.0
    length = 1
    if use_multiple
        length := len * multi_value
    else 
        length := len

    if type == 'sma' // Simple
        ma := ta.sma(src,length) 

    if type == 'ema' // Exponential
        ma := ta.ema(src,length)

    if type == 'zema' // Zero Lag Exponential
        e1 = ta.ema(close,length)
        e2 = ta.ema(e1,length)
        diff = e1 - e2
        ma := e1 + diff 

    if type=="dema" // Double Exponential
        e = ta.ema(src, length)
        ma := 2 * e - ta.ema(e, length)
    if type == 'tema' // Triple Exponential
        ema1 = ta.ema(src, length)
        ema2 = ta.ema(ema1, length)
        ema3 = ta.ema(ema2, length)
        ma := 3 * (ema1 - ema2) + ema3
    if type == 'wma' // Weighted
        ma := ta.wma(src,length)
    if type == 'vwma' // Volume Weighted
        ma := ta.vwma(src,length)
    if type=="smma" // Smoothed
        w = ta.wma(src, length)
        ma := na(w[1]) ? ta.sma(src, length) : (w[1] * (length - 1) + src) / length
    if type == "rma"
        ma := ta.rma(src, length)
    if type == 'hma' // Hull
        ma := ta.hma(src, length)
       // ma := ta.wma(2*ta.wma(src, length/2)-ta.wma(src, length), math.floor(math.sqrt(length) ))
    if type=="lsma" // Least Squares
        ma := ta.linreg(src, length, 0)
    if type=="McGinley"
        mg = 0.0
        mg := na(mg[1]) ? ta.ema(src, length) : mg[1] + (src - mg[1]) / (length * math.pow(src/mg[1], 4))
        ma :=mg

    if i_ma_use_smooth
        ma := ta.sma(ma,i_ma_smooth)
    ma


ma_conv(t1, t2) =>
    
    diff = get_pip_value(t1, t2, true)
    conv = show_conv and diff < conv_amount and diff > conv_amount * -1 ? true : false
    [diff, conv]


// === HMA ===
// ==================================================
[h1,h2,h3,h4,h5,h6,h7,h8,h9] = request.security(syminfo.tickerid, "", 
 [ma_graph(l1,ma_type1)
 ,ma_graph(l2,ma_type1)
 ,ma_graph(l3,ma_type1)
 ,ma_graph(l4,ma_type1)
 ,ma_graph(l5,ma_type1)
 ,ma_graph(l6,ma_type1)
 ,ma_graph(l7,ma_type1)
 ,ma_graph(l8,ma_type1)
 ,ma_graph(l9,ma_type1)
 ]
 ,gaps=i_gaps?barmerge.gaps_on:barmerge.gaps_off
 ,lookahead=i_lookahead ? barmerge.lookahead_on : barmerge.lookahead_off ) 

[h1_a,h2_a,h3_a,h4_a,h5_a,h6_a,h7_a,h8_a,h9_a] = request.security(syminfo.tickerid, i_ma_time, 
 [angle(h1,angle_amount)
 ,angle(h2,angle_amount)
 ,angle(h3,angle_amount)
 ,angle(h4,angle_amount)
 ,angle(h5,angle_amount)
 ,angle(h6,angle_amount)
 ,angle(h7,angle_amount)
 ,angle(h8,angle_amount)
 ,angle(h9,angle_amount)
 ]
 ,gaps=i_gaps?barmerge.gaps_on:barmerge.gaps_off
 ,lookahead=i_lookahead ? barmerge.lookahead_on : barmerge.lookahead_off ) 

[h2_h4_diff,h2_h4_conv] = ma_conv(h2,h4)
[h4_h5_diff,h4_h5_conv] = ma_conv(h4,h5)
[h5_h6_diff,h5_h6_conv] = ma_conv(h5,h6)
[h7_h8_diff,h7_h8_conv] = ma_conv(h7,h8)



// === Multi-Timeframe ===
[h1_m,h2_m,h3_m,h4_m,h5_m,h6_m,h7_m,h8_m,h9_m] = request.security(syminfo.tickerid, i_ma_time, 
 [ma_graph(l1,ma_type1)
 ,ma_graph(l2,ma_type1)
 ,ma_graph(l3,ma_type1)
 ,ma_graph(l4,ma_type1)
 ,ma_graph(l5,ma_type1)
 ,ma_graph(l6,ma_type1)
 ,ma_graph(l7,ma_type1)
 ,ma_graph(l8,ma_type1)
 ,ma_graph(l9,ma_type1)
 ]
 ,gaps=i_gaps?barmerge.gaps_on:barmerge.gaps_off
 ,lookahead=i_lookahead ? barmerge.lookahead_on : barmerge.lookahead_off ) 

// Angles
[h1_a_m,h2_a_m,h3_a_m,h4_a_m,h5_a_m,h6_a_m,h7_a_m,h8_a_m,h9_a_m] = request.security(syminfo.tickerid, i_ma_time, 
 [angle(h1_m,angle_amount)
 ,angle(h2_m,angle_amount)
 ,angle(h3_m,angle_amount)
 ,angle(h4_m,angle_amount)
 ,angle(h5_m,angle_amount)
 ,angle(h6_m,angle_amount)
 ,angle(h7_m,angle_amount)
 ,angle(h8_m,angle_amount)
 ,angle(h9_m,angle_amount)
 ]
 ,gaps=i_gaps?barmerge.gaps_on:barmerge.gaps_off
 ,lookahead=i_lookahead ? barmerge.lookahead_on : barmerge.lookahead_off ) 

[h2_h4_diff_m,h2_h4_conv_m] = ma_conv(h2_m,h4_m)
[h4_h5_diff_m,h4_h5_conv_m] = ma_conv(h4_m,h5_m)
[h5_h6_diff_m,h5_h6_conv_m] = ma_conv(h5_m,h6_m)
[h7_h8_diff_m,h7_h8_conv_m] = ma_conv(h7_m,h8_m)

// Colorized MA angles
ma_select()=>
    float select = switch i_ma_select
        2 => h2_a_m
        3 => h3_a_m
        4 => h4_a_m
        5 => h5_a_m
        6 => h6_a_m
        7 => h7_a_m
        8 => h8_a_m
        => h5_a_m

    ma_a = math.abs(select)
    ma_zone =  
     ma_a   < i_ang_1 ? 0 
     : ma_a < i_ang_2 ? 1 
     : ma_a < i_ang_3 ? 2 
     : ma_a < i_ang_4 ? 3 
     : ma_a < i_ang_5 ? 4 
     : ma_a < i_ang_6 ? 5 
     : ma_a < i_ang_7 ? 6 
     : ma_a < i_ang_8 ? 7 
     : ma_a > i_ang_7 ? 8 
     : na

    ma_color = 
     ma_zone   == 0 ? red 
     : ma_zone == 1 ? orange 
     : ma_zone == 2 ? yellow 
     : ma_zone == 3 ? gray 
     : ma_zone == 4 ? green 
     : ma_zone == 5 ? lime
     : ma_zone == 6 ? blue
     : ma_zone == 7 ? aqua
     : ma_zone == 8 ? white : na

    [ma_color]

[ma_color] = ma_select()


// === McGinley ===
// ==================================================
[m1_m,m2_m,m3_m,m4_m,m5_m,m6_m,m7_m,m8_m,m9_m] = request.security(syminfo.tickerid, i_ma_time, 
 [ma_graph(l1,ma_type2)
 ,ma_graph(l2,ma_type2)
 ,ma_graph(l3,ma_type2)
 ,ma_graph(l4,ma_type2)
 ,ma_graph(l5,ma_type2)
 ,ma_graph(l6,ma_type2)
 ,ma_graph(l7,ma_type2)
 ,ma_graph(l8,ma_type2)
 ,ma_graph(l9,ma_type2)
 ]
 ,gaps=i_gaps?barmerge.gaps_on:barmerge.gaps_off
 ,lookahead=i_lookahead ? barmerge.lookahead_on : barmerge.lookahead_off ) 

[m1_a_m,m2_a_m,m3_a_m,m4_a_m,m5_a_m,m6_a_m,m7_a_m,m8_a_m,m9_a_m] = request.security(syminfo.tickerid, i_ma_time, 
 [angle(m1_m,angle_amount)
 ,angle(m2_m,angle_amount)
 ,angle(m3_m,angle_amount)
 ,angle(m4_m,angle_amount)
 ,angle(m5_m,angle_amount)
 ,angle(m6_m,angle_amount)
 ,angle(m7_m,angle_amount)
 ,angle(m8_m,angle_amount)
 ,angle(m9_m,angle_amount)
 ]
 ,gaps=i_gaps?barmerge.gaps_on:barmerge.gaps_off
 ,lookahead=i_lookahead ? barmerge.lookahead_on : barmerge.lookahead_off ) 

[m2_m4_diff_m,m2_m4_conv_m] = ma_conv(m2_m,m4_m)
[m4_m5_diff_m,m4_m5_conv_m] = ma_conv(m4_m,m5_m)
[m5_m6_diff_m,m5_m6_conv_m] = ma_conv(m5_m,m6_m)
[m7_m8_diff_m,m7_m8_conv_m] = ma_conv(m7_m,m8_m)



// Colorize candles based on MA angles
//barcolor(use_candles? ma_color : na)
// Plots
l_width = 2

get_distance = get_pip_value(h7_m, h8_m, true)
//plot(get_distance, title='Distance',color=color.new(blue,90))


// Angles
// plot(show_angles and h1_a_m ? h1_a_m : na,color=h1_a_m>0?color.new(aqua,100):color.new(orange,100),title="M1 A")
// plot(show_angles and h2_a_m ? h2_a_m : na,color=h2_a_m>0 ? color.new(color.green,100): color.new(color.red,100),title="M2 A")
// plot(show_angles and h3_a_m ? h3_a_m : na,color=i_ma_select==3 ? color.new(ma_color,100) : color.new(color.blue,100),title="M3 A")
// plot(show_angles and h4_a_m ? h4_a_m : na,color=i_ma_select==4 ? color.new(ma_color,100) : color.new(yellow,100),title="M4 A")
// plot(show_angles and h5_a_m ? h5_a_m : na,color=i_ma_select==5 ? color.new(ma_color,100) : color.new(orange,100),title="M5 A")
// plot(show_angles and h6_a_m ? h6_a_m : na,color=i_ma_select==6 ? color.new(ma_color,100) : color.new(red,100), title="M6 A")
// plot(show_angles and h7_a_m ? h7_a_m : na,color=i_ma_select==7 ? color.new(ma_color,100) : color.new(orange,100),title="M7 A")
// plot(show_angles and h8_a_m ? h8_a_m : na,color=i_ma_select==8 ? color.new(ma_color,100) : color.new(red,100),title="M8 A")
//plot(show_angles and h9_a_m ? h9_a_m : na,color=i_ma_select==9 ? color.new(ma_color,100) : color.new(red,100),title="M9 A")


// Plot Line
//p_h1_m = plot(h1_m and show_h1?h1_m:na, style=plot.style_line, color= h1_a_m>0?aqua:orange,title="M1",linewidth=i_ma_select==1?l_width:1,offset = i_offset)
// p_h2_m = plot(i_show_ma and h2_m and show_h2?h2_m:na, style=plot.style_line, color= h2_a_m>0 ? green: red,title="M2",linewidth=i_ma_select==2?l_width:1,offset = i_offset)
// p_h3_m = plot(i_show_ma and h3_m and show_h3?h3_m:na, style=plot.style_line, color= i_ma_select==3 ? ma_color : h3_a_m>0 ? blue : aqua,title="M3",linewidth=i_ma_select==3?l_width:1,offset = i_offset)
// p_h4_m = plot(i_show_ma and h4_m and show_h4?h4_m:na, style=plot.style_line, color= i_ma_select==4 ? ma_color : yellow,title="M4",linewidth=i_ma_select==4?l_width:1,offset = i_offset)
// p_h5_m = plot(i_show_ma and h5_m and show_h5?h5_m:na, style=plot.style_line, color= i_ma_select==5 ? ma_color : h5_a_m<1 and h5_a_m>-1 ? aqua : h5_a_m>0 ? orange : h5_a_m<0 and h5_m>h6_m ? red : green,title="M5",linewidth=i_ma_select==5?l_width:1,offset = i_offset)
// p_h6_m = plot(i_show_ma and h6_m and show_h6?h6_m:na, style=plot.style_line, color= i_ma_select==6 ? ma_color : h6_a_m > 0 ? red : lime, title="M6",linewidth=i_ma_select==6?l_width:1,offset = i_offset)
// p_h7_m = plot(i_show_ma and h7_m and show_h7?h7_m:na, style=plot.style_line, color= i_ma_select==7 ? ma_color : h7_a_m>0 ? orange : h7_a_m<0 and h7_m>h8_m ? red : green,title="M7",linewidth=i_ma_select==7?l_width:1,offset = i_offset)
// p_h8_m = plot(i_show_ma and h8_m and show_h8?h8_m:na, style=plot.style_line, color= i_ma_select==8 ? ma_color : h8_a_m>0 ? red : lime,title="M8",linewidth=i_ma_select==8?l_width:1,offset = i_offset)
// p_h9_m = plot(i_show_ma and h9_m and show_h9?h9_m:na, style=plot.style_line, color= h9_a_m>0 ? red : lime,title="M9",linewidth=i_ma_select==9?l_width:1,offset = i_offset)

// Fills
//// fill(p_h5_m,p_h6_m,title="S5/S6 Conv", color=show_fill and h5_m<h6_m?color.new(green,90): show_fill ? color.new(red,90):na)
//// fill(p_h7_m,p_h8_m,title="S7/S8 Conv", color=show_fill and h7_m<h8_m?color.new(green,90): show_fill ? color.new(red,90):na)
// fill(p_h5_m,p_h6_m,title="M5/M6 Conv", color=h5_h6_conv_m and h5_m>h6_m?color.new(red,70) : h5_h6_conv_m and h5_m<h6_m?color.new(green,70) : na)
// fill(p_h7_m,p_h8_m,title="M7/M8 Conv", color=h7_h8_conv_m and h7_m>h8_m?color.new(red,70) : h7_h8_conv_m and h7_m<h8_m?color.new(green,70) : na)


var int flag_u = 0
var int flag_d = 0
flag_u := h2_a>20 and h2_a>h2_a[1] ? 1 : flag_u==1 and h2_a<h2_a[1] ? 0 : flag_u==1 and h2_a>0 ? 1 : na
flag_d := h2_a<-10 and h2_a<h2_a[1] ? -1 : flag_d==-1 and h2_a>h2_a[1] ? 0 : flag_d==-1 and h2_a<0 ? -1 : na
//plotshape(flag_u==0 ? 1 : na,title="Flag Up",color=red ,style=shape.circle,location=location.top)
//plotshape(flag_d==0?1:na,title="Flag Down",color=green ,style=shape.circle,location=location.bottom)



// Previous State
// var state_change = blue
// ss_red2 = m5 > m6 and m5_a > 0
// ss_orange2 = m5 > m6 and m5_a < 0
// ss_lime2 = m5 < m6 and m6_a > 0
// ss_green2 = m5 < m6 and m6_a < 0
// m5_m6_c = ss_red2 ? red : ss_orange2 ? orange : ss_lime2 ? lime : ss_green2 ? green : na

// Distance to M8
h8_dist = get_pip_value(close, h8, true)
//plot(m8_dist, title='M8 Dist', color= m8_dist>20 ? color.new(red,100) : color.new(green,100) )

// plot(i_show_ma and show_m4 ? h4 : na, color=h4_a>0?yellow:orange, title='M4 HMA')
// plot(i_show_ma and show_m7 ? h7 : na, color=h7_a>0?green:red, title='M7 HMA')
// plot(i_show_ma and show_m8 ? h8 : na, color=h8_a>0?green:red, title='M8 HMA')




// SUPERTREND
// ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ //

g_super = 'Supertrend ----------------------------------------------------'
i_show_sup = input.bool(true, title='Show Supertrend', group=g_super)
i_time_sup = input.timeframe(title='Timeframe', defval='120', group=g_super)
inl_sup1 = "inl_sup1"
i_sup_show1= input.bool(false,title='Sup 1',inline=inl_sup1, group=g_super)
i_sup1_a = input.int(10,title='',inline=inl_sup1)
i_sup1_b = input.int(1,title='',inline=inl_sup1)
inl_sup2 = "inl_sup2"
i_sup_show2= input.bool(true,title='Sup 2',inline=inl_sup2, group=g_super)
i_sup2_a = input.int(11,title='',inline=inl_sup2)
i_sup2_b = input.int(2,title='',inline=inl_sup2)
inl_sup3 = "inl_sup3"
i_sup_show3= input.bool(false,title='Sup 3',inline=inl_sup3, group=g_super)
i_sup3_a = input.int(12,title='',inline=inl_sup3)
i_sup3_b = input.int(3,title='',inline=inl_sup3)
inl_sup4 = "inl_sup4"
i_sup_show4 = input.bool(true,title='Sup 4',inline=inl_sup4, group=g_super) 
i_sup4_a = input.int(5,title='',inline=inl_sup4) 
i_sup4_b = input.int(5,title='',inline=inl_sup4) 

sup_src = hl2 //input(hl2, title='Source', group=g_super)
//Multiplier = input.float(title='ATR Multiplier', step=0.1, defval=3.0)
changeATR = true // input(title='Change ATR Calculation Method?', defval=true, group=g_super)
showsignals = false// input(title='Show Buy/Sell Signals ?', defval=false, group=g_super)

supertend(p, m) =>
    atr2 = ta.sma(ta.tr, p)
    atr = changeATR ? ta.atr(p) : atr2
    up = sup_src - m * atr
    up1 = nz(up[1], up)
    up := close[1] > up1 ? math.max(up, up1) : up
    dn = sup_src + m * atr
    dn1 = nz(dn[1], dn)
    dn := close[1] < dn1 ? math.min(dn, dn1) : dn
    trend = 1
    trend := nz(trend[1], trend)
    trend := trend == -1 and close > dn1 ? 1 : trend == 1 and close < up1 ? -1 : trend
    b_sig = trend == 1 and trend[1] == -1

    s_sig = trend == -1 and trend[1] == 1

    [trend, up, dn, b_sig, s_sig]


[trend1, up1, dn1, b_sig1, s_sig1]  = request.security(syminfo.tickerid, i_time_sup, supertend(i_sup1_a, i_sup1_b)  )
[trend2, up2, dn2, b_sig2, s_sig2]  = request.security(syminfo.tickerid, i_time_sup, supertend(i_sup2_a, i_sup2_b)  )
[trend3, up3, dn3, b_sig3, s_sig3]  = request.security(syminfo.tickerid, i_time_sup, supertend(i_sup3_a, i_sup3_b)  )
[trend4, up4, dn4, b_sig4, s_sig4]  = request.security(syminfo.tickerid, i_time_sup, supertend(i_sup4_a, i_sup4_b)  )

newbar(res) => ta.change(time(res)) == 0 ? 0 : 1
change = newbar(i_time_sup)
plot(change)

// Supertrend 1
up1 := change ? up1 : up1[1]
trend1  := change ? trend1 : trend1[1]
upPlot1 = plot(i_show_sup and trend1 == 1 ? up1 : up1, title='Up Trend 1', style=plot.style_linebr, linewidth=1, 
 color=i_sup_show1 and trend1 == 1 ? aqua : i_sup_show1 and trend1 == -1 ? color.new(aqua,80) : na  )
dn1 := change ? dn1 : dn1[1]
dnPlot1 = plot(i_show_sup and trend1 == -1 ? dn1 : dn1 , title='Down Trend 1', style=plot.style_linebr, linewidth=1, 
 color=i_sup_show1 and trend1 == -1 ? yellow : i_sup_show1 and trend1 == 1 ? color.new(yellow,80) : na )
dir_chng1 = trend1[1]==-1 and trend1==1 ? dn1[1] : trend1[1]==1 and trend1==-1 ? up1[1] : 0

// Supertrend 2
up2 := change ? up2 : up2[1]
trend2  := change ? trend2 : trend2[1]
upPlot2 = plot(trend2 == 1 ? up2 : na, title='Up Trend 2', style=plot.style_linebr, linewidth=1, color=i_sup_show2 ? blue:na )
dn2 := change ? dn2 : dn2[1]
dnPlot2 = plot(trend2 == -1 ? dn2 : na , title='Down Trend 2', style=plot.style_linebr, linewidth=1, color=i_sup_show2 ? orange : na )

// Supertrend 3
up3     := change ? up3 : up3[1]
trend3  := change ? trend3 : trend3[1]
upPlot3 = plot(trend3==1 ? up3 : na, title='Up Trend 3', style=plot.style_linebr, linewidth=1, color=i_sup_show3 ? lime:na )
dn3 := change ? dn3 : dn3[1]
dnPlot3 = plot(trend3==-1 ? dn3 : na , title='Down Trend 3', style=plot.style_linebr, linewidth=1, color=i_sup_show3 ? violet :na )

// Supertrend 4
up4     := change ? up4 : up4[1]
trend4  := change ? trend4 : trend4[1]
upPlot4 = plot(trend4==1 ? up4 : na, title='Up Trend 4', style=plot.style_linebr, linewidth=1, color=i_sup_show4 ? green:na )
dn4 := change ? dn4 : dn4[1]
dnPlot4 = plot(trend4==-1 ? dn4 : na , title='Down Trend 4', style=plot.style_linebr, linewidth=1, color=i_sup_show4 ?red :na )
     

     
     
//
// ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ //
// HIGHEST LOWEST
// ------------------------------------------------------------------------------------------------------------------
g_hl = 'HIGHEST LOWEST -------------------------------------------------------------'
hl_res = input.timeframe(title='Resolution', defval='60', group=g_hl)
hl_res2 = input.timeframe(title='Resolution 2', defval='120', group=g_hl)
i_use_hl1 = input.bool(false,title='Display HL1', group=g_hl)
i_use_hl2 = input.bool(false,title='Display HL2', group=g_hl)
i_show_bars = input.bool(false,title='Show Bars', group=g_hl)
gaps = input.bool(false,title='Bar Merge Gaps On', group=g_hl)
hl_len = input(title='Length', defval=15, group=g_hl)  // 21
src_h = input(title='Source', defval=close, group=g_hl)
src_l = input(title='Source', defval=close, group=g_hl)
use_diff = input(title='Filter Diff', defval=true, group=g_hl)
diff_range = input(10, title='FDiff Range', group=g_hl)
offset = input(title='Offset', defval=0, group=g_hl)
i_hl_smooth = input(title='Smooth', defval=true, group=g_hl)
i_hl_smooth_len = input(title='Smooth Length', defval=14, group=g_hl)

// HL 1
var float hh = 0.0
var float ll = 0.0
hh := timeframe.change(hl_res) ? request.security(syminfo.tickerid, hl_res, ta.highest(src_h, hl_len), gaps=gaps ? barmerge.gaps_on : barmerge.gaps_off) : hh[1]
ll := timeframe.change(hl_res) ? request.security(syminfo.tickerid, hl_res, ta.lowest(src_l, hl_len), gaps=gaps ? barmerge.gaps_on : barmerge.gaps_off) : ll[1]
hl_mid = i_hl_smooth ? ta.sma((hh + ll) * 0.5, i_hl_smooth_len) : (hh + ll) * 0.5
hh_a = angle(hh, hl_len)
ll_a = angle(ll, hl_len)
diff = hh - ll
h1_p1 = plot(i_use_hl1 ? hh : na, title='HH', linewidth=2, color=hh_a == 0 ? violet : red)
h1_p2 = plot(i_use_hl1 ? ll : na, title='LL', linewidth=2, color=ll_a == 0 ? aqua : blue)
//plot(i_use_hl1 ? hl_mid : na, title='HL Midline', linewidth=2)
//barcolor(i_show_bars and i_use_hl1 and timeframe.change(hl_res) ? blue : na, title='HL1 Bar Color')

// HL 2
var float hh2 = 0.0
var float ll2 = 0.0
hh2 := timeframe.change(hl_res2) ? request.security(syminfo.tickerid, hl_res2, ta.highest(src_h, hl_len), gaps=gaps ? barmerge.gaps_on : barmerge.gaps_off) : hh2[1]
ll2 := timeframe.change(hl_res2) ? request.security(syminfo.tickerid, hl_res2, ta.lowest(src_l, hl_len), gaps=gaps ? barmerge.gaps_on : barmerge.gaps_off) : ll2[1]
hh2_a = angle(hh2, hl_len)
ll2_a = angle(ll2, hl_len)
diff2 = hh2 - ll2
hl_mid2 = i_hl_smooth ? ta.sma((hh2 + ll2) * 0.5, i_hl_smooth_len) : (hh2 + ll2) * 0.5

hh2_p1 = plot(i_use_hl2 ? hh2 : na, title='HH2', linewidth=2, color=hh2_a == 0 ? #ff00ff : #ff0000)
hh2_p2 = plot(i_use_hl2 ? ll2 : na, title='LL2', linewidth=2, color=ll2_a == 0 ? #55d51a : #00FFFF)
plot(i_use_hl2 ? hl_mid2 : na, title='HL2 Mid', linewidth=2)
//fill(p1, p2, title='Fill', color=#33333365)
//plot(diff, title='Diff', color=color.new(color.blue, 100), linewidth=0)




// === RSI Wicks ===
// ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ //
g_rsi = 'RSI Wicks -------------------------------------------------------------'
inl_rsi1 = '1'
inl_rsi2 = '2'
i_show_rsi  = input.bool(false, title='Show RSI', inline=inl_rsi1, group=g_rsi)
rsi_strong  = input.bool(title='Strongest', defval=false, inline=inl_rsi1, group=g_rsi)
rsi_len     = input.int(12, minval=1, title='Length', inline=inl_rsi2, group=g_rsi) // 12
rsi_len_m   = input.int(12, minval=1, title='Length', inline=inl_rsi2, group=g_rsi) // 20 14
rsi_pos     = 'tb' //input.string(title='Position', defval='tb', options=['tb', 't', 'b'], inline=inl_rsi2, group=g_rsi)
i_use_rsi_candles = input.bool(false, title='Use RSI Candles', inline=inl_rsi1, group=g_rsi)


g_rsi_time = ''
inl_rsi3 = '3'
use_rsi_curr = input.bool(title='Show Current', defval=true, inline=inl_rsi3, group=g_rsi_time)
use_rsi_multi = input.bool(title='Show Multi', defval=false, inline=inl_rsi3, group=g_rsi_time)
i_rsi_time = input.timeframe(title='Timeframe', defval='', group=g_rsi_time)

wicks = true // input(true, title="Wicks based on stand-alone RSI")
rsi_target_up = 70  // input(70, minval=1, title="RSI Up")
rsi_target_down = 30  // input(30, minval=1, title="RSI Down")
src_close = close
src_open = open
src_high = high
src_low = low
rsi_l_up = 70
rsi_l_dn = 30

//trend4==1 and trend1==1 and close<up1 and RSI_low<rsi_target_down

rsi_wicks(rsi_len) =>
    norm_close = math.avg(src_close, src_close[1])
    gain_loss_close = ta.change(src_close) / norm_close
    RSI_close = 50 + 50 * ta.rma(gain_loss_close, rsi_len) / ta.rma(math.abs(gain_loss_close), rsi_len)

    norm_open = if wicks == true
        math.avg(src_open, src_open[1])
    else
        math.avg(src_close, src_close[1])
    gain_loss_open = ta.change(src_open) / norm_open
    RSI_open = 50 + 50 * ta.rma(gain_loss_open, rsi_len) / ta.rma(math.abs(gain_loss_open), rsi_len)

    norm_high = if wicks == true
        math.avg(src_high, src_high[1])
    else
        math.avg(src_close, src_close[1])
    gain_loss_high = ta.change(src_high) / norm_high
    RSI_high = 50 + 50 * ta.rma(gain_loss_high, rsi_len) / ta.rma(math.abs(gain_loss_high), rsi_len)

    norm_low = if wicks == true
        math.avg(src_low, src_low[1])
    else
        math.avg(src_close, src_close[1])
    gain_loss_low = ta.change(src_low) / norm_low
    RSI_low = 50 + 50 * ta.rma(gain_loss_low, rsi_len) / ta.rma(math.abs(gain_loss_low), rsi_len)

    [RSI_open, RSI_close, RSI_high, RSI_low]

[RSI_open, RSI_close, RSI_high, RSI_low] = rsi_wicks(rsi_len)
[RSI_open_2, RSI_close_2, RSI_high_2, RSI_low_2] = rsi_wicks(rsi_len_m)
[close_m, open_m, RSI_open_m, RSI_high_m,RSI_low_m,RSI_close_m] = request.security(syminfo.tickerid, i_rsi_time, [close, open, RSI_open_2,RSI_high_2,RSI_low_2,RSI_close_2])
//open_m = request.security(syminfo.tickerid, i_rsi_time, open)
// RSI_open_m = request.security(syminfo.tickerid, i_rsi_time, RSI_open_2)
// RSI_high_m = request.security(syminfo.tickerid, i_rsi_time, RSI_high_2)
// RSI_low_m = request.security(syminfo.tickerid, i_rsi_time, RSI_low_2)
// RSI_close_m = request.security(syminfo.tickerid, i_rsi_time, RSI_close_2)
//[close_m, open_m,RSI_open_m, RSI_high_m, RSI_low_m, RSI_close_m] = request.security(syminfo.tickerid, i_rsi_time, [close, open, RSI_open,RSI_high,RSI_low,RSI_close] )

// plot(RSI_high_m, title='RSI High', color=color.new(blue,100))
// plot(RSI_open_m, title='RSI Open', color=color.new(blue,100))
// plot(RSI_close_m, title='RSI close', color=color.new(blue,100))
// plot(RSI_low_m, title='RSI Low', color=color.new(blue,100))

// Why are normal and multi different? You are using 'close_m > open_m' instead of close > open
// Sell
up_cond_m = close_m > open_m
plotshape(i_show_rsi and use_rsi_multi and RSI_high_m > rsi_target_up and RSI_close_m < rsi_target_up ? 1 : na, title='Up Weak', color=color.new(yellow, 0), style=shape.circle, location=location.top)
plotshape(i_show_rsi and use_rsi_multi and RSI_close_m > rsi_target_up ? 1 : na, title='Up Mid', color=color.new(orange, 0), style=shape.circle, location=location.top)
//plot(perc_change(RSI_close_m)*.01, title="Percent Change", color=color.new(blue,100),style=plot.style_circles)
//plotshape(i_show_rsi and use_rsi_multi and close>open and RSI_close_m > rsi_target_up ? 1 : na, title='Up High', color=color.new(#ff0000, 0), style=shape.circle, location=location.top)
// Buy
down_cond_m = close_m < open_m
plotshape(i_show_rsi and use_rsi_multi and RSI_low_m < rsi_target_down and RSI_close_m>rsi_target_down ? 1 : na, title='Low Weak', color=color.new(violet, 0), style=shape.circle, location=location.bottom)
plotshape(i_show_rsi and use_rsi_multi and RSI_close_m < rsi_target_down ? 1 : na, title='Low Mid', color=color.new(blue, 0), style=shape.circle, location=location.bottom)

//plotshape(i_show_rsi and use_rsi_multi and close<open and RSI_close_m < rsi_target_down ? 1 : na, title='Up High Multi', color=green, style=shape.circle, location=location.bottom)




g_bb = 'BB Bands ----------------------------------------------------'
i_show_bb_bands = input.bool(false,title='Show BB Bands', group=g_bb)
i_showbb_fast = input.bool(false, title="Show Fast")
i_showbb = input.bool(false, title="Show BB")
i_bb_len_fast = input(20, title='BB Len Fast', group=g_bb)
i_bb_len = input(500, title='BB Len', group=g_bb)
i_show_back = input.bool(false, title='Show Background')
sqz_length = 80
// Fast
bb_basis_f = ta.sma(close, i_bb_len_fast)
dev_f = 2 * ta.stdev(close, i_bb_len_fast)
bb_upper_f = bb_basis_f + dev_f
bb_lower_f = bb_basis_f - dev_f
bb_spread_f = bb_upper_f - bb_lower_f
bb_angle_f = angle(bb_basis_f,1)
// plot(i_show_bb_bands and i_showbb_fast ? bb_basis_f : na,title="Basis")
// plot(i_show_bb_bands and i_showbb_fast ? bb_upper_f : na,title="bb_upper")
// plot(i_show_bb_bands and i_showbb_fast ? bb_lower_f : na,title="bb_lower")

f_bb_slow() =>

    bb_basis = ta.ema(close, i_bb_len)
    dev = 2 * ta.stdev(close, i_bb_len)
    bb_upper = bb_basis + dev
    bb_lower = bb_basis - dev
    bb_spread = bb_upper - bb_lower
    bb_angle = angle(bb_basis,3)
    bb_avgspread = ta.sma(bb_spread, sqz_length)

    [bb_basis,bb_upper,bb_lower,bb_spread,bb_angle,bb_avgspread]

[bb_basis,bb_upper,bb_lower,bb_spread,bb_angle,bb_avgspread] = request.security(syminfo.tickerid, '30', f_bb_slow() )

bb_squeeze = 0.00
bb_squeeze := bb_spread / bb_avgspread * 100
// Zones
bb_zone = bb_squeeze < 53 ? 0 : 
 bb_squeeze < sqz_length ? 1 : 
 bb_squeeze < 120 ? 2 :
 bb_squeeze < 160 ? 3 :
 bb_squeeze > 160 ? 4 :
 bb_squeeze > 200 ? 5 : na
sqz_color = bb_zone == 0 ? #0045b3 :
 bb_zone == 1 ? #ff0062 : 
 bb_zone == 2 ?  gray : 
 bb_zone == 3 ?  #00c3ff : 
 bb_zone == 4 ? white:
 bb_zone == 5 ? yellow: na

bb_zones_color =  sqz_color

//plot(i_show_bb_bands and i_showbb ? bb_basis : na,title="Basis", color=bb_zones_color)
plot(i_show_bb_bands and i_showbb ? bb_upper : na,title="bb_upper")
plot(i_show_bb_bands and i_showbb ? bb_lower : na,title="bb_lower")
//plot(i_show_bb_bands ? bb_angle : na,title="BB Angle 2", color=bb_angle>0?green:red)
//bb_cond = m1 < bb_lower ? 1 : m1 > bb_upper ? -1 : na
//bgcolor(i_show_back and bb_cond == 1 ? aqua : i_show_back and bb_cond == -1 ? orange : na)
//barcolor(bb_cond == 1 ? aqua : bb_cond == -1 ? orange : na)





g_rsi_band = 'RSI + Bands ----------------------------------------------------'
rsi_time = input.timeframe("10",title="Timeframe", group=g_rsi_band)
isBB = true
rsiLengthInput = input.int(14, minval=1, title="RSI Length", group=g_rsi_band) // 21
rsiSourceInput = input.source(close, "Source", group=g_rsi_band)
maLengthInput = input.int(20, title="MA Length", group=g_rsi_band) // 14
bbMultInput = input.float(1.5, minval=0.001, maxval=50, title="BB StdDev", group=g_rsi_band) // 2.0

rsi_bb()=>
    rsi_up = ta.rma(math.max(ta.change(rsiSourceInput), 0), rsiLengthInput)
    rsi_down = ta.rma(-math.min(ta.change(rsiSourceInput), 0), rsiLengthInput)
    rsi = rsi_down == 0 ? 100 : rsi_up == 0 ? 0 : 100 - (100 / (1 + rsi_up / rsi_down))
    rsiMA = ta.sma(rsi, maLengthInput)
    bbUpperBand = rsiMA + ta.stdev(rsi, maLengthInput) * bbMultInput
    bbLowerBand = rsiMA - ta.stdev(rsi, maLengthInput) * bbMultInput

    [rsi,rsiMA,bbUpperBand,bbLowerBand]

[rsi,rsiMA,bbUpperBand,bbLowerBand] = rsi_bb()

rsi_m = request.security(syminfo.tickerid, rsi_time, rsi )
rsiMA_m = request.security(syminfo.tickerid, rsi_time, rsiMA )
bbUpperBand_m = request.security(syminfo.tickerid, rsi_time, bbUpperBand )
bbLowerBand_m = request.security(syminfo.tickerid, rsi_time, bbLowerBand )

// Plot
// plot(rsiMA_m ,title="RSI MA", style=plot.style_circles,color=color.red)
// plot(rsi_m ,title="RSI", style=plot.style_circles,color=color.green)
// plot(bbUpperBand_m ,title="BB up", style=plot.style_circles,color=color.red)
// plot(bbLowerBand_m ,title="BB down", style=plot.style_circles,color=color.green)

rsi_up = rsi_m>bbUpperBand_m and rsiMA_m>50 ? rsiMA_m : na
//plot(rsi_up,title="RSI Up", style=plot.style_circles,color=color.red)
rsi_down = rsi_m<bbLowerBand_m and rsiMA_m<50 ? rsiMA_m : na
//plot(rsi_down,title="RSI Down", style=plot.style_circles,color=color.green)




// === Impulse MACD ===
// ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ //
g_impulse_macd = 'Impulse MACD ----------------------------------------------------'
macd_time = input.timeframe("",title="Timeframe", group=g_impulse_macd)
macd_time_mult = input.timeframe("30",title="Timeframe Multi", group=g_impulse_macd)
macd_len = input(34, title='Length MA', group=g_impulse_macd)
macd_s_len = input(9, title='Length Signal', group=g_impulse_macd)
macd_src = input.source(hlc3, "Source", group=g_impulse_macd) 
macd_show = input.bool(true, title='Show MACD', group=g_impulse_macd)
macd_show_multi = input.bool(true, title='Show MACD Multi', group=g_impulse_macd)

calc_smma(src, len) =>
    smma = 0.0
    sma_1 = ta.sma(src, len)
    smma := na(smma[1]) ? sma_1 : (smma[1] * (len - 1) + src) / len
    smma

calc_zlema(src, length) =>
    ema1 = ta.ema(src, length)
    ema2 = ta.ema(ema1, length)
    d = ema1 - ema2
    ema1 + d

macd_fun()=>
    macd_hi = calc_smma(high, macd_len)
    macd_lo = calc_smma(low, macd_len)
    macd_mi = calc_zlema(macd_src, macd_len)
    macd = macd_mi > macd_hi ? macd_mi - macd_hi : macd_mi < macd_lo ? macd_mi - macd_lo : 0
    macd_s = ta.sma(macd, macd_s_len)
    macd_h = macd - macd_s

    [macd_hi, macd_lo, macd_mi, macd, macd_s, macd_h]

[macd_hi, macd_lo, macd_mi, macd, macd_s, macd_h] = request.security(syminfo.tickerid, macd_time, macd_fun() )
[macd_hi_m, macd_lo_m, macd_mi_m, macd_m, macd_s_m, macd_h_m] = request.security(syminfo.tickerid, macd_time_mult, macd_fun() )

macd_mdc   = macd_src > macd_mi ? macd_src > macd_hi ? lime : green : macd_src < macd_lo ? red : orange
macd_mdc_m = macd_src > macd_mi_m ? macd_src > macd_hi_m ? lime : green : macd_src < macd_lo_m ? red : orange





// ===  Fibo Trend ===
// ==================================================
g_fibo_trend = 'G Fibo Trend ----------------------------------------------------'
inl_fibo = 'inl-fib'
fibo_trend_time = input.timeframe("",title="Timeframe", group=g_fibo_trend)
show_gfibo = input.bool(false,"Show G Fibo Trend",group=g_fibo_trend)
show_candles = input.bool(true,"Fibo Candles",group=g_fibo_trend)
ma_val = input.int(10, title="MA",group=g_fibo_trend ) // 6
fibo_period = input.int(25,"Analysis Period",group=g_fibo_trend) // 50
lowerValue = input.float(0.382,"Lower Fibonacci Level",options=[0.236, 0.382, 0.50, 0.618, 0.786],group=g_fibo_trend)
upperValue = input.float(0.618,"Upper Fibonacci Level",options=[0.236, 0.382, 0.50, 0.618, 0.786],group=g_fibo_trend)
showFill = input.bool(true,"Show Filling",group=g_fibo_trend)
changeCandle = input.bool(true,"Change Candle Color",group=g_fibo_trend)




f_fibo_trend() =>
    ma = ta.wma(close,ma_val)
    max = ta.highest(close, fibo_period)
    min = ta.lowest(close, fibo_period)
    lowerFib = min + (max-min)*lowerValue
    upperFib = min + (max-min)*upperValue
    [ma, lowerFib, upperFib]

[ma, lowerFib, upperFib] = request.security(syminfo.tickerid, fibo_trend_time, f_fibo_trend() )

float closeVal = ma
float openVal = ma
color fibo_color = closeVal>upperFib and openVal>upperFib?green:closeVal<lowerFib and openVal<lowerFib?red:yellow

// maxLine = plot(max and show_gfibo?max : na,color=color.green,title="Max")
// minLine = plot(min and show_gfibo?min : na,color=color.red,title="Min")
// LowerFibLine = plot(lowerFib and show_gfibo?lowerFib : na,color=color.rgb(228, 255, 75, 20),title="Lower Fib")
// UpperFibLine = plot(upperFib and show_gfibo?upperFib : na,color=color.rgb(228, 255, 75, 20),title="Upper Fib")
// fill(maxLine,UpperFibLine,color=showFill?color.rgb(0,255,0,changeCandle?95:70):na)
// fill(UpperFibLine,LowerFibLine,color=showFill?color.rgb(228, 255, 75, changeCandle?95:70):na)
// fill(LowerFibLine,minLine,color=showFill?color.rgb(255,0,0,changeCandle?95:70):na)
barcolor(show_candles? fibo_color : na, title='Fibo Bar Color')
//plotcandle(open,high,low,close,"Bar",color=changeCandle?fibo_color:na,wickcolor=changeCandle?fibo_color:na,bordercolor=changeCandle?fibo_color:na)



// ===  PD Retrace ===
// ==================================================
g_retrace = 'PD Retrace -------------------------------------------------------------'
i_pd_time = input.timeframe('D', "Resolution", group=g_retrace) // 1 hour 4 hour
i_pd_lookback = input.int(1, title='Lookback', group=g_retrace)
i_pd_fibo = input.bool(false, title='Show Fibo', group=g_retrace)
i_pd_labels = input.bool(false, title="Show Labels", group=g_retrace)
i_pd_fills = input.bool(false, title='Fill in Fibo Levels', group=g_retrace)
i_pd_fills_trans = input.int(90, title='Fill Transparency', group=g_retrace)

// Fibo
//close_m = request.security(syminfo.tickerid, i_pd_time, close, lookahead=barmerge.lookahead_on)
fibo0   = request.security(syminfo.tickerid, i_pd_time, high[i_pd_lookback], lookahead=barmerge.lookahead_on)
fibo100 = request.security(syminfo.tickerid, i_pd_time, low[i_pd_lookback], lookahead=barmerge.lookahead_on)
fibo23  = (fibo100-fibo0)*0.786+fibo0
fibo38  = (fibo100-fibo0)*0.618+fibo0
fibo50  = (fibo100-fibo0)/2+fibo0
fibo62  = (fibo100-fibo0)*0.382+fibo0
fibo78  = (fibo100-fibo0)*0.236+fibo0

f_retrace_level(obj) =>
    var int level = 0
    // Above Red
    if obj>fibo0 
        level := 8
    // Red 
    if obj<fibo0 and obj>fibo78
        level := 7
    // Orange 
    if obj<fibo78 and obj>fibo62
        level := 6
    // Yellow
    if obj<fibo62 and obj>fibo50
        level := 5
    // Aqua
    if obj<fibo50 and obj>fibo38
        level := 4
    // Green
    if obj<fibo38 and obj>fibo23
        level := 3
    // Lime
    if obj<fibo23 and obj>fibo100
        level := 2
    // Below Lime
    if obj<fibo100
        level := 1

    level

// Fibo Plots
// p_fib0 = plot(i_pd_fibo ? fibo0 : na ,title='Fibo 0',color=color.new(color.red,50),linewidth=2)
// p_fibo100 = plot(i_pd_fibo ? fibo100 : na ,title='Fibo 100',color=color.new(color.green,50),linewidth=2)
// p_fibo23  = plot(i_pd_fibo ? fibo23 : na,title='Fibo 23',color=color.new(color.gray,50) )
// p_fibo38  = plot(i_pd_fibo ? fibo38 : na,title='Fibo 38',color=color.new(color.gray,50) )
// p_fibo50  = plot(i_pd_fibo ? fibo50 : na,title='Fibo 50',color=color.new(color.gray,50) )
// p_fibo62  = plot(i_pd_fibo ? fibo62 : na,title='Fibo 62',color=color.new(color.gray,50) )
// p_fibo78  = plot(i_pd_fibo ? fibo78 : na,title='Fibo 78',color=color.new(color.gray,50) )

// fill(p_fibo78, p_fib0,title='Fill 100',color=i_pd_fills? color.new(red,i_pd_fills_trans) : na )
// fill(p_fibo62, p_fibo78,title='Fill 78',color=i_pd_fills? color.new(orange,i_pd_fills_trans) : na )
// fill(p_fibo50, p_fibo62,title='Fill 62',color=i_pd_fills? color.new(yellow,i_pd_fills_trans) : na )
// fill(p_fibo50, p_fibo38,title='Fill 38',color=i_pd_fills? color.new(aqua,i_pd_fills_trans) : na )
// fill(p_fibo38, p_fibo23,title='Fill 23',color=i_pd_fills? color.new(green,i_pd_fills_trans) : na )
// fill(p_fibo23, p_fibo100,title='Fill 0',color=i_pd_fills? color.new(lime,i_pd_fills_trans) : na )

diff_fibo0 = ta.change(fibo0,1) 
diff_fibo100 = ta.change(fibo100,1)  

if ta.change(fibo0) and i_pd_labels
    higher = fibo0 > fibo0[1] ? "Higher: " + str.tostring(diff_fibo0)  : fibo0 < fibo0[1] ? "Lower: " + str.tostring(diff_fibo0) : "No Change: " + str.tostring(diff_fibo0)

    
    txt1 = str.tostring(get_pip_value(fibo0, fibo100, false))
    info1 = label.new(x=time,y=fibo0,xloc=xloc.bar_time, text=txt1, textcolor=#ffffff)

    lower = fibo100 > fibo100[1] ? "Higher: " + str.tostring(diff_fibo100)  : fibo100 < fibo100[1] ? "Lower: " + str.tostring(diff_fibo100) : "No Change: " + str.tostring(diff_fibo100)
    txt2 = str.tostring(lower)
    info2 = label.new(x=time,y=fibo100,xloc=xloc.bar_time, text=txt2, textcolor=#ffffff, style=label.style_label_up)


retrace_level = f_retrace_level(close)
plot(retrace_level, title="Retrace", color=color.new(blue, 100))




// ===  Stochastic ===
// ==================================================
g_rstoch = 'Stochastic -------------------------------------------------------------'
stoch_time = input.timeframe('', "Stoch Time", group=g_rstoch)
a = input.int(10, "Percent K Length", group=g_rstoch) // 10
b = input.int(3, "Percent D Length", group=g_rstoch)
ob = input.int(40, "Overbought", group=g_rstoch)
os = input.int(-40, "Oversold", group=g_rstoch)
smooth = input.int(1, "Smoothing", group=g_rstoch)
show_bars = input.bool(false, title="Show Bars", group=g_rstoch)

stoch() =>
    // Range Calculation
    ll = ta.lowest (low, a)
    hh = ta.highest (high, a)
    diff = hh - ll
    rdiff = close - (hh+ll)/2

    avgrel = ta.ema(ta.ema(rdiff,b),b)
    avgdiff = ta.ema(ta.ema(diff,b),b)
    // SMI calculations
    SMI = avgdiff != 0 ? (avgrel/(avgdiff/2)*100) : 0

stoch_m = request.security(syminfo.tickerid, stoch_time, stoch() )
stoch_SMI = request.security(syminfo.tickerid, stoch_time, ta.ema(stoch_m,b) ) 
stoch_EMA = request.security(syminfo.tickerid, stoch_time, ta.ema(stoch_m, 10) )  
 
//plot(stoch_SMI, title="Stochastic", color=color.new(blue,100) )
//plot(stoch_EMA, title="stoch_EMA", color=color.new(yellow,100))

// level_40 = ob
// level_40smi = stoch_SMI > ob ? stoch_SMI : level_40
// level_m40 = os
// level_m40smi = stoch_SMI < os ? stoch_SMI : level_m40

stoch_color = stoch_SMI<stoch_EMA and stoch_EMA>ob ? yellow : stoch_EMA>ob ? red : stoch_SMI>ob ? orange : 
 stoch_SMI>stoch_EMA and stoch_EMA<os ? aqua : stoch_EMA<os ? green : stoch_SMI<os ? lime : na

//barcolor(show_bars?stoch_color:na, title='Stoch Bar Color')
 




//
// ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ //
// ADX
// ------------------------------------------------------------------------------------------------------------------
g_adx = 'ADX -------------------------------------------------------------'
g_adx_time = ''
adx_time = input.timeframe(title='Timeframe', defval='60', group=g_adx)
i_adx_showbars = input.bool(false, title="ADX bars", group=g_adx)
inl_adx = '1'
use_adx_curr = input.bool(title='Show Current', defval=true, inline=inl_adx, group=g_adx)
use_adx_multi = input.bool(title='Show Multi', defval=true, inline=inl_adx, group=g_adx)

adx_len = input(9, title='Length', group=g_adx)  // 14
adx_line = input(20, title='threshold', group=g_adx)  // 20
adx_avg = input(8, title='SMA', group=g_adx)  // 10
adx_top = input(50, title='High', group=g_adx)  // 41
adx_high = input(39.5, title='High', group=g_adx)  // 41
adx_mid = input(title='Mid', defval=33, group=g_adx)
adx_center = input(title='Center', defval=20, group=g_adx)
adx_low = input(title='Low', defval=12, group=g_adx)

// Show adx
// show_di_plus = input(title='Di Plus', defval=true,group=g_adx)
// show_di_minus = input(title='Di Minus', defval=true,group=g_adx)
// show_adx = input(title='ADX', defval=true,group=g_adx)
// show_adx_sma = input(title='ADX SMA', defval=true,group=g_adx)


f_adx() =>
    smooth_tr = 0.0
    smooth_di_plus = 0.0
    smooth_di_minus = 0.0
    TrueRange = math.max(math.max(high - low, math.abs(high - nz(close[1]))), math.abs(low - nz(close[1])))
    DI_plus = high - nz(high[1]) > nz(low[1]) - low ? math.max(high - nz(high[1]), 0) : 0
    DI_minus = nz(low[1]) - low > high - nz(high[1]) ? math.max(nz(low[1]) - low, 0) : 0
    smooth_tr := nz(smooth_tr[1]) - nz(smooth_tr[1]) / adx_len + TrueRange
    smooth_di_plus := nz(smooth_di_plus[1]) - nz(smooth_di_plus[1]) / adx_len + DI_plus
    smooth_di_minus := nz(smooth_di_minus[1]) - nz(smooth_di_minus[1]) / adx_len + DI_minus

    di_plus = smooth_di_plus / smooth_tr * 100
    di_minus = smooth_di_minus / smooth_tr * 100
    DX = math.abs(di_plus - di_minus) / (di_plus + di_minus) * 100
    adx = ta.sma(DX, adx_len)
    adx_sma = ta.sma(adx, adx_avg)

    [adx, adx_sma, di_plus, di_minus]

[adx, adx_sma, di_plus, di_minus] = f_adx()

adx_m = request.security(syminfo.tickerid, adx_time, adx)
adx_sma_m = request.security(syminfo.tickerid, adx_time, adx_sma)
di_plus_m = request.security(syminfo.tickerid, adx_time, di_plus)
di_minus_m = request.security(syminfo.tickerid, adx_time, di_minus)
//plot(di_plus_m, title='Di Plus')
barcolor(i_adx_showbars and di_plus_m>di_minus_m ? red : i_adx_showbars and di_minus_m>di_plus_m ? green : na, title='ADX Bar Color')




//
// ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ //
// STOCH RSI
// ------------------------------------------------------------------------------------------------------------------
g_stoch_rsi = 'STOCH RSI -------------------------------------------------------------'
stoch_rsi_time = input.timeframe(title='Timeframe', defval='', group=g_stoch_rsi)
smoothK = input.int(3, "K", minval=1, group=g_stoch_rsi)
smoothD = input.int(3, "D", minval=1, group=g_stoch_rsi)
lengthRSI = input.int(14, "RSI Length", minval=1, group=g_stoch_rsi)
lengthStoch = input.int(14, "Stochastic Length", minval=1, group=g_stoch_rsi)
stoch_src = input(close, title="RSI Source", group=g_stoch_rsi)
i_stoch_smooth = input.bool(true, title="Use Smoothing")
i_sto_smooth_amount = input.int(3, title="Smooth Amount")

stoch_rsi()=>
    stoch_rsi = ta.rsi(stoch_src, lengthRSI )
    stoch = ta.stoch(stoch_rsi, stoch_rsi, stoch_rsi, lengthStoch)
    k = ta.sma(stoch, smoothK)
    d = ta.sma(k, smoothD)

    if i_stoch_smooth
        stoch_rsi := ta.sma(stoch_rsi, i_sto_smooth_amount)
        stoch := ta.sma(stoch, i_sto_smooth_amount)
        k := ta.sma(k, i_sto_smooth_amount)
        d := ta.sma(d, i_sto_smooth_amount)

    [stoch_rsi,stoch,k,d]

[stoch_rsi,stoch,stoch_k,stoch_d] = request.security(syminfo.tickerid, stoch_rsi_time, stoch_rsi() ) 

stoch_ch = ta.change(bar_index % str.tonumber(stoch_rsi_time) == 0 ) ? 1 : 0
stoch_rsi := stoch_ch ? stoch_rsi : stoch_rsi[1]
stoch := stoch_ch ? stoch : stoch[1]
stoch_k := stoch_ch ? stoch_k : stoch_k[1]
stoch_d := stoch_ch ? stoch_d : stoch_d[1]
stoch_rsi_ma_angle = request.security(syminfo.tickerid, stoch_rsi_time, angle(stoch_k,1) ) 


//diff = math.abs(stoch_k - stoch_d)
//plot(stoch_rsi, "RSI", color=color.new(color.yellow, 100) )
//plot(stoch, "Stoch RSI", color=color.new(color.green, 100) )
//pk = plot(stoch_k, "K", color=color.new(blue,100))
//pd = plot(stoch_d, "D", color=color.new(orange,100))







//
// ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ //
// Nadaraya–Watson Regression using a Rational Quadratic Kernel
rqk_group = 'Nadaraya Watson -------------------------------------------------------------'

// Settings
i_rqk_show_rqk = input.bool(false, title='Show RQK ', group=rqk_group)
i_rqk_show_ma = input.bool(false, title='Show Ma\'s ', group=rqk_group)
i_rqk_use_multi = input.bool(true, title="Use Multi Timeframe", group=rqk_group)
i_rqk_resCustom = input.timeframe(title='Timeframe', defval='30', group=rqk_group)

rqk_h = input.float(20.0, 'Lookback Window', tooltip='The number of bars used for the estimation. This is a sliding value that represents the most recent historical bars. Recommended range: 3-50', group=rqk_group) // 8
rqk_r = input.float(20.0, 'Relative Weighting', step=0.25, tooltip='Relative weighting of time frames. As this value approaches zero, the longer time frames will exert more influence on the estimation. As this value approaches infinity, the behavior of the Rational Quadratic Kernel will become identical to the Gaussian kernel. Recommended range: 0.25-25', group=rqk_group) // 8
rqk_x_0 = input.int(100, "Start Regression at Bar", tooltip='Bar index on which to start regression. The first bars of a chart are often highly volatile, and omission of these initial bars often leads to a better overall fit. Recommended range: 5-25', group=rqk_group) // 4 6 25
rqk_smoothColors = input.bool(true, "Smooth Colors", tooltip="Uses a crossover based mechanism to determine colors. This often results in less color transitions overall.", inline='1', group='Colors', group=rqk_group) // false
rqk_lag = input.int(1, "Lag", tooltip="Lag for crossover detection. Lower values result in earlier crossovers. Recommended range: 1-2", inline='1', group='Colors', group=rqk_group) // 2

//i_bars_merge = input.bool(false, title='Lookahead On')
rqk_src = close //input.source(close, 'Source', group=rqk_group)
rqk_size = array.size(array.from(rqk_src) ) // size of the data series

//MA
i_rqk_angle_amount = input.int(14, title='Angle Amount', group=rqk_group)
i_rqk_conv = input.int(27, title='Convergence Amount', group=rqk_group)
i_rqk_show_between = input.bool(false, title='Show Between ', group=rqk_group)

// rqk_m2 = ta.hma(close,20)
// rqk_m2_a = angle(rqk_m2,i_rqk_angle_amount)
// rqk_m4 = ta.hma(close,75)
// rqk_m4_a = angle(rqk_m4,i_rqk_angle_amount)
// rqk_m8 = ta.hma(close,1000)
// rqk_m8_a = angle(rqk_m8,i_rqk_angle_amount)

//plot(size,title="array size",color=color.new(blue,100))

// Further Reading:
// The Kernel Cookbook: Advice on Covariance functions. David Duvenaud. Published June 2014.
// Estimation of the bandwidth parameter in Nadaraya-Watson kernel non-parametric regression based on universal threshold level. Ali T, Heyam Abd Al-Majeed Hayawi, Botani I. Published February 26, 2021.
kernel_regression(_src, _size, _h) =>
    float _currentWeight = 0.
    float _cumulativeWeight = 0.
    for i = 0 to _size + rqk_x_0
        y = _src[i] 
        w = math.pow(1 + (math.pow(i, 2) / ((math.pow(_h, 2) * 2 * rqk_r))), -rqk_r)
        _currentWeight += y*w
        _cumulativeWeight += w
    _currentWeight / _cumulativeWeight

bull_bear() =>
    // Estimations
    yhat1 = kernel_regression(rqk_src, rqk_size, rqk_h)
    yhat2 = kernel_regression(rqk_src, rqk_size, rqk_h - rqk_lag)

    // Rates of Change
    bool wasBearish = yhat1[2] > yhat1[1]
    bool wasBullish = yhat1[2] < yhat1[1]
    bool isBearish = yhat1[1] > yhat1
    bool isBullish = yhat1[1] < yhat1
    bool isBearishChange = isBearish and wasBullish
    bool isBullishChange = isBullish and wasBearish

    // Crossovers
    bool isBullishCross = ta.crossover(yhat2, yhat1)
    bool isBearishCross = ta.crossunder(yhat2, yhat1) 
    bool isBullishSmooth = yhat2 > yhat1
    bool isBearishSmooth = yhat2 < yhat1

    [yhat1,isBullish,isBearish,isBearishChange,isBullishChange,isBullishCross,isBearishCross,isBullishSmooth,isBearishSmooth, wasBearish, wasBullish]

[yhat1,isBullish,isBearish,isBearishChange,isBullishChange,isBullishCross,isBearishCross,isBullishSmooth,isBearishSmooth, wasBearish, wasBullish] = bull_bear()


rqk = request.security(syminfo.tickerid, i_rqk_use_multi? i_rqk_resCustom : "", yhat1)
rqk_a = request.security(syminfo.tickerid, i_rqk_use_multi? i_rqk_resCustom : "", angle(rqk,i_rqk_angle_amount))

rqk_bwt =  is_between(rqk, h9)
//barcolor(i_rqk_show_between and rqk_bwt and i_rqk_show_rqk ? green : (i_rqk_show_between and i_rqk_show_rqk) and not rqk_bwt ? red : na, title='Is between RQK and M8')

// Colors
rqk_buy_col = #3AFF17
rqk_sell_col = #FD1707
color c_bullish = input.color(rqk_buy_col, 'Bullish Color', group='Colors')
color c_bearish = input.color(rqk_sell_col, 'Bearish Color', group='Colors')
color colorByCross = isBullishSmooth ? c_bullish : c_bearish
color colorByRate = isBullish ? c_bullish : c_bearish
color plotColor = rqk_smoothColors ? colorByCross : colorByRate

// RQK and M8 Conv
rqk_conv = get_pip_value(rqk,h9, true)
// Price and M8 Distance
rqk_price_dist = get_pip_value(close,h9, true)

p_rqk = plot(i_rqk_show_rqk ? rqk : na, "RQK", color=plotColor, linewidth=2)
//fill(p_rqk_m8, p_rqk, title="Fill Convergence", color=rqk_conv<i_rqk_conv ? color.new(red,80) : na)
//p_rqk_m8 = plot(i_rqk_show_rqk and i_rqk_show_ma ? h9 : na, title="M8", color=rqk_m8_a>0?green:red)

// plot(rqk_conv,title="Convergence",color=color.new(blue,100) )
// plot(rqk_price_dist,title="Price Distance",color=color.new(blue,100) )
// plot(rqk_a,title="RQK Angle",color=color.new(blue,100) )
// p_m2 = plot(i_rqk_show_ma ? rqk_m2 : na, title="M2", color=rqk_m2_a>0?green:red)
// p_m4 = plot(i_rqk_show_ma ? rqk_m4 : na, title="M4", color=rqk_m4_a>0?yellow:orange)






//
// ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ //
// TRADING
// ------------------------------------------------------------------------------------------------------------------

g_trading = 'Trading ----------------------------------------------------'
i_live_trading  = input.bool(false, title='Live Trading Only',group=g_trading)
isLive          = barstate.isrealtime
i_equity        = input.string("Equity", options=["Initial", "Equity"], title="Initial or Equity",group=g_trading)
i_long_trades   = input.bool(true, title='Long Trades',group=g_trading)
i_short_trades  = input.bool(true, title='Short Trades',group=g_trading)
i_use_pos       = input.bool(true,title="Use Percentage based Position Size",group=g_trading)
i_pctStop       = input(1.5, '% of Risk to Starting Equity Use to Size Positions',group=g_trading) / 100

Session(sess) => na(time("2",sess)) == false
i_show_tr = input.bool(false,title='Show Session')
i_show_nt = input.bool(true, title='Show No Trade')
i_session  = input.session(title="Session", defval="1400-1600")
i_no_trading = input.session(title="No Trading Hours", defval="1045-1300")
i_GMT = input.string(title='GMT', defval='GMT-10', options=['GMT-10', 'GMT-9', 'GMT-8', 'GMT-7', 'GMT-6', 'GMT-5', 'GMT-4', 'GMT-3', 'GMT-2', 'GMT-1', 'GMT-0', 'GMT+1', 'GMT+2', 'GMT+3', 'GMT+4', 'GMT+5', 'GMT+6', 'GMT+7', 'GMT+8', 'GMT+9', 'GMT+10', 'GMT+11', 'GMT+12', 'GMT+13'] )
// Set the start of day
start_time = Session(i_session)
timerange = time(timeframe.period, i_session, i_GMT) and i_show_tr
no_trading = time(timeframe.period, i_no_trading, i_GMT) and i_show_nt 
// ▒▒▒▒▒ Sessions ▒▒▒▒▒ 
session_StartDate = input.time(timestamp("1 January 2023 00:00 -1000"), title="Start Date", group=g_trading )
show_sessions = input.bool(false,title='Sessions', group=g_trading)
// As  = input.session(title="Asia", defval="1800-0300")
// Lon = input.session(title="London", defval="0300-1200")
// Ny  = input.session(title="New York", defval="0800-1800")
Dz  = input.session(title="Deadzone - High Spreads", defval="1645-1830")




inl_color = "inl_color"
inl_color2 = "inl_color2"
c1_on = false //input.bool(true,title="", group=g_trading,inline=inl_color)
c1 = color.new(#00bcd4,90) //input.color(title="Asia", defval=#00bcd4,group=g_trading,inline=inl_color)
c2_on = true //input.bool(true,title="",group=g_trading,inline=inl_color)
c2 = color.new(#00796b,90) //input.color(title="London", defval=#00796b,group=g_trading,inline=inl_color)
c3_on = true //input.bool(true,title="",group=g_trading,inline=inl_color2)
c3 = color.new(#b71c1c,90) //input.color(title="New York", defval=#b71c1c,group=g_trading,inline=inl_color2)
c4_on = true //input.bool(true,title="", group=g_trading,inline=inl_color2)
c4 = color.new(color.purple,90) //input.color(title="Deadzone", defval=#b71c1c,group=g_trading,inline=inl_color2)

// Session(sess) => na(time("2",sess)) == false
// Asia = Session(As) and c1_on and show_sessions? c1 : na
// London = Session(Lon) and c2_on and show_sessions ? c2 : na
// NewYork = Session(Ny) and c3_on and show_sessions ? c3 : na
Deadzone = Session(Dz) and c4_on and show_sessions ? c4 : na
// bgcolor(Asia)
// bgcolor(London)
// bgcolor(NewYork)
// bgcolor(Deadzone)


//
// ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ //
// STOP LOSS
// ------------------------------------------------------------------------------------------------------------------
g_sl = 'Stop Loss ----------------------------------------------------'
i_plot_trades = input.bool(true, title="Display Trades",group=g_sl)
i_sl_type   = input.string("ATR", title="SL Type", options=["ATR", "Pips","MA","SUP","Lowest"],group=g_sl) // ATR
i_ma_atr    = input.string("M6", title="Select MA ATR", options=["M6","M7","M8"],group=g_sl) // ATR

atr_group   = 'ATR'
show_sl     = input.bool(false,title="Show Stop Loss",group=atr_group)
sl_Multip   = input.float(1.5, title='SL Amount',group=atr_group) // 4 1.5
atr_len     = input.int(14, title='ATR Length ',group=atr_group)
atr_src     = input.string('close', title='Close or Wicks', options=['close', 'wicks'],group=atr_group) // close
//atr_type    = input.string(title='ATR Type', defval='ema', options=['sma', 'ema', 'zema', 'dema', 'tema', 'wma', 'vwma', 'smma', 'rma', 'hma', 'lsma', 'McGinley'], group=atr_group)
atr_smooth  = input.int(5, title="ATR Smooth", group=atr_group)
i_sl_min_max = input.bool(false,title='Use SL Min and Max',group=atr_group)
sl_min      = input.float(2.0, title='Stop Loss Minimum Pips',group=atr_group)
sl_max      = input.float(12.0, title='Stop Loss Maximum Pips',group=atr_group)


// Take Profit
g_tp = 'Take Profit ----------------------------------------------------'
i_tpFactor  = input(35, 'Target Profit',group=g_tp) // 13 3.5 3
i_qty_mult  = input.float(1.0, title='Quantity Multiplier',group=g_tp) // 5.0 2
i_tsFactor  = input(2.0, 'Trailing Stop',group=g_tp) // 1.25
i_ts        = input.bool(true, title="Use Trailing Stop",group=g_tp)
i_ticks     = input.float(10, title='Min Ticks',group=g_tp) // 100 for Forex 1000 for NAS
show_ts     = input.bool(true,title="Trailing Stop",group=g_tp)
i_bkcandles = 11 //input.int(11, title="Lowerest range - Number of candles",group=g_sl)


float qty_value = switch syminfo.type
    "forex" => 100000.0
    "futures" => 10.0
    "index" => 10.0
    => 10.0
    
stop_loss()=>

    float sl_short  = na
    float sl_long   = na
    float sl_short_t   = na
    float quantity  = syminfo.currency == 'JPY' ? i_pctStop * 100 : i_pctStop

    if i_sl_type == "ATR"
        atr_len = 14
        ATR = ta.atr(atr_len)
        sl_long     := (atr_src =='close' ? close : low)  - ATR * sl_Multip 
        sl_short    := (atr_src =='close' ? close : high) + ATR * sl_Multip 
        
    if i_sl_type == "MA"
        atr_len = 14
        ATR = ta.atr(atr_len)
        float ma_select = switch i_ma_atr
            "M6" => h6
            "M7" => h7
            "M8" => h8
            => h6
        
        sl_long     := ma_select
        sl_short    := ma_select


    if i_sl_type == "Lowest"
        sl_short    = ta.highest(high, i_bkcandles)[1]
        sl_long     = ta.lowest(low, i_bkcandles)[1]


    min = 1 / math.pow(10, (decimals-1) ) * sl_min
    max = 1 / math.pow(10, (decimals-1) ) * sl_max
    longRatio = math.abs(close - sl_long)
    sl_long   := i_sl_min_max==false ? sl_long 
     : longRatio < min ? close - min 
     : longRatio > max ? close - max : sl_long
    longDiff  = math.abs(close - sl_long) 
    longPips  = get_pip_value(close,sl_long,true)
    longTS    = close + (i_tsFactor * longDiff)
    longTP    = close + (i_tpFactor * longDiff)
    plValue   = (i_equity=="Initial" ? strategy.initial_capital : strategy.equity) * quantity / (longDiff / close)
    pl_size   = i_use_pos ? math.ceil( plValue ) / close : math.ceil( qty_value * i_qty_mult )
    // Short
    shortRatio = math.abs(close - sl_short)
    sl_short   := i_sl_min_max==false ? sl_short 
     : shortRatio < min ? close + min 
     : shortRatio > max ? close + max : sl_short
    shortDiff = shortRatio
    shortPips = get_pip_value(close,sl_short,true)
    shortTS   = close - (i_tsFactor * shortDiff)
    shortTP   = close - (i_tpFactor * shortDiff)
    psValue   = (i_equity=="Initial" ? strategy.initial_capital : strategy.equity) * quantity / (shortDiff / close)
    ps_size   = i_use_pos ? math.ceil( psValue / close ) : math.ceil( qty_value * i_qty_mult )


    [sl_short,sl_long, close, close, shortTP, longTP, longTS, shortTS, pl_size, ps_size, longDiff, shortDiff, longPips, shortPips ]

[atr_short, atr_long, long_close, short_close, shortTP, longTP, longTS, shortTS, pl_size, ps_size, longDiff, shortDiff, longPips, shortPips] = stop_loss()


// ATR
atr_upper = ta.sma( ta.ema(atr_short, atr_len), atr_smooth ) // ma_types(atr_len, atr_type, atr_short)
atr_lower = ta.sma( ta.ema(atr_long, atr_len), atr_smooth )  // ma_types(atr_len, atr_type, atr_long)
atr_mid = (atr_lower + atr_upper) * 0.5
atr_mid_a = angle(atr_mid,14)
tmp = 1 / math.pow(10, (decimals-1) )  * sl_min

// Plot ATR
// plot(show_sl ? atr_lower  : na,"ATR Lower ", color=lime )
// plot(show_sl ? atr_upper  : na,"ATR Upper ", color=red )
// plot(show_sl ? atr_mid  : na,"ATR Mid ", color=atr_mid_a > 0 ? white : gray )
// plot(show_sl ? atr_mid_a  : na,"ATR Angle ", color=atr_mid_a > 0 ? color.new(green,100) : color.new(red,100) )
// plot(show_sl ? atr_long  : na,"ATR + ", color=color.new(green,70) )
// plot(atr_long - tmp , title='Min Pip Value', color=color.new(red,100)  )
// plot(show_sl ? atr_short : na,"ATR - ", color=color.new(red,70) )


// ▒▒▒▒▒ Stages ▒▒▒▒▒ 

g_stages = 'Stages ----------------------------------------------------'
stage2= 20
stage2_p= 10
stage3= 30
stage3_p= 15



// ▒▒▒▒▒ FILTERS ▒▒▒▒▒ 

g_filters = 'Filters ----------------------------------------------------'

i_fibo_filter = input.bool(true, title='Fibo Filter',group=g_filters)
i_use_rsi_filter = input.bool(false, title='RSI Filter',group=g_filters)
i_use_retrace_filter = input.bool(false,title='Filter Retrace Level',group=g_filters)
//i_use_filters = input.bool(false, title='Enable Filters',group=g_filters)
//i_use_time_filter = input.bool(false, title='Time Restraint',group=g_filters)
i_deadzone = input.bool(false, title='Do not take trades during',group=g_filters)

// var int flag_d = na
// flag_d := m2_a<-10 and m2_a<m2_a[1] ? -1 : flag_d==-1 and m2_a>m2_a[1] ? 0 : flag_d==-1 and m2_a<0 ? -1 : na


float shortSL = 0.0
float longSL  = 0.0
float ratio_l = 0.0
float short_ticks = 0.0
float long_ticks = 0.0
int counter_trade = 0

var int hma_stage = 0
hma_stage := 
 h5_m>h6_m and h5_h6_conv_m==1 and m3_a_m>0 ? 1
 : h5_m>h6_m and h5_h6_conv_m==0 ? 2
 : h5_m>h6_m and h5_h6_conv_m==1 and h3_a_m<0 ? 3
 : h5_m<h6_m and h5_h6_conv_m==1 ? 4
 : h5_m<h6_m and h5_h6_conv_m==0 ? 5
 : h5_m<h6_m and h5_h6_conv_m==1 ? 6 : 0

plot(hma_stage, title='HMA Stage', color=color.new(blue,100))

// ▒▒▒▒▒ TRADE LOGIC ▒▒▒▒▒ 
trade_dir() =>
    c           = close>open ? 1 : 0
    dir         = 0
    entryLong   = 0
    entryShort  = 0
    exitLong    = 0
    exitShort   = 0
    closeAll    = 0
    longSL      = 0.0
    cond        = ''
    cnt         = 0 // Counter Trend

    // Multi
    red_s       = h5_m>h6_m
    green_s     = h5_m<h6_m
    red_s2      = h7_m>h8_m
    green_s2    = h7_m<h8_m
    red_conv    = h5_h6_conv_m==1
    red_conv2   = h7_h8_conv_m==1
    green_conv  = h5_h6_conv_m==1
    green_conv2  = h7_h8_conv_m==1

    s1 = h8_a_m>0 and h8_m>h9_m
    s2 = h9_a_m<0 and h8_m<h9_m
    up_red = h8_a_m>0 and red_s ? true : false
    up_green = h8_a_m>0 and green_s ? true : false
    up_ph1 = up_red and  h5_h6_conv_m==1 and h3_a_m>0 // strong
    up_ph2 = up_red and h5_h6_conv_m==0 and h3_a_m>0 // strong
    up_ph3 = up_red and h5_h6_conv_m==0 and h3_a_m<0 // weakening
    up_ph4 = up_red and h5_h6_conv_m==1 and h3_a_m<0 // weakening
    up_ph5 = up_green and h5_h6_conv_m==1 and h3_a_m<0
    up_ph6 = up_green and h5_h6_conv_m==0 and h5_a_m<0
    up_ph7 = up_green and h5_h6_conv_m==0 and h5_a_m>0 // getting stronger
    up_ph8 = up_green and h5_h6_conv_m==1 and h5_a_m>0 // getting stronger

    // RSI
    rsi_weak_sell = RSI_high_m > rsi_target_up
    rsi_mid_sell = RSI_close_m > rsi_target_up
    rsi_mid_buy = RSI_close_m < rsi_target_down
    rsi_weak_buy = RSI_low_m < rsi_target_down

    // HH LL
    bars_hh = ta.barssince(ta.change(hh) )
    bars_ll = ta.barssince(ta.change(ll) )


    //btw_m7_m8 = ( h7>h8 and (close<h7) and (close>h8) ) or ( h7<h8 and (close>h7) and (close<h8) )? true : false
    btw_hma = ( h7>h8 and (close<h7) and (close>h8) ) or ( h7<h8 and (close>h7) and (close<h8) )? true : false
    rsi_bb_cond1 = rsi_m<bbLowerBand_m and rsiMA_m<50 ? 1 : 0
    
    // Time and Live Trading
    allow_trades = i_live_trading==0 and time>session_StartDate ? true : i_live_trading==1 and isLive ? 1 : 0



    // ▒▒▒▒▒ ENTRY LONG ▒▒▒▒▒ 

    if s1 and c==0 
     and (close<h6_m or close<h8_m)
     and (rsi_weak_buy or rsi_mid_buy)
     and close<ll
     and not(h3_m>h6_m)
     and not(h3>h4 and h3_a<0)
     and h5_a_m<0
     //and atr_lower>atr_lower[1]
     //and not(green_s and h6_m>hh2)
        //and not(green_s and h4_a_m<0)
        entryLong := 1
        cond := 'rsi_m'

    // if h8_a_m>0 and c==0
    //  and (close<h7 and h7<h8 and h7_a>0)
    //  and (rsi_weak_buy or rsi_mid_buy) 
    //  and not(h7<h8 and h8_a>0)
    //     entryLong := 1
    //     cond := 'h7'



    // ▒▒▒▒▒ FILTERS ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ 

    // Fibo Filter
    if i_fibo_filter and fibo_color != red
        entryLong := 0

    // Retrace Level
    if i_use_retrace_filter and retrace_level>4
        entryLong := 0

    // Supretrend
    if trend4==-1
        entryLong := 0

    if perc_change()>7
        entryLong := 0


    // MA's
    // if h9_a<-8 and h8_a<0 and h7_a<0
    //     entryLong := 0

    // ATR
    // if atr_mid_a < -10
    //     entryLong := 0


    // ▒▒▒▒▒ EXITS ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ 

    if high>hh and c==1
     and h5_m>h6_m and h3_m>h5_m and h3_m>h4_m
     and adx_m>di_plus_m and di_plus_m>di_minus_m
     and not(h7<h8)
        exitLong := 1

    // Keep this
    // if high>hh and c==1
    //  and h5_m>h6_m and h3_m>h5_m and h3_m>h4_m
    //  and adx_m>di_plus_m and di_plus_m>di_minus_m
    //  and not(h7<h8)
    //     exitLong := 1

    // if h8<h9 and h8_a<0 and close>h8
    //  and m6_m>m8_m
    //     exitLong := 1

    // if h5<h6 and h8_a<0 and h5_a<0
    //  and c==1
    //  and hh==hh[1]
    //  and close>hh
    //  and h1>hl_mid
    //     entryShort := 1
    //     cond := 'hh'

    // if low<ll
    //     exitShort := 1


    // ▒▒▒▒▒ COUNTER TRADES ▒▒▒▒▒

    if red_s and c==1 and h9_a_m>0
     and h4_a_m<1 and high>h4_m
     and h6_m>h8_m
        entryShort := 1
        cond := 's-cnt'
        cnt := 1

    // if s1 and c==1 
    //  and (rsi_weak_sell or rsi_mid_sell) 
    //  and h4_a_m<0
    //     entryShort := 1
    //     cond := 's-cnt'
    //     cnt := 1




    //if cnt==0 and strategy.position_size > 0




    // ▒▒▒▒▒ ENTRY Short ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ 

    //if cnt==0 and strategy.position_size > 0
    // if c==1 and m9_a_m<0
    //  and red_s
    //  and h4_a_m<0 and close>h4_m and close>h5_m
    //     entryShort := 1
    //     cond := 'red'

    // if c==1 and m9_a_m<0
    //  and green_s and close>h6

    //     entryShort := 1
    //     cond := 'green'

    

    // if c==1 and m9_a_m<0
    //  and close>hl_mid2
    //  and close>bb_upper_f
    //     entryShort := 1
    //     cond := 'bb'

    if c==1 and h9_a_m<0
     and adx_sma_m<di_plus_m and di_plus_m>di_minus_m
     and h2_a_m>0 and h2_a_m>h5_a_m
     and not(h5_m>h6_m and h4_m<h5_m)
     and not(red_s and h6_m<ll2)
     //and not(h9_a_m>-1 and h6_m<)
        entryShort := 1
        cond := 'adx'

    // if h9_a_m<0 and c==1
    //  and close>h4_m and h4_a_m<0 and h3_m<h4_m
    //     entryShort := 1
    //     cond := 'h4'

    // if h8_a_m<0 and c==1
    //  and (close>h6_m or close>h8_m)
    //  and (rsi_weak_sell or rsi_mid_sell)
    //  and close>hh
    //     entryShort := 1
    //     cond := 'rsi_m'


    // ▒▒▒▒▒ FILTERS ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒

    // HMA
    if h5_m<h6_m and h3_a_m>0 and h3_m<h6_m
        entryShort := 0

    // Retrace Level
    if i_use_retrace_filter and retrace_level<6
        entryShort := 0

    // Supretrend
    if trend4==1
        entryShort := 0

    // ADX
    if di_minus_m>di_plus_m
        entryShort := 0


    // ▒▒▒▒▒ EXIT SHORT ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒

    //if strategy.position_size < 0

    if low<ll and c==0
     and h5_m<h6_m and h3_m<h5_m and h3_m<h4_m
     and di_minus_m>di_plus_m
     and (rsi_weak_buy or rsi_mid_buy)
     and close<bb_lower
     //and adx_m>di_minus_m and di_minus_m>di_plus_m
     and not(h7>h8)
        exitShort := 1


    // ▒▒▒▒▒ COUNTER TRADES ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒

    // Counter Downtrend

    // if c==0 and bb_lower<ll2 and open<ll2
    //  and ta.barssince(ta.change(ll2))>50
    //  and h3_m<h4_m and h4_m<h8_m and h4_m<h6_m and h8_m<h9_m
    //     entryLong := 1
    //     cond := 'll2'

    // if c==0 and trend4==-1 and trend3==-1 and trend1==1
    //  and h7>h8 and close<up1
    //  and not(h5_a_m<h5_a_m[1] and h5_a_m>h6_a_m)
    //     entryLong := 1
    //     cond := 'sup-cnt'
    //     cnt := 1


    // ▒▒▒▒▒ GLOBAL FILTERS ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒

    // Time Restraint
    if time<session_StartDate
        entryLong := 0
        entryShort := 0

    if i_long_trades==false
        entryLong := 0

    if i_short_trades==false
        entryShort := 0


    [entryLong,entryShort,exitLong,exitShort,closeAll,cond, cnt]

[entryLong,entryShort,exitLong,exitShort,closeAll,trade_label, cnt] = trade_dir()

counter_trade := cnt
// check if live trading or market closed
//plotshape(barstate.islastconfirmedhistory, title='Real Time', color=red, style=shape.circle, location=location.top)


plot(strategy.position_size < 0 ? -1 : strategy.position_size > 0  ? 1 : 0, title='Trade Direction', color=color.new(blue,100))


// longDiff := entryLong and strategy.opentrades == 0 ? longDiff * 100 : strategy.opentrades > 0 ? longDiff[1] : 0


// Short Plots
plot_trans = 90

short_close := entryShort and strategy.opentrades == 0 ? short_close : strategy.opentrades > 0 ? short_close[1] : 0
short_ticks := short_close - (syminfo.mintick * i_ticks)
shortSL     := entryShort and strategy.opentrades == 0 ? atr_short : strategy.opentrades > 0 ? shortSL[1] : 0
shortTS     := entryShort and strategy.opentrades == 0 ? shortTS : strategy.opentrades > 0 ? shortTS[1] : 0
shortTP     := entryShort and strategy.opentrades == 0 ? shortTP : strategy.opentrades > 0 ? shortTP[1] : 0
plot(strategy.opentrades > 0 and i_plot_trades ? shortPips : na, title="Short Pips", color=color.new(blue,100))
p_s_c       = plot( strategy.opentrades > 0 and i_plot_trades ? short_close : na, title="Short Close", color=color.new(green,plot_trans), linewidth=1, style=plot.style_linebr)
p_s_ticks   = plot( strategy.opentrades > 0 and i_plot_trades ? short_ticks : na, title="Short Ticks", color=lime, style=plot.style_linebr)
p_s_sl      = plot( strategy.opentrades > 0 and i_plot_trades ? shortSL : na, title="Short SL", color=color.new(red,plot_trans), linewidth=1, style=plot.style_linebr)
p_s_ts      = plot( strategy.opentrades > 0 and i_plot_trades ? shortTS : na, title="Short TS", color=color.new(green,plot_trans), linewidth=1, style=plot.style_linebr)
p_s_tp      = plot( strategy.opentrades > 0 and i_plot_trades ? shortTP : na, title="Short TP", color=color.new(green,plot_trans), linewidth=1, style=plot.style_linebr)
fill(p_s_sl, p_s_c,title='Fill Short SL',color=color.new(red,plot_trans) )
fill(p_s_ts, p_s_c,title='Fill Short TS',color=color.new(lime,plot_trans) )
fill(p_s_ts, p_s_tp,title='Fill Short TP',color=color.new(green,plot_trans) )

// Long Plots
long_close  := entryLong and strategy.opentrades == 0 ? long_close : strategy.opentrades > 0 ? long_close[1] : 0
long_ticks  := long_close + (syminfo.mintick * i_ticks)
longSL      := entryLong and strategy.opentrades == 0 ? atr_long : strategy.opentrades > 0 ? longSL[1] : 0
longTS      := entryLong and strategy.opentrades == 0 ? longTS : strategy.opentrades > 0 ? longTS[1] : 0
longTP      := entryLong and strategy.opentrades == 0 ? longTP : strategy.opentrades > 0 ? longTP[1] : 0
ratio_l     := ( close  - long_close )
//plot( sl_short  ,"sl_short ", color=color.new(red,100) )

// var int decimals = int(math.log10(1/syminfo.mintick))
// decimals := decimals == 2 ? 1 : decimals == 0 ? 1 : decimals
plot(strategy.opentrades > 0 and i_plot_trades ? syminfo.mintick : na, title="Min Ticks", color=color.new(blue,100))
plot(strategy.opentrades > 0 and i_plot_trades ? math.pow(10, decimals) * syminfo.mintick : na, title="Ticks Converted", color=color.new(blue,100))
plot(strategy.opentrades > 0 and i_plot_trades ? decimals : na, title="Decimals", color=color.new(blue,100))
plot(strategy.opentrades > 0 and i_plot_trades ? longDiff : na, title="Long Diff", color=color.new(yellow,100))
plot(strategy.opentrades > 0 and i_plot_trades ? longPips : na, title="Long Pips", color=color.new(yellow,100))
//plot(strategy.opentrades > 0 and i_plot_trades ? longDiff / syminfo.mintick : na, title="Ticks to Pips")
p_l_dist    = plot( strategy.opentrades > 0 and i_plot_trades ? get_pip_value(long_close,longSL,true) * (math.pow(10, decimals) * syminfo.mintick) : na, title="SL in Pips", color=color.new(blue,100) )
p_l_c       = plot( strategy.opentrades > 0 and i_plot_trades ? long_close : na, title="Long Close", color=color.new(red,plot_trans), linewidth=1, style=plot.style_linebr)
p_l_ticks   = plot( strategy.opentrades > 0 and i_plot_trades ? long_ticks : na, title="Long Ticks", color=color.new(lime,plot_trans), style=plot.style_linebr)
p_l_sl      = plot( strategy.opentrades > 0 and i_plot_trades ? longSL : na, title="Long SL", color=color.new(red,plot_trans), linewidth=1, style=plot.style_linebr)
p_l_ts      = plot( strategy.opentrades > 0 and i_plot_trades ? longTS : na, title="Trailing Stop", color=color.new(yellow,plot_trans), linewidth=1, style=plot.style_linebr)
p_l_tp      = plot( strategy.opentrades > 0 and i_plot_trades ? longTP : na, title="Long TP", color=color.new(green,plot_trans), linewidth=1, style=plot.style_linebr)

//psize       = plot(pl_size, title="Position Size")
//p_l_ratio   = plot( ratio_l, title="Ratio", color=color.new(red,75), linewidth=1, style=plot.style_linebr)
fill(p_l_sl,p_l_c, title='Fill Long SL', color=color.new(red,plot_trans))
fill(p_l_ts,p_l_c, title='Fill Long Trailing Stop', color=color.new(lime,plot_trans) )
fill(p_l_tp,p_l_ts, title='Fill Long Take Profit', color=color.new(green,plot_trans))



cd=close>open?1:0

if Session(Dz) and i_deadzone
    entryShort := 0
    entryLong := 0

// Long

// if high<atr_lower and entryLong
//     entryLong := 0

// if  atr_upper>m2 and entryLong
//     entryLong := 0

var int trade_num = 0
if (entryLong)
    //pd_level = get_retrace_level(close)
    // * decimals , "#.00"
    trade_num := strategy.opentrades == 0 ? trade_num + 1 : trade_num
	strategy.entry("L", strategy.long, qty = pl_size, comment=trade_label + ' #' 
     + str.tostring(trade_num) + ' ' 
     + str.tostring( longPips, '#.##' ) 
     //+ str.tostring( math.round_to_mintick(longDiff)* 10 * decimals) + ' '
     //+ str.tostring( get_pip_value(long_close, close, true) )
     //+ str.tostring( pd_level )
     )
    //strategy.exit('EXIT L', 'L', stop = longSL)
        
else
	strategy.cancel("S")

if (exitLong ) //and counter_trade==0
    exit_com = trade_label + ' ' + str.tostring( get_pip_value(long_close, close, true) )
	strategy.close("L", comment = exit_com)

// Short
if (entryShort)
    trade_num := strategy.opentrades == 0 ? trade_num + 1 : trade_num
	strategy.entry("S", strategy.short, qty = ps_size, comment=trade_label 
     + ' #' + str.tostring(trade_num) + ' '
     + str.tostring( shortPips, '#.##' )  
     //+ str.tostring( math.round_to_mintick(shortDiff * 100))
     )
    //strategy.exit('EXIT S', 'S', stop=shortSL)
else
	strategy.cancel("S")

if (exitShort)
    exit_com = trade_label + ' ' + str.tostring( get_pip_value(short_close, close, true) )
	strategy.close("S", comment = exit_com)

// Filters
if Session(Dz) and i_deadzone
    strategy.close_all(comment = "close all entries")

in_profit() =>
    if strategy.position_size > 0 and close > longTS
        true
    else if strategy.position_size < 0 and close < shortTS
        true
    else
        false

getCurrentStage() =>
    var stage = 0
    if strategy.position_size == 0
        stage := 0
        stage
    if stage == 0 and strategy.position_size != 0
        stage := 1
        stage
    else if stage == 1 
        if strategy.position_size > 0 and close > longTS
            stage := 2
        if strategy.position_size < 0 and close < shortTS
            stage := 2
    else if stage == 2
        if strategy.position_size > 0 and close > longTP
            stage := 3
        if strategy.position_size < 0 and close < shortTP
            stage := 3
            stage
    stage

curStage = getCurrentStage()
plot(curStage,title='Current Stage', color=color.new(color.blue,100))

float stopLevel = na
string comment  = ''
float limit     = na //strategy.position_size > 0 and close>longTP and m2>m4 ? close : strategy.position_size < 0 and close<shortTP and m2<m4 ? close : na
string win     = 'Take Profit'
string loss    = 'Loss'
string bkeven  = 'Break Even'
string ts      = 'Trailing Stop'

if curStage == 1
    // Comment Strings

    if strategy.position_size > 0
        stopLevel := longSL
        comment   := close < long_close ? loss : win
        limit     := high>longTP ? high : na
    else if strategy.position_size < 0
        stopLevel := shortSL
        comment   := close > short_close ? loss : win
        limit     := low<shortTP ? low : na
        
    strategy.exit('x', stop=stopLevel, limit=limit, comment=comment)

else if curStage == 2

    if strategy.position_size > 0
        stopLevel := long_close + (syminfo.mintick * i_ticks)
        pips = str.tostring( get_pip_value(long_close, close, true) )  
        comment   := close <= longTS ? bkeven + ' ' + pips  : win + ' ' + pips
        limit     := high>longTP ? high: na
        //get_pip_value(long_close, close, true)>60 ? high 
    else if strategy.position_size < 0
        stopLevel := short_close - (syminfo.mintick * i_ticks)
        pips       = str.tostring( get_pip_value(short_close, close, true) )
        comment   := close >= shortTS ? bkeven + ' ' + pips : win + ' ' + pips
        limit     := low<shortTP ? low : na

    strategy.exit('x', stop=stopLevel, limit=limit, comment=comment)

else if curStage == 3

    if strategy.position_size > 0
        stopLevel := longTS
        pips      = str.tostring( get_pip_value(long_close, close, true) )
        comment   := close >= longTS and close<longTP ? ts + ' ' + pips : win + ' ' + pips
        limit     := high>longTP ? high : na
    else if strategy.position_size < 0
        stopLevel := shortTS
        pips       = str.tostring( get_pip_value(short_close, close, true) )
        comment   := close <= shortTS and close>shortTP ? ts + ' ' + pips : win  + ' ' + pips
        limit     := low<shortTP ? low : na

    strategy.exit('x', stop=stopLevel, limit=limit, comment=comment)

else
    strategy.cancel('x')


bars_long = ta.barssince(strategy.position_size > 0)
plot(bars_long, title='Bars since long', color=color.new(blue,98))
bars_short = ta.barssince(strategy.position_size < 0)
plot(bars_short, title='Bars since short', color=color.new(blue,98))