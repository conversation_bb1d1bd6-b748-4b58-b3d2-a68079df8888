//@version=5
indicator('RSI - Multi', shorttitle='RSI - Multi', overlay=true, max_labels_count=500)


g_rsi = 'RSI Wicks -------------------------------------------------------------'
inl_rsi1 = '1'
inl_rsi2 = '2'
show_rsi = input.bool(title='Show RSI', defval=true, inline=inl_rsi1, group=g_rsi)
rsi_strong = input.bool(title='Strongest', defval=false, inline=inl_rsi1, group=g_rsi)
rsi_len_cur = input.int(10, minval=1, title='Length', inline=inl_rsi2, group=g_rsi)
rsi_len_m = input.int(5, minval=1, title='Length', inline=inl_rsi2, group=g_rsi)
rsi_pos = input.string(title='Position', defval='tb', options=['tb', 't', 'b'], inline=inl_rsi2, group=g_rsi)

g_rsi_time = ''
inl_rsi3 = '3'
use_rsi_curr = input.bool(title='Show Current', defval=true, inline=inl_rsi3, group=g_rsi_time)
use_rsi_multi = input.bool(title='Show Multi', defval=true, inline=inl_rsi3, group=g_rsi_time)
rsi_time = input.timeframe(title='Timeframe', defval='15', group=g_rsi_time)

wicks = true  // input(true,  title="Wicks based on stand-alone RSI")
target_up = 70  // input(70, minval=1, title="RSI Up")
target_down = 30  // input(30, minval=1, title="RSI Down")
src_close = close
src_open = open
src_high = high
src_low = low


orange = #ff9800
yellow = #ffee58
violet = #814dff
aqua = #43d3ff
teal = #36fcf6
blue = #0053ff
lime = #50ff00


// Change
perc_change(obj) =>
    perc = math.abs((1 - obj[1] / obj) * 10000)
    perc

newbar(res) => ta.change(time(res)) == 0 ? 0 : 1

rsi_wicks(rsi_len) =>
    norm_close = math.avg(src_close, src_close[1])
    gain_loss_close = ta.change(src_close) / norm_close
    RSI_close = 50 + 50 * ta.rma(gain_loss_close, rsi_len) / ta.rma(math.abs(gain_loss_close), rsi_len)

    norm_open = if wicks == true
        math.avg(src_open, src_open[1])
    else
        math.avg(src_close, src_close[1])
    gain_loss_open = ta.change(src_open) / norm_open
    RSI_open = 50 + 50 * ta.rma(gain_loss_open, rsi_len) / ta.rma(math.abs(gain_loss_open), rsi_len)

    norm_high = if wicks == true
        math.avg(src_high, src_high[1])
    else
        math.avg(src_close, src_close[1])
    gain_loss_high = ta.change(src_high) / norm_high
    RSI_high = 50 + 50 * ta.rma(gain_loss_high, rsi_len) / ta.rma(math.abs(gain_loss_high), rsi_len)

    norm_low = if wicks == true
        math.avg(src_low, src_low[1])
    else
        math.avg(src_close, src_close[1])
    gain_loss_low = ta.change(src_low) / norm_low
    RSI_low = 50 + 50 * ta.rma(gain_loss_low, rsi_len) / ta.rma(math.abs(gain_loss_low), rsi_len)

    [RSI_open, RSI_close, RSI_high, RSI_low]

[RSI_open, RSI_close, RSI_high, RSI_low] = rsi_wicks(rsi_len_cur)

rw_change = perc_change(RSI_close) / 100
//plot(rw_change,title='Change',color=color.new(blue,100),style=plot.style_circles)
rsi_color = RSI_close > RSI_close[1] ? #65D25Bff : #FE6B69ff



// SELL
up_cond = (rsi_pos == 'tb' or rsi_pos == 't')
show_rsi_curr = show_rsi and use_rsi_curr
// Up
ru_1 = RSI_high > target_up
ru_h_1 = RSI_open > target_up
ru_m_1 = RSI_close > target_up and RSI_open < target_up
ru_l_1 = RSI_close < target_up and RSI_high > target_up
// Down
rd_1 = RSI_low < target_down
rd_l_1 = RSI_close > target_down and RSI_low < target_down
rd_m_1 = RSI_close < target_down and RSI_open > target_down
rd_h_1 = RSI_open < target_down

// Strong
plotshape(show_rsi_curr and ru_h_1 and up_cond ? 1 : na, title='Up High Multi', color=color.new(#ff0000, 0), style=shape.circle, location=location.top)
plotshape(show_rsi_curr and ru_h_1 and up_cond ? 1 : na, title='Up High', color=color.new(#ff0000, 0), style=shape.circle, location=location.top)
// Mid
plotshape(show_rsi_curr and ru_m_1 and up_cond and rsi_strong == false ? 1 : na, title='Up Mid', color=color.new(orange, 0), style=shape.circle, location=location.top)
//plot(perc_change(RSI_close)*.01, title="Percent Change", color=color.new(blue,100),style=plot.style_circles)
// Weak
plotshape(show_rsi_curr and ru_l_1 and up_cond and rsi_strong == false ? 1 : na, title='Up Weak', color=color.new(yellow, 0), style=shape.circle, location=location.top)

// BUY
down_cond = (rsi_pos == 'tb' or rsi_pos == 'b')

// Weak
plotshape(show_rsi_curr and rd_l_1 and down_cond and rsi_strong == false ? 1 : na, title='Low weak', color=color.new(violet, 0), style=shape.circle, location=location.bottom)
// Mid
plotshape(show_rsi_curr and rd_m_1 and down_cond and rsi_strong == false ? 1 : na, title='Low Mid', color=color.new(blue, 0), style=shape.circle, location=location.bottom)
// Strong
plotshape(show_rsi_curr and rd_h_1 and down_cond ? 1 : na, title='Low strong', color=color.new(lime, 0), style=shape.circle, location=location.bottom)

// Multi
[RSI_open_m, RSI_close_m, RSI_high_m, RSI_low_m] = request.security(syminfo.tickerid, rsi_time, rsi_wicks(rsi_len_m) ) 

close_m = request.security(syminfo.tickerid, rsi_time, close)
open_m = request.security(syminfo.tickerid, rsi_time, open)

if newbar(rsi_time) == 0
    RSI_open_m := RSI_open_m[1]
    RSI_close_m := RSI_close_m[1]
    RSI_high_m := RSI_high_m[1]
    RSI_low_m := RSI_low_m[1]
    close_m := close_m[1]
    open_m := open_m[1]


show_rsi_m = show_rsi and use_rsi_multi
// Up
ru_2 = RSI_high_m > target_up
ru_h_2 = RSI_open_m > target_up
ru_m_2 = RSI_close_m > target_up and RSI_open_m < target_up
ru_l_2 = RSI_close_m < target_up and RSI_high_m > target_up
// Down
rd_2 = RSI_low_m < target_down
rd_l_2 = RSI_close_m > target_down and RSI_low_m < target_down
rd_m_2 = RSI_close_m < target_down and RSI_open_m > target_down
rd_h_2 = RSI_open_m < target_down


// RSI_open_m = request.security(syminfo.tickerid, rsi_time, RSI_open)
// RSI_high_m = request.security(syminfo.tickerid, rsi_time, RSI_high)
// RSI_low_m = request.security(syminfo.tickerid, rsi_time, RSI_low)
// RSI_close_m = request.security(syminfo.tickerid, rsi_time, RSI_close)

// Multi
// plot(RSI_open_m,title="RSI open",color=color.new(color.blue,100))
// plot(RSI_high_m,title="RSI high",color=color.new(color.blue,100))
// plot(RSI_low_m,title="RSI low",color=color.new(color.blue,100))
plot(RSI_close_m,title="RSI close",color=color.new(color.blue,100))


// SELL
up_cond_m = close_m > open_m and (rsi_pos == 'tb' or rsi_pos == 't')
// Strong
plotshape(show_rsi_m and ru_h_2 and up_cond_m ? 1 : na, title='Up High Multi', color=color.new(#ff0000, 0), style=shape.circle, location=location.top)
// Mid
plotshape(show_rsi_m and ru_m_2 and up_cond_m and rsi_strong == false ? 1 : na, title='Up Mid', color=color.new(orange, 0), style=shape.circle, location=location.top)
// Weak
plotshape(show_rsi_m and ru_l_2 and up_cond_m and rsi_strong == false ? 1 : na, title='Up Weak', color=color.new(yellow, 0), style=shape.circle, location=location.top)

// BUY
down_cond_m = close_m < open_m and (rsi_pos == 'tb' or rsi_pos == 'b')
// Weak
plotshape(show_rsi_m and rd_l_2 and down_cond_m and rsi_strong == false ? 1 : na, title='Low weak', color=color.new(violet, 0), style=shape.circle, location=location.bottom)
// Mid
plotshape(show_rsi_m and rd_m_2 and down_cond_m and rsi_strong == false ? 1 : na, title='Low Mid', color=color.new(blue, 0), style=shape.circle, location=location.bottom)
// Strong
plotshape(show_rsi_m and rd_h_2 and down_cond_m ? 1 : na, title='Low strong', color=color.new(lime, 0), style=shape.circle, location=location.bottom)


