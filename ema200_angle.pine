//@version=4
study(title="EMA 200 Angle", shorttitle="EMA 200 Angle")

// ATR plot
input_angle = input(3, "EMA range")
basis_len = input(25, "Basis Length")
red = #ff0062
aqua = #00bcd4
yellow = #FFFF00
orange = #ff9800
green = #4caf50
lime = #00E676
white = #ffffff
c_hide = color.new(#ffffff,100)
blue = #42a5f5
violet = #814dff
gray = #707070
black = #000000
sell_color = color.new(#ff0062,20)
buy_color = color.new(#00c3ff,20)

angle(_src, len) =>
    rad2degree=180/3.14159265359  //pi 
    ang=rad2degree*atan((_src[0] - _src[1])/atr(len))

ema_200 = ema(close,200)
ea = angle(ema_200,input_angle)
plot(ea, title="EMA angle")

basis = sma(close, basis_len)
ba = angle(basis,input_angle)
plot(ba, title="Basis angle",color=orange)

s3_len = 15
s3 = wma(2*wma(close, s3_len/2)-wma(close, s3_len), round(sqrt(s3_len)))
s3_a = angle(s3,input_angle)
plot(s3_a, title="S3 angle",color=blue)

s4_len = 75
s4 = wma(2*wma(close, s4_len/2)-wma(close, s4_len), round(sqrt(s4_len)))
s4_a = angle(s4,input_angle)
plot(s4_a, title="S4 angle",color=yellow)

s5_len = 150
s5 = wma(2*wma(close, s5_len/2)-wma(close, s5_len), round(sqrt(s5_len)))
s5_a = angle(s5,input_angle)
plot(s5_a, title="S5 angle",color=green)

// basis = sma(close, 100)
// ba = angle(basis,input_angle)
// plot(ba, title="Basis angle",color=green)

hline(0)
hline(1,color=#0045b3)
hline(-1,color=#0045b3)
hline(3,color=#ff0062)
hline(-3,color=#ff0062)
hline(5,color=#707070)
hline(-5,color=#707070)
hline(7,color=#00c3ff)
hline(-7,color=#00c3ff)
hline(10,color=#ffffff)
hline(-10,color=#ffffff)

// hline(3.5,color=green)
// hline(-3.5,color=green)
// hline(1,color=red)
// hline(-1,color=red)
// hline(0)