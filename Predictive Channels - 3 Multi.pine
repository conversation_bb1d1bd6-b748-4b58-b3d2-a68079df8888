// This work is licensed under a Attribution-NonCommercial-ShareAlike 4.0 International (CC BY-NC-SA 4.0) https://creativecommons.org/licenses/by-nc-sa/4.0/
// © LuxAlgo

//@version=5
indicator("Predictive Channels - 3 Multi",  overlay = true)

//-----------------------------------------------------------------------------}
//Settings
//-----------------------------------------------------------------------------{
g_PC = 'Predictive Channels -------------------------------------------------------------'
i_pc_show_1  = input.bool(false,title = 'Show 1', group=g_PC)
i_pc_time1  = input.timeframe('5', "Resolution", group=g_PC) // 5 mins
PC_factor1  = input.float(3, 'Factor 1',  minval = 0, group=g_PC) // 5
PC_slope1   = input.float(50, 'Slope 1', minval = 0, group=g_PC) * PC_factor1
PC_select_1 = input.string(title="Prev Select", defval="Avg", options=["R2","R1","Avg","S1","S2"],group=g_PC)
pc_i_show_info_1  = input.bool(false,title = 'Show Info', group=g_PC)
i_PC_fill_color1 = input.color(#00000033, "Fill 1")
g_PC2 = 'PC 2 -------------------------------------------------------------'
i_pc_show_2  = input.bool(true,title = 'Show 2', group=g_PC2)
i_pc_time2  = input.timeframe('5', "Resolution", group=g_PC2) // 10 mins
PC_factor2  = input.float(5, 'Factor 2', minval = 0, group=g_PC2) // 10
PC_slope2   = input.float(50, 'Slope 2',minval = 0, group=g_PC2) * PC_factor2
PC_select_2 = input.string(title="Prev Select", defval="Avg", options=["R2","R1","Avg","S1","S2"],group=g_PC2)
pc_i_show_info_2  = input.bool(false,title = 'Show Info', group=g_PC2)
i_PC_fill_color2 = input.color(#001c3e55, "Fill 2")
g_PC3 = 'PC 3 -------------------------------------------------------------'
i_pc_show_3     = input.bool(false,title = 'Show Multi', group=g_PC3)
i_pc_time3  = input.timeframe('30', "Resolution", group=g_PC3) // 10 min, 1 hour 
PC_factor_3     = input.float(5, 'Factor Multi', minval = 0, group=g_PC3) // 10
PC_slope_3      = input.float(50, minval = 0, group=g_PC3) * PC_factor_3
PC_select_3 = input.string(title="Prev Select", defval="Avg", options=["R2","R1","Avg","S1","S2"],group=g_PC3)
pc_i_show_info_3  = input.bool(false,title = 'Show Info', group=g_PC3)
i_PC_fill_color3 = input.color(#00000033, "Fill 3")
i_PC_show_ma    = false //input.bool(false,title = 'Show MA', group=g_PC3)

//Style
red = #ff0062
orange = #ff9800
yellow = #FFFF00
green = #4caf50
lime = #50e600
aqua = #00bcd4
blue = #2962ff
dark_blue = #2962ff
violet = #814dff
white = #ffffff
gray = #707070
black = #000000

min_tick = syminfo.mintick
var int decimals = int(math.log10(1/min_tick))
decimals := decimals == 2 ? 1 : decimals == 0 ? 1 : decimals
get_pip_distance(point1, point2) =>
    diff_points = math.abs( (point1 - point2) )
    pip_value = decimals<3 ? diff_points / syminfo.mintick / 100 : diff_points / syminfo.mintick / 10

newbar(res) => ta.change(time(res)) == 0 ? 0 : 1
perc_change(obj) =>
    perc = math.abs((1 - obj[1] / obj) * 10000)
    perc

PC_get_angle(obj, value) =>
    obj>obj[value] ? 1 : -1



//-----------------------------------------------------------------------------}
//Calculation 1 
//-----------------------------------------------------------------------------{
f_pc1()=>

    var PC_avg = close
    var PC_os = 1
    var PC_hold_atr = 0.0

    PC_atr = nz(ta.atr(200)) * PC_factor1

    PC_avg := math.abs(close - PC_avg) > PC_atr ? close 
     : PC_avg + PC_os * PC_hold_atr / PC_slope1

    PC_hold_atr := PC_avg == close ? PC_atr / 2 : PC_hold_atr
    PC_os := PC_avg > PC_avg[1] ? 1 : PC_avg < PC_avg[1] ? -1 : PC_os

    PC_R2 = PC_avg + PC_hold_atr
    PC_R1 = PC_avg + PC_hold_atr/2
    PC_S1 = PC_avg - PC_hold_atr/2
    PC_S2 = PC_avg - PC_hold_atr

    [PC_R2,PC_R1,PC_S1,PC_S2, PC_avg, PC_os, PC_hold_atr]

[PC_R2_1, PC_R1_1, PC_S1_1, PC_S2_1, PC_avg_1, PC_dir_1, PC_hold_atr_1] = request.security(syminfo.tickerid, i_pc_time1, f_pc1() )

PC_select_1()=>
    float PC_sel = switch PC_select_1
        'R2' => PC_R2_1
        'R1' => PC_R1_1
        'Avg' => PC_avg_1
        'S1' => PC_S1_1
        'S2' => PC_S2_1
        => PC_R2_1

    PC_sel

if newbar(i_pc_time1) == 0
    PC_R2_1 := PC_R2_1[1]
    PC_R1_1 := PC_R1_1[1]
    PC_S1_1 := PC_S1_1[1]
    PC_S2_1 := PC_S2_1[1]
    PC_avg_1:= PC_avg_1[1]
    PC_dir_1 := PC_dir_1[1]
    PC_hold_atr_1 := PC_hold_atr_1[1]

PC_bars_1 = ta.barssince(close < PC_R2_1)
PC_angle_1 = PC_get_angle(PC_R2_1, 5)
PC_up_1 = pc_i_show_info_1 and close>PC_R2_1 and get_pip_distance(PC_R2_1, high) > 2
PC_down_1  = pc_i_show_info_1 and close<PC_S2_1 and get_pip_distance(PC_S2_1, low) > 2

var PC_last_1 = 0
var PC_last_sel_1 = 0.0
PC_evt_1 = ta.change( get_pip_distance(PC_R2_1,PC_R2_1[1])>1 )
PC_new_1  = ta.barssince( PC_evt_1 ) 
PC_last_1  := PC_evt_1 ? PC_angle_1[2] : PC_last_1
PC_last_sel_1  := PC_evt_1 ? PC_select_1()[2] : PC_last_sel_1

//-----------------------------------------------------------------------------}
//Calculation 2 
//-----------------------------------------------------------------------------{
f_pc2()=>

    var PC_avg_2 = close
    var PC_dir_2 = 1
    var PC_hold_atr_2 = 0.0

    PC_atr_2 = nz(ta.atr(200)) * PC_factor2

    PC_avg_2 := math.abs(close - PC_avg_2) > PC_atr_2 ? close 
     : PC_avg_2 + PC_dir_2 * PC_hold_atr_2 / PC_slope2

    PC_hold_atr_2 := PC_avg_2 == close ? PC_atr_2 / 2 : PC_hold_atr_2
    PC_dir_2 := PC_avg_2 > PC_avg_2[1] ? 1 : PC_avg_2 < PC_avg_2[1] ? -1 : PC_dir_2

    PC_R2 = PC_avg_2 + PC_hold_atr_2
    PC_R1 = PC_avg_2 + PC_hold_atr_2/2
    PC_S1 = PC_avg_2 - PC_hold_atr_2/2
    PC_S2 = PC_avg_2 - PC_hold_atr_2

    [PC_R2,PC_R1,PC_S1,PC_S2, PC_avg_2, PC_dir_2, PC_hold_atr_2]

[PC_R2_2, PC_R1_2, PC_S1_2, PC_S2_2, PC_avg_2, PC_dir_2, PC_hold_atr_2] = request.security(syminfo.tickerid, i_pc_time2, f_pc2() )

PC_select_2()=>
    float PC_sel = switch PC_select_2
        'R2' => PC_R2_2
        'R1' => PC_R1_2
        'Avg' => PC_avg_2
        'S1' => PC_S1_2
        'S2' => PC_S2_2
        => PC_R2_2

    PC_sel
    
if newbar(i_pc_time2) == 0
    PC_R2_2 := PC_R2_2[1]
    PC_R1_2 := PC_R1_2[1]
    PC_S1_2 := PC_S1_2[1]
    PC_S2_2 := PC_S2_2[1]
    PC_avg_2:= PC_avg_2[1]
    PC_dir_2 := PC_dir_2[1]
    PC_hold_atr_2 := PC_hold_atr_2[1]

PC_bars_2 = ta.barssince(close < PC_R2_2)
PC_angle_2 = PC_get_angle(PC_R2_2, 5)
PC_up_2 = pc_i_show_info_2 and close>PC_R2_2 and get_pip_distance(PC_R2_2, high) > 2
PC_down_2  = pc_i_show_info_2 and close<PC_S2_2 and get_pip_distance(PC_S2_2, low) > 2

var PC_last_2 = 0
var PC_last_sel_2 = 0.0
PC_evt_2 = ta.change( get_pip_distance(PC_R2_2,PC_R2_2[1])>1 )
PC_new_2  = ta.barssince( PC_evt_2 ) 
PC_last_2  := PC_evt_2 ? PC_angle_2[2] : PC_last_2
PC_last_sel_2  := PC_evt_2 ? PC_select_2()[2] : PC_last_sel_2


//-----------------------------------------------------------------------------}
//Calculation 3
//-----------------------------------------------------------------------------{
f_pc3()=>

    var PC_avg_3 = close
    var PC_dir_3 = 1
    var PC_hold_atr_3 = 0.0

    PC_atr = nz(ta.atr(200)) * PC_factor_3

    PC_avg_3 := math.abs(close - PC_avg_3) > PC_atr ? close 
     : PC_avg_3 + PC_dir_3 * PC_hold_atr_3 / PC_slope_3

    PC_hold_atr_3 := PC_avg_3 == close ? PC_atr / 2 : PC_hold_atr_3
    PC_dir_3 := PC_avg_3 > PC_avg_3[1] ? 1 : PC_avg_3 < PC_avg_3[1] ? -1 : PC_dir_3

    PC_R2_3 = PC_avg_3 + PC_hold_atr_3
    PC_R1_3 = PC_avg_3 + PC_hold_atr_3/2
    PC_S1_3 = PC_avg_3 - PC_hold_atr_3/2
    PC_S2_3 = PC_avg_3 - PC_hold_atr_3

    [PC_R2_3,PC_R1_3,PC_S1_3,PC_S2_3, PC_avg_3, PC_dir_3, PC_hold_atr_3]

[PC_R2_3, PC_R1_3, PC_S1_3, PC_S2_3, PC_avg_3, PC_dir_3, PC_hold_atr_3] = request.security(syminfo.tickerid, i_pc_time3, f_pc3() )

PC_select_3()=>
    float PC_sel = switch PC_select_3
        'R2' => PC_R2_3
        'R1' => PC_R1_3
        'Avg' => PC_avg_3
        'S1' => PC_S1_3
        'S2' => PC_S2_3
        => PC_R2_3

    PC_sel

if newbar(i_pc_time3) == 0
    PC_R2_3 := PC_R2_3[1]
    PC_R1_3 := PC_R1_3[1]
    PC_S1_3 := PC_S1_3[1]
    PC_S2_3 := PC_S2_3[1]
    PC_avg_3:= PC_avg_3[1]
    PC_dir_3 := PC_dir_3[1]
    PC_hold_atr_3 := PC_hold_atr_3[1]

PC_bars_3 = ta.barssince(close < PC_R2_3)
PC_angle_3 = PC_get_angle(PC_R2_3, 15)
PC_up_3 = pc_i_show_info_3 and close>PC_R2_3 and get_pip_distance(PC_R2_3, high) > 2
PC_down_3  = pc_i_show_info_3 and close<PC_S2_3 and get_pip_distance(PC_S2_3, low) > 2

var PC_last_3 = 0.0
var PC_last_sel_3 = 0.0
PC_evt_3 = ta.change( get_pip_distance(PC_R2_3,PC_R2_3[2])>10 )
PC_new_3 = ta.barssince( PC_evt_3 )
PC_last_3 := PC_evt_3 ? PC_angle_3[5] : PC_last_3
PC_last_sel_3 := PC_evt_3 ? PC_select_3()[2] : PC_last_sel_3

// Moving Averages
i_h2 = input.int(20, 'H2')
i_h3 = input.int(20, 'H3')
h2 = ta.hma(close,i_h2)
h3 = ta.hma(close,i_h3)

// EMA 30 min
ema20 = request.security(syminfo.tickerid, "30", ta.ema(close,20) )


//-----------------------------------------------------------------------------}
//Functions
//-----------------------------------------------------------------------------{
var upper_bound = 0.0
var pc_direction = 0
var pc_bars = 0



PC_get_level(obj) =>
  
    var int level = 0
    // Above R2
    if obj>PC_R2_3 
        level := 6
    // Above R1 
    if obj<PC_R2_3 and obj>PC_R1_3
        level := 5
    // Above Average 
    if obj<PC_R1_3 and obj>PC_avg_3
        level := 4
    // Below Average
    if obj<PC_avg_3 and obj>PC_S1_3
        level := 3
    // Below S1
    if obj<PC_S1_3 and obj>PC_S2_3
        level := 2
    // Below S2
    if obj<PC_S2_3
        level := 1

    level

PC_is_between(obj) =>
    between = PC_R2_3>obj and PC_S2_3<obj ? 1 : 0

PC_barssince(obj)=>
    bars = ta.barssince( get_pip_distance(obj,obj[1]) > 5 )

PC_prev_box(obj)=>
    //var prev_box = obj
    bars = ta.barssince( get_pip_distance(obj,obj[1]) > 5 )
    dir = bars == 0 ? obj[1] : obj


pc_bars := PC_barssince(PC_dir_3)

prev_box_3 = PC_prev_box(PC_R2_3) 
// plot(PC_dir_3, 'Multi Direction', color=PC_dir_3==1 ? color.new(green,100) : color.new(red,100) )
// plot(prev_box_3, 'Last Box', color=prev_box_3==1 ? color.new(green,100) : color.new(red,100) )



//-----------------------------------------------------------------------------}
//Plot PC 1
//-----------------------------------------------------------------------------{
PC_top1 = plot(i_pc_show_1 ? PC_R2_1 : na, 'Upper Resistance', close == PC_avg_1 ? na : red)
plot(i_pc_show_1 ? PC_R1_1 : na, 'Lower Resistance', close == PC_avg_1 ? na : color.new(red, 50))
plot_avg1 = plot(i_pc_show_1 ? PC_avg_1 : na, 'Average', close == PC_avg_1 ? na : gray)
plot(i_pc_show_1 ? PC_S1_1 : na, 'Upper Support', close == PC_avg_1 ? na : color.new(green, 50))
PC_bottom1 = plot(i_pc_show_1 ? PC_S2_1 : na, 'Lower Support', close == PC_avg_1 ? na : green)
fill(PC_top1, PC_bottom1, i_PC_fill_color1, 'PC Fill')

//plot(i_pc_show_1 ? PC_bars_1 : na, 'Bars Since 1', color=color.new(color.blue,100), display=display.data_window )
plot(i_pc_show_1 ? PC_angle_1 : na, title='Get Angle 1', color=color.new(white,100), display=display.data_window )
plot(i_pc_show_1 ? PC_new_1 : na, 'Bars Since 1', color=color.new(red, 100), display=display.data_window )
plot(i_pc_show_1 ? PC_last_1 : na, 'Last Box 1', color=color.new(red, 100), display=display.data_window )
plot(i_pc_show_1 ? PC_last_sel_1 : na, 'Last Select', color=color.new(red, 100), display=display.data_window )
plotshape(PC_up_1 ? 1 : na,title="Sell",color=red ,style=shape.circle,location=location.top)
plotshape(PC_down_1  ? 1 : na,title="Buy",color=green ,style=shape.circle,location=location.bottom)

//-----------------------------------------------------------------------------}
//Plot PC 2
//-----------------------------------------------------------------------------{
PC_top2 = plot(i_pc_show_2 ? PC_R2_2 : na, 'Upper Resistance', close == PC_avg_2 ? na : red)
plot(i_pc_show_2 ? PC_R1_2 : na, 'Lower Resistance', close == PC_avg_2 ? na : color.new(red, 50))
plot_avg2 = plot(i_pc_show_2 ? PC_avg_2 : na, 'Average', close == PC_avg_2 ? na : gray)
plot(i_pc_show_2 ? PC_S1_2 : na, 'Upper Support', close == PC_avg_2 ? na : color.new(green, 50))
PC_bottom2 = plot(i_pc_show_2 ? PC_S2_2 : na, 'Lower Support', close == PC_avg_2 ? na : green)
fill(PC_top2, PC_bottom2, i_PC_fill_color2, 'PC Fill')

//plot(i_pc_show_2 and pc_i_show_info_2 ? PC_bars_2 : na, 'Bars Since 2', color=color.new(color.blue,100), display=display.data_window )
plot(i_pc_show_2 ? PC_angle_2 : na, title='Get Angle 2', color=color.new(white,100), display=display.data_window )
plot(i_pc_show_2 ? PC_new_2 : na, 'Bars Since 2', color=color.new(red, 100), display=display.data_window )
plot(i_pc_show_2 ? PC_last_2 : na, 'Last Box 2', color=color.new(red, 100), display=display.data_window )
plot(i_pc_show_2 ? PC_last_sel_2 : na, 'Last Select', color=color.new(red, 100), display=display.data_window )
plotshape(PC_up_2 ? 1 : na,title="Sell",color=red ,style=shape.circle,location=location.top)
plotshape(PC_down_2  ? 1 : na,title="Buy",color=green ,style=shape.circle,location=location.bottom)

//-----------------------------------------------------------------------------}
//Plot PC 3
//-----------------------------------------------------------------------------{
PC_top_3 = plot(i_pc_show_3 ? PC_R2_3 : na, 'Upper Resistance 3', close == PC_avg_3 ? na : red)
plot(i_pc_show_3 ? PC_R1_3 : na, 'Lower Resistance 3', close == PC_avg_3 ? na : color.new(red, 50))
plot_avg_3 = plot(i_pc_show_3 ? PC_avg_3 : na, 'Average 3', close == PC_avg_3 ? na : gray)
plot(i_pc_show_3 ? PC_S1_3 : na, 'Upper Support 3', close == PC_avg_3 ? na : color.new(green, 50))
PC_bottom_3 = plot(i_pc_show_3 ? PC_S2_3 : na, 'Lower Support 3', close == PC_avg_3 ? na : green)
fill(PC_top_3, PC_bottom_3, i_PC_fill_color3, 'PC Fill')
// Bars and Angles
//plot(i_pc_show_3 and pc_i_show_info_3 ? PC_bars_3 : na, title='Bars Since 3', color=color.new(white,100), display=display.data_window )
plot(i_pc_show_3 ? PC_angle_3 : na, title='Get Angle 3', color=color.new(white,100), display=display.data_window )
plot(i_pc_show_3 ? PC_new_3 : na, 'Bars Since 3', color=color.new(red, 100), display=display.data_window )
plot(i_pc_show_3 ? PC_last_3 : na, 'Last Box 3', color=color.new(red, 100), display=display.data_window )
plot(i_pc_show_3 ? PC_last_sel_3 : na, 'Last Select', color=color.new(red, 100), display=display.data_window )
plotshape(PC_up_3 ? 1 : na,title="Sell",color=red ,style=shape.circle,location=location.top)
plotshape(PC_down_3  ? 1 : na,title="Buy",color=green ,style=shape.circle,location=location.bottom)

// plot(i_pc_show_3 and pc_i_show_info_3 ? PC_prev_box(PC_dir_3) : na, title='Prev Box Direction', color=color.new(white,100))
// plot(i_pc_show_3 and pc_i_show_info_3 ? pc_direction : na, title='direction', color=color.new(white,100))
// plot(i_pc_show_3 and pc_i_show_info_3 ? PC_is_between(ema20) : na, title='is between', color=color.new(white,100))

plot_0 = plot(i_pc_show_3 ? close : na, color = na, display = display.none, editable = false)

//Fill
topcss = close > PC_avg_3 ? color.new(green, 80) : color.new(chart.bg_color, 100)
btmcss = close < PC_avg_3 ? color.new(red, 80) : color.new(chart.bg_color, 100)
fill(plot_0, plot_avg_3, PC_R2_3, PC_S2_3, topcss, btmcss)


//plotshape(close>PC_R2_3 ? 1 : 0,"Top End",style=shape.circle, location = location.top, color = close>open ? red : color.rgb(230, 122, 0))
//plotshape(close<PC_S2_3 ? 1 : 0,"Bottom End",style=shape.circle, location = location.bottom, color = close<open ? #00be00 : color.rgb(0, 45, 208))

// Plot MA
plot(i_PC_show_ma ? h2 : na, title = 'H2', color = color.white)
plot(i_PC_show_ma ? h3 : na, title = 'H3', color = color.blue)
// Plot Levels
plot(pc_i_show_info_3 ? PC_get_level(h3) : na, title = 'Get Level', color = color.new(color.blue,100))

// Alerts

// pc_sell_cond = pc_i_show_info_3 and close>PC_R2_3 and h3>PC_R2_3
// pc_buy_cond  = pc_i_show_info_3 and close<PC_S2_3 and h3<PC_S2_3



//-----------------------------------------------------------------------------}

