//@version=4
study(title="SSL channel Ichimoku", overlay=true, shorttitle="SSL Channel -  Ichimoku")

ch_len=input(title="Period", defval=17) // 48 10
show_channel_fill = input(title="Show Fill",type=input.bool,defval=false)
channel_line_width = input(title="Show Fill",type=input.integer, defval=2)
conversionPeriods = input(9, minval=1, title="Conversion Line Length")
basePeriods = input(26, minval=1, title="Base Line Length")
displacement = input(26, minval=1, title="Displacement")

red = #ff0000
yellow = #FFFF00
green = #00ff00


smaHigh=sma(high, ch_len)
smaLow=sma(low, ch_len)
var ch_Hlv = 0

ch_Hlv := close>smaHigh ? 1 : close<smaLow ? -1 : ch_Hlv[1]
ch2_t = ch_Hlv < 0 ? smaHigh: smaLow
ch2_b   = ch_Hlv < 0 ? smaLow : smaHigh
ch2_sell = ch2_state==1?true:false
ch2_buy = ch2_state==-1?true:false

sdplot= plot(ch2_t, linewidth=channel_line_width, color=color.lime)
suplot=plot(ch2_b, linewidth=channel_line_width, color=color.red)

fill(sdplot,suplot, color=ch2_t>ch2_b and show_channel_fill?color.lime:ch2_b>ch2_t and show_channel_fill? color.red:na)

// Ichimoku
donchian(len) => avg(lowest(len), highest(len))
c_line = donchian(conversionPeriods)
b_line = donchian(basePeriods)
close_offets = close[displacement] 
plot(c_line, color=#0496ff, title="Conversion Line")
plot(b_line, color=#991515, title="Base Line")

cond_1 = c_line<b_line and c_line<ch2_b 
plotshape(cond_1,title="Ichimoku",color=green,style=shape.circle,location=location.bottom)
