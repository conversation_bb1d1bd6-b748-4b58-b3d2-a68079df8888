//@version=4
study(title="SSL Angles 2", shorttitle="SSL Angles 2")

// Global
g_global = "Global"
inl_g = "1"
input_angle = input(14, "Angle degree",group=g_global,inline=inl_g) // 14
sma_input = input(14, "s1 SMA", minval=1 ,group=g_global,inline=inl_g) // 100
plot_type_input = input(title='Choose Plot Type', options=["line", "columns", "area","histo","step"], defval="line",group=g_global,inline=inl_g)
plot_type = 
 plot_type_input == "line" ? plot.style_line :
 plot_type_input == "columns" ? plot.style_columns :
 plot_type_input == "area" ? plot.style_area :
 plot_type_input == "histo" ? plot.style_histogram :
 plot_type_input == "stepline" ? plot.style_linebr : na

red = #ff0062
aqua = #00bcd4
yellow = #FFFF00
orange = #ff9800
green = #4caf50
lime = #00E676
white = #ffffff
c_hide = color.new(#ffffff,100)
blue = #42a5f5
violet = #814dff
magenta = #ff0062
purple = #0045b3
gray = #707070
black = #000000
sell_color = color.new(#ff0062,20)
buy_color = color.new(#00c3ff,20)

angle(_src, len) =>
    rad2degree=180/3.14159265359  //pi 
    ang=rad2degree*atan((_src[0] - _src[1])/atr(len))

angles(len1,type) =>
    float v = na
    if type=="SMA"
        v := sma(close, len1)
    if type=="EMA"
        v := ema(close, len1)
    if type=="WMA"
        v := wma(2*wma(close, len1/2)-wma(close, len1), round(sqrt(len1)))

    obj_angle = sma(angle(v,input_angle), sma_input) 
    [obj_angle]

// Basis
g_bb = "Basis"
inl_bb = "1"
show_ba = input(title="Show BA's", type=input.bool, defval=false)
basis_len = input(20, "Basis Length",group=g_bb,inline=inl_bb) // 20
basis_len2 = input(150, "Basis Length 2",group=g_bb,inline=inl_bb) // 40
basis_len3 = input(300, "Basis Length 3",group=g_bb,inline=inl_bb) // 100
smooth_type = input(title="Smooth Type", defval="SMA", options=["SMA", "EMA", "WMA"])
BB_stdDev = 2

basis = sma(close, basis_len)
dev = BB_stdDev * stdev(close, basis_len)
bb_upper = basis + dev
bb_lower = basis - dev
bb_ua = angle(bb_upper,3) 
bb_la = angle(bb_lower,3)

[ba] = angles(basis_len, smooth_type)
[ba2] = angles(basis_len2, smooth_type)
[ba3] = angles(basis_len3, smooth_type)



plot(show_ba ? bb_ua:na, title="BB Upper Angle",color=bb_ua<0?green:red, style=plot_type)
plot(show_ba ? bb_la:na, title="BB Lower Angle",color=bb_la<0?white:blue, style=plot_type)
plot(show_ba?ba:na, title="Basis angle 1",color=orange, style=plot_type)
plot(show_ba? ba2:na, title="Basis angle2",color=show_ba==true?yellow:color.new(yellow,100), style=plot_type)
plot(show_ba? ba3:na, title="Basis angle3",color=show_ba==true?blue:color.new(blue,100), style=plot_type)

// EMA
g_ema = " "
inl_ema = "1"
show_ema = input(title="Show EMA's", type=input.bool, defval=false)
e1_len = input(8, "E1",group=g_ema,inline=inl_ema) // 30 
e2_len = input(20, "E2",group=g_ema,inline=inl_ema)
e3_len = input(50, "E3",group=g_ema,inline=inl_ema)
e4_len = input(200, "E 200",group=g_ema,inline=inl_ema)
[e1_a] = angles(e1_len, "EMA")
[e2_a] = angles(e2_len, "EMA")
[e3_a] = angles(e3_len, "EMA")
[ea] = angles(e4_len, "EMA")

ea_zone = abs(ea)
e_zones = ea_zone < 1 ? 0 : 
 ea_zone < 2 ? 1 : 
 ea_zone < 3 ? 2 :
 ea_zone < 4 ? 3 :
 ea_zone < 5 ? 4 :
 ea_zone < 6 ? 5 :
 ea_zone > 6 ? 6 : na
e_color = e_zones == 0 ? purple :
 e_zones == 1 ? magenta : 
 e_zones == 2 ?  gray : 
 e_zones == 3 ?  #00c3ff : 
 e_zones == 4 ? white:
 e_zones == 5 ? yellow:
 e_zones == 6 ? purple: na

plot(show_ema?e1_a:na, title="EMA 8 angle",color=e1_a<ba?white:gray, style=plot_type)
plot(show_ema?e2_a:na, title="EMA 20 angle",color=e2_a<ba?aqua:#3179f5, style=plot_type)
plot(show_ema?e3_a:na, title="EMA 50 angle",color=e3_a>ba?#cd1beb:e3_a<ba and e3_a>0?yellow:green, style=plot_type)
plot(show_ema?ea:na, title="EMA 200 angle",color=e_color, style=plot_type)


// SSL
g_ssl = "SSL"
inl_ssl1 = "1"
inl_ssl2 = "2"
show_ssl = input(title="Show SSL", type=input.bool, defval=true,group=g_ssl)
show_s1 = input(title="Show S1", type=input.bool, defval=true,group=g_ssl,inline=inl_ssl2)
show_s3 = input(title="Show S3", type=input.bool, defval=false,group=g_ssl,inline=inl_ssl2)
show_s4 = input(title="Show S4", type=input.bool, defval=false,group=g_ssl,inline=inl_ssl2)
show_s5 = input(title="Show S5", type=input.bool, defval=false,group=g_ssl,inline=inl_ssl2)
show_s6 = input(title="Show S6", type=input.bool, defval=false,group=g_ssl,inline=inl_ssl2)
show_s7 = input(title="Show S7", type=input.bool, defval=false,group=g_ssl,inline=inl_ssl2)
show_s8 = input(title="Show S8", type=input.bool, defval=false,group=g_ssl,inline=inl_ssl2)
s1_len = input(20, "S1",group=g_ssl,inline=inl_ssl1) // 30 
s3_len = input(8, "S3",group=g_ssl,inline=inl_ssl1)
s4_len = input(75, "S4",group=g_ssl,inline=inl_ssl1)
s5_len = input(100, "S5",group=g_ssl,inline=inl_ssl1)
s6_len = input(200, "S6",group=g_ssl,inline=inl_ssl1)
s7_len = input(300, "S7",group=g_ssl,inline=inl_ssl1)
s8_len = input(300, "S8",group=g_ssl,inline=inl_ssl1)

[s1_a] = angles(s1_len, "WMA")
[s3_a] = angles(s3_len, "WMA")
[s4_a] = angles(s4_len, "WMA")
[s5_a] = angles(s5_len, "WMA")
[s6_a] = angles(s6_len, "WMA")
[s7_a] = angles(s7_len, "WMA")
[s8_a] = angles(s8_len, "WMA")
plot(show_ssl and show_s1?s1_a:na, title="s1",color=s1_a > 0 ? red : green,linewidth=2,style=plot_type)
plot(show_ssl and show_s3?s3_a:na, title="S3 angle",color=color.new(white,50),style=plot_type)
plot(show_ssl and show_s4?s4_a:na, title="S4 angle",color=show_s4==true?yellow:color.new(yellow,100),style=plot_type)
plot(show_ssl and show_s5?s5_a:na, title="S5 angle",color=s5_a>0?green:red,style=plot_type)
plot(show_ssl and show_s6?s6_a:na, title="S6 angle",color=show_s6==true?green:color.new(green,100),style=plot_type)
plot(show_ssl and show_s7?s7_a:na, title="S7 angle", color=s7_a>0 ? color.new(red,60) : color.new(green,60),style=plot_type )
plot(show_ssl and show_s8?s8_a:na, title="S8 angle", color=s8_a>0 ? color.new(red,60) : color.new(green,60),style=plot_type )

// s4_cross = abs(s4_a - s5_a)
// plot(s4_cross and show_ema==false?s4_cross:na, title="S4 Cross",color=color.new(yellow,100))

// s4_s5_dist = abs(s4 - s5) * 1000
// plot(s4_s5_dist and show_ema==false?s4_s5_dist:na, title="S4 Dist",color=color.new(yellow,100))
// // s1 higher than s5

plotshape(show_ba and ba>10?1:na,title="BA top",color=#ff0000,style=shape.circle,location=location.top)

// ema_cond1 = s3_a<-10 and s3b_a<-10 and e2_a<0 and e3_a>0
// plotshape(ema_cond1?1:na, title='counter trade', color=orange, style=shape.circle,location=location.bottom)
ema_cond2 = e3_a>ba and e3_a>0 and s1_a<0 and e2_a<0 and e2_a<e2_a[1] and s4_a>s5_a
plotshape(show_ema and ema_cond2?1:na, title='counter trade', color=blue, style=shape.circle,location=location.bottom)

ema_cond3 = e3_a<ba and e3_a<0 and s1_a<-15 and e2_a<-8 and e2_a<e2_a[1] and s4_a<ba
plotshape(show_ema and ema_cond3?1:na, title='counter trade', color=lime, style=shape.circle,location=location.bottom)

s1_cond_u = s1_a>20 and s4_a>15 and s1_a<s1_a[1] and s1_a>s4_a
plotshape(show_ssl and s1_cond_u?1:na, title='counter trade', color=red, style=shape.circle,location=location.top)

s1_cond_d = s1_a<-20 and s4_a<-15 and s1_a>s1_a[1] and s1_a<s4_a and s4_a<s5_a
plotshape(show_ssl and s1_cond_d?1:na, title='counter trade', color=lime, style=shape.circle,location=location.bottom)
s4_cond_d = s4_a<-15 and s1_a>s4_a
plotshape(show_ssl and s4_cond_d?1:na, title='counter trade', color=blue, style=shape.circle,location=location.bottom)

hline(0)
hline(10,color=color.new(#ffffff,50)) 
hline(-10,color=color.new(#ffffff,50))
hline(15,color=color.new(red,50)) 
hline(-15,color=color.new(green,50))
hline(20,color=color.new(red,50)) 
hline(-20,color=color.new(green,50))
