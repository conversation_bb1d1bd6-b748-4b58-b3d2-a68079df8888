//@version=4
study(title = "Trading Bot", shorttitle = "Trading Bot", overlay=true, format = format.price, precision = 5, resolution = "")



// Custom inputs
trending = input(title="Trending/Ranging", type=input.bool, defval=true)



// === Bollinger Bands %B ===
// ==================================================
length = input(20, minval=1)
src = input(close, title="Source")
multi = input(2.0, minval=0.001, maxval=50, title="StdDev")
basis = sma(src, length)
deviation = multi * stdev(src, length)
upper = basis + deviation
lower = basis - deviation
bbr = (src - lower)/(upper - lower)
plot(bbr, "Bollinger Bands %B", color=color.teal)
band1 = hline(1, "Overbought", color=#606060, linestyle=hline.style_dashed)
band0 = hline(0, "Oversold", color=#606060, linestyle=hline.style_dashed)
fill(band1, band0, color=color.teal, title="Background")



// === BBand width ratio - kapipara180@ === 
// ==================================================
BB_Period = input(20, title="BBand period")
Deviation = input(2.0, "Deviation")
sko = stdev(close,BB_Period)
BBandwr = 2*(Deviation*sko)/sma(close,BB_Period)*1000 //length from UPband to Downband / SMA
BBandwr2 = 2*(Deviation*sko)/sma(close,BB_Period)*100
plot(BBandwr,"BBand width ratio", linewidth=3, color= #ff0000,transp=100)



// === Bollinger Awesome Alert R1.1 by JustUncleL ===
// ==================================================
bb_use_ema = input(false, title="Use EMA for Bollinger Band")
bb_source = input(close, title="Bollinger Source")
bb_length = input(20, minval=1, title="Bollinger Length")
bb_mult = input(2.0, title="Base Multiplier", minval=0.5, maxval=10)
sqz_length = 100
sqz_threshold = 50

// EMA inputs
fast_ma_len = input(3, title="Fast EMA length", minval=2)

// Breakout Indicator Inputs
ema_1 = ema(bb_source, bb_length)
sma_1 = sma(bb_source, bb_length)
bb_basis = bb_use_ema ? ema_1 : sma_1
fast_ma = ema(bb_source, fast_ma_len)

// Deviation
// * I'm sure there's a way I could write some of this cleaner, but meh.
dev = stdev(bb_source, bb_length)
bb_dev = bb_mult * dev
// Bands
bb_upper = bb_basis + bb_dev
bb_lower = bb_basis - bb_dev

// Calculate BB spread and average spread
bb_spread = bb_upper - bb_lower
avgspread = sma(bb_spread, sqz_length)

// Calculate BB relative %width for Squeeze indication
bb_squeeze = bb_spread / avgspread * 100
// plot fast ma
plot(fast_ma, title="Fast EMA", color=color.yellow, transp=10, linewidth=2)
plot(bb_basis, title="Basis Line", color=color.red, transp=10, linewidth=2)


// === SSL Hybrid - Mihkel00 ===
// ==================================================
len = input(title = "SSL1 / Baseline Length", defval = 60)
show_Baseline = input(title="Show Baseline", type=input.bool, defval=true)
show_SSL1 = input(title = "Show SSL1", type = input.bool, defval = false)
show_atr = input(title = "Show ATR bands", type = input.bool, defval = true)
//ATR
atrlen = input(14, "ATR Period")
mult = input(1, "ATR Multi", step = 0.1)
smoothing = input(title = "ATR Smoothing", defval = "WMA", options = ["RMA", "SMA", "EMA", "WMA"])


ma_function(source, atrlen) =>
	if smoothing == "RMA"
		rma(source, atrlen)
	else
		if smoothing == "SMA"
			sma(source, atrlen)
		else
			if smoothing == "EMA"
				ema(source, atrlen)
			else
				wma(source, atrlen)

atr_slen = ma_function(tr(true), atrlen)

////ATR Up/Low Bands
upper_band = atr_slen * mult + close
lower_band = close - atr_slen * mult

//SSL 1 and SSL2
emaHigh = wma(2 * wma(high, len / 2) - wma(high, len), round(sqrt(len)))
emaLow = wma(2 * wma(low, len / 2) - wma(low, len), round(sqrt(len)))

// BASELINE VALUES
BBMC = wma(2 * wma(close, len / 2) - wma(close, len), round(sqrt(len)))
multy = 0.2
range = tr
rangema = ema(range, len)
upperk =BBMC + rangema * multy
lowerk = BBMC - rangema * multy

//SSL1 VALUES
Hlv = int(na)
Hlv := close > emaHigh ? 1 : close < emaLow ? -1 : Hlv[1]
sslDown = Hlv < 0 ? emaHigh : emaLow

//COLORS
color_ssl1 = close > sslDown ? #00c3ff : close < sslDown ? #ff0062 : na
color_bar = close > upperk ? #00c3ff : close < lowerk ? #ff0062 : color.gray

//Plot Baseline & SSL1
p1 = plot(show_Baseline ? BBMC : na, color=color_bar, linewidth=4,transp=0, title='MA Baseline')
DownPlot = plot( show_SSL1 ? sslDown : na, title="SSL1", linewidth=3, color=color_ssl1, transp=10)

// ATR plot
u = plot(show_atr ? upper_band : na, "+ATR", color=color.white, transp=80)
l = plot(show_atr ? lower_band : na, "-ATR", color=color.white, transp=80)



// === MACD ===
// ==================================================
fast_length = 12
slow_length =26
md_src = close
signal_length = 9
sma_source = false
sma_signal = false
// Plot colors
col_grow_above = #26A69A
col_grow_below = #FFCDD2
col_fall_above = #B2DFDB
col_fall_below = #EF5350
col_macd = #0094ff
col_signal = #ff6a00
// Calculating
md_fast_ma = sma_source ? sma(md_src, fast_length) : ema(md_src, fast_length)
slow_ma = sma_source ? sma(md_src, slow_length) : ema(md_src, slow_length)
macd = md_fast_ma - slow_ma
signal = sma_signal ? sma(macd, signal_length) : ema(macd, signal_length)
hist = macd - signal
// plot(hist, title="Histogram", style=plot.style_columns, color=(hist>=0 ? (hist[1] < hist ? col_grow_above : col_fall_above) : (hist[1] < hist ? col_grow_below : col_fall_below) ), transp=0 )
// plot(macd, title="MACD", color=col_macd, transp=0)
// plot(signal, title="Signal", color=col_signal, transp=0)



// === RES - Ranging EMA Spread  ===
// ==================================================
ema1length = 12
ema2length = 50
ranginglength = 3
rangingmaxvalue = 0.1
rangingminvalue = -0.1
enablebarcolors = false

// EMA spread
ema1 = ema(close, ema1length)
ema2 = ema(close, ema2length)
spread = ((ema2 / ema1) -1) * 100
r1 = (spread >= rangingminvalue and spread <= rangingmaxvalue) 
r2 = (spread[1] >= rangingminvalue and spread[1] <= rangingmaxvalue)
r3 = (spread[2] >= rangingminvalue and spread[2] <= rangingmaxvalue)
r4 = (spread[3] >= rangingminvalue and spread[3] <= rangingmaxvalue)
r5 = (spread[4] >= rangingminvalue and spread[4] <= rangingmaxvalue)

ranging = false

if (ranginglength == 1) 
    ranging := r1
if (ranginglength == 2) 
    ranging := r1 and r2
if (ranginglength == 3) 
    ranging := r1 and r2 and r3
if (ranginglength == 4) 
    ranging := r1 and r2 and r3 and r4
if (ranginglength == 5) 
    ranging := r1 and r2 and r3 and r4 and r5

color_res = ranging ? #FFFF00 : spread > spread[1] ? #007600 : #18f918
res = spread > 0 ? 0-spread : abs(spread)
res := res * 100
plot(res,"RES", style=plot.style_histogram, linewidth=2, color=color_res, transp=100)



// === EMA 200 ===
// ==================================================
//@version=4
len_200 = 200
offset = input(title="Offset", type=input.integer, defval=0, minval=-500, maxval=500)
ema_200 = ema(close, len_200)
plot(ema_200, title="EMA 200", color=#fff176, offset=offset, linewidth=3)


 
// === Angle of Moving Average  ===
// ==================================================
i_lookback   = input(2,     "Angle Period", input.integer, minval = 1)
i_atrPeriod  = input(14,    "ATR Period",   input.integer, minval = 1)
i_angleLevel = input(6,     "Angle Level",  input.integer, minval = 1)

i_maLength   = input(20,    "MA Length",    input.integer, minval = 1)
i_maType     = input("WMA", "MA Type",      input.string, options=["ALMA", "EMA", "DEMA", "TEMA", "WMA", "VWMA", "SMA", "SMMA", "HMA", "LSMA", "Kijun", "McGinley"])
i_maSource   = input(close, "MA Source",    input.source)

i_lsmaOffset = input(0,   "* Least Squares (LSMA) Only - Offset Value", minval=0)
i_almaOffset = input(0.85,"* Arnaud Legoux (ALMA) Only - Offset Value", minval=0, step=0.01)
i_almaSigma  = input(6,   "* Arnaud Legoux (ALMA) Only - Sigma Value",  minval=0)

i_barColor   = input(true, "Bar Color ?")
i_noTZone    = input(true,  "No Trade Zone")

// ————— Determine Angle by KyJ
f_angle(_src, _lookback, _atrPeriod) =>
    rad2degree = 180 / 3.141592653589793238462643  //pi 
    ang = rad2degree * atan((_src[0] - _src[_lookback]) / atr(_atrPeriod)/_lookback)
    ang

// ————— Moving Averages
f_ma(type, _src, _len) =>
    float result = 0
    if type=="SMA" // Simple
        result := sma(_src, _len)
    if type=="EMA" // Exponential
        result := ema(_src, _len)
    if type=="DEMA" // Double Exponential
        e = ema(_src, _len)
        result := 2 * e - ema(e, _len)
    if type=="TEMA" // Triple Exponential
        e = ema(_src, _len)
        result := 3 * (e - ema(e, _len)) + ema(ema(e, _len), _len)
    if type=="WMA" // Weighted
        result := wma(_src, _len)
    if type=="VWMA" // Volume Weighted
        result := vwma(_src, _len) 
   //  if type=="SMMA" // Smoothed
   //      w = wma(_src, _len)
   //      result := na(w[1]) ? sma(_src, _len) : (w[1] * (_len - 1) + _src) / _len
    if type=="HMA" // Hull
        result := wma(2 * wma(_src, _len / 2) - wma(_src, _len), round(sqrt(_len)))
    if type=="LSMA" // Least Squares
        result := linreg(_src, _len, i_lsmaOffset)
    if type=="ALMA" // Arnaud Legoux
        result := alma(_src, _len, i_almaOffset, i_almaSigma)
    if type=="Kijun" //Kijun-sen
        kijun = avg(lowest(_len), highest(_len))
        result :=kijun
   //  if type=="McGinley"
   //      mg = 0.0
   //      mg := na(mg[1]) ? ema(_src, _len) : mg[1] + (_src - mg[1]) / (_len * pow(_src/mg[1], 4))
   //      result :=mg
    result
    
_ma    = f_ma(i_maType, i_maSource, i_maLength)
_angle = f_angle(_ma, i_lookback, i_atrPeriod)
color_H = _angle > 0 ? color.lime : _angle < 0 ? color.red : color.gray
color_L = 
   _angle > _angle[1] and (_angle > i_angleLevel or _angle < -i_angleLevel) ? color.lime :
   _angle < _angle[1] and (_angle > i_angleLevel or _angle < -i_angleLevel) ? color.red : color.gray

c_ntz = i_noTZone ? color_L : color_H
plot(_angle,"Angle MA Line", c_ntz, 3, plot.style_line,transp=100)
barcolor(i_barColor ? c_ntz : na)



// === WaveTrend with Crosses [LazyBear]  ===
// ==================================================
n1 = input(10, "Channel Length")
n2 = input(21, "Average Length")
obLevel1 = input(60, "Over Bought Level 1")
obLevel2 = input(53, "Over Bought Level 2")
osLevel1 = input(-60, "Over Sold Level 1")
osLevel2 = input(-53, "Over Sold Level 2")
 
ap = hlc3 
esa = ema(ap, n1)
d = ema(abs(ap - esa), n1)
ci = (ap - esa) / (0.015 * d)
tci = wma(ci, n2)
 
wt1 = tci
wt2 = sma(wt1,4)

//plot(0, color=color.gray)
//plot(obLevel1,title="Plot 1", color=color.red)
//plot(osLevel1,title="Plot 2", color=color.green)
//plot(obLevel2,title="Plot 3", color=color.red)
//plot(osLevel2,title="Plot 4", color=color.green)

red = #ff0000
green = #00ff00
lime = #bfff00
blue = #0000ff
black = #000000
//plot(wt1,title="WT Green", color=green, transp=80, style=plot.style_area)
//plot(wt2,title="WT Red", color=red, transp=80, style=plot.style_area)
//plot(wt1-wt2, color=blue, style=plot.style_area, transp=80)
//plot(cross(wt1, wt2) ? wt2 : na, color = black , style = plot.style_circles, linewidth = 3)
//plot(cross(wt1, wt2) ? wt2 : na, color = (wt2 - wt1 > 0 ? red : lime) , style = plot.style_circles, linewidth = 2)
//barcolor(cross(wt1, wt2) ? (wt2 - wt1 > 0 ? aqua : yellow) : na)



// === Trading Properties ===
// ==================================================
var int bb_count = 0
allow_trade = 0
entry_price = 0.00
exit_price = 0.00
// Initialize an empty array to store variables
props = array.new_float(4)


// === Trading Methods ===
// ==================================================
entry_signal() =>
	dir = 0
	trading = 0
	// Type of candle - Long / Short
	candle = close > open ? 1 : 0

	//co = crossover(sslDown,bb_basis) 
	//cu = crossunder(sslDown,bb_basis)

	// Sell signal
	if (bbr > 0.99 and candle == 1) or (fast_ma < open and high > bb_upper and candle == 1)
		dir := -1
		trading := 1
		
		//if bb_squeeze > sqz_threshold
			// Angle of Moving Average
			// if c_ntz == color.gray
			// 	dir := na
			// 	trading := 0

			// Wave Trend - Filter
			// if wt2 < 60 
			// 	dir := na
			// 	trading := 0

		//Above Fast MA line
		// if fast_ma < open
		// 	dir := -1
		// 	trading := 1
		// else 
		// 	dir := na
		// 	trading := 0

		// Crossdown - SSL and Bollinger Band basis line
		// if sslDown > bb_basis
		// 	dir := -1
		// 	trading := 1
		// else 
		// 	dir := na
		// 	trading := 0

		// RES FILTER 1
		// if res > 0
		// 	dir := -1
		// 	trading := 1
		// else 
		// 	dir := na
		// 	trading := 0

	// Buy signal
	else if (bbr < 0.01 and candle == 0) or (fast_ma > open and low < bb_lower and candle == 0)
	// else if (bbr < 0.01 and fast_ma > open) or (fast_ma > open and low < bb_lower and candle == 0)
		dir := 1
		trading := 1

		// Wave Trend - Filter
		// if wt2 > -60 
		// 	dir := na
		// 	trading := 0

		// Angle of Moving Average
		// if c_ntz == color.gray
		// 	dir := na
		// 	trading := 0

		// Below Fast MA line
		// if fast_ma > open
		// 	dir := -1
		// 	trading := 1
		// else 
		// 	dir := na
		// 	trading := 0

		// EMA 200 FILTER 1
		// if  close < ema_200 and open < ema_200 
		// 	dir := 1
		// 	trading := 1
		// else 
		// 	dir := na
		// 	trading := 0

		
		// RES FILTER 1
		// if res < 0
		// 	dir := 1
		// 	trading := 1
		// else 
		// 	dir := na
		// 	trading := 0

		// Crossdown - SSL and Bollinger Band basis line
		// if sslDown < bb_basis
		// 	dir := 1
		// 	trading := 1
		// else 
		// 	dir := na
		// 	trading := 0

	// Don't trade
	else
		dir := na
		trading := 0

	[dir, trading]

[trade_dir, trading] = entry_signal() 

if trade_dir
	bb_count := bb_count + 1
else
	bb_count := 0


if trade_dir
	bb_count := bb_count + 1
if bb_count > 2
	allow_trade := trade_dir
// if bb_count > 4
// 	bb_count := 0

// Res filter
// if res > 0 and trade_dir < 0
// 	dir := -1
// 	trading := 1
plotshape(trade_dir > 0 ? 1 : na, title="Entry Buy", color=res < 0 and trade_dir > 0 ? color.orange : color.green, location = location.belowbar, style=shape.labelup, text="B", textcolor=color.white, size=size.small)
plotshape(trade_dir < 0 ? -1 : na, title="Entry Sell", color= res > 0 and trade_dir < 0 ? color.orange : color.red, location = location.abovebar, style=shape.labeldown, text="S", textcolor=color.white, size=size.small)
//plotarrow(trade_dir, title="Trade Direction",colorup=color.green, colordown=color.red, transp=20, maxheight=20, offset=0)
bb_color = bb_count < 2 ? #555555 : #00ff00
plot(bb_count,title="BB Count", color=red, transp=0)
//plot(trade_dir, title="trade_dir",color=color_ssl1)


//label.new(x=bar_index, y=high, text= array.get(props, trade_direction) )
	//array.set(props, trade_direction, dir)
// if array.get(props, trade_direction) == 1	
// 	plotshape(array.get(props, trade_direction), title="Trade Direction", color=color.green, location = location.belowbar, style=shape.xcross, text="Buy", textcolor=color.green, size=size.tiny)

// Cross: SSL 1 & BBAWE base line
co = crossover(sslDown,bb_basis) 
cu = crossunder(sslDown,bb_basis)
//plotshape(cu, title="SSL crossup", color=color.green, location = location.belowbar, style=shape.xcross, text="Buy", textcolor=color.green, size=size.tiny)
//plotshape(co, title="SSL crossdown", color=color.red, location = location.abovebar, style=shape.xcross, text="Sell", textcolor=color.red, size=size.tiny)

channel_width = sslDown > bb_basis ? (sslDown - bb_basis) * 100 : (bb_basis - sslDown) * 100
histbase = sslDown > bb_basis ? sslDown * -1 : sslDown
//plot(channel_width, title="Channel Width",color=color_ssl1)


