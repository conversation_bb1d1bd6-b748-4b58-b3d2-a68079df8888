//@version=5
indicator(title="Aroon Copy", overlay=false)
strict = input(1,title="Strict")
i_time_aroon = input.timeframe(title='Timeframe', defval='15')

angle(_src, len) =>
    rad2degree = 180 / 3.14159265359  //pi 
    ang = rad2degree * math.atan((_src[0] - _src[1]) / ta.atr(len))
    ang

aroon_len = input(14) // 20, 9, 33
aroon_fun() =>
    float arn_u = 0.0
    float arn_l = 0.0
    arn_u := 100 * (ta.highestbars(high, aroon_len+1) + aroon_len)/aroon_len
    arn_l := 100 * (ta.lowestbars(low, aroon_len+1) + aroon_len)/aroon_len
    [arn_u,arn_l]

[arn_u,arn_l] = request.security(syminfo.tickerid, i_time_aroon, aroon_fun() )
plot(arn_u, "Aroon Sell", color=#ff0057) 
plot(arn_l, "Aroon Buy", color=#55d51a)

cond_sell = 
 strict==1 and arn_u==100 and arn_l==0?true
 : strict==2 and arn_u==100 and (arn_l==0 or arn_l<20)?true
 : strict==3 and arn_u==100 and (arn_l==0 or arn_l<20 or arn_l<50)?true
 : 0
//and arn_l[2]>50 strict 1
cond_buy = 
 strict==1 and arn_u==0 and arn_l==100?true
 : strict==2 and (arn_u==0  or arn_u<20) and arn_l==100?true
 : strict==3 and (arn_u==0  or arn_u<20  or arn_u<50) and arn_l==100?true
 : 0
plotshape(cond_sell,title="Sell",color=#FF0000,style=shape.circle,location=location.top)
plotshape(cond_buy,title="Buy",color=strict==1?#50ff00:strict==2?#00FF00 : strict==3?#814dff:na,style=shape.circle,location=location.bottom)
hline(50)

// Renko candles may help with counter trades if green and rsi_close5<30 take the trade

//plot((close - open) / (high - low), color=color.red)
//plot((close[1] - open[1]) / (high[1] - low[1]), color=color.green)

//plotshape(arn_u, "Aroon Up", color=#FB8C00), style=shape.circle,location=location.top

