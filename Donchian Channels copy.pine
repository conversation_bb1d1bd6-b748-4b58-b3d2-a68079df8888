//@version=5
indicator(title="Donchian Channels - Ben", overlay=true)

red = #ff0000
orange = #ff9800
yellow = #FFFF00
green = #4caf50
lime = #50e600
aqua = #00bcd4
blue = #2962ff
dark_blue = #2962ff
violet = #814dff
white = #ffffff
gray = #707070
black = #000000

// Change
perc_change() =>
    perc = math.abs((1 - close[1] / close) * 10000)
    perc

angle(_src, len) =>
    rad2degree = 180 / 3.14159265359  //pi 
    ang = rad2degree * math.atan((_src[0] - _src[1]) / ta.atr(len))
    ang

// SYM Info
min_tick = syminfo.mintick
var int decimals = int(math.log10(1/min_tick))
decimals := decimals == 2 ? 1 : decimals == 0 ? 1 : decimals
plot(min_tick, title='Min Tick')
plot(decimals, title='Decimals')
get_pip_distance(point1, point2) =>
    diff_points = math.abs( (point1 - point2) )
    pip_value = decimals<3 ? diff_points / syminfo.mintick / 100 : diff_points / syminfo.mintick / 10
    //pip_value = decimals>3 ? diff_points * (math.pow(10, decimals) * syminfo.mintick) : diff_points / syminfo.mintick / 10


//
// ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ //
// DONCHIAN CHANNELS
// ------------------------------------------------------------------------------------------------------------------
g_doch = 'Donchian Channels -----------------------------------------------------------'
i_doch_show = input.bool(true, title='Show', group=g_doch)
i_doch_time = input.timeframe('', "Resolution", group=g_doch) // 6 hour
i_doch_len = input.int(96, title='Length', minval=1, group=g_doch)
i_doch_smooth = input.int(1, 'Smoothing', group=g_doch)
i_doch_dist = input.float(0.1, 'Distance', step=0.1, group=g_doch)
i_perc_ch = input.float(2.5, 'Percent Change', step=0.1, group=g_doch)

f_doch()=>
    u = i_doch_smooth ? ta.sma(ta.highest(i_doch_len), i_doch_smooth ) : ta.highest(i_doch_len)
    l = i_doch_smooth ? ta.sma(ta.lowest(i_doch_len), i_doch_smooth ) : ta.lowest(i_doch_len)
    b = math.avg(u,l)
    a = angle(b,14)

    [u,l,b,a]

[doch_u,doch_l,doch_b,doch_a] = request.security(syminfo.tickerid, i_doch_time, f_doch() )

// PLOTS
plot(doch_b, "Doch Basis", color=#ff9800)
plot(doch_a, 'Doch Angle', color=color.new(blue,100))
u = plot(i_doch_show ? doch_u : na, "Doch Upper", color=#f23645)
l = plot(i_doch_show ? doch_l : na, "Doch Lower", color=#f23645)
fill(u, l, color=color.new(#f23645,90), title="Background")
// Distance
plot(get_pip_distance(high, doch_u), 'Distance', color=color.new(white,100), style=plot.style_circles)
doch_up = get_pip_distance(high, doch_u) < i_doch_dist ? 1 : na
plotshape(doch_up and close>open?1:na, title='Touch Down 2', color=red, style=shape.cross, location=location.top)
doch_down = get_pip_distance(low, doch_l) < i_doch_dist ? 1  : na
plotshape(doch_down and open>close ? 1: na, title='Touch Up 2', color=green, style=shape.cross, location=location.bottom)

// Candles percent change
barcolor(perc_change()>i_perc_ch ? yellow : na, title='Percent Change')


// ===  Fibo Trend ===
// ==================================================
g_fibo_trend = 'G Fibo Trend ----------------------------------------------------'
inl_fibo = 'inl-fib'
fibo_trend_time = input.timeframe("",title="Fibo Trend Time", group=g_fibo_trend)
show_gfibo = input.bool(false,"Show G Fibo Trend",group=g_fibo_trend)
show_candles = input.bool(true,"Fibo Candles",group=g_fibo_trend)
fibo_period = input.int(25,"Analysis Period",group=g_fibo_trend) // 50
lowerValue = input.float(0.382,"Lower Fibonacci Level",options=[0.236, 0.382, 0.50, 0.618, 0.786],group=g_fibo_trend)
upperValue = input.float(0.618,"Upper Fibonacci Level",options=[0.236, 0.382, 0.50, 0.618, 0.786],group=g_fibo_trend)
showFill = input.bool(true,"Show Filling",group=g_fibo_trend)
changeCandle = input.bool(true,"Change Candle Color",group=g_fibo_trend)

ma_val = input.int(10, title="MA",group=g_fibo_trend ) // 6

f_fibo_trend() =>
    ma = ta.wma(close,ma_val)
    max = ta.highest(close, fibo_period)
    min = ta.lowest(close, fibo_period)
    lowerFib = min + (max-min)*lowerValue
    upperFib = min + (max-min)*upperValue
    [ma, lowerFib, upperFib]

[ma, lowerFib, upperFib] = request.security(syminfo.tickerid, fibo_trend_time, f_fibo_trend() )

float closeVal = ma
float openVal = ma
color fibo_color = closeVal>upperFib and openVal>upperFib?green:closeVal<lowerFib and openVal<lowerFib?red:yellow
barcolor(show_candles? fibo_color : na, title='Fibo Bar Color')
plot(ma,title='MA')



