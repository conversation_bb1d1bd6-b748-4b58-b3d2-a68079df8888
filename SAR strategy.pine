// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © LonesomeTheBlue

//@version=5
indicator('Parabolic SAR', 'SAR Strategy', overlay=true,max_labels_count=500)

i_show_sar = input.bool(true, title='Show SAR')
i_sar_timeframe = input.timeframe('60', "SAR Timeframe")
start = input.float(title='Start', defval=0.02, step=0.001)
increment = input.float(title='Increment', defval=0.02, step=0.001)
maximum = input.float(title='Max Value', defval=0.2, step=0.01)
putlabel = input(title='Put Labels', defval=true)
colup = input.color(title='Colors', defval=color.lime, inline='col')
coldn = input.color(title='', defval=color.red, inline='col')

int trend = 0
float sar = 0.0
float ep = 0.0
float af = 0.0

trend := nz(trend[1])
ep := nz(ep[1])
af := nz(af[1])
sar := sar[1]

if trend == 0 and not na(high[1])
    trend := high >= high[1] or low >= low[1] ? 1 : -1
    sar := trend > 0 ? low[1] : high[1]
    ep := trend > 0 ? high[1] : low[1]
    af := start
    af
else
    nextsar = sar
    if trend > 0
        if high[1] > ep
            ep := high[1]
            af := math.min(maximum, af + increment)
            af

        nextsar := sar + af * (ep - sar)
        nextsar := math.min(math.min(low[1], low[2]), nextsar)

        //Reversal
        if nextsar > low
            trend := -1
            nextsar := ep
            ep := low
            af := start
            af
    else
        if low[1] < ep
            ep := low[1]
            af := math.min(maximum, af + increment)
            af

        nextsar := sar + af * (ep - sar)
        nextsar := math.max(math.max(high[1], high[2]), nextsar)

        //Reversal
        if nextsar < high
            trend := 1
            nextsar := ep
            ep := high
            af := start
            af
    sar := nextsar
    sar

plot(i_show_sar and sar ? sar : na, title='Parabolic SAR', color=trend > 0 ? colup : coldn, linewidth=2, style=plot.style_circles)

// Close 
var float change_level = 0.0
var float prev_level   = 0.0
candle = close>open ? 1 : 0
if ta.change(trend) > 0 and putlabel and i_show_sar
    // up
    change_level := math.round_to_mintick(sar)
    //prev_level  := math.round_to_mintick(sar)
    label.new(bar_index, sar, text=str.tostring(change_level ), color=colup, style=label.style_label_up, size=size.small)
    //label.new(bar_index - 1, change_level[1], text=str.tostring(change_level[1]), color=coldn, style=label.style_label_down, size=size.small)
if ta.change(trend) < 0 and putlabel and i_show_sar
    // Down
    change_level := math.round_to_mintick(sar)
    label.new(bar_index, sar, text=str.tostring(change_level ), color=coldn, style=label.style_label_down, size=size.small)

// alertcondition(ta.change(trend) > 0, title='PSAR Trend UP', message='PSAR Trend UP')
// alertcondition(ta.change(trend) < 0, title='PSAR Trend DOWN', message='PSAR Trend DOWN')

