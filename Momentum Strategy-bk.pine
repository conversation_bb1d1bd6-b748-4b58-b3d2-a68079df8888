//@version=5
// https://www.tradingview.com/pine-script-docs/en/v4/essential/Strategies.html
// https://www.tradingcode.net/tradingview/orders/
strategy("Momentum Strategy - v1", overlay=true, default_qty_value = 10, default_qty_type = strategy.fixed, initial_capital = 10000)

red = #ff0062
orange = #ff9800
yellow = #FFFF00
green = #4caf50
lime = #00E676
aqua = #00bcd4
blue = #2962ff
dark_blue = #2962ff
violet = #814dff
white = #ffffff
gray = #707070

// Angle
angle(_src, len) =>
    rad2degree = 180 / 3.14159265359  //pi 
    ang = rad2degree * math.atan((_src[0] - _src[1]) / ta.atr(len))
    ang

// Time Restiction
use_time 	= input.bool(false,title="Restrict Time")
timeAllowed = input.session("0300-1700", "Allowed hours")
daysAllowed = input.string("12345","Days")
timeIsAllowed = time(timeframe.period, timeAllowed + ":" + daysAllowed) // time(timeframe.period, "1000-1100,1400-1500:23456")

plot(time(timeframe.period, timeAllowed + ":" + daysAllowed),title="time")
plot(timeIsAllowed,title="Time", color=color.green, linewidth=2, style=plot.style_circles)
//bgcolor(timeIsAllowed ? color.new(green,90) : na)

timeinrange(res, sess) => not na(time(res, sess, "America/New_York")) ? 1 : 0
//plot(timeinrange("1", timeAllowed + ":" + daysAllowed), title="Time Range",color=color.red)

// This plots 1.0 at every start of 10 minute bar on a 1 minute chart:
newbar(res) => ta.change(time(res)) == 0 ? 0 : 1
//plot(newbar("10"), title="new bar")

// Label
if barstate.islastconfirmedhistory
    myLabel = label.new(x=bar_index + 100, y=close, style=label.style_label_left,
         color=color.blue, textcolor=color.white, size=size.normal,
         text="Exchange: " + str.tostring(syminfo.prefix)+ "\nSymbol: " + str.tostring(syminfo.ticker) )

symbol = str.tostring(syminfo.ticker)=='NAS100' ? 5 : str.tostring(syminfo.ticker)=='BTCUSD' ? 1 : 5
plot(symbol,title="Symbol")

// Momentum
length = input.int(5) // 5 NAS 1 BTC
price = close
momentum(seria, length) =>
	mom = seria - seria[length]
	mom
mom0 = momentum(price, length)
mom1 = momentum( mom0, 1)

goLong      = mom0 > 0 and mom1 > 0
goShort     = mom0 < 0 and mom1 < 0

// SMA
m50 = ta.sma(price,50)
m50_a = angle(m50,3)
// HMA
m8 = ta.hma(close,500)
m8_a = angle(m8,3)

// Bollinger Bands
bb_len = 14
basis = ta.sma(close,bb_len)
upper = 2 + ta.stdev(close,bb_len)
lower = 2 - ta.stdev(close,bb_len)
plot(basis, title="basis")
plot(upper, title="upper")
plot(lower, title="lower")

// Plots
use_mfilter = input.bool(true,title="M Filter")
plot(m50, color=m50_a>0 ? red : green)
plot(m8, color=m8_a>0 ? red : green)
//plot(m8_a, color=m8_a>0 ? red : green)

// Trading
trade_dir()=>
	var int dir = 0

	// Buy
	if mom0 > 0 and mom1 > 0
		dir := 1
	// Sell
	if mom0 < 0 and mom1 < 0
		dir := -1

	// Filters
	if basis>m50 and use_mfilter
		dir := 0
	// Time
	if use_time and nz(timeIsAllowed)
		dir := 0

	dir

var int dir = 0
dir := trade_dir()

candle_color = #505050
var int cnt_long = 1
var int cnt_short = 1
if (dir!= 0)

	if (dir==1)
		n_long  ='L #'+ str.tostring(cnt_long)
		strategy.entry("L", strategy.long, stop=high + syminfo.mintick, comment=n_long)
		cnt_long := cnt_long + 1
		candle_color := aqua
	else
		strategy.cancel("L")
	if (dir==-1)
		n_short ='S #'+ str.tostring(cnt_short)
		strategy.entry("S", strategy.short, stop=low-syminfo.mintick, comment=n_short)
		cnt_short := cnt_short + 1
		candle_color := orange
	else
		strategy.cancel("S")



// if (timeIsAllowed==0.00)
// 	strategy.close(id="MomLE",comment="Close \n MomLE ")
// 	strategy.close(id="MomSE",comment="Close \n MomSE ")

barcolor(candle_color)

//plot(high+syminfo.mintick, title="Stop Loss")
//plot(strategy.equity, title="equity", color=color.new(red,90), style=plot.style_areabr)
