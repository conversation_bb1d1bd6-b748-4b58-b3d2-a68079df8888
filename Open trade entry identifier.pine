//@version=5
strategy(title="Open trade entry identifier", overlay=true)

// Calculate and plot the smoothed average
smoothEMA = ta.ema(ta.ema(close, 20), 5)

plot(smoothEMA, color=color.lime, linewidth=2, title="Smoothed EMA")

// Go long and short with two consecutive bars above the moving average
if close > smoothEMA and close[1] > smoothEMA[1]
    strategy.entry("Enter Long", strategy.long)

if close < smoothEMA and close[1] < smoothEMA[1]
    strategy.entry("Enter Short", strategy.short)

// Use the total number of trades to see if a new entry happened
totalTrades = strategy.closedtrades + strategy.opentrades
newEntry = totalTrades > totalTrades[1] and strategy.opentrades > 0

// When there's a new entry, make a text label
if newEntry
    // Generate the label's text with the entry name and price
    labelText = strategy.opentrades.entry_id(0) + "\n@ " + 
         str.tostring(strategy.opentrades.entry_price(0))
    
    // When the entry order name contains the phrase 'Long', make a
    // green label. Else a red, up-pointing one.
    if str.contains(strategy.opentrades.entry_id(0), "Long")
        label.new(bar_index, high, color=#D0F0C0, text=labelText)
    else
        label.new(bar_index, low, color=#FDBCB4, text=labelText,
             style=label.style_label_up)