//@version=4
study(title="BB Bands - New", overlay=true, shorttitle="BB Bands - New")

// two bb bands working together
// large-77 Small - 20 or 33
BB_length = input(25) // 45 100 25
//sqz_length = input(100, title="Squeeze") // 100
bb_smooth_type = input(title="Smooth Type", defval="SMA", options=["SMA", "EMA", "HULL" ])
smoothing_amount = input(1,title="Smoothing")
bb_trans = input(50,title="Bands transparency")
fill_trans = input(100,title="Fill transparency")
use_fill = input(title="Use Fill", type=input.bool, defval=true)
ba_diff_angle_input = input(title="Basis Diff Angle", defval=3)
bias = input(title="Bias", type=input.float, defval=89.7,step=0.1)

use_ba_color = input(title="Basis Angle color", type=input.bool, defval=false)
use_barcolor = input(title="Enable bar color", type=input.bool, defval=true)
bar_color = input(title="Bar Color", type=input.color, defval=color.yellow)

white = #ffffff
gray = #707070
yellow = #FFFF00
aqua = #00bcd4
lime = #00E676
red = #ff0000
green = #4caf50

// Gradient 1
g1 = #0045b3
g2 = #ff0062
g3 = #707070
g4 = #00c3ff
g5 = #ffffff
g6 = #FFFF00

// Gradient 1
// g1 = #662400
// g2 = #B33F00
// g3 = #FF6B1A
// g4 = #006663
// g5 = #00B3AD
// g6 = #00f7ef

angle(_src, len) =>
    rad2degree=180/3.14159265359  //pi 
    ang=rad2degree*atan((_src[0] - _src[1])/atr(len))


bb_smoothing(len1,type) =>
    float v = na
    if type=="SMA"
        v := sma(close, len1)
    if type=="EMA"
        v := ema(close, len1)
    if type=="HULL"
        v := wma(2*wma(close, len1/2)-wma(close, len1), round(sqrt(len1)))

    v := sma(v, smoothing_amount) 

    [v]


// ===  Bollinger Bands ===
// ==================================================

BB_stdDev = 2 //input(2, minval=2.0, maxval=3)
sqz_length  = 100 
[basis] = bb_smoothing(BB_length,bb_smooth_type)
dev = BB_stdDev * stdev(close, BB_length)
bb_upper = basis + dev
bb_lower = basis - dev
bb_spread = bb_upper - bb_lower
avgspread = sma(bb_spread, sqz_length )
bb_squeeze = 0.00
bb_squeeze := bb_spread / avgspread * 100
bb_diff = (bb_upper - bb_lower) * 1000
ba = angle(basis,ba_diff_angle_input)
bbu_angle = angle(bb_upper,ba_diff_angle_input)
bbl_a = angle(bb_lower,ba_diff_angle_input)
bb_diff_a = angle(bb_diff,ba_diff_angle_input)

barcolor(use_barcolor==true and bb_diff_a>bias ? bar_color:na, title="BB Candles")

bb_zone = bb_squeeze < 53 ? 0 : 
 bb_squeeze < sqz_length  ? 1 : 
 bb_squeeze < 120 ? 2 :
 bb_squeeze < 160 ? 3 :
 bb_squeeze < 200 ? 4 :
 bb_squeeze < 250 ? 5 :
 bb_squeeze > 250 ? 6 : na
sqz_color = bb_zone == 0 ? g1 :
 bb_zone == 1 ? g2 : 
 bb_zone == 2 ? g3 : 
 bb_zone == 3 ? g4 :  
 bb_zone == 4 ? g5:
 bb_zone == 5 ? g6:
 bb_zone == 6 ? white: na
 

bb_zones_color =  color.new(sqz_color,bb_trans)

// Basis Angle color
ba_abs = abs(ba)
ba_zone = ba_abs < 1 ? 0 : 
 ba_abs < 3 ? 1 : 
 ba_abs < 5 ? 2 :
 ba_abs < 7 ? 3 :
 ba_abs < 9 ? 4 :
 ba_abs < 11 ? 5 :
 ba_abs > 11 ? 6 : na
ba_color = ba_zone == 0 ? g1 :
 ba_zone == 1 ? g2 : 
 ba_zone == 2 ? g3 : 
 ba_zone == 3 ? g4 :  
 ba_zone == 4 ? g5:
 ba_zone == 5 ? g6:
 ba_zone == 6 ? white: na

dist_to_close = close>open ? bb_upper - close : close - bb_lower
plot( (bb_diff / 10), title="BB diff", style=plot.style_circles, color= ba>0 ? color.new(red,100) : color.new(green,100))
//plot(dist_to_close, title="Dist to close", style=plot.style_circles, color= close>open ? red : green)

plot(ba,title="BA", color=color.new(bb_zones_color,100),style=plot.style_circles)
plot(bbu_angle,title="BB Upper Angle", color=color.new(bb_zones_color,100),style=plot.style_circles)
plot(bbl_a,title="BB Lower Angle", color=color.new(bb_zones_color,100),style=plot.style_circles)
plot(bb_diff,title="BB Diff", color=color.new(bb_zones_color,100),style=plot.style_circles)
plot(bb_diff_a,title="BB Diff Angle",color=color.new(white,100),style=plot.style_circles)

bb_color = use_ba_color ? ba_color : bb_zones_color
plot(basis, "Basis ",color=ba>0 ? red : green)
p1 = plot(bb_upper, "BB Upper ",color=bb_color)
p2 = plot(bb_lower, "BB Lower ",color=bb_color)
fill(p1,p2, color=use_fill?color.new(bb_color,fill_trans):na)
plot(bb_squeeze, title="BB Zones", color=color.new(bb_color,100) )

