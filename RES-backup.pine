//@version=4
study("Ranging EMA Spread", shorttitle="RES copy", overlay=false, precision=2)

ema1length = input(12, minval=1, title="EMA 1 Length") // 40 12
ema2length = input(50, minval=1, title="EMA 2 Length") // 100 43 50
// ema1length = input(8, minval=1, title="EMA 1 Length")
// ema2length = input(45, minval=1, title="EMA 2 Length")
ranginglength = input(3, minval=1, maxval=5, type=input.integer, title="Bar Ranging Threshold")
rangingmaxvalue = input(0.12, minval=0.01, maxval=2, step=0.01, type=input.float, title="Max Ranging Value") // 0.1
rangingminvalue = input(-0.1, minval=-0.01, maxval=-2, step=0.01, type=input.float, title="Min Ranging Value") // -0.1
enablebarcolors = input(true, title="Enable Bar Colors?")

// Colors
weak = color.new(#707070, 0)
average = color.new(#0045b3, 0)
strong = color.new(#00c3ff, 0)

// EMA spread
ema1 = ema(close, ema1length)
ema2 = ema(close, ema2length)
//ema2 = sma(close, ema2length)
spread = ((ema2 / ema1) -1) * 100

r1 = (spread >= rangingminvalue and spread <= rangingmaxvalue) 
r2 = (spread[1] >= rangingminvalue and spread[1] <= rangingmaxvalue)
r3 = (spread[2] >= rangingminvalue and spread[2] <= rangingmaxvalue)
r4 = (spread[3] >= rangingminvalue and spread[3] <= rangingmaxvalue)
r5 = (spread[4] >= rangingminvalue and spread[4] <= rangingmaxvalue)

ranging = false

if (ranginglength == 1) 
    ranging := r1
if (ranginglength == 2) 
    ranging := r1 and r2
if (ranginglength == 3) 
    ranging := r1 and r2 and r3
if (ranginglength == 4) 
    ranging := r1 and r2 and r3 and r4
if (ranginglength == 5) 
    ranging := r1 and r2 and r3 and r4 and r5

color = ranging ? weak : spread > spread[1] ? average : strong

flippedspread = spread > 0 ? 0-spread : abs(spread)
plot(flippedspread, style=plot.style_columns, linewidth=2, color=color)

barcolor(enablebarcolors ? color : na)