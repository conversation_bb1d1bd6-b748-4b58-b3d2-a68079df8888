//@version=4
study(title="SSL channel Angles", overlay=false, shorttitle="SSL Channel Angles")


angle_input = input(title="Angle Amount",type=input.integer, defval=3)
show_line = input(title="Show Lines", type=input.bool, defval=true)

angle(_src, len) =>
    rad2degree=180/3.14159265359  //pi 
    ang=rad2degree*atan((_src[0] - _src[1])/atr(len))

// Ch1
ch_len=input(title="Ch1 length", defval=100) // 33
smaHigh=sma(high, ch_len)
smaLow=sma(low, ch_len)
var ch_Hlv = 0
ch_Hlv := close>smaHigh ? 1 : close<smaLow ? -1 : ch_Hlv[1]
sslDown = ch_Hlv < 0 ? smaHigh: smaLow
sslUp   = ch_Hlv < 0 ? smaLow : smaHigh
ch1_mid = (sslDown + sslUp) * 0.5
ch1_a = angle(ch1_mid,angle_input)

// Ch2
ch_len2=input(title="Ch2 length", defval=40) // 17
smaHigh2=sma(high, ch_len2)
smaLow2=sma(low, ch_len2)
var ch_Hlv2 = 0
ch_Hlv2 := close>smaHigh2 ? 1 : close<smaLow2 ? -1 : ch_Hlv2[1]
sslDown2 = ch_Hlv2 < 0 ? smaHigh2: smaLow2
sslUp2   = ch_Hlv2 < 0 ? smaLow2 : smaHigh2
ch2_mid = (sslDown2 + sslUp2) * 0.5
ch2_a = angle(ch2_mid,angle_input)

// Ch3
ch_len3=input(title="Ch3 length", defval=25) // 17
smaHigh3=sma(high, ch_len3)
smaLow3=sma(low, ch_len3)
var ch_Hlv3 = 0
ch_Hlv3 := close>smaHigh3 ? 1 : close<smaLow3 ? -1 : ch_Hlv3[1]
sslDown3 = ch_Hlv3 < 0 ? smaHigh3: smaLow3
sslUp3   = ch_Hlv3 < 0 ? smaLow3 : smaHigh3
mid_way3 = (sslDown3 + sslUp3) * 0.5

ch3_mid = (sslDown3 + sslUp3) * 0.5
ch3_a = angle(ch3_mid,angle_input)


plot(ch1_a, title="Ch1 Angle", color=ch1_a>0?color.white:color.green)
plot(ch2_a, title="Ch2 Angle", color=color.blue)
plot(ch3_a, title="Ch3 Angle", color=color.red)

hline(show_line?10:na,color=color.new(#ff0000,60))
hline(show_line?5:na,color=color.new(#ff9800,60))
hline(show_line?0:na,color=color.new(color.gray,60))
hline(show_line?-5:na,color=color.new(#089981,60))
hline(show_line?-10:na,color=color.new(#55d51a,60))
// Mid way points
//plot(mid_way,title="mid ch1",color=color.green)
//plot(mid_way2,title="mid ch2",color=color.white)

//suplot=plot(angle(sslUp,3), linewidth=channel_line_width, color=color.red)
//plot(mid_way, color=color.white)

//fill(sdplot,suplot, color=sslDown>sslUp and show_channel_fill?color.lime:sslUp>sslDown and show_channel_fill? color.red:na)