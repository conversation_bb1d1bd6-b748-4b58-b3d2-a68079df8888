//@version=5
strategy('Marks Strategy', overlay=true, precision=4, initial_capital=10000, default_qty_type=strategy.fixed, default_qty_value=10, currency=currency.USD, process_orders_on_close=true, pyramiding=1,  max_labels_count=500)

red = #ff0062
orange = #ff9800
yellow = #FFFF00
green = #4caf50
lime = #00E676
aqua = #00bcd4
blue = #2962ff
dark_blue = #2962ff
violet = #814dff
white = #ffffff
gray = #707070
black = #000000

// Angle
angle(_src, len) =>
    rad2degree = 180 / 3.14159265359  //pi 
    ang = rad2degree * math.atan((_src[0] - _src[1]) / ta.atr(len))
    ang

// Change
perc_change() =>
    perc = math.abs((1 - close[1] / close) * 10000)
    perc


// g_1min = '1 Minute MA\'s ----------------------------------------------------'
// inl_1min = '1-min'
// inl_1min_len = '1-min-len'
// show_ma_1min = input.bool(title='Show', defval=false, group=g_1min, inline=inl_1min)
// m_1min_len1 = input.int(8, minval=1, title='M1', inline=inl_1min_len) 
// m_1min_len2 = input.int(20, minval=1, title='M2', inline=inl_1min_len) 
// m_1min_len3 = input.int(50, minval=1, title='SMA', inline=inl_1min_len) 

// ma_1min() =>
//     src = close
//     m1 = ta.hma(src,m_1min_len1)
//     m2 = ta.hma(src,m_1min_len2)
//     m3 = ta.sma(src,m_1min_len3)

//     [m1,m2,m3]

// [m1f,m2f,m3f] = ma_1min()

// plot(m1f and show_ma_1min?m1f:na,title="m1",color=#53bad1,linewidth=2)
// plot(m2f and show_ma_1min?m2f:na,title="m2",color=#c04045,linewidth=2)
// plot(m3f and show_ma_1min?m3f:na,title="m3",color=#fdea60,linewidth=2)



g_mom = 'Momentum ----------------------------------------------------'
length = input.int(5,group=g_mom) // 5 NAS 1 BTC
price = close
momentum(seria, length) =>
	mom = seria - seria[length]
	mom
mom0 = momentum(price, length)
mom1 = momentum( mom0, length)



g_rsi_band = 'RSI + Bands ----------------------------------------------------'
rsi_time = input.timeframe("",title="Timeframe", group=g_rsi_band)
isBB = true
rsiLengthInput = input.int(14, minval=1, title="RSI Length", group=g_rsi_band)
rsiSourceInput = input.source(close, "Source", group=g_rsi_band)
maLengthInput = input.int(20, title="MA Length", group=g_rsi_band) // 14
bbMultInput = input.float(1.0, minval=0.001, maxval=50, title="BB StdDev", group=g_rsi_band) // 2.0

rsi_bb()=>
    up = ta.rma(math.max(ta.change(rsiSourceInput), 0), rsiLengthInput)
    down = ta.rma(-math.min(ta.change(rsiSourceInput), 0), rsiLengthInput)
    rsi = down == 0 ? 100 : up == 0 ? 0 : 100 - (100 / (1 + up / down))
    rsiMA = ta.sma(rsi, maLengthInput)
    bbUpperBand = rsiMA + ta.stdev(rsi, maLengthInput) * bbMultInput
    bbLowerBand = rsiMA - ta.stdev(rsi, maLengthInput) * bbMultInput

    [rsi,rsiMA,bbUpperBand,bbLowerBand]

[rsi,rsiMA,bbUpperBand,bbLowerBand] = rsi_bb()

rsi_m = request.security(syminfo.tickerid, rsi_time, rsi )
rsiMA_m = request.security(syminfo.tickerid, rsi_time, rsiMA )
bbUpperBand_m = request.security(syminfo.tickerid, rsi_time, bbUpperBand )
bbLowerBand_m = request.security(syminfo.tickerid, rsi_time, bbLowerBand )

// Alerts
rsi_up = rsi_m>bbUpperBand_m and rsiMA_m>50 ? rsiMA_m : na
plot(rsi_up,title="RSI Up", style=plot.style_circles,color=color.red)
rsi_down = rsi_m<bbLowerBand_m and  rsiMA_m<50 ? rsiMA_m : na
plot(rsi_down,title="RSI Down", style=plot.style_circles,color=color.green)



// ===  Fibo Trend ===
// ==================================================
g_fibo_trend = 'G Fibo Trend ----------------------------------------------------'
inl_fibo = 'inl-fib'
show_gfibo = false //input.bool(false,"Show G Fibo Trend",group=g_fibo_trend)
show_candles = input.bool(false,"Fibo Candles",group=g_fibo_trend)
BackStep = input.int(25,"Analysis Period") // 50
lowerValue = input.float(0.382,"Lower Fibonacci Level",options=[0.236, 0.382, 0.50, 0.618, 0.786])
upperValue = input.float(0.618,"Upper Fibonacci Level",options=[0.236, 0.382, 0.50, 0.618, 0.786])
showFill = input.bool(true,"Show Filling")
changeCandle = input.bool(true,"Change Candle Color")
atr = ta.atr(200)
max = ta.highest(close,BackStep)
min = ta.lowest(close,BackStep)

lowerFib = min + (max-min)*lowerValue
upperFib = min + (max-min)*upperValue
ma_val = input.int(10, title="MA" ) // 6
ma = ta.wma(close,ma_val)

float closeVal = ma
float openVal = ma
color clrToUse = closeVal>upperFib and openVal>upperFib?green:closeVal<lowerFib and openVal<lowerFib?red:yellow

// maxLine = plot(max and show_gfibo?max : na,color=color.green,title="Max")
// minLine = plot(min and show_gfibo?min : na,color=color.red,title="Min")
// LowerFibLine = plot(lowerFib and show_gfibo?lowerFib : na,color=color.rgb(228, 255, 75, 20),title="Lower Fib")
// UpperFibLine = plot(upperFib and show_gfibo?upperFib : na,color=color.rgb(228, 255, 75, 20),title="Upper Fib")
// fill(maxLine,UpperFibLine,color=showFill?color.rgb(0,255,0,changeCandle?95:70):na)
// fill(UpperFibLine,LowerFibLine,color=showFill?color.rgb(228, 255, 75, changeCandle?95:70):na)
// fill(LowerFibLine,minLine,color=showFill?color.rgb(255,0,0,changeCandle?95:70):na)
barcolor(show_candles? clrToUse : na)
//plotcandle(open,high,low,close,"Bar",color=changeCandle?clrToUse:na,wickcolor=changeCandle?clrToUse:na,bordercolor=changeCandle?clrToUse:na)

float LowerRetracement = (max-min)*0.318
float UpperRetracement = (max-min)*0.618




// ===  GMMA ===
// ==================================================
g_GMMA_OSC = 'GMMA OSC ----------------------------------------------------'
inl_g_GMMA_OSC = 'inl-g_GMMA_OSC'

// Use Alternate Anchor TF for MAs 
anchor = 0 //input.int(0, minval=0, maxval=1440, title='Use Alternate Anchor TimeFrame (0=none, max=1440 (mins,D,W)')
gmmaType = 'Guppy' //input.string('Guppy', title='Calculate Oscillator From Which GMMA Sets', options=['Guppy', 'SuperGuppy'])
smoothLen = input.int(3, minval=1, title='Oscillator Smoothing Length (1=none)', group=g_GMMA_OSC)  // 1
signalLen = input.int(13, minval=1, title='GMMA Oscillator Signal Length', group=g_GMMA_OSC)
showZones = input(false, title='Show Bullish/Bearish Zones', group=g_GMMA_OSC)
//
src = close //input(close, title='Source')
angle_amount = 14 //input.int(14, minval=1, title='Angle Len')
show_angles = input(title='Show Angles', defval=false)
use_zero_line = true //input(title='Zero Line', defval=true)

//Fast Guppy Avg EMA
GMMAFast(src, mult) =>
    ema1 = ta.ema(src, 3 * mult)
    ema2 = ta.ema(src, 5 * mult)
    ema3 = ta.ema(src, 8 * mult)
    ema4 = ta.ema(src, 10 * mult)
    ema5 = ta.ema(src, 12 * mult)
    ema6 = ta.ema(src, 15 * mult)
    return_1 = ema1 + ema2 + ema3 + ema4 + ema5 + ema6
    return_1

//Slow Guppy Avg EMA
GMMASlow(src, mult) =>
    ema7 = ta.ema(src, 30 * mult)
    ema8 = ta.ema(src, 35 * mult)
    ema9 = ta.ema(src, 40 * mult)
    ema10 = ta.ema(src, 45 * mult)
    ema11 = ta.ema(src, 50 * mult)
    ema12 = ta.ema(src, 60 * mult)
    return_2 = ema7 + ema8 + ema9 + ema10 + ema11 + ema12
    return_2

//Fast SuperGuppy Avg EMA
superGMMAFast(src, mult) =>
    emaF1 = ta.ema(src, 3 * mult)
    emaF2 = ta.ema(src, 5 * mult)
    emaF3 = ta.ema(src, 7 * mult)
    emaF4 = ta.ema(src, 9 * mult)
    emaF5 = ta.ema(src, 11 * mult)
    emaF6 = ta.ema(src, 13 * mult)
    emaF7 = ta.ema(src, 15 * mult)
    emaF8 = ta.ema(src, 17 * mult)
    emaF9 = ta.ema(src, 19 * mult)
    emaF10 = ta.ema(src, 21 * mult)
    emaF11 = ta.ema(src, 23 * mult)
    return_3 = (emaF1 + emaF2 + emaF3 + emaF4 + emaF5 + emaF6 + emaF7 + emaF8 + emaF9 + emaF10 + emaF11) / 11
    return_3

//Slow SuperGuppy Avg EMA
superGMMASlow(src, mult) =>
    emaS1 = ta.ema(src, 25 * mult)
    emaS2 = ta.ema(src, 28 * mult)
    emaS3 = ta.ema(src, 31 * mult)
    emaS4 = ta.ema(src, 34 * mult)
    emaS5 = ta.ema(src, 37 * mult)
    emaS6 = ta.ema(src, 40 * mult)
    emaS7 = ta.ema(src, 43 * mult)
    emaS8 = ta.ema(src, 46 * mult)
    emaS9 = ta.ema(src, 49 * mult)
    emaS10 = ta.ema(src, 52 * mult)
    emaS11 = ta.ema(src, 55 * mult)
    emaS12 = ta.ema(src, 58 * mult)
    emaS13 = ta.ema(src, 61 * mult)
    emaS14 = ta.ema(src, 64 * mult)
    emaS15 = ta.ema(src, 67 * mult)
    emaS16 = ta.ema(src, 70 * mult)
    // average
    return_4 = (emaS1 + emaS2 + emaS3 + emaS4 + emaS5 + emaS6 + emaS7 + emaS8 + emaS9 + emaS10 + emaS11 + emaS12 + emaS13 + emaS14 + emaS15 + emaS16) / 16
    return_4


// Calculate the Multiplier for Anchor MAs.
mult = not timeframe.isintraday or anchor == 0 or timeframe.multiplier <= 0 or timeframe.multiplier >= anchor or anchor > 1440 ? 1 : math.round(anchor / timeframe.multiplier) > 1 ? math.round(anchor / timeframe.multiplier) : 1
mult := timeframe.isintraday or anchor == 0 or timeframe.multiplier <= 0 or timeframe.multiplier >= anchor or anchor > 52 ? mult : math.round(anchor / timeframe.multiplier) > 1 ? math.round(anchor / timeframe.multiplier) : 1

// Select type of Oscillator calculation
gmmaFast = gmmaType == 'Guppy' ? GMMAFast(src, mult) : superGMMAFast(src, mult)
gmmaSlow = gmmaType == 'Guppy' ? GMMASlow(src, mult) : superGMMASlow(src, mult)

// Calculate Oscillator, Smoothed Osc and signal line
gmmaOscRaw = (gmmaFast - gmmaSlow) / gmmaSlow * 100
gmmaOsc = ta.sma(gmmaOscRaw, smoothLen)
gmmaSignal = ta.ema(gmmaOscRaw, signalLen)
gmmaClr = gmmaOsc < gmmaSignal ? red : gmmaOsc > gmmaSignal ? green : gray

// Angles 
gmmaOsc_a = show_angles ? angle(gmmaOsc, angle_amount) * 0.001 : na
gmmaSignal_a = show_angles ? angle(gmmaSignal, angle_amount) * 0.001 : na

// bullish signal rule: 
bullishRule = ta.crossover(gmmaOsc, gmmaSignal)
// bearish signal rule: 
bearishRule = ta.crossunder(gmmaOsc, gmmaSignal)
// current trading State
ruleState = 0
ruleState := bullishRule ? 1 : bearishRule ? -1 : nz(ruleState[1])


// === PLOTTING === 
// plot(gmmaOsc, title='GMMA OSC Smooth', style=plot.style_line, linewidth=2, color=color.new(gmmaClr,10) )
// plot(gmmaOsc_a, title='GMMA OSC Angle', style=plot.style_line, linewidth=0, color=color.new(gmmaClr,65) )
// plot(gmmaSignal, title='GMMA Signal', style=plot.style_line, linewidth=1, color=color.new(orange, 10))
// plot(gmmaSignal_a, title='GMMA Signa Angle', style=plot.style_line, linewidth=0, color=color.new(orange, 65))


//color.new(hline,90)(0, title='Zero line', linestyle=hline.style_dotted, linewidth=2, color=gray)
//color.new(hline,90)(0.23, title='High', linestyle=hline.style_dotted, linewidth=2, color=color.new(red, 50))
//color.new(hline,90)(-0.23, title='Low', linestyle=hline.style_dotted, linewidth=2, color=color.new(green, 50))
// === /PLOTTING === 

//
// ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ //

// === ALERTS === 

bgcolor(showZones ? ruleState == 1 ? color.new(green,90) : ruleState == -1 ? color.new(red,90) : color.new(gray,90) : na, title='Guppy Bullish/Bearish Zones')


// ===  MA's ===
// ==================================================

g_cb = 'MA\' s ----------------------------------------------------'
ma_inl1 = 'len1'
ma_type = input.string(title='Type', defval='hma', options=['sma', 'ema', 'dema', 'tema', 'wma', 'vwma', 'smma', 'rma', 'hma', 'lsma', 'McGinley'], group=g_cb)
show_m1 = input.bool(title='m1', defval=true, group=g_cb, inline=ma_inl1)
show_m2 = input.bool(title='m2', defval=true, group=g_cb, inline=ma_inl1)
show_m3 = input.bool(title='m3', defval=true, group=g_cb, inline=ma_inl1)
show_m4 = input.bool(title='m4', defval=false, group=g_cb, inline=ma_inl1)
show_m5 = input.bool(title='m5', defval=true, group=g_cb, inline=ma_inl1)
show_m6 = input.bool(title='m6', defval=true, group=g_cb, inline=ma_inl1)
show_m7 = input.bool(title='m7', defval=false, group=g_cb, inline=ma_inl1)
show_m8 = input.bool(title='m8', defval=true, group=g_cb, inline=ma_inl1)

ma_inl2 = 'len2'
m_len1 = input.int(5, minval=1, title='M1', inline=ma_inl2)  // 8
m_len2 = input.int(20, minval=1, title='M2', inline=ma_inl2)  // 20
m_len3 = input.int(50, minval=1, title='M3', inline=ma_inl2)  // 50
m_len4 = input.int(75, minval=1, title='M4', inline=ma_inl2)  // 75 
m_len5 = input.int(100, minval=1, title='M5', inline=ma_inl2)  // 100
m_len6 = input.int(200, minval=1, title='M6', inline=ma_inl2)  // 200
m_len7 = input.int(300, minval=1, title='M7', inline=ma_inl2)  // 300
m_len8 = input.int(500, minval=1, title='M8', inline=ma_inl2)  // 500

ma_inl3 = 'len3'
ma_inl4 = 'len4'
ma_inl5 = 'len5'
i_use_smooth  = input.bool(false, title="", inline=ma_inl3)
i_smooth      = input.int(10, title="Smooth", inline=ma_inl3)
i_use_angle  = input.bool(false, title="", inline=ma_inl4)
i_angle =  input.int(14,title="Angle Amount", inline=ma_inl4) // 25
i_ma_multiple = input.bool(false, title="", inline=ma_inl5)
i_ma_multi_value = input.int(9, title="Multiply Value", inline=ma_inl5) // 10


g_fill = 'Plot'
inl_fill = 'fill'
inl_conv = 'conv'
show_fill = input.bool(title='Show Fill', defval=true, inline=inl_fill, group=g_fill)
show_conv = input.bool(title='Show Conv', defval=true, inline=inl_fill, group=g_fill)
conv_amount = input.float(defval=25, title='Conv Amount', step=1, inline=inl_conv, group=g_fill) //4 
c_type = input.string(title='Type', defval='NAS', options=['NAS', 'USD', 'JPY'], inline=inl_conv, group=g_fill)
line_input = 1  //input(1, title="Line width", type=input.integer,inline=inl_fill )

g_multi = 'Multi Settings'
inl_multi = 'multi'
show_current = input.bool(title='Show Current', defval=false, inline=inl_multi, group=g_multi)
show_multi = input.bool(title='Show Multi', defval=false, inline=inl_multi, group=g_multi)
resCustom = input.timeframe(title='Timeframe', defval='10', group=g_multi) // chart
//candle_close = input.timeframe(title='Candle Close', defval='', group=g_multi)

//useCurrentRes = input(true, title="Chart Resolution?",inline=inl_multi,group=g_multi)



// Lines and Angles
ma_types(len) =>
    obj = 0.0
    this_src = close
    length = len
    if i_ma_multiple 
        length := i_ma_multi_value * len 
    if ma_type == 'sma' // Simple Moving Average
        obj := ta.sma(this_src,length)
    if ma_type == 'ema' // Exponential
        obj := ta.ema(this_src,length)
    if ma_type=="dema" // Double Exponential
        e = ta.ema(this_src, length)
        obj := 2 * e - ta.ema(e, length)
    if ma_type == 'tema' // Triple Exponential
        ema1 = ta.ema(this_src, length)
        ema2 = ta.ema(ema1, length)
        ema3 = ta.ema(ema2, length)
        obj := 3 * (ema1 - ema2) + ema3
    if ma_type == 'wma' // Weighted
        obj := ta.wma(this_src,length)
    if ma_type == 'vwma' // Volume Weighted
        obj := ta.vwma(this_src,length)
    if ma_type=="smma" // Smoothed
        w = ta.wma(this_src, length)
        obj := na(w[1]) ? ta.sma(this_src, length) : (w[1] * (length - 1) + this_src) / length
    if ma_type == "rma"
        obj := ta.rma(this_src, length)
    if ma_type == 'hma' // Hull
        obj := ta.wma(2*ta.wma(this_src, length/2)-ta.wma(this_src, length), math.floor(math.sqrt(length) ))
    if ma_type=="lsma" // Least Squares
        obj := ta.linreg(this_src, length, 0)
    if ma_type=="McGinley"
        mg = 0.0
        mg := na(mg[1]) ? ta.ema(this_src, length) : mg[1] + (this_src - mg[1]) / (length * math.pow(this_src/mg[1], 4))
        obj :=mg

    // if i_use_smooth
    //     ma := ta.sma(ma,i_smooth)

    obj

ma_angles(obj) =>
    this_ma = obj
    if i_use_smooth
        this_ma := ta.sma(this_ma,i_smooth)

    ma_angle = angle(this_ma,i_use_angle ? i_angle : 1)

    ma_angle

m1   = request.security(syminfo.tickerid, resCustom, ma_types(m_len1) )
m1_a = request.security(syminfo.tickerid, resCustom, ma_angles(m1) )
m2   = request.security(syminfo.tickerid, resCustom, ma_types(m_len2) )
m2_a = request.security(syminfo.tickerid, resCustom, ma_angles(m2) )
m3   = request.security(syminfo.tickerid, resCustom, ma_types(m_len3) )
m3_a = request.security(syminfo.tickerid, resCustom, ma_angles(m3) )
m4   = request.security(syminfo.tickerid, resCustom, ma_types(m_len4) )
m4_a = request.security(syminfo.tickerid, resCustom, ma_angles(m4) )
m5   = request.security(syminfo.tickerid, resCustom, ma_types(m_len5) )
m5_a = request.security(syminfo.tickerid, resCustom, ma_angles(m5) )
m6   = request.security(syminfo.tickerid, resCustom, ma_types(m_len6) )
m6_a = request.security(syminfo.tickerid, resCustom, ma_angles(m6) )
m7   = request.security(syminfo.tickerid, resCustom, ma_types(m_len7) )
m7_a = request.security(syminfo.tickerid, resCustom, ma_angles(m7) )
m8   = request.security(syminfo.tickerid, resCustom, ma_types(m_len8) )
m8_a = request.security(syminfo.tickerid, resCustom, ma_angles(m8) )


// Diff and Convergence
ma_conv(t1, t2) =>
    
    float boost = switch syminfo.ticker
        "GBPUSD" => 10000
        "EURUSD" => 10000
        "GBPJPY" => 100
        "BTCUSD" => 0.1
        "NAS100" => 1
        "NDQ100" => 1
        "GOLD"   => 10
        "SILVER" => 100
        => 1

    diff = (t1 - t2) * boost
    conv = show_conv and diff < conv_amount and diff > conv_amount * -1 ? true : false
    [diff, conv]

[m2_m4_diff, m2_m4_conv] = ma_conv(m2, m4)
[m4_m5_diff, m4_m5_conv] = ma_conv(m4, m5)
[m5_m6_diff, m5_m6_conv] = ma_conv(m5, m6)
[m7_m8_diff, m7_m8_conv] = ma_conv(m7, m8)


// Plot Angles
//plot(show_m1 ? m1_a : na, color=m1_a>0?color.new(green,100):color.new(red,100), title='M1 A', linewidth=0)
plot(show_m2 ? m2_a: na, title='M2 A', color=m2_a> 0 ? color.new(red, 0) : color.new(lime, 0))
//m8_f_a = plot(show_m8 ? m8_a : na, title='m8 Multi A', color=m8_a > 0 ? color.new(red, 100) : color.new(lime, 100))

// Plot
m1_f = plot(show_current and show_m1 ? m1: na, color=m1_a>0?green:red, title='M1', linewidth=line_input)
m2_f = plot(show_current and show_m2 ? m2: na, color=color.new(white, 0), title='M2', linewidth=2)
m3_f = plot(show_current and show_m3 ? m3: na, color=color.new(blue, 0), title='M3')
m4_f = plot(show_current and show_m4 ? m4: na, color=m4_a > 0 ? yellow : orange, title='M4', linewidth=line_input)
m5_f = plot(show_current and show_m5 ? m5: na, title='M5', linewidth=line_input, color=m5_a > 0 ? orange : m5_a < 0 and m5 > m6 ? red : green)
m6_f = plot(show_current and show_m6 ? m6: na, title='M6', linewidth=line_input, color=m6_a > 0 ? red : lime)
m7_f = plot(show_current and show_m7 ? m7: na, title='M7', linewidth=line_input, color=m7_a > 0 ? orange : m7_a < 0 and m7 > m8 ? red : green)
m8_f = plot(show_current and show_m8 ? m8: na, title='M8', linewidth=line_input, color=m8_a > 0 ? red : lime)



// Fills
fill(m5_f, m6_f, title='m5/m6 Fill Multi', color=show_fill and m5_m6_diff < 1 ? color.new(green, 90) : show_fill ? color.new(red, 90) : na)
fill(m7_f, m8_f, title='m7/m8 Fill Multi', color=show_fill and m7_m8_diff < 1 ? color.new(green, 90) : show_fill ? color.new(red, 90) : na)
// Conv
fill(m2_f, m4_f, title='m2/m4 Conv Multi', color=m2_m4_conv and m2 > m4 ? color.new(red, 70) : m2_m4_conv and m2 < m4 ? color.new(green, 70) : na)
fill(m5_f, m6_f, title='m5/m6 Conv Multi', color=m5_m6_conv and m5 > m6 ? color.new(red, 70) : m5_m6_conv and m5 < m6 ? color.new(green, 70) : na)
fill(m7_f, m8_f, title='m7/m8 Conv Multi', color=m7_m8_conv and m7 > m8 ? color.new(red, 70) : m7_m8_conv and m7 < m8 ? color.new(green, 70) : na)
plot(m5_m6_diff, title='m5/m6 Diff Multi', linewidth=0, color=color.new(blue, 100))

// Previous State
var state_change = blue
ss_red2 = m5 > m6 and m5_a > 0
ss_orange2 = m5 > m6 and m5_a < 0
ss_lime2 = m5 < m6 and m6_a > 0
ss_green2 = m5 < m6 and m6_a < 0
m5_m6_c = ss_red2 ? red : ss_orange2 ? orange : ss_lime2 ? lime : ss_green2 ? green : na




// ===  Bollinger Bands ===
// ==================================================
// 
BB_length = 20 //input(250, title='BB Len')
basis = ta.sma(close, BB_length)
dev = 2 * ta.stdev(close, BB_length)
bb_upper = basis + dev
bb_lower = basis - dev
bb_spread = bb_upper - bb_lower
//avgspread = sma(bb_spread, sqz_length)
// plot(basis,title="Basis")
// plot(bb_upper,title="bb_upper")
// plot(bb_lower,title="bb_lower")
bb_cond = m1 < bb_lower ? 1 : m1 > bb_upper ? -1 : na
barcolor(bb_cond == 1 ? aqua : bb_cond == -1 ? orange : na)




g_trading = 'Trading ----------------------------------------------------'
i_equity = input.string("Initial", options=["Initial", "Equity"], title="Initial or Equity",group=g_trading)
inl_stoploss = 'inl-stoploss'
inl_sessions = 'inl-sessions'
account_size = input.int(50000, minval=1000, title='Account Size',group=g_trading)
show_st = input.bool(title='Show Stop Loss', defval=false, group=g_trading)
atr_mult = input.float(0.85, 'ATR Mult', step=0.01, group=g_trading, inline=inl_stoploss) // 1.5
//atr_src = input.string('close', title='Close or Wicks', options=['close', 'wicks'],group=g_trading, inline=inl_stoploss)


// Sessions
show_sessions = input.bool(false,title='Sessions', group=g_trading)
As = "1800-0300" //input.session(title="Asia", defval="1800-0300")
Lon = "0300-1200" //input.session(title="London", defval="0300-1200")
Ny = "0800-1700" //input.session(title="New York", defval="0800-1800")
Dz = "1715-2030" //input.session(title="Deadzone", defval="1715-2030")


inl_color = "inl_color"
inl_color2 = "inl_color2"
c1_on = false //input.bool(true,title="", group=g_trading,inline=inl_color)
c1 = color.new(#00bcd4,90) //input.color(title="Asia", defval=#00bcd4,group=g_trading,inline=inl_color)
c2_on = true //input.bool(true,title="",group=g_trading,inline=inl_color)
c2 = color.new(#00796b,90) //input.color(title="London", defval=#00796b,group=g_trading,inline=inl_color)
c3_on = true //input.bool(true,title="",group=g_trading,inline=inl_color2)
c3 = color.new(#b71c1c,90) //input.color(title="New York", defval=#b71c1c,group=g_trading,inline=inl_color2)
c4_on = false //input.bool(true,title="", group=g_trading,inline=inl_color2)
c4 = color.new(color.purple,90) //input.color(title="Deadzone", defval=#b71c1c,group=g_trading,inline=inl_color2)

Session(sess) => na(time("2",sess)) == false
Asia = Session(As) and c1_on and show_sessions? c1 : na
London = Session(Lon) and c2_on and show_sessions ? c2 : na
NewYork = Session(Ny) and c3_on and show_sessions ? c3 : na
Deadzone = Session(Dz) and c4_on and show_sessions ? c4 : na
bgcolor(Asia)
bgcolor(London)
bgcolor(NewYork)
//bgcolor(Deadzone)



// var int flag_d = na
// flag_d := m2_a<-10 and m2_a<m2_a[1] ? -1 : flag_d==-1 and m2_a>m2_a[1] ? 0 : flag_d==-1 and m2_a<0 ? -1 : na
// plotshape(flag_d==0?1:na,title="Flag Down",color=green ,style=shape.circle,location=location.bottom)
trade_dir() =>
    candle      = close>open ? 1 : 0
    dir         = 0
    entryLong   = 0
    entryShort  = 0
    exitLong    = 0
    exitShort   = 0
    closeAll    = 0
    longSL      = 0.0

    red_s       = m5>m6
    green_s     = m5<m6
    green_conv  = m5_m6_conv==1 and green_s  

    // Uptrend
    if m7>m8 and m8_a>0

        // Enter
        // and not(m7_a<0 and m1_a<m2_a)
        if clrToUse== red and m1<m2 and candle==0 and close<close[1]
         and not(bbUpperBand>70)
         and not(m7_a<0)
         //and not(m1_a<-15)
         //and not(m4_a<-2)
         and not(close<m7)
         //and not(close<m4 and m1_a<m2_a)
            entryLong := 1

        // if rsi<30 and candle==0
        //     entryLong := 1

        // if clrToUse== red and candle==0 and green_s and m5_a>0 and m1<m2
        //     entryLong := 1

        // if close<m8 and candle==0 and clrToUse== red
        //     entryLong := 1

        // if rsi_down and flag_d==0
        //     entryLong := 1

        // if m2_a<0 and m2_a>m2_a[1] and clrToUse== red and candle==0 and close<m6
        //     entryLong := 1

        // Exits
        if clrToUse==green and close>m2 and m1>m2 and candle==1
         and not(m2_a<0)
         and not(m2<m8 or m2<m5)
         and not(m1_a<m2_a)
         and not(m1_a<15)
         //and not(m2_m4_conv)
        //  and not(green_s)
        //  and not(m4<m5)
            exitLong := 1
            //entryShort := 1



        // Filters
        // if m7_a<0
        //     closeAll := 1

        // Reversals
        // if m4_a<0 and m4>m5 and m1_a>m4_a
        //     entryShort := 1

        // if green_s and close>m6
        //     entryShort := 1
            
    // Downtrend
    if m7<m8 and m8_a<0
        // Entry
        if rsi_up and clrToUse== green and candle==1
         and not(red_s)
            entryShort := 1

        // Exit
        if rsi_down and clrToUse== red and candle==0
         and not(m2_m4_conv and m1>m4)
            exitShort := 1 


    [entryLong,entryShort,exitLong,exitShort,closeAll]

[entryLong,entryShort,exitLong,exitShort,closeAll] = trade_dir()

// check if live trading or market closed
//plotshape(barstate.islastconfirmedhistory, title='Real Time', color=red, style=shape.circle, location=location.top)


// STOP LOSS
g_sl = 'Stop Loss ----------------------------------------------------'
i_tpFactor  = input(1.5, 'Target Profit')
Multip      = input.float(defval=1.5, title='Stop Loss',group=g_sl)
i_pctStop   = input(1.0, '% of Risk to Starting Equity Use to Size Positions') / 100
atr_src     = input.string('wicks', title='Close or Wicks', options=['close', 'wicks'],group=g_sl)
i_sl_type   = input.string("Lowest", title="SL Type", options=["Lowest","ATR"],group=g_sl)
i_bkcandles = input.int(11, title="Lowerest range - Number of candles",group=g_sl)
show_sl     = input.bool(false,title="Stop Loss",group=g_sl)
show_ts     = input.bool(false,title="Trailing Stop",group=g_sl)

stop_loss()=>

    float sl_short  = na
    float shortDiff = na
    float shortTP   = na
    float sl_long   = na
    float longDiff  = na
    float longTP    = na
    float longTS    = na
    float pValue    = na
    float pSize    = na

    if i_sl_type == "ATR"
        atr_len = 14
        ATR = ta.atr(atr_len)
        // Short
        sl_short    := (atr_src =='close' ? close : high) + ATR * Multip
        shortDiff   := math.abs(close - sl_short)
        shortTP     := close - (i_tpFactor * shortDiff)
        // Long
        sl_long     := (atr_src =='close' ? close : low) - ATR * Multip
        longDiff    := math.abs(close - sl_long)
        longTP      := close + (i_tpFactor * longDiff)
        longTS      := close + (1.5 * longDiff)
        pValue      := (i_equity=="Initial" ? strategy.initial_capital : strategy.equity) * i_pctStop / (longDiff / close)
        pSize       := pValue / close

    if i_sl_type == "Lowest"
        sl_short    := ta.highest(high, i_bkcandles)[1]
        sl_long     := ta.lowest(low, i_bkcandles)[1]
        longDiff  := math.abs(close - sl_long)
        longTP      := close + (i_tpFactor * longDiff)

    [sl_short,sl_long, shortTP, longTP, close, close,longTS,pSize]

float shortSL = 0.0
float longSL  = 0.0
float closeSL = 0.0
float ratio_l = 0.0
[atr_short, atr_long, shortTP, longTP, long_close, short_close, longTS, pSize] = stop_loss()

shortSL     := entryShort and strategy.opentrades == 0 ? atr_short : strategy.opentrades > 0 ? shortSL[1] : 0
shortTP     := entryShort and strategy.opentrades == 0 ? shortTP : strategy.opentrades > 0 ? shortTP[1] : 0
short_close  := entryShort and strategy.opentrades == 0 ? short_close : strategy.opentrades > 0 ? short_close[1] : 0
p_s_sl      = plot( strategy.opentrades > 0 ? shortSL : na, title="Short SL", color=color.new(red,75), linewidth=1, style=plot.style_linebr)
p_s_tp      = plot( strategy.opentrades > 0 ? shortTP : na, title="Short TP", color=color.new(green,75), linewidth=1, style=plot.style_linebr)
p_o_tp      = plot( strategy.opentrades > 0 ? short_close : na, title="Short Open", color=color.new(green,75), linewidth=1, style=plot.style_linebr)
fill(p_s_sl, p_o_tp,color=color.new(red,75) )
fill(p_s_tp, p_o_tp,color=color.new(green,75) )


longSL      := entryLong and strategy.opentrades == 0 ? atr_long : strategy.opentrades > 0 ? longSL[1] : 0
longTP      := entryLong and strategy.opentrades == 0 ? longTP : strategy.opentrades > 0 ? longTP[1] : 0
long_close := entryLong and strategy.opentrades == 0 ? long_close : strategy.opentrades > 0 ? long_close[1] : 0
ratio_l     := ( close  - long_close )
p_l_sl      = plot( strategy.opentrades > 0 ? longSL : na, title="long SL", color=color.new(red,75), linewidth=1, style=plot.style_linebr)
p_l_tp      = plot( strategy.opentrades > 0 ? longTP : na, title="long TP", color=color.new(green,75), linewidth=1, style=plot.style_linebr)
p_c_sl      = plot( strategy.opentrades > 0 ? long_close : na, title="Close SL", color=color.new(red,75), linewidth=1, style=plot.style_linebr)
//p_l_ratio   = plot( ratio_l, title="Ratio", color=color.new(red,75), linewidth=1, style=plot.style_linebr)
fill(p_l_sl,p_c_sl,color=color.new(red,75))
fill(p_l_tp,p_c_sl,color=color.new(green,75))

// ATR
plot(show_sl? atr_long  : na,"ATR + ", color=green)
plot(show_sl? atr_short : na,"ATR - ", color=red)


// Trailing Stop
take_profit()=>
    longDiffSL = math.abs(close - longSL)
    positionValue = (i_equity=="Initial" ? strategy.initial_capital : strategy.equity) * i_pctStop / (longDiffSL / close)
    positionSize = positionValue / close

    [longDiffSL,positionSize]

//[longDiffSL,positionSize] = take_profit()
//longDiffSL = math.abs(closeSL - longSL) 
//longTP := entryShort and strategy.opentrades == 0 ? close + (i_tpFactor * longDiffSL) : strategy.opentrades > 0 ? longTP[1] : 0

//p_long_tp = plot(longDiffSL, title="Take Profit", color=yellow,linewidth=1, style=plot.style_linebr)



// TAKE PROFIT
// var float longTP = 0.0




// longSL := entryLong and strategy.opentrades == 0 ? stop_loss() : longSL[1]

// plot(longSL,title="Long SL",color=red)

//sl_mid = (Shortstop + Longstop) * 0.5
//sl_smoothed = ta.sma(sl_mid,10)
// plot(show_sl ? Shortstop : na, title="Stop Short")
// plot(show_sl ? sl_smoothed : na, title="SL Mid")
// plot(show_sl ? Longstop : na, title="Long Short")



cd=close>open?1:0
// Long
if (entryLong)
	strategy.entry("L", strategy.long, comment="L")
    strategy.exit('EXIT L', 'L', stop=longSL)
else
	strategy.cancel("S")

if (exitLong)
	strategy.close("L", comment = "Close L")

// Special case to handle fake outs
if (m8_a<0 )
    strategy.close("L", comment = "Exit L")

// Short
if (entryShort)
	strategy.entry("S", strategy.short,stop= cd==1 ?high+(syminfo.mintick) : low-(syminfo.mintick), comment="S")
    strategy.exit('EXIT S', 'S', stop=shortSL)
else
	strategy.cancel("S")

if (exitShort)
	strategy.close("S", comment = "Exit S")

// longPlotSL = strategy.opentrades > 0 and strategy.position_size > 0 ? longSL : na
// plot(longPlotSL, title='LONG STOP LOSS', linewidth=2, style=plot.style_linebr, color=color.new(color.red, 0))


// PLOT TAKE PROFIT
// longPlotTP = strategy.opentrades > 0 and strategy.position_size > 0 ? longTP : na
// plot(longPlotTP, title='LONG TAKE PROFIT', linewidth=2, style=plot.style_linebr, color=color.new(color.green, 0))

// strategy.close(id="S", when=exitLong)
// strategy.close(id="B", when=exitShort)


// plotshape(direction==-1, title='Sell 1', color=red, style=shape.circle, location=location.top)
// plotshape(direction==1, title='Buy', color=lime, style=shape.circle, location=location.bottom)
// plot(direction==-1?1:na, title="Flag Up")
// plot(direction==1?1:na, title="Flag Down")

// if ta.change(close_m)
//     m1_a_text = label.new(x=bar_index, y=close,yloc=yloc.abovebar, style=label.style_label_center, color=color.white,textcolor=color.red, size=size.normal,text=str.tostring(m1_a, '#.##') )

// === Labels ===
// label_txt = "Equity: " + str.tostring(account_size)
// if barstate.islastconfirmedhistory
//     buy = label.new(x=bar_index + 20, y=close, style=label.style_label_left,color=color.blue, textcolor=color.white, size=size.normal,
//      text="Exchange: " + str.tostring(syminfo.prefix)+ "\nSymbol: " + str.tostring(syminfo.ticker) )


