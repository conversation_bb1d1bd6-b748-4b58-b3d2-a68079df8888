
//@version=4
study(title = "S3 angle", shorttitle="S3 angle")

angle(_src, len) =>
    rad2degree=180/3.14159265359  //pi 
    ang=rad2degree*atan((_src[0] - _src[1])/atr(len))
    

ssl_len1 = input(8, minval=1,title="SSL 3") // 8 7
ssl_len3 = input(15, minval=1,title="SSL 3 B") // 8 7
ssl_len5 = input(150, minval=1,title="SSL 5") //7
a_degree = input(3, minval=1,title="Angle")

// 8
s1 = wma(2*wma(close, ssl_len1/2)-wma(close, ssl_len1), round(sqrt(ssl_len1)))
s1_a = angle(s1,a_degree)

// 15
s3 = wma(2*wma(close, ssl_len3/2)-wma(close, ssl_len3), round(sqrt(ssl_len3)))
s3_a = angle(s3,a_degree)

// s5 150
s5 = wma(2*wma(close, ssl_len5/2)-wma(close, ssl_len5), round(sqrt(ssl_len5)))
s5_a = angle(s5,2)


plot(s1_a, title="SSL angle 3",color=#ffaa00)
plot(s3_a, title="SSL angle 3B")
plot(s5_a, title="SSL angle 5",color=s5_a<s5_a[1]?#00ff00:#ff0000)
h1 = hline(0)
hline(23,color=color.new(#ff0000,50))
hline(-23,color=color.new(#00ff00,50))