//@version=4
study("RES - Multi", shorttitle="RES - Multi", overlay=false, precision=2)

d_blue = #0053ff
aqua = #00c3ff
gray = #707070

g_res = "RES - Ranging EMA Spread ---------------------------------------------------"
inl_res1 = "inl_res1"
ema1length = input(12, minval=1, title="EMA 1") // 40 12
ema2length = input(50, minval=1, title="EMA 2") // 100 43 50
ranginglength = input(3, minval=1, maxval=5, type=input.integer, title="Ranging Threshold")
rangingmaxvalue = input(0.12, step=0.01, type=input.float, title="Max Value") // 0.1
rangingminvalue = input(-0.1,step=0.01, type=input.float, title="Min Value") // -0.1
show_res = input(title="Show RES", type=input.bool, defval=false,group=g_res) 
res_timeframe = input(title="Timeframe", type=input.resolution, defval="15",group=g_res)
candle_close = input(title="Candle Close", type=input.resolution, defval="15",group=g_res)

// Rangin EMA spread
res_func() =>
    ema1 = ema(close, ema1length)
    ema2 = ema(close, ema2length)
    spread = ((ema2 / ema1) -1) * 100
    [spread]

//[spread] = res_func()
close_m  = security(syminfo.tickerid, candle_close, close)
[spread] = security(syminfo.tickerid, res_timeframe, res_func() )

r1 = (spread >= rangingminvalue and spread <= rangingmaxvalue) 
r2 = (spread[1] >= rangingminvalue and spread[1] <= rangingmaxvalue)
r3 = (spread[2] >= rangingminvalue and spread[2] <= rangingmaxvalue)
r4 = (spread[3] >= rangingminvalue and spread[3] <= rangingmaxvalue)
r5 = (spread[4] >= rangingminvalue and spread[4] <= rangingmaxvalue)

ranging = false
if (ranginglength == 1) 
    ranging := r1
if (ranginglength == 2) 
    ranging := r1 and r2
if (ranginglength == 3) 
    ranging := r1 and r2 and r3
if (ranginglength == 4) 
    ranging := r1 and r2 and r3 and r4
if (ranginglength == 5) 
    ranging := r1 and r2 and r3 and r4 and r5

res = spread > 0 ? 0-spread : abs(spread)
res := res * 100
res_weak = gray // grey
res_mid = d_blue // navy
res_strong = aqua // aqua
res_color = ranging ? res_weak : spread > spread[1] ? res_mid : res_strong
// Targets
res_top = 15
res_high = 6
res_low = -6
res_bottom = -15

plotshape(change(close_m),title="Close Multi",color=#ff0000,style=shape.triangleup,location=location.top)
plot(res, title="RES", color=res_color, style=plot.style_columns)

close_color = change(close_m) ? res_color : res_color[1]
barcolor(close_color, title="RES")