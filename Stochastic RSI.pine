//@version=5
indicator(title="Stoch RSI - Multi", format=format.price, precision=6)
stoch_rsi_time = input.timeframe(title='Timeframe', defval='')
smoothK = input.int(3, "K", minval=1)
smoothD = input.int(3, "D", minval=1)
lengthRSI = input.int(14, "RSI Length", minval=1)
lengthStoch = input.int(14, "Stochastic Length", minval=1)
stoch_src = input(close, title="RSI Source")
i_stoch_smooth = input.bool(true, title="Use Smoothing")
i_sto_smooth_amount = input.int(3, title="Smooth Amount")
i_show_angles = input.bool(false, title='Show angles')
i_gaps = input.bool(false, title="Use Gaps")

// Angle
angle(_src, len) =>
    rad2degree = 180 / 3.14159265359  //pi 
    ang = rad2degree * math.atan((_src[0] - _src[1]) / ta.atr(len))
    ang

stoch_rsi()=>
    stoch_rsi = ta.rsi(stoch_src, lengthRSI )
    stoch = ta.stoch(stoch_rsi, stoch_rsi, stoch_rsi, lengthStoch)
    k = ta.sma(stoch, smoothK)
    d = ta.sma(k, smoothD)

    if i_stoch_smooth
        stoch_rsi := ta.sma(stoch_rsi, i_sto_smooth_amount)
        stoch := ta.sma(stoch, i_sto_smooth_amount)
        k := ta.sma(k, i_sto_smooth_amount)
        d := ta.sma(d, i_sto_smooth_amount)

    [stoch_rsi,stoch,k,d]

[stoch_rsi,stoch,k,d] = request.security(syminfo.tickerid, stoch_rsi_time, stoch_rsi(), gaps= i_gaps ? barmerge.gaps_on: barmerge.gaps_off) 

stoch_diff = math.abs(k - d)
stoch_angle = request.security(syminfo.tickerid, stoch_rsi_time, angle(stoch,1), lookahead=barmerge.lookahead_on ) 
stoch_rsi_angle = request.security(syminfo.tickerid, stoch_rsi_time, angle(stoch_rsi,1), lookahead=barmerge.lookahead_on ) 

stoch_ch = ta.change(bar_index % str.tonumber(stoch_rsi_time) == 0 ) ? 1 : 0
stoch_rsi := stoch_ch ? stoch_rsi : stoch_rsi[1]
stoch := stoch_ch ? stoch : stoch[1]
k := stoch_ch ? k : k[1]
d := stoch_ch ? d : d[1]

h0 = hline(80, "Upper Band", color=#787B86)
hline(70, "Lower Band", color=#787B86)
h2 = hline(50, "Lower Band", color=#787B86)
hline(30, "Lower Band", color=#787B86)
h1 = hline(20, "Lower Band", color=#787B86)
fill(h0, h1, color=color.rgb(33, 150, 243, 90), title="Background")

//plot(stoch_diff, "Difference", color=color.new(color.yellow, 100))
plot(i_show_angles ? stoch_diff : na, "Diff", color=color.new(color.blue, 100))
plot(i_show_angles ? stoch_rsi_angle : na, "Angle K", color=color.new(color.blue, 100))
plot(stoch_rsi, "RSI", color=color.new(color.yellow, 0))
plot(stoch, "Stoch RSI", color=color.new(color.green, 0))
plot(k, "K", color=#2962FF)
plot(d, "D", color=#FF6D00)

// c_up = (k>80 and d>80) and (k<d)
// plotshape(c_up,title="Up",color=#ff0000,style=shape.circle,location=location.top)
// c_down = (k<20 and d<20) and (k>d)
// plotshape(c_down,title="Down",color=#00ff00,style=shape.circle,location=location.bottom)
// c_diff = diff<1 or diff==0
// plotshape(c_diff,title="Diff",color=#ffff00,style=shape.circle)

// var float state = 0
// state := (k>80 and d>80) and (k<d) ? -1 : 1
// plot(state, title="State", color=state==1?color.new(#ff0000,100):color.new(#00ff00,100))