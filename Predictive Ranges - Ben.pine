// This work is licensed under a Attribution-NonCommercial-ShareAlike 4.0 International (CC BY-NC-SA 4.0) https://creativecommons.org/licenses/by-nc-sa/4.0/
// © LuxAlgo

//@version=5
indicator("Predictive Ranges - Ben", overlay = true)

red = #ff0000
green = #00ff00

// Calculate Multi Timeframes
newbar(res) => ta.change(time(res)) == 0 ? 0 : 1

//
// ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ //
// PREDICTIVE RANGES
// ------------------------------------------------------------------------------------------------------------------
PR_source      = input.source(close, "Source")
g_PR = 'PREDICTIVE RANGES ------------------------------------------------------'
PR_show_1      = input.bool(false, title='Show Channels',group=g_PR)
PR_attr_1      = input.bool(false, title='Show Attributes',group=g_PR)
PR_tf1         = input.timeframe('5', 'Timeframe',group=g_PR)
PR_len1        = input.int(215, 'Length', minval = 2,group=g_PR)
PR_mult1       = input.float(6.5, 'Factor', minval = 0, step = .5,group=g_PR) // 6.0 , 10.0
PR_s_cand1     = input.bool(false, title='Show Candles',group=g_PR)
PR_trans1      = input.int(70, title='Trans',group=g_PR)

g_PR2 = 'PR 2 ------------------------------------------------------'
PR_show_2      = input.bool(true, title='Show Channels',group=g_PR2)
PR_attr_2      = input.bool(false, title='Show Attributes',group=g_PR2)
PR_tf2         = input.timeframe('15', 'Timeframe',group=g_PR2)
PR_len2        = input.int(200, 'Length', minval = 2,group=g_PR2)
PR_mult2       = input.float(6., 'Factor', minval = 0, step = .5,group=g_PR2) // 6.0 , 10.0
PR_s_cand2     = input.bool(false, title='Show Candles',group=g_PR2)
PR_trans2      = input.int(70, title='Trans',group=g_PR2)

g_PR3 = 'PR 3 ------------------------------------------------------'
PR_show_3      = input.bool(false, title='Show Channels',group=g_PR3)
PR_attr_3      = input.bool(false, title='Show Attributes',group=g_PR3)
PR_tf3         = input.timeframe('', 'Timeframe',group=g_PR3)
PR_len3        = input.int(200, 'Length', minval = 2,group=g_PR3)
PR_mult3       = input.float(6., 'Factor', minval = 0, step = .5,group=g_PR3) // 6.0 , 10.0
PR_s_cand3     = input.bool(false, title='Show Candles',group=g_PR3)
PR_trans3      = input.int(95, title='Trans',group=g_PR3)

//-----------------------------------------------------------------------------}
//Function
//-----------------------------------------------------------------------------{
pred_ranges(PR_len, PR_mult)=>
    var R2 = 0.
    var R1 = 0.
    var S1 = 0.
    var S2 = 0.
    var avg = close
    var hold_atr = 0.

    atr = nz(ta.atr(PR_len)) * PR_mult
        
    avg := close - avg > atr ? avg + atr : 
      avg - close > atr ? avg - atr : 
      avg
        
    hold_atr := avg != avg[1] ? atr / 2 : hold_atr

    R2 := avg + hold_atr * 2
    R1 := avg + hold_atr
    S1 := avg - hold_atr
    S2 := avg - hold_atr * 2

    //l38  = (R2-S2) * 0.382 + S2
    //l62  = (R2-S2) * 0.618 + S2
        
    [R2, 
     R1, 
     avg, 
     S1, 
     S2]

//-----------------------------------------------------------------------------}
//PR 1
//-----------------------------------------------------------------------------{
[PR_R2_1
  , PR_R1_1
  , PR_avg_1
  , PR_S1_1
  , PR_S2_1] = request.security(syminfo.tickerid, PR_tf1, pred_ranges(PR_len1, PR_mult1))

// Color
PR_col1 = 
 close>PR_R1_1 ? color.red
 : low>PR_avg_1 and close<PR_R1_1 ? color.yellow 
 : high<PR_avg_1 and close>PR_S1_1 ? color.blue 
 : high<PR_S1_1 ? color.green
 : na


if newbar(PR_tf1) == 0
    PR_R2_1 := PR_R2_1[1]
    PR_R1_1 := PR_R1_1[1]
    PR_S1_1 := PR_S1_1[1]
    PR_S2_1 := PR_S2_1[1]
    PR_avg_1:= PR_avg_1[1]

// Bars Since Change
pr1_bars = ta.barssince(ta.change(PR_R2_1))

var pr1_count_up = 0
var pr1_count_dn = 0

if pr1_bars == 0
    pr1_count_up := 0
    pr1_count_dn := 0

if pr1_bars != 0

    if high > PR_R1_1 and high[1] < PR_R1_1
        pr1_count_up := pr1_count_up + 1

    if low < PR_S1_1 and low[1] > PR_S1_1
        pr1_count_dn := pr1_count_dn + 1

// Plots
p_PR_R2_1  = plot(PR_show_1 ? PR_R2_1: na, 'PR Upper 2', PR_avg_1 != PR_avg_1[1] ? na : #f23645)
p_PR_R1_1  = plot(PR_show_1 ? PR_R1_1: na, 'PR Upper 1', PR_avg_1 != PR_avg_1[1] ? na : #f23645)
p_PR_avg_1 = plot(PR_show_1 ? PR_avg_1: na , 'PR Average', PR_avg_1 != PR_avg_1[1] ? na : #5b9cf6)
p_PR_S1_1  = plot(PR_show_1 ? PR_S1_1: na, 'PR Lower 1', PR_avg_1 != PR_avg_1[1] ? na : #089981)
p_PR_S2_1  = plot(PR_show_1 ? PR_S2_1: na, 'PR Lower 2', PR_avg_1 != PR_avg_1[1] ? na : #089981)
//Fills
fill(p_PR_R2_1, p_PR_R1_1, PR_avg_1 != PR_avg_1[1] ? na : color.new(#f23645, PR_trans1))
fill(p_PR_S1_1, p_PR_S2_1, PR_avg_1 != PR_avg_1[1] ? na : color.new(#089981, PR_trans1))
// Bar since
plot_bars1 = plot(PR_show_1 ? pr1_bars: na, 'Bars Since', color=color.new(color.blue,100))
barcolor(PR_s_cand1 ? PR_col1 : na)

PR1_change = ta.change(PR_R2_1)
var float PR1_dir = 0
PR1_dir := ta.change(PR_R2_1) and PR_R2_1 < PR_R2_1[10] ? -1 : ta.change(PR_R2_1) and PR_R2_1 > PR_R2_1[10] ? 1 : PR1_dir

//plot(PR1_dir, 'PR State', color= PR1_dir==1 ? green : PR1_dir==-1 ? red : na) 
plotshape(PR_show_1 and PR_attr_1 and PR1_dir == -1 ? 1 : na, title='PR Change Down', color=red, style=shape.cross, location=location.top)
plotshape(PR_show_1 and PR_attr_1 and PR1_dir == 1 ? 1 : na, title='PR Change Up', color=green, style=shape.cross, location=location.bottom)

// Number of Hits
plot(pr1_count_up, 'Hits Up', color=color.new(red,100))
plot(pr1_count_dn, 'Hits Down', color=color.new(green,100))

//-----------------------------------------------------------------------------}
//PR 2
//-----------------------------------------------------------------------------{
[PR_R2_2
  , PR_R1_2
  , PR_avg_2
  , PR_S1_2
  , PR_S2_2] = request.security(syminfo.tickerid, PR_tf2, pred_ranges(PR_len2, PR_mult2))

// Color
PR_col2 = 
 close>PR_R1_2 ? color.red
 : low>PR_avg_2 and close<PR_R1_2 ? color.yellow 
 : high<PR_avg_2 and close>PR_S1_2 ? color.blue 
 : high<PR_S1_2 ? color.green
 : na

// Bars Since Change
pr_bars2 = ta.barssince(ta.change(PR_R2_2))

if newbar(PR_tf2) == 0
    PR_R2_2 := PR_R2_2[1]
    PR_R1_2 := PR_R1_2[1]
    PR_S1_2 := PR_S1_2[1]
    PR_S2_2 := PR_S2_2[1]
    PR_avg_2:= PR_avg_2[1]

// Direction
var float PR2_dir = 0
PR2_dir := ta.change(PR_R2_2) and PR_R2_2 < PR_R2_2[10] ? -1 : ta.change(PR_R2_2) and PR_R2_2 > PR_R2_2[10] ? 1 : PR2_dir
plotshape(PR_show_2 and PR_attr_2 and PR2_dir == -1 ? 1 : na, title='PR Change Down', color=red, style=shape.cross, location=location.top)
plotshape(PR_show_2 and PR_attr_2 and PR2_dir == 1 ? 1 : na, title='PR Change Up', color=green, style=shape.cross, location=location.bottom)

// Plots
//p_prR2_2_state  = plot( PR_dir_2 , 'PR 2 State', color=PR_dir_2==1?green:red)
p_prR2_2  = plot(PR_show_2 ? PR_R2_2: na, 'PR Upper 2', PR_avg_2 != PR_avg_2[1] ? na : #f23645)
p_prR1_2  = plot(PR_show_2 ? PR_R1_2: na, 'PR Upper 1', PR_avg_2 != PR_avg_2[1] ? na : #f23645)
p_pravg_2 = plot(PR_show_2 ? PR_avg_2: na , 'PR Average', PR_avg_2 != PR_avg_2[1] ? na : #5b9cf6)
p_prS1_2  = plot(PR_show_2 ? PR_S1_2: na, 'PR Lower 1', PR_avg_2 != PR_avg_2[1] ? na : #089981)
p_prS2_2  = plot(PR_show_2 ? PR_S2_2: na, 'PR Lower 2', PR_avg_2 != PR_avg_2[1] ? na : #089981)
//Fills
fill(p_prR2_2, p_prR1_2, PR_avg_2 != PR_avg_2[1] ? na : color.new(#f23645, PR_trans2))
fill(p_prS1_2, p_prS2_2, PR_avg_2 != PR_avg_2[1] ? na : color.new(#089981, PR_trans2))
// Bar since
plot_bars2 = plot(PR_show_2 ? pr_bars2: na, 'Bars Since', color=color.new(color.blue,100))
barcolor(PR_s_cand1 ? PR_col2 : na)
//-----------------------------------------------------------------------------}


//-----------------------------------------------------------------------------}
//PR 3
//-----------------------------------------------------------------------------{
[PR_R2_3
  , PR_R1_3
  , PR_avg_3
  , PR_S1_3
  , PR_S2_3] = request.security(syminfo.tickerid, PR_tf3, pred_ranges(PR_len3, PR_mult3))

// Previous
f_prev(curr) =>
  dir = curr == curr[1] ? 0 : curr > curr[1] ? 1 : -1


// var prev_box = 0.
// prev_box := ta.change(PR_R2_3) ? f_prev(PR_R2_3) : prev_box[1]
// plot(prev_box, 'Prev Box', color=prev_box==1?#089981:#f23645)

// Color
PR_col3 = 
 close>PR_R1_3 ? color.red
 : low>PR_avg_3 and close<PR_R1_3 ? color.yellow 
 : high<PR_avg_3 and close>PR_S1_3 ? color.blue 
 : high<PR_S1_3 ? color.green
 : na

// Bars Since Change
pr_bars3 = ta.barssince(ta.change(PR_R2_3))

if newbar(PR_tf3) == 0
    PR_R2_3 := PR_R2_3[1]
    PR_R1_3 := PR_R1_3[1]
    PR_S1_3 := PR_S1_3[1]
    PR_S2_3 := PR_S2_3[1]
    PR_avg_3:= PR_avg_3[1]

pr_onchange_3 = ta.change(PR_R2_3)
var float PR_state_3 = 0
PR_state_3 := ta.change(PR_R2_3) and PR_R2_3 < PR_R2_3[1] ? -1 : ta.change(PR_R2_3) and PR_R2_3 > PR_R2_3[1] ? 1 : PR_state_3

//plot(PR2_state, 'PR State', color= PR2_state==1 ? green : PR2_state==-1 ? red : na) 
plotshape(PR_attr_3 and pr_onchange_3 and PR_R2_3 < PR_R2_3[1], title='PR Change Down', color=red, style=shape.cross, location=location.top)
plotshape(PR_attr_3 and pr_onchange_3 and PR_R2_3 > PR_R2_3[1], title='PR Change Up', color=green, style=shape.cross, location=location.bottom)

// Plots
p_prR2_3  = plot(PR_show_3 ? PR_R2_3: na, 'PR Upper 2', PR_avg_3 != PR_avg_3[1] ? na : #f23645)
p_prR1_3  = plot(PR_show_3 ? PR_R1_3: na, 'PR Upper 1', PR_avg_3 != PR_avg_3[1] ? na : #f23645)
p_pravg_3 = plot(PR_show_3 ? PR_avg_3: na , 'PR Average', PR_avg_3 != PR_avg_3[1] ? na : #5b9cf6)
p_prS1_3  = plot(PR_show_3 ? PR_S1_3: na, 'PR Lower 1', PR_avg_3 != PR_avg_3[1] ? na : #089981)
p_prS2_3  = plot(PR_show_3 ? PR_S2_3: na, 'PR Lower 2', PR_avg_3 != PR_avg_3[1] ? na : #089981)
//Fills
fill(p_prR2_3, p_prR1_3, PR_avg_3 != PR_avg_3[1] ? na : color.new(#f23645, PR_trans3))
fill(p_prS1_3, p_prS2_3, PR_avg_3 != PR_avg_3[1] ? na : color.new(#089981, PR_trans3))
// Bar since
plot_bars3 = plot(PR_show_3 ? pr_bars3: na, 'Bars Since', color=color.new(color.blue,100))
barcolor(PR_s_cand3 ? PR_col3 : na)

//-----------------------------------------------------------------------------}