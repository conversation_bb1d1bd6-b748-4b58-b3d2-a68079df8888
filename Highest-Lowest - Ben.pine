//@version=5
indicator(title='Highest/Lowest - Ben', overlay=true, max_labels_count = 500)


red = #ff0062
orange = #ff9800
yellow = #FFFF00
green = #4caf50
lime = #50e600
aqua = #00bcd4
blue = #2962ff
dark_blue = #2962ff
violet = #814dff
white = #ffffff
gray = #707070
black = #000000

timeinrange(hl_res, sess) => not na(time(hl_res, sess, "America/New_York")) ? 1 : 0
//plot(timeinrange("1", "1300-1400"), color=color.red)


angle(_src, len) =>
    rad2degree = 180 / 3.14159265359  //pi 
    ang = rad2degree * math.atan((_src[0] - _src[1]) / ta.atr(len))
    ang

// SYM Info
min_tick = syminfo.mintick
var int decimals = int(math.log10(1/min_tick))
decimals := decimals == 2 ? 1 : decimals == 0 ? 1 : decimals
//plot(min_tick, title='Min Tick')
//plot(decimals, title='Decimals')

get_pip_distance(point1, point2) =>
    diff_points = math.abs( (point1 - point2) )
    pip_value = decimals<3 ? diff_points / syminfo.mintick / 100 : diff_points / syminfo.mintick / 10
    //pip_value = decimals>3 ? diff_points * (math.pow(10, decimals) * syminfo.mintick) : diff_points / syminfo.mintick / 10


newbar(res) => ta.change(time(res)) == 0 ? 0 : 1


g_hl1 = 'HIGHEST LOWEST -------------------------------------------------------------'
inl_hl1_1 = "inl_hl1_1"
i_use_hl1 = input.bool(true,title='Display HL1', group=g_hl1)
hl_res = input.timeframe(title='Resolution', defval='120', group=g_hl1)
hl_len1 = input(title='Length 1', defval=20, group=g_hl1)  // 20
hl1_l_width = input.int(1, 'Line width 1', group=g_hl1)
hl1_col1 = input.color(yellow, "", group=g_hl1, inline=inl_hl1_1)
hl1_col2 = input.color(white, "", group=g_hl1, inline=inl_hl1_1)
hl1_col3 = input.color(orange , "", group=g_hl1, inline=inl_hl1_1)
//hl1_col4 = input.color(blue, "", group=g_hl1, inline=inl_hl1_1)
hl1_fill1 = input.color(color.new(black,70), "Fill 1", group=g_hl1)

g_hl2 = 'HL 2 -------------------------------------------------------------'
inl_hl2_1 = "inl_hl2_1"
i_use_hl2 = input.bool(true,title='Display HL2', group=g_hl2)
hl_res2 = input.timeframe(title='Resolution 2', defval='240', group=g_hl2)
hl_len2 = input(title='Length 2', defval=20, group=g_hl2)  // 20
hl2_l_width = input.int(1, 'Line width 2', group=g_hl2)
hl2_col1 = input.color(red, "", group=g_hl2, inline=inl_hl2_1)
hl2_col2 = input.color(gray, "", group=g_hl2, inline=inl_hl2_1)
hl2_col3 = input.color(aqua, "", group=g_hl2, inline=inl_hl2_1)
//hl2_col4 = input.color(#00FFFF, "", group=g_hl2, inline=inl_hl2_1)
hl2_fill1 = input.color(color.new(black,70), "Fill 2", group=g_hl2)

g_hl3 = 'Group -------------------------------------------------------------'
i_show_fill = input.bool(false, title='Fill Boxes', group=g_hl3)
i_show_bars = input.bool(false,title='Show Bars', group=g_hl3)
i_show_labels = input.bool(false,title='Show Labels', group=g_hl3)
src_h = close // input(title='Source', defval=close, group=g_hl3)
src_l = close // input(title='Source', defval=close, group=g_hl3)
use_diff = input(title='Filter Diff', defval=true, group=g_hl3)
diff_range = input(10, title='FDiff Range', group=g_hl3)


// H1
// -----------------------------------------------------
var float hh = 0.0
var float ll = 0.0
hh := timeframe.change(hl_res) ? request.security(syminfo.tickerid, hl_res, ta.highest(src_h, hl_len1)) : hh[1]
ll := timeframe.change(hl_res) ? request.security(syminfo.tickerid, hl_res, ta.lowest(src_l, hl_len1)) : ll[1]
hl_mid = (hh + ll) * 0.5
hh_a = angle(hh, hl_len1)
ll_a = angle(ll, hl_len1)
hl_dist = get_pip_distance(hh,ll)
hl_diff = hh - ll
hh_bars = ta.barssince(ta.change(hh))
ll_bars = ta.barssince(ta.change(ll))

// Plot
h1_p1 = plot(i_use_hl1 ? hh : na, title='HH', linewidth=hl1_l_width, color=hl1_col1)
plot(i_use_hl1 ? hl_mid : na, title='HL Midline', linewidth=hl1_l_width,color=hl1_col2)
h1_p2 = plot(i_use_hl1 ? ll : na, title='LL', linewidth=hl1_l_width, color=hl1_col3)
fill(h1_p1, h1_p2,title='Fill H1', color = i_show_fill ? hl1_fill1 : na )
//fill(h1_p1, h1_p2,title='Fill H1', color= i_show_fill and ll>ll[1] ? color.new(green,50) : i_show_fill and ll<ll[1] ? color.new(red,50) : na )
plot(math.round(hl_dist),"Distance ", color=color.new(green,100) )
plot(i_use_hl1 ? hh_bars : na, 'Bars Since High', style=plot.style_circles)
plot(i_use_hl1 ? ll_bars : na, 'Bars Since Low', style=plot.style_circles)

// up_or_down1 = hh
// up_or_down1 := ta.change(hh>hh[1]) ? 1 : ta.change(hh<hh[1]) ? -1 : up_or_down1
// plotshape(i_use_hl1 and up_or_down1 == -1 ? 1 : 0, title='HH1 Down', color=color.new(color.red, 0), style=shape.circle, location=location.top)
// plotshape(i_use_hl1 and up_or_down1 == 1 ? 1 : 0, title='HH1 Up', color=color.new(color.green, 0), style=shape.circle, location=location.bottom)

if ta.change(hh) and i_use_hl1 and i_show_labels
    txt2 = "Pips: \n" + str.tostring(hl_dist)
    info2 = label.new(x=time,y=hh + 0.0005,xloc=xloc.bar_time, text=txt2, textcolor=#ffffff, style=label.style_label_down, color=red)

if ta.change(ll) and i_use_hl1 and i_show_labels
    txt2 = "Pips: \n" + str.tostring(hl_dist)
    info2 = label.new(x=time, y=ll - 0.0005, xloc=xloc.bar_time, text=txt2, textcolor=#ffffff, style=label.style_label_up, color=green)


// H2
// -----------------------------------------------------
var float hh2 = 0.0
var float ll2 = 0.0
hh2 := timeframe.change(hl_res2) ? request.security(syminfo.tickerid, hl_res2, ta.highest(src_h, hl_len2) ) : hh2[1]
ll2 := timeframe.change(hl_res2) ? request.security(syminfo.tickerid, hl_res2, ta.lowest(src_l, hl_len2) ) : ll2[1]
hl2_mid = (hh2 + ll2) * 0.5
hh2_a = angle(hh2, hl_len2)
ll2_a = angle(ll2, hl_len2)
hl2_dist = math.round(get_pip_distance(hh2,ll2) )
hl2_diff = hh2 - ll2
// Bars since close broke levels
hh2_bars = ta.barssince(close>hh2)
ll2_bars = ta.barssince(close<ll2)
// Bars Since Change
// hh2_bars = ta.barssince(ta.change(hh2))
// ll2_bars = ta.barssince(ta.change(ll2))

// Plot
hh2_p1 = plot(i_use_hl2 ? hh2 : na, title='HH2', linewidth=hl2_l_width, color=hl2_col1)
plot(i_use_hl2 ? hl2_mid : na, title='HL2 Mid', linewidth=hl2_l_width, color=hl2_col2)
hh2_p2 = plot(i_use_hl2 ? ll2 : na, title='LL2', linewidth=hl2_l_width, color=hl2_col3)
fill(hh2_p1, hh2_p2,title='Fill H2', color = i_show_fill ? hl2_fill1 : na )
plot(hl2_dist,"Distance 2", color=color.new(green,100),style=plot.style_circles )
plot(i_use_hl2 ? hh2_bars : na, 'Bars Since High', style=plot.style_circles)
plot(i_use_hl2 ? ll2_bars : na, 'Bars Since Low', style=plot.style_circles)

// var hh2_change = 0
// var ll2_change = 0
// hh2_change := ta.change(hh2) and hh2>hh2[1] ? 1 : ta.change(hh2) and hh2<hh2[1] ? -1 : hh2_change
// ll2_change := ta.change(ll2) and ll2>ll2[1] ? 1 : ta.change(ll2) and ll2<ll2[1] ? -1 : ll2_change
//c1_high_low := ta.change(ch1) and ch1 < ch1[1] ? -1 : ta.change(ch1) and ch1 > ch1[1] ? 1 : c1_high_low

// plotshape(i_use_hl2 and hh2_change == -1 ? 1 : 0, title='HH2 Down', color=color.new(color.red, 0), style=shape.circle, location=location.top)
// plotshape(i_use_hl2 and hh2_change ==  1 ? 1 : 0, title='HH2 Up', color=color.new(color.green, 0), style=shape.circle, location=location.bottom)

if ta.change(hh2) and i_use_hl2 and i_show_labels
    txt2 = str.tostring(hl2_dist)
    info2 = label.new(x=time,y=hh2 + 0.0005,xloc=xloc.bar_time, text=txt2, textcolor=#ffffff, style=label.style_label_down, color=red)

if ta.change(ll2) and i_use_hl2 and i_show_labels
    txt2 = str.tostring(hl2_dist)
    info2 = label.new(x=time, y=ll2 - 0.0005, xloc=xloc.bar_time, text=txt2, textcolor=#ffffff, style=label.style_label_up, color=green)

//sell_cond = high > hh ? 1 : na
//buy_cond = low < ll and use_diff and diff > diff_range ? 1 : na
//plotshape(sell_cond, title='Sell 1', color=color.new(color.red, 0), style=shape.circle, location=location.top)
//plotshape(buy_cond, title='Sell 1', color=color.new(color.green, 0), style=shape.circle, location=location.bottom)


//
// ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ //
// STOP LOSS
// ------------------------------------------------------------------------------------------------------------------
g_sl = 'Stop Loss ----------------------------------------------------'
atr_group   = 'ATR'
atr_time_1  = input.timeframe(title='Timeframe', defval='30', group=atr_group)
show_sl     = input.bool(false,title="Display ATR",group=atr_group)
sl_Multip   = input.float(1.5, title='Stop Loss',group=atr_group) // 4 1.5
atr_len     = input.int(14, title='ATR Length ',group=atr_group)
atr_group2   = 'ATR 2'
atr_time_2  = input.timeframe(title='Timeframe', defval='120', group=atr_group2)
show_sl_2   = input.bool(false,title="Display ATR",group=atr_group2)
atr_attr   = 'ATR Attr'
atr_src     = input.string('close', title='Close or Wicks', options=['close', 'wicks'],group=atr_attr) // close
atr_type    = input.string(title='ATR Type', defval='ema', options=['sma', 'ema', 'zema', 'dema', 'tema', 'wma', 'vwma', 'smma', 'rma', 'hma', 'lsma', 'McGinley'], group=atr_attr)
atr_smooth  = input.int(5, title="ATR Smooth", group=atr_attr)
atr_blackout= input.bool(false,title="Blackout",group=atr_attr)

sl_min      = input.float(0.07, title='Stop Loss Minimum Pips')
sl_max      = input.float(0.12, title='Stop Loss Maximum Pips')


f_atr()=>

    ATR = ta.atr(atr_len)
    var float sl_long = 0.0
    var float sl_short = 0.0
    sl_long     := (atr_src =='close' ? close : low)  - ATR * sl_Multip 
    sl_short    := (atr_src =='close' ? close : high) + ATR * sl_Multip 

    atr_upper = ta.sma( ta.ema(sl_short, atr_len), atr_smooth ) //ma_types(atr_len, atr_type, sl_short)
    atr_lower = ta.sma( ta.ema(sl_long, atr_len), atr_smooth )  //ma_types(atr_len, atr_type, sl_long)
    atr_mid = (atr_lower + atr_upper) * 0.5

    atr_mid_a = angle(atr_mid, 14)
    // atr_upper_a = angle(atr_upper, 14)
    // atr_lower_a = angle(atr_lower, 14)

    [sl_long, sl_short, atr_upper, atr_lower, atr_mid, atr_mid_a]

[atr_long_1, atr_short_1, atr_upper_1, atr_lower_1, atr_mid_1, atr_mid_a_1] = request.security(syminfo.tickerid, atr_time_1, f_atr() )
[atr_long_2, atr_short_2, atr_upper_2, atr_lower_2, atr_mid_2, atr_mid_a_2] = request.security(syminfo.tickerid, atr_time_2, f_atr() )

// 
// if newbar(atr_time_1) == 0
//     atr_mid_a_1 := atr_mid_a_1[1]

// Distance close to ATR
atr_dist_up = get_pip_distance(close, atr_short_1)
atr_dist_down = get_pip_distance(close, atr_long_1)

plot(show_sl ? atr_lower_1  : na,"ATR Lower ", color=lime )
plot(show_sl ? atr_upper_1  : na,"ATR Upper ", color=red )
plot(show_sl ? atr_mid_1  : na,"ATR Mid ", color=atr_mid_a_1 > 0 ? white : gray )
plot(show_sl ? atr_mid_a_1  : na,"ATR Angle ", color=atr_mid_a_1 > 0 ? color.new(green,100) : color.new(red,100) )
plot(show_sl ? atr_short_1 : na,"ATR - ", color=color.new(red,70) )
plot(show_sl ? atr_long_1  : na,"ATR + ", color=color.new(green,70) )
plot(show_sl ? atr_dist_up : na,"Dist Up", style=plot.style_circles, color=color.new(red,100) )
plot(show_sl ? atr_dist_down  : na,"Dist Down", style=plot.style_circles, color=color.new(green,100) )
candle = close>open ? 1 : 0
atr_sell = atr_short_1>atr_upper_1 and atr_mid_a_1<0 and candle==1 ? 1 : 0
atr_buy = atr_long_1<atr_lower_1 and atr_mid_a_1>0 and candle==1 ? 1 : 0

plot(show_sl_2 ? atr_lower_2  : na,"ATR Lower ", color=lime )
plot(show_sl_2 ? atr_upper_2  : na,"ATR Upper ", color=red )
plot(show_sl_2 ? atr_mid_2  : na,"ATR Mid ", color=atr_mid_a_2 > 0 ? white : gray )
plot(show_sl_2 ? atr_mid_a_2  : na,"ATR Angle ", color=atr_mid_a_2 > 0 ? color.new(green,100) : color.new(red,100) )
plot(show_sl_2 ? atr_short_2 : na,"ATR - ", color=color.new(red,70) )
plot(show_sl_2 ? atr_long_2  : na,"ATR + ", color=color.new(green,70) )
plot(show_sl_2 ? atr_dist_up : na,"Dist Up", style=plot.style_circles, color=color.new(red,100) )
plot(show_sl_2 ? atr_dist_down  : na,"Dist Down", style=plot.style_circles, color=color.new(green,100) )





// Blackout 
//barcolor(atr_blackout and (close>atr_mid and sl_short<atr_upper) ? black : na)
//barcolor(atr_blackout and (close<atr_mid and sl_long>atr_lower) ? black : na)

//plotshape(show_sl and atr_sell ? 1 : 0,"Top End",style=shape.circle, location = location.top, color = color.rgb(230, 0, 0))
//plotshape(show_sl and atr_buy ? 1 : 0,"Bottom End",style=shape.circle, location = location.bottom, color =  #00be00 )

