//@version=4
study(title = "Trading Bot - JPY", shorttitle="TB - JPY",overlay=true)
//this is a test
show_entry = input(title="Show Entry", type=input.bool, defval=true)
show_atr = input(title="Show ATR", type=input.bool, defval=true)
show_plots = input(title="Show Plots", type=input.bool, defval=false)
show_bb= input(title="Show BB", type=input.bool, defval=true)
use_barcolor = input(title="Use Bar Color", type=input.bool, defval=false)
show_candles = input(title="Show Candlesticks", type=input.bool, defval=false)
use_rsi = input(title="Use RSI", type=input.bool, defval=true)
show_lastprice = input(title="Show Last Price", type=input.bool, defval=false)
use_logic = input(title="Use Logic", type=input.bool, defval=false)
show_cond = input(title="Use Cond", type=input.bool, defval=true)
show_friday= input(title="Show Friday", type=input.bool, defval=true)


red = #ff0000
red_light = #ff0062
aqua = #00bcd4
yellow = #FFFF00
orange = #ff9800
green = #4caf50
green_light = #4caf50
lime = #00E676
white = #ffffff
blue = #42a5f5
gray = #707070
black = #000000
sell_color = color.new(#ff0062,20)
buy_color = color.new(#00c3ff,20)
friday = color.new(sell_color,92)

bgcolor(show_friday and dayofweek == 1 ? friday : na)

angle(_src, len) =>
    rad2degree=180/3.14159265359  //pi 
    ang=rad2degree*atan((_src[0] - _src[1])/atr(len))

// Change
perc_change() =>
    perc = abs( (1 - (close[1] / close)) * 10000 )


// ===  EMA's ===
// ==================================================
ema_200 = ema(close, 200)
ema_200_angle = angle(ema_200,2)
// ema_len_f = 5//input(5, minval=1, title="EMA Length") //6
// ema_fast = ema(close, ema_len_f)
// ema_angle = angle(ema_fast,2)



// ===  ATR ===
// ==================================================
atrlen = 14 //input(14, "ATR Period")
atr_mult = 1.8 //input(1.8, "ATR Mult", step = 0.1) // 1.15
atr_stop = 0.0003 //input(0.0003, "ATR Stop", step = 0.0001) // 0.001
atr_slen = wma(tr(true), atrlen)
atr_upper = atr_slen * atr_mult + close
atr_lower = close - atr_slen * atr_mult

atr_offset = input(5, "ATR Offset") 
atr_s = sma(atr_upper,atrlen+atr_offset)
atr_b = sma(atr_lower,atrlen+atr_offset)
atr_s_angle=(angle(atr_s,5))
atr_b_angle=(angle(atr_b,5))

// ===  Kijun ===
// ==================================================
middleDonchian(Length) =>
    lower = lowest(Length)
    upper = highest(Length)
    avg(upper, lower)

//basePeriods = 14 //input(14, minval=1) //26
kijun =  middleDonchian(14)
k_angle = angle(kijun,3)


// ===  BB ===
// ==================================================
var float bb_squeeze = 0.00
var float bb_diff = 0.00
BB_length = input(200,title="BB length") // 100 40 25 20 43 45
bb_dev2 = input(1.5,title="BB2 dev")
BB_stdDev = 2 //input(2, minval=2.0, maxval=3)
sqz_length = 100 //input(100, minval=2, maxval=200) // 100
sqz_threshold = 76 
bb_s = close //kijun //close
basis = sma(close, BB_length)
//basis = sma(close, BB_length)
dev = BB_stdDev * stdev(bb_s, BB_length)
bb_upper = basis + dev
bb_lower = basis - dev
bb_upper2 = basis + dev * bb_dev2
bb_lower2 = basis - dev * bb_dev2
bb_spread = bb_upper - bb_lower
avgspread = sma(bb_spread, sqz_length)
bb_squeeze := bb_spread / avgspread * 100
diffspread = sma(bb_spread, 14)
bb_diff := bb_spread / diffspread * 100
basis_angle = angle(basis,1) // 3
basis_angle_h = angle(sma(close, 43),2)
bbu_angle = angle(bb_upper,1) // 3
bbl_angle = angle(bb_lower,1) // 3
bbu_diff_ema = abs(bb_upper-ema_200)
bbl_diff_ema = abs(bb_lower-ema_200)
// BB Zones
bb_zone = bb_squeeze < 53 ? 0 : 
 bb_squeeze < sqz_threshold ? 1 : 
 bb_squeeze < 110 ? 2 :
 bb_squeeze < 181 ? 3 :
 bb_squeeze > 181 ? 4 : na
sqz_color = bb_zone == 0 ? #0045b3 :
 bb_zone == 1 ? #ff0062 : 
 bb_zone == 2 ?  gray : 
 bb_zone == 3 ?  #00c3ff : 
 bb_zone == 4 ? white: na

bb_zones_color =  sqz_color



// === Bollinger Bands %B ===
// ==================================================
bbr_len = input(60, title="BBR",minval=1) // 100 60 20, 29, 43
multi = 2 //input(2.0, minval=0.001, maxval=50, title="StdDev")
bbr_basis = sma(close, bbr_len)
deviation = multi * stdev(close, bbr_len)
bbr_upper = bbr_basis + deviation
bbr_lower = bbr_basis - deviation
bbr = (close - bbr_lower)/(bbr_upper - bbr_lower)
bbr_color = bbr>1 ? #ff0062 : bbr<0 ? #00ff64 : #00c3ff
bbr_high = 1
bbr_low = -0.080
//bbr_color = bbr[1]>1 ? #ff0062 : bbr[1]<0 ? #00ff64 : #00c3ff



// ===  SSL ===
// ==================================================
ssl_len = input(12, minval=1,title="SSL Len") // 14 43 60 75
ssl_len2 = input(20, minval=1,title="SSL 2")
ssl_long = input(100, minval=1,title="SSL Long")
ssl_len3 = 8 //input(8, minval=1,title="SSL 3") //7
SSL = wma(2*wma(close, ssl_len/2)-wma(close, ssl_len), round(sqrt(ssl_len)))
SSL2 = wma(2*wma(close, ssl_len2/2)-wma(close, ssl_len2), round(sqrt(ssl_len2)))
SSL3 = wma(2*wma(close, ssl_len3/2)-wma(close, ssl_len3), round(sqrt(ssl_len3)))
SSL_long = wma(2*wma(close, ssl_long/2)-wma(close, ssl_len3), round(sqrt(ssl_len3)))
//ssl_angle = angle(SSL,3)
ssl_angle = angle(SSL,2)
ssl_angle2 = angle(SSL2,2)
ssl_angle3 = angle(SSL3,2)
ssl_diff = abs(SSL - SSL2) * 100
ssl_color = ssl_angle > 0 ? lime : red
ssl_color2 = ssl_angle2 > 0 ? yellow : orange



// === Basis to ema 200 distance ===
// ==================================================
be_length = BB_length // 20 35 43
be_high = 50
be_up = 10
be_down = -10
be_low = -50

be_mult = syminfo.currency == 'JPY' ? 100 : syminfo.currency == 'NZD' ? 10000 : 10000
be_basis = sma(close, be_length) 
be_dist = (be_basis - ema_200) * be_mult
be_close_ema = (close - ema_200) * be_mult
be_close_lower = (close - bb_lower) * be_mult
be_u = (bb_upper - ema_200) * be_mult
be_l = (bb_lower - ema_200) * be_mult
be_angle = abs(angle(be_dist,2))
be_color = be_dist>0?color.green  : color.red


// === MACD Crossover === 
// ==================================================
fastLength = 8 //input(8, minval=1) // 8 //8
slowLength = 25 //input(25,minval=1) // 16 // 21
signalLength= 9 //input(9,minval=1) // 11 // 5
fastMA = ema(close, fastLength)
slowMA = ema(close, slowLength)
macd_c = fastMA - slowMA
sig_c = sma(macd_c, signalLength)
pos = 0
pos := iff(sig_c < macd_c , 1,iff(sig_c > macd_c, -1, nz(pos[1], 0))) 
mc_color = pos == -1 ? red: pos == 1 ? green : blue
mc_angle = angle(macd_c,2)
s_angle = angle(sig_c,2)
mc_diff = macd_c / sig_c

macd_high = syminfo.currency == 'NZD' ? 0.00170 : syminfo.currency == 'JPY' ? 0.395 : 0.0012
macd_up = 0.160
macd_down = -0.160
macd_low = syminfo.currency == 'NZD' ? -0.00170 : syminfo.currency == 'JPY' ? -0.395 : -0.0012


// === WAE ===
// ==================================================
sensitivity = 150
wae_fast = 20 // 40 8
wae_slow = 40 // 40 80 50
channelLength= 20 // 45
wae_mult = 2.0 //1.85
var float wae_columns = 0.000
wae_dz = nz(rma(tr(true),100)) * 3.7
calc_macd(source, wae_fast, wae_slow) =>
	a = ema(source, wae_fast)
	b = ema(source, wae_slow)
	a - b
calc_BBUpper(source, length, wae_mult) => 
	wae_basis = wma(source, length)
	wae_dev = wae_mult * stdev(source, length)
	t = wae_basis + wae_dev
	[t]
calc_BBLower(source, length, wae_mult) => 
	wae_basis = wma(source, length)
	wae_dev = wae_mult * stdev(source, length)
	t = wae_basis - wae_dev
	[t]

t1 = (calc_macd(close, wae_fast, wae_slow) - calc_macd(close[1], wae_fast, wae_slow))*sensitivity * 100
t2 = (calc_macd(close[2], wae_fast, wae_slow) - calc_macd(close[3], wae_fast, wae_slow))*sensitivity * 100
[e1a] = calc_BBUpper(close, channelLength, wae_mult)
[e1b] = calc_BBLower(close, channelLength, wae_mult)
wae_line = (e1a - e1b)
wae_diff = (wae_line - wae_dz) * 100
//wae_perc = (wae_line / wae_dz) * 100
trendUp = (t1 >= 0) ? t1 : 0
trendDown = (t1 < 0) ? (-1*t1) : 0
wae_columns := abs(t1/100)
wae_color = #000000
wae_angle = angle(wae_line,2)
if t1 >= 0
	wae_color := trendUp<trendUp[1] ? lime : green
if t1 < 0
	wae_color := trendDown<trendDown[1] ? orange : red



// === ADX + DI with SMA ===
// ==================================================
adx_len = input(9, title="ADX Length") // 13
adx_line = 9 // input(20, title="threshold", type=integer, defval=20)
adx_avg = 8 // 10 // input(title="SMA", type=integer, defval=10)
var float adx_high = 38 // 39
var float adx_mid = 31
var float adx_center = 20
var float adx_low = 12
var float smooth_tr = 0
var float smooth_di_plus = 0
var float smooth_di_minus = 0
TrueRange = max(max(high-low, abs(high-nz(close[1]))), abs(low-nz(close[1])))
DI_plus = high-nz(high[1]) > nz(low[1])-low ? max(high-nz(high[1]), 0): 0
DI_minus = nz(low[1])-low > high-nz(high[1]) ? max(nz(low[1])-low, 0): 0
smooth_tr := nz(smooth_tr[1]) - (nz(smooth_tr[1])/adx_len) + TrueRange
smooth_di_plus := nz(smooth_di_plus[1]) - (nz(smooth_di_plus[1])/adx_len) + DI_plus
smooth_di_minus := nz(smooth_di_minus[1]) - (nz(smooth_di_minus[1])/adx_len) + DI_minus

di_plus = smooth_di_plus / smooth_tr * 100
di_minus = smooth_di_minus / smooth_tr * 100
DX = abs(di_plus-di_minus) / (di_plus+di_minus)*100
adx = sma(DX, adx_len)
adx_sma = sma(adx, adx_avg)
//adx_angle = (angle(adx,2))



// === RSI Chart Bars ===
// ==================================================
var rsi_color = #000000
rsi_len = 14 //input(14, minval=1, title="Length")
rsi_up = rma(max(change(close), 0), rsi_len)
rsi_down = rma(-min(change(close), 0), rsi_len)
rsi = rsi_down == 0 ? 100 : rsi_up == 0 ? 0 : 100 - (100 / (1 + rsi_up / rsi_down))
isup() => rsi > 70
isdown() => rsi < 30 
// plot(rsi,title="RSI",color=color.blue)
rsi_color := use_rsi and isup() ? color.new(buy_color, 10) : use_rsi and isdown() ? color.new(color.purple, 10)  : na
barcolor(rsi_color, title="Rsi Candles")
barcolor(pos == -1 ? #ff0000: pos == 1 ? #00a000 : gray)

// === RSI Wicks ===
// ==================================================
std         = false //input(false, title="Show Standard RSI")
rsi_candles = false //input(true,  title="Show Candles")
wicks       = true  //input(true,  title="Wicks based on stand-alone RSI")
src_close   = close
src_open    = open
src_high    = high
src_low     = low 
rsiw_len    = 14 //input(14, minval=1, title="RSI wicks length")

norm_close  = avg(src_close,src_close[1])
gain_loss_close   = change(src_close)/norm_close
RSI_close         = 50+50*rma(gain_loss_close, rsiw_len)/rma(abs(gain_loss_close), rsiw_len)

norm_open = if wicks==true 
    avg(src_open,src_open[1])
else 
    avg(src_close,src_close[1])
gain_loss_open   = change(src_open)/norm_open
RSI_open         = 50+50*rma(gain_loss_open, rsiw_len)/rma(abs(gain_loss_open), rsiw_len)
        
norm_high = if wicks==true 
    avg(src_high,src_high[1])
else 
    avg(src_close,src_close[1])
gain_loss_high   = change(src_high)/norm_high
RSI_high         = 50+50*rma(gain_loss_high, rsiw_len)/rma(abs(gain_loss_high), rsiw_len)
        
norm_low  = if wicks==true
    avg(src_low,src_low[1])
else 
    avg(src_close,src_close[1])
gain_loss_low   = change(src_low)/norm_low
RSI_low         = 50+50*rma(gain_loss_low, rsiw_len)/rma(abs(gain_loss_low), rsiw_len)
rws_strong = RSI_open>69 and close>open
rws_mid = RSI_close>70 and RSI_open<70 and close>open
rws_weak = RSI_close<70 and RSI_high>70 and close>open
rwb_weak = RSI_close>30 and RSI_low<30 and close<open
rwb_mid = RSI_close<30 and RSI_open>30 and close<open
rwb_strong = RSI_open<30 and close<open



// === RES - Ranging EMA Spread  ===
// ==================================================
ema1length = 21 // 24 40 12
ema2length = 100//BB_length
ranginglength = 3
rangingmaxvalue = 0.14 //0.1
rangingminvalue = -0.1
enablebarcolors = false


// EMA spread
ema1 = ema(close, ema1length)
ema2 = ema(close, ema2length)
spread = ((ema2 / ema1) -1) * 100
r1 = (spread >= rangingminvalue and spread <= rangingmaxvalue) 
r2 = (spread[1] >= rangingminvalue and spread[1] <= rangingmaxvalue)
r3 = (spread[2] >= rangingminvalue and spread[2] <= rangingmaxvalue)
r4 = (spread[3] >= rangingminvalue and spread[3] <= rangingmaxvalue)
r5 = (spread[4] >= rangingminvalue and spread[4] <= rangingmaxvalue)

ranging = false

if (ranginglength == 1) 
    ranging := r1
if (ranginglength == 2) 
    ranging := r1 and r2
if (ranginglength == 3) 
    ranging := r1 and r2 and r3
if (ranginglength == 4) 
    ranging := r1 and r2 and r3 and r4
if (ranginglength == 5) 
    ranging := r1 and r2 and r3 and r4 and r5

res = spread > 0 ? 0-spread : abs(spread)
res := res * 100
col_low = gray // grey
col_mid = blue// navy
col_high = aqua // aqua
res_c = ranging ? col_low : spread > spread[1] ? col_mid : col_high




// === Stochastic Momentum Index ===
// ==================================================
k_len = 10 // Length") // 12
d_len = 3 // input(3, "Percent D Length")
d_len2 = 2 // input(2, "Percent D Length 2")
mom_high = 45 // input(45, "Overbought")
mom_low = -45 // input(-45, "Oversold")
// Range Calculation
ll = lowest (low, k_len)
hh = highest (high, k_len)
diff = hh - ll
rdiff = close - (hh+ll)/2

avgrel = ema(ema(rdiff,d_len),d_len)
avgdiff = ema(ema(diff,d_len),d_len)

// SMI
SMI = avgdiff != 0 ? (avgrel/(avgdiff/2)*100) : 0
mom_sig = ema(SMI,d_len)
mom_ema = ema(SMI, k_len)
mom_angle = angle(mom_ema,2)
c_sma = mom_sig > mom_high or mom_sig < mom_low ? color.blue : na
// SMI 2
avgrel2 = ema(ema(rdiff,d_len2),d_len2)
avgdiff2 = ema(ema(diff,d_len2),d_len2)
SMI2 = avgdiff != 0 ? (avgrel2/(avgdiff2/2)*100) : 0
mom_sig2 = ema(SMI2,d_len2)
c_sma2 = mom_sig2 > mom_high or mom_sig2 < mom_low ? color.new(color.white,50) : na




// === SSL Hybrid - Mihkel00 ===
// ==================================================
ssl_h_len = input(150,title = "SSL1 / Baseline Length") // 200 55

//ATR
ssl_atr_len = 14 // input(14, "ATR Period")
mult = 1 //input(1, "ATR Multi", step = 0.1)
smoothing = "WMA" //input(title = "ATR Smoothing", defval = "WMA", options = ["RMA", "SMA", "EMA", "WMA"])

ma_function(source, ssl_atr_len) =>
	if smoothing == "RMA"
		rma(source, ssl_atr_len)
	else
		if smoothing == "SMA"
			sma(source, ssl_atr_len)
		else
			if smoothing == "EMA"
				ema(source, ssl_atr_len)
			else
				wma(source, ssl_atr_len)

ssl_atr_slen = ma_function(tr(true), ssl_atr_len)

// ATR Up/Low Bands
upper_band = ssl_atr_slen * mult + close
lower_band = close - ssl_atr_slen * mult

// SSL 1 and SSL2
emaHigh = wma(2 * wma(high, ssl_h_len / 2) - wma(high, ssl_h_len), round(sqrt(ssl_h_len)))
emaLow = wma(2 * wma(low, ssl_h_len / 2) - wma(low, ssl_h_len), round(sqrt(ssl_h_len)))

// BASELINE VALUES
sslh = wma(2 * wma(close, ssl_h_len / 2) - wma(close, ssl_h_len), round(sqrt(ssl_h_len)))
sslh_angle = angle(sslh,2)
multy = 0.2
range = tr
rangema = ema(range, ssl_h_len)
upperk =sslh + rangema * multy
lowerk = sslh - rangema * multy

//SSL1 VALUES
Hlv = int(na)
Hlv := close > emaHigh ? 1 : close < emaLow ? -1 : Hlv[1]
sslDown = Hlv < 0 ? emaHigh : emaLow

//COLORS
color_ssl1 = close > sslDown ? #00c3ff : close < sslDown ? #ff0062 : na
//ssl_color_buy = #00c3ff
//ssl_color_sell = #ff0062
sslh_c = close > upperk ? aqua : close < lowerk ? red : color.gray


// === Plot === 
// ==================================================
plot(basis_angle, color= white, title="Basis angle", style=plot.style_circles)
plot(sslh, color= sslh_c , title="SSLH")
plot(sslh_angle, color= sslh_c , title="SSLH Angle", style=plot.style_circles)
plot(bbr, color=bbr_color, title="BBR",style=plot.style_circles)
plot(res,"RES", style=plot.style_circles, linewidth=0, color=res_c)
plot(wae_line, color=wae_line>wae_dz ? green : red , title="WAE line",style=plot.style_circles)
plot(wae_dz, color=white , title="WAE dz",style=plot.style_circles)
plot(rsi, color=rsi_color , title="RSI",style=plot.style_circles)

//plot(bb_squeeze, color=white , title="BB Squeeze",style=plot.style_circles)
//plot(ema_200_angle, color=white , title="Ema 200 angle",style=plot.style_circles)
//plot(atr_s_angle, color= white , title="ATR S angle ",style=plot.style_circles)
//plot(atr_b_angle, color= white , title="ATR B angle ",style=plot.style_circles)
//plot(basis_angle_h, color= white , title="Basis angle High",style=plot.style_circles)

// ADX
plot(adx, color=yellow, title="ADX",style=plot.style_circles)
plot(adx_sma, color=white, title="ADX SMA",style=plot.style_circles)
plot(di_plus, color=green, title="DI+",style=plot.style_circles)
plot(di_minus, color=red, title="DI-",style=plot.style_circles)
plot(DX, color=red, title="DX",style=plot.style_circles)
// Stoch MTM
plot(mom_ema, title="Mom EMA", color=#ff0000,style=plot.style_circles)
plot(mom_sig, title="Mom SMA", color=c_sma,style=plot.style_circles)
plot(mom_sig2, title="Mom SMA 2", color=c_sma2,style=plot.style_circles)
plot(mom_angle, title="EMA angle", color=#ff0000,style=plot.style_circles)
plot(ssl_diff,title="SSL diff",style=plot.style_circles)
plot(ssl_angle, color=white , title="SSL angle",style=plot.style_circles)
plot(ssl_angle2, color= white , title="SSL2 angle",style=plot.style_circles)
plot(ssl_angle3, color= white , title="SSL3 angle",style=plot.style_circles)
// BE Distance
// plot(be_dist, color=be_color, title="BE distance",style=plot.style_circles)
// plot(be_angle, color=be_color, title="BE angle",style=plot.style_circles)
// plot(be_u, color=be_color, title="BE Up",style=plot.style_circles)
// plot(be_l, color=be_color, title="BE Down",style=plot.style_circles)


// Kijun
plot(kijun, color=yellow, title="Kijun",linewidth=2)
// SSL
plot(SSL,title="SSL",linewidth=2, color=ssl_color)
plot(SSL2,title="SSL 2",color=ssl_color2,linewidth=2)
plot(SSL3, color= green , title="SSL 3")
plot(SSL_long, color= orange , title="SSL Long")
atr_color = color.new(#55d51a,70)
atr_s_upper = plot(atr_s,"ATR Sell",color=atr_color)
atr_b_lower = plot(atr_b,"ATR Buy",color=atr_color)


// BB
//plot(bbu_diff_ema, title="Diff BBU ema 200", color=bb_zones_color,linewidth=3)
plot(basis, title="Basis", color=sslh_c,linewidth=3)
p1 = plot(bb_upper, "BB Upper ",color=bb_zones_color,linewidth=2)
p2 = plot(bb_lower, "BB Lower ",color=bb_zones_color,linewidth=2)
plot(bb_upper2, "BB Upper ",color=color.new(bb_zones_color,50),linewidth=1)
plot(bb_lower2, "BB Lower ",color=color.new(bb_zones_color,50),linewidth=1)
// EMA 200 bands large
plot(ema_200, "EMA 200", color=color.new(yellow,50),linewidth=2 )

// ATR
//atr_cross = basis_angle<-6 and atr_upper>atr_s?1:na
plot(show_atr ? atr_upper + atr_stop : na, "+ATR stop", color=color.new(#ffff00,75))
plot(show_atr ? atr_lower - atr_stop : na, "-ATR stop", color=color.new(#ffff00,75))
fill(atr_s_upper,atr_b_lower,title='ATR Fill',color=color.new(atr_color,93))
//plotshape(atr_cross,title="ATR cross",color=red,style=shape.circle)
//plot(show_plots ? atr_upper : na, "+ATR Upper", color=color.new(#ffffff,85))
//plot(show_plots ? atr_lower : na, "-ATR Lower", color=color.new(#ffffff,85))


var bool enter_exit = false
//var int trade_dir = 0
var int state = 0
var int bar_num = 0
var float lastPrice = 0.0
var mult_diff = 10000
var int num_pips = 20
var int num_bars = 4

entry_signal() =>
	candle = close > open ? 1 : 0
    dir = 0
    counter = 0
    allow = false
    adxb_cond01 = di_minus>adx_high and di_minus>di_minus[1] and 
     adx>di_minus and adx_sma>adx_high and adx>adx[1] ? 1 : na
    adxb_cond02 = di_minus>adx_high and di_plus<adx_low and 
     adx_sma>adx_high and adx_sma>adx and di_minus>di_minus[1] ? 1 : na
    adxb_cond03 = di_minus>adx_high and di_minus>di_minus[1] and 
     di_plus<adx_center and adx<adx_mid and adx>adx_sma ? 1 : na
    cond = ''

    // Sell
    if candle==1 and sslh_c==aqua and res_c!=gray and close>SSL3 and
     atr_upper>bb_upper2 and wae_color==green and mc_color==green
     and not(open>bb_upper and open<atr_b)
     and not(atr_s>bb_upper and SSL2<atr_s)
        dir := -1
        cond := 'sslh-s'

    if dir==-1
        if (isup() and close<bb_lower) or
         (open<bb_upper and di_minus>adx_mid and adx>adx_mid and adx<adx_high) or
         (open>bb_upper and adx_sma<adx_high) or
         (SSL_long<bb_lower)
         and not(di_minus>adx_high and di_plus<adx_low and adx<adx_mid)
            dir := 0

    if bb_zone>2 and candle==0 and SSL_long<basis and 
     mc_color==red and wae_color==red and low>basis
        dir := 1
        counter :=1
        cond := 'uptrend-cnt'


    // === Buy ===
    // ===========
    if bb_zone>2 and basis_angle<0 and candle==0 and sslh_c==red  and close<SSL3 and
     atr_lower<bb_lower2 and wae_color==red and mc_color==red and adxb_cond01
        dir := 1
        cond := 'sslh-b'

    // if bb_zone>2 and basis_angle<0 and candle==0 and sslh_c==red  and close<SSL3 and
    //  atr_lower<bb_lower2 and wae_color==red and mc_color==red and
    //  bbr<0
    //  //and not(isdown() and SSL2>atr_b)
    //  //and not(adx>di_minus and adx_sma>adx_high )
    //  //and not(high<bb_lower and close<bb_lower2 and mom_ema[1]>mom_low)
    //  //and not(bb_zone<3 and close<bb_lower2 and basis_angle<-1 and mom_ema[1]>mom_low)
    //  //and not(open<bb_lower and open>atr_b)
    //  //and not(basis_angle<-1 and sslh<atr_s)
    //  //and not(open<bb_lower and open>atr_b)
    //     dir := 1
    //     cond := 'sslh-b'

    // sslh-0-1-2
    if bb_zone<3 and candle==0 and sslh_c==red  and close<SSL3 and
     atr_lower<bb_lower2 and wae_color==red and mc_color==red
     and not(open<bb_lower and open>atr_b)
     and not(di_minus>adx or adx_sma>adx or di_minus<di_minus[1])
        dir := 1
        cond := 'sslh\n0-1-2'
    // Filter out strong downtrend

    // Jul 8
    if dir==1
        if (isdown() and close>bb_lower) or
         (open<bb_lower and open<SSL) or
         (open>bb_lower and di_minus>adx_mid and adx>adx_mid and adx<adx_high) or
         (open<bb_lower and adx_sma<adx_high) or
         (bb_zone>2 and isdown()==0 and high<bb_lower) or
         (bb_zone>2 and isdown()==1 and high<bb_lower and adx_sma>adx)
         and not(di_minus>adx_high and di_plus<adx_low and adx<adx_mid) // allow this one condition through
            dir := 0


    if candle==0 and isdown()==1 and 
     (
     (close>bb_lower and atr_lower<bb_lower2) or
     (close<sslh and open>sslh and low>bb_lower)
     )
        dir := 1
        counter := 1
        cond := 'rsi-b'

    if bb_zone<3 and candle==0 and close<bb_lower and high>bb_lower2 and 
     atr_lower<bb_lower2 and mc_color==red
     and not(isdown()==1)
        dir := 1
        counter := 1
        cond := 'hi-above'

    if candle==0 and 
     di_minus>adx_high and di_plus<adx_low and adx_sma>adx_high and adx_sma>adx and 
     isdown()==1 and atr_lower<bb_lower2
        dir := 1
        cond := 'sslh\nminus_plus'

    // counter sell downtrend
    if bb_zone>2 and candle==1 and (atr_upper>basis or atr_upper>ema_200) and bbr>0 and
     mc_color==green and sslh_c==aqua and sslh_angle<3 and
     basis_angle<0
        dir := -1
        counter := 1
        cond := 'sslh-basis\nema-cnt'

    if bb_zone>2 and candle==1 and basis_angle<0 and atr_upper>SSL_long and 
     sslh_angle<2 and SSL_long>kijun and close<basis and
     sslh>open and di_plus>adx_center
        dir := -1
        counter := 1
        cond := 'sslh-atr\ncnt'

    // if candle==1 and basis_angle<0 and atr_upper>atr_s and 
    //  sslh_angle<2 and SSL_long>kijun and close<basis and
    //  sslh>open and di_plus>adx_center
    //     dir := -1
    //     counter := 1
    //     cond := 'sslh-atr\ncnt'

    // atr_upper>bb_lower
     


    // Filter out same direction trades, wait 4 candles
    // and price higher than 20 pips before allowing another trade
    if dir == state and use_logic
        allow := (bar_index - bar_num) > num_bars ? true : false
        // sell
        if dir == -1
            dir := ((close - lastPrice) * mult_diff) > num_pips and allow ? dir : 0
            //counter := dir!=0 ? 1 : 0
        // buy
        if dir == 1
            dir := ((close - lastPrice) * mult_diff) < (num_pips * -1) and allow ? dir : 0

            //counter := dir!=0 ? 1 : 0

    // if dir!=state and dir==1
    //     if abs( ((lastPrice - close) * mult_diff) ) < 50
    //         dir==0



    //var int enter_exit = basis_angle > 0 and dir == -1 ? 0 : basis_angle < 0 and dir
    type = dir > 0 ? 1 : dir < 0 ? -1 : 0
    [type,counter,cond]

[trade_dir,counter,condition] = entry_signal() 
    
// if trade_dir[1]==0 and trade_dir != 0
//     dir == 0

if trade_dir != 0 and use_logic
    state := trade_dir

    //enter_exit := counter == 1 ? 1 : 0
    // Sell
    // if basis_angle > 0 and trade_dir == -1
    //     enter_exit := -1
    // // Counter
    // if basis_angle > 0 and trade_dir == 1
    //     enter_exit := 0
    // // Buy
    // if basis_angle < 0 and trade_dir == 1
    //     enter_exit := -1
    // // Counter
    // if basis_angle < 0 and trade_dir == -1
    //     enter_exit := 0
    // if basis>ema_200
    //     enter_exit := trade_dir == -1 ? false : trade_dir == 1 or counter ? true : false
    // if basis<ema_200
    //     enter_exit := trade_dir == 1 ? false : trade_dir == -1 or counter ? true : false
    //enter_exit := counter == 1 ? true : enter_exit == false ? true : false
    lastPrice := close
    bar_num   := bar_index

// if enter_exit != trade_dir and trade_dir != 0
//     enter_exit := enter_exit == false ? true : false



// === Labels ===
// show_exit = trade_dir == 1 and counter == 0? '\nExit': ''
// labelText = tostring(condition) + show_exit
labelText = tostring(condition)
trade_color = trade_dir > 0 ? buy_color : sell_color
if trade_dir != 0 and show_cond
	if trade_dir > 0
        buy = label.new(x=time,y=high,text=labelText,xloc=xloc.bar_time, yloc=yloc.belowbar,color=trade_color,textcolor=color.white, size=size.normal,style=label.style_label_up)
    else
        sell = label.new(x=time,y=high,text=labelText,xloc=xloc.bar_time, yloc=yloc.abovebar,color=trade_color,textcolor=color.white, size=size.normal)

plot(trade_dir!=0?lastPrice:na,title="Last Price", style=plot.style_circles)
//plot(show_lastprice and lastPrice?lastPrice:na,title="Last Price", style=plot.style_circles)
plot(counter,title="Counter Trade", style=plot.style_circles)
//plot(state,"State",style=plot.style_circles)

// Buy
tmp_text = counter==1? "B":"Exit"
plotshape(show_entry and trade_dir == 1 and counter == 1 ? 1 : na, title="Entry Buy", color=trade_color, location = location.belowbar, style=shape.labelup, text="B", textcolor=color.white, size=size.small)
plotshape(show_entry and trade_dir == 1 and counter == 0 ? 1 : na, title="Exit Buy", color=trade_color, location = location.belowbar, style=shape.labelup, text="Exit", textcolor=color.white, size=size.small)
// Sell
plotshape(show_entry and trade_dir == -1 and counter == 1 ? -1: na, title="Entry Sell", color=trade_color, location = location.abovebar, style=shape.labeldown, text="S", textcolor=color.white, size=size.small)
plotshape(show_entry and trade_dir == -1 and counter == 0 ? -1 : na, title="Exit Sell", color=trade_color, location = location.abovebar, style=shape.labeldown, text="Exit", textcolor=color.white, size=size.small)
