//@version=4
study(title='Highest/Lowest - Angles', overlay=false)

angle(_src, len) =>
    rad2degree=180/3.14159265359  //pi 
    ang=rad2degree*atan((_src[0] - _src[1])/atr(len))

src_h = input(title = "Series", type = input.source, defval = close)
src_l = input(title = "Series", type = input.source, defval = close)
res = input(title="Resolution", type=input.resolution, defval="15")
length = input(title = "Length",defval = 21)
offset = input(title = "Offset", defval = 0)
use_smooth = input(title = "Smooth", type = input.bool, defval = false)
smooth_len = input(title = "Smooth Length",defval = 14) 

highest = highest(src_h, length)
lowest = lowest(src_l, length)

h = security(syminfo.tickerid, res, highest[offset])
l = security(syminfo.tickerid, res, lowest[offset])

h_a = angle(h,length)
l_a = angle(l,length)
mid = use_smooth ? sma( (h_a + l_a) * 0.5 ,smooth_len ) : (h_a + l_a) * 0.5
mid_a = angle(mid,length)
p1 = plot(h_a,title = "Highest",linewidth = 2, color = h_a==0? #ff00ff : #ff0000)
p2 = plot(l_a,title = "Lowest",linewidth = 2, color =  l_a==0 ? #55d51a : #00FFFF)
plot(mid,title = "Mid line",linewidth = 2)
fill(p1, p2, title = "Fill", color = #33333344)


