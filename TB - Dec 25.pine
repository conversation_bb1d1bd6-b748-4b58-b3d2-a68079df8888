//@version=5
strategy('TB - Dec 25',
 overlay=true,
 precision=6,
 currency=currency.USD,
 initial_capital=10000,
 default_qty_value=10,
 commission_type=strategy.commission.percent,
 commission_value = 0.0012,
 //max_bars_back = 500,
 pyramiding=1)

//strategy.risk.max_position_size(400000)

red = #ff0062
orange = #ff9800
yellow = #FFFF00
green = #4caf50
lime = #50e600
aqua = #00bcd4
blue = #2962ff
dark_blue = #2962ff
violet = #814dff
white = #ffffff
gray = #707070
black = #000000

// Angle
angle(_src, len) =>
    rad2degree = 180 / 3.14159265359  //pi 
    ang = rad2degree * math.atan((_src[0] - _src[1]) / ta.atr(len))
    ang

// SYM Info
min_tick = syminfo.mintick
var int decimals = int(math.log10(1/min_tick))
decimals := decimals == 2 ? 1 : decimals == 0 ? 1 : decimals
plot(min_tick, title='Min Tick')
plot(decimals, title='Decimals')
get_pip_distance(point1, point2) =>
    diff_points = math.abs( (point1 - point2) )
    pip_value = decimals<3 ? diff_points / syminfo.mintick / 100 : diff_points / syminfo.mintick / 10
    //pip_value = decimals>3 ? diff_points * (math.pow(10, decimals) * syminfo.mintick) : diff_points / syminfo.mintick / 10

// Change
perc_change() =>
    perc = math.abs((1 - close[1] / close) * 10000)
    perc

// Calculate Multi Timeframes
newbar(res) => ta.change(time(res)) == 0 ? 0 : 1

is_between(p1, p2) =>
    is_inside = p1>p2 ? (close<p1) and (open<p1) and (close>p2) and (open>p2) : (close>p1) and (open>p1) and (close<p2) and (open<p2)
    is_inside
    

    
//
// ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ //
// Moving Averages
// ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ //
// ------------------------------------------------------------------------------------------------------------------
g_ma = "MA ----------------------------------------------------"
inl_cb = "cb"
inl_ma = "ma"
i_ma_time = input.timeframe(title='Timeframe', defval='30',group=g_ma)
ma_type1 = input.string(title="MA Type 1", defval="hma", options=["sma","ema","zema","dema","tema","wma","vwma","smma","rma","hma","lsma","McGinley"],group=g_ma)
ma_type2 = input.string(title="MA Type 2", defval="ema", options=["sma","ema","zema","dema","tema","wma","vwma","smma","rma","hma","lsma","McGinley"],group=g_ma)
i_show_ma = input.bool(false,title='Show MAs',group=g_ma)


show_h1 = input.bool(title="M1", defval=false,group=g_ma,inline=inl_cb)
show_h2 = input.bool(title="M2", defval=true,group=g_ma,inline=inl_cb)
show_h3 = input.bool(title="M3", defval=false,group=g_ma,inline=inl_cb)
show_h4 = input.bool(title="M4", defval=true,group=g_ma,inline=inl_cb)
show_h5 = input.bool(title="M5", defval=false,group=g_ma,inline=inl_cb)
show_h6 = input.bool(title="M6", defval=false,group=g_ma,inline=inl_cb)
show_h7 = input.bool(title="M7", defval=false,group=g_ma,inline=inl_cb)
show_h8 = input.bool(title="M8", defval=false,group=g_ma,inline=inl_cb)
show_h9 = input.bool(title="M9", defval=false,group=g_ma,inline=inl_cb)

l1 = input.int(5,title="M1",group=g_ma,inline=inl_ma)
l2 = input.int(15,title="M2",group=g_ma,inline=inl_ma)
l3 = input.int(50,title="M3",group=g_ma,inline=inl_ma)
l4 = input.int(75,title="M4",group=g_ma,inline=inl_ma)
l5 = input.int(100,title="M5",group=g_ma,inline=inl_ma)
l6 = input.int(200,title="M6",group=g_ma,inline=inl_ma)
l7 = input.int(300,title="M7",group=g_ma,inline=inl_ma)
l8 = input.int(500,title="M8",group=g_ma,inline=inl_ma)
l9 = input.int(1000,title="M9",group=g_ma,inline=inl_ma)

angle_amount = 14 //input.int(14, title="Angle Amount",group=g_ma)
i_gaps = input.bool(false,title='Use Gaps',group=g_ma)
show_ma_candles = false //input.bool(false,title="Show Candle Division",group=g_ma)
show_angles = input.bool(false,title='Show Angles',group=g_ma)
i_offset = 0 //input.int(0, title="Offset",group=g_ma) 
src = close //input.source(close, "Source",group=g_ma)
i_ma_use_smooth = false //input.bool(false, title="Use Smoothing",group=g_ma)
i_ma_smooth = 1 //input.int(1,title="Smoothing",group=g_ma)
use_multiple = false //input.bool(false, title="Use Multiply",group=g_ma)
multi_value = 10 //input.int(10, title="Multiply Value",group=g_ma)

g_fill = "Fills"
inl_fill = "fill"
inl_conv = "conv"
i_ma_select = input.int(3, title="Colorized", options=[1,2,3,4,5,6,7,8],group=g_fill)
show_fill = input.bool(title="Show Fill", defval=true,inline=inl_fill,group=g_fill)
show_conv = input.bool(title="Show Conv", defval=true,inline=inl_fill,group=g_fill)
conv_amount = input.float(50, title="Conv Amount", step=1,inline=inl_conv,group=g_fill ) // 4

g_angles = "MA Angles ----------------------------------------------------"
inl_angles = "angles"
i_ang_1 =  input.int(1, title='A1',group=g_angles,inline=inl_angles)
i_ang_2 =  input.int(2, title='A2',group=g_angles,inline=inl_angles)
i_ang_3 =  input.int(3, title='A3',group=g_angles,inline=inl_angles)
i_ang_4 =  input.int(4, title='A4',group=g_angles,inline=inl_angles)
i_ang_5 =  input.int(5, title='A5',group=g_angles,inline=inl_angles)
i_ang_6 =  input.int(7, title='A6',group=g_angles,inline=inl_angles)
i_ang_7 =  input.int(10, title='A7',group=g_angles,inline=inl_angles)
i_ang_8 =  input.int(12, title='A8',group=g_angles,inline=inl_angles)

use_candles = input.bool(false,title="Colorize Candles")
g_select = "Select ----------------------------------------------------"
i_sel_ma = input.int(title='MA Select', defval=3, options=[1,2,3,4,5,6,7,8,9], group=g_select)
i_sel_angle = input.int(title="Angle Select", defval=3, options=[1,2,3,4,5,7,10,12,14],group=g_select)


// Lines and Angles
ma_graph(len,type) =>
    ma =0.0
    length = 1
    if use_multiple
        length := len * multi_value
    else 
        length := len

    if type == 'sma' // Simple
        ma := ta.sma(src,length) 

    if type == 'ema' // Exponential
        ma := ta.ema(src,length)

    if type == 'zema' // Zero Lag Exponential
        e1 = ta.ema(close,length)
        e2 = ta.ema(e1,length)
        diff = e1 - e2
        ma := e1 + diff 

    if type=="dema" // Double Exponential
        e = ta.ema(src, length)
        ma := 2 * e - ta.ema(e, length)
    if type == 'tema' // Triple Exponential
        ema1 = ta.ema(src, length)
        ema2 = ta.ema(ema1, length)
        ema3 = ta.ema(ema2, length)
        ma := 3 * (ema1 - ema2) + ema3
    if type == 'wma' // Weighted
        ma := ta.wma(src,length)
    if type == 'vwma' // Volume Weighted
        ma := ta.vwma(src,length)
    if type=="smma" // Smoothed
        w = ta.wma(src, length)
        ma := na(w[1]) ? ta.sma(src, length) : (w[1] * (length - 1) + src) / length
    if type == "rma"
        ma := ta.rma(src, length)
    if type == 'hma' // Hull
        ma := ta.hma(src, length)
       // ma := ta.wma(2*ta.wma(src, length/2)-ta.wma(src, length), math.floor(math.sqrt(length) ))
    if type=="lsma" // Least Squares
        ma := ta.linreg(src, length, 0)
    if type=="McGinley"
        mg = 0.0
        mg := na(mg[1]) ? ta.ema(src, length) : mg[1] + (src - mg[1]) / (length * math.pow(src/mg[1], 4))
        ma :=mg

    if i_ma_use_smooth
        ma := ta.sma(ma,i_ma_smooth)
    ma


ma_conv(t1, t2) =>
    
    diff = get_pip_distance(t1, t2)
    conv = show_conv and diff < conv_amount and diff > conv_amount * -1 ? true : false
    [diff, conv]


// === HMA ===
// ==================================================
h1 = ma_graph(l1,ma_type1)
h2 = ma_graph(l2,ma_type1)
h3 = ma_graph(l3,ma_type1)
h4 = ma_graph(l4,ma_type1)
h5 = ma_graph(l5,ma_type1)
h6 = ma_graph(l6,ma_type1)
h7 = ma_graph(l7,ma_type1)
h8 = ma_graph(l8,ma_type1)
h9 = ma_graph(l9,ma_type1)

// Angles
h1_a = angle(h1,angle_amount)
h2_a = angle(h2,angle_amount)
h3_a = angle(h3,angle_amount)
h4_a = angle(h4,angle_amount)
h5_a = angle(h5,angle_amount)
h6_a = angle(h6,angle_amount)
h7_a = angle(h7,angle_amount)
h8_a = angle(h8,angle_amount)
h9_a = angle(h9,angle_amount)

[h2_h4_diff,h2_h4_conv] = ma_conv(h2,h4)
[h4_h5_diff,h4_h5_conv] = ma_conv(h4,h5)
[h5_h6_diff,h5_h6_conv] = ma_conv(h5,h6)
[h7_h8_diff,h7_h8_conv] = ma_conv(h7,h8)



// === Multi-Timeframe ===

[h1_m,h2_m,h3_m,h4_m,h5_m,h6_m,h7_m,h8_m,h9_m] = request.security(syminfo.tickerid, i_ma_time, 
 [ma_graph(l1,ma_type1)
 ,ma_graph(l2,ma_type1)
 ,ma_graph(l3,ma_type1)
 ,ma_graph(l4,ma_type1)
 ,ma_graph(l5,ma_type1)
 ,ma_graph(l6,ma_type1)
 ,ma_graph(l7,ma_type1)
 ,ma_graph(l8,ma_type1)
 ,ma_graph(l9,ma_type1)
 ]
 ,gaps=i_gaps?barmerge.gaps_on:barmerge.gaps_off ) 

// Angles
[h1_a_m,h2_a_m,h3_a_m,h4_a_m,h5_a_m,h6_a_m,h7_a_m,h8_a_m,h9_a_m] = request.security(syminfo.tickerid, i_ma_time, 
 [angle(h1_m,angle_amount)
 ,angle(h2_m,angle_amount)
 ,angle(h3_m,angle_amount)
 ,angle(h4_m,angle_amount)
 ,angle(h5_m,angle_amount)
 ,angle(h6_m,angle_amount)
 ,angle(h7_m,angle_amount)
 ,angle(h8_m,angle_amount)
 ,angle(h9_m,angle_amount)
 ]
 ,gaps=i_gaps?barmerge.gaps_on:barmerge.gaps_off ) 

[h2_h4_diff_m,h2_h4_conv_m] = ma_conv(h2_m,h4_m)
[h4_h5_diff_m,h4_h5_conv_m] = ma_conv(h4_m,h5_m)
[h5_h6_diff_m,h5_h6_conv_m] = ma_conv(h5_m,h6_m)
[h7_h8_diff_m,h7_h8_conv_m] = ma_conv(h7_m,h8_m)

// Colorized MA angles
ma_select()=>
    float select = switch i_ma_select
        2 => h2_a_m
        3 => h3_a_m
        4 => h4_a_m
        5 => h5_a_m
        6 => h6_a_m
        7 => h7_a_m
        8 => h8_a_m
        => h5_a_m

    ma_a = math.abs(select)
    ma_zone =  
     ma_a   < i_ang_1 ? 0 
     : ma_a < i_ang_2 ? 1 
     : ma_a < i_ang_3 ? 2 
     : ma_a < i_ang_4 ? 3 
     : ma_a < i_ang_5 ? 4 
     : ma_a < i_ang_6 ? 5 
     : ma_a < i_ang_7 ? 6 
     : ma_a < i_ang_8 ? 7 
     : ma_a > i_ang_7 ? 8 
     : na

    ma_color = 
     ma_zone   == 0 ? red 
     : ma_zone == 1 ? orange 
     : ma_zone == 2 ? yellow 
     : ma_zone == 3 ? gray 
     : ma_zone == 4 ? green 
     : ma_zone == 5 ? lime
     : ma_zone == 6 ? blue
     : ma_zone == 7 ? aqua
     : ma_zone == 8 ? white : na

    [ma_color]

[ma_color] = ma_select()


// === EMA ===
// ==================================================
m1 = ma_graph(l1,ma_type2)
m2 = ma_graph(l2,ma_type2)
m3 = ma_graph(l3,ma_type2)
m4 = ma_graph(l4,ma_type2)
m5 = ma_graph(l5,ma_type2)
m6 = ma_graph(l6,ma_type2)
m7 = ma_graph(l7,ma_type2)
m8 = ma_graph(l8,ma_type2)
m9 = ma_graph(l9,ma_type2)

// Angles
m1_a = angle(m1,angle_amount)
m2_a = angle(m2,angle_amount)
m3_a = angle(m3,angle_amount)
m4_a = angle(m4,angle_amount)
m5_a = angle(m5,angle_amount)
m6_a = angle(m6,angle_amount)
m7_a = angle(m7,angle_amount)
m8_a = angle(m8,angle_amount)
m9_a = angle(m9,angle_amount)

[m1_m,m2_m,m3_m,m4_m,m5_m,m6_m,m7_m,m8_m,m9_m] = request.security(syminfo.tickerid, i_ma_time, 
 [ma_graph(l1,ma_type2)
 ,ma_graph(l2,ma_type2)
 ,ma_graph(l3,ma_type2)
 ,ma_graph(l4,ma_type2)
 ,ma_graph(l5,ma_type2)
 ,ma_graph(l6,ma_type2)
 ,ma_graph(l7,ma_type2)
 ,ma_graph(l8,ma_type2)
 ,ma_graph(l9,ma_type2)
 ]
 ,gaps=i_gaps?barmerge.gaps_on:barmerge.gaps_off ) 

[m1_a_m,m2_a_m,m3_a_m,m4_a_m,m5_a_m,m6_a_m,m7_a_m,m8_a_m,m9_a_m] = request.security(syminfo.tickerid, i_ma_time, 
 [angle(m1_m,angle_amount)
 ,angle(m2_m,angle_amount)
 ,angle(m3_m,angle_amount)
 ,angle(m4_m,angle_amount)
 ,angle(m5_m,angle_amount)
 ,angle(m6_m,angle_amount)
 ,angle(m7_m,angle_amount)
 ,angle(m8_m,angle_amount)
 ,angle(m9_m,angle_amount)
 ]
 ,gaps=i_gaps?barmerge.gaps_on:barmerge.gaps_off ) 


ma_select2()=>
    float select = switch i_ma_select
        2 => m2_a_m
        3 => m3_a_m
        4 => m4_a_m
        5 => m5_a_m
        6 => m6_a_m
        7 => m7_a_m
        8 => m8_a_m
        => m5_a_m

    ma_a = math.abs(select)
    ma_zone =  
     ma_a   < i_ang_1 ? 0 
     : ma_a < i_ang_2 ? 1 
     : ma_a < i_ang_3 ? 2 
     : ma_a < i_ang_4 ? 3 
     : ma_a < i_ang_5 ? 4 
     : ma_a < i_ang_6 ? 5 
     : ma_a < i_ang_7 ? 6 
     : ma_a < i_ang_8 ? 7 
     : ma_a > i_ang_7 ? 8 
     : na

    ma_color = 
     ma_zone   == 0 ? red 
     : ma_zone == 1 ? orange 
     : ma_zone == 2 ? yellow 
     : ma_zone == 3 ? gray 
     : ma_zone == 4 ? green 
     : ma_zone == 5 ? lime
     : ma_zone == 6 ? blue
     : ma_zone == 7 ? aqua
     : ma_zone == 8 ? white : na

    [ma_color]

[ma_color2] = ma_select2()

[m2_m4_diff_m,m2_m4_conv_m] = ma_conv(m2_m,m4_m)
[m4_m5_diff_m,m4_m5_conv_m] = ma_conv(m4_m,m5_m)
[m5_m6_diff_m,m5_m6_conv_m] = ma_conv(m5_m,m6_m)
[m7_m8_diff_m,m7_m8_conv_m] = ma_conv(m7_m,m8_m)


// WMA 4 hours
// -----------------------------------------------------
// var w1_m = 0.0
// var w1_a_m = 0.0
//i_wma_time = input.timeframe(title='WMA Timeframe', defval='239',group=g_ma)

//w1_m = request.security(syminfo.tickerid, i_wma_time, ma_graph(10,"wma")[1], lookahead=barmerge.lookahead_on)
//w1_a_m = request.security(syminfo.tickerid, i_wma_time, angle(w1_m,angle_amount)[1], lookahead=barmerge.lookahead_on)
// w1_m := request.security(syminfo.tickerid, i_wma_time, newbar(i_wma_time) ? ma_graph(10,"wma") : w1_m[1] )
// w1_a_m := request.security(syminfo.tickerid, i_wma_time, newbar(i_wma_time) ? angle(w1_m,angle_amount) : w1_a_m[1] )  
//plot(w1_m, title="W1-4hr", color=w1_a_m>0 ? aqua : orange)

// Colorize candles based on MA angles
//barcolor(use_candles? ma_color : na)
// Plots
l_width = 2

get_distance = get_pip_distance(h7_m, h8_m)
//plot(get_distance, title='Distance',color=color.new(blue,90))


// Angles HMA
// plot(show_angles and h1_a_m ? h1_a_m : na,color=h1_a_m>0?color.new(aqua,100):color.new(orange,100),title="M1 A")
// plot(show_angles and h2_a_m ? h2_a_m : na,color=h2_a_m>0 ? color.new(color.green,100): color.new(color.red,100),title="M2 A")
// plot(show_angles and h3_a_m ? h3_a_m : na,color=i_ma_select==3 ? color.new(ma_color,100) : color.new(color.blue,100),title="M3 A")
// plot(show_angles and h4_a_m ? h4_a_m : na,color=i_ma_select==4 ? color.new(ma_color,100) : color.new(yellow,100),title="M4 A")
// plot(show_angles and h5_a_m ? h5_a_m : na,color=i_ma_select==5 ? color.new(ma_color,100) : color.new(orange,100),title="M5 A")
// plot(show_angles and h6_a_m ? h6_a_m : na,color=i_ma_select==6 ? color.new(ma_color,100) : color.new(red,100), title="M6 A")
// plot(show_angles and h7_a_m ? h7_a_m : na,color=i_ma_select==7 ? color.new(ma_color,100) : color.new(orange,100),title="M7 A")
// plot(show_angles and h8_a_m ? h8_a_m : na,color=i_ma_select==8 ? color.new(ma_color,100) : color.new(red,100),title="M8 A")
//plot(show_angles and h9_a_m ? h9_a_m : na,color=i_ma_select==9 ? color.new(ma_color,100) : color.new(red,100),title="M9 A")


// Plot Line
//p_h1_m = plot(h1_m and show_h1?h1_m:na, style=plot.style_line, color= h1_a_m>0?aqua:orange,title="M1",linewidth=i_ma_select==1?l_width:1,offset = i_offset)
//p_h2_m = plot(i_show_ma and h2_m and show_h2?h2_m:na, style=plot.style_line, color= h2_a_m>0 ? green: red,title="M2",linewidth=i_ma_select==2?l_width:1,offset = i_offset)
//p_h3_m = plot(i_show_ma and h3_m and show_h3?h3_m:na, style=plot.style_line, color= i_ma_select==3 ? ma_color : h3_a_m>0 ? blue : aqua,title="M3",linewidth=i_ma_select==3?l_width:1,offset = i_offset)
// p_h4_m = plot(i_show_ma and h4_m and show_h4?h4_m:na, style=plot.style_line, color= i_ma_select==4 ? ma_color : yellow,title="M4",linewidth=i_ma_select==4?l_width:1,offset = i_offset)
//p_h5_m = plot(i_show_ma and h5_m and show_h5?h5_m:na, style=plot.style_line, color= i_ma_select==5 ? ma_color : h5_a_m<1 and h5_a_m>-1 ? aqua : h5_a_m>0 ? orange : h5_a_m<0 and h5_m>h6_m ? red : green,title="M5",linewidth=i_ma_select==5?l_width:1,offset = i_offset)
//p_h6_m = plot(i_show_ma and h6_m and show_h6?h6_m:na, style=plot.style_line, color= i_ma_select==6 ? ma_color : h6_a_m > 0 ? red : lime, title="M6",linewidth=i_ma_select==6?l_width:1,offset = i_offset)
// p_h7_m = plot(i_show_ma and h7_m and show_h7?h7_m:na, style=plot.style_line, color= i_ma_select==7 ? ma_color : h7_a_m>0 ? orange : h7_a_m<0 and h7_m>h8_m ? red : green,title="M7",linewidth=i_ma_select==7?l_width:1,offset = i_offset)
// p_h8_m = plot(i_show_ma and h8_m and show_h8?h8_m:na, style=plot.style_line, color= i_ma_select==8 ? ma_color : h8_a_m>0 ? red : lime,title="M8",linewidth=i_ma_select==8?l_width:1,offset = i_offset)
// p_h9_m = plot(i_show_ma and h9_m and show_h9?h9_m:na, style=plot.style_line, color= h9_a_m>0 ? red : lime,title="M9",linewidth=i_ma_select==9?l_width:1,offset = i_offset)

// Fills
//fill(p_h5_m,p_h6_m,title="S5/S6 Conv", color=show_fill and h5_m<h6_m?color.new(green,90): show_fill ? color.new(red,90):na)
//// fill(p_h7_m,p_h8_m,title="S7/S8 Conv", color=show_fill and h7_m<h8_m?color.new(green,90): show_fill ? color.new(red,90):na)
//fill(p_h5_m,p_h6_m,title="M5/M6 Conv", color=h5_h6_conv_m and h5_m>h6_m?color.new(red,70) : h5_h6_conv_m and h5_m<h6_m?color.new(green,70) : na)
// fill(p_h7_m,p_h8_m,title="M7/M8 Conv", color=h7_h8_conv_m and h7_m>h8_m?color.new(red,70) : h7_h8_conv_m and h7_m<h8_m?color.new(green,70) : na)




// === EMA Plot ===
//p_m1_m = plot(m1_m and show_m1?m1_m:na, style=plot.style_line, color= m1_a_m>0?aqua:orange,title="M1",linewidth=i_ma_select==1?l_width:1,offset = i_offset)
//p_m2_m = plot(i_show_ma and m2_m and show_h2?m2_m:na, style=plot.style_line, color= m2_a_m>0 ? green: red,title="M2",linewidth=i_ma_select==2?l_width:1,offset = i_offset)
//p_m3_m = plot(i_show_ma and m3_m and show_h3?m3_m:na, style=plot.style_line, color= i_ma_select==3 ? ma_color : m3_a_m>0 ? blue : aqua,title="M3",linewidth=i_ma_select==3?l_width:1,offset = i_offset)
//p_m4_m = plot(i_show_ma and m4_m and show_m4?m4_m:na, style=plot.style_line, color= i_ma_select==4 ? ma_color : yellow,title="M4",linewidth=i_ma_select==4?l_width:1,offset = i_offset)
//p_m5_m = plot(i_show_ma and m5_m and show_h5?m5_m:na, style=plot.style_line, color= i_ma_select==5 ? ma_color : m5_a_m<1 and m5_a_m>-1 ? aqua : m5_a_m>0 ? orange : m5_a_m<0 and m5_m>m6_m ? red : green,title="M5",linewidth=i_ma_select==5?l_width:1,offset = i_offset)
//p_m6_m = plot(i_show_ma and m6_m and show_h6?m6_m:na, style=plot.style_line, color= i_ma_select==6 ? ma_color2 : m6_a_m > 0 ? red : lime, title="M6",linewidth=i_ma_select==6?l_width:1,offset = i_offset)
// p_m7_m = plot(i_show_ma and m7_m and show_h7?m7_m:na, style=plot.style_line, color= i_ma_select==7 ? ma_color : h7_a_m>0 ? orange : h7_a_m<0 and m7_m>m8_m ? red : green,title="M7",linewidth=i_ma_select==7?l_width:1,offset = i_offset)
// p_m8_m = plot(i_show_ma and m8_m and show_h8?m8_m:na, style=plot.style_line, color= i_ma_select==8 ? ma_color : h8_a_m>0 ? red : lime,title="M8",linewidth=i_ma_select==8?l_width:1,offset = i_offset)
// p_m9_m = plot(i_show_ma and m9_m and show_h9?m9_m:na, style=plot.style_line, color= h9_a_m>0 ? red : lime,title="M9",linewidth=i_ma_select==9?l_width:1,offset = i_offset)

// Angles EMA
// plot(show_angles and h1_a_m ? h1_a_m : na,color=h1_a_m>0?color.new(aqua,100):color.new(orange,100),title="M1 A")
// plot(show_angles and h2_a_m ? h2_a_m : na,color=h2_a_m>0 ? color.new(color.green,100): color.new(color.red,100),title="M2 A")
// plot(show_angles and h3_a_m ? h3_a_m : na,color=i_ma_select==3 ? color.new(ma_color,100) : color.new(color.blue,100),title="M3 A")
// plot(show_angles and h4_a_m ? h4_a_m : na,color=i_ma_select==4 ? color.new(ma_color,100) : color.new(yellow,100),title="M4 A")
// plot(show_angles and h5_a_m ? h5_a_m : na,color=i_ma_select==5 ? color.new(ma_color,100) : color.new(orange,100),title="M5 A")
//plot(show_angles and m6_m and show_h6 ? m6_a_m : na,color=i_ma_select==6 ? color.new(ma_color2,100) : color.new(red,100), title="M6 A")
// plot(show_angles and h7_a_m ? h7_a_m : na,color=i_ma_select==7 ? color.new(ma_color,100) : color.new(orange,100),title="M7 A")
// plot(show_angles and h8_a_m ? h8_a_m : na,color=i_ma_select==8 ? color.new(ma_color,100) : color.new(red,100),title="M8 A")
//plot(show_angles and h9_a_m ? h9_a_m : na,color=i_ma_select==9 ? color.new(ma_color,100) : color.new(red,100),title="M9 A")

var int flag_u = 0
var int flag_d = 0
flag_u := h2_a>20 and h2_a>h2_a[1] ? 1 : flag_u==1 and h2_a<h2_a[1] ? 0 : flag_u==1 and h2_a>0 ? 1 : na
flag_d := h2_a<-10 and h2_a<h2_a[1] ? -1 : flag_d==-1 and h2_a>h2_a[1] ? 0 : flag_d==-1 and h2_a<0 ? -1 : na
//plotshape(flag_u==0 ? 1 : na,title="Flag Up",color=red ,style=shape.circle,location=location.top)
//plotshape(flag_d==0?1:na,title="Flag Down",color=green ,style=shape.circle,location=location.bottom)



// Previous State
// var state_change = blue
// ss_red2 = m5 > m6 and m5_a > 0
// ss_orange2 = m5 > m6 and m5_a < 0
// ss_lime2 = m5 < m6 and m6_a > 0
// ss_green2 = m5 < m6 and m6_a < 0
// m5_m6_c = ss_red2 ? red : ss_orange2 ? orange : ss_lime2 ? lime : ss_green2 ? green : na

// Distance to M8
h8_dist = get_pip_distance(close, h8)
//plot(m8_dist, title='M8 Dist', color= m8_dist>20 ? color.new(red,100) : color.new(green,100) )

// plot(i_show_ma and show_m4 ? h4 : na, color=h4_a>0?yellow:orange, title='M4 HMA')
// plot(i_show_ma and show_m7 ? h7 : na, color=h7_a>0?green:red, title='M7 HMA')
// plot(i_show_ma and show_m8 ? h8 : na, color=h8_a>0?green:red, title='M8 HMA')




// CANDLES MULTI-TIMEFRAME
// -----------------------------------------------------
g_candle_multi = 'Candles Multi -----------------------------------------------------------'
i_candle_timeframe = input.timeframe('60', "Resolution", group=g_candle_multi) // 1 hour
i_candle_timeframe2 = input.timeframe('360', "Resolution", group=g_candle_multi) // 6 hour
i_candle_lookback = input.int(1, title='Lookback', group=g_candle_multi)
i_show_candles = input.bool(true, title="Show Candles", group=g_candle_multi)
i_show_candles2 = input.bool(true, title="Show Candles 2", group=g_candle_multi)
i_candle_heikin = input.bool(false,title='Use Heikin Ashi Candles', group=g_candle_multi)
i_candle_mid = input.bool(false, title='Show Candle Midline', group=g_candle_multi)
i_candle_type = input.bool(false, title='Show Candle Types', group=g_candle_multi)
i_candle_trans = input.int(70, title='Candles Transparency', group=g_candle_multi)

f_get_ohlc()=>
    o = open
    h = high
    l = low
    c = close

    [o,h,l,c]

// Candle Types - Doji ect..
f_candle_type(co, ch, cl, cc, dist, c_type, show) =>

    if ta.change(cc) and i_candle_type and show

        short_sm = dist<4 and c_type == 0 ? 1 : 0
        long_sm  = dist<4 and c_type == 1 ? 1 : 0
        if short_sm
            short_sm_txt = "Doji \n Short"
            info1 = label.new(x=time,y=ch,xloc=xloc.bar_time, text=short_sm_txt, textcolor=#ffffff,color =red, style=label.style_label_down)
        if long_sm
            info2 = label.new(x=time,y=cl,xloc=xloc.bar_time, text="Doji \n Long", textcolor=#ffffff,color =green, style=label.style_label_up)


// CANDLES ATTRIBUTES 1
// -----------------------------------------------------
co  = 
 i_candle_heikin ? request.security(ticker.heikinashi(syminfo.tickerid), i_candle_timeframe, open[i_candle_lookback], lookahead=barmerge.lookahead_on) 
 : request.security(syminfo.tickerid, i_candle_timeframe, open[i_candle_lookback], lookahead=barmerge.lookahead_on)
ch  = 
 i_candle_heikin ? request.security(ticker.heikinashi(syminfo.tickerid), i_candle_timeframe, high[i_candle_lookback], lookahead=barmerge.lookahead_on) 
 : request.security(syminfo.tickerid, i_candle_timeframe, high[i_candle_lookback], lookahead=barmerge.lookahead_on)
cl   =
 i_candle_heikin ? request.security(ticker.heikinashi(syminfo.tickerid), i_candle_timeframe, low[i_candle_lookback], lookahead=barmerge.lookahead_on) 
 : request.security(syminfo.tickerid, i_candle_timeframe, low[i_candle_lookback], lookahead=barmerge.lookahead_on)
cc = 
 i_candle_heikin ? request.security(ticker.heikinashi(syminfo.tickerid), i_candle_timeframe, close[i_candle_lookback], lookahead=barmerge.lookahead_on) 
 : request.security(syminfo.tickerid, i_candle_timeframe, close[i_candle_lookback], lookahead=barmerge.lookahead_on)
cm   = (cc + co) * 0.5

chl = get_pip_distance(ch,cl)
cco = get_pip_distance(cc,co)
c_type = cc>co ? 1 : 0
candle_color = c_type == 1 ? green : red
candle_dist = get_pip_distance(cc,co)

f_candle_type(co, ch, cl, cc, candle_dist, c_type, i_show_candles )


// CANDLES ATTRIBUTES 2
// -----------------------------------------------------
co2  = 
 i_candle_heikin ? request.security(ticker.heikinashi(syminfo.tickerid), i_candle_timeframe2, open[i_candle_lookback], lookahead=barmerge.lookahead_on) 
 : request.security(syminfo.tickerid, i_candle_timeframe2, open[i_candle_lookback], lookahead=barmerge.lookahead_on)
ch2  = 
 i_candle_heikin ? request.security(ticker.heikinashi(syminfo.tickerid), i_candle_timeframe2, high[i_candle_lookback], lookahead=barmerge.lookahead_on) 
 : request.security(syminfo.tickerid, i_candle_timeframe2, high[i_candle_lookback], lookahead=barmerge.lookahead_on)
cl2   =
 i_candle_heikin ? request.security(ticker.heikinashi(syminfo.tickerid), i_candle_timeframe2, low[i_candle_lookback], lookahead=barmerge.lookahead_on) 
 : request.security(syminfo.tickerid, i_candle_timeframe2, low[i_candle_lookback], lookahead=barmerge.lookahead_on)
cc2 = 
 i_candle_heikin ? request.security(ticker.heikinashi(syminfo.tickerid), i_candle_timeframe2, close[i_candle_lookback], lookahead=barmerge.lookahead_on) 
 : request.security(syminfo.tickerid, i_candle_timeframe2, close[i_candle_lookback], lookahead=barmerge.lookahead_on)

cm2   = (cc2 + co2) * 0.5
chl2 = get_pip_distance(ch2,cl2)
cco2 = get_pip_distance(cc2,co2)
c_type2 = cc2>co2 ? 1 : 0
candle_color2 = c_type2 == 1 ? green : red
candle_dist2 = get_pip_distance(cc2,co2)

f_candle_type(co2, ch2, cl2, cc2, candle_dist2, c_type2, i_show_candles2)


// PLOT CANDLE 1
// -----------------------------------------------------
//plot(c_type, title='Candle Type', color=candle_color)
p_co  = plot(i_show_candles ? co : na, title='Candle Open',color=color.new(candle_color,i_candle_trans) )
p_ch  = plot(i_show_candles ? ch : na, title='Candle High',color=color.new(color.white,80) )
p_cl   = plot(i_show_candles ? cl : na, title='Candle Low',color=color.new(color.white,80) )
p_cc = plot(i_show_candles ? cc : na, title='Candle Close',color=color.new(candle_color,i_candle_trans) )
p_cm   = plot(i_show_candles and i_candle_mid ? cm : na, title='Candle Mid',color=color.new(color.white,80) )
fill(p_cc, p_co, title='Fill Candle 1',color=i_show_candles ? color.new(candle_color,i_candle_trans) : na )
plot(chl, title='Pips HL', color=color.new(candle_color,100))
plot(cco, title='Pips Body', color=color.new(candle_color,100))


// PLOT CANDLE 2
// -----------------------------------------------------
//plot(c_type, title='Candle Type', color=candle_color)
p_co2  = plot(i_show_candles2 ? co2 : na, title='Candle Open 2',color=color.new(candle_color2,i_candle_trans) )
p_ch2  = plot(i_show_candles2 ? ch2 : na, title='Candle High 2',color=color.new(color.white,80) )
p_cl2   = plot(i_show_candles2 ? cl2 : na, title='Candle Low 2',color=color.new(color.white,80) )
p_cc2 = plot(i_show_candles2 ? cc2 : na, title='Candle Close 2',color=color.new(candle_color2,i_candle_trans) )
p_cm2   = plot(i_show_candles2 and i_candle_mid ? cm2 : na, title='Candle Mid 2',color=color.new(color.white,80) )
fill(p_cc2, p_co2, title='Fill Candle 2',color=i_show_candles2 ? color.new(candle_color2,i_candle_trans) : na )
plot(chl2, title='Pips High to Low', color=color.new(candle_color2,100))
plot(cco2, title='Pips Close to Open', color=color.new(candle_color2,100))



// ===  PD Retrace ===
// ==================================================
g_retrace = 'PD Retrace -------------------------------------------------------------'
i_pd_time  = input.timeframe('720', "Fibo Resolution") // 12 hours
i_pd_time2 = input.timeframe('240', "Fibo Resolution") // 12 hours
i_pd_lookback = input.int(1, title='Lookback')
i_pd_labels = input.bool(false, title="Show Labels")
i_pd_fibo = input.bool(false, title='Show Fibo')
i_pd_fills = input.bool(false, title='Fill in Fibo Levels')
i_pd_show_candle = input.bool(false, title="Show PD Candles")
i_pd_fills_trans = input.int(90, title='Fill Transparency')
i_pd_candle_trans = input.int(50, title='Candles Transparency')


// Fibo
//close_m = request.security(syminfo.tickerid, i_pd_time, close, lookahead=barmerge.lookahead_on)
fibo0   = request.security(syminfo.tickerid, i_pd_time, high[i_pd_lookback], lookahead=barmerge.lookahead_on)
fibo100 = request.security(syminfo.tickerid, i_pd_time, low[i_pd_lookback], lookahead=barmerge.lookahead_on)
fibo23  = (fibo100-fibo0)*0.786+fibo0
fibo38  = (fibo100-fibo0)*0.618+fibo0
fibo50  = (fibo100-fibo0)/2+fibo0
fibo62  = (fibo100-fibo0)*0.382+fibo0
fibo78  = (fibo100-fibo0)*0.236+fibo0

// Candles
fibo_close = request.security(syminfo.tickerid, i_pd_time2, close[i_pd_lookback], lookahead=barmerge.lookahead_on)
fibo_open  = request.security(syminfo.tickerid, i_pd_time2, open[i_pd_lookback], lookahead=barmerge.lookahead_on)
fibo_high  = request.security(syminfo.tickerid, i_pd_time2, high[i_pd_lookback], lookahead=barmerge.lookahead_on)
fibo_low   = request.security(syminfo.tickerid, i_pd_time2, low[i_pd_lookback], lookahead=barmerge.lookahead_on)
fibo_mid   = (fibo_close + fibo_open) * 0.5

f_retrace_level(obj) =>
    var int level = 0
    // Above Red
    if obj>fibo0 
        level := 8
    // Red 
    if obj<fibo0 and obj>fibo78
        level := 7
    // Orange 
    if obj<fibo78 and obj>fibo62
        level := 6
    // Yellow
    if obj<fibo62 and obj>fibo50
        level := 5
    // Aqua
    if obj<fibo50 and obj>fibo38
        level := 4
    // Green
    if obj<fibo38 and obj>fibo23
        level := 3
    // Lime
    if obj<fibo23 and obj>fibo100
        level := 2
    // Below Lime
    if obj<fibo100
        level := 1

    level

// Fibo Plots
// p_fib0 = plot(i_pd_fibo ? fibo0 : na ,title='Fibo 0',color=color.new(color.red,50),linewidth=2)
// p_fibo100 = plot(i_pd_fibo ? fibo100 : na ,title='Fibo 100',color=color.new(color.green,50),linewidth=2)
// p_fibo23  = plot(i_pd_fibo ? fibo23 : na,title='Fibo 23',color=color.new(color.gray,50) )
// p_fibo38  = plot(i_pd_fibo ? fibo38 : na,title='Fibo 38',color=color.new(color.gray,50) )
// p_fibo50  = plot(i_pd_fibo ? fibo50 : na,title='Fibo 50',color=color.new(color.gray,50) )
// p_fibo62  = plot(i_pd_fibo ? fibo62 : na,title='Fibo 62',color=color.new(color.gray,50) )
// p_fibo78  = plot(i_pd_fibo ? fibo78 : na,title='Fibo 78',color=color.new(color.gray,50) )

// fill(p_fibo78, p_fib0,title='Fill 100',color=i_pd_fills? color.new(red,i_pd_fills_trans) : na )
// fill(p_fibo62, p_fibo78,title='Fill 78',color=i_pd_fills? color.new(orange,i_pd_fills_trans) : na )
// fill(p_fibo50, p_fibo62,title='Fill 62',color=i_pd_fills? color.new(yellow,i_pd_fills_trans) : na )
// fill(p_fibo50, p_fibo38,title='Fill 38',color=i_pd_fills? color.new(aqua,i_pd_fills_trans) : na )
// fill(p_fibo38, p_fibo23,title='Fill 23',color=i_pd_fills? color.new(green,i_pd_fills_trans) : na )
// fill(p_fibo23, p_fibo100,title='Fill 0',color=i_pd_fills? color.new(lime,i_pd_fills_trans) : na )

// Fibo Candle
// fibo_candle_color = fibo_close>fibo_open ? green : red
// p_fibo_close = plot(i_pd_show_candle ? fibo_close : na, title='Fibo Close',color=color.new(fibo_candle_color,i_pd_candle_trans) )
// p_fibo_open  = plot(i_pd_show_candle ? fibo_open : na, title='Fibo Open',color=color.new(fibo_candle_color,i_pd_candle_trans) )
// p_fibo_high  = plot(i_pd_show_candle ? fibo_high : na, title='Fibo High',color=color.new(color.white,80) )
// p_fibo_low   = plot(i_pd_show_candle ? fibo_low : na, title='Fibo Low',color=color.new(color.white,80) )
// p_fibo_mid   = plot(i_pd_show_candle ? fibo_mid : na, title='Fibo Mid',color=color.new(color.white,80) )
// fill(p_fibo_close, p_fibo_open, title='Fill Candle',color=i_pd_show_candle ? color.new(fibo_candle_color,i_pd_candle_trans) : na )


// diff_fibo0 = ta.change(fibo0,1) 
// diff_fibo100 = ta.change(fibo100,1)  

// if ta.change(fibo0) and i_pd_labels
//     higher = fibo0 > fibo0[1] ? "Higher: " + str.tostring(diff_fibo0)  : fibo0 < fibo0[1] ? "Lower: " + str.tostring(diff_fibo0) : "No Change: " + str.tostring(diff_fibo0)

    
//     txt1 = str.tostring(get_pip_distance(fibo0, fibo100))
//     info1 = label.new(x=time,y=fibo0,xloc=xloc.bar_time, text=txt1, textcolor=#ffffff)

//     lower = fibo100 > fibo100[1] ? "Higher: " + str.tostring(diff_fibo100)  : fibo100 < fibo100[1] ? "Lower: " + str.tostring(diff_fibo100) : "No Change: " + str.tostring(diff_fibo100)
//     txt2 = str.tostring(lower)
//     info2 = label.new(x=time,y=fibo100,xloc=xloc.bar_time, text=txt2, textcolor=#ffffff, style=label.style_label_up)


retrace_level = f_retrace_level(close)
//plot(retrace_level, title="Retrace", color=color.new(blue, 100))




//
// ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ //
// HIGHEST LOWEST
// ------------------------------------------------------------------------------------------------------------------
g_hl = 'HIGHEST LOWEST -------------------------------------------------------------'
hl_res = input.timeframe(title='Resolution', defval='30', group=g_hl) // 60
hl_res2 = input.timeframe(title='Resolution 2', defval='120', group=g_hl) // 240
i_use_hl1 = input.bool(false,title='Display HL1', group=g_hl)
i_use_hl2 = input.bool(false,title='Display HL2', group=g_hl)
i_show_bars = input.bool(false,title='Show Bars', group=g_hl)
gaps = input.bool(false,title='Bar Merge Gaps On', group=g_hl)
hl_len = input(title='Length', defval=15, group=g_hl)  // 21
src_h = input(title='Source', defval=close, group=g_hl)
src_l = input(title='Source', defval=close, group=g_hl)
use_diff = input(title='Filter Diff', defval=true, group=g_hl)
diff_range = input(10, title='FDiff Range', group=g_hl)
offset = input(title='Offset', defval=0, group=g_hl)
i_hl_smooth = input(title='Smooth', defval=true, group=g_hl)
i_hl_smooth_len = input(title='Smooth Length', defval=14, group=g_hl)

// HL 1
var float hh = 0.0
var float ll = 0.0
hh := timeframe.change(hl_res) ? request.security(syminfo.tickerid, hl_res, ta.highest(src_h, hl_len), gaps=gaps ? barmerge.gaps_on : barmerge.gaps_off) : hh[1]
ll := timeframe.change(hl_res) ? request.security(syminfo.tickerid, hl_res, ta.lowest(src_l, hl_len), gaps=gaps ? barmerge.gaps_on : barmerge.gaps_off) : ll[1]
hl_mid = i_hl_smooth ? ta.sma((hh + ll) * 0.5, i_hl_smooth_len) : (hh + ll) * 0.5
hh_a = angle(hh, hl_len)
ll_a = angle(ll, hl_len)
diff = hh - ll

// h1_p1 = plot(i_use_hl1 ? hh : na, title='HH', linewidth=2, color=hh_a == 0 ? violet : red)
// h1_p2 = plot(i_use_hl1 ? ll : na, title='LL', linewidth=2, color=ll_a == 0 ? aqua : blue)

// //plot(i_use_hl1 ? hl_mid : na, title='HL Midline', linewidth=2)
// //barcolor(i_show_bars and i_use_hl1 and timeframe.change(hl_res) ? blue : na, title='HL1 Bar Color')

// HL 2
var float hh2 = 0.0
var float ll2 = 0.0
hh2 := timeframe.change(hl_res2) ? request.security(syminfo.tickerid, hl_res2, ta.highest(src_h, hl_len), gaps=gaps ? barmerge.gaps_on : barmerge.gaps_off) : hh2[1]
ll2 := timeframe.change(hl_res2) ? request.security(syminfo.tickerid, hl_res2, ta.lowest(src_l, hl_len), gaps=gaps ? barmerge.gaps_on : barmerge.gaps_off) : ll2[1]
hh2_a = angle(hh2, hl_len)
ll2_a = angle(ll2, hl_len)
diff2 = hh2 - ll2
hl_mid2 = i_hl_smooth ? ta.sma((hh2 + ll2) * 0.5, i_hl_smooth_len) : (hh2 + ll2) * 0.5

// hh2_p1 = plot(i_use_hl2 ? hh2 : na, title='HH2', linewidth=2, color=hh2_a == 0 ? #ff00ff : #ff0000)
// hh2_p2 = plot(i_use_hl2 ? ll2 : na, title='LL2', linewidth=2, color=ll2_a == 0 ? #55d51a : #00FFFF)
// plot(i_use_hl2 ? hl_mid2 : na, title='HL2 Mid', linewidth=2)


//fill(p1, p2, title='Fill', color=#33333365)
//plot(diff, title='Diff', color=color.new(color.blue, 100), linewidth=0)



//
// ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ //
// LuxAlgo Predictive Channels
// ------------------------------------------------------------------------------------------------------------------
g_PC = 'Predictive Channels -------------------------------------------------------------'
PC_mult  = input.float(5, 'Factor', minval = 0, group=g_PC)
PC_slope = input.float(50, minval = 0, group=g_PC) * PC_mult
i_PC_show_plot = input.bool(true,title = 'Show Plots', group=g_PC)
pc_i_show_info = input.bool(true,title = 'Show Info', group=g_PC)
i_PC_show_ma = input.bool(true,title = 'Show MA', group=g_PC)

var upper_bound = 0.0
var pc_direction = 0
var pc_bars = 0

//-----------------------------------------------------------------------------}
//Calculation
//-----------------------------------------------------------------------------{
var PC_avg = close
var PC_os = 1
var PC_hold_atr = 0.0

PC_atr = nz(ta.atr(200)) * PC_mult

PC_avg := math.abs(close - PC_avg) > PC_atr ? close 
  : PC_avg + PC_os * PC_hold_atr / PC_slope

PC_hold_atr := PC_avg == close ? PC_atr / 2 : PC_hold_atr
PC_os := PC_avg > PC_avg[1] ? 1 : PC_avg < PC_avg[1] ? -1 : PC_os

PC_R2 = PC_avg + PC_hold_atr
PC_R1 = PC_avg + PC_hold_atr/2
PC_S1 = PC_avg - PC_hold_atr/2
PC_S2 = PC_avg - PC_hold_atr


PC_get_angle() =>
    PC_R2>PC_R2[1] ? 1 : -1

PC_get_level(obj) =>
  
    var int level = 0
    // Above R2
    if obj>PC_R2 
        level := 6
    // Above R1 
    if obj<PC_R2 and obj>PC_R1
        level := 5
    // Above Average 
    if obj<PC_R1 and obj>PC_avg
        level := 4
    // Below Average
    if obj<PC_avg and obj>PC_S1
        level := 3
    // Below S1
    if obj<PC_S1 and obj>PC_S2
        level := 2
    // Below S2
    if obj<PC_S2
        level := 1

    level

PC_is_between(obj) =>
    between = PC_R2>obj and PC_S2<obj ? 1 : 0

PC_barssince()=>
    bars = ta.barssince( get_pip_distance(PC_R2,PC_R2[1])>5 )

if get_pip_distance(PC_R2,PC_R2[1])>5
    upper_bound := PC_R2
    pc_direction := upper_bound>upper_bound[1] ? 1 : -1

pc_bars := PC_barssince()


//-----------------------------------------------------------------------------}
//Plot
//-----------------------------------------------------------------------------{
// plot(pc_i_show_info ? pc_bars : na, title='Bars Since', color=color.new(white,100))
// plot(pc_i_show_info ? pc_direction : na, title='direction', color=color.new(white,100))
// plot(pc_i_show_info ? PC_is_between(m2) : na, title='is between', color=color.new(white,100))
// plot(pc_i_show_info ? PC_get_angle() : na, title='Get Angle', color=color.new(white,100))

// plot_0 = plot(i_PC_show_plot ? close : na, color = na, display = display.none, editable = false)

// //SR PLots
// plot(i_PC_show_plot ? PC_R2 : na, 'Upper Resistance', close == PC_avg ? na : red)
// plot(i_PC_show_plot ? PC_R1 : na, 'Lower Resistance', close == PC_avg ? na : color.new(red, 50))
// plot_avg = plot(i_PC_show_plot ? PC_avg : na, 'Average', close == PC_avg ? na : gray)
// plot(i_PC_show_plot ? PC_S1 : na, 'Upper Support', close == PC_avg ? na : color.new(green, 50))
// plot(i_PC_show_plot ? PC_S2 : na, 'Lower Support', close == PC_avg ? na : green)

// //Fill
// topcss = close > PC_avg ? color.new(green, 80) : color.new(chart.bg_color, 100)
// btmcss = close < PC_avg ? color.new(red, 80) : color.new(chart.bg_color, 100)
// fill(plot_0, plot_avg, PC_R2, PC_S2, topcss, btmcss)




//
// ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ //
// Consolidation Zones
// ------------------------------------------------------------------------------------------------------------------
g_cz = 'Consolidation Zones -------------------------------------------------------'
cz_show = input(false, title='Show', group=g_cz)
cz_prd = input.int(defval=20, title='Loopback Period', minval=2, maxval=50, group=g_cz) // 10
cz_len = input.int(defval=5, title='Min Consolidation Length', minval=2, maxval=20, group=g_cz) // 5
cz_show_area = input(defval=true, title='Paint Consolidation Area ', group=g_cz)
cz_zonecol = input(defval=color.new(color.blue, 70), title='Zone Color', group=g_cz)

float cz_hb_ = ta.highestbars(cz_prd) == 0 ? high : na
float cz_lb_ = ta.lowestbars(cz_prd) == 0 ? low : na
var int cz_dir = 0
float cz_zz = na
float cz_pp = na

cz_iff_1 = cz_lb_ and na(cz_hb_) ? -1 : cz_dir
cz_dir := cz_hb_ and na(cz_lb_) ? 1 : cz_iff_1
if cz_hb_ and cz_lb_
    if cz_dir == 1
        cz_zz := cz_hb_
        cz_zz
    else
        cz_zz := cz_lb_
        cz_zz
else
    cz_iff_1 = cz_lb_ ? cz_lb_ : na
    cz_zz := cz_hb_ ? cz_hb_ : cz_iff_1
    cz_zz

for x = 0 to 1000 by 1
    if na(close) or cz_dir != cz_dir[x]
        break
    if cz_zz[x]
        if na(cz_pp)
            cz_pp := cz_zz[x]
            cz_pp
        else
            if cz_dir[x] == 1 and cz_zz[x] > cz_pp
                cz_pp := cz_zz[x]
                cz_pp
            if cz_dir[x] == -1 and cz_zz[x] < cz_pp
                cz_pp := cz_zz[x]
                cz_pp

var int cz_conscnt = 0
var float cz_condhigh = na
var float cz_condlow = na
float cz_H_ = ta.highest(cz_len)
float cz_L_ = ta.lowest(cz_len)
var line cz_upline = na
var line cz_dnline = na
bool cz_breakoutup = false
bool cz_breakoutdown = false

if ta.change(cz_pp)
    if cz_conscnt > cz_len
        if cz_pp > cz_condhigh
            cz_breakoutup := true
            cz_breakoutup
        if cz_pp < cz_condlow
            cz_breakoutdown := true
            cz_breakoutdown
    if cz_conscnt > 0 and cz_pp <= cz_condhigh and cz_pp >= cz_condlow
        cz_conscnt += 1
        cz_conscnt
    else
        cz_conscnt := 0
        cz_conscnt
else
    cz_conscnt += 1
    cz_conscnt

if cz_conscnt >= cz_len and cz_show
    if cz_conscnt == cz_len
        cz_condhigh := cz_H_
        cz_condlow := cz_L_
        cz_condlow
    else
        line.delete(cz_upline)
        line.delete(cz_dnline)
        cz_condhigh := math.max(cz_condhigh, high)
        cz_condlow := math.min(cz_condlow, low)
        cz_condlow

    cz_upline := line.new(bar_index, cz_condhigh, bar_index - cz_conscnt, cz_condhigh, color=color.red, style=line.style_dashed)
    cz_dnline := line.new(bar_index, cz_condlow, bar_index - cz_conscnt, cz_condlow, color=color.lime, style=line.style_dashed)
    cz_dnline
//

var float cz_cz_condMid_ch = 0.0
cz_condMid = cz_condhigh or cz_condlow ? (cz_condhigh + cz_condlow) * 0.5 : na
cz_cz_condMid_ch := ta.change(cz_condMid)

// Plot
// plot(cz_show ? cz_condMid : na, color=#ffff00, style=plot.style_stepline)
// plot( ta.change(cz_condMid) and cz_show ? ta.change(cz_condMid) : cz_cz_condMid_ch[1] , title="Change")
// fill(plot(cz_condhigh, color=na, style=plot.style_stepline), plot(cz_condlow, color=na, style=plot.style_stepline), color=cz_show_area and cz_conscnt > cz_len and cz_show ? cz_zonecol : color.new(color.white, 100), transp=90)






//
// ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ //
// Bollinger Bands
// ------------------------------------------------------------------------------------------------------------------
g_bb = 'BB Bands ----------------------------------------------------'
bb_res = input.timeframe(title='Resolution', defval='', group=g_bb) // 60
i_show_bb_bands = input.bool(false,title='Show BB Bands', group=g_bb)
i_showbb_fast = input.bool(false, title="Show Fast")
i_showbb = input.bool(false, title="Show BB")
i_bb_len_fast = input(20, title='BB Len Fast', group=g_bb)
i_bb_len = input(500, title='BB Len', group=g_bb)
i_show_back = input.bool(false, title='Show Background')
sqz_length = 80
// Fast
bb_basis_f = ta.sma(close, i_bb_len_fast)
dev_f = 2 * ta.stdev(close, i_bb_len_fast)
bb_upper_f = bb_basis_f + dev_f
bb_lower_f = bb_basis_f - dev_f
bb_spread_f = bb_upper_f - bb_lower_f
bb_angle_f = angle(bb_basis_f,1)
// plot(i_show_bb_bands and i_showbb_fast ? bb_basis_f : na,title="Basis")
// plot(i_show_bb_bands and i_showbb_fast ? bb_upper_f : na,title="bb_upper")
// plot(i_show_bb_bands and i_showbb_fast ? bb_lower_f : na,title="bb_lower")

f_bb_slow() =>

    bb_basis = ta.sma(close, i_bb_len)
    dev = 2 * ta.stdev(close, i_bb_len)
    bb_upper = bb_basis + dev
    bb_lower = bb_basis - dev
    bb_spread = bb_upper - bb_lower
    bb_angle = angle(bb_basis,3)
    bb_avgspread = ta.sma(bb_spread, sqz_length)

    [bb_basis,bb_upper,bb_lower,bb_spread,bb_angle,bb_avgspread]

[bb_basis,bb_upper,bb_lower,bb_spread,bb_angle,bb_avgspread] = request.security(syminfo.tickerid, bb_res, f_bb_slow() )

bb_squeeze = 0.00
bb_squeeze := bb_spread / bb_avgspread * 100
// Zones
bb_zone = bb_squeeze < 53 ? 0 : 
 bb_squeeze < sqz_length ? 1 : 
 bb_squeeze < 120 ? 2 :
 bb_squeeze < 160 ? 3 :
 bb_squeeze > 160 ? 4 :
 bb_squeeze > 200 ? 5 : na
sqz_color = bb_zone == 0 ? #0045b3 :
 bb_zone == 1 ? #ff0062 : 
 bb_zone == 2 ?  gray : 
 bb_zone == 3 ?  #00c3ff : 
 bb_zone == 4 ? white:
 bb_zone == 5 ? yellow: na

bb_zones_color =  sqz_color

// plot(i_show_bb_bands and i_showbb ? bb_upper : na,title="bb_upper")
// plot(i_show_bb_bands and i_showbb ? bb_lower : na,title="bb_lower")
// plot(i_show_bb_bands and i_showbb ? bb_basis : na,title="Basis", color=bb_zones_color)

//plot(i_show_bb_bands ? bb_angle : na,title="BB Angle 2", color=bb_angle>0?green:red)
bb_cond = h1 < bb_lower ? 1 : h1 > bb_upper ? -1 : na
//bgcolor(i_show_back and bb_cond == 1 ? color.new(aqua,80) : i_show_back and bb_cond == -1 ? color.new(orange,80) : na)
//barcolor(bb_cond == 1 ? aqua : bb_cond == -1 ? orange : na)




// ===  Fibo Trend ===
// ==================================================
g_fibo_trend = 'G Fibo Trend ----------------------------------------------------'
inl_fibo = 'inl-fib'
fibo_trend_time = input.timeframe("",title="Timeframe", group=g_fibo_trend)
show_gfibo = input.bool(false,"Show G Fibo Trend",group=g_fibo_trend)
show_candles = input.bool(true,"Fibo Candles",group=g_fibo_trend)
ma_val = input.int(10, title="MA",group=g_fibo_trend ) // 6
fibo_period = input.int(25,"Analysis Period",group=g_fibo_trend) // 50
lowerValue = input.float(0.382,"Lower Fibonacci Level",options=[0.236, 0.382, 0.50, 0.618, 0.786],group=g_fibo_trend)
upperValue = input.float(0.618,"Upper Fibonacci Level",options=[0.236, 0.382, 0.50, 0.618, 0.786],group=g_fibo_trend)
showFill = input.bool(true,"Show Filling",group=g_fibo_trend)
changeCandle = input.bool(true,"Change Candle Color",group=g_fibo_trend)




f_fibo_trend() =>
    ma = ta.wma(close,ma_val)
    max = ta.highest(close, fibo_period)
    min = ta.lowest(close, fibo_period)
    lowerFib = min + (max-min)*lowerValue
    upperFib = min + (max-min)*upperValue
    [ma, lowerFib, upperFib]

[ma, lowerFib, upperFib] = request.security(syminfo.tickerid, fibo_trend_time, f_fibo_trend() )

float closeVal = ma
float openVal = ma
color fibo_color = closeVal>upperFib and openVal>upperFib?green:closeVal<lowerFib and openVal<lowerFib?red:yellow

// maxLine = plot(max and show_gfibo?max : na,color=color.green,title="Max")
// minLine = plot(min and show_gfibo?min : na,color=color.red,title="Min")
// LowerFibLine = plot(lowerFib and show_gfibo?lowerFib : na,color=color.rgb(228, 255, 75, 20),title="Lower Fib")
// UpperFibLine = plot(upperFib and show_gfibo?upperFib : na,color=color.rgb(228, 255, 75, 20),title="Upper Fib")
// fill(maxLine,UpperFibLine,color=showFill?color.rgb(0,255,0,changeCandle?95:70):na)
// fill(UpperFibLine,LowerFibLine,color=showFill?color.rgb(228, 255, 75, changeCandle?95:70):na)
// fill(LowerFibLine,minLine,color=showFill?color.rgb(255,0,0,changeCandle?95:70):na)
//plotcandle(open,high,low,close,"Bar",color=changeCandle?fibo_color:na,wickcolor=changeCandle?fibo_color:na,bordercolor=changeCandle?fibo_color:na)

//barcolor(show_candles? fibo_color : na, title='Fibo Bar Color')





//
// ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ //
// ADX
// ------------------------------------------------------------------------------------------------------------------
g_adx = 'ADX -------------------------------------------------------------'
g_adx_time = ''
adx_time = input.timeframe(title='Timeframe', defval='60', group=g_adx)
i_adx_showbars = input.bool(false, title="ADX bars", group=g_adx)
inl_adx = '1'
use_adx_curr = input.bool(title='Show Current', defval=true, inline=inl_adx, group=g_adx)
use_adx_multi = input.bool(title='Show Multi', defval=true, inline=inl_adx, group=g_adx)

adx_len = input(9, title='Length', group=g_adx)  // 14
adx_line = input(20, title='threshold', group=g_adx)  // 20
adx_avg = input(8, title='SMA', group=g_adx)  // 10
adx_top = input(50, title='High', group=g_adx)  // 41
adx_high = input(39.5, title='High', group=g_adx)  // 41
adx_mid = input(title='Mid', defval=33, group=g_adx)
adx_center = input(title='Center', defval=20, group=g_adx)
adx_low = input(title='Low', defval=12, group=g_adx)

// Show adx
// show_di_plus = input(title='Di Plus', defval=true,group=g_adx)
// show_di_minus = input(title='Di Minus', defval=true,group=g_adx)
// show_adx = input(title='ADX', defval=true,group=g_adx)
// show_adx_sma = input(title='ADX SMA', defval=true,group=g_adx)


f_adx() =>
    smooth_tr = 0.0
    smooth_di_plus = 0.0
    smooth_di_minus = 0.0
    TrueRange = math.max(math.max(high - low, math.abs(high - nz(close[1]))), math.abs(low - nz(close[1])))
    DI_plus = high - nz(high[1]) > nz(low[1]) - low ? math.max(high - nz(high[1]), 0) : 0
    DI_minus = nz(low[1]) - low > high - nz(high[1]) ? math.max(nz(low[1]) - low, 0) : 0
    smooth_tr := nz(smooth_tr[1]) - nz(smooth_tr[1]) / adx_len + TrueRange
    smooth_di_plus := nz(smooth_di_plus[1]) - nz(smooth_di_plus[1]) / adx_len + DI_plus
    smooth_di_minus := nz(smooth_di_minus[1]) - nz(smooth_di_minus[1]) / adx_len + DI_minus

    di_plus = smooth_di_plus / smooth_tr * 100
    di_minus = smooth_di_minus / smooth_tr * 100
    DX = math.abs(di_plus - di_minus) / (di_plus + di_minus) * 100
    adx = ta.sma(DX, adx_len)
    adx_sma = ta.sma(adx, adx_avg)

    [adx, adx_sma, di_plus, di_minus]

[adx, adx_sma, di_plus, di_minus] = f_adx()

adx_m = request.security(syminfo.tickerid, adx_time, adx)
adx_sma_m = request.security(syminfo.tickerid, adx_time, adx_sma)
di_plus_m = request.security(syminfo.tickerid, adx_time, di_plus)
di_minus_m = request.security(syminfo.tickerid, adx_time, di_minus)
////plot(di_plus_m, title='Di Plus')

//barcolor(i_adx_showbars and di_plus_m>di_minus_m ? red : i_adx_showbars and di_minus_m>di_plus_m ? green : na, title='ADX Bar Color')






//
// ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ //
// TRADING
// ------------------------------------------------------------------------------------------------------------------

g_trading = 'Trading ----------------------------------------------------'
i_live_trading  = input.bool(false, title='Live Trading Only',group=g_trading)
isLive          = barstate.isrealtime
i_equity        = input.string("Equity", options=["Initial", "Equity"], title="Initial or Equity",group=g_trading)
i_long_trades   = input.bool(true, title='Long Trades',group=g_trading)
i_long_back_trades = input.bool(true, title='Long Backside Trades',group=g_trading)
i_short_trades  = input.bool(true, title='Short Trades',group=g_trading)
i_short_back_trades = input.bool(true, title='Short Backside Trades',group=g_trading)
i_use_pos       = input.bool(true,title="Use Percentage based Position Size",group=g_trading)
i_pctStop       = input(1.5, '% of Risk to Starting Equity Use to Size Positions',group=g_trading) / 100

Session(sess) => na(time("2",sess)) == false
i_show_tr = input.bool(false,title='Show Session')
i_show_nt = input.bool(true, title='Show No Trade')
i_session  = input.session(title="Session", defval="1400-1600")
i_no_trading = input.session(title="No Trading Hours", defval="1045-1300")
i_GMT = input.string(title='GMT', defval='GMT-10', options=['GMT-10', 'GMT-9', 'GMT-8', 'GMT-7', 'GMT-6', 'GMT-5', 'GMT-4', 'GMT-3', 'GMT-2', 'GMT-1', 'GMT-0', 'GMT+1', 'GMT+2', 'GMT+3', 'GMT+4', 'GMT+5', 'GMT+6', 'GMT+7', 'GMT+8', 'GMT+9', 'GMT+10', 'GMT+11', 'GMT+12', 'GMT+13'] )
// Set the start of day
start_time = Session(i_session)
timerange = time(timeframe.period, i_session, i_GMT) and i_show_tr
no_trading = time(timeframe.period, i_no_trading, i_GMT) and i_show_nt 
// ▒▒▒▒▒ Sessions ▒▒▒▒▒ 
session_StartDate = input.time(timestamp("1 January 2023 00:00 -1000"), title="Start Date", group=g_trading )
show_sessions = input.bool(false,title='Sessions', group=g_trading)
// As  = input.session(title="Asia", defval="1800-0300")
// Lon = input.session(title="London", defval="0300-1200")
// Ny  = input.session(title="New York", defval="0800-1800")
Dz  = input.session(title="Deadzone - High Spreads", defval="1645-1830")




inl_color = "inl_color"
inl_color2 = "inl_color2"
c1_on = false //input.bool(true,title="", group=g_trading,inline=inl_color)
c1 = color.new(#00bcd4,90) //input.color(title="Asia", defval=#00bcd4,group=g_trading,inline=inl_color)
c2_on = true //input.bool(true,title="",group=g_trading,inline=inl_color)
c2 = color.new(#00796b,90) //input.color(title="London", defval=#00796b,group=g_trading,inline=inl_color)
c3_on = true //input.bool(true,title="",group=g_trading,inline=inl_color2)
c3 = color.new(#b71c1c,90) //input.color(title="New York", defval=#b71c1c,group=g_trading,inline=inl_color2)
c4_on = true //input.bool(true,title="", group=g_trading,inline=inl_color2)
c4 = color.new(color.purple,90) //input.color(title="Deadzone", defval=#b71c1c,group=g_trading,inline=inl_color2)

// Session(sess) => na(time("2",sess)) == false
// Asia = Session(As) and c1_on and show_sessions? c1 : na
// London = Session(Lon) and c2_on and show_sessions ? c2 : na
// NewYork = Session(Ny) and c3_on and show_sessions ? c3 : na
Deadzone = Session(Dz) and c4_on and show_sessions ? c4 : na
// bgcolor(Asia)
// bgcolor(London)
// bgcolor(NewYork)
bgcolor(Deadzone)


//
// ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ //
// STOP LOSS
// ------------------------------------------------------------------------------------------------------------------
g_sl = 'Stop Loss ----------------------------------------------------'
i_plot_trades = input.bool(true, title="Display Trades",group=g_sl)
i_sl_type   = input.string("ATR", title="SL Type", options=["ATR", "Pips","MA","SUP","Lowest"],group=g_sl) // ATR
i_ma_atr    = input.string("M6", title="Select MA ATR", options=["M6","M7","M8"],group=g_sl) // ATR

atr_group   = 'ATR'
show_sl     = input.bool(false,title="Show Stop Loss",group=atr_group)
sl_Multip   = input.float(0.75, title='SL Amount',group=atr_group) // 1.25 1.5
atr_len     = input.int(14, title='ATR Length ',group=atr_group)
atr_src     = input.string('close', title='Close or Wicks', options=['close', 'wicks'],group=atr_group) // close
//atr_type    = input.string(title='ATR Type', defval='ema', options=['sma', 'ema', 'zema', 'dema', 'tema', 'wma', 'vwma', 'smma', 'rma', 'hma', 'lsma', 'McGinley'], group=atr_group)
atr_smooth  = input.int(5, title="ATR Smooth", group=atr_group)
i_sl_min_max = input.bool(true,title='Use SL Min and Max',group=atr_group)
sl_min      = input.float(2.0, title='Stop Loss Minimum Pips',group=atr_group) // 2.0
sl_max      = input.float(2.0, title='Stop Loss Maximum Pips',group=atr_group) // 4.0 12.0


// Take Profit
g_tp = 'Take Profit ----------------------------------------------------'
i_tpFactor  = input(200, 'Target Profit',group=g_tp) // 100 13 3.5 3
i_qty_mult  = input.float(1.0, title='Quantity Multiplier',group=g_tp) // 5.0 2
i_tsFactor  = input(2.0, 'Trailing Stop',group=g_tp) // 1.25
i_ts        = input.bool(true, title="Use Trailing Stop",group=g_tp)
i_ticks     = input.float(10, title='Min Ticks',group=g_tp) // 100 for Forex 1000 for NAS
show_ts     = input.bool(true,title="Trailing Stop",group=g_tp)
i_bkcandles = 11 //input.int(11, title="Lowerest range - Number of candles",group=g_sl)


float qty_value = switch syminfo.type
    "forex" => 100000.0
    "futures" => 10.0
    "index" => 10.0
    => 10.0
    
stop_loss()=>

    float sl_short  = na
    float sl_long   = na
    float sl_short_t   = na
    float quantity  = syminfo.currency == 'JPY' ? i_pctStop * 100 : i_pctStop

    if i_sl_type == "ATR"
        atr_len = 14
        ATR = ta.atr(atr_len)
        sl_long     := (atr_src =='close' ? close : low)  - ATR * sl_Multip 
        sl_short    := (atr_src =='close' ? close : high) + ATR * sl_Multip 
        
    if i_sl_type == "MA"
        atr_len = 14
        ATR = ta.atr(atr_len)
        float ma_select = switch i_ma_atr
            "M6" => h6
            "M7" => h7
            "M8" => h8
            => h6
        
        sl_long     := ma_select
        sl_short    := ma_select


    if i_sl_type == "Lowest"
        sl_short    = ta.highest(high, i_bkcandles)[1]
        sl_long     = ta.lowest(low, i_bkcandles)[1]


    min = 1 / math.pow(10, (decimals-1) ) * sl_min
    max = 1 / math.pow(10, (decimals-1) ) * sl_max
    longRatio = math.abs(close - sl_long)
    sl_long   := i_sl_min_max==false ? sl_long 
     : longRatio < min ? close - min 
     : longRatio > max ? close - max : sl_long
    longDiff  = math.abs(close - sl_long) 
    longPips  = get_pip_distance(close,sl_long)
    longTS    = close + (i_tsFactor * longDiff)
    longTP    = close + (i_tpFactor * longDiff)
    plValue   = (i_equity=="Initial" ? strategy.initial_capital : strategy.equity) * quantity / (longDiff / close)
    pl_size   = i_use_pos ? math.ceil( plValue ) / close : math.ceil( qty_value * i_qty_mult )
    // Short
    shortRatio = math.abs(close - sl_short)
    sl_short   := i_sl_min_max==false ? sl_short 
     : shortRatio < min ? close + min 
     : shortRatio > max ? close + max : sl_short
    shortDiff = shortRatio
    shortPips = get_pip_distance(close,sl_short)
    shortTS   = close - (i_tsFactor * shortDiff)
    shortTP   = close - (i_tpFactor * shortDiff)
    psValue   = (i_equity=="Initial" ? strategy.initial_capital : strategy.equity) * quantity / (shortDiff / close)
    ps_size   = i_use_pos ? math.ceil( psValue / close ) : math.ceil( qty_value * i_qty_mult )


    [sl_short,sl_long, close, close, shortTP, longTP, longTS, shortTS, pl_size, ps_size, longDiff, shortDiff, longPips, shortPips ]

[atr_short, atr_long, long_close, short_close, shortTP, longTP, longTS, shortTS, pl_size, ps_size, longDiff, shortDiff, longPips, shortPips] = stop_loss()


// ATR
atr_upper = ta.sma( ta.ema(atr_short, atr_len), atr_smooth ) // ma_types(atr_len, atr_type, atr_short)
atr_lower = ta.sma( ta.ema(atr_long, atr_len), atr_smooth )  // ma_types(atr_len, atr_type, atr_long)
atr_mid = (atr_lower + atr_upper) * 0.5
atr_mid_a = angle(atr_mid,14)
tmp = 1 / math.pow(10, (decimals-1) )  * sl_min

// Plot ATR
plot(show_sl ? atr_upper  : na,"ATR Upper ", color=red )
plot(show_sl ? atr_lower  : na,"ATR Lower ", color=lime )
plot(show_sl ? atr_mid  : na,"ATR Mid ", color=atr_mid_a > 0 ? white : gray )
plot(show_sl ? atr_mid_a  : na,"ATR Angle ", color=atr_mid_a > 0 ? color.new(green,100) : color.new(red,100) )
plot(show_sl ? atr_long  : na,"ATR + ", color=color.new(green,70) )
plot(atr_long - tmp , title='Min Pip Value', color=color.new(red,100)  )
plot(show_sl ? atr_short : na,"ATR - ", color=color.new(red,70) )


// ▒▒▒▒▒ Stages ▒▒▒▒▒ 

g_stages = 'Stages ----------------------------------------------------'
stage2= 20
stage2_p= 10
stage3= 30
stage3_p= 15



// ▒▒▒▒▒ FILTERS ▒▒▒▒▒ 

g_filters = 'Filters ----------------------------------------------------'
inl_fi1 = 'inl_fi1'
inl_fi2 = 'inl_fi2'
inl_fi3 = 'inl_fi3'
i_perc_filter = input.bool(false, title='Percent Filter',group=g_filters, inline=inl_fi1)
i_perc_filter_num = input.float(7.0, title='Percent Number',group=g_filters, inline=inl_fi1)

i_cz_filter = input.bool(true, title='Consolidation Filter',group=g_filters, inline=inl_fi2)
i_cz_filter_num = input.float(15.0, title='Consolidation Number',group=g_filters, inline=inl_fi2)

i_atr_filter = input.bool(false, title='ATR Filter',group=g_filters, inline=inl_fi3)
i_atr_filter_num = input.float(9.0, title='Percent Number', group=g_filters, inline=inl_fi3)
i_perc_hhll = input.bool(false, title='Highest Lowest Filter',group=g_filters)
i_rsi_filter = input.bool(false, title='RSI Filter',group=g_filters)
i_ssl_filter = input.bool(false, title='SSL Filter',group=g_filters)
i_fibo_filter = input.bool(false, title='Fibo Filter',group=g_filters)
i_retrace_filter = input.bool(false,title='Filter Retrace Level',group=g_filters)
//i_use_filters = input.bool(false, title='Enable Filters',group=g_filters)
//i_use_time_filter = input.bool(false, title='Time Restraint',group=g_filters)
i_deadzone = input.bool(false, title='Do not take trades during',group=g_filters)

// var int flag_d = na
// flag_d := m2_a<-10 and m2_a<m2_a[1] ? -1 : flag_d==-1 and m2_a>m2_a[1] ? 0 : flag_d==-1 and m2_a<0 ? -1 : na


float shortSL = 0.0
float longSL  = 0.0
float ratio_l = 0.0
float short_ticks = 0.0
float long_ticks = 0.0
int counter_trade = 0

var int hma_stage = 0
hma_stage := 
 h5_m>h6_m and h5_h6_conv_m==1 and m3_a_m>0 ? 1
 : h5_m>h6_m and h5_h6_conv_m==0 ? 2
 : h5_m>h6_m and h5_h6_conv_m==1 and h3_a_m<0 ? 3
 : h5_m<h6_m and h5_h6_conv_m==1 ? 4
 : h5_m<h6_m and h5_h6_conv_m==0 ? 5
 : h5_m<h6_m and h5_h6_conv_m==1 ? 6 : 0

plot(hma_stage, title='HMA Stage', color=color.new(blue,100))

plot(get_pip_distance(close, atr_long), title='ATR pip distance', color=color.new(blue,100) )

// ▒▒▒▒▒ TRADE LOGIC ▒▒▒▒▒ 
trade_dir() =>
    c           = close>open ? 1 : 0
    dir         = 0
    entryLong   = 0
    entryShort  = 0
    exitLong    = 0
    exitShort   = 0
    closeAll    = 0
    longSL      = 0.0
    cond        = ''
    cnt         = 0 // Counter Trend

    red_n       = h5>h6
    red_n2      = h7>h8
    green_n     = h5<h6
    green_n2    = h7<h8
    
    // Multi
    red_s       = h5_m>h6_m
    red_s2      = h7_m>h8_m
    green_s     = h5_m<h6_m
    green_s2    = h7_m<h8_m
    red_conv    = h5_h6_conv_m==1
    red_conv2   = h7_h8_conv_m==1
    green_conv  = h5_h6_conv_m==1
    green_conv2  = h7_h8_conv_m==1

    s1 = h8_a_m>0 and h8_m>h9_m
    s2 = h9_a_m<0 and h8_m<h9_m
    up_red = h8_a_m>0 and red_s ? true : false
    up_green = h8_a_m>0 and green_s ? true : false
    up_ph1 = up_red and  h5_h6_conv_m==1 and h3_a_m>0 // strong
    up_ph2 = up_red and h5_h6_conv_m==0 and h3_a_m>0 // strong
    up_ph3 = up_red and h5_h6_conv_m==0 and h3_a_m<0 // weakening
    up_ph4 = up_red and h5_h6_conv_m==1 and h3_a_m<0 // weakening
    up_ph5 = up_green and h5_h6_conv_m==1 and h3_a_m<0
    up_ph6 = up_green and h5_h6_conv_m==0 and h5_a_m<0
    up_ph7 = up_green and h5_h6_conv_m==0 and h5_a_m>0 // getting stronger
    up_ph8 = up_green and h5_h6_conv_m==1 and h5_a_m>0 // getting stronger

    // ATR
    atr_f_s = atr_upper<atr_short
    atr_f_l = atr_lower<atr_long

    // HH LL
    bars_hh = ta.barssince(ta.change(hh) )
    bars_ll = ta.barssince(ta.change(ll) )


    //btw_m7_m8 = ( h7>h8 and (close<h7) and (close>h8) ) or ( h7<h8 and (close>h7) and (close<h8) )? true : false
    btw_hma = ( h7>h8 and (close<h7) and (close>h8) ) or ( h7<h8 and (close>h7) and (close<h8) )? true : false
    

    // ▒▒▒▒▒ UPTREND ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒  

    // EMA's - Strong Uptrend
    if m2_m>m3_m and m3_m>m5_m and m5_m>m6_m 
     and not(h2_a_m<0 and h2_m>h3_m)
     and not(h3_a_m<0 and h3_m[2]>h5_m) 
     //and not(h9>m2_m and h9_a>5)
     //and not(green_s and h2_a_m<-5) 


        if c==0 and close<m3_m and h3_a_m>h3_a_m[1]
         and not(bb_zones_color==white)
            entryLong := 1
            cond := 'm3'

        // if m5_m > m6_m and m3_m > m5_m and m2_m>m3_m and c==0
        //  and open<m5_m and atr_lower<m5_m and h3_a>h3_a[1]
        //     entryLong := 1
        //     cond := 'm5'

        // and close>close[3]
        if c==0 and close<m5_m
            entryLong := 1
            cond := 'm5'

        if c==0 and close<h3_m
         and h9_a_m>0 and green_s and h3_m<h5_m
         and h3_a_m>0
            entryLong := 1
            cond := 'h3_m'

        // if bars_ll>50 and low>ll 
        //  and atr_short<ll
        //     entryLong := 1
        //     cond := 'atr'

        // if entryLong==1 and
        //  h5_a_m<0
        //     entryLong := 0

    if strategy.position_size == 0 

        if c==1 and close>h6_m 
         and green_s and m5_m>m6_m and f_retrace_level(close)>7
         and not(h5_a_m>0 and h6_a_m>0)
        //and not(m6_a_m<3)
            entryShort := 1
            cond := 'h6-b'

        // if c==1 and red_s and h2_a_m<0
        //  and close>h2_m
        //     entryShort := 1
        //     cond := 'hh-h2'


    // if m3_m>m8_m
    //  and green_s and red_s2  
    //  and h3_a_m>0 and h3_m<h7_m and h5_m<h7_m and close<h3
    //     entryLong := 1
    //     cond := 'h3'


    // EMA's - Backside of weakening Downtrend
    if i_long_back_trades and m3_a_m>0 and m5_m<m6_m and close<h9 and h9_a>0
     and not(h2_a_m<0 and h2_m>h3_m)
     and not(h3_a_m<0 and h3_m>h5_m)
     and not(h3_a_m<0 and h5_a_m<0 and h3_m[2]>h5_m)
     //and not(close>hl_mid) 
    //and not(m2_a_m<m2_a_m[1])

        if c==0 and close<m3_m
            entryLong := 1
            cond := 'm3-b'
            cnt := 1

        if c==0 and close<m5_m
            entryLong := 1
            cond := 'm5-b'
            cnt := 1

    // if close<PC_S2 and h3<PC_S2
    //  and m3_m<m6_m
    //     entryLong := 1
    //     cond := 'pc-m3'

    if strategy.position_size == 0 
     and c==1 and green_s and m5_m<m6_m
     and close<ll and h5_a_m>0
     //and not(h8>h9)
        entryLong := 1
        cond := 'll-b'
        cnt := 1

    



    // ▒▒▒▒▒ FILTERS ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ 

    // Percent
    if i_perc_filter and perc_change()>i_perc_filter_num
        entryLong := 0

    // Consolidation
    if i_cz_filter and get_pip_distance(cz_condMid, close)>i_cz_filter_num
        entryLong := 0

    // Highest Lowest Filter
    if i_perc_hhll and close>ll
        entryLong := 0

    // // ATR
    if i_atr_filter and get_pip_distance(close, atr_long)>i_perc_filter_num
        entryLong := 0

    // EMA angle
    if m1_a_m<-15 and m1_a_m<m1_a_m[1]
        entryLong := 0

    // // HMA
    // if green_s and h5_a_m<0 and h6_a_m<0
    //     entryLong := 0

    // if h9_a<0 and h8_a<0 and h8<h9
    //     entryLong := 0
        
    // if red_s and h5_a_m<0
    //     entryLong := 0

    // Fibo Filter
    if i_fibo_filter and fibo_color != red
        entryLong := 0

    // Retrace Level
    // if i_retrace_filter and retrace_level>4
    //     entryLong := 0

    // if ( i_retrace_filter and f_retrace_level(h5_m)>=7 and h5_m>h3_m and h3_a_m<-5 )
    //     entryLong := 0

    if i_retrace_filter and 
     h5_a_m<0 and f_retrace_level(h5_m)>=7 
     and h2_a_m<-7
        entryLong := 0




    // ▒▒▒▒▒ EXITS ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ 

    if high>hh and c==1
     and h5_m>h6_m and h3_m>h5_m and h3_m>h4_m and h5_m>h7_m
     and adx_m>di_plus_m and di_plus_m>di_minus_m
     and not(h7<h8)
     and h2_m>h3_m
     and h3_m>h8_m
     and h5_m>h8_m
     and low>h1_m
        exitLong := 1
        cond := 'Main'

    // HMA Multi - h2>h3 and h2_a>0 and and h3>h8
    // HMA + EMA multi - h3>m2 and h3>m6
    // Maybe use? h2_m>h3_m and h2_a>0 and 
    if h3_m>h8_m and c==1
     and h3_m>m2_m and h3_m>m6_m
     and high>h3_m and h3_a_m>-1
     and h2_m>h3_m
     and h3_m>h8_m
     and h5_m>h8_m
     and low>h1_m
     //and high>PC_R1
        exitLong := 1
        cond := 'h3-m'

    // if h9>hh2 and h9_a<0 and close>hh2 and c==1
    //     exitLong := 1
    //     cond := 'h9-hh2'







    // ▒▒▒▒▒ ENTRY Short ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ 

    //and h4_a<h4_a[1]
    if m5_m < m6_m and m3_m < m5_m and m2_m<m3_m and c==1
     and close>m3_m and h2>h3 
     and not(h3_a_m>0)
     and not(red_s2 and h7_a<0)
     and not(red_n and red_n2)
     and not(red_s and h6_a_m<0 and h5_a_m>0)
     //and not(h5_a_m>1)
     //and not(bb_lower_f<bb_lower)
        entryShort := 1
        cond := 'e3'

    if m5_m < m6_m and m3_m < m5_m and m2_m<m3_m and c==1
     and close>m5_m and h2>h3 
     and not(h3_a_m>0)
     and not(red_s2 and h7_a<0)
     and not(red_n and red_n2)
     and not(red_s and h6_a_m<0 and h5_a_m>0)
     //and not(h5_a_m>1)
     //and not(bb_lower_f<bb_lower)
        entryShort := 1
        cond := 'e5'


    // if m5_m < m6_m and m3_m < m5_m and m2_m<m3_m and c==1
    //  and red_n and green_n2 
    //  and h5>h7 and h5_a<0
    //  and h7_a>0
    //  and close>h5
    //     entryShort := 1
    //     cond := 'h5'

    // if m5_m < m6_m and m3_m < m5_m and m2_m<m3_m and c==1
    //  and h8_a<0 and green_n2 and close>m2_m
    //     entryShort := 1
    //     cond := 'e2'

        


    // ▒▒▒▒▒ FILTERS ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒


    if entryShort == 1

        if i_perc_filter and perc_change()>i_perc_filter_num
            entryShort := 0
            
        if i_perc_filter and perc_change()>i_perc_filter_num
            entryShort := 0

        // HMA
        // if h5_m<h6_m and h3_a_m>0 and h3_m<h6_m
        //     entryShort := 0

        // HMA - h7_a
        // if green_s2 and h7_a_m>0
        //     entryShort := 0

        // Retrace Level
        if i_retrace_filter and retrace_level<6
            entryShort := 0

        // ADX
        // if di_minus_m>di_plus_m
        //     entryShort := 0


    // ▒▒▒▒▒ EXIT SHORT ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒

    if strategy.position_size < 0

        // Main
        if low<ll and c==0
         and h5_m<h6_m and h3_m<h5_m and h3_m<h4_m
         and di_minus_m>di_plus_m
         and close<bb_lower
         and not(h7>h8)
         and h2_m<h3_m
         and h3_m<h8_m
         and h5_m<h8_m
         and low<h1_m
            exitShort := 1
            cond := 'Main'

        if h3_m<h8_m and c==0
         and h3_m<m2_m and h3_m<m6_m
         and low<h3_m and h3_a_m<-1
         and h2_m<h3_m
         and h3_m<h8_m
         and h5_m<h8_m
         and low<h1_m
         and not(get_pip_distance(atr_short, close)<50)
            exitShort := 1
            cond := 'h3-m'

        if green_conv==1
            exitShort := 0



    // ▒▒▒▒▒ COUNTER TRADES ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒




    // ▒▒▒▒▒ GLOBAL FILTERS ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒

    // Exclude New York close
    if i_deadzone and Session(Dz)
        entryLong := 0
        entryShort := 0

    // Trading Start Date
    if time<session_StartDate
        entryLong := 0
        entryShort := 0

    if i_long_trades==false
        entryLong := 0

    if i_short_trades==false
        entryShort := 0


    [entryLong,entryShort,exitLong,exitShort,closeAll,cond, cnt]

[entryLong,entryShort,exitLong,exitShort,closeAll,trade_label, cnt] = trade_dir()

counter_trade := cnt
// check if live trading or market closed
//plotshape(barstate.islastconfirmedhistory, title='Real Time', color=red, style=shape.circle, location=location.top)


plot(strategy.position_size < 0 ? -1 : strategy.position_size > 0  ? 1 : 0, title='Trade Direction', color=color.new(blue,100))


// longDiff := entryLong and strategy.opentrades == 0 ? longDiff * 100 : strategy.opentrades > 0 ? longDiff[1] : 0


// Short Plots
plot_trans = 90

short_close := entryShort and strategy.opentrades == 0 ? short_close : strategy.opentrades > 0 ? short_close[1] : 0
short_ticks := short_close - (syminfo.mintick * i_ticks)
shortSL     := entryShort and strategy.opentrades == 0 ? atr_short : strategy.opentrades > 0 ? shortSL[1] : 0
shortTS     := entryShort and strategy.opentrades == 0 ? shortTS : strategy.opentrades > 0 ? shortTS[1] : 0
shortTP     := entryShort and strategy.opentrades == 0 ? shortTP : strategy.opentrades > 0 ? shortTP[1] : 0
plot(strategy.opentrades > 0 and i_plot_trades ? shortPips : na, title="Short Pips", color=color.new(blue,100))
p_s_c       = plot( strategy.opentrades > 0 and i_plot_trades ? short_close : na, title="Short Close", color=color.new(green,plot_trans), linewidth=1, style=plot.style_linebr)
p_s_ticks   = plot( strategy.opentrades > 0 and i_plot_trades ? short_ticks : na, title="Short Ticks", color=lime, style=plot.style_linebr)
p_s_sl      = plot( strategy.opentrades > 0 and i_plot_trades ? shortSL : na, title="Short SL", color=color.new(red,plot_trans), linewidth=1, style=plot.style_linebr)
p_s_ts      = plot( strategy.opentrades > 0 and i_plot_trades ? shortTS : na, title="Short TS", color=color.new(green,plot_trans), linewidth=1, style=plot.style_linebr)
p_s_tp      = plot( strategy.opentrades > 0 and i_plot_trades ? shortTP : na, title="Short TP", color=color.new(green,plot_trans), linewidth=1, style=plot.style_linebr)
fill(p_s_sl, p_s_c,title='Fill Short SL',color=color.new(red,plot_trans) )
fill(p_s_ts, p_s_c,title='Fill Short TS',color=color.new(lime,plot_trans) )
fill(p_s_ts, p_s_tp,title='Fill Short TP',color=color.new(green,plot_trans) )

// Long Plots
long_close  := entryLong and strategy.opentrades == 0 ? long_close : strategy.opentrades > 0 ? long_close[1] : 0
long_ticks  := long_close + (syminfo.mintick * i_ticks)
longSL      := entryLong and strategy.opentrades == 0 ? atr_long : strategy.opentrades > 0 ? longSL[1] : 0
longTS      := entryLong and strategy.opentrades == 0 ? longTS : strategy.opentrades > 0 ? longTS[1] : 0
longTP      := entryLong and strategy.opentrades == 0 ? longTP : strategy.opentrades > 0 ? longTP[1] : 0
ratio_l     := ( close  - long_close )
//plot( sl_short  ,"sl_short ", color=color.new(red,100) )

// var int decimals = int(math.log10(1/syminfo.mintick))
// decimals := decimals == 2 ? 1 : decimals == 0 ? 1 : decimals
plot(strategy.opentrades > 0 and i_plot_trades ? syminfo.mintick : na, title="Min Ticks", color=color.new(blue,100))
plot(strategy.opentrades > 0 and i_plot_trades ? math.pow(10, decimals) * syminfo.mintick : na, title="Ticks Converted", color=color.new(blue,100))
plot(strategy.opentrades > 0 and i_plot_trades ? decimals : na, title="Decimals", color=color.new(blue,100))
plot(strategy.opentrades > 0 and i_plot_trades ? longDiff : na, title="Long Diff", color=color.new(yellow,100))
plot(strategy.opentrades > 0 and i_plot_trades ? longPips : na, title="Long Pips", color=color.new(yellow,100))
//plot(strategy.opentrades > 0 and i_plot_trades ? longDiff / syminfo.mintick : na, title="Ticks to Pips")
p_l_dist    = plot( strategy.opentrades > 0 and i_plot_trades ? get_pip_distance(long_close,longSL) * (math.pow(10, decimals) * syminfo.mintick) : na, title="SL in Pips", color=color.new(blue,100) )
p_l_c       = plot( strategy.opentrades > 0 and i_plot_trades ? long_close : na, title="Long Close", color=color.new(red,plot_trans), linewidth=1, style=plot.style_linebr)
p_l_ticks   = plot( strategy.opentrades > 0 and i_plot_trades ? long_ticks : na, title="Long Ticks", color=color.new(lime,plot_trans), style=plot.style_linebr)
p_l_sl      = plot( strategy.opentrades > 0 and i_plot_trades ? longSL : na, title="Long SL", color=color.new(red,plot_trans), linewidth=1, style=plot.style_linebr)
p_l_ts      = plot( strategy.opentrades > 0 and i_plot_trades ? longTS : na, title="Trailing Stop", color=color.new(yellow,plot_trans), linewidth=1, style=plot.style_linebr)
p_l_tp      = plot( strategy.opentrades > 0 and i_plot_trades ? longTP : na, title="Long TP", color=color.new(green,plot_trans), linewidth=1, style=plot.style_linebr)

//psize       = plot(pl_size, title="Position Size")
//p_l_ratio   = plot( ratio_l, title="Ratio", color=color.new(red,75), linewidth=1, style=plot.style_linebr)
fill(p_l_sl,p_l_c, title='Fill Long SL', color=color.new(red,plot_trans))
fill(p_l_ts,p_l_c, title='Fill Long Trailing Stop', color=color.new(lime,plot_trans) )
fill(p_l_tp,p_l_ts, title='Fill Long Take Profit', color=color.new(green,plot_trans))



cd=close>open?1:0

if Session(Dz) and i_deadzone
    entryShort := 0
    entryLong := 0

// Long

// if high<atr_lower and entryLong
//     entryLong := 0

// if  atr_upper>m2 and entryLong
//     entryLong := 0

var int trade_num = 0
if (entryLong)
    //pd_level = get_retrace_level(close)
    // * decimals , "#.00"
    trade_num := strategy.opentrades == 0 ? trade_num + 1 : trade_num
	strategy.entry("L", strategy.long, qty = pl_size, comment=trade_label + ' #' 
     + str.tostring(trade_num) + ' ' 
     + str.tostring( longPips, '#.##' ) 
     //+ str.tostring( math.round_to_mintick(longDiff)* 10 * decimals) + ' '
     //+ str.tostring( get_pip_distance(long_close, close) )
     //+ str.tostring( pd_level )
     )
    //strategy.exit('EXIT L', 'L', stop = longSL)
        
else
	strategy.cancel("S")

if (exitLong ) //and counter_trade==0
    exit_com = trade_label + ' ' + str.tostring( get_pip_distance(long_close, close) )
	strategy.close("L", comment = exit_com)

// Short
if (entryShort)
    trade_num := strategy.opentrades == 0 ? trade_num + 1 : trade_num
	strategy.entry("S", strategy.short, qty = ps_size, comment=trade_label 
     + ' #' + str.tostring(trade_num) + ' '
     + str.tostring( shortPips, '#.##' )  
     //+ str.tostring( math.round_to_mintick(shortDiff * 100))
     )
    //strategy.exit('EXIT S', 'S', stop=shortSL)
else
	strategy.cancel("S")

if (exitShort)
    exit_com = trade_label + ' ' + str.tostring( get_pip_distance(short_close, close) )
	strategy.close("S", comment = exit_com)

// Filters
// if Session(Dz) and i_deadzone
//     strategy.close_all(comment = "close all entries")

in_profit() =>
    if strategy.position_size > 0 and close > longTS
        true
    else if strategy.position_size < 0 and close < shortTS
        true
    else
        false

getCurrentStage() =>
    var stage = 0
    if strategy.position_size == 0
        stage := 0
        stage
    if stage == 0 and strategy.position_size != 0
        stage := 1
        stage
    else if stage == 1 
        if strategy.position_size > 0 and close > longTS
            stage := 2
        if strategy.position_size < 0 and close < shortTS
            stage := 2
    else if stage == 2
        if strategy.position_size > 0 and close > longTP
            stage := 3
        if strategy.position_size < 0 and close < shortTP
            stage := 3
            stage
    stage

curStage = getCurrentStage()
plot(curStage,title='Current Stage', color=color.new(color.blue,100))

float stopLevel = na
string comment  = ''
float limit     = na //strategy.position_size > 0 and close>longTP and m2>m4 ? close : strategy.position_size < 0 and close<shortTP and m2<m4 ? close : na
string win     = 'Take Profit'
string loss    = 'Loss'
string bkeven  = 'Break Even'
string ts      = 'Trailing Stop'

if curStage == 1
    // Comment Strings

    if strategy.position_size > 0
        stopLevel := longSL
        comment   := close < long_close ? loss : win
        limit     := high>longTP ? high : na
    else if strategy.position_size < 0
        stopLevel := shortSL
        comment   := close > short_close ? loss : win
        limit     := low<shortTP ? low : na
        
    strategy.exit('x', stop=stopLevel, limit=limit, comment=comment)

else if curStage == 2

    if strategy.position_size > 0
        stopLevel := long_close + (syminfo.mintick * i_ticks)
        pips = str.tostring( get_pip_distance(long_close, close) )  
        comment   := close <= longTS ? bkeven + ' ' + pips  : win + ' ' + pips
        limit     := high>longTP ? high: na
        //get_pip_distance(long_close, close)>60 ? high 
    else if strategy.position_size < 0
        stopLevel := short_close - (syminfo.mintick * i_ticks)
        pips       = str.tostring( get_pip_distance(short_close, close) )
        comment   := close >= shortTS ? bkeven + ' ' + pips : win + ' ' + pips
        limit     := low<shortTP ? low : na

    strategy.exit('x', stop=stopLevel, limit=limit, comment=comment)

else if curStage == 3

    if strategy.position_size > 0
        stopLevel := longTS
        pips      = str.tostring( get_pip_distance(long_close, close) )
        comment   := close >= longTS and close<longTP ? ts + ' ' + pips : win + ' ' + pips
        limit     := high>longTP ? high : na
    else if strategy.position_size < 0
        stopLevel := shortTS
        pips       = str.tostring( get_pip_distance(short_close, close) )
        comment   := close <= shortTS and close>shortTP ? ts + ' ' + pips : win  + ' ' + pips
        limit     := low<shortTP ? low : na

    strategy.exit('x', stop=stopLevel, limit=limit, comment=comment)

else
    strategy.cancel('x')


bars_long = ta.barssince(strategy.position_size > 0)
plot(bars_long, title='Bars since long', color=color.new(blue,98))
bars_short = ta.barssince(strategy.position_size < 0)
plot(bars_short, title='Bars since short', color=color.new(blue,98))