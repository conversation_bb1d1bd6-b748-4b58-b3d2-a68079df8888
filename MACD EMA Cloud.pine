//@version=5
strategy("MACD EMA Cloud Strategy", overlay=true, precision=4, initial_capital=10000, default_qty_value=10, currency=currency.USD, process_orders_on_close=true, pyramiding=1,  max_labels_count=500)

red = #ff0062
orange = #ff9800
yellow = #FFFF00
green = #4caf50
lime = #00E676
aqua = #00bcd4
blue = #2962ff
dark_blue = #2962ff
violet = #814dff
white = #ffffff
gray = #707070
black = #000000

angle(_src, len) =>
    rad2degree = 180 / 3.14159265359  //pi 
    ang = rad2degree * math.atan((_src[0] - _src[1]) / ta.atr(len))
    ang




// EMA CLOUD
g_cloud = 'EMA Cloud ----------------------------------------------------'
fast = input(20, title="Fast EMA",group=g_cloud)
mid = input(50, title="Fast EMA",group=g_cloud)
slow = input(200, title="Slow EMA",group=g_cloud)
show_ma = input.bool(true,title="Show MA's")
emaFast = ta.ema(close, fast)
emaMid = ta.ema(close, mid)
emaSlow = ta.ema(close, slow)


p1 = plot(show_ma ? emaFast:na, title="Fast MA", style=plot.style_linebr, linewidth=2, color=white)
p2 = plot(show_ma ? emaMid:na, title="Mid MA", style=plot.style_linebr, linewidth=2, color=blue)
p3 = plot(show_ma ? emaSlow:na, title="Slow MA", style=plot.style_linebr, linewidth=4, color=color.new(color.blue, 100))
fill(p1, p3, color=#fccbcd, transp=70)

emaFast_a = angle(emaFast,3) 
emaMid_a = angle(emaMid,3) 
emaSlow_a = angle(emaSlow,3) 
plot(show_ma ? emaFast_a:na, title="Fast A", style=plot.style_linebr, color=emaFast_a>0 ? color.new(green,100): color.new(red,100))
plot(show_ma ? emaMid_a:na, title="Mid A", style=plot.style_linebr, color=emaMid_a>0 ? color.new(green,100): color.new(red,100))
plot(show_ma ? emaSlow_a:na, title="Slow A", style=plot.style_linebr, color=emaSlow_a>0 ? color.new(green,100): color.new(red,100))





// MACD
g_macd = 'MACD ----------------------------------------------------'
fast_length = input(title="Fast Length", defval=12,group=g_macd)
slow_length = input(title="Slow Length", defval=26,group=g_macd)
src = input(title="Source", defval=close,group=g_macd)
signal_length = input.int(title="Signal Smoothing",  minval = 1, maxval = 50, defval = 9,group=g_macd)
sma_source = "EMA" //input.string(title="Oscillator MA Type",  defval="EMA", options=["SMA", "EMA"])
sma_signal = "EMA" //input.string(title="Signal Line MA Type", defval="EMA", options=["SMA", "EMA"])
// Calculating
fast_ma = sma_source == "SMA" ? ta.sma(src, fast_length) : ta.ema(src, fast_length)
slow_ma = sma_source == "SMA" ? ta.sma(src, slow_length) : ta.ema(src, slow_length)
macd = fast_ma - slow_ma
signal = sma_signal == "SMA" ? ta.sma(macd, signal_length) : ta.ema(macd, signal_length)
hist = macd - signal






// Supertrend
g_super = 'Supertrend ----------------------------------------------------'
i_show1 = input.bool(true,title='Sup 1',group=g_super)
i_show2= input.bool(true,title='Sup 2',group=g_super)
i_show3 = input.bool(true,title='Sup 3',group=g_super)

super_src = input(hl2, title='Source',group=g_super)
Multiplier = input.float(title='ATR Multiplier', step=0.1, defval=3.0,group=g_super)
changeATR = input(title='Change ATR Calculation Method ?', defval=true,group=g_super)
showsuper = input(title='Show Trend ?', defval=true,group=g_super)
showsignals = input(title='Show Signals ?', defval=true,group=g_super)
showlabels = input(title='Show Labels ?', defval=true,group=g_super)

g_multi = 'Multi'
inl_multi = 'multi'
i_time_sup = input.timeframe(title='Timeframe', defval='', group=g_multi)

supertend(p, m) =>
    atr2 = ta.sma(ta.tr, p)
    atr = changeATR ? ta.atr(p) : atr2
    up = super_src - m * atr
    up1 = nz(up[1], up)
    up := close[1] > up1 ? math.max(up, up1) : up
    dn = super_src + m * atr
    dn1 = nz(dn[1], dn)
    dn := close[1] < dn1 ? math.min(dn, dn1) : dn
    trend = 1
    trend := nz(trend[1], trend)
    trend := trend == -1 and close > dn1 ? 1 : trend == 1 and close < up1 ? -1 : trend
    b_sig = trend == 1 and trend[1] == -1

    s_sig = trend == -1 and trend[1] == 1

    [trend, up, dn, b_sig, s_sig]


[sup_fast, sup_fast_up, sup_fast_dn, sup_fast_buy, sup_fast_sell] = request.security(syminfo.tickerid, i_time_sup, supertend(10, 1) ) 
[trend, up, dn, b_sig, s_sig] =  request.security(syminfo.tickerid, i_time_sup, supertend(7, 5) ) 
//[trend2, up2, dn2, b_sig2, s_sig2] = supertend(11, 2)


// upPlot3 = plot(sup_fast == 1 ? sup_fast_up : na, title='Up Trend', style=plot.style_linebr, linewidth=1, color=i_show3 ? green:na )
// dnPlot3 = plot(sup_fast == 1 ? na : sup_fast_dn, title='Down Trend', style=plot.style_linebr, linewidth=1, color=i_show3 ?red :na )
plotshape(sup_fast_sell and showsuper and showsignals? sup_fast_dn : na, title='DownTrend Begins', location=location.absolute, style=shape.circle, size=size.tiny, color=i_show3 ? red:na)
plotshape(sup_fast_sell and showsuper and showlabels ? sup_fast_dn : na, title='Sell', text='Sell', location=location.absolute, style=shape.labeldown, size=size.tiny, color=i_show3 ?red :na, textcolor=i_show3 ? white : na)

plotshape(sup_fast_buy and showsuper and showsignals? sup_fast_up : na, title='UpTrend Begins', location=location.absolute, style=shape.circle, size=size.tiny, color=i_show3 ? green:na )
plotshape(sup_fast_buy and showsuper and showlabels ? sup_fast_up : na, title='Buy', text='Buy', location=location.absolute, style=shape.labelup, size=size.tiny, color=i_show3 ? green : na , textcolor=i_show3 ? white : na)


upPlot = plot(trend == 1 ? up : na, title='Up Trend', style=plot.style_linebr, linewidth=1, color=i_show1 ? green:na )
dnPlot = plot(trend == 1 ? na : dn, title='Down Trend', style=plot.style_linebr, linewidth=1, color=i_show1 ?red :na )
plotshape(s_sig and showsuper and showsignals? dn : na, title='DownTrend Begins', location=location.absolute, style=shape.circle, size=size.tiny, color=i_show1 ? red:na)
plotshape(s_sig and showsuper and showlabels ? dn : na, title='Sell', text='Sell', location=location.absolute, style=shape.labeldown, size=size.tiny, color=i_show1 ?red :na, textcolor=i_show1 ? white : na)
plotshape(b_sig and showsuper and showsignals? up : na, title='UpTrend Begins', location=location.absolute, style=shape.circle, size=size.tiny, color=i_show1 ? green:na )
plotshape(b_sig and showsuper and showlabels ? up : na, title='Buy', text='Buy', location=location.absolute, style=shape.labelup, size=size.tiny, color=i_show1 ? green : na , textcolor=i_show1 ? white : na)


// upPlot2 = plot(trend2 == 1 ? up2 : na, title='Up Trend', style=plot.style_linebr, linewidth=1, color=i_show2 ? green:na )
// dnPlot2 = plot(trend2 == 1 ? na : dn2, title='Down Trend', style=plot.style_linebr, linewidth=1, color=i_show2 ?red :na )
// plotshape(b_sig2 ? up2 : na, title='UpTrend Begins', location=location.absolute, style=shape.circle, size=size.tiny, color=i_show2 ? green:na )
// plotshape(b_sig2 and showsignals ? up2 : na, title='Buy', text='Buy', location=location.absolute, style=shape.labelup, size=size.tiny, color=i_show2 ? green : na , textcolor=i_show2 ? white : na)
// plotshape(s_sig2 ? dn2 : na, title='DownTrend Begins', location=location.absolute, style=shape.circle, size=size.tiny, color=i_show2 ? red:na)
// plotshape(s_sig2 and showsignals ? dn2 : na, title='Sell', text='Sell', location=location.absolute, style=shape.labeldown, size=size.tiny, color=i_show2 ?red :na, textcolor=i_show2 ? white : na)




g_consol = 'Consolidation Zones ----------------------------------------------------'
show_consol = input.bool(true, title="Show Consolidation", group=g_consol)
prd = input.int(defval=10, title='Loopback Period', minval=2, maxval=50, group=g_consol)
conslen = input.int(defval=5, title='Min Consolidation Length', minval=2, maxval=20, group=g_consol)
paintcons = input(defval=true, title='Paint Consolidation Area ', group=g_consol)
zonecol = input(defval=color.new(color.blue, 70), title='Zone Color', group=g_consol)

float hb_ = ta.highestbars(prd) == 0 ? high : na
float lb_ = ta.lowestbars(prd) == 0 ? low : na
var int dir = 0
float zz = na
float pp = na

iff_1 = lb_ and na(hb_) ? -1 : dir
dir := hb_ and na(lb_) ? 1 : iff_1
if hb_ and lb_
    if dir == 1
        zz := hb_
        zz
    else
        zz := lb_
        zz
else
    iff_1 = lb_ ? lb_ : na
    zz := hb_ ? hb_ : iff_1
    zz

for x = 0 to 1000 by 1
    if na(close) or dir != dir[x]
        break
    if zz[x]
        if na(pp)
            pp := zz[x]
            pp
        else
            if dir[x] == 1 and zz[x] > pp
                pp := zz[x]
                pp
            if dir[x] == -1 and zz[x] < pp
                pp := zz[x]
                pp

var int conscnt = 0
var float condhigh = na
var float condlow = na
float H_ = ta.highest(conslen)
float L_ = ta.lowest(conslen)
var line upline = na
var line dnline = na
bool breakoutup = false
bool breakoutdown = false

if ta.change(pp)
    if conscnt > conslen
        if pp > condhigh
            breakoutup := true
            breakoutup
        if pp < condlow
            breakoutdown := true
            breakoutdown
    if conscnt > 0 and pp <= condhigh and pp >= condlow
        conscnt += 1
        conscnt
    else
        conscnt := 0
        conscnt
else
    conscnt += 1
    conscnt

if conscnt >= conslen
    if conscnt == conslen
        condhigh := H_
        condlow := L_
        condlow
    else
        line.delete(upline)
        line.delete(dnline)
        condhigh := math.max(condhigh, high)
        condlow := math.min(condlow, low)
        condlow

    upline := line.new(bar_index, condhigh, bar_index - conscnt, condhigh, color=color.red, style=line.style_dashed)
    dnline := line.new(bar_index, condlow, bar_index - conscnt, condlow, color=color.lime, style=line.style_dashed)
    dnline


fill(plot(show_consol and condhigh ? condhigh : na, color=na, style=plot.style_stepline), plot(show_consol and condlow ? condlow : na, color=na, style=plot.style_stepline), color=paintcons and conscnt > conslen ? zonecol : color.new(color.white, 100), transp=90)

condMid = condhigh or condlow ? (condhigh + condlow) * 0.5 : na
plot(show_consol and condMid ? condMid : na, color=#ffff00, style=plot.style_stepline)
buy_cond = show_consol and condMid<emaSlow and condMid<emaFast and close<condMid and close<emaFast and close<open
plotshape(buy_cond,title="Buy 1",color=green ,style=shape.circle,location=location.bottom)





g_trading = 'Trading ----------------------------------------------------'
i_equity = input.string("Initial", options=["Initial", "Equity"], title="Initial or Equity",group=g_trading)
account_size = input.int(50000, minval=1000, title='Account Size',group=g_trading)
show_st = input.bool(title='Show Stop Loss', defval=false, group=g_trading)

// m_state     = macd<signal and signal>0 ? -1 : macd>signal and signal<0 ? 1 : na
// plot(m_state,title='macd state')


trade_dir() =>
    candle      = close>open ? 1 : 0
    dir         = 0
    entryLong   = 0
    entryShort  = 0
    exitLong    = 0
    exitShort   = 0
    closeAll    = 0

    m_state     = macd<signal and signal>0 ? -1 : macd>signal and signal<0 ? 1 : na

    // Sell Downtrend
    if emaFast<emaSlow

        // Supertrend
        if sup_fast_sell and macd>signal
         and not(emaFast_a>0)
         and not(emaMid_a>-2.5)
         //and not( hist<0 )
         //and not(emaSlow_a>2.5)
            entryShort := 1

        if sup_fast_buy
            exitShort := 1

        // MACD
        // if close<emaFast and m_state == -1
        //     entryShort := 1

        // if close>emaSlow and macd>emaSlow
        //     entryShort := 1

    // Buy Uptrend
    // if emaFast>emaSlow and close<emaSlow and candle==0
    //     entryLong := 1

    // if emaFast>emaSlow and close>emaFast and m_state == 1
    //     entryLong := 1


    [entryLong,entryShort,exitLong,exitShort,closeAll]

[entryLong,entryShort,exitLong,exitShort,closeAll] = trade_dir()


// STOP LOSS
g_sl = 'Stop Loss ----------------------------------------------------'
i_tpFactor  = input.float(4, 'Target Profit',group=g_sl)
Multip      = input.float(defval=1.5, title='Stop Loss',group=g_sl)
i_use_pos   = input.bool(true,title="Use Percentage based Position Size",group=g_sl)
i_pctStop   = input(1.0, '% of Risk to Starting Equity Use to Size Positions',group=g_sl) / 100
atr_src     = input.string('close', title='Close or Wicks', options=['close', 'wicks'],group=g_sl)
i_sl_type   = input.string("ATR", title="SL Type", options=["Lowest","ATR"],group=g_sl)
i_bkcandles = input.int(11, title="Lowerest range - Number of candles",group=g_sl)
show_sl     = input.bool(false,title="Stop Loss",group=g_sl)
show_ts     = input.bool(false,title="Trailing Stop",group=g_sl)

float qty_value = switch syminfo.type
    "forex" => 100000.0
    "futures" => 10.0
    "index" => 10.0
    "crypto" => 1
    => 1
    
stop_loss()=>

    float sl_short  = na
    float shortDiff = na
    float shortTP   = na
    float pvalShort = na
    float shortPOS   = na
    float sl_long   = na
    float longDiff  = na
    float longTP    = na
    float longTS    = na
    float pvalLong  = na
    float longPOS   = na

    if i_sl_type == "ATR"
        atr_len = 14
        ATR = ta.atr(atr_len)
        // Short
        sl_short    := (atr_src =='close' ? close : high) + ATR * Multip
        shortDiff   := math.abs(close - sl_short)
        shortTP     := close - (i_tpFactor * shortDiff)
        pvalShort   := (i_equity=="Initial" ? strategy.initial_capital : strategy.equity) * i_pctStop / (shortDiff / close)
        shortPOS    := i_use_pos ? pvalShort / close : qty_value 
        // Long
        sl_long     := (atr_src =='close' ? close : low) - ATR * Multip
        longDiff    := math.abs(close - sl_long)
        longTP      := close + (i_tpFactor * longDiff)
        longTS      := close + (1.5 * longDiff)
        pvalLong    := (i_equity=="Initial" ? strategy.initial_capital : strategy.equity) * i_pctStop / (longDiff / close)
        longPOS     := i_use_pos ? pvalLong / close : qty_value 

    else
        // Short
        sl_short    := ta.highest(high, i_bkcandles)[1]
        shortDiff   := math.abs(close - sl_short)
        shortTP     := close - (i_tpFactor * shortDiff)
        pvalShort   := (i_equity=="Initial" ? strategy.initial_capital : strategy.equity) * i_pctStop / (shortDiff / close)
        shortPOS    := i_use_pos ? pvalShort / close : qty_value 

        // Long
        sl_long     := ta.lowest(low, i_bkcandles)[1]
        longDiff    := math.abs(close - sl_long)
        longTP      := close + (i_tpFactor * longDiff)
        

    [sl_short,sl_long, shortTP, longTP, close, close, shortPOS, longPOS]

float shortSL = 0.0
float longSL  = 0.0
float closeSL = 0.0
float ratio_l = 0.0
[atr_short, atr_long, shortTP, longTP, long_close, short_close, shortPOS, longPOS] = stop_loss()

shortSL     := entryShort and strategy.opentrades == 0 ? atr_short : strategy.opentrades > 0 ? shortSL[1] : 0
shortTP     := entryShort and strategy.opentrades == 0 ? shortTP : strategy.opentrades > 0 ? shortTP[1] : 0
short_close := entryShort and strategy.opentrades == 0 ? short_close : strategy.opentrades > 0 ? short_close[1] : 0
p_s_sl      = plot( strategy.opentrades > 0 ? shortSL : na, title="Short SL", color=color.new(red,75), linewidth=1, style=plot.style_linebr)
p_o_tp      = plot( strategy.opentrades > 0 ? short_close : na, title="Short Open", color=color.new(green,75), linewidth=1, style=plot.style_linebr)
p_s_tp      = plot( strategy.opentrades > 0 ? shortTP : na, title="Short TP", color=color.new(green,75), linewidth=1, style=plot.style_linebr)
p_s_tsl     = plot( strategy.opentrades > 0 ? atr_short : na, title="Short Trailing", color=color.new(red,75), linewidth=1, style=plot.style_linebr)
fill(p_s_sl, p_o_tp,color=color.new(red,75) )
fill(p_s_tp, p_o_tp,color=color.new(green,75) )
//fill(p_s_tsl, p_o_tp,color=color.new(green,75) )


longSL      := entryLong and strategy.opentrades == 0 ? atr_long : strategy.opentrades > 0 ? longSL[1] : 0
longTP      := entryLong and strategy.opentrades == 0 ? longTP : strategy.opentrades > 0 ? longTP[1] : 0
long_close  := entryLong and strategy.opentrades == 0 ? long_close : strategy.opentrades > 0 ? long_close[1] : 0
ratio_l     := ( close  - long_close )
p_l_sl      = plot( strategy.opentrades > 0 ? longSL : na, title="Long SL", color=color.new(red,75), linewidth=1, style=plot.style_linebr)
p_l_tp      = plot( strategy.opentrades > 0 ? longTP : na, title="Long TP", color=color.new(green,75), linewidth=1, style=plot.style_linebr)
p_l_c       = plot( strategy.opentrades > 0 ? long_close : na, title="Long Close", color=color.new(red,75), linewidth=1, style=plot.style_linebr)
//p_l_t       = plot( strategy.opentrades > 0 ? atr_long : na, title="Long Trailing", color=color.new(yellow,75), linewidth=1, style=plot.style_linebr)
plot(shortPOS, title="Short Position Size")
plot(longPOS, title="Long Position Size")
//p_l_ratio   = plot( ratio_l, title="Ratio", color=color.new(red,75), linewidth=1, style=plot.style_linebr)
fill(p_l_sl,p_l_c,color=color.new(red,75))
fill(p_l_tp,p_l_c,color=color.new(green,75))
//fill(p_l_tp,p_l_t,color=color.new(green,75))

// ATR
plot(show_sl? atr_long  : na,"ATR + ", color=green)
plot(show_sl? atr_short : na,"ATR - ", color=red)

//bearFib = bearFib(float priceLow, float priceHigh, float fibRatio = 0.382) => (priceHigh - priceLow) * fibRatio + priceLow


//close>longTP
//close>longTP ? long_close: longSL
cd=close>open?1:0

// Short
if (entryShort)
	strategy.entry("S", strategy.short,qty = shortPOS, stop=open, comment="S")
    strategy.exit('EXIT S', 'S', stop=shortSL)
// if (exitShort)
//     strategy.exit('EXIT S', 'S', limit=open)

if (close<shortTP)
	strategy.close("S", comment = "Ex S")


// Long
if (entryLong)
	strategy.entry("L", strategy.long, comment="L", qty = longPOS)
    strategy.exit('EXIT L', 'L', stop = longSL )

if (close>longTP)
	strategy.close("L", comment = "Close L")


