//@version=4
study(title="Ichimoku Cloud", shorttitle="Ichimoku", overlay=true)

yellow = #FFFF00
red = #FF0000

use_fill = input(title="Use Fill", type=input.bool, defval=false)

conversionPeriods = input(9, minval=1, title="Conversion Line Length")
basePeriods = input(30, minval=1, title="Base Line Length") // 26
laggingSpan2Periods = input(52, minval=1, title="Lagging Span 2 Length")
displacement = input(26, minval=1, title="Displacement")
donchian(len) => avg(lowest(len), highest(len))
c_line = donchian(conversionPeriods)
b_line = donchian(basePeriods)
leadLine1 = avg(c_line, b_line)
leadLine2 = donchian(laggingSpan2Periods)
close_offets = close[displacement] 
c= plot(c_line, color=c_line>b_line?red:yellow, title="Conversion Line")
b= plot(b_line, color=#991515, title="Base Line")
plot(close_offets, color=#00ff00, title="Close 26")
plot(close, offset = -displacement + 1, color=#459915, title="Lagging Span")
p1 = plot(leadLine1, offset = displacement - 1, color=color.yellow,title="Lead 1")
p2 = plot(leadLine2, offset = displacement - 1, color=color.red,title="Lead 2")
fill(c, b, color = use_fill ? c_line>b_line ? red : yellow : na)

