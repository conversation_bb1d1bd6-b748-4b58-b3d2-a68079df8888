entryLong Notes

if going long and SL is higher than candle don't take. Use it as a filter which mean moving the function above the trading function

Check when m2<m4 and when m4_a<0

Maybe only take entries
    1. when m1_a>m2_a
    2. m4_a>0
    3. m2_a<m4_a
    4. Different logic when candles between m7 and m8
    5. Temporarily removed ability for entries when green and m7_a>0 - Or change m8==700 instead of 500
    6. Exit when m8_a<0 or green - To help with fake outs

Add Stop Loss and Trailing Stop logic
    Traling Stop @ 2 percent

exitLong
if m1_a>20 maybe wait for red rsi dot (5 min @ 11 or current @ 20)