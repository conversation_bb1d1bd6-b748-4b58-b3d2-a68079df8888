//@version=5
indicator('Long Shadows - V2', overlay=true,precision=6)
C_LongLowerShadowPercent = input.float(75.0, title='Shadow Amount')
C_ShadowPercent = input.float(5.0, title='Size of Shadows')  // size of shadows
label_color_bullish = input(color.blue, "Label Color Bullish")
label_color_bearish = input(color.red, "Label Color Bearish")
patternLabelPosLow = low - (ta.atr(30) * 0.6)
patternLabelPosHigh = high + (ta.atr(30) * 0.6)

C_Range = high-low
C_BodyHi = math.max(close, open)
C_BodyLo = math.min(close, open)
C_Body = C_BodyHi - C_BodyLo


C_UpShadow = high - C_BodyHi
C_DnShadow = C_BodyLo - low
C_up_value = C_ShadowPercent / 100 * C_Body
C_HasUpShadow = C_UpShadow > C_ShadowPercent / 100 * C_Body
C_HasDnShadow = C_DnShadow > C_ShadowPercent / 100 * C_Body

C_LongLowerShadowBullishNumberOfCandles = 1
C_LongLowerShadowBullish = C_DnShadow > C_Range/100*C_LongLowerShadowPercent
plot(C_up_value, title='C_up_value',color=color.new(color.blue,100))

if C_LongLowerShadowBullish
    label.new(bar_index, patternLabelPosLow, text="LLS", style=label.style_label_up, color = label_color_bullish, textcolor=color.white)


C_LongUpperShadowBearishNumberOfCandles = 1
C_LongShadowPercent = 75.0
C_LongUpperShadowBearish = C_UpShadow > C_Range/100*C_LongShadowPercent
//plot(C_HasDnShadow, title='C_HasDnShadow',color=color.new(color.red,100))

if C_LongUpperShadowBearish
    label.new(bar_index, patternLabelPosHigh, text="LUS", style=label.style_label_down, color = label_color_bearish, textcolor=color.white)
