//@version=5
strategy('TB - 2 min - June 5',
 overlay=true,
 precision=6,
 currency=currency.USD,
 initial_capital=12500,
 default_qty_value=10,
 commission_type=strategy.commission.cash_per_order,
 commission_value = 30,
 process_orders_on_close=true,
 pyramiding=1,
 max_labels_count=500)

red = #ff0062
orange = #ff9800
yellow = #FFFF00
green = #4caf50
lime = #50e600
aqua = #00bcd4
blue = #2962ff
dark_blue = #2962ff
violet = #814dff
white = #ffffff
gray = #707070
black = #000000

// Angle
angle(_src, len) =>
    rad2degree = 180 / 3.14159265359  //pi 
    ang = rad2degree * math.atan((_src[0] - _src[1]) / ta.atr(len))
    ang

// Pip Value
var int decimals = int(math.log10(1/syminfo.mintick))
decimals := decimals == 2 ? 1 : decimals == 0 ? 1 : decimals
get_pip_value(point1, point2, abs_value) =>
    diff_points = abs_value ? math.abs( (point1 - point2) ) : point1 - point2
    pip_value = decimals>3 ? diff_points * (math.pow(10, decimals) * syminfo.mintick) : diff_points / syminfo.mintick / 10

// Change
perc_change() =>
    perc = math.abs((1 - close[1] / close) * 10000)
    perc


is_between(p1, p2) =>
    is_inside = p1>p2 ? (close<p1) and (open<p1) and (close>p2) and (open>p2) : (close>p1) and (open>p1) and (close<p2) and (open<p2)
    is_inside
    

    
//
// ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ //
// Moving Averages
// ------------------------------------------------------------------------------------------------------------------

// Slow MA
g_slow_ma = 'Slow MA\' s ----------------------------------------------------'
i_slow_ma_time = input.timeframe('240', "MA Slow Resolution", group=g_slow_ma ) // 4 hours
i_slow_ma_len1 = input.int(5, title='MA length', group=g_slow_ma )
i_slow_ma_len2 = input.int(10, title='MA length 2', group=g_slow_ma )
i_slow_ma_lookback = input.int(1,title='Slow MA lookback', group=g_slow_ma )
m1_slow = request.security(syminfo.tickerid, i_slow_ma_time, ta.ema(close,i_slow_ma_len1)[i_slow_ma_lookback],lookahead=barmerge.lookahead_on )
m1_slow_a = request.security(syminfo.tickerid, i_slow_ma_time, angle(m1_slow,14)[i_slow_ma_lookback],lookahead=barmerge.lookahead_on ) 
m2_slow = request.security(syminfo.tickerid, i_slow_ma_time, ta.ema(close,i_slow_ma_len2)[i_slow_ma_lookback],lookahead=barmerge.lookahead_on )
m2_slow_a = request.security(syminfo.tickerid, i_slow_ma_time, angle(m2_slow,14)[i_slow_ma_lookback],lookahead=barmerge.lookahead_on ) 
// p_m1_slow = plot(i_show_ma ? m1_slow : na ,color=m1_slow_a>0?aqua:orange,linewidth=2)
// p_m2_slow = plot(i_show_ma ? m2_slow : na ,color=m2_slow_a>0?green:red,linewidth=2)


g_ma = 'MA\' s ----------------------------------------------------'
i_show_ma = input.bool(false, title='Show MAs', group=g_ma)
i_show_hma = input.bool(true, title='Show HMA', group=g_ma)
i_ma_time = input.timeframe(title='Timeframe', defval='30', group=g_ma) // chart
i_ma_offset = input.int(0, title='Offset MAs', group=g_ma)
ma_inl1 = 'len1'
ma_type = input.string(title='Type', defval='McGinley', options=['sma', 'ema', 'zema', 'dema', 'tema', 'wma', 'vwma', 'smma', 'rma', 'hma', 'lsma', 'McGinley'], group=g_ma)

show_m1 = input.bool(title='m1', defval=true, group=g_ma, inline=ma_inl1)
show_m2 = input.bool(title='m2', defval=true, group=g_ma, inline=ma_inl1)
show_m3 = input.bool(title='m3', defval=false, group=g_ma, inline=ma_inl1)
show_m4 = input.bool(title='m4', defval=true, group=g_ma, inline=ma_inl1)
show_m5 = input.bool(title='m5', defval=false, group=g_ma, inline=ma_inl1)
show_m6 = input.bool(title='m6', defval=false, group=g_ma, inline=ma_inl1)
show_m7 = input.bool(title='m7', defval=false, group=g_ma, inline=ma_inl1)
show_m8 = input.bool(title='m8', defval=true, group=g_ma, inline=ma_inl1)
show_m9 = input.bool(title='m9', defval=true, group=g_ma, inline=ma_inl1)

ma_inl2 = 'len2'
m_len1 = input.int(5, minval=1, title='M1', inline=ma_inl2)  // 8
m_len2 = input.int(10, minval=1, title='M2', inline=ma_inl2)  // 20
m_len3 = input.int(50, minval=1, title='M3', inline=ma_inl2)  // 50
m_len4 = input.int(75, minval=1, title='M4', inline=ma_inl2)  // 75 
m_len5 = input.int(75, minval=1, title='M5', inline=ma_inl2)  // 100
m_len6 = input.int(200, minval=1, title='M6', inline=ma_inl2)  // 200
m_len7 = input.int(300, minval=1, title='M7', inline=ma_inl2)  // 300
m_len8 = input.int(500, minval=1, title='M8', inline=ma_inl2)  // 500
m_len9 = input.int(1000, minval=1, title='M9', inline=ma_inl2)  // 500

ma_inl3 = 'len3'
ma_inl4 = 'len4'
ma_inl5 = 'len5'
i_use_smooth  = input.bool(true, title="", inline=ma_inl3)
i_smooth      = input.int(5, title="Smooth", inline=ma_inl3)
i_use_angle  = input.bool(true, title="", inline=ma_inl4)
i_angle =  input.int(100,title="Angle Amount", inline=ma_inl4) // 14 25
i_ma_multiple = input.bool(false, title="", inline=ma_inl5)
i_ma_multi_value = input.int(1, title="Multiply Value", inline=ma_inl5) // 10


g_ma_plot = 'Plot'
i_ma_select = input.int(6, title="Colorized", options=[1,2,3,4,5,6,7,8],group=g_ma_plot)
i_ma_candles = input.bool(false,title='MA Candles',group=g_ma_plot)
inl_fill = 'fill'
inl_conv = 'conv'
show_fill = input.bool(title='Show Fill', defval=true, inline=inl_fill, group=g_ma_plot)
show_conv = input.bool(title='Show Conv', defval=true, inline=inl_fill, group=g_ma_plot)
conv_amount = input.float(defval=25, title='Conv Amount', step=1, inline=inl_conv, group=g_ma_plot) //4 
c_type = input.string(title='Type', defval='NAS', options=['NAS', 'USD', 'JPY'], inline=inl_conv, group=g_ma_plot)
line_input = 1  //input(1, title="Line width", type=input.integer,inline=inl_fill )
l_width = 2


// Lines and Angles
ma_types(len, type, src) =>
    obj = 0.0
    this_src = src
    length = len
    if i_ma_multiple 
        length := i_ma_multi_value * len 
    if type == 'ema' // Exponential
        obj := ta.ema(this_src,length)
    if type == 'zema' // Zero Lag Exponential
        e1 = ta.ema(close,length)
        e2 = ta.ema(e1,length)
        diff = e1 - e2
        obj := e1 + diff 
    if type == 'sma' // Simple Moving Average
        obj := ta.sma(this_src,length)
    if type=="dema" // Double Exponential
        e = ta.ema(this_src, length)
        obj := 2 * e - ta.ema(e, length)
    if type == 'tema' // Triple Exponential
        ema1 = ta.ema(this_src, length)
        ema2 = ta.ema(ema1, length)
        ema3 = ta.ema(ema2, length)
        obj := 3 * (ema1 - ema2) + ema3
    if type == 'wma' // Weighted
        obj := ta.wma(this_src,length)
    if type == 'vwma' // Volume Weighted
        obj := ta.vwma(this_src,length)
    // if type=="smma" // Smoothed
    //     w = ta.wma(src, length)
    //     ma := na(w[1]) ? ta.sma(src, length) : (w[1] * (length - 1) + src) / length
    if type == "rma"
        obj := ta.rma(this_src, length)
    if type == 'hma' // Hull
        obj := ta.wma(2*ta.wma(this_src, length/2)-ta.wma(this_src, length), math.floor(math.sqrt(length) ))
    if type=="lsma" // Least Squares
        obj := ta.linreg(this_src, length, 0)
    if type=="McGinley"
        mg = 0.0
        mg := na(mg[1]) ? ta.ema(this_src, length) : mg[1] + (this_src - mg[1]) / (length * math.pow(this_src/mg[1], 4))
        obj :=mg

    if i_use_smooth
        obj := ta.sma(obj,i_smooth)

    obj

ma_angles(obj) =>
    this_ma = obj
    if i_use_smooth
        this_ma := ta.sma(this_ma,i_smooth)

    ma_angle = angle(this_ma,i_use_angle ? i_angle : 1)

    ma_angle

float m1 = 0.0
//ma_change = request.security(syminfo.tickerid, i_ma_time, close )

ma_types_hma() =>

    h1 = ta.wma(2*ta.wma(close, m_len1/2)-ta.wma(close, m_len1), math.floor(math.sqrt(m_len1) ))
    h2 = ta.wma(2*ta.wma(close, m_len2/2)-ta.wma(close, m_len2), math.floor(math.sqrt(m_len2) ))
    h3 = ta.wma(2*ta.wma(close, m_len3/2)-ta.wma(close, m_len3), math.floor(math.sqrt(m_len3) ))
    h4 = ta.wma(2*ta.wma(close, m_len4/2)-ta.wma(close, m_len4), math.floor(math.sqrt(m_len4) ))
    h5 = ta.wma(2*ta.wma(close, m_len5/2)-ta.wma(close, m_len5), math.floor(math.sqrt(m_len5) ))
    h6 = ta.wma(2*ta.wma(close, m_len6/2)-ta.wma(close, m_len6), math.floor(math.sqrt(m_len6) ))
    h7 = ta.wma(2*ta.wma(close, m_len7/2)-ta.wma(close, m_len7), math.floor(math.sqrt(m_len7) ))
    h8 = ta.wma(2*ta.wma(close, m_len8/2)-ta.wma(close, m_len8), math.floor(math.sqrt(m_len8) ))
    h9 = ta.wma(2*ta.wma(close, m_len9/2)-ta.wma(close, m_len9), math.floor(math.sqrt(m_len9) ))
    h1_a = ma_angles(h1)
    h2_a = ma_angles(h2)
    h3_a = ma_angles(h3)
    h4_a = ma_angles(h4)
    h5_a = ma_angles(h5)
    h6_a = ma_angles(h6)
    h7_a = ma_angles(h7)
    h8_a = ma_angles(h8)
    h9_a = ma_angles(h9)

    [ h1,h1_a,h2,h2_a,h3,h3_a,h4,h4_a,h5,h5_a,h6,h6_a,h7,h7_a,h8,h8_a,h9,h9_a ]

// HULL
[ h1,h1_a,h2,h2_a,h3,h3_a,h4,h4_a,h5,h5_a,h6,h6_a,h7,h7_a,h8,h8_a,h9,h9_a ] = request.security(syminfo.tickerid, i_ma_time, ma_types_hma() )
// h1   = request.security(syminfo.tickerid, i_ma_time, ma_types(m_len1, "hma", close) )
// h1_a = request.security(syminfo.tickerid, i_ma_time, ma_angles(h1) )
// h2   = request.security(syminfo.tickerid, i_ma_time, ma_types(m_len2, "hma", close) )
// h2_a = request.security(syminfo.tickerid, i_ma_time, ma_angles(h2) )
// h3   = request.security(syminfo.tickerid, i_ma_time, ma_types(m_len3, "hma", close) )
// h3_a = request.security(syminfo.tickerid, i_ma_time, ma_angles(h3) )
// h4   = request.security(syminfo.tickerid, i_ma_time, ma_types(m_len4, "hma", close) )
// h4_a = request.security(syminfo.tickerid, i_ma_time, ma_angles(h4) )
// h5   = request.security(syminfo.tickerid, i_ma_time, ma_types(m_len5, "hma", close) )
// h5_a = request.security(syminfo.tickerid, i_ma_time, ma_angles(h5) )
// h6   = request.security(syminfo.tickerid, i_ma_time, ma_types(m_len6, "hma", close) )
// h6_a = request.security(syminfo.tickerid, i_ma_time, ma_angles(h6) )
// h7   = request.security(syminfo.tickerid, i_ma_time, ma_types(m_len7, "hma", close) )
// h7_a = request.security(syminfo.tickerid, i_ma_time, ma_angles(h7) )
// h8   = request.security(syminfo.tickerid, i_ma_time, ma_types(m_len8, "hma", close) )
// h8_a = request.security(syminfo.tickerid, i_ma_time, ma_angles(h8) )
// h9   = request.security(syminfo.tickerid, i_ma_time, ma_types(m_len9, "hma", close) )
// h9_a = request.security(syminfo.tickerid, i_ma_time, ma_angles(h9) )
plot(i_show_hma and show_m1 ? h1 : na, title="H1", color=h1_a>0?aqua:blue)
//plot(i_show_hma and show_m2 ? h2 : na, title="H2", color=h2_a>0?yellow:orange)
plot(i_show_hma and show_m5 ? h5 : na, title="H5", color=h5_a>0?yellow:orange)
plot(i_show_hma and show_m6 ? h6 : na, title="H6", color=h6_a>0?red:violet)
plot(i_show_hma and show_m8 ? h8 : na, title="H8", color=h8_a>0?white:red)

// McGinley
m1   := request.security(syminfo.tickerid, i_ma_time, ma_types(m_len1, ma_type, close) )
m1_a = request.security(syminfo.tickerid, i_ma_time, ma_angles(m1) )
m2   = request.security(syminfo.tickerid, i_ma_time, ma_types(m_len2, ma_type, close) )
m2_a = request.security(syminfo.tickerid, i_ma_time, ma_angles(m2) )
m3   = request.security(syminfo.tickerid, i_ma_time, ma_types(m_len3, ma_type, close) )
m3_a = request.security(syminfo.tickerid, i_ma_time, ma_angles(m3) )
m4   = request.security(syminfo.tickerid, i_ma_time, ma_types(m_len4, ma_type, close) )
m4_a = request.security(syminfo.tickerid, i_ma_time, ma_angles(m4) )
m5   = request.security(syminfo.tickerid, i_ma_time, ma_types(m_len5, ma_type, close) )
m5_a = request.security(syminfo.tickerid, i_ma_time, ma_angles(m5) )
m6   = request.security(syminfo.tickerid, i_ma_time, ma_types(m_len6, ma_type, close) )
m6_a = request.security(syminfo.tickerid, i_ma_time, ma_angles(m6) )
m7   = request.security(syminfo.tickerid, i_ma_time, ma_types(m_len7, ma_type, close) )
m7_a = request.security(syminfo.tickerid, i_ma_time, ma_angles(m7) )
m8   = request.security(syminfo.tickerid, i_ma_time, ma_types(m_len8, ma_type, close) )
m8_a = request.security(syminfo.tickerid, i_ma_time, ma_angles(m8) )



// Diff and Convergence
ma_conv(t1, t2) =>
    
    float boost = switch syminfo.ticker
        "GBPUSD" => 10000
        "EURUSD" => 10000
        "GBPJPY" => 100
        "BTCUSD" => 0.1
        "NAS100" => 1
        "NDQ100" => 1
        "GOLD"   => 10
        "SILVER" => 100
        => 1

    diff = (t1 - t2) * boost
    conv = show_conv and diff < conv_amount and diff > conv_amount * -1 ? true : false
    [diff, conv]

[m2_m4_diff, m2_m4_conv] = ma_conv(m2, m4)
[m4_m5_diff, m4_m5_conv] = ma_conv(m4, m5)
[m5_m6_diff, m5_m6_conv] = ma_conv(m5, m6)
[m7_m8_diff, m7_m8_conv] = ma_conv(m7, m8)

// Colorized MA angles
ma_select()=>
    float select = switch i_ma_select
        2 => m2_a
        3 => m3_a
        4 => m4_a
        5 => m5_a
        6 => m6_a
        7 => m7_a
        8 => m8_a
        => m6_a

    ma_a = math.abs(select)
    ma_zone =  
     ma_a   < 1 ? 0 
     : ma_a < 2 ? 1 
     : ma_a < 3 ? 2 
     : ma_a < 4 ? 3 
     : ma_a < 5 ? 4 
     : ma_a < 6 ? 5 
     : ma_a > 6 ? 6 
     : na

    ma_color = 
     ma_zone   == 0 ? red 
     : ma_zone == 1 ? orange 
     : ma_zone == 2 ? yellow 
     : ma_zone == 3 ? gray 
     : ma_zone == 4 ? blue 
     : ma_zone == 5 ? lime 
     : ma_zone == 6 ? white : na

    [ma_color]

[ma_color] = ma_select()




var int flag_u = 0
var int flag_d = 0
flag_u := m2_a>20 and m2_a>m2_a[1] ? 1 : flag_u==1 and m2_a<m2_a[1] ? 0 : flag_u==1 and m2_a>0 ? 1 : na
flag_d := m2_a<-10 and m2_a<m2_a[1] ? -1 : flag_d==-1 and m2_a>m2_a[1] ? 0 : flag_d==-1 and m2_a<0 ? -1 : na
//plotshape(flag_u==0 ? 1 : na,title="Flag Up",color=red ,style=shape.circle,location=location.top)
//plotshape(flag_d==0?1:na,title="Flag Down",color=green ,style=shape.circle,location=location.bottom)



// Previous State
var state_change = blue
ss_red2 = m5 > m6 and m5_a > 0
ss_orange2 = m5 > m6 and m5_a < 0
ss_lime2 = m5 < m6 and m6_a > 0
ss_green2 = m5 < m6 and m6_a < 0
m5_m6_c = ss_red2 ? red : ss_orange2 ? orange : ss_lime2 ? lime : ss_green2 ? green : na

// Distance to M8
m8_dist = get_pip_value(close, m8, true)
//plot(m8_dist, title='M8 Dist', color= m8_dist>20 ? color.new(red,100) : color.new(green,100) )

// plot(i_show_ma and show_m4 ? h4 : na, color=h4_a>0?yellow:orange, title='M4 HMA')
// plot(i_show_ma and show_m7 ? h7 : na, color=h7_a>0?green:red, title='M7 HMA')
// plot(i_show_ma and show_m8 ? h8 : na, color=h8_a>0?green:red, title='M8 HMA')




g_bb = 'BB Bands ----------------------------------------------------'
i_show_bb_bands = input.bool(false,title='Show BB Bands', group=g_bb)
i_showbb_fast = input.bool(false, title="Show Fast")
i_showbb = input.bool(false, title="Show BB")
i_bb_len_fast = input(20, title='BB Len Fast', group=g_bb)
i_bb_len = input(500, title='BB Len', group=g_bb)
i_show_back = input.bool(false, title='Show Background')
sqz_length = 80
// Fast
bb_basis_f = ta.sma(close, i_bb_len_fast)
dev_f = 2 * ta.stdev(close, i_bb_len_fast)
bb_upper_f = bb_basis_f + dev_f
bb_lower_f = bb_basis_f - dev_f
bb_spread_f = bb_upper_f - bb_lower_f
bb_angle_f = angle(bb_basis_f,1)
// plot(i_show_bb_bands and i_showbb_fast ? bb_basis_f : na,title="Basis")
// plot(i_show_bb_bands and i_showbb_fast ? bb_upper_f : na,title="bb_upper")
// plot(i_show_bb_bands and i_showbb_fast ? bb_lower_f : na,title="bb_lower")

f_bb_slow() =>

    bb_basis = ta.ema(close, i_bb_len)
    dev = 2 * ta.stdev(close, i_bb_len)
    bb_upper = bb_basis + dev
    bb_lower = bb_basis - dev
    bb_spread = bb_upper - bb_lower
    bb_angle = angle(bb_basis,3)
    bb_avgspread = ta.sma(bb_spread, sqz_length)

    [bb_basis,bb_upper,bb_lower,bb_spread,bb_angle,bb_avgspread]

[bb_basis,bb_upper,bb_lower,bb_spread,bb_angle,bb_avgspread] = request.security(syminfo.tickerid, '30', f_bb_slow() )

bb_squeeze = 0.00
bb_squeeze := bb_spread / bb_avgspread * 100
// Zones
bb_zone = bb_squeeze < 53 ? 0 : 
 bb_squeeze < sqz_length ? 1 : 
 bb_squeeze < 120 ? 2 :
 bb_squeeze < 160 ? 3 :
 bb_squeeze > 160 ? 4 :
 bb_squeeze > 200 ? 5 : na
sqz_color = bb_zone == 0 ? #0045b3 :
 bb_zone == 1 ? #ff0062 : 
 bb_zone == 2 ?  gray : 
 bb_zone == 3 ?  #00c3ff : 
 bb_zone == 4 ? white:
 bb_zone == 5 ? yellow: na

bb_zones_color =  sqz_color

plot(i_show_bb_bands and i_showbb ? bb_basis : na,title="Basis", color=bb_zones_color)
// plot(i_show_bb_bands and i_showbb ? bb_upper : na,title="bb_upper")
// plot(i_show_bb_bands and i_showbb ? bb_lower : na,title="bb_lower")
//plot(i_show_bb_bands ? bb_angle : na,title="BB Angle 2", color=bb_angle>0?green:red)
bb_cond = m1 < bb_lower ? 1 : m1 > bb_upper ? -1 : na
//bgcolor(i_show_back and bb_cond == 1 ? aqua : i_show_back and bb_cond == -1 ? orange : na)
//barcolor(bb_cond == 1 ? aqua : bb_cond == -1 ? orange : na)





g_rsi_band = 'RSI + Bands ----------------------------------------------------'
rsi_time = input.timeframe("10",title="Timeframe", group=g_rsi_band)
isBB = true
rsiLengthInput = input.int(14, minval=1, title="RSI Length", group=g_rsi_band) // 21
rsiSourceInput = input.source(close, "Source", group=g_rsi_band)
maLengthInput = input.int(20, title="MA Length", group=g_rsi_band) // 14
bbMultInput = input.float(1.5, minval=0.001, maxval=50, title="BB StdDev", group=g_rsi_band) // 2.0

rsi_bb()=>
    rsi_up = ta.rma(math.max(ta.change(rsiSourceInput), 0), rsiLengthInput)
    rsi_down = ta.rma(-math.min(ta.change(rsiSourceInput), 0), rsiLengthInput)
    rsi = rsi_down == 0 ? 100 : rsi_up == 0 ? 0 : 100 - (100 / (1 + rsi_up / rsi_down))
    rsiMA = ta.sma(rsi, maLengthInput)
    bbUpperBand = rsiMA + ta.stdev(rsi, maLengthInput) * bbMultInput
    bbLowerBand = rsiMA - ta.stdev(rsi, maLengthInput) * bbMultInput

    [rsi,rsiMA,bbUpperBand,bbLowerBand]

[rsi,rsiMA,bbUpperBand,bbLowerBand] = rsi_bb()

rsi_m = request.security(syminfo.tickerid, rsi_time, rsi )
rsiMA_m = request.security(syminfo.tickerid, rsi_time, rsiMA )
bbUpperBand_m = request.security(syminfo.tickerid, rsi_time, bbUpperBand )
bbLowerBand_m = request.security(syminfo.tickerid, rsi_time, bbLowerBand )

// Plot
plot(rsiMA_m ,title="RSI MA", style=plot.style_circles,color=color.red)
plot(rsi_m ,title="RSI", style=plot.style_circles,color=color.green)
plot(bbUpperBand_m ,title="BB up", style=plot.style_circles,color=color.red)
plot(bbLowerBand_m ,title="BB down", style=plot.style_circles,color=color.green)

rsi_up = rsi_m>bbUpperBand_m and rsiMA_m>50 ? rsiMA_m : na
//plot(rsi_up,title="RSI Up", style=plot.style_circles,color=color.red)
rsi_down = rsi_m<bbLowerBand_m and rsiMA_m<50 ? rsiMA_m : na
//plot(rsi_down,title="RSI Down", style=plot.style_circles,color=color.green)





// ===  Fibo Trend ===
// ==================================================
g_fibo_trend = 'G Fibo Trend ----------------------------------------------------'
inl_fibo = 'inl-fib'
fibo_trend_time = input.timeframe("",title="Timeframe", group=g_fibo_trend)
show_gfibo = false //input.bool(false,"Show G Fibo Trend",group=g_fibo_trend)
show_candles = input.bool(true,"Fibo Candles",group=g_fibo_trend)
ma_val = input.int(10, title="MA",group=g_fibo_trend ) // 6
fibo_period = input.int(25,"Analysis Period",group=g_fibo_trend) // 50
lowerValue = input.float(0.382,"Lower Fibonacci Level",options=[0.236, 0.382, 0.50, 0.618, 0.786],group=g_fibo_trend)
upperValue = input.float(0.618,"Upper Fibonacci Level",options=[0.236, 0.382, 0.50, 0.618, 0.786],group=g_fibo_trend)
showFill = input.bool(true,"Show Filling",group=g_fibo_trend)
changeCandle = input.bool(true,"Change Candle Color",group=g_fibo_trend)




f_fibo_trend() =>
    ma = ta.wma(close,ma_val)
    max = ta.highest(close, fibo_period)
    min = ta.lowest(close, fibo_period)
    lowerFib = min + (max-min)*lowerValue
    upperFib = min + (max-min)*upperValue
    [ma, lowerFib, upperFib]

[ma, lowerFib, upperFib] = request.security(syminfo.tickerid, fibo_trend_time, f_fibo_trend() )

float closeVal = ma
float openVal = ma
color fibo_color = closeVal>upperFib and openVal>upperFib?green:closeVal<lowerFib and openVal<lowerFib?red:yellow

// maxLine = plot(max and show_gfibo?max : na,color=color.green,title="Max")
// minLine = plot(min and show_gfibo?min : na,color=color.red,title="Min")
// LowerFibLine = plot(lowerFib and show_gfibo?lowerFib : na,color=color.rgb(228, 255, 75, 20),title="Lower Fib")
// UpperFibLine = plot(upperFib and show_gfibo?upperFib : na,color=color.rgb(228, 255, 75, 20),title="Upper Fib")
// fill(maxLine,UpperFibLine,color=showFill?color.rgb(0,255,0,changeCandle?95:70):na)
// fill(UpperFibLine,LowerFibLine,color=showFill?color.rgb(228, 255, 75, changeCandle?95:70):na)
// fill(LowerFibLine,minLine,color=showFill?color.rgb(255,0,0,changeCandle?95:70):na)
barcolor(show_candles? fibo_color : na, title='Fibo Bar Color')
//plotcandle(open,high,low,close,"Bar",color=changeCandle?fibo_color:na,wickcolor=changeCandle?fibo_color:na,bordercolor=changeCandle?fibo_color:na)






// ===  Stochastic ===
// ==================================================
g_rstoch = 'Stochastic -------------------------------------------------------------'
stoch_time = input.timeframe('5', "Stoch Time", group=g_rstoch)
a = input.int(10, "Percent K Length", group=g_rstoch) // 10
b = input.int(3, "Percent D Length", group=g_rstoch)
ob = input.int(40, "Overbought", group=g_rstoch)
os = input.int(-40, "Oversold", group=g_rstoch)
smooth = input.int(1, "Smoothing", group=g_rstoch)
show_bars = input.bool(false, title="Show Bars", group=g_rstoch)

stoch() =>
    // Range Calculation
    ll = ta.lowest (low, a)
    hh = ta.highest (high, a)
    diff = hh - ll
    rdiff = close - (hh+ll)/2

    avgrel = ta.ema(ta.ema(rdiff,b),b)
    avgdiff = ta.ema(ta.ema(diff,b),b)
    // SMI calculations
    SMI = avgdiff != 0 ? (avgrel/(avgdiff/2)*100) : 0

stoch_m = request.security(syminfo.tickerid, stoch_time, stoch() )
stoch_SMI = request.security(syminfo.tickerid, stoch_time, ta.ema(stoch_m,b) ) 
stoch_EMA = request.security(syminfo.tickerid, stoch_time, ta.ema(stoch_m, 10) )  
 
//plot(stoch_SMI, title="Stochastic", color=color.new(blue,100) )
//plot(stoch_EMA, title="stoch_EMA", color=color.new(yellow,100))

// level_40 = ob
// level_40smi = stoch_SMI > ob ? stoch_SMI : level_40
// level_m40 = os
// level_m40smi = stoch_SMI < os ? stoch_SMI : level_m40

stoch_color = stoch_SMI<stoch_EMA and stoch_EMA>ob ? yellow : stoch_EMA>ob ? red : stoch_SMI>ob ? orange : 
 stoch_SMI>stoch_EMA and stoch_EMA<os ? aqua : stoch_EMA<os ? green : stoch_SMI<os ? lime : na

barcolor(show_bars?stoch_color:na, title='Stoch Bar Color')
 




//
// ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ //
// ADX
// ------------------------------------------------------------------------------------------------------------------
g_adx = 'ADX -------------------------------------------------------------'
g_adx_time = ''
adx_time = input.timeframe(title='Timeframe', defval='10', group=g_adx)
i_adx_showbars = input.bool(false, title="ADX bars", group=g_adx)
inl_adx = '1'
use_adx_curr = input.bool(title='Show Current', defval=true, inline=inl_adx, group=g_adx)
use_adx_multi = input.bool(title='Show Multi', defval=true, inline=inl_adx, group=g_adx)

adx_len = input(9, title='Length', group=g_adx)  // 14
adx_line = input(20, title='threshold', group=g_adx)  // 20
adx_avg = input(8, title='SMA', group=g_adx)  // 10
adx_top = input(50, title='High', group=g_adx)  // 41
adx_high = input(39.5, title='High', group=g_adx)  // 41
adx_mid = input(title='Mid', defval=33, group=g_adx)
adx_center = input(title='Center', defval=20, group=g_adx)
adx_low = input(title='Low', defval=12, group=g_adx)

// Show adx
// show_di_plus = input(title='Di Plus', defval=true,group=g_adx)
// show_di_minus = input(title='Di Minus', defval=true,group=g_adx)
// show_adx = input(title='ADX', defval=true,group=g_adx)
// show_adx_sma = input(title='ADX SMA', defval=true,group=g_adx)


f_adx() =>
    smooth_tr = 0.0
    smooth_di_plus = 0.0
    smooth_di_minus = 0.0
    TrueRange = math.max(math.max(high - low, math.abs(high - nz(close[1]))), math.abs(low - nz(close[1])))
    DI_plus = high - nz(high[1]) > nz(low[1]) - low ? math.max(high - nz(high[1]), 0) : 0
    DI_minus = nz(low[1]) - low > high - nz(high[1]) ? math.max(nz(low[1]) - low, 0) : 0
    smooth_tr := nz(smooth_tr[1]) - nz(smooth_tr[1]) / adx_len + TrueRange
    smooth_di_plus := nz(smooth_di_plus[1]) - nz(smooth_di_plus[1]) / adx_len + DI_plus
    smooth_di_minus := nz(smooth_di_minus[1]) - nz(smooth_di_minus[1]) / adx_len + DI_minus

    di_plus = smooth_di_plus / smooth_tr * 100
    di_minus = smooth_di_minus / smooth_tr * 100
    DX = math.abs(di_plus - di_minus) / (di_plus + di_minus) * 100
    adx = ta.sma(DX, adx_len)
    adx_sma = ta.sma(adx, adx_avg)

    [adx, adx_sma, di_plus, di_minus]

[adx, adx_sma, di_plus, di_minus] = f_adx()

adx_m = request.security(syminfo.tickerid, adx_time, adx)
adx_sma_m = request.security(syminfo.tickerid, adx_time, adx_sma)
di_plus_m = request.security(syminfo.tickerid, adx_time, di_plus)
di_minus_m = request.security(syminfo.tickerid, adx_time, di_minus)
//plot(di_plus_m, title='Di Plus')
barcolor(i_adx_showbars and di_plus_m>di_minus_m ? red : i_adx_showbars and di_minus_m>di_plus_m ? green : na, title='ADX Bar Color')




//
// ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ //
// STOCH RSI
// ------------------------------------------------------------------------------------------------------------------
g_stoch_rsi = 'STOCH RSI -------------------------------------------------------------'
stoch_rsi_time = input.timeframe(title='Timeframe', defval='', group=g_stoch_rsi)
smoothK = input.int(3, "K", minval=1, group=g_stoch_rsi)
smoothD = input.int(3, "D", minval=1, group=g_stoch_rsi)
lengthRSI = input.int(14, "RSI Length", minval=1, group=g_stoch_rsi)
lengthStoch = input.int(14, "Stochastic Length", minval=1, group=g_stoch_rsi)
stoch_src = input(close, title="RSI Source", group=g_stoch_rsi)
i_stoch_smooth = input.bool(true, title="Use Smoothing")
i_sto_smooth_amount = input.int(3, title="Smooth Amount")

stoch_rsi()=>
    stoch_rsi = ta.rsi(stoch_src, lengthRSI )
    stoch = ta.stoch(stoch_rsi, stoch_rsi, stoch_rsi, lengthStoch)
    k = ta.sma(stoch, smoothK)
    d = ta.sma(k, smoothD)

    if i_stoch_smooth
        stoch_rsi := ta.sma(stoch_rsi, i_sto_smooth_amount)
        stoch := ta.sma(stoch, i_sto_smooth_amount)
        k := ta.sma(k, i_sto_smooth_amount)
        d := ta.sma(d, i_sto_smooth_amount)

    [stoch_rsi,stoch,k,d]

[stoch_rsi,stoch,stoch_k,stoch_d] = request.security(syminfo.tickerid, stoch_rsi_time, stoch_rsi() ) 

stoch_ch = ta.change(bar_index % str.tonumber(stoch_rsi_time) == 0 ) ? 1 : 0
stoch_rsi := stoch_ch ? stoch_rsi : stoch_rsi[1]
stoch := stoch_ch ? stoch : stoch[1]
stoch_k := stoch_ch ? stoch_k : stoch_k[1]
stoch_d := stoch_ch ? stoch_d : stoch_d[1]
stoch_rsi_angle = request.security(syminfo.tickerid, stoch_rsi_time, angle(stoch_k,1) ) 


//diff = math.abs(stoch_k - stoch_d)
//plot(stoch_rsi, "RSI", color=color.new(color.yellow, 100) )
//plot(stoch, "Stoch RSI", color=color.new(color.green, 100) )
//pk = plot(stoch_k, "K", color=color.new(blue,100))
//pd = plot(stoch_d, "D", color=color.new(orange,100))



//
// ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ //
// HIGHEST LOWEST
// ------------------------------------------------------------------------------------------------------------------
g_hl = 'HIGHEST LOWEST -------------------------------------------------------------'
hl_res = input.timeframe(title='Resolution', defval='30', group=g_hl)
hl_res2 = input.timeframe(title='Resolution 2', defval='120', group=g_hl)
i_use_hl1 = input.bool(true,title='Display HL1', group=g_hl)
i_use_hl2 = input.bool(true,title='Display HL2', group=g_hl)
i_show_bars = input.bool(true,title='Show Bars', group=g_hl)
gaps = input.bool(false,title='Bar Merge Gaps On', group=g_hl)
hl_len = input(title='Length', defval=10, group=g_hl)  // 21
src_h = input(title='Source', defval=close, group=g_hl)
src_l = input(title='Source', defval=close, group=g_hl)
use_diff = input(title='Filter Diff', defval=true, group=g_hl)
diff_range = input(10, title='FDiff Range', group=g_hl)
offset = input(title='Offset', defval=0, group=g_hl)
i_hl_smooth = input(title='Smooth', defval=true, group=g_hl)
i_hl_smooth_len = input(title='Smooth Length', defval=14, group=g_hl)

// HL 1
var float hh = 0.0
var float ll = 0.0
hh := timeframe.change(hl_res) ? request.security(syminfo.tickerid, hl_res, ta.highest(src_h, hl_len), gaps=gaps ? barmerge.gaps_on : barmerge.gaps_off) : hh[1]
ll := timeframe.change(hl_res) ? request.security(syminfo.tickerid, hl_res, ta.lowest(src_l, hl_len), gaps=gaps ? barmerge.gaps_on : barmerge.gaps_off) : ll[1]
hl_mid = i_hl_smooth ? ta.sma((hh + ll) * 0.5, i_hl_smooth_len) : (hh + ll) * 0.5
hh_a = angle(hh, hl_len)
ll_a = angle(ll, hl_len)
diff = hh - ll
h1_p1 = plot(i_use_hl1 ? hh : na, title='HH', linewidth=2, color=hh_a == 0 ? violet : red)
h1_p2 = plot(i_use_hl1 ? ll : na, title='LL', linewidth=2, color=ll_a == 0 ? aqua : blue)
plot(i_use_hl1 ? hl_mid : na, title='HL Midline', linewidth=2)
barcolor(i_show_bars and i_use_hl1 and timeframe.change(hl_res) ? blue : na, title='HL1 Bar Color')

// HL 2
var float hh2 = 0.0
var float ll2 = 0.0
hh2 := timeframe.change(hl_res2) ? request.security(syminfo.tickerid, hl_res2, ta.highest(src_h, hl_len), gaps=gaps ? barmerge.gaps_on : barmerge.gaps_off) : hh2[1]
ll2 := timeframe.change(hl_res2) ? request.security(syminfo.tickerid, hl_res2, ta.lowest(src_l, hl_len), gaps=gaps ? barmerge.gaps_on : barmerge.gaps_off) : ll2[1]
hh2_a = angle(hh2, hl_len)
ll2_a = angle(ll2, hl_len)
diff2 = hh2 - ll2
hl_mid2 = i_hl_smooth ? ta.sma((hh2 + ll2) * 0.5, i_hl_smooth_len) : (hh2 + ll2) * 0.5

hh2_p1 = plot(i_use_hl2 ? hh2 : na, title='HH2', linewidth=2, color=hh2_a == 0 ? #ff00ff : #ff0000)
hh2_p2 = plot(i_use_hl2 ? ll2 : na, title='LL2', linewidth=2, color=ll2_a == 0 ? #55d51a : #00FFFF)
plot(i_use_hl2 ? hl_mid2 : na, title='HL2 Mid', linewidth=2)
//fill(p1, p2, title='Fill', color=#33333365)
//plot(diff, title='Diff', color=color.new(color.blue, 100), linewidth=0)

sell_cond = high > hh ? 1 : na
buy_cond = low < ll and use_diff and diff > diff_range ? 1 : na
buy_cond2 = close<hl_mid2 and m3_a>2
//plotshape(sell_cond, title='Sell 1', color=color.new(color.red, 0), style=shape.circle, location=location.top)
plotshape(buy_cond2, title='HL Buy 2', color=green, style=shape.circle, location=location.bottom)
barcolor(i_show_bars and i_use_hl2 and timeframe.change(hl_res2) ? blue : na, title='HL2 Bar Color')




//
// ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ //
// Nadaraya–Watson Regression using a Rational Quadratic Kernel
rqk_group = 'Nadaraya Watson -------------------------------------------------------------'

// Settings
i_rqk_show_rqk = input.bool(true, title='Show RQK ', group=rqk_group)
i_rqk_show_ma = input.bool(false, title='Show Ma\'s ', group=rqk_group)
i_rqk_use_multi = input.bool(true, title="Use Multi Timeframe", group=rqk_group)
i_rqk_resCustom = input.timeframe(title='Timeframe', defval='30', group=rqk_group)

rqk_h = input.float(20.0, 'Lookback Window', tooltip='The number of bars used for the estimation. This is a sliding value that represents the most recent historical bars. Recommended range: 3-50', group=rqk_group) // 8
rqk_r = input.float(20.0, 'Relative Weighting', step=0.25, tooltip='Relative weighting of time frames. As this value approaches zero, the longer time frames will exert more influence on the estimation. As this value approaches infinity, the behavior of the Rational Quadratic Kernel will become identical to the Gaussian kernel. Recommended range: 0.25-25', group=rqk_group) // 8
rqk_x_0 = input.int(100, "Start Regression at Bar", tooltip='Bar index on which to start regression. The first bars of a chart are often highly volatile, and omission of these initial bars often leads to a better overall fit. Recommended range: 5-25', group=rqk_group) // 4 6 25
rqk_smoothColors = input.bool(true, "Smooth Colors", tooltip="Uses a crossover based mechanism to determine colors. This often results in less color transitions overall.", inline='1', group='Colors', group=rqk_group) // false
rqk_lag = input.int(1, "Lag", tooltip="Lag for crossover detection. Lower values result in earlier crossovers. Recommended range: 1-2", inline='1', group='Colors', group=rqk_group) // 2

//i_bars_merge = input.bool(false, title='Lookahead On')
rqk_src = close //input.source(close, 'Source', group=rqk_group)
rqk_size = array.size(array.from(rqk_src) ) // size of the data series

//MA
i_rqk_angle_amount = input.int(14, title='Angle Amount', group=rqk_group)
i_rqk_conv = input.int(27, title='Convergence Amount', group=rqk_group)
i_rqk_show_between = input.bool(false, title='Show Between ', group=rqk_group)

// rqk_m2 = ta.hma(close,20)
// rqk_m2_a = angle(rqk_m2,i_rqk_angle_amount)
// rqk_m4 = ta.hma(close,75)
// rqk_m4_a = angle(rqk_m4,i_rqk_angle_amount)
// rqk_m8 = ta.hma(close,1000)
// rqk_m8_a = angle(rqk_m8,i_rqk_angle_amount)

//plot(size,title="array size",color=color.new(blue,100))

// Further Reading:
// The Kernel Cookbook: Advice on Covariance functions. David Duvenaud. Published June 2014.
// Estimation of the bandwidth parameter in Nadaraya-Watson kernel non-parametric regression based on universal threshold level. Ali T, Heyam Abd Al-Majeed Hayawi, Botani I. Published February 26, 2021.
kernel_regression(_src, _size, _h) =>
    float _currentWeight = 0.
    float _cumulativeWeight = 0.
    for i = 0 to _size + rqk_x_0
        y = _src[i] 
        w = math.pow(1 + (math.pow(i, 2) / ((math.pow(_h, 2) * 2 * rqk_r))), -rqk_r)
        _currentWeight += y*w
        _cumulativeWeight += w
    _currentWeight / _cumulativeWeight

bull_bear() =>
    // Estimations
    yhat1 = kernel_regression(rqk_src, rqk_size, rqk_h)
    yhat2 = kernel_regression(rqk_src, rqk_size, rqk_h - rqk_lag)

    // Rates of Change
    bool wasBearish = yhat1[2] > yhat1[1]
    bool wasBullish = yhat1[2] < yhat1[1]
    bool isBearish = yhat1[1] > yhat1
    bool isBullish = yhat1[1] < yhat1
    bool isBearishChange = isBearish and wasBullish
    bool isBullishChange = isBullish and wasBearish

    // Crossovers
    bool isBullishCross = ta.crossover(yhat2, yhat1)
    bool isBearishCross = ta.crossunder(yhat2, yhat1) 
    bool isBullishSmooth = yhat2 > yhat1
    bool isBearishSmooth = yhat2 < yhat1

    [yhat1,isBullish,isBearish,isBearishChange,isBullishChange,isBullishCross,isBearishCross,isBullishSmooth,isBearishSmooth, wasBearish, wasBullish]

[yhat1,isBullish,isBearish,isBearishChange,isBullishChange,isBullishCross,isBearishCross,isBullishSmooth,isBearishSmooth, wasBearish, wasBullish] = bull_bear()


rqk = request.security(syminfo.tickerid, i_rqk_use_multi? i_rqk_resCustom : "", yhat1)
rqk_a = request.security(syminfo.tickerid, i_rqk_use_multi? i_rqk_resCustom : "", angle(rqk,i_rqk_angle_amount))

rqk_bwt =  is_between(rqk, h9)
//barcolor(i_rqk_show_between and rqk_bwt and i_rqk_show_rqk ? green : (i_rqk_show_between and i_rqk_show_rqk) and not rqk_bwt ? red : na, title='Is between RQK and M8')

// Colors
rqk_buy_col = #3AFF17
rqk_sell_col = #FD1707
color c_bullish = input.color(rqk_buy_col, 'Bullish Color', group='Colors')
color c_bearish = input.color(rqk_sell_col, 'Bearish Color', group='Colors')
color colorByCross = isBullishSmooth ? c_bullish : c_bearish
color colorByRate = isBullish ? c_bullish : c_bearish
color plotColor = rqk_smoothColors ? colorByCross : colorByRate

// RQK and M8 Conv
rqk_conv = get_pip_value(rqk,h9, true)
// Price and M8 Distance
rqk_price_dist = get_pip_value(close,h9, true)

p_rqk = plot(i_rqk_show_rqk ? rqk : na, "RQK", color=plotColor, linewidth=2)
//fill(p_rqk_m8, p_rqk, title="Fill Convergence", color=rqk_conv<i_rqk_conv ? color.new(red,80) : na)
//p_rqk_m8 = plot(i_rqk_show_rqk and i_rqk_show_ma ? h9 : na, title="M8", color=rqk_m8_a>0?green:red)

// plot(rqk_conv,title="Convergence",color=color.new(blue,100) )
// plot(rqk_price_dist,title="Price Distance",color=color.new(blue,100) )
// plot(rqk_a,title="RQK Angle",color=color.new(blue,100) )
// p_m2 = plot(i_rqk_show_ma ? rqk_m2 : na, title="M2", color=rqk_m2_a>0?green:red)
// p_m4 = plot(i_rqk_show_ma ? rqk_m4 : na, title="M4", color=rqk_m4_a>0?yellow:orange)






//
// ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ //
// TRADING
// ------------------------------------------------------------------------------------------------------------------

g_trading = 'Trading ----------------------------------------------------'
i_live_trading  = input.bool(false, title='Live Trading Only',group=g_trading)
isLive          = barstate.isrealtime
i_equity        = input.string("Equity", options=["Initial", "Equity"], title="Initial or Equity",group=g_trading)
i_long_trades   = input.bool(true, title='Long Trades',group=g_trading)
i_short_trades  = input.bool(true, title='Short Trades',group=g_trading)
i_use_pos       = input.bool(true,title="Use Percentage based Position Size",group=g_trading)
i_pctStop       = input(1.5, '% of Risk to Starting Equity Use to Size Positions',group=g_trading) / 100

Session(sess) => na(time("2",sess)) == false
i_show_tr = input.bool(false,title='Show Session')
i_show_nt = input.bool(true, title='Show No Trade')
i_session  = input.session(title="Session", defval="1400-1600")
i_no_trading = input.session(title="No Trading Hours", defval="1045-1300")
i_GMT = input.string(title='GMT', defval='GMT-10', options=['GMT-10', 'GMT-9', 'GMT-8', 'GMT-7', 'GMT-6', 'GMT-5', 'GMT-4', 'GMT-3', 'GMT-2', 'GMT-1', 'GMT-0', 'GMT+1', 'GMT+2', 'GMT+3', 'GMT+4', 'GMT+5', 'GMT+6', 'GMT+7', 'GMT+8', 'GMT+9', 'GMT+10', 'GMT+11', 'GMT+12', 'GMT+13'] )
// Set the start of day
start_time = Session(i_session)
timerange = time(timeframe.period, i_session, i_GMT) and i_show_tr
no_trading = time(timeframe.period, i_no_trading, i_GMT) and i_show_nt 
// ▒▒▒▒▒ Sessions ▒▒▒▒▒ 
session_StartDate = input.time(timestamp("1 January 2023 00:00 -1000"), title="Start Date", group=g_trading )
show_sessions = input.bool(false,title='Sessions', group=g_trading)
As  = input.session(title="Asia", defval="1800-0300")
Lon = input.session(title="London", defval="0300-1200")
Ny  = input.session(title="New York", defval="0800-1800")
Dz  = input.session(title="Deadzone - High Spreads", defval="1645-1830")


inl_color = "inl_color"
inl_color2 = "inl_color2"
c1_on = false //input.bool(true,title="", group=g_trading,inline=inl_color)
c1 = color.new(#00bcd4,90) //input.color(title="Asia", defval=#00bcd4,group=g_trading,inline=inl_color)
c2_on = true //input.bool(true,title="",group=g_trading,inline=inl_color)
c2 = color.new(#00796b,90) //input.color(title="London", defval=#00796b,group=g_trading,inline=inl_color)
c3_on = true //input.bool(true,title="",group=g_trading,inline=inl_color2)
c3 = color.new(#b71c1c,90) //input.color(title="New York", defval=#b71c1c,group=g_trading,inline=inl_color2)
c4_on = true //input.bool(true,title="", group=g_trading,inline=inl_color2)
c4 = color.new(color.purple,90) //input.color(title="Deadzone", defval=#b71c1c,group=g_trading,inline=inl_color2)

// Session(sess) => na(time("2",sess)) == false
Asia = Session(As) and c1_on and show_sessions? c1 : na
London = Session(Lon) and c2_on and show_sessions ? c2 : na
NewYork = Session(Ny) and c3_on and show_sessions ? c3 : na
Deadzone = Session(Dz) and c4_on and show_sessions ? c4 : na
// bgcolor(Asia)
// bgcolor(London)
// bgcolor(NewYork)
// bgcolor(Deadzone)


//
// ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ //
// STOP LOSS
// ------------------------------------------------------------------------------------------------------------------
g_sl = 'Stop Loss ----------------------------------------------------'
i_plot_trades = input.bool(true, title="Display Trades",group=g_sl)
i_sl_type   = input.string("ATR", title="SL Type", options=["ATR", "Pips","MA","SUP","Lowest"],group=g_sl) // ATR
i_ma_atr    = input.string("M6", title="Select MA ATR", options=["M6","M7","M8"],group=g_sl) // ATR

atr_group   = 'ATR'
show_sl     = input.bool(true,title="Show Stop Loss",group=atr_group)
sl_Multip   = input.float(1.5, title='SL Amount',group=atr_group) // 4 1.5
atr_len     = input.int(14, title='ATR Length ',group=atr_group)
atr_src     = input.string('close', title='Close or Wicks', options=['close', 'wicks'],group=atr_group) // close
//atr_type    = input.string(title='ATR Type', defval='ema', options=['sma', 'ema', 'zema', 'dema', 'tema', 'wma', 'vwma', 'smma', 'rma', 'hma', 'lsma', 'McGinley'], group=atr_group)
atr_smooth  = input.int(5, title="ATR Smooth", group=atr_group)
sl_min      = input.float(7.0, title='Stop Loss Minimum Pips',group=atr_group)
sl_max      = input.float(12.0, title='Stop Loss Maximum Pips',group=atr_group)

// Take Profit
g_tp = 'Take Profit ----------------------------------------------------'
i_tpFactor  = input(13.0, 'Target Profit',group=g_tp) // 3.5 3
i_qty_mult  = input.float(1.0, title='Quantity Multiplier',group=g_tp) // 5.0 2
i_tsFactor  = input(2.0, 'Trailing Stop',group=g_tp) // 1.25
i_ts        = input.bool(true, title="Use Trailing Stop",group=g_tp)
i_ticks     = input.float(10, title='Min Ticks',group=g_tp) // 100 for Forex 1000 for NAS
show_ts     = input.bool(true,title="Trailing Stop",group=g_tp)
i_bkcandles = 11 //input.int(11, title="Lowerest range - Number of candles",group=g_sl)


float qty_value = switch syminfo.type
    "forex" => 100000.0
    "futures" => 10.0
    "index" => 10.0
    => 10.0
    
stop_loss()=>

    float sl_short  = na
    float sl_long   = na
    float sl_short_t   = na
    float quantity  = syminfo.currency == 'JPY' ? i_pctStop * 100 : i_pctStop

    if i_sl_type == "ATR"
        atr_len = 14
        ATR = ta.atr(atr_len)
        sl_long     := (atr_src =='close' ? close : low)  - ATR * sl_Multip 
        sl_short    := (atr_src =='close' ? close : high) + ATR * sl_Multip 
        

    if i_sl_type == "MA"
        atr_len = 14
        ATR = ta.atr(atr_len)
        float ma_select = switch i_ma_atr
            "M6" => m6
            "M7" => m7
            "M8" => m8
            => m6
        
        sl_long     := ma_select
        sl_short    := ma_select


    if i_sl_type == "Lowest"
        sl_short    = ta.highest(high, i_bkcandles)[1]
        sl_long     = ta.lowest(low, i_bkcandles)[1]

    // Long
    // sl_long   := math.abs(close - sl_long) < sl_min ? close - sl_min
    //  : math.abs(close - sl_long) > sl_max ? close - sl_max
    //  : sl_long
    // sl_short := math.abs(close - sl_short) < sl_min ? close + sl_min
    //  : math.abs(close - sl_short) > sl_max ? close + sl_max
    //  : sl_short
    //decimals = int(math.log10(1/syminfo.mintick))
    longDiff  = math.abs(close - sl_long) // + (syminfo.mintick * 10)
    longPips  = get_pip_value(close,sl_long,true)
    longTS    = close + (i_tsFactor * longDiff)
    longTP    = close + (i_tpFactor * longDiff)
    plValue   = (i_equity=="Initial" ? strategy.initial_capital : strategy.equity) * quantity / (longDiff / close)
    pl_size   = i_use_pos ? math.ceil( plValue ) / close : math.ceil( qty_value * i_qty_mult )
    // Short
    shortDiff = math.abs(close - sl_short)
    shortPips = get_pip_value(close,sl_short,true)
    shortTS   = close - (i_tsFactor * shortDiff)
    shortTP   = close - (i_tpFactor * shortDiff)
    psValue   = (i_equity=="Initial" ? strategy.initial_capital : strategy.equity) * quantity / (shortDiff / close)
    ps_size   = i_use_pos ? math.ceil( psValue / close ) : math.ceil( qty_value * i_qty_mult )


    [sl_short,sl_long, close, close, shortTP, longTP, longTS, shortTS, pl_size, ps_size, longDiff, shortDiff, longPips, shortPips ]

[atr_short, atr_long, long_close, short_close, shortTP, longTP, longTS, shortTS, pl_size, ps_size, longDiff, shortDiff, longPips, shortPips] = stop_loss()


// ATR

atr_upper = ta.sma( ta.ema(atr_short, atr_len), atr_smooth ) // ma_types(atr_len, atr_type, atr_short)
atr_lower = ta.sma( ta.ema(atr_long, atr_len), atr_smooth )  // ma_types(atr_len, atr_type, atr_long)
atr_mid = (atr_lower + atr_upper) * 0.5
atr_mid_a = angle(atr_mid,14)
plot(show_sl ? atr_lower  : na,"ATR Lower ", color=lime )
plot(show_sl ? atr_upper  : na,"ATR Upper ", color=red )
plot(show_sl ? atr_mid  : na,"ATR Mid ", color=atr_mid_a > 0 ? white : gray )
plot(show_sl ? atr_mid_a  : na,"ATR Angle ", color=atr_mid_a > 0 ? color.new(green,100) : color.new(red,100) )
plot(show_sl ? atr_long  : na,"ATR + ", color=color.new(green,70) )
plot(show_sl ? atr_short : na,"ATR - ", color=color.new(red,70) )




// ▒▒▒▒▒ FILTERS ▒▒▒▒▒ 
g_filters = 'Filters'
i_use_filters = input.bool(false, title='Enable Filters',group=g_filters)
i_use_rsi_filter = input.bool(false, title='RSI Filter',group=g_filters)
i_use_time_filter = input.bool(false, title='Time Restraint',group=g_filters)
i_deadzone = input.bool(false, title='Do not take trades during',group=g_filters)

// var int flag_d = na
// flag_d := m2_a<-10 and m2_a<m2_a[1] ? -1 : flag_d==-1 and m2_a>m2_a[1] ? 0 : flag_d==-1 and m2_a<0 ? -1 : na

float shortSL = 0.0
float longSL  = 0.0
float ratio_l = 0.0
float short_ticks = 0.0
float long_ticks = 0.0
int counter_trade = 0

// ▒▒▒▒▒ TRADE LOGIC ▒▒▒▒▒ 
trade_dir() =>
    c      = close>open ? 1 : 0
    dir         = 0
    entryLong   = 0
    entryShort  = 0
    exitLong    = 0
    exitShort   = 0
    closeAll    = 0
    longSL      = 0.0
    cond        = ''
    cnt         = 0 // Counter Trend

    red_s       = m5>m6
    green_s     = m5<m6
    red_s2      = m7>m8
    green_s2    = m7<m8
    red_conv    = m5_m6_conv==1
    red_conv2   = m7_m8_conv==1
    green_conv  = m5_m6_conv==1
    green_conv2  = m7_m8_conv==1
    btw_m7_m8 = ( m7>m8 and (close<m7) and (close>m8) ) or ( m7<m8 and (close>m7) and (close<m8) )? true : false
    btw_hma = ( h7>h8 and (close<h7) and (close>h8) ) or ( h7<h8 and (close>h7) and (close<h8) )? true : false
    rsi_bb_cond1 = rsi_m<bbLowerBand_m and rsiMA_m<50 ? 1 : 0
    
    // Time and Live Trading
    allow_trades = i_live_trading==0 and time>session_StartDate ? true : i_live_trading==1 and isLive ? 1 : 0


    // Entry Long

    if time>session_StartDate

        // if close<m4 and m1<m4 and close<ll2 and c==0
        //  and h2_a>0 and close<h2
        //  and not(h7>h8 and h6>h7)
        //  //and not(h6>h5)
        //     entryLong := 1
        //     cond := 'LL'

        // if h1>h2 and h1[1]<h2[1] and c==0
        //  and atr_long<atr_lower
        //  and close<atr_mid
        //     entryLong := 1
        //     cond := 'm1'

        if close<m3 and m3_a>0
         and c==0 
         //and ll==ll[1]
         and close<ll
         and h1<hl_mid and m1<m3
         and not(m4>m3)
         and not(rqk_a<0)
         //and not( get_pip_value(hh,ll,true)<20  )
         //and not(hh>atr_upper)
            entryLong := 1
            cond := 'll'

        // h5>h6 and h8_a>0 and h5_a>0

        if high>hh and close>bb_upper
            exitLong := 1

        if h5<h6 and h8_a<0 and h5_a<0
         and c==1
         and hh==hh[1]
         and close>hh
         and h1>hl_mid
         and m1_a>0
            entryShort := 1
            cond := 'hh'

        if low<ll
            exitShort := 1

        // if h1>atr_mid and h1>h5 
        //  and close>hh and c==1
        //     exitLong := 1

        // if close>bb_upper or close>hh2
        //     exitLong := 1

        // Uptrend Counter
        // if high>hh2 and hh2>bb_upper and c==1
        //  and atr_upper>hh2 and atr_lower<hh2
        //  and not(perc_change()>5)
        //     entryShort := 1
        //     cond := 'cnt-l'
        //     cnt := 1



        // Downtrend Counter
        // if h2<ll2 and low<ll2 and ll2>bb_lower and c==0
        //  and atr_lower<ll2 and atr_upper>ll2
        //     entryLong := 1
        //     cond := 'cnt-s'
        //     cnt := 1

        // if low<bb_lower and h2<bb_lower and fibo_color!=green
        //  and h1>h2
        //     entryLong := 1
        //     cond := 'cnt-s'
        //     cnt := 1

        // if h1<h2 and h1[1]>h2[1]
        //     exitLong := 1
            //cond := 'm1'

        //         and atr_lower<hl_mid2 and high>high[1]


    // Counter Trades


    // Filters Long
    // HMA
    // if h6_a<0
    //     entryLong := 0

    if i_long_trades==false
        entryLong := 0

    if i_short_trades==false
        entryShort := 0





    //if cnt==0 and strategy.position_size > 0




    // Exit Short


    // Exit Short



    // Downtrend
    // if close>hh and bb_angle<0
    //     entryShort := 1
    //     cond := 'BB-hl_mid'




    // Filters
    // if di_minus_m>di_plus_m
    //     entryShort := 0

    // if h6_a>0 or h4_a>-1
    //     entryShort := 0

    // if i_short_trades==false
    //     entryShort := 0


    //Exits
    //if strategy.position_size < 0


    [entryLong,entryShort,exitLong,exitShort,closeAll,cond, cnt]

[entryLong,entryShort,exitLong,exitShort,closeAll,trade_label, cnt] = trade_dir()

counter_trade := cnt
// check if live trading or market closed
//plotshape(barstate.islastconfirmedhistory, title='Real Time', color=red, style=shape.circle, location=location.top)


plot(strategy.position_size < 0 ? -1 : strategy.position_size > 0  ? 1 : 0, title='Trade Direction', color=color.new(blue,100))


// longDiff := entryLong and strategy.opentrades == 0 ? longDiff * 100 : strategy.opentrades > 0 ? longDiff[1] : 0


// Short Plots
plot_trans = 90

short_close := entryShort and strategy.opentrades == 0 ? short_close : strategy.opentrades > 0 ? short_close[1] : 0
short_ticks := short_close - (syminfo.mintick * i_ticks)
shortSL     := entryShort and strategy.opentrades == 0 ? atr_short : strategy.opentrades > 0 ? shortSL[1] : 0
shortTS     := entryShort and strategy.opentrades == 0 ? shortTS : strategy.opentrades > 0 ? shortTS[1] : 0
shortTP     := entryShort and strategy.opentrades == 0 ? shortTP : strategy.opentrades > 0 ? shortTP[1] : 0
plot(strategy.opentrades > 0 and i_plot_trades ? shortPips : na, title="Short Pips", color=color.new(blue,100))
p_s_c       = plot( strategy.opentrades > 0 and i_plot_trades ? short_close : na, title="Short Close", color=color.new(green,plot_trans), linewidth=1, style=plot.style_linebr)
p_s_ticks   = plot( strategy.opentrades > 0 and i_plot_trades ? short_ticks : na, title="Short Ticks", color=lime, style=plot.style_linebr)
p_s_sl      = plot( strategy.opentrades > 0 and i_plot_trades ? shortSL : na, title="Short SL", color=color.new(red,plot_trans), linewidth=1, style=plot.style_linebr)
p_s_ts      = plot( strategy.opentrades > 0 and i_plot_trades ? shortTS : na, title="Short TS", color=color.new(green,plot_trans), linewidth=1, style=plot.style_linebr)
p_s_tp      = plot( strategy.opentrades > 0 and i_plot_trades ? shortTP : na, title="Short TP", color=color.new(green,plot_trans), linewidth=1, style=plot.style_linebr)
fill(p_s_sl, p_s_c,title='Fill Short SL',color=color.new(red,plot_trans) )
fill(p_s_ts, p_s_c,title='Fill Short TS',color=color.new(lime,plot_trans) )
fill(p_s_ts, p_s_tp,title='Fill Short TP',color=color.new(green,plot_trans) )

// Long Plots
long_close  := entryLong and strategy.opentrades == 0 ? long_close : strategy.opentrades > 0 ? long_close[1] : 0
long_ticks  := long_close + (syminfo.mintick * i_ticks)
longSL      := entryLong and strategy.opentrades == 0 ? atr_long : strategy.opentrades > 0 ? longSL[1] : 0
longTS      := entryLong and strategy.opentrades == 0 ? longTS : strategy.opentrades > 0 ? longTS[1] : 0
longTP      := entryLong and strategy.opentrades == 0 ? longTP : strategy.opentrades > 0 ? longTP[1] : 0
ratio_l     := ( close  - long_close )
//plot( sl_short  ,"sl_short ", color=color.new(red,100) )

// var int decimals = int(math.log10(1/syminfo.mintick))
// decimals := decimals == 2 ? 1 : decimals == 0 ? 1 : decimals
plot(strategy.opentrades > 0 and i_plot_trades ? syminfo.mintick : na, title="Min Ticks", color=color.new(blue,100))
plot(strategy.opentrades > 0 and i_plot_trades ? math.pow(10, decimals) * syminfo.mintick : na, title="Ticks Converted", color=color.new(blue,100))
plot(strategy.opentrades > 0 and i_plot_trades ? decimals : na, title="Decimals", color=color.new(blue,100))
plot(strategy.opentrades > 0 and i_plot_trades ? longDiff : na, title="Long Diff", color=color.new(blue,100))
plot(strategy.opentrades > 0 and i_plot_trades ? longPips : na, title="Long Pips", color=color.new(blue,100))
//plot(strategy.opentrades > 0 and i_plot_trades ? longDiff / syminfo.mintick : na, title="Ticks to Pips")
p_l_dist    = plot( strategy.opentrades > 0 and i_plot_trades ? get_pip_value(long_close,longSL,true) * (math.pow(10, decimals) * syminfo.mintick) : na, title="SL in Pips", color=color.new(blue,100) )
p_l_c       = plot( strategy.opentrades > 0 and i_plot_trades ? long_close : na, title="Long Close", color=color.new(red,plot_trans), linewidth=1, style=plot.style_linebr)
p_l_ticks   = plot( strategy.opentrades > 0 and i_plot_trades ? long_ticks : na, title="Long Ticks", color=color.new(lime,plot_trans), style=plot.style_linebr)
p_l_sl      = plot( strategy.opentrades > 0 and i_plot_trades ? longSL : na, title="Long SL", color=color.new(red,plot_trans), linewidth=1, style=plot.style_linebr)
p_l_ts      = plot( strategy.opentrades > 0 and i_plot_trades ? longTS : na, title="Trailing Stop", color=color.new(yellow,plot_trans), linewidth=1, style=plot.style_linebr)
p_l_tp      = plot( strategy.opentrades > 0 and i_plot_trades ? longTP : na, title="Long TP", color=color.new(green,plot_trans), linewidth=1, style=plot.style_linebr)

//psize       = plot(pl_size, title="Position Size")
//p_l_ratio   = plot( ratio_l, title="Ratio", color=color.new(red,75), linewidth=1, style=plot.style_linebr)
fill(p_l_sl,p_l_c, title='Fill Long SL', color=color.new(red,plot_trans))
fill(p_l_ts,p_l_c, title='Fill Long Trailing Stop', color=color.new(lime,plot_trans) )
fill(p_l_tp,p_l_ts, title='Fill Long Take Profit', color=color.new(green,plot_trans))



cd=close>open?1:0

if Session(Dz) and i_deadzone
    entryShort := 0
    entryLong := 0

// Long

// if high<atr_lower and entryLong
//     entryLong := 0

// if  atr_upper>m2 and entryLong
//     entryLong := 0

var int trade_num = 0
if (entryLong)
    //pd_level = get_retrace_level(close)
    trade_num := strategy.opentrades == 0 ? trade_num + 1 : trade_num
	strategy.entry("L", strategy.long, qty = pl_size, comment=trade_label + ' #' 
     + str.tostring(trade_num) + ' ' 
     + str.tostring( math.round_to_mintick(longDiff * 100)) + ' '
     //+ str.tostring( pd_level )
     )
    //strategy.exit('EXIT L', 'L', stop = longSL)
        
else
	strategy.cancel("S")

if (exitLong ) //and counter_trade==0
    exit_com = trade_label + ' ' + str.tostring( get_pip_value(long_close, close, true) )
	strategy.close("L", comment = exit_com)

// Short
if (entryShort)
    trade_num := strategy.opentrades == 0 ? trade_num + 1 : trade_num
	strategy.entry("S", strategy.short, qty = ps_size, comment=trade_label 
     + ' #' + str.tostring(trade_num) + ' ' 
     + str.tostring( math.round_to_mintick(shortDiff * 100))
     )
    //strategy.exit('EXIT S', 'S', stop=shortSL)
else
	strategy.cancel("S")

if (exitShort)
    exit_com = trade_label + ' ' + str.tostring( get_pip_value(short_close, close, true) )
	strategy.close("S", comment = exit_com)

// Filters
if Session(Dz) and i_deadzone
    strategy.close_all(comment = "close all entries")

in_profit() =>
    if strategy.position_size > 0 and close > longTS
        true
    else if strategy.position_size < 0 and close < shortTS
        true
    else
        false

getCurrentStage() =>
    var stage = 0
    if strategy.position_size == 0
        stage := 0
        stage
    if stage == 0 and strategy.position_size != 0
        stage := 1
        stage
    else if stage == 1 
        if strategy.position_size > 0 and close > longTS
            stage := 2
        if strategy.position_size < 0 and close < shortTS
            stage := 2
    else if stage == 2
        if strategy.position_size > 0 and close > longTP
            stage := 3
        if strategy.position_size < 0 and close < shortTP
            stage := 3
            stage
    stage

curStage = getCurrentStage()
plot(curStage,title='Current Stage', color=color.new(color.blue,100))

float stopLevel = na
string comment  = ''
float limit     = na //strategy.position_size > 0 and close>longTP and m2>m4 ? close : strategy.position_size < 0 and close<shortTP and m2<m4 ? close : na
string win     = 'Take Profit'
string loss    = 'Loss'
string bkeven  = 'Break Even'
string ts      = 'Trailing Stop'

if curStage == 1
    // Comment Strings

    if strategy.position_size > 0
        stopLevel := longSL
        comment   := close < long_close ? loss : win
        limit     := high>longTP ? high : na
    else if strategy.position_size < 0
        stopLevel := shortSL
        comment   := close > short_close ? loss : win
        limit     := low<shortTP ? low : na
        
    strategy.exit('x', stop=stopLevel, limit=limit, comment=comment)

else if curStage == 2

    if strategy.position_size > 0
        stopLevel := long_close + (syminfo.mintick * i_ticks)
        pips = str.tostring( get_pip_value(long_close, close, true) )
        comment   := close <= longTS ? bkeven + ' ' + pips  : win + ' ' + pips
        limit     := high>longTP ? high: na
        //get_pip_value(long_close, close, true)>60 ? high 
    else if strategy.position_size < 0
        stopLevel := short_close - (syminfo.mintick * i_ticks)
        pips       = str.tostring( get_pip_value(short_close, close, true) )
        comment   := close >= shortTS ? bkeven + ' ' + pips : win + ' ' + pips
        limit     := low<shortTP ? low : na

    strategy.exit('x', stop=stopLevel, limit=limit, comment=comment)

else if curStage == 3

    if strategy.position_size > 0
        stopLevel := longTS
        pips      = str.tostring( get_pip_value(long_close, close, true) )
        comment   := close >= longTS and close<longTP ? ts + ' ' + pips : win + ' ' + pips
        limit     := high>longTP ? high : na
    else if strategy.position_size < 0
        stopLevel := shortTS
        pips       = str.tostring( get_pip_value(short_close, close, true) )
        comment   := close <= shortTS and close>shortTP ? ts + ' ' + pips : win  + ' ' + pips
        limit     := low<shortTP ? low : na

    strategy.exit('x', stop=stopLevel, limit=limit, comment=comment)

else
    strategy.cancel('x')


bars_long = ta.barssince(strategy.position_size > 0)
plot(bars_long, title='Bars since long', color=color.new(blue,98))
bars_short = ta.barssince(strategy.position_size < 0)
plot(bars_short, title='Bars since short', color=color.new(blue,98))