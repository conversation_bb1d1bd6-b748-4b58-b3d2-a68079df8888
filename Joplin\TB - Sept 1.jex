1c80e5a7fca846b59bab6913fdd5479d.md                                                                 000644                  0000000544  14663736631 013004  0                                                                                                    ustar 00                                                                000000  000000                                                                                                                                                                         TB Sept 1

id: 1c80e5a7fca846b59bab6913fdd5479d
created_time: 2024-08-25T19:01:35.041Z
updated_time: 2024-08-25T19:01:35.041Z
user_created_time: 2024-08-25T19:01:35.041Z
user_updated_time: 2024-08-25T19:01:35.041Z
encryption_cipher_text: 
encryption_applied: 0
parent_id: 
is_shared: 0
share_id: 
master_key_id: 
icon: 
user_data: 
deleted_time: 0
type_: 2                                                                                                                                                            452b72fd9b264419bfb251a0161bcefb.md                                                                 000644                  0000020553  14663736631 012673  0                                                                                                    ustar 00                                                                000000  000000                                                                                                                                                                         TB - Sept 1

## **Indicators**

<span style="color: #3598db;">**Moving Averages**</span>

- <span style="color: #2dc26b;">**HMA (10 min 5min \* 2) fast**</span>
- <span style="color: #2dc26b;">**HMA (1.5 hour 30min \* 3)**</span>
- <span style="color: #2dc26b;">**EMA (15 min 5min \* 3) fast**</span>
- <span style="color: #2dc26b;">**EMA (3 hour 1 hour \* 3)**</span>
    - <span style="color: #2dc26b;">**Smoothing 20**</span>

<span style="color: #3598db;">**Highs and Lows**</span>

- <span style="color: #2dc26b;">**HL 1 (1 hour)**</span>
    
- <span style="color: #2dc26b;">**HL 2 (4 hour)**</span>
    
- <span style="color: #ffffff;">**ATR**</span>
    
    - <span style="color: #2dc26b;">**atr2 (30 min) \[1.5, 14, ema, smooth==5\]**</span>
    - ```pinescript
              // ATR Filter - Buy Entries
              // Touching 4 hour ll - Strong downtrend
              if atr_short > atr_mid
                  entryLong := 0
        ```
        

<span style="color: #3598db;">**Bollinger Bands**</span>

- <span style="color: #2dc26b;">**bb1 (5 or 10 min) \[Len = 100, WMA, dev==1.5\] fast**</span>
    - <span style="color: #2dc26b;">**bars since touch top or bottom - Use as a Filter**</span>
- <span style="color: #2dc26b;">**bb2 (15 min) \[Len = 200, WMA, dev==1.5\]**</span>

&nbsp;

- <span style="color: #3598db;">**Candles**</span>
    
    - <span style="color: #2dc26b;">**4 hour Heikin Ashi**</span>

&nbsp;

# LOGIC

<table border="1" style="border-collapse: collapse; width: 99.9668%;"><thead><tr><th scope="col" style="width: 46.932%;"><h2 id="uptrend"><span style="color: #3598db;" class="jop-noMdConv">Uptrend</span></h2><h3 id="entry-main">Entry - Main</h3><ul><li><p>EMA - 50 is above 200</p></li><li><p>RED stage 1-3 or Green stage 4 and H3 angled up</p><ol><li><p>EMA's</p><ul><li>price below EMA 15</li><li>price below ema fast 200</li></ul></li><li><p>HMA</p><ol><li>H2 &gt; H3 and H3 &gt; H5</li></ol></li><li><p>price below HL1</p></li><li><p>HMA fast</p></li><li><p>price below bb1&nbsp; or at least has touched within last 50 bars</p></li></ol></li></ul><div class="joplin-editable"><pre class="joplin-source" data-joplin-language="pinescript" data-joplin-source-open="```pinescript
" data-joplin-source-close="
```">    // RED - Stage 1-3 || EMA 50 higher than 200
    if hd_m == 1 and hs_s &lt; 4 and m3_m &gt; m6_m
<!-- -->    	
    	// Price lower than EMA
    	if close &lt; m2_m
    	</pre><pre class="hljs"><code>    <span class="hljs-comment">// RED - Stage 1-3 || EMA 50 higher than 200</span>
    <span class="hljs-meta">if</span> hd_m == <span class="hljs-number">1</span> <span class="hljs-keyword">and</span> hs_s &lt; <span class="hljs-number">4</span> <span class="hljs-keyword">and</span> m3_m &gt; m6_m
<!-- -->    	
    	<span class="hljs-comment">// Price lower than EMA</span>
    	<span class="hljs-meta">if</span> close &lt; m2_m
    	</code></pre></div><ul><li><p>Green</p><ul><li>Is H3 below H5 and angled up and H3 still below H6</li><li>H5 angled up (yellow). Is price below H3</li><li>Price below ll2 4 hour</li></ul></li></ul><div class="joplin-editable"><pre class="joplin-source" data-joplin-language="pinescript" data-joplin-source-open="```pinescript
" data-joplin-source-close="
```">    if hd_m == -1 and hs_s &lt; 4
<!-- -->    
        if h3_m &lt; h5_m</pre><pre class="hljs"><code>   <span class="hljs-built_in"> if </span>hd_m == -1<span class="hljs-built_in"> and </span>hs_s &lt; 4
<!-- -->    
       <span class="hljs-built_in"> if </span>h3_m &lt; h5_m</code></pre></div><hr><h3 id="filters">Filters</h3><ul><li>Is price below EMA 15</li><li>Is Price below bb2 basis</li><li>Candles - 4 hour</li><li>If below M3 and looking for bottom (LL2) wait until M3 angle is below 0 line</li><li>Special<ul><li>If price below M6 (200)</li><li>Price lower than BB_lower_2<ul><li>If bb color white wait until candle is half way inside and outside bb_lower</li></ul></li><li>99% change its hitting LL2 4 hour. Minimum below LL1 1 hour</li></ul></li></ul><p>&nbsp;</p><h3 id="exits">Exits</h3><ul><li>Price higher than HH 4 hour</li><li>HMA fast is red<ul><li>h3 and h5 higher than h7</li><li>Price higher than h7</li><li>Angels&nbsp;</li></ul></li></ul><p>&nbsp;</p><h3 id="counter-trades">Counter Trades</h3><ul><li><p>HMA</p><ul><li>Red Stage 4-6 or Green 1-3</li><li>Is H3 above H5 or inside H5</li><li>Is price above h3</li></ul></li><li><p>EMA</p><ul><li>M1 angle below M2 and both above 0 line</li><li>M2 (15) angled down and price above M2</li><li>M2 below M3 (50)</li></ul></li><li><p>EMA fast</p><ul><li>Is m2 (50) angled up and price is below m2</li><li>Is angle higher than (6, white) and above 4 hour HH2?</li><li>Or, 15 line is above HH2 or distance is very close and is slowing down</li></ul></li><li><p>Candles - 4 hour</p><ul><li>Red and price higher than candle high</li></ul></li><li><p>Filters</p><ul><li>Not when Green and yellow H5</li></ul></li></ul></th><th scope="col" style="width: 46.932%;"><h2 id="uptrend"><span style="color: #3598db;" class="jop-noMdConv">Downtrend</span></h2><h3 id="entry-main">Entry - Main</h3><ul><li><p>EMA - 50 is below 200</p></li><li><p>Green stage 1-3 or Red stage 4 and H3 angled down</p><ol><li><p>EMA's</p><ul><li>price above EMA 15</li><li>price above ema fast 200</li></ul></li><li><p>HMA</p><ol><li>H2 &gt; H3 and H3 &gt; H5</li></ol></li><li><p>price above HL1</p></li><li><p>HMA fast</p></li><li><p>price below bb1&nbsp; or at least has touched within last 50 bars</p></li></ol></li></ul><div class="joplin-editable"><pre class="joplin-source" data-joplin-language="pinescript" data-joplin-source-open="```pinescript
" data-joplin-source-close="
```">    // GREEN - Stage 1-3
    if hd_m == -1 and hs_s &lt; 4
<!-- -->    
    	// Price higher than EMA
    	if close &gt; m2_m
    	</pre><pre class="hljs"><code>    <span class="hljs-comment">// GREEN - Stage 1-3</span>
    <span class="hljs-keyword">if</span> hd_m == <span class="hljs-number">-1</span> <span class="hljs-keyword">and</span> hs_s &lt; <span class="hljs-number">4</span>
<!-- -->    
    	<span class="hljs-comment">// Price higher than EMA</span>
    	<span class="hljs-keyword">if</span> close &gt; m2_m
    	</code></pre></div><ul><li><p>RED</p><ul><li>Is H3 above H5 and angled down and H3 still above H6</li><li>H5 angled down (red). Is price above H3</li><li>Price above ll2 4 hour</li></ul></li></ul><p>&nbsp;</p><hr><h3 id="filters">Filters</h3><ul><li>Is price above EMA 15</li><li>Is Price above bb2 basis</li><li>Candles - 4 hour</li><li>If below M3 and looking for bottom (LL2) wait until M3 angle is below 0 line</li><li>Special<ul><li>If price below M6 (200)</li><li>Price lower than BB_lower_2<ul><li>If bb color white wait until candle is half way inside and outside bb_lower</li></ul></li><li>99% change its hitting LL2 4 hour. Minimum below LL1 1 hour</li></ul></li></ul><p>&nbsp;</p><h3 id="exits">Exits</h3><ul><li style="list-style-type: none;"><ul><li>Price higher than HH 4 hour</li><li>HMA fast is red<ul><li>h3 and h5 higher than h7</li><li>Price higher than h7</li><li>Angels -</li></ul></li></ul></li></ul><p>&nbsp;</p><h3 id="counter-trades">Counter Trades</h3><ul><li><p>HMA</p><ul><li>Red Stage 4-6 or Green 1-3</li><li>Is H3 above H5 or inside H5</li><li>Is price above h3</li></ul></li><li><p>EMA</p><ul><li>M1 angle below M2 and both above 0 line</li><li>M2 (15) angled down and price above M2</li><li>M2 below M3 (50)</li></ul></li><li><p>EMA fast</p><ul><li>Is m2 (50) angled up and price is below m2</li><li>Is angle higher than (6, white) and above 4 hour HH2?</li><li>Or, 15 line is above HH2 or distance is very close and is slowing down</li></ul></li><li><p>Candles - 4 hour</p><ul><li>Red and price higher than candle high</li></ul></li><li><p>Filters</p><ul><li>Not when Green and yellow H5</li></ul></li></ul></th></tr></thead><tbody></tbody></table>

&nbsp;

id: 452b72fd9b264419bfb251a0161bcefb
parent_id: 1c80e5a7fca846b59bab6913fdd5479d
created_time: 2024-08-25T20:09:18.977Z
updated_time: 2024-08-29T00:15:07.652Z
is_conflict: 0
latitude: 20.76442750
longitude: -156.44500630
altitude: 0.0000
author: 
source_url: 
is_todo: 0
todo_due: 0
todo_completed: 0
source: joplin-desktop
source_application: net.cozic.joplin-desktop
application_data: 
order: 0
user_created_time: 2024-08-25T20:09:18.977Z
user_updated_time: 2024-08-29T00:15:07.652Z
encryption_cipher_text: 
encryption_applied: 0
markup_language: 1
is_shared: 0
share_id: 
conflict_original_id: 
master_key_id: 
user_data: 
deleted_time: 0
type_: 1                                                                                                                                                     4dc66eec98b5427eac6aa5f573a29e79.md                                                                 000644                  0000001432  14663736631 013011  0                                                                                                    ustar 00                                                                000000  000000                                                                                                                                                                         Oahu Trip

# Oahu Trip

Hotel - 1440

- Is there a charge for parking
- Is there a cleaning fee

Flights 130-140

Car 150

Food 200

Gas 180

id: 4dc66eec98b5427eac6aa5f573a29e79
parent_id: 1c80e5a7fca846b59bab6913fdd5479d
created_time: 2024-08-26T00:00:14.572Z
updated_time: 2024-08-26T02:11:32.830Z
is_conflict: 0
latitude: 20.76442750
longitude: -156.44500630
altitude: 0.0000
author: 
source_url: 
is_todo: 0
todo_due: 0
todo_completed: 0
source: joplin-desktop
source_application: net.cozic.joplin-desktop
application_data: 
order: 0
user_created_time: 2024-08-26T00:00:14.572Z
user_updated_time: 2024-08-26T02:11:32.830Z
encryption_cipher_text: 
encryption_applied: 0
markup_language: 1
is_shared: 0
share_id: 
conflict_original_id: 
master_key_id: 
user_data: 
deleted_time: 0
type_: 1                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      