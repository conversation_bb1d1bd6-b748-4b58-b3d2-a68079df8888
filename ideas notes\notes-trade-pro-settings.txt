Group - Position Entry Settings
Bool - Enable Long Entries 
    Tooltip - If disabled strategy will not open Long Positions
Bool - Enable Short Entries
    Tooltip - If disabled strategy will not open Short Positions

Entry Type 
    options - Market Limit Stop
    Tooltip - Select the type of entry order you wish the strategy to take:
    1. Market: Once the entry criteria is valid, place a market order and enter the position immediately
    2. Limit: Once the entry criteria is valid, place a pending limit entry order (below the current price for longs, above the current price for shorts)
    3. Stop: Once the entry criteria is valid, place a pending stop entry order (above the cureent price for longs, below the current price for shorts)

Entry Stategy
    options - ATR based, % based, Tick based Wick based,Pivot based
    Tooltip - How far to place the Limiti or Stop order from the entry price, once the entry criteria is valid:
    1. ATR based: The entry will be submitted at the current price plus / minus the ATR, depending on the Entry Type and the direction of the trade
    2. % based: The entry will be submitted at the current price plus / minus the given percentage, depending on the Entry Type and the direction of the trade
    3. Tick based: The entry will be submitted at the current price plus / minus the given amount of ticks, depending on the Entry Type and the direction of the trade
    4. Wick based: The entry will be submitted at the wick of the candle. Whether it is at the high or the low of the candle, depends on the Entry Type and the direction of the trade
    5. Pivot based: The entry will be submitted at the last pivot low or pivot high, depending on the Entry Type and the direction of the trade.

ATR Length
    Tooltip - How many historical bars to calculate the ATR on. Only used with the ATR based entry strategy.

ATR Multiplier
    Tooltip - How many times the ATR to place the limit entry order from the current price. Only used with the ATR based entry strategy.

%
    Tooltip - The % at which to place the entry order above / below the current price. Only used with the % based entry strategy.

ticks - 
    default - 500
    Tooltip - How many ticks from the current price should the entry be placed. Only used with the 'Tick based' strategy. See the Trade Widget.

Pivot Lookback
    default - 14
    Tooltip - How many historical bars to look at for determining the last pivot. Only used with the Pivot based entry strategy.

Pivot Include Last candle
    default - no
    Tooltip - Choose whether to use the last candle when determining the pivot. Only used with the Pivot based entry strategy.

Restrict Entries to Date Range
    Tooltip
    Limit the strategy to only enter into a position between the given dates.

    Please note that the strategy can only backtest on data TradingView has made available to you. The amount of bars available to you to backtest on depends on your TradingView subscription.

    For example, if you have a free subscription, TradingView will give you access to 5000 historical bars. On a 1 hour chart, that means that the strategy can only backtest on 5000 hours worth of price data, which is ablut 208 days. On a 1 minute chart, it will be 5000 minutes, which is about 3.5 days






