//@version=4
//[SHK] STC colored indicator
//https://www.tradingview.com/u/shayankm/

study(title="STC overlay", shorttitle="STC overlay", overlay=true)
stc_signal = input(12,"Length") // 12
stc_fast = input(26,"FastLength") // 26
stc_slow = input(50,"SlowLength") // 50
AAA      = input(0.45,title="Bias", type=input.float,step=0.1) // 0.203

angle(_src) =>
    rad2degree=180/3.14159265359  //pi 
    ang=rad2degree*atan((_src[0] - _src[1])/atr(2))

stc_macd(BBB, stc_fast, stc_slow) =>
    stc_fastMA = ema(BBB, stc_fast)
    stc_slowMA = ema(BBB, stc_slow)
    stc_macd = stc_fastMA - stc_slowMA
    stc_macd
    
AAAAA(stc_signal, stc_fast, stc_slow) => 
    
    var CCCCC = 0.0
    var DDD = 0.0
    var DDDDDD = 0.0
    var EEEEE = 0.0
    tmp_macd = stc_macd(close,stc_fast,stc_slow)     
    lowest = lowest(tmp_macd, stc_signal)
    highest = highest(tmp_macd, stc_signal) - lowest    
    CCCCC := (highest > 0 ? ((tmp_macd - lowest) / highest) * 100 : nz(CCCCC[1])) 
    DDD := (na(DDD[1]) ? CCCCC : DDD[1] + (AAA * (CCCCC - DDD[1]))) 
    DDDD = lowest(DDD, stc_signal) 
    DDDDD = highest(DDD, stc_signal) - DDDD     
    DDDDDD := (DDDDD > 0 ? ((DDD - DDDD) / DDDDD) * 100 : nz(DDDDDD[1])) 
    EEEEE := (na(EEEEE[1]) ? DDDDDD : EEEEE[1] + (AAA * (DDDDDD - EEEEE[1])))
    EEEEE

mAAAAA = AAAAA(stc_signal,stc_fast,stc_slow)
mColor = mAAAAA > mAAAAA[1] ? color.new(color.green,20) : color.new(color.red,20)
stc_low = input(0.1,"STC low",type=input.float,step=0.1) 
stc_high = input(99.5,"STC high",type=input.float,step=0.1)   //75
stc_line = 49//(stc_high + stc_low) * 0.5
stc = mAAAAA 

stc_color = stc>stc_high ? #4caf50 : stc<stc_low ? #ff5252 : na
plotshape(stc>stc_high?1:na, color=#4caf50,style=shape.triangledown,location=location.top)
plotshape(stc<stc_low?1:na, color=#ff5252,style=shape.triangleup,location=location.bottom)