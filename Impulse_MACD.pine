//@version=5
//
// <AUTHOR> 
// 
// List of my public indicators: http://bit.ly/1LQaPK8 
// List of my app-store indicators: http://blog.tradingview.com/?p=970 
//
//
indicator('Impulse MACD - V5', overlay=false)

red = #ff0062
orange = #ff9800
yellow = #FFFF00
green = #4caf50
lime = #50e600
aqua = #00bcd4
blue = #2962ff
dark_blue = #2962ff
violet = #814dff
white = #ffffff
gray = #707070
black = #000000

g_impulse_macd = 'Impulse MACD ----------------------------------------------------'
macd_time = input.timeframe("",title="Timeframe", group=g_impulse_macd)
macd_time_mult = input.timeframe("30",title="Timeframe Multi", group=g_impulse_macd)
macd_len = input(34, title='Length MA', group=g_impulse_macd)
macd_s_len = input(9, title='Length Signal', group=g_impulse_macd)
macd_src = input.source(hlc3, "Source", group=g_impulse_macd) 
macd_show = input.bool(true, title='Show MACD', group=g_impulse_macd)
macd_show_multi = input.bool(true, title='Show MACD Multi', group=g_impulse_macd)

min_tick = syminfo.mintick
var int decimals = int(math.log10(1/min_tick))
decimals := decimals == 2 ? 1 : decimals == 0 ? 1 : decimals
// plot(min_tick, title='Min Tick')
// plot(decimals, title='Decimals')
get_pip_value(point1, point2, abs_value) =>
    diff_points = abs_value ? math.abs( (point1 - point2) ) : point1 - point2
    pip_value = decimals<3 ? diff_points / syminfo.mintick / 100 : diff_points / syminfo.mintick / 10
    //pip_value = decimals>3 ? diff_points * (math.pow(10, decimals) * syminfo.mintick) : diff_points / syminfo.mintick / 10


newbar(res) => 
    ta.change(time(res)) == 0 ? 0 : 1
    
macd_change = newbar(macd_time_mult)
//plot(macd_change, title='Change')

calc_smma(src, len) =>
    smma = 0.0
    sma_1 = ta.sma(src, len)
    smma := na(smma[1]) ? sma_1 : (smma[1] * (len - 1) + src) / len
    smma

calc_zlema(src, length) =>
    ema1 = ta.ema(src, length)
    ema2 = ta.ema(ema1, length)
    d = ema1 - ema2
    ema1 + d

macd_fun()=>
    macd_hi = calc_smma(high, macd_len)
    macd_lo = calc_smma(low, macd_len)
    macd_mi = calc_zlema(macd_src, macd_len)
    macd    = macd_mi > macd_hi ? macd_mi - macd_hi : macd_mi < macd_lo ? macd_mi - macd_lo : 0
    macd_s  = ta.sma(macd, macd_s_len)
    macd_h  = macd - macd_s

    // if i_multiple 
    //     macd_hi := macd_hi * decimals

    [macd_hi, macd_lo, macd_mi, macd, macd_s, macd_h]

[macd_hi, macd_lo, macd_mi, macd, macd_s, macd_h] = request.security(syminfo.tickerid, macd_time, macd_fun() )
[macd_hi_m, macd_lo_m, macd_mi_m, macd_m, macd_s_m, macd_h_m] = request.security(syminfo.tickerid, macd_time_mult, macd_fun() )

macd_m := ta.change(macd_change)    ? macd_m : macd_m[1]
macd_s_m := ta.change(macd_change)  ? macd_s_m : macd_s_m[1]
macd_h_m := ta.change(macd_change)  ? macd_h_m : macd_h_m[1]

macd_mdc   = macd_src > macd_mi ? macd_src > macd_hi ? lime : green : macd_src < macd_lo ? red : orange
macd_mdc_m = macd_src > macd_mi_m ? macd_src > macd_hi_m ? lime : green : macd_src < macd_lo_m ? red : orange

// MACD States
macd_up = macd > 0 and macd_s > 0
macd_dn = macd < 0 and macd_s < 0
// Multi
macd_up_m = macd_m > 0 and macd_s_m > 0
macd_dn_m = macd_m < 0 and macd_s_m < 0
macd_color = 
 macd_up_m and macd_h_m > 0 ? red 
 : macd_up_m and macd_h_m < 0 ? orange
 : macd_dn_m and macd_h_m > 0 ? yellow
 : macd_dn_m and macd_h_m < 0 ? green 
 : (macd_m>=0 and macd_s_m<0) or (macd_m<=0 and macd_s_m>0) ? white 
 : na
//  macd_up_m and macd_h_m > 0 and macd_m < macd_m[120] ? dark_blue
//  : macd_up_m and macd_h_m < 0 and macd_m > macd_m[120] ? yellow

macd_c_prev = ta.crossunder(macd_m, macd_s_m) or ta.crossover(macd_m, macd_s_m) ? macd_color[1] : macd_color
plot(macd_m, title='MACD Color', color=color.new(macd_color, 100) )

// Calculate Decimals
macd_dist = get_pip_value(0, macd_m, true)
//plot(macd_dist, title='MACD Dist', color=color.new(macd_color, 100) )

plot(0, color=color.new(color.gray, 0), linewidth=1, title='MidLine')
// MACD
plot(macd_show ? macd : na, title='MACD', color=dark_blue )
plot(macd_show ? macd_s : na, title='Signal', color=color.new(orange, 0))
plot(macd_show ? macd_h : na, title='Histo', color=macd_h>0 ? color.new(red, 25) : color.new(green, 25), style=plot.style_columns)

// MACD MULTI
plot(macd_show_multi ? macd_m : na, title='MACD', color=blue, linewidth=2 )
plot(macd_show_multi ? macd_s_m : na, title='Signal', color=color.new(yellow, 0), linewidth=2)
plot(macd_show_multi ? macd_h_m : na, title='Histo', color=color.new(gray, 60), linewidth=2, style=plot.style_columns)

macd_candles = input(false, title='Enable bar colors')
barcolor(macd_candles ? macd_color : na)



