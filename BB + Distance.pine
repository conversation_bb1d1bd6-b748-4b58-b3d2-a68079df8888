//@version=4
study(shorttitle="basis_distance", title="BB + Distance")

angle(_src, len) =>
    rad2degree=180/3.14159265359  //pi 
    ang=rad2degree*atan((_src[0] - _src[1])/atr(len))

// === EMA 200 ===
// ==================================================
len_200 = 200 // input(200, minval=1, title="EMA 200")
ema_200 = ema(close, len_200)

// Bollinger Bands Inputs
bb_use_ema = input(false, title="Use EMA for Bollinger Band")
bb_length = input(20, minval=1, title="Bollinger Length") // 50
bb_source = input(close, title="Bollinger Source")
bb_mult = 2.0 // input(2.0, title="Base Multiplier", minval=0.5, maxval=10)

// === SERIES ===

// Breakout Indicator Inputs
ema_1 = ema(bb_source, bb_length)
sma_1 = sma(bb_source, bb_length)
bb_basis = bb_use_ema ? ema_1 : sma_1
// Deviation
dev = stdev(bb_source, bb_length)
bb_dev = bb_mult * dev
// Upper bands
bb_upper = bb_basis + bb_dev
// Lower Bands
bb_lower = bb_basis - bb_dev

// Calculate Upper and Lower band painting offsets based on 50% of atr.
bb_offset = atr(14) * 0.5
bb_sqz_upper = bb_upper + bb_offset
bb_sqz_lower = bb_lower - bb_offset

bb_diff = (bb_upper - bb_lower) * 10
bbu_ema = (bb_upper-ema_200) * 10
bbu_angle = angle(bb_upper,2)
bbl_ema = (bb_lower-ema_200) * 10
bbl_angle = angle(bb_lower,2)

//plot(bb_diff ,title="BB Distance",color=color.green,style=plot.style_columns)
plot(bbu_ema ,title="BBU to EMA",color=color.red,style=plot.style_columns)
plot(bbu_angle ,title="BBU Angle",color=color.green,style=plot.style_columns)
plot(bbl_ema ,title="BBL to EMA",color=color.red,style=plot.style_columns)
plot(bbl_angle ,title="BBL Angle",color=color.blue,style=plot.style_columns)
