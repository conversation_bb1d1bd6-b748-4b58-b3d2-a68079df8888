// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © RedKTrader

//@version=5

indicator('RedK Magic Ribbon', shorttitle='MagicRibbon v5.0', overlay=true, timeframe='', timeframe_gaps=false)
// Jan31, 2023: Release Notes for version 5.0
// ------------------------------
// Add more optional MA's and standardizing their colors for consistency with other MA's & charts
// updated LazyLine() with latest version 
// display for MA values are only in the data window to reduce clutter

//====================================================================
f_LazyLine(_data, _length) =>
    w1 = 0,     w2 = 0,     w3 = 0
    L1 = 0.0,   L2 = 0.0,   L3 = 0.0
    
    w = _length / 3

    if _length > 4
        w2 := math.round(w)
        w1 := math.round((_length - w2) / 2)
        w3 := int((_length - w2) / 2)

        L1 := ta.wma(_data, w1)
        L2 := ta.wma(L1, w2)
        L3 := ta.wma(L2, w3)
        L3
    else
        L3 := ta.wma(_data, _length)
    
    L3
//=====================================================================

f_CoraWave(source, length, s) =>
    numerator = 0.0,    denom = 0.0
    c_weight = 0.0,     r_multi = 2.0
    
    Start_Wt = 0.01  // Start Weight & r_multi are set to basic values here.
    End_Wt = length  // use length as initial End Weight to calculate base "r"

    r = math.pow(End_Wt / Start_Wt, 1 / (length - 1)) - 1
    base = 1 + r * r_multi

    for i = 0 to length - 1 by 1
        c_weight := Start_Wt * math.pow(base, length - i)
        numerator += source[i] * c_weight
        denom += c_weight
        denom

    cora_raw = numerator / denom

    cora_wave = ta.wma(cora_raw, s)
    cora_wave
// ======================================================================    


Source_1    = input.source(close,   'Source',           inline='CRMA1', group='CoRa Wave (Fast MA)')
Length_1    = input.int(10,         'Length', minval=1, inline='CRMA1', group='CoRa Wave (Fast MA)')
smooth      = input.int(3,          'Smooth', minval=1, inline='CRMA2', group='CoRa Wave (Fast MA)')

Source_2    = input.source(close,   'Source',               inline='RSSMA', group='RSS_WMA (Slow MA)')
Length_2    = input.int(15,         'Smoothness', minval=1, inline='RSSMA', group='RSS_WMA (Slow MA)')

ShowFill    = input.bool(true, 'Ribbon Fill?', group='RSS_WMA (Slow MA)')

FastLine    = f_CoraWave(Source_1, Length_1, smooth)
SlowLine    = f_LazyLine(Source_2, Length_2)

c_fup       = color.new(color.aqua, 30)
c_fdn       = color.new(color.orange, 30)
Fast_up     = FastLine > FastLine[1]
Fast_dn     = FastLine < FastLine[1]

c_sup       = color.new(#33ff00, 0)
c_sdn       = color.new(#ff1111, 0)
Slow_up     = SlowLine > SlowLine[1]
Slow_dn     = SlowLine < SlowLine[1]

FastPlot    = plot(FastLine, title='Fast Line', color=Fast_up ? c_fup : c_fdn, linewidth=2)
SlowPlot    = plot(SlowLine, title='Slow Line', color=Slow_up ? c_sup : c_sdn, linewidth=2)

c_rup       = color.new(#33ff00, 70)
c_rdn       = color.new(#ff1111, 70)
c_rsw       = color.new(color.gray, 70)

Ribbon_up   = Fast_up and Slow_up
Ribbon_dn   = not Fast_up and not Slow_up

fill(FastPlot, SlowPlot, title='Ribbon Fill', color=ShowFill ? Ribbon_up ? c_rup : Ribbon_dn ? c_rdn : c_rsw : na)

// ======================================================================================================
// v3.0 adds 2 optional MA's - to enable us to track what many other traders are working with
// the below code is based on the built-in MA Ribbon in the TV library - with some modifications

// ======================================================================    
f_ma(source, length, type) =>
    type == 'SMA' ? ta.sma(source, length) : 
      type == 'EMA' ? ta.ema(source, length) : 
      ta.wma(source, length)
// ======================================================================    

gr_ma       = 'Optional Moving Averages'
t_ma1       = 'MA #1'
t_ma2       = 'MA #2'
t_ma3       = 'MA #3'
t_ma4       = 'MA #4'


show_ma1    = input.bool(false, t_ma1,                                  inline=t_ma1, group=gr_ma)
ma1_type    = input.string('SMA', '', options=['SMA', 'EMA', 'WMA'],    inline=t_ma1, group=gr_ma)
ma1_source  = input.source(close, '',                                   inline=t_ma1, group=gr_ma)
ma1_length  = input.int(10, '', minval=1,                               inline=t_ma1, group=gr_ma)

ma1_color   = color.new(#ffeb3b,0)
ma1         = f_ma(ma1_source, ma1_length, ma1_type)
plot(show_ma1 ? ma1 : na, color=ma1_color, title=t_ma1, linewidth=1, display = display.pane + display.data_window)

show_ma2    = input.bool(false, t_ma2,                                  inline=t_ma2, group=gr_ma)
ma2_type    = input.string('SMA', '', options=['SMA', 'EMA', 'WMA'],    inline=t_ma2, group=gr_ma)
ma2_source  = input.source(close, '',                                   inline=t_ma2, group=gr_ma)
ma2_length  = input.int(20, '', minval=1,                              inline=t_ma2, group=gr_ma)

ma2_color   = color.new(#f57c00,0)
ma2         = f_ma(ma2_source, ma2_length, ma2_type)
plot(show_ma2 ? ma2 : na, color=ma2_color, title=t_ma2, linewidth=1, display = display.pane + display.data_window)

show_ma3    = input.bool(false, t_ma3,                                  inline=t_ma3, group=gr_ma)
ma3_type    = input.string('SMA', '', options=['SMA', 'EMA', 'WMA'],    inline=t_ma3, group=gr_ma)
ma3_source  = input.source(close, '',                                   inline=t_ma3, group=gr_ma)
ma3_length  = input.int(50, '', minval=1,                               inline=t_ma3, group=gr_ma)

ma3_color   = color.new(#ab47bc,0)
ma3         = f_ma(ma3_source, ma3_length, ma3_type)
plot(show_ma3 ? ma3 : na, color=ma3_color, title=t_ma3, linewidth=1, display = display.pane + display.data_window)

show_ma4    = input.bool(false, t_ma4,                                  inline=t_ma4, group=gr_ma)
ma4_type    = input.string('SMA', '', options=['SMA', 'EMA', 'WMA'],    inline=t_ma4, group=gr_ma)
ma4_source  = input.source(close, '',                                   inline=t_ma4, group=gr_ma)
ma4_length  = input.int(100, '', minval=1,                              inline=t_ma4, group=gr_ma)

ma4_color   = color.new(#2424f0,0)
ma4         = f_ma(ma4_source, ma4_length, ma4_type)
plot(show_ma4 ? ma4 : na, color=ma4_color, title=t_ma4, linewidth=1, display = display.pane + display.data_window)


// ======================================================================================================
// v4.0 adds alerts for Fast and Slow swinging to a new move up or new move down
// This is easier than looking for the visual signal / color change .. as requested
// The main signal is really when the ribbon "agrees" on a spcific color 
// ======================================================================================================


Alert_Fastup = Fast_up and not Fast_up[1]
Alert_Fastdn = Fast_dn and not Fast_dn[1]

Alert_Slowup = Slow_up and not Slow_up[1]
Alert_Slowdn = Slow_dn and not Slow_dn[1]

alertcondition(Alert_Fastup, 'Fast Line Swings Up',     'MagicRibbon - Fast Line Swing Up Detected!')
alertcondition(Alert_Fastdn, 'Fast Line Swings Down',   'MagicRibbon - Fast Line Swing Down Detected!')

alertcondition(Alert_Slowup, 'Slow Line Swings Up',     'MagicRibbon - Slow Line Swing Up Detected!')
alertcondition(Alert_Slowdn, 'Slow Line Swings Down',   'MagicRibbon - Slow Line Swing Down Detected!')

