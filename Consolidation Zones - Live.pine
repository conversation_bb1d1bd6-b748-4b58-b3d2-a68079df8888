// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © LonesomeTheBlue

//@version=5
indicator('Consolidation Zones - Live - copy', overlay=true, max_lines_count=400)
g_cz = 'Consolidation Zones -------------------------------------------------------'
cz_show = input(false, title='Show', group=g_cz)
cz_time = input.timeframe('60', "Resolution") // Hourly
cz_prd = input.int(defval=50, title='Loopback Period', group=g_cz) // 20, 10
cz_len = input.int(defval=2, title='Min Consolidation Length', minval=2, maxval=20, group=g_cz) // 5, 2
cz_show_area = input(defval=true, title='Paint Consolidation Area ', group=g_cz)
cz_zonecol = input(defval=color.new(color.blue, 70), title='Zone Color', group=g_cz)

float cz_hb_ = ta.highestbars(cz_prd) == 0 ? high : na
float cz_lb_ = ta.lowestbars(cz_prd) == 0 ? low : na
var int cz_dir = 0
float cz_zz = na
float cz_pp = na

plot(cz_hb_,color=color.new(color.blue,100))

cz_iff_1 = cz_lb_ and na(cz_hb_) ? -1 : cz_dir
cz_dir := cz_hb_ and na(cz_lb_) ? 1 : cz_iff_1
if cz_hb_ and cz_lb_
    if cz_dir == 1
        cz_zz := cz_hb_
        cz_zz
    else
        cz_zz := cz_lb_
        cz_zz
else
    cz_iff_1 = cz_lb_ ? cz_lb_ : na
    cz_zz := cz_hb_ ? cz_hb_ : cz_iff_1
    cz_zz

for x = 0 to 1000 by 1
    if na(close) or cz_dir != cz_dir[x]
        break
    if cz_zz[x]
        if na(cz_pp)
            cz_pp := cz_zz[x]
            cz_pp
        else
            if cz_dir[x] == 1 and cz_zz[x] > cz_pp
                cz_pp := cz_zz[x]
                cz_pp
            if cz_dir[x] == -1 and cz_zz[x] < cz_pp
                cz_pp := cz_zz[x]
                cz_pp



var int cz_conscnt = 0
var float cz_condhigh = na
var float cz_condlow = na
float cz_H_ = ta.highest(cz_len)
float cz_L_ = ta.lowest(cz_len)
var line cz_upline = na
var line cz_dnline = na
bool cz_breakoutup = false
bool cz_breakoutdown = false

if ta.change(cz_pp)
    if cz_conscnt > cz_len
        if cz_pp > cz_condhigh
            cz_breakoutup := true
            cz_breakoutup
        if cz_pp < cz_condlow
            cz_breakoutdown := true
            cz_breakoutdown
    if cz_conscnt > 0 and cz_pp <= cz_condhigh and cz_pp >= cz_condlow
        cz_conscnt += 1
        cz_conscnt
    else
        cz_conscnt := 0
        cz_conscnt
else
    cz_conscnt += 1
    cz_conscnt

if cz_conscnt >= cz_len and cz_show
    if cz_conscnt == cz_len
        cz_condhigh := cz_H_
        cz_condlow := cz_L_
        cz_condlow
    else
        line.delete(cz_upline)
        line.delete(cz_dnline)
        cz_condhigh := math.max(cz_condhigh, high)
        cz_condlow := math.min(cz_condlow, low)
        cz_condlow

    cz_upline := line.new(bar_index, cz_condhigh, bar_index - cz_conscnt, cz_condhigh, color=color.red, style=line.style_dashed)
    cz_dnline := line.new(bar_index, cz_condlow, bar_index - cz_conscnt, cz_condlow, color=color.lime, style=line.style_dashed)
    cz_dnline

fill(plot(cz_condhigh, color=na, style=plot.style_stepline), plot(cz_condlow, color=na, style=plot.style_stepline), color=cz_show_area and cz_conscnt > cz_len and cz_show ? cz_zonecol : color.new(color.white, 100), transp=90)

var float cz_condMid_ch = 0.0
cz_condMid = cz_condhigh or cz_condlow ? (cz_condhigh + cz_condlow) * 0.5 : na
cz_condMid_ch := ta.change(cz_condMid)
plot(cz_show ? cz_condMid : na, color=#ffff00, style=plot.style_stepline)
plot( ta.change(cz_condMid) and cz_show ? ta.change(cz_condMid) : cz_condMid_ch[1] , title="Change")
// cz_buy_cond = cz_condMid<emaSlow and close>cz_condMid
// plotshape(cz_buy_cond,title="Buy 1",color=color.green ,style=shape.circle,location=location.bottom)

// alertcondition(cz_breakoutup, title='Breakout Up', message='Breakout Up')
// alertcondition(cz_breakoutdown, title='Breakout Down', message='Breakout Down')

