//@version=5
strategy(title='Strategy Time Example', shorttitle='Strategy Time Example', overlay=true, pyramiding=0, default_qty_type=strategy.percent_of_equity, default_qty_value=10, currency=currency.USD)

red = #ff0000
green = #00ff00
// Revision:        1
// Author:          @JayRogers
//
// *** THIS IS JUST AN EXAMPLE OF STRATEGY TIME LIMITING ***
//
//  This is a follow up to my previous strategy example for risk management, extended to include a time limiting factor.

// === GENERAL INPUTS ===
// short ma
maFastSource = input(defval=open, title='Fast MA Source')
maFastLength = input.int(defval=14, title='Fast MA Period', minval=1)
// long ma
maSlowSource = input(defval=open, title='Slow MA Source')
maSlowLength = input.int(defval=21, title='Slow MA Period', minval=1)

// === STRATEGY RELATED INPUTS ===
tradeInvert = input(defval=false, title='Invert Trade Direction?')
// Risk management
inpTakeProfit = input.int(defval=1000, title='Take Profit', minval=0)
inpStopLoss = input.int(defval=200, title='Stop Loss', minval=0)
inpTrailStop = input.int(defval=200, title='Trailing Stop Loss', minval=0)
inpTrailOffset = input.int(defval=0, title='Trailing Stop Loss Offset', minval=0)
// *** FOCUS OF EXAMPLE ***
// Time limiting
// a toggle for enabling/disabling
useTimeLimit = input(defval=true, title='Use Start Time Limiter?')
// set up where we want to run from
startYear = input.int(defval=2016, title='Start From Year', minval=0, step=1)
startMonth = input.int(defval=05, title='Start From Month', minval=0, step=1)
startDay = input.int(defval=01, title='Start From Day', minval=0, step=1)
startHour = input.int(defval=00, title='Start From Hour', minval=0, step=1)
startMinute = input.int(defval=00, title='Start From Minute', minval=0, step=1)

// === RISK MANAGEMENT VALUE PREP ===
// if an input is less than 1, assuming not wanted so we assign 'na' value to disable it.
useTakeProfit = inpTakeProfit >= 1 ? inpTakeProfit : na
useStopLoss = inpStopLoss >= 1 ? inpStopLoss : na
useTrailStop = inpTrailStop >= 1 ? inpTrailStop : na
useTrailOffset = inpTrailOffset >= 1 ? inpTrailOffset : na

// *** FOCUS OF EXAMPLE ***
// === TIME LIMITER CHECKING FUNCTION ===
// using a multi line function to return true or false depending on our input selection
// multi line function logic must be indented.
startTimeOk() =>
    // get our input time together
    inputTime = timestamp(syminfo.timezone, startYear, startMonth, startDay, startHour, startMinute)
    // check the current time is greater than the input time and assign true or false
    timeOk = time > inputTime ? true : false
    // last line is the return value, we want the strategy to execute if..
    // ..we are using the limiter, and the time is ok -OR- we are not using the limiter
    r = useTimeLimit and timeOk or not useTimeLimit
    r

// === SERIES SETUP ===
/// a couple of ma's..
maFast = ta.ema(maFastSource, maFastLength)
maSlow = ta.ema(maSlowSource, maSlowLength)

// === PLOTTING ===
fast = plot(maFast, title='Fast MA', color=color.new(green, 50), linewidth=2)
slow = plot(maSlow, title='Slow MA', color=color.new(red, 50), linewidth=2)

// === LOGIC ===
// is fast ma above slow ma?
aboveBelow = maFast >= maSlow ? true : false
// are we inverting our trade direction?
tradeDirection = tradeInvert ? aboveBelow ? false : true : aboveBelow ? true : false

// *** FOCUS OF EXAMPLE ***
// wrap our strategy execution in an if statement which calls the time checking function to validate entry
// like the function logic, content to be included in the if statement must be indented.
var int counter = 1
if startTimeOk()
    n_long = "L " 
    n_short = "S " 
    n_long_exit = "Exit Long " + str.tostring(counter)
    n_short_exit = "Exit Short " + str.tostring(counter)
    // === STRATEGY - LONG POSITION EXECUTION ===
    enterLong = not tradeDirection[1] and tradeDirection
    exitLong = tradeDirection[1] and not tradeDirection
    strategy.entry(id=n_long, direction=strategy.long, when=enterLong)
    strategy.close(id=n_long, when=exitLong)

    // === STRATEGY - SHORT POSITION EXECUTION ===
    enterShort = tradeDirection[1] and not tradeDirection
    exitShort = not tradeDirection[1] and tradeDirection
    strategy.entry(id=n_short, direction=strategy.short, when=enterShort)
    strategy.close(id=n_short, when=exitShort)

    // === STRATEGY RISK MANAGEMENT EXECUTION ===
    strategy.exit('Exit Long', from_entry=n_long, profit=useTakeProfit, loss=useStopLoss, trail_points=useTrailStop, trail_offset=useTrailOffset)
    strategy.exit('Exit Short', from_entry=n_short, profit=useTakeProfit, loss=useStopLoss, trail_points=useTrailStop, trail_offset=useTrailOffset)
    counter := counter + 1

