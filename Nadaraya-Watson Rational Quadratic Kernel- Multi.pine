// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © jdehorty
// @version=5

// A non-repainting implementation of Nadaraya–Watson Regression using a Rational Quadratic Kernel. 

indicator('Nadaraya-Watson: RQK - Multi', overlay=true)

red = #ff0062
orange = #ff9800
yellow = #FFFF00
green = #005a04
lime = #00E676
aqua = #00bcd4
blue = #2962ff
dark_blue = #2962ff
violet = #814dff
white = #ffffff
gray = #707070

angle(_src, len) =>
    rad2degree=180/3.14159265359  //pi 
    ang=rad2degree*math.atan((_src[0] - _src[1]) / ta.atr(len))

get_point_value(point1, point2, abs_value) =>
    // JPY - 0.001
    // Forex - 0.00001
    // NAS - 0.01
    // Gold - 0.01
    // BTC - 0.1
    float diff_points = 0.0
    float point_value = 0.0
    diff_points := abs_value ? math.abs( (point1 - point2) ) : point1 - point2
    point_value := diff_points / syminfo.mintick / 10
    //point_value := diff_points / close * 100

is_between(p1, p2) =>
    is_inside = p1>p2 ? (close<p1) and (open<p1) and (close>p2) and (open>p2) : (close>p1) and (open>p1) and (close<p2) and (open<p2)
    is_inside
    

//MA
i_rqk_use_multi = input.bool(false, title="Use Multi Timeframe")
i_rqk_resCustom = input.timeframe(title='Timeframe', defval='')
i_rqk_show_ma = input.bool(false, title='Show Ma\'s ')
i_rqk_angle_amount = input.int(3, title='Angle Amount')
i_rqk_conv = input.int(30, title='Convergence Amount')
i_rqk_show_between = input.bool(false, title='Show Between ')

rqk_m2 = ta.hma(close,20)
rqk_m2_a = angle(rqk_m2,i_rqk_angle_amount)
rqk_m4 = ta.hma(close,75)
rqk_m4_a = angle(rqk_m4,i_rqk_angle_amount)
rqk_m8 = ta.hma(close,1000)
rqk_m8_a = angle(rqk_m8,i_rqk_angle_amount)


// Settings
i_bars_merge = input.bool(false, title='Lookahead On')
rqk_src = input.source(close, 'Source')
rqk_h = input.float(20.0, 'Lookback Window', tooltip='The number of bars used for the estimation. This is a sliding value that represents the most recent historical bars. Recommended range: 3-50') // 8
rqk_r = input.float(20.0, 'Relative Weighting', step=0.25, tooltip='Relative weighting of time frames. As this value approaches zero, the longer time frames will exert more influence on the estimation. As this value approaches infinity, the behavior of the Rational Quadratic Kernel will become identical to the Gaussian kernel. Recommended range: 0.25-25') // 8
rqk_x_0 = input.int(100, "Start Regression at Bar", tooltip='Bar index on which to start regression. The first bars of a chart are often highly volatile, and omission of these initial bars often leads to a better overall fit. Recommended range: 5-25') // 4 6 25
rqk_smoothColors = input.bool(false, "Smooth Colors", tooltip="Uses a crossover based mechanism to determine colors. This often results in less color transitions overall.", inline='1', group='Colors') // false
rqk_lag = input.int(1, "Lag", tooltip="Lag for crossover detection. Lower values result in earlier crossovers. Recommended range: 1-2", inline='1', group='Colors') // 2
rqk_size = array.size(array.from(rqk_src)) // size of the data series

//plot(size,title="array size",color=color.new(blue,100))

// Further Reading:
// The Kernel Cookbook: Advice on Covariance functions. David Duvenaud. Published June 2014.
// Estimation of the bandwidth parameter in Nadaraya-Watson kernel non-parametric regression based on universal threshold level. Ali T, Heyam Abd Al-Majeed Hayawi, Botani I. Published February 26, 2021.
kernel_regression(_src, _size, _h) =>
    float _currentWeight = 0.
    float _cumulativeWeight = 0.
    for i = 0 to _size + rqk_x_0
        y = _src[i] 
        w = math.pow(1 + (math.pow(i, 2) / ((math.pow(_h, 2) * 2 * rqk_r))), -rqk_r)
        _currentWeight += y*w
        _cumulativeWeight += w
    _currentWeight / _cumulativeWeight

bull_bear() =>
    // Estimations
    yhat1 = kernel_regression(rqk_src, rqk_size, rqk_h)
    yhat2 = kernel_regression(rqk_src, rqk_size, rqk_h - rqk_lag)

    // Rates of Change
    bool wasBearish = yhat1[2] > yhat1[1]
    bool wasBullish = yhat1[2] < yhat1[1]
    bool isBearish = yhat1[1] > yhat1
    bool isBullish = yhat1[1] < yhat1
    bool isBearishChange = isBearish and wasBullish
    bool isBullishChange = isBullish and wasBearish

    // Crossovers
    bool isBullishCross = ta.crossover(yhat2, yhat1)
    bool isBearishCross = ta.crossunder(yhat2, yhat1) 
    bool isBullishSmooth = yhat2 > yhat1
    bool isBearishSmooth = yhat2 < yhat1

    [yhat1,isBullish,isBearish,isBearishChange,isBullishChange,isBullishCross,isBearishCross,isBullishSmooth,isBearishSmooth, wasBearish, wasBullish]

[yhat1,isBullish,isBearish,isBearishChange,isBullishChange,isBullishCross,isBearishCross,isBullishSmooth,isBearishSmooth, wasBearish, wasBullish] = bull_bear()


rqk = request.security(syminfo.tickerid, i_rqk_use_multi? i_rqk_resCustom : "", yhat1, lookahead=i_bars_merge ? barmerge.lookahead_on : barmerge.lookahead_off)
rqk_a = request.security(syminfo.tickerid, i_rqk_use_multi? i_rqk_resCustom : "", angle(rqk,i_rqk_angle_amount), lookahead=i_bars_merge ? barmerge.lookahead_on : barmerge.lookahead_off)

rqk_bwt =  is_between(rqk, rqk_m8)
barcolor(i_rqk_show_between and rqk_bwt ? green : i_rqk_show_between and not rqk_bwt ? red : na, title='Is between RQK and M8')



// Colors
rqk_buy_col = #3AFF17
rqk_sell_col = #FD1707
color c_bullish = input.color(rqk_buy_col, 'Bullish Color', group='Colors')
color c_bearish = input.color(rqk_sell_col, 'Bearish Color', group='Colors')
color colorByCross = isBullishSmooth ? c_bullish : c_bearish
color colorByRate = isBullish ? c_bullish : c_bearish
color plotColor = rqk_smoothColors ? colorByCross : colorByRate

// RQK and M8 Conv
rqk_conv = get_point_value(rqk,rqk_m8, true)
// Price and M8 Distance
rqk_price_dist = get_point_value(close,rqk_m8, true)
rqk_mid_point = (rqk + rqk_m8) * 0.5

// Plot
plot(rqk_conv,title="Convergence",color=color.new(blue,100) )
plot(rqk_price_dist,title="Price Distance",color=color.new(blue,100) )
plot(syminfo.mintick, title='Min Tick Value',color=color.new(blue,100))
plot(syminfo.pointvalue, title='Point Value',color=color.new(blue,100))

p_m2 = plot(i_rqk_show_ma ? rqk_m2 : na, title="M2", color=rqk_m2_a>0?green:red)
p_m4 = plot(i_rqk_show_ma ? rqk_m4 : na, title="M4", color=rqk_m4_a>0?yellow:orange)
p_m8 = plot(i_rqk_show_ma ? rqk_m8 : na, title="M8", color=rqk_m8_a>0?green:red)
p_rqk = plot(rqk, "RQK", color=plotColor, linewidth=2)
p_mid_point = plot(i_rqk_show_ma ? rqk_mid_point : na, title="Mid point", color=#777777)
plot(rqk_a,title="RQK Angle",color=color.new(blue,100) )
fill(p_m8, p_rqk, title="Fill Convergence", color=rqk_conv<i_rqk_conv ? color.new(red,80) : na)

// Angles
//plot(m1_a[1],title="M1 A",color=color.new(blue,100) )

// Alert Variables
bool alertBullish = rqk_smoothColors ? isBearishCross : isBearishChange
bool alertBearish = rqk_smoothColors ? isBullishCross : isBullishChange

// Alerts for Color Changes
alertcondition(condition=alertBullish, title='Bearish Color Change', message='Nadaraya-Watson: {{ticker}} ({{interval}}) turned Bearish ▼')
alertcondition(condition=alertBearish, title='Bullish Color Change', message='Nadaraya-Watson: {{ticker}} ({{interval}}) turned Bullish ▲')

// Non-Displayed Plot Outputs (i.e., for use in other indicators)
rqk_dir = alertBearish ? -1 : alertBullish ? 1 : 0
plot(alertBearish ? -1 : alertBullish ? 1 : 0, "Alert Stream", display=display.none)

// Change condition
//plotshape(alertBullish and m2_a<0 and close<m2?1:na,title="Sell",color=orange ,style=shape.circle,location=location.top)
// plotshape(alertBullish?1:na,title="Change",color=red ,style=shape.circle,location=location.top)
// plotshape(isBullish?1:na,title="Change",color=plotColor ,style=shape.circle,location=location.bottom)
buy_cond1 = isBearish and rqk_a>0 and rqk_m8_a>0 and rqk_m2_a>-5 ? 1 : 0
buy_cond2 = rqk<rqk_m8 and rqk_a>0 and rqk_m8_a<0 and rqk_m8_a>-3
 and isBullish and rqk_conv<i_rqk_conv 
 and close<rqk_m2 and rqk_m4<rqk ? 1 : 0
//plotshape(buy_cond1,title="Buy 1",color=#00ff00 ,style=shape.circle,location=location.bottom)
//plotshape(buy_cond2,title="Buy 1",color=red ,style=shape.circle,location=location.bottom)