// LOVE JOY PEACE PATIENCE KINDNESS GOODNESS FAITHFULNESS GENTLENESS SELF-CONTROL 
// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// Author: © JoshuaMcGowan

//@version=4
// Original code taken from TV built in library for Direction Movement Index or DMI.
// This concept taken from a pine script course with the purpose of combining several concepts into one indicator. 
// Includes multi time frame indicators, custom functions, and custom alert conditions. Also the way the information is displayed is not as conventional as well. 

study(title="Multi Timeframe ADX and DI", shorttitle="Multi-TF ADX", format=format.price, precision=4, overlay=false)

// ### Inputs Section ###

len = input(14, minval=1, title="DI Length")
lensig = input(14, title="ADX Smoothing", minval=1, maxval=50)

// In the next version will make inputs for the timeframes and ADX threshold here
TF1 = input(title="HTF Resolution", defval="1W", options=["1", "3", "5", "15", "30", "45", "60", "120", "180", "240", "1D", "1W", "1M"])
TF2 = input(title="MTF Resolution", defval="1D", options=["1", "3", "5", "15", "30", "45", "60", "120", "180", "240", "1D", "1W", "1M"])
TF3 = input(title="LTF Resolution", defval="240", options=["1", "3", "5", "15", "30", "45", "60", "120", "180", "240", "1D", "1W", "1M"])

// ### Logic Section ###

// Calculates ADX and DI 
up = change(high)
down = -change(low)
plusDM = na(up) ? na : (up > down and up > 0 ? up : 0)
minusDM = na(down) ? na : (down > up and down > 0 ? down : 0)
trur = rma(tr, len)
plus = fixnan(100 * rma(plusDM, len) / trur)
minus = fixnan(100 * rma(minusDM, len) / trur)
sum = plus + minus
adx = 100 * rma(abs(plus - minus) / (sum == 0 ? 1 : sum), lensig)

// Please note: 
// The security function was designed to request data of a timeframe higher than the current chart timeframe. 
// On a 60 minutes chart, this would mean requesting 240, D, W, or any higher timeframe.
// More reading: https://www.tradingview.com/pine-script-docs/en/v4/essential/Context_switching_the_security_function.html

// Calculates ADX on 3 timeframes
adxHTF = security(syminfo.tickerid, TF1, adx, barmerge.gaps_off, barmerge.lookahead_off)
adxMTF = security(syminfo.tickerid, TF2, adx, barmerge.gaps_off, barmerge.lookahead_off)
adxLTF = security(syminfo.tickerid, TF3, adx, barmerge.gaps_off, barmerge.lookahead_off)

// Calculates DI Plus on 3 timeframes
plusHTF = security(syminfo.tickerid, TF1, plus, barmerge.gaps_off, barmerge.lookahead_off)
plusMTF = security(syminfo.tickerid, TF2, plus, barmerge.gaps_off, barmerge.lookahead_off)
plusLTF = security(syminfo.tickerid, TF3, plus, barmerge.gaps_off, barmerge.lookahead_off)

// Calculates DI Minus on 3 timeframes
minusHTF = security(syminfo.tickerid, TF1, minus, barmerge.gaps_off, barmerge.lookahead_off)
minusMTF = security(syminfo.tickerid, TF2, minus, barmerge.gaps_off, barmerge.lookahead_off)
minusLTF = security(syminfo.tickerid, TF3, minus, barmerge.gaps_off, barmerge.lookahead_off)

// Defines Trending Bull/Bear conditions
bullHTF = (adxHTF >= 20) and (plusHTF >= minusHTF) ? 1 : 0
bearHTF = (adxHTF >= 20) and (plusHTF < minusHTF) ? -1 : 0

bullMTF = (adxMTF >= 20) and (plusMTF >= minusMTF) ? 1 : 0
bearMTF = (adxMTF >= 20) and (plusMTF < minusMTF) ? -1 : 0

bullLTF = (adxLTF >= 20) and (plusLTF >= minusLTF) ? 1 : 0
bearLTF = (adxLTF >= 20) and (plusLTF < minusLTF) ? -1 : 0

// Defines square color based on trend strength and direction
cColorHTF = bullHTF==1 ? color.green : bearHTF==-1 ? color.red : color.gray
cColorMTF = bullMTF==1 ? color.green : bearMTF==-1 ? color.red : color.gray
cColorLTF = bullLTF==1 ? color.green : bearLTF==-1 ? color.red : color.gray

// ### View Section ###

// Original Plots updated for Daily only. 
// plot(plusHTF, color=color.green, title="+DI")
// plot(minusHTF, color=color.red, title="-DI")
// plot(adxHTF, color=color.aqua, title="ADX")

// plot(plusMTF, color=color.green, title="+DI")
// plot(minusMTF, color=color.red, title="-DI")
// plot(adxMTF, color=color.aqua, title="ADX")

// plot(plusLTF, color=color.green, title="+DI")
// plot(minusLTF, color=color.red, title="-DI")
// plot(adxLTF, color=color.aqua, title="ADX")

// These will plot the wheel of fortune like circles/squares and HTF is on top and works its way down. 
plotshape(21, title="High Time Frame Trend Info", style=shape.circle, location=location.absolute, color=cColorHTF)
plotshape(11, title="Mid Time Frame Trend Info", style=shape.circle, location=location.absolute, color=cColorMTF)
plotshape(1, title="Low Time Frame Trend Info", style=shape.circle, location=location.absolute, color=cColorLTF)

// ### Alert Conditions ###

// Alert fires when a circle changes color from either red or gray to green for bull condition. Change from either green or gray to red would indicate a bearish trend signal. 
bullHTFCondition  = false
bearHTFCondition = false
bullHTFCondition  := change(bullHTF) and bullHTF==1
bearHTFCondition := change(bearHTF) and bearHTF==-1

bullMTFCondition  = false
bearMTFCondition = false
bullMTFCondition  := change(bullMTF) and bullMTF==1
bearMTFCondition := change(bearMTF) and bearMTF==-1

bullLTFCondition  = false
bearLTFCondition = false
bullLTFCondition  := change(bullLTF) and bullLTF==1
bearLTFCondition := change(bearLTF) and bearLTF==-1

// alertcondition(bullHTFCondition, title="HTF Trending Bull", message="HTF Trending Bull")
// alertcondition(bullMTFCondition, title="MTF Trending Bull", message="MTF Trending Bull")
// alertcondition(bullLTFCondition, title="LTF Trending Bull", message="LTF Trending Bull")

// alertcondition(bearHTFCondition, title="HTF Trending Bear", message="HTF Trending Bear")
// alertcondition(bearMTFCondition, title="MTF Trending Bear", message="MTF Trending Bear")
// alertcondition(bearLTFCondition, title="LTF Trending Bear", message="LTF Trending Bear")

// END