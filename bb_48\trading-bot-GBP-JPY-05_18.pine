//@version=4
study(title = "Trading Bot", shorttitle = "Trading Bot - BB 48", overlay=true, format = format.price, precision = 4)


show_entry = input(title="Show Entry", type=input.bool, defval=true)
show_atr = input(title = "Show Stop Loss / ATR", defval = true, type = input.bool)
use_special = input(title="Use Special Setups", type=input.bool, defval=true)
show_c_patterns = input(title="Show Candlestick data", type=input.bool, defval=true)
hide_c_icons = input(title="Hide Candlesticks", type=input.bool, defval=true)
use_atr_inside = input(title="Use ATR inside", type=input.bool, defval=true)
bb_sm = input(0.71, title="BB small Mult")
show_squeeze = true //input(title="Show BB Zones", type=input.bool, defval=true)
show_BB = true //input(title="Show BB", type=input.bool, defval=true)
show_BB_faded = true //input(title="Show Fill Faded", type=input.bool, defval=true)
use_deadzone = false //input(title="Deadzone", type=input.bool, defval=false)
dz_time = "1715-1830" //input(title="DZ Timeframe", type=input.session, defval= "1715-1830")


// === Colors ===
sell_color = #ff0062
buy_color = #00c3ff
red = #e04566 // e04566 //dd1c1c
dark_red = #ff0000
orange = #FF7F00
green = #55aa1a
dark_green = #008000
light_green = #00FF00
blue = #2196f3
white = #ffffff
gray  = #707070

// === Functions ===
angle(_src, len) =>
    rad2degree=180/3.14159265359  //pi 
    ang=rad2degree*atan((_src[0] - _src[1])/atr(len))

Session(sess) => na(time(timeframe.period, sess)) == false

// Change
perc_change = abs( (1 - (close[1] / close)) * 10000 )


// === Bollinger Bands %B ===
// ==================================================
bb_length = input(45, minval=1, title="Bollinger Length") // 48
bbr_length = bb_length //input(45, minval=1, title="BBR length") // 48 // 15 or 25
bbr_mult = 2.0//input(2.0, minval=0.001, maxval=4, title="BBR StdDev") // 2.2 or 2.0
bbr_basis = sma(close, bbr_length)
bbr_dev = bbr_mult * stdev(close, bbr_length)
bbr_upper = bbr_basis + bbr_dev
bbr_lower = bbr_basis - bbr_dev
bbr = (close - bbr_lower)/(bbr_upper - bbr_lower)


// ===  EMA 200 ===
// ==================================================
ema_200 = ema(close, 200)
ema_200_dev = 0.27 * stdev(close, 200)
ema_200_upper = ema_200 + ema_200_dev
ema_200_lower = ema_200 - ema_200_dev
ema_200_outer_dev = 0.23 * stdev(close, 200)
ema_200_outer_upper = ema_200_upper + ema_200_outer_dev
ema_200_outer_lower = ema_200_lower - ema_200_outer_dev


// ===  Moving Average EMA SMA ===
// ==================================================
ema_len_f = input(7, minval=1, title="ema Length") // 8 // default 8
ema_len = input(13, minval=1, title="ema Length") // 8 // default 8
sma_len = input(39, minval=1, title="SMA Length") // 48 // 50 // 45
src = close
ema_fast = ema(src, ema_len_f)
ema_mid = ema(src, ema_len)
up = ema_mid > ema_mid[1]
down = ema_mid < ema_mid[1]
fast_ema_angle = angle(ema_mid,4)
// Slow SMA
sma_slow = sma(close, sma_len)
up2 = sma_slow > sma_slow[1]
down2 = sma_slow < sma_slow[1]
sma_angle = angle(sma_slow,4)
ema_angle = angle(ema_mid,4)
mycolor = up ?  green : down ? red : #00c3ff
sma_slow_color = up2 ? dark_green : down2 ? dark_red : blue


// === Schaff Trend Cycle (STC) ===
// ==================================================
stc_signal = 12 // input(12,"Length")
stc_fast = 26 //input(26,"FastL/ength")
stc_slow = 45 //input(50,"SlowLength")
stc_low = 12
stc_high = 85
stc_line = 50 //(stc_high + stc_low) * 0.5
stc_macd(BBB, stc_fast, stc_slow) =>
    stc_fastMA = ema(BBB, stc_fast)
    stc_slowMA = ema(BBB, stc_slow)
    stc_macd = stc_fastMA - stc_slowMA
    stc_macd
    
AAAAA(stc_signal, stc_fast, stc_slow) => 
    AAA= 0.203 //input(0.203,"STC Sensitivity") // 0.25
    var CCCCC = 0.0
    var DDD = 0.0
    var DDDDDD = 0.0
    var EEEEE = 0.0
    tmp_macd = stc_macd(close,stc_fast,stc_slow)     
    lowest = lowest(tmp_macd, stc_signal)
    highest = highest(tmp_macd, stc_signal) - lowest    
    CCCCC := (highest > 0 ? ((tmp_macd - lowest) / highest) * 100 : nz(CCCCC[1])) 
    DDD := (na(DDD[1]) ? CCCCC : DDD[1] + (AAA * (CCCCC - DDD[1]))) 
    DDDD = lowest(DDD, stc_signal) 
    DDDDD = highest(DDD, stc_signal) - DDDD     
    DDDDDD := (DDDDD > 0 ? ((DDD - DDDD) / DDDDD) * 100 : nz(DDDDDD[1])) 
    EEEEE := (na(EEEEE[1]) ? DDDDDD : EEEEE[1] + (AAA * (DDDDDD - EEEEE[1])))
    EEEEE

stc = AAAAA(stc_signal,stc_fast,stc_slow)
stc_color = stc > stc[1] ? buy_color : sell_color


// === SSL ===
// ==================================================
ssl_len = input(40,"SSL Length") // 48 //55
SSL = wma(2 * wma(close, ssl_len / 2) - wma(close, ssl_len), round(sqrt(ssl_len)))
ssl_multy = 0.2
ssl_range = tr
rangema = ema(ssl_range, ssl_len)
upperk = SSL + rangema * ssl_multy
lowerk = SSL - rangema * ssl_multy
ssl_angle = angle(SSL,2) // 4
ssl_color_buy = #00c3ff
ssl_color_sell = #ff0062
ssl_color = close > upperk ? ssl_color_buy : close < lowerk ? ssl_color_sell : gray
// SSL bands
ssl_dev = 0.08 * stdev(close, ssl_len)
ssl_upper = SSL + ssl_dev
ssl_lower = SSL - ssl_dev


// === Bollinger Bands ===
// ==================================================
bb_use_ema = false//input(false, title="Use EMA for Bollinger Band")
bb_mult = 2.0 //input(2.0, title="Base Multiplier", minval=0.5, maxval=10)
sqz_length = 100
sqz_threshold = 76 //input(76, minval=0, title="Squeeze Threshold") // 78 // old 82 //  86  // 73 //  65

// Breakout Indicator Inputs
ema_1 = ema(close, bb_length)
sma_1 = sma(close, bb_length)
bb_basis = bb_use_ema ? ema_1 : sma_1
basis_angle = angle(bb_basis,4)
bb_basis_dev = 0.05 * stdev(close, bb_length)
bb_basis_up = bb_basis + bb_basis_dev
bb_basis_low = bb_basis - bb_basis_dev
bdev = stdev(close, bb_length)
bb_dev = bb_mult * bdev
bb_dev2 = bb_sm * bdev
// Bands
bb_inner_upper = bb_basis + bb_dev
bb_inner_lower = bb_basis - bb_dev
// Calculate BB spread and average spread
bb_spread = bb_inner_upper - bb_inner_lower
avgspread = sma(bb_spread, sqz_length)
// Calculate BB relative %width for Squeeze indication
bb_squeeze = 0.00
bb_squeeze := bb_spread / avgspread * 100
// Calculate Upper and Lower band painting offsets based on 50% of atr.
bb_offset = atr(14) * 0.5
bb_upper = bb_inner_upper + bb_offset
bb_lower = bb_inner_lower - bb_offset



// ===  ATR ===
// ==================================================
atrlen = 14 //input(14, "ATR Period")
atr_mult = 1.25//input(1.25, "ATR Mult", step = 0.1) // 1.15
atr_slen = wma(tr(true), atrlen)
atr_upper = atr_slen * atr_mult + close
atr_lower = close - atr_slen * atr_mult


// === RSI Chart Bars ===
// ==================================================
var rsi_color = #000000
rsi_len = 14 //input(14, minval=1, title="Length")
rsi_up = rma(max(change(close), 0), rsi_len)
rsi_down = rma(-min(change(close), 0), rsi_len)
rsi = rsi_down == 0 ? 100 : rsi_up == 0 ? 0 : 100 - (100 / (1 + rsi_up / rsi_down))
isup() => rsi > 70
isdown() => rsi < 30
// plot(rsi,title="RSI",color=color.blue)
rsi_color := isup() ? color.new(buy_color, 20) : isdown() ? color.new(sell_color, 20)  : na
barcolor(rsi_color, title="Rsi Candles")


// ==================================================
// === QQE MOD ===
// ==================================================
RSI_Period = 7//6
SF = 2//5
QQE = 3 //3
ThreshHold = 3 //3
qqe_src = close
Wilders_Period = RSI_Period * 2 - 1
Rsi = rsi(qqe_src, RSI_Period)
RsiMa = ema(Rsi, SF)
AtrRsi = abs(RsiMa[1] - RsiMa)
MaAtrRsi = ema(AtrRsi, Wilders_Period)
dar = ema(MaAtrRsi, Wilders_Period) * QQE

longband = 0.0
shortband = 0.0
trend = 0

DeltaFastAtrRsi = dar
RSIndex = RsiMa
newshortband = RSIndex + DeltaFastAtrRsi
newlongband = RSIndex - DeltaFastAtrRsi
longband := RSIndex[1] > longband[1] and RSIndex > longband[1] ? 
   max(longband[1], newlongband) : newlongband
shortband := RSIndex[1] < shortband[1] and RSIndex < shortband[1] ? 
   min(shortband[1], newshortband) : newshortband
cross_1 = cross(longband[1], RSIndex)
trend := cross(RSIndex, shortband[1]) ? 1 : cross_1 ? -1 : nz(trend[1], 1)
FastAtrRsiTL = trend == 1 ? longband : shortband

qqe_length = 45//50
qqe_length2 = 50
qqe_mult = 2 //0.35
qqe_basis = sma(FastAtrRsiTL - qqe_length2, qqe_length)
qqe_dev = qqe_mult * stdev(FastAtrRsiTL - qqe_length2, qqe_length)
qqe_upper = qqe_basis + qqe_dev
qqe_lower = qqe_basis - qqe_dev
color_bar = RsiMa - qqe_length2 > qqe_upper ? #00c3ff : RsiMa - qqe_length2 < qqe_lower ? #ff0062 : color.gray

// Zero cross
QQEzlong = 0
QQEzlong := nz(QQEzlong[1])
QQEzshort = 0
QQEzshort := nz(QQEzshort[1])
QQEzlong := RSIndex >= 50 ? QQEzlong + 1 : 0
QQEzshort := RSIndex < 50 ? QQEzshort + 1 : 0

Zero = hline(0, color=color.white, linestyle=hline.style_dotted, linewidth=1)
RSI_Period2 = 7 //input(6, title='RSI Length')
SF2 = 2 //input(5, title='RSI Smoothing')
QQE2 = 1.61 //input(1.61, title='Fast QQE2 Factor')
ThreshHold2 = 3 //input(3, title="Thresh-hold")

src2 = close //input(close, title="RSI Source")
Wilders_Period2 = RSI_Period2 * 2 - 1

Rsi2 = rsi(src2, RSI_Period2)
qqe_histo = (ema(Rsi2, SF2) )
AtrRsi2 = abs(qqe_histo[1] - qqe_histo)
MaAtrRsi2 = ema(AtrRsi2, Wilders_Period2)
dar2 = ema(MaAtrRsi2, Wilders_Period2) * QQE2
longband2 = 0.0
shortband2 = 0.0
trend2 = 0

DeltaFastAtrRsi2 = dar2
RSIndex2 = qqe_histo
newshortband2 = RSIndex2 + DeltaFastAtrRsi2
newlongband2 = RSIndex2 - DeltaFastAtrRsi2
longband2 := RSIndex2[1] > longband2[1] and RSIndex2 > longband2[1] ? 
   max(longband2[1], newlongband2) : newlongband2
shortband2 := RSIndex2[1] < shortband2[1] and RSIndex2 < shortband2[1] ? 
   min(shortband2[1], newshortband2) : newshortband2
cross_2 = cross(longband2[1], RSIndex2)
trend2 := cross(RSIndex2, shortband2[1]) ? 1 : cross_2 ? -1 : nz(trend2[1], 1)
qqe_line = trend2 == 1 ? longband2 : shortband2


//
// Zero cross
QQE2zlong = 0
QQE2zlong := nz(QQE2zlong[1])
QQE2zshort = 0
QQE2zshort := nz(QQE2zshort[1])
QQE2zlong := RSIndex2 >= 50 ? QQE2zlong + 1 : 0
QQE2zshort := RSIndex2 < 50 ? QQE2zshort + 1 : 0
//  

hcolor2 = qqe_histo - qqe_length2 > ThreshHold2 ? color.silver :
   qqe_histo - qqe_length2 < 0 - ThreshHold2 ? color.silver : na

Greenbar1 = qqe_histo - qqe_length2 > ThreshHold2
Greenbar2 = RsiMa - qqe_length2 > qqe_upper

Redbar1 = qqe_histo - qqe_length2 < 0 - ThreshHold2
Redbar2 = RsiMa - qqe_length2 < qqe_lower



// === MACD Dema === 
// ==================================================
dema_sma = input(7,title='DEMA Short') // 12
dema_lma = input(45,title='DEMA Long') // 26
dema_signal_in = input(13,title='Signal') // 9
show_lines = input(true,title="Lines")

MMEslowa = ema(close,dema_lma)
MMEslowb = ema(MMEslowa,dema_lma)
DEMAslow = ((2 * MMEslowa) - MMEslowb )

MMEfasta = ema(close,dema_sma)
MMEfastb = ema(MMEfasta,dema_sma)
DEMAfast = ((2 * MMEfasta) - MMEfastb)

dema_line = (DEMAfast - DEMAslow)

MMEsignala = ema(dema_line, dema_signal_in)
MMEsignalb = ema(MMEsignala, dema_signal_in)
dema_signal = ((2 * MMEsignala) - MMEsignalb )

dema_histo = (dema_line - dema_signal)

swap1 = dema_histo>0?green:red

// === Candlesticks ===
// ==================================================
candle_transp = 20
DojiSize = input(0.1, minval=0.01, title="Doji size")
c_pat(type, prev_c) =>
	match = false
	o = open
	c = close
	l = low
	h = high
	//close_2 = prev_c and (type == 'bear_e' or)
	if type == 'doji'
		match :=(abs(open - close) <= (high - low) * DojiSize)
	if type == 'h'
		match := (((high - low)>3*(open -close)) and  ((close - low)/(.001 + high - low) > 0.6) and ((open - low)/(.001 + high - low) > 0.6))
	if type == 'ih'
		match := (((high - low)>3*(open -close)) and  ((high - close)/(.001 + high - low) > 0.6) and ((high - open)/(.001 + high - low) > 0.6))
	if type == 'bear_e'
		match := (close[1] > open[1] and open > close and open >= close[1] and open[1] >= close and open - close > close[1] - open[1] )
	if type == 'bear_h'
		match := (close[1] > open[1] and open > close and open <= close[1] and open[1] <= close and open - close < close[1] - open[1] )
	if type == 'bull_h'
		match := (open[1] > close[1] and close > open and close <= open[1] and close[1] <= open and close - open < open[1] - close[1] )
	if type == 'bull_e'
		match := (open[1] > close[1] and close > open and close >= open[1] and close[1] >= open and close - open > open[1] - close[1] )
		
	[match]

// Doji
[doji] = c_pat('doji', false)
// Hammer
[h] = c_pat('h', false)
// Inverted Hammer
[ih] = c_pat('ih', false)
// Bearish Engulfing
[bear_e] = c_pat('bear_e', false)
// Bearish Harami
[bear_h] = c_pat('bear_h', false)
// Bullish Harami
[bull_h] = c_pat('bull_h', false)
// Bullish Engulfing
[bull_e] = c_pat('bull_e', false)

candle_type = 'na'
candle_type := doji == 1 ? 'doji' : 
 h == 1 ? 'h' : 
 ih == 1 ? 'ih' : 
 bear_e == 1 ? 'bear_e' :
 bear_h == 1 ? 'bear_e' :
 bull_e == 1 ? 'bull_e' :
 bull_h == 1 ? 'bull_h' : 'na'


// === BB Squeeze Zones ===
// ==================================================
bb_sqz_zone = bb_squeeze < 53 ? 0 : 
 bb_squeeze < sqz_threshold ? 1 : 
 bb_squeeze < 120 ? 2 :
 bb_squeeze < 180 ? 3 :
 bb_squeeze > 181 ? 4 : na
sqz_color = bb_sqz_zone == 0 ? #0045b3 :
 bb_sqz_zone == 1 ? sell_color : 
 bb_sqz_zone == 2 ?  gray : 
 bb_sqz_zone == 3 ?  buy_color : 
 bb_sqz_zone == 4 ? white: na
// BB location Zones 
bb_lower_zone = bb_lower > ema_200_upper ? 1 : 
 bb_inner_lower < ema_200_upper and bb_lower > ema_200 ? 2 : 
 bb_inner_lower < ema_200_upper and bb_inner_lower > ema_200 and bb_lower < ema_200 ? 3 : 
 bb_inner_lower < ema_200 and bb_lower > ema_200_lower ? 4 : 
 bb_inner_lower < ema_200 and bb_lower < ema_200_lower ? 5 :
 bb_inner_lower < ema_200_lower and bb_lower < ema_200_lower ? 6 : 7 

bb_upper_zone = bb_upper < ema_200_lower ? 1 : 
 bb_upper > ema_200_lower and bb_inner_upper < ema_200_lower ? 2 : 
 bb_upper > ema_200_lower and  bb_upper < ema_200 and bb_inner_upper > ema_200_lower ? 3 : 
 bb_upper > ema_200 and bb_inner_upper < ema_200 and bb_inner_upper > ema_200_lower ? 4 : 
 bb_upper < ema_200_upper and bb_inner_upper > ema_200 ? 5 : 
 bb_upper > ema_200_upper and bb_inner_upper > ema_200 and bb_inner_upper < ema_200_upper ? 6 :
 bb_upper > ema_200_upper and bb_inner_upper > ema_200_upper ? 7 : 8 
 
bb_zones_color =  sqz_color


// === Plot ===
// ==================================================
plot(bb_sqz_zone, title="BB Zones", color=bb_zones_color)
plot(bb_lower_zone, title="BB Lower Zones", color=bb_zones_color, style=plot.style_circles)
plot(bb_upper_zone, title="BB Upper Zones", color=bb_zones_color, style=plot.style_circles)
plot(stc,color=stc_color, title="STC",linewidth=2,style=plot.style_circles)
// QQE
plot(qqe_line - qqe_length2, title='QQE Line', color=color.white, transp=0, linewidth=2)
plot(qqe_histo - qqe_length2, color=hcolor2, transp=50, title='QQE Histo', style=plot.style_columns)
qqe_up = Greenbar1 and Greenbar2 == 1 ? qqe_histo - qqe_length2 : na
plot(qqe_up, title="QQE Up", style=plot.style_columns, color=#00c3ff, transp=0)
qqe_down = Redbar1 and Redbar2 == 1 ? qqe_histo - qqe_length2 : na
plot(qqe_down, title="QQE Down", style=plot.style_columns, color=#ff0062, transp=0)

// MACD Dema
plot(dema_histo,color=swap1,title='DEMA Histo',histbase=0, style=plot.style_circles)
p1 = plot(dema_line,color=blue,title='DEMA Line',style=plot.style_circles)
p2 = plot(dema_signal,color=red,title='Dema Signal',style=plot.style_circles)
// Angle of Moving Averages
plot(basis_angle, title="Basis Angle", style=plot.style_circles,color=white)
plot(sma_angle, title="SMA angle", style=plot.style_circles,color=white)
plot(ema_angle, title="EMA angle", style=plot.style_circles,color=white)
plot(ssl_angle,title='SSL Angle', color=white, linewidth=0,transp=0, style=plot.style_circles)
bb_diff = plot(bb_inner_upper - bb_inner_lower, title="BB diff", color=white,style=plot.style_circles)

plot(bbr, "Bollinger Bands %B", color=color.teal)
plot(SSL, title="SSL", linewidth=2, color=ssl_color)
// Moving Averages
plot(ema_fast, title="EMA Fast", color=#ffff00, linewidth=1)
plot(ema_mid, title="EMA Mid", color=mycolor, linewidth=3)
plot(sma_slow , title="SMA", color=sma_slow_color, linewidth=1, transp=0)
plot(bb_basis, title="Basis Line", color=color.red, transp=10, linewidth=1)

// === Candles ===
c_trans = hide_c_icons ? 100 : 70
plotchar(doji and show_c_patterns ? doji: na, title="Doji", text='Doji', color=color.white,transp=c_trans)
plotshape(h and show_c_patterns ? h : na,  title= "Hammer", location=location.belowbar, color=color.white, style=shape.arrowup, text="H",transp=c_trans)
plotshape(ih and show_c_patterns ? ih : na,  title= "Inverted hammer", location=location.belowbar, color=color.white, style=shape.arrowup, text="IH",transp=c_trans)
plotshape(bear_h and show_c_patterns ? bear_h: na,title= "Bearish Harami", color=sell_color, style=shape.arrowdown, text="BH",transp=c_trans)
plotshape(bear_e and show_c_patterns ? bear_e: na,title= "Bearish Engulfing", color=sell_color, style=shape.arrowdown, text="BE",transp=c_trans)
plotshape(bull_h and show_c_patterns ? bull_h : na,  title= "Bullish Harami", location=location.belowbar, color=buy_color, style=shape.arrowup, text="BH",transp=c_trans)
plotshape(bull_e and show_c_patterns ? bull_e : na, title= "Bullish Engulfing", location=location.belowbar, color=buy_color, style=shape.arrowup, text="BE",transp=c_trans)
// === Moving Averages ===
plot(ema_200, title="EMA 200", color=#fff176, linewidth=3)
// EMA 200 bands large
e200_up = plot(ema_200_upper, title="EMA 200 Upper", color=#ffe676, linewidth=1,transp=70)
e200_down = plot(ema_200_lower, title="EMA 200 Lower", color=#ffe676, linewidth=1,transp=70)

// BB Bands
usqzi = plot(show_BB ? bb_upper: na, "BB Upper ", transp=0, linewidth=1,color=bb_zones_color)
ubi = plot(show_BB ? bb_inner_upper: na, title="BB Upper Inner", color=bb_zones_color, transp=10, linewidth=1)
lbi = plot(show_BB ? bb_inner_lower:na, title="BB Lower Inner", color=bb_zones_color, transp=10, linewidth=1)
lsqzi = plot(show_BB ? bb_lower: na, "BB Lower", transp=0, linewidth=1,color=bb_zones_color)
plot(bb_squeeze, title="BB Squeeze", color=sqz_color, transp=10, linewidth=0,style=plot.style_circles)
fill_transp = show_BB_faded ? 80 : 35
fill(ubi, usqzi, title="BB Fill", color=bb_zones_color, transp=fill_transp)
fill(lbi, lsqzi, title="BB Fill", color=bb_zones_color, transp=fill_transp)
// BB small
bb_sm_upper = bb_inner_upper - (bb_dev2)
bb_sm_lower = bb_inner_lower + (bb_dev2)
ubii = plot(show_BB ? bb_sm_upper : na, title="BB Small Upper", color=white, transp=90, linewidth=1)
lbii = plot(show_BB ? bb_sm_lower :na, title="BB Small Lower", color=white, transp=90, linewidth=1)
fill(ubii, lbii, title="BB Fill", color=white, transp=95)


// ATR
plot(show_atr ? atr_upper : na, "+ATR Upper", color=#ffffff, transp=80)
plot(show_atr ? atr_lower : na, "-ATR Lower", color=#ffffff, transp=80)


entry_signal() =>
	dir = 0
	trading = 0

	// Type of candle - Long / Short
	candle = close > open ? 1 : 0
	Deadzone = Session(dz_time) ? 1 : 0
	special = 0

	// === Uptrend ===
	// ==================================================

	// === Sell ===
	if (candle == 1 and sma_slow > ema_200 and bbr > 1) //  ema_200 - ema_200_dev_sm
		dir := -1
		trading := 1

		// if atr_lower > open or atr_lower > bb_inner_upper or 
		//  ema_fast > bb_inner_upper or atr_lower < bb_sm_upper
		// 	dir := na
		// 	trading := 0

		// if SSL < sma_slow
		// 	dir := na
		// 	trading := 0

		// === Zone 0 ===
		if bb_sqz_zone == 0

			if ema_fast < bb_sm_upper or stc < stc_line
				dir := na
				trading := 0

			if bb_lower_zone == 1 and SSL < bb_sm_upper and basis_angle > 2
				dir := na
				trading := 0

		// === Zone 1 ===
		if bb_sqz_zone == 1
 
			if atr_lower < ema_mid or ema_fast < bb_sm_upper or 
			 stc < stc_line or (dema_signal < dema_histo or dema_signal < 0)
				dir := na
				trading := 0

		if bb_sqz_zone == 2


			if bb_lower_zone == 1 and stc < stc_line
				dir := na
				trading := 0
			if bb_lower_zone == 2 and (ssl_angle > 15 or ema_mid < bb_sm_upper)
				dir := na
				trading := 0

			if bb_lower_zone == 3 and (ssl_angle > 15 or ema_mid < bb_sm_upper)
				dir := na
				trading := 0

			if bb_lower_zone == 4 //and (ssl_angle > 15 or ema_mid < bb_sm_upper)
				dir := na
				trading := 0

			if bb_lower_zone == 5
				dir := na
				trading := 0
				// if atr_upper < ema_fast or SSL < bb_sm_upper
				// 	dir := na
				// 	trading := 0

				// if bb_basis < ema_200_upper
				// 	dir := na
				// 	trading := 0

		// === Zone 3 ===
		if bb_sqz_zone == 3

			// if atr_lower < bb_sm_upper or atr_lower > ema_fast or
			//  atr_lower > bb_inner_upper or doji==1
			// 	dir := na
			// 	trading := 0

			// if SSL < ema_mid and ema_200_lower > bb_sm_lower
			// 	dir := na
			// 	trading := 0

			if bb_lower_zone == 1
			
				if atr_lower > open
					dir := na
					trading := 0

			if bb_lower_zone == 4
				dir := na
				trading := 0

			if bb_lower_zone == 5 
				
				if (atr_lower < SSL or stc < stc_high or atr_lower > bb_inner_upper or ema_fast > bb_inner_upper)
				 and not(qqe_up > 0 and  qqe_line > qqe_histo)
					dir := na
					trading := 0

				// if atr_lower < SSL or atr_lower < bb_sm_upper or
				//  SSL < bb_sm_upper or SSL > ema_fast or bb_basis < ema_200_upper
				// 	dir := na
				// 	trading := 0

				// if atr_lower > ema_fast or atr_lower < bb_sm_upper or
				//  atr_lower > bb_inner_upper
				// 	dir := na
				// 	trading := 0

		// === Zone 4 ===
		if bb_sqz_zone == 4

			// May 10
			if atr_upper < bb_inner_upper or ema_fast < bb_inner_upper or
			 stc < stc_high or dema_histo < 0 or qqe_up == na
				dir := na
				trading := 0

			// if bb_lower_zone == 1 and ema_mid < bb_sm_upper 
			// 	dir := na
			// 	trading := 0

			// if bb_lower_zone == 5

			// 	if atr_lower > ema_fast or wt2 < 35
			// 		dir := na
			// 		trading := 0

			// 	// if (SSL < ema_fast or wt2 < obLevel1)
			// 	// 	dir := na
			// 	// 	trading := 0


	// === Counter - Buy ===
	if candle == 0 and sma_slow > ema_200 
		dir := 1
		trading := 1

		if atr_upper < open and not(perc_change > 20)
			dir := na
			trading := 0
			
		// BBR
		if bbr > 0
			dir := na
			trading := 0

		// === Zone 0 ===
		if bb_sqz_zone == 0

			if ema_fast > bb_sm_lower or atr_upper > ema_mid
				dir := na
				trading := 0

			if bb_lower_zone == 5

				if atr_upper < ema_fast or ema_fast > bb_sm_lower or atr_upper > bb_sm_lower
					dir := na
					trading := 0

		// === Zone 1 ===
		if bb_sqz_zone == 1

			// if perc_change > 15 or atr_upper > bb_sm_lower
			// 	dir := na
			// 	trading := 0

			if bb_lower_zone == 1 and 
			 (SSL > bb_sm_lower or ssl_angle < -12)
				dir := na
				trading := 0

			if bb_lower_zone == 7
				dir := na
				trading := 0

			if bb_lower_zone == 2 and ema_mid > bb_sm_lower
				dir := na
				trading := 0

			if bb_lower_zone == 3
				dir := na
				trading := 0

			if bb_lower_zone == 5 and (basis_angle < -3 )
				dir := na
				trading := 0

		// if bb_sqz_zone >= 2 and (ema_fast < bb_inner_lower or atr_upper > bb_sm_lower)
		// 	dir := na
		// 	trading := 0

		// === Zone 2 ===
		if bb_sqz_zone == 2

			if (atr_upper < ema_fast or atr_upper > bb_sm_lower or
			 high > ema_fast or SSL > bb_sm_lower)
			 and not (isdown() and open < bb_lower and atr_upper < bb_lower)
				dir := na
				trading := 0

			if bb_lower_zone == 3 
				if atr_upper < ema_fast or SSL < bb_sm_upper
					dir := na
					trading := 0

			// if bb_lower_zone == 5 

			// 	if atr_upper < ema_fast or ema_fast < bb_inner_lower or
			// 	 SSL > bb_sm_lower or basis_angle < -10.5 or atr_upper > bb_sm_lower
			// 		dir := na
			// 		trading := 0

		// === Zone 3 ===
		if bb_sqz_zone == 3

			if stc > stc_line or qqe_down == na or dema_signal > 0 or dema_signal < dema_line
				dir := na
				trading := 0

			if bb_lower_zone == 2
				dir := na
				trading := 0

			// if SSL > bb_sm_lower
			// 	dir := na
			// 	trading := 0

			// if (atr_upper < ema_fast or atr_upper > bb_sm_lower or
			//  high > ema_fast or sma_angle < -10) 
			//  and not(perc_change > 20)
			// 	dir := na
			// 	trading := 0


			if bb_lower_zone == 5 and SSL > bb_sm_lower //basis_angle < -5
				dir := na
				trading := 0
				

			// 	if SSL > bb_basis or ssl_angle < -12
			// 		dir := na
			// 		trading := 0

			// if bb_lower_zone >= 2 and open > ema_200_lower
			// 	dir := na
			// 	trading := 0



		// === Zone 4 ===
		if bb_sqz_zone == 4
			if bb_lower_zone == 5 and bb_basis > sma_slow
				dir := na
				trading := 0
			if bb_lower_zone == 5 and (stc > stc_low or ssl_angle < -12) or 
			 (perc_change > 30)
				dir := na
				trading := 0

		
	// === Uptrend Special Candles ===
	if use_special and sma_slow > ema_200

		// === Zone 0 ===
		if bb_sqz_zone == 0

			if bb_lower_zone == 7 and high > bb_inner_upper and 
			 close < bb_inner_upper and atr_upper > bb_upper and SSL > bb_basis
				dir := -1
				trading := 1

			if bb_lower_zone == 1 
				// Sell
				// if high > bb_inner_upper and sma_slow > open and close < bb_inner_upper
				// 	dir := -1
				// 	trading := 1

				// Buy
				if candle == 0 and not(doji==1) 
				 and low < bb_inner_lower and close > bb_inner_lower and 
				  SSL < ema_mid and SSL < bb_sm_lower
					dir := 1
					trading := 1

				// Sell - Bullish Engulfing
				if bull_e == 1 and perc_change > 10 and
				 SSL > close and high > bb_inner_upper
					dir := -1
					trading := 1
				// IH - Sell
				if bull_e == 1 and perc_change > 10 and
				 SSL > close and high > bb_inner_upper
					dir := -1
					trading := 1
				// Bearish Harami - Sell
				if bear_h == 1 and perc_change < 2.5 and
				 sma_slow < bb_sm_lower and high > bb_upper and
				 open > bb_inner_upper
					dir := -1
					trading := 1
				// IH - Sell
				if ih == 1 and perc_change < 2 and
				 open > bb_sm_upper and close < bb_inner_upper and candle == 1
					dir := -1
					trading := 1

				// Buy - hammer
				if candle == 0 and low < bb_inner_lower and 
				 sma_angle > 3 and open < bb_sm_lower and SSL < bb_sm_lower // might need to be an explicit hammer
					dir := 1
					trading := 1
					

			// Hammer Buy
			if h == 1 and low < bb_inner_lower and
			 ema_mid < sma_slow 
				dir := 1
				trading := 1
			//  Buy
			if bb_lower_zone == 1 and candle == 0 and low < bb_lower and
			 close < bb_sm_lower and close > bb_inner_lower
				dir := 1
				trading := 1
			//  Buy
			if bb_lower_zone == 2 and candle == 0 and bear_e == 1 and
			 SSL < ema_mid and low < bb_inner_lower
				dir := 1
				trading := 1

		// === Zone 1 ===
		if bb_sqz_zone == 1
			
			// Sell 
			// if candle == 1 and sma_angle < 0 and SSL > bb_sm_lower and
			//  high > bb_basis and close < bb_basis
			// 	dir := -1
			// 	trading := 1
			if bb_lower_zone == 1
				// Buy
				if candle == 0 and atr_lower < bb_lower and atr_upper < open and
				 close < bb_sm_lower and close > bb_inner_lower and basis_angle > 1
					dir := 1
					trading := 1
				// Buy
				if candle == 1 and atr_lower < bb_inner_lower and
				 close < bb_sm_lower and open > bb_inner_lower and basis_angle > 1 and
				 SSL > sma_slow
					dir := 1
					trading := 1
				// Buy
				if candle == 0 and atr_lower < bb_sm_lower and atr_upper < bb_sm_upper and
				 open > bb_basis and close < bb_basis and basis_angle > 3
					dir := 1
					trading := 1

			if bb_lower_zone == 7
				// Buy
				if candle == 0 and atr_lower < bb_lower and low < bb_inner_lower and
				 close > bb_inner_lower and open < bb_sm_lower and perc_change < 1
					dir := 1
					trading := 1

			if bb_lower_zone == 2
				// Buy
				if candle == 0 and atr_lower < bb_sm_lower and 
				 open < ema_fast and close > bb_sm_lower and perc_change < 2
					dir := 1
					trading := 1

			if bb_lower_zone == 3
				// Sell - Bullish Engulfing
				if bull_e == 1 and high > bb_basis and basis_angle < -1 and atr_lower > bb_sm_lower
					dir := -1
					trading := 1
				// Buy
				// if bear_e == 1 and atr_lower < bb_sm_lower and high > bb_basis and
				//  open > ema_fast and close < ema_fast
				// 	dir := 1
				// 	trading := 1

			if bb_lower_zone == 5 
				// Buy
				if candle == 0 and low < bb_lower and 
				 close > bb_inner_lower and close < bb_sm_lower
					dir := 1
					trading := 1

		// === Zone 2 ===
		if bb_sqz_zone == 2

			if bb_lower_zone == 1 
				// Sell - Mar 31
				if candle == 1 and atr_upper > bb_inner_upper and atr_lower > open and
				 SSL > ema_fast and SSL < bb_sm_upper and stc < stc_line and
				 dema_histo > 0 and dema_line < 0
					dir := -1
					trading := 1
				// Buy - Feb 16
				if candle == 0 and atr_lower < bb_sm_lower and
				 open < bb_basis and high < sma_slow and
				 qqe_down < 0 and ssl_angle 
					dir := 1
					trading := 1
				// Sell
				if candle == 1 and atr_upper > bb_inner_upper and atr_lower > ema_mid and
				 open > SSL and close < bb_inner_upper and SSL > ema_fast and
				 basis_angle > 10
					dir := -1
					trading := 1
				// Sell
				if candle == 1 and atr_upper > bb_inner_upper and atr_lower > open and
				 stc > stc_line
					dir := -1
					trading := 1

				// Sell
				if candle == 1 and atr_upper > bb_upper and atr_lower > sma_slow and
				 open > SSL and close < bb_inner_upper and SSL > ema_mid and
				 stc > stc_line
					dir := -1
					trading := 1

				// Sell
				if isup() and open > bb_upper and
				 SSL > bb_basis and ssl_angle < 15
					dir := -1
					trading := 1

				// Buy - Hammer
				if candle == 0 and h == 1 and 
				 perc_change < 1.5 and SSL < bb_basis and close < bb_inner_lower
					dir := 1
					trading := 1
				// Buy 
				if candle == 0 and perc_change < 2.5 and 
				 SSL < bb_sm_lower and open > bb_sm_lower and close < bb_sm_lower
					dir := 1
					trading := 1
				// Buy 
				// if candle == 0 and low < bb_inner_lower and close > bb_inner_lower and
				//  low < bb_sm_lower and sma_angle > -3 and bbr < 0.03 and
				//  (stc < stc_line and wt2 < 0)
				// 	dir := 1
				// 	trading := 1



			// 	dir := -1
			// 	trading := 1

			if bb_lower_zone == 7
				// Sell  
				// if close > ema_mid and
				//  open < ema_mid and stc < stc_low and wt2 < osLevel1
				// 	dir := -1
				// 	trading := 1
				// Sell - ih
				if ih == 1 and high > bb_upper and SSL > bb_basis and ema_mid > SSL
					dir := -1
					trading := 1
				// Buy 
				if candle == 0 and atr_lower < bb_lower and atr_upper < SSL and 
				 low < bb_inner_lower and open > bb_sm_lower and 
				 basis_angle > 2 and ssl_angle < -4
					dir := 1
					trading := 1

			if bb_lower_zone == 4
				// Sell - h
				if h == 1 and candle == 0 and open > bb_upper and close > bb_inner_upper
					dir := -1
					trading := 1

			if bb_lower_zone == 5

				// Sell 
				if candle == 1 and atr_upper > SSL and
				 SSL > ema_fast and SSL > sma_slow and
				 open > ema_fast and close < SSL and
				 ssl_angle > 0 and atr_upper < bb_sm_upper
					dir := -1
					trading := 1
			
		// === Zone 3 ===
		if bb_sqz_zone == 3

			//  Sell
			if bb_lower_zone == 1 
				// Sell - Bearish Harami 
				if bear_h == 1 and high > bb_inner_upper and close > bb_sm_upper and perc_change < 3
					dir := -1
					trading := 1
				// Sell - Bearish Harami
				if bear_h == 1 and isup() and close > bb_upper
					dir := -1
					trading := 1
				//  Buy
				if candle == 0 and 
				 SSL > ema_mid and ssl_color == ssl_color_sell and ssl_angle > 0 and
				 open < ema_mid
					dir := 1
					trading := 1
			// Sell - Hammer
			if bb_lower_zone == 3

				// Buy
				if candle == 0 and atr_lower < bb_basis and atr_upper < SSL and
				 close < sma_slow and open < ema_fast and low < bb_basis and 
				 stc < stc_low and not
				 (qqe_down == na) and
				 dema_signal > dema_line 
					dir := 1
					trading := 1

				// Hammer
				if candle == 1 and h == 1 and 
				 ssl_color == ssl_color_buy and bb_basis < sma_slow and
				 SSL > bb_basis
					dir := -1
					trading := 1
				//
				if candle_type[1] == 'bull_e' and candle == 1 and low > bb_sm_upper and 
				 low < SSL and SSL > ema_mid and sma_slow > bb_basis
					dir := -1
					trading := 1
			// Buy
			// if bb_lower_zone == 5 and bull_h == 1 and close < ema_200 and 
			//  and SSL < ema_200 and ema_mid < ema_200
			// 	dir := 1
			// 	trading := 1

			if bb_lower_zone == 4

				// Buy - Mar 31 21
				if candle == 0 and atr_lower < sma_slow and open < ema_fast and close < ema_mid and
				 dema_histo < 0
					dir := 1
					trading := 1

			if bb_lower_zone == 5
				// Sell - Mar 31 21
				if isup() and atr_upper > bb_upper and atr_lower > ema_mid and
				 open > SSL and SSL > ema_fast and (dema_signal > dema_histo or dema_signal > 0)
					dir := -1
					trading := 1
				// Sell
				if candle == 1 and atr_upper > bb_upper and atr_lower > open and
				 atr_lower > bb_sm_upper and close < bb_inner_upper
					dir := -1
					trading := 1

				// Sell - BE
				if bull_e == 1 and SSL < ema_mid and
				 open < bb_sm_lower and close > bb_sm_lower 
					dir := -1
					trading := 1

				// Sell - strong uptrend ending
				// if candle == 1 and isup() and open > SSL and SSL > ema_fast and 
				//  (wt1 > obLevel3) and basis_angle > 9 and atr_upper > bb_upper
				// 	dir := -1
				// 	trading := 1
				// Sell - downtrend continue 
				if candle == 1 and basis_angle < -4 and atr_upper > ema_mid and atr_lower > bb_inner_lower 
				 and close > bb_sm_lower and SSL < ema_fast and ssl_angle < -12 
					dir := -1
					trading := 1
				// Sell
				// if candle == 0 and high > bb_inner_upper and 
				//  open > bb_sm_upper and close < bb_inner_upper and wt2 > obLevel3 and close > SSL and ema_fast > SSL
				// 	dir := -1
				// 	trading := 1

				// Buy - BE + RSI
				if bull_e == 1 and isdown() and
				 close < SSL and close < bb_sm_lower and
				 open < SSL and open < bb_sm_lower
					dir := 1
					trading := 1

		// === Zone 4 ===
		if bb_sqz_zone == 4

			// if (bb_lower_zone == 4) and wt2 > obLevel1 and bbr > 0.98
			//  and perc_change > 12 and low < SSL and close < bb_inner_upper
			// 	dir := -1
			// 	trading := 1

			if bb_lower_zone == 5 
				// Sell
				if isup() and atr_upper > bb_upper and atr_lower > bb_sm_upper and
				 close < bb_inner_upper and open > ema_fast and SSL > ema_fast and
				 dema_histo > 0
					dir := -1
					trading := 1
				// Sell
				if (bear_h == 1 or ih == 1) and (close > bb_upper and basis_angle < 6 )
					dir := -1
					trading := 1
				// Sell
				if ih == 1 and basis_angle > 15 and atr_lower < bb_sm_upper and
				 atr_upper < bb_inner_upper and SSL > ema_fast
					dir := -1
					trading := 1
				// Buy
				// if candle == 0 and not(doji==1) and open < ema_mid and ema_fast < bb_sm_upper and
				//  atr_upper > bb_sm_upper
				// 	dir := 1
				// 	trading := 1
				// Buy
				if candle == 0 and atr_lower < bb_lower and atr_upper < SSL and
				 SSL < ema_fast
					dir := 1
					trading := 1


		special := dir == 1 ? 1 : 0
	//
	// ==================================================
	// === Downtrend ===
	// ==================================================

	// === Buy ===
	if (candle == 0 and sma_slow < ema_200 and close < bb_inner_lower)
		dir := 1
		trading := 1

		if bb_sqz_zone == 0 

			if (atr_upper < open or atr_upper > bb_sm_lower or ssl_angle < - 10) 
			 and not(isdown() and perc_change > 15 and basis_angle > -5)
			 and not(stc < stc_low and atr_upper < SSL)
				dir := na
				trading := 0

			if bb_upper_zone == 7 and basis_angle < 0
				dir := na
				trading := 0
				
		if bb_sqz_zone == 1 

			// if SSL > bb_sm_lower or atr_upper > bb_sm_lower
			// 	dir := na
			// 	trading := 0

			if bb_upper_zone == 1 and (atr_upper > bb_sm_lower or basis_angle < -5)
				dir := na
				trading := 0

			if bb_upper_zone == 2 and basis_angle < -4
				dir := na
				trading := 0

			if bb_upper_zone == 7
				dir := na
				trading := 0
			if bb_upper_zone >= 3 and 
			 (SSL > bb_sm_lower or atr_upper > bb_sm_lower or basis_angle < -3 )
				dir := na
				trading := 0

		// ATR Filter
		if bb_sqz_zone >=2 and (atr_upper > bb_sm_lower or atr_upper < ema_fast or atr_upper < open)
			dir := na
			trading := 0

		if bb_sqz_zone == 2
			
			if bb_upper_zone == 6
				dir := na
				trading := 0

			if bb_upper_zone == 7 
				dir := na
				trading := 0
				// if atr_upper < ema_fast or ema_fast < bb_inner_lower or
				//  basis_angle < -10 or atr_upper > bb_sm_lower
				// 	dir := na
				// 	trading := 0


		if bb_sqz_zone == 3

			if atr_upper < ema_fast or ema_fast < bb_inner_lower or
			 ssl_angle < -20
				dir := na
				trading := 0

			if bb_upper_zone == 4 and SSL > ema_mid
				dir := na
				trading := 0

			if bb_upper_zone == 7 and basis_angle < -6
				dir := na
				trading := 0

		if bb_sqz_zone == 4
			
			if atr_upper < bb_inner_lower or atr_upper < ema_fast or
			 atr_upper > SSL
				dir := na
				trading := 0


			// if bb_upper_zone == 1
			// 	dir := na
			// 	trading := 0
			// if bb_upper_zone == 4
			// 	dir := na
			// 	trading := 0


	// === Counter Sell ===
	if (candle == 1 and sma_slow < ema_200)
		dir := -1
		trading := 1

		// BBR
		if bbr < 1
			dir := na
			trading := 0

		// ATR
		if (atr_lower > bb_inner_upper or atr_lower > open)
		 and not(atr_upper > bb_upper and perc_change > 30)
			dir := na
			trading := 0

		if bb_sqz_zone == 0 

			if bb_upper_zone == 1 and SSL < ema_mid and
			 sma_slow < bb_sm_upper
				dir := na
				trading := 0

			if bb_basis > sma_slow or ssl_angle > 7
				dir := na
				trading := 0

		if bb_sqz_zone == 1 

			if (atr_lower < bb_sm_upper  or SSL < bb_sm_upper) and (basis_angle > 3)
				dir := na
				trading := 0

			if bb_upper_zone >= 5 and (atr_lower < bb_sm_upper or basis_angle > 4 )
				dir := na
				trading := 0

		if bb_sqz_zone == 2 
			
			if SSL < bb_sm_upper and not (sma_angle > 2)
				dir := na
				trading := 0
				
			if bb_upper_zone == 3  
				dir := na
				trading := 0

			if bb_upper_zone == 4 
			 and not(bull_e == 1 and SSL > ema_mid and bb_basis >= sma_slow) //and not(open < sma_slow and close > sma_slow) 
				dir := na
				trading := 0

			if bb_upper_zone == 5 
				dir := na
				trading := 0

			if bb_upper_zone == 6 // and ema_mid < bb_sm_upper
				dir := na
				trading := 0

			if bb_upper_zone == 7
				dir := na
				trading := 0

				// if (atr_upper < ema_fast or SSL < bb_sm_upper)
				// 	dir := na
				// 	trading := 0

		if bb_sqz_zone == 3 
			
			if bb_basis < sma_slow
				dir := na
				trading := 0

			if atr_upper < bb_sm_upper
				dir := na
				trading := 0
				
			// if ssl_angle > 17
			// 	dir := na
			// 	trading := 0
			if bb_upper_zone == 1 and isup()
				dir := na
				trading := 0

		if bb_sqz_zone == 4
			if bb_basis < sma_slow
				dir := na
				trading := 0
			
			if ssl_angle > 15 // 26
				dir := na
				trading := 0

			

	// === Downtrend Special Candles ===
	if use_special and sma_slow < ema_200
		
		if bb_sqz_zone == 0
			// Sell
			if bb_upper_zone == 1 and doji == 1 and high > bb_sm_upper and
			 sma_slow > bb_basis and low > bb_basis
				dir := -1
				trading := 1

			// Sell - Bullish Engulfing
			if bb_upper_zone == 7 
				// Sell - Bullish Engulfing
				if bull_e == 1 and high > bb_inner_upper and
				 sma_slow < bb_lower and SSL > ema_mid
					dir := -1
					trading := 1
				// Sell
				if candle == 1 and high > bb_inner_upper and open > bb_sm_upper and
				 low < bb_sm_upper and perc_change < 1.5 and 
				 sma_slow < bb_lower
					dir := -1
					trading := 1

		if bb_sqz_zone == 1
			// Buy
			// if low < bb_lower and candle == 0 and perc_change < 3
			// 	dir := 1
			// 	trading := 1

			if bb_upper_zone == 1
				// Buy
				if low < bb_lower and sma_slow > bb_inner_upper and
				 SSL < ema_mid
					dir := 1
					trading := 1

			if bb_upper_zone == 3
				// Buy
				if atr_lower < bb_inner_lower and atr_upper > bb_sm_lower and ema_fast > bb_sm_lower and open > bb_sm_lower and close < bb_sm_lower
					dir := 1
					trading := 1

			if bb_upper_zone == 6
				// Buy
				if candle == 0 and low < bb_sm_lower and atr_lower < bb_sm_lower and
				 perc_change < 4 and atr_upper < SSL
					dir := 1
					trading := 1

			if bb_upper_zone == 7

				// Sell
				if candle == 1 and close > bb_sm_upper and close < bb_inner_upper and
				 atr_upper > bb_inner_upper and basis_angle < 3
					dir := -1
					trading := 1

				// Sell
				// if high > bb_inner_upper and low > bb_sm_upper
				// 	dir := -1
				// 	trading := 1

				// Buy
				// if candle == 0 and low < bb_inner_lower and 
				//  open < ema_200_lower and close > bb_inner_lower and
				//  atr_lower < bb_lower and wt2 < 0
				// 	dir := 1
				// 	trading := 1
				
		if bb_sqz_zone == 2
			
			// Sell - Hammer
			if h == 1 and atr_upper > bb_upper and atr_lower > bb_sm_upper and
			 open > ema_fast and SSL > bb_sm_upper
				dir := -1
				trading := 1

			// Buy - 
			if candle == 1 and close < bb_lower and high < bb_inner_lower 
			 and SSL < bb_sm_lower and ssl_angle > -18
				dir := 1
				trading := 1

			if bb_upper_zone == 1
				// Sell - Bearish Harami
				if bear_h == 1 and sma_slow < bb_sm_upper and
				 SSL < bb_sm_lower and ema_mid < bb_basis and
				 open > bb_basis 
					dir := -1
					trading := 1
				// Sell - Doji
				if doji == 1 and SSL < ema_mid and ssl_angle < -12 and
				 open < bb_sm_lower
					dir := -1
					trading := 1
				// Sell - prev be
				if candle_type[1] == 'bull_e' and SSL > ema_mid and
				 high < bb_inner_upper and close > bb_sm_upper and
				 open < bb_sm_upper and open < SSL
					dir := -1
					trading := 1

			if bb_upper_zone == 2 and candle == 1 and atr_upper > bb_basis and 
			 ssl_angle > 5 and atr_lower > low
				dir := -1
				trading := 1
			// Sell
			if bb_upper_zone == 4 and candle == 1 and 
			 atr_upper > bb_sm_upper and atr_lower > open and
			 open > SSL and close > bb_basis
				dir := -1
				trading := 1

			if bb_upper_zone == 7

				// Sell
				// if candle == 1 and open > bb_sm_upper and close < bb_inner_upper and
				//  atr_upper > bb_inner_upper
				// 	dir := -1
				// 	trading := 1

				// Buy - Bullish Engulfing
				if bull_e == 1 and open < SSL and
				 SSL < ema_mid and perc_change < 3.5
					dir := 1
					trading := 1

			// Sell - Hammer
			if bb_upper_zone == 6 and h == 1 and bear_h == 1 and
			 candle == 0 and open > ema_mid
				dir := -1
				trading := 1
		
		if bb_sqz_zone == 3

			// Buy
			if bb_upper_zone == 7 
				if candle == 0 and SSL < ema_mid and SSL < bb_sm_lower and ssl_angle > -5 
				 and low < SSL and close < bb_sm_lower and bbr > 0
					dir := 1
					trading := 1
				// Buy
				if candle == 0 and basis_angle > -0.5 and atr_lower < bb_lower and 
				 low < bb_inner_lower and open > bb_sm_lower and close < bb_sm_lower
					dir := 1
					trading := 1

				// Buy - bh
				// if bull_h == 1 and isdown() and
				//  perc_change < 1.5 and SSL < ema_mid
				// 	dir := 1
				// 	trading := 1
				// // Sell - ih
				// if bear_e == 1 and ih == 1 and
				//  perc_change < 3.5 and SSL < ema_mid and close > ema_mid
				// 	dir := -1
				// 	trading := 1
				// // Buy - RSI short candle
				// if isdown() and open < bb_inner_lower and close < bb_lower and
				//  perc_change < 4 and ema_mid < SSL
				// 	dir := 1
				// 	trading := 1


		if bb_sqz_zone == 4
			// Sell - Bearish Engulfing
			if bear_e == 1 and atr_upper < SSL and atr_upper > ema_fast and
			 SSL > ema_fast and close > bb_sm_upper
				dir := -1
			// Sell - Hammer
			if h == 1 and SSL > ema_mid and
			 close > bb_sm_upper and bb_basis > ema_200
				dir := -1
				trading := 1
			// Sell - Hammer
			if h == 1 and SSL > ema_mid and
			 close > bb_sm_upper and bb_basis > ema_200
				dir := -1
				trading := 1
			// Buy - Bullish Harami
			if bb_upper_zone >= 5 and bull_h == 1 and
			 open < bb_lower and close < bb_inner_lower and SSL < bb_sm_lower
				dir := 1
				trading := 1
			// Buy - Bullish Harami
			if bb_upper_zone == 6 and bull_h == 1 and
			 close > SSL and open > SSL and open < bb_sm_lower
				dir := 1
				trading := 1

			
			if bb_upper_zone == 7 
				// Sell- Hammer
				if h == 1 and candle == 1 and
				 open > ema_fast and open > bb_sm_lower and SSL > ema_fast and 
				 atr_upper > bb_inner_upper
					dir := -1
					trading := 1
				// Sell 
				if candle == 1 and low > ema_mid and 
				 close < SSL and perc_change < 3.5 and ssl_angle < 0
					dir := -1
					trading := 1
				// Buy 
				if candle == 0 and low < bb_inner_lower and ema_fast < SSL and
				 open < ema_fast and close > bb_inner_lower and atr_lower < bb_lower
					dir := 1
					trading := 1
				// Buy 
				if bull_h == 1 and open < bb_sm_lower and ema_fast < SSL and
				 close > bb_inner_lower and atr_lower < bb_inner_lower
					dir := 1
					trading := 1


		special := dir == -1 ? 1 : 0 


	


	// Don't trade during high spread periods
	if use_deadzone and Deadzone == 1
		dir := na
		trading := 0

	[dir, trading,special]

[trade_dir, trading,special] = entry_signal() 

// Buy / Sell indicators
trade_color = trade_dir > 0  ? buy_color : sell_color
plotshape(trade_dir > 0 and show_entry ? 1 : na, title="Entry Buy",transp=20, color=trade_color, location = location.belowbar, style=shape.labelup, text="B", textcolor=color.white, size=size.small)
plotshape(trade_dir < 0 and show_entry ? -1 : na, title="Entry Sell",transp=20, color=trade_color, location = location.abovebar, style=shape.labeldown, text="S", textcolor=color.white, size=size.small)
