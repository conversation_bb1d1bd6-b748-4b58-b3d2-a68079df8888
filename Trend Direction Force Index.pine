//@version=4
study("Trend Direction Force Index - TDFI [wm]", shorttitle = "TDFI [wm]")


td_lb = input(25, title = "Lookback") // 13
td_fh = input(0.05, title = "Filter", step=0.01) // 0.05
td_fl =  td_fh * -1 //input(-0.05, title = "Filter Low") 
td_max = input(0.8, title = "Max Line", step=0.1) 
price = input(close, "Period")


mma = ema(price * 1000, td_lb)
smma = ema(mma, td_lb)

impetmma = mma - mma[1]
impetsmma= smma - smma[1]
divma = abs(mma - smma)
averimpet = (impetmma + impetsmma) / 2

number = averimpet
var int pow = 3 
var float result = na

for i = 1 to pow - 1
    if i == 1
        result := number
    result := result * number

tdf = divma * result
ntdf = tdf / highest(abs(tdf), td_lb * 3)

c = ntdf > td_fh ? #008000 : ntdf < td_fl ? #ff0000 : #505050
plot(ntdf, linewidth = 2, color = c)

hline(td_max, color = #ffff00)
hline(td_fh, color = #505050)
hline(td_fl, color = #505050)
hline(td_max * -1, color = #ffff00)