// This work is licensed under a Attribution-NonCommercial-ShareAlike 4.0 International (CC BY-NC-SA 4.0) https://creativecommons.org/licenses/by-nc-sa/4.0/
// © LuxAlgo

//@version=5
indicator("Predictive Ranges", overlay = true)
//------------------------------------------------------------------------------
//Settings
//-----------------------------------------------------------------------------{
PR_length = input.int(200, 'Length', minval = 2)
PR_mult   = input.float(6., 'Factor', minval = 0, step = .5) // 6.0 , 10.0
PR_tf     = input.timeframe('', 'Timeframe')
PR_show = input.bool(false, title='Show Channels')
PR_show_levels = input.bool(false, title='Show Levels')
PR_show_candles = input.bool(true, title='Show Candles')
PR_trans = input.int(95, title='Trans')


//-----------------------------------------------------------------------------}
//Function
//-----------------------------------------------------------------------------{
pred_ranges(PR_length, PR_mult)=>
    var avg = close
    var hold_atr = 0.

    atr = nz(ta.atr(PR_length)) * PR_mult
        
    avg := close - avg > atr ? avg + atr : 
      avg - close > atr ? avg - atr : 
      avg
        
    hold_atr := avg != avg[1] ? atr / 2 : hold_atr

    prR2 = avg + hold_atr * 2
    prR1 = avg + hold_atr
    prS1 = avg - hold_atr
    prS2 = avg - hold_atr * 2

    l38  = (prR2-prS2) * 0.382 + prS2
    l62  = (prR2-prS2) * 0.618 + prS2
        
    [prR2, 
     prR1, 
     avg, 
     prS1, 
     prS2,
     l38,
     l62]

//-----------------------------------------------------------------------------}
//Calculation
//-----------------------------------------------------------------------------{
[prR2
  , prR1
  , pravg
  , prS1
  , prS2     
  , l38
  , l62] = request.security(syminfo.tickerid, PR_tf, pred_ranges(PR_length, PR_mult))


pr_bars = ta.barssince(ta.change(prR2))

//-----------------------------------------------------------------------------}
//Plots
//-----------------------------------------------------------------------------{
plot_bars  = plot(PR_show ? pr_bars: na, 'Bars Since', color=color.new(color.blue,100))
plot_pru2  = plot(PR_show ? prR2: na, 'PR Upper 2', pravg != pravg[1] ? na : #f23645)
plot_pru1  = plot(PR_show ? prR1: na, 'PR Upper 1', pravg != pravg[1] ? na : #f23645)
plot_pravg = plot(PR_show ? pravg: na , 'PR Average', pravg != pravg[1] ? na : #5b9cf6)
plot_prl1  = plot(PR_show ? prS1: na, 'PR Lower 1', pravg != pravg[1] ? na : #089981)
plot_prl2  = plot(PR_show ? prS2: na, 'PR Lower 2', pravg != pravg[1] ? na : #089981)
plot_38    = plot(PR_show_levels and PR_show ? l38 : na, 'Level 38', pravg != pravg[1] ? na : #ff9800)
plot_62    = plot(PR_show_levels and PR_show ? l62 : na, 'Level 62', pravg != pravg[1] ? na : #ffeb3b)


//Fills
fill(plot_pru2, plot_pru1, pravg != pravg[1] ? na : color.new(#f23645, PR_trans))
fill(plot_prl1, plot_prl2, pravg != pravg[1] ? na : color.new(#089981, PR_trans))

// Candles
PR_col = 
 close>prR1 ? color.red
 : low>pravg and close<prR1 ? color.yellow 
 : high<pravg and close>prS1 ? color.blue 
 : high<prS1 ? color.green
 : na

barcolor(PR_show_candles ? PR_col : na)

//-----------------------------------------------------------------------------}