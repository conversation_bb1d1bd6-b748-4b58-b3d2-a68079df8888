//@version=4
study(title="SSL Angles overlay", shorttitle="SSL Angles overlay",overlay=true)

// ATR plot
input_angle = input(3, "Angle degree")
basis_len = input(100, "Basis Length") // 25

red = #ff0062
aqua = #00bcd4
yellow = #FFFF00
orange = #ff9800
green = #4caf50
lime = #00E676
white = #ffffff
c_hide = color.new(#ffffff,100)
blue = #42a5f5
violet = #814dff
gray = #707070
black = #000000
sell_color = color.new(#ff0062,20)
buy_color = color.new(#00c3ff,20)

angle(_src, len) =>
    rad2degree=180/3.14159265359  //pi 
    ang=rad2degree*atan((_src[0] - _src[1])/atr(len))

ema_200 = ema(close,200)
ea = angle(ema_200,input_angle)
//plot(ea, title="EMA 200 angle",color=color.new(blue,100))

ema_50 = ema(close, 50)
e50_a = angle(ema_50,3)
//plot(e50_a, title="EMA 50 angle",color=e50_a>0?red:green)

ema_20 = ema(close, 20)
e20_a = angle(ema_20,3)

basis = sma(close, basis_len)
ba = angle(basis,input_angle)
//plot(ba, title="Basis angle",color=orange)


s1_len = input(45, "S1")
s1 = wma(2*wma(close, s1_len/2)-wma(close, s1_len), round(sqrt(s1_len)))
s1_a = angle(s1,input_angle)
s1_color  = s1_a > 0 ? red : green
//plot(s1_a, title="s1 angle",color=s1_color,linewidth=2)

s3_len = input(8, "S3")
s3 = wma(2*wma(close, s3_len/2)-wma(close, s3_len), round(sqrt(s3_len)))
s3_a = angle(s3,input_angle)
//plot(s3_a, title="S3 angle",color=white)

s3b_len = input(15, "S3b")
s3b = wma(2*wma(close, s3b_len/2)-wma(close, s3b_len), round(sqrt(s3b_len)))
s3b_a = angle(s3b,input_angle)
//plot(s3b_a, title="S3B angle",color=blue)

s4_len = 75
s4 = wma(2*wma(close, s4_len/2)-wma(close, s4_len), round(sqrt(s4_len)))
s4_a = angle(s4,input_angle)
//plot(s4_a, title="S4 angle",color=yellow)

s5_len = 150
s5 = wma(2*wma(close, s5_len/2)-wma(close, s5_len), round(sqrt(s5_len)))
s5_a = angle(s5,input_angle)
//plot(s5_a, title="S5 angle",color=green)

s4_cross = abs(s4_a - s5_a)
//plot(s4_cross, title="S4 Cross",color=color.new(yellow,100))

s4_s5_dist = abs(s4 - s5) * 1000
//plot(s4_s5_dist, title="S4 Dist",color=color.new(yellow,100))

// S3b
plotshape(s3_a>10 and s3b_a>10?1:na,title="S3 top",color=#ff0000,style=shape.circle,location=location.top)
plotshape(s3_a<-10 and s3b_a<-10?1:na,title="S3 bottom",color=#00ff00,style=shape.circle,location=location.bottom)

