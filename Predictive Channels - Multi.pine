// This work is licensed under a Attribution-NonCommercial-ShareAlike 4.0 International (CC BY-NC-SA 4.0) https://creativecommons.org/licenses/by-nc-sa/4.0/
// © LuxAlgo

//@version=5
indicator("Predictive Channels - Multi",  overlay = true)

//-----------------------------------------------------------------------------}
//Settings
//-----------------------------------------------------------------------------{
g_PC = 'Predictive Channels -------------------------------------------------------------'
PC_factor1 = input.float(5, 'Factor 1',  minval = 0, group=g_PC)
PC_slope1  = input.float(50, 'Slope 1', minval = 0, group=g_PC) * PC_factor1
PC_factor2 = input.float(13, 'Factor 2', minval = 0, group=g_PC)
PC_slope2  = input.float(50, 'Slope 2',minval = 0, group=g_PC) * PC_factor2
i_pc_show       = input.bool(false,title = 'Show 1', group=g_PC)
i_pc_show2      = input.bool(false,title = 'Show 2', group=g_PC)
g_PC_multi = 'Multi -------------------------------------------------------------'
i_pc_time  = input.timeframe('30', "Resolution", group=g_PC_multi) // 1 hour
PC_factor_m     = input.float(5, 'Factor Multi', minval = 0, group=g_PC_multi)
PC_slope_m      = input.float(25, minval = 0, group=g_PC_multi) * PC_factor_m
i_pc_show_m     = input.bool(true,title = 'Show Multi', group=g_PC_multi)
pc_i_show_info  = input.bool(true,title = 'Show Info', group=g_PC_multi)
i_PC_show_ma    = input.bool(false,title = 'Show MA', group=g_PC_multi)
i_PC_fill_color = input.color(#000000, "PC Fill Color")

//Style
red = #ff0062
orange = #ff9800
yellow = #FFFF00
green = #4caf50
lime = #50e600
aqua = #00bcd4
blue = #2962ff
dark_blue = #2962ff
violet = #814dff
white = #ffffff
gray = #707070
black = #000000

min_tick = syminfo.mintick
var int decimals = int(math.log10(1/min_tick))
decimals := decimals == 2 ? 1 : decimals == 0 ? 1 : decimals
get_pip_distance(point1, point2) =>
    diff_points = math.abs( (point1 - point2) )
    pip_value = decimals<3 ? diff_points / syminfo.mintick / 100 : diff_points / syminfo.mintick / 10

newbar(res) => ta.change(time(res)) == 0 ? 0 : 1
perc_change(obj) =>
    perc = math.abs((1 - obj[1] / obj) * 10000)
    perc
//-----------------------------------------------------------------------------}
//Calculation 1 
//-----------------------------------------------------------------------------{
f_pc1()=>

    var PC_avg = close
    var PC_os = 1
    var PC_hold_atr = 0.0

    PC_atr = nz(ta.atr(200)) * PC_factor1

    PC_avg := math.abs(close - PC_avg) > PC_atr ? close 
     : PC_avg + PC_os * PC_hold_atr / PC_slope1

    PC_hold_atr := PC_avg == close ? PC_atr / 2 : PC_hold_atr
    PC_os := PC_avg > PC_avg[1] ? 1 : PC_avg < PC_avg[1] ? -1 : PC_os

    PC_R2 = PC_avg + PC_hold_atr
    PC_R1 = PC_avg + PC_hold_atr/2
    PC_S1 = PC_avg - PC_hold_atr/2
    PC_S2 = PC_avg - PC_hold_atr

    [PC_R2,PC_R1,PC_S1,PC_S2, PC_avg, PC_os, PC_hold_atr]

[PC_R2, PC_R1, PC_S1, PC_S2, PC_avg, PC_os, PC_hold_atr] = request.security(syminfo.tickerid, "", f_pc1() )


//-----------------------------------------------------------------------------}
//Calculation 2 
//-----------------------------------------------------------------------------{
f_pc2()=>

    var PC_avg_2 = close
    var PC_os_2 = 1
    var PC_hold_atr_2 = 0.0

    PC_atr_2 = nz(ta.atr(200)) * PC_factor2

    PC_avg_2 := math.abs(close - PC_avg_2) > PC_atr_2 ? close 
     : PC_avg_2 + PC_os_2 * PC_hold_atr_2 / PC_slope2

    PC_hold_atr_2 := PC_avg_2 == close ? PC_atr_2 / 2 : PC_hold_atr_2
    PC_os_2 := PC_avg_2 > PC_avg_2[1] ? 1 : PC_avg_2 < PC_avg_2[1] ? -1 : PC_os_2

    PC_R2 = PC_avg_2 + PC_hold_atr_2
    PC_R1 = PC_avg_2 + PC_hold_atr_2/2
    PC_S1 = PC_avg_2 - PC_hold_atr_2/2
    PC_S2 = PC_avg_2 - PC_hold_atr_2

    [PC_R2,PC_R1,PC_S1,PC_S2, PC_avg_2, PC_os_2, PC_hold_atr_2]

[PC_R2_2, PC_R1_2, PC_S1_2, PC_S2_2, PC_avg_2, PC_os_2, PC_hold_atr_2] = request.security(syminfo.tickerid, "", f_pc2() )


//-----------------------------------------------------------------------------}
//Calculation 3 - Multi
//-----------------------------------------------------------------------------{
f_pc_multi()=>

    var PC_avg_m = close
    var PC_os_m = 1
    var PC_hold_atr_m = 0.0

    PC_atr = nz(ta.atr(200)) * PC_factor_m

    PC_avg_m := math.abs(close - PC_avg_m) > PC_atr ? close 
     : PC_avg_m + PC_os_m * PC_hold_atr_m / PC_slope_m

    PC_hold_atr_m := PC_avg_m == close ? PC_atr / 2 : PC_hold_atr_m
    PC_os_m := PC_avg_m > PC_avg_m[1] ? 1 : PC_avg_m < PC_avg_m[1] ? -1 : PC_os_m

    PC_R2_m = PC_avg_m + PC_hold_atr_m
    PC_R1_m = PC_avg_m + PC_hold_atr_m/2
    PC_S1_m = PC_avg_m - PC_hold_atr_m/2
    PC_S2_m = PC_avg_m - PC_hold_atr_m

    [PC_R2_m,PC_R1_m,PC_S1_m,PC_S2_m, PC_avg_m, PC_os_m, PC_hold_atr_m]

[PC_R2_m, PC_R1_m, PC_S1_m, PC_S2_m, PC_avg_m, PC_os_m, PC_hold_atr_m] = request.security(syminfo.tickerid, i_pc_time, f_pc_multi() )

if newbar(i_pc_time) == 0
    PC_R2_m := PC_R2_m[1]
    PC_R1_m := PC_R1_m[1]
    PC_S1_m := PC_S1_m[1]
    PC_S2_m := PC_S2_m[1]
    PC_avg_m:= PC_avg_m[1]
    PC_os_m := PC_os_m[1]
    PC_hold_atr_m := PC_hold_atr_m[1]

// Moving Averages
i_h2 = input.int(20, 'H2')
i_h3 = input.int(20, 'H3')
h2 = ta.hma(close,i_h2)
h3 = ta.hma(close,i_h3)

// EMA 30 min
ema20 = request.security(syminfo.tickerid, "30", ta.ema(close,20) )


//-----------------------------------------------------------------------------}
//Functions
//-----------------------------------------------------------------------------{
var upper_bound = 0.0
var pc_direction = 0
var pc_bars = 0

PC_get_angle(obj) =>
    obj>obj[1] ? 1 : -1

PC_get_level(obj) =>
  
    var int level = 0
    // Above R2
    if obj>PC_R2_m 
        level := 6
    // Above R1 
    if obj<PC_R2_m and obj>PC_R1_m
        level := 5
    // Above Average 
    if obj<PC_R1_m and obj>PC_avg_m
        level := 4
    // Below Average
    if obj<PC_avg_m and obj>PC_S1_m
        level := 3
    // Below S1
    if obj<PC_S1_m and obj>PC_S2_m
        level := 2
    // Below S2
    if obj<PC_S2_m
        level := 1

    level

PC_is_between(obj) =>
    between = PC_R2_m>obj and PC_S2_m<obj ? 1 : 0

PC_barssince(obj)=>
    bars = ta.barssince( get_pip_distance(obj,obj[1]) > 5 )

PC_prev_box(obj)=>
    //var prev_box = obj
    bars = ta.barssince( get_pip_distance(obj,obj[1]) > 5 )
    dir = bars == 0 ? obj[1] : obj


pc_bars := PC_barssince(PC_os_m)

prev_box_m = PC_prev_box(PC_R2_m) 
plot(PC_os_m, 'Multi Direction', color=PC_os_m==1 ? color.new(green,100) : color.new(red,100) )
plot(prev_box_m, 'Last Box', color=prev_box_m==1 ? color.new(green,100) : color.new(red,100) )


//-----------------------------------------------------------------------------}
//Plot PC 1
//-----------------------------------------------------------------------------{
PC_top1 = plot(i_pc_show ? PC_R2 : na, 'Upper Resistance', close == PC_avg ? na : red)
plot(i_pc_show ? PC_R1 : na, 'Lower Resistance', close == PC_avg ? na : color.new(red, 50))
plot_avg1 = plot(i_pc_show ? PC_avg : na, 'Average', close == PC_avg ? na : gray)
plot(i_pc_show ? PC_S1 : na, 'Upper Support', close == PC_avg ? na : color.new(green, 50))
PC_bottom1 = plot(i_pc_show ? PC_S2 : na, 'Lower Support', close == PC_avg ? na : green)
fill(PC_top1, PC_bottom1, i_PC_fill_color, 'PC Fill')

//-----------------------------------------------------------------------------}
//Plot PC 2
//-----------------------------------------------------------------------------{
PC_top2 = plot(i_pc_show2 ? PC_R2_2 : na, 'Upper Resistance', close == PC_avg ? na : red)
plot(i_pc_show2 ? PC_R1_2 : na, 'Lower Resistance', close == PC_avg ? na : color.new(red, 50))
plot_avg2 = plot(i_pc_show2 ? PC_avg_2 : na, 'Average', close == PC_avg ? na : gray)
plot(i_pc_show2 ? PC_S1_2 : na, 'Upper Support', close == PC_avg ? na : color.new(green, 50))
PC_bottom2 = plot(i_pc_show2 ? PC_S2_2 : na, 'Lower Support', close == PC_avg ? na : green)
fill(PC_top2, PC_bottom2, i_PC_fill_color, 'PC Fill')

//-----------------------------------------------------------------------------}
//Plot PC Multi
//-----------------------------------------------------------------------------{
plot(pc_i_show_info ? PC_prev_box(PC_os_m) : na, title='Prev Box Direction', color=color.new(white,100))
plot(pc_i_show_info ? pc_bars : na, title='Bars Since', color=color.new(white,100))
plot(pc_i_show_info ? pc_direction : na, title='direction', color=color.new(white,100))
plot(pc_i_show_info ? PC_is_between(ema20) : na, title='is between', color=color.new(white,100))
plot(pc_i_show_info ? PC_get_angle(PC_R2_m) : na, title='Get Angle', color=color.new(white,100))

plot_0 = plot(i_pc_show_m ? close : na, color = na, display = display.none, editable = false)

//SR PLots
PC_top_m = plot(i_pc_show_m ? PC_R2_m : na, 'Upper Resistance M', close == PC_avg_m ? na : red)
plot(i_pc_show_m ? PC_R1_m : na, 'Lower Resistance M', close == PC_avg_m ? na : color.new(red, 50))
plot_avg_m = plot(i_pc_show_m ? PC_avg_m : na, 'Average M', close == PC_avg_m ? na : gray)
plot(i_pc_show_m ? PC_S1_m : na, 'Upper Support M', close == PC_avg_m ? na : color.new(green, 50))
PC_bottom_m = plot(i_pc_show_m ? PC_S2_m : na, 'Lower Support M', close == PC_avg_m ? na : green)
fill(PC_top_m, PC_bottom_m, i_PC_fill_color, 'PC Fill')


//Fill
topcss = close > PC_avg_m ? color.new(green, 80) : color.new(chart.bg_color, 100)
btmcss = close < PC_avg_m ? color.new(red, 80) : color.new(chart.bg_color, 100)
fill(plot_0, plot_avg_m, PC_R2_m, PC_S2_m, topcss, btmcss)


//plotshape(close>PC_R2_m ? 1 : 0,"Top End",style=shape.circle, location = location.top, color = close>open ? red : color.rgb(230, 122, 0))
//plotshape(close<PC_S2_m ? 1 : 0,"Bottom End",style=shape.circle, location = location.bottom, color = close<open ? #00be00 : color.rgb(0, 45, 208))

// Plot MA
plot(i_PC_show_ma ? h2 : na, title = 'H2', color = color.white)
plot(i_PC_show_ma ? h3 : na, title = 'H3', color = color.blue)
// Plot Levels
plot(pc_i_show_info ? PC_get_level(h3) : na, title = 'Get Level', color = color.new(color.blue,100))

// Alerts
pc_sell_cond = pc_i_show_info and close>PC_R2_m and get_pip_distance(PC_R2_m, high) > 2
pc_buy_cond  = pc_i_show_info and close<PC_S2_m and get_pip_distance(PC_S2_m, low) > 2
// pc_sell_cond = pc_i_show_info and close>PC_R2_m and h3>PC_R2_m
// pc_buy_cond  = pc_i_show_info and close<PC_S2_m and h3<PC_S2_m

plotshape(pc_sell_cond ? 1 : na,title="Sell",color=red ,style=shape.circle,location=location.top)
plotshape(pc_buy_cond  ? 1 : na,title="Buy",color=green ,style=shape.circle,location=location.bottom)

//-----------------------------------------------------------------------------}

