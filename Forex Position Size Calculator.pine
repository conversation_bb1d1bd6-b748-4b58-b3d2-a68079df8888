//@version=5
// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © ZenAndTheArtOfTrading / www.PineScriptMastery.com
indicator('Forex Position Size Calculator', overlay=true, precision=2)

// Get user input
accountBalance = input.float(title='Account Balance', defval=10000.0, step=100, group='AutoView Oanda Settings', tooltip='Your Oanda account balance (optional - used for automation through AutoView plugin)')
accountCurrency = input.string(title='Account Currency', defval='USD', options=['AUD', 'CAD', 'CHF', 'EUR', 'GBP', 'JPY', 'NZD', 'USD'], group='AutoView Oanda Settings', tooltip='Your Oanda account currency (optional - used for automation through AutoView plugin)')
riskPerTrade = input.float(1.0, title="Risk Per Trade %", step=0.5, tooltip="Your risk per trade as a % of your account balance (optional - used for automation through AutoView plugin)")
i_riskReward = input.float(2.0, title="Risk Reward", step=0.5, tooltip="Risk Reward")
i_show_sym_info = input.bool(false, title='Show Broker Information')

// Get current ATR
atr = ta.atr(14)

// Custom function to convert pips into whole numbers
toWhole(number) =>
    return_1 = atr < 1.0 ? number / syminfo.mintick / (10 / syminfo.pointvalue) : number
    return_1 := atr >= 1.0 and atr < 100.0 and syminfo.currency == 'JPY' ? return_1 * 100 : return_1
    return_1

//------------- DETERMINE POSITION SIZE -------------//
// Check if our account currency is the same as the base or quote currency (for risk $ conversion purposes)
accountSameAsCounterCurrency = accountCurrency == syminfo.currency
accountSameAsBaseCurrency = accountCurrency == syminfo.basecurrency

// Check if our account currency is neither the base or quote currency (for risk $ conversion purposes)
accountNeitherCurrency = not accountSameAsCounterCurrency and not accountSameAsBaseCurrency

// Get currency conversion rates if applicable
conversionCurrencyPair = accountSameAsCounterCurrency ? syminfo.tickerid : accountNeitherCurrency ? accountCurrency + syminfo.currency : accountCurrency + syminfo.currency
conversionCurrencyRate = request.security(symbol=syminfo.type == 'forex' ? conversionCurrencyPair : 'GBPUSD', timeframe='30', expression=close)



// Position Size
leverage = input.string(title='Leverage', defval='50', options=['10', '25', '50', '100'], group='AutoView Oanda Settings', tooltip='Your Oanda account currency (optional - used for automation through AutoView plugin)')
exchange_rate = request.security(syminfo.tickerid, '', close)
sym_type = syminfo.type
sym_id = syminfo.tickerid
sym_ticker = syminfo.ticker

lotsize = math.round(accountBalance * str.tonumber(leverage) / exchange_rate)
// pip value
pip_size = 0.0001
pip_value = pip_size / exchange_rate * lotsize

plot(lotsize, title='Lot Size', color=color.new(color.blue, 100))
plot(exchange_rate, title='Exhange Rate', color=color.new(color.blue, 100))

// Calculate position size
getPositionSize(stopLossSizePoints) =>
    riskAmount = accountBalance * (riskPerTrade / 100) * (accountSameAsBaseCurrency or accountNeitherCurrency ? conversionCurrencyRate : 1.0)
    riskPerPoint = stopLossSizePoints * syminfo.pointvalue
    positionSize = syminfo.type == 'forex' ? riskAmount / riskPerPoint / syminfo.mintick : 0
    math.round(positionSize)
//------------- END POSITION SIZE CODE -------------//

// Detect setup
var inLongTrade = false
var riskReward = i_riskReward
var tradeEntry = 0.0
var tradeStop = 0.0
var tradeStopDistance = 0.0
var tradeTarget = 0.0
var tradeSize = 0
i_lookback = input.int(10,title='Lookback') // Remove only temporary!
longSetup = low == ta.lowest(low, i_lookback)
//longSetup = low == ta.lowest(low, 15) and close >= open[1] and close[1] < open[1] and open >= low[1]

// Calculate stops & targets
if longSetup and not inLongTrade
    tradeStop := low - atr
    tradeEntry := close
    tradeStopDistance := close - tradeStop
    tradeTarget := close + tradeStopDistance * riskReward
    tradeSize := getPositionSize(toWhole(tradeStopDistance) * 10)
    inLongTrade := true
    inLongTrade

// Exit trade when appropriate
if inLongTrade and (high >= tradeTarget or low <= tradeStop)
    inLongTrade := false
    inLongTrade

// Draw setup info
plotshape(longSetup, color=color.new(color.green, 0), style=shape.triangleup, location=location.belowbar, title='Long Setup')
plot(inLongTrade ? tradeEntry : na, color=color.new(color.red, 0), style=plot.style_linebr, title='Trade Entry')
plot(inLongTrade ? tradeStop : na, color=color.new(color.red, 0), style=plot.style_linebr, title='Trade Stop')
plot(inLongTrade ? tradeTarget : na, color=color.new(color.green, 0), style=plot.style_linebr, title='Trade Target')

// Draw position size
plot(inLongTrade ? tradeStopDistance : na, color=color.new(color.gray, 100), title='Trade Stop Distance')
plot(inLongTrade ? tradeSize : na, color=color.new(color.purple, 100), title='Position Size')

var trade_info = table.new(position = position.middle_right, columns = 1, rows = 3, bgcolor = color.blue, border_width = 1, border_color=color.new(color.white,80) )
var string decimals = '#.#####'
if barstate.islast

    in_profit = inLongTrade and close>tradeEntry ? 1 : 0
    cell_col = in_profit ? color.green : inLongTrade and in_profit==0 ? color.red : color.blue
    //'Pair: ' + str.tostring(conversionCurrencyPair)
    position_txt =
     'Position Size: ' + str.tostring(lotsize) 
     + '\nRate: ' + str.tostring(exchange_rate) 
     + '\nPip Size: ' + str.tostring(pip_value) 
    stop_loss_txt = 'Entry Price: ' + str.tostring(tradeEntry, decimals) 
     + '\nStop Price: ' + str.tostring(tradeStop, decimals) 
     + '\nSL: ' + str.tostring(tradeStopDistance, decimals) 
     + '\nTP: ' + str.tostring(tradeTarget, decimals) 
    sym_info_label = 'Sym Type: ' + str.tostring(sym_type) 
     + '\nSym ID: ' + str.tostring(sym_id) 
     + '\nSum Ticker: ' + str.tostring(sym_ticker)

    table.cell(table_id = trade_info, column = 0, row = 0, text_color=color.white, text_halign=text.align_left,text = str.tostring(position_txt))
    
    table.cell(table_id = trade_info, column = 0, row = 1, bgcolor=cell_col, text_color=color.white, text_halign=text.align_left,text = str.tostring(stop_loss_txt))
    if i_show_sym_info 
        table.cell(table_id = trade_info, column = 0, row = 2, text_color=color.white, text_halign=text.align_left,text = str.tostring(sym_info_label), bgcolor=color.teal)

// if barstate.islastconfirmedhistory
//     myLabel = label.new(x=bar_index + 8, y=close, style=label.style_label_left, color=color.blue, textcolor=color.white, size=size.normal, 
//      text='Position Size: ' + str.tostring(lotsize) 
//      + '\nRate: ' + str.tostring(exchange_rate) 
//      + '\nPip Size: ' + str.tostring(pip_value) 
//      + '\nSym Type: ' + str.tostring(sym_type) 
//      + '\nSym ID: ' + str.tostring(sym_id) 
//      + '\nSum Ticker: ' + str.tostring(sym_ticker)
//      )
//     myLabel




