// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © jdehorty
// @version=5

// A non-repainting implementation of Nadaraya–Watson Regression using a Rational Quadratic Kernel. 

indicator('Nadaraya-Watson: Rational Quadratic Kernel (Non-Repainting)', overlay=true, timeframe="")

// Settings
src = input.source(close, 'Source')
h = input.float(8., 'Lookback Window', minval=3., tooltip='The number of bars used for the estimation. This is a sliding value that represents the most recent historical bars. Recommended range: 3-50')
r = input.float(8., 'Relative Weighting', step=0.25, tooltip='Relative weighting of time frames. As this value approaches zero, the longer time frames will exert more influence on the estimation. As this value approaches infinity, the behavior of the Rational Quadratic Kernel will become identical to the Gaussian kernel. Recommended range: 0.25-25')
x_0 = input.int(25, "Start Regression at Bar", tooltip='Bar index on which to start regression. The first bars of a chart are often highly volatile, and omission of these initial bars often leads to a better overall fit. Recommended range: 5-25')
smoothColors = input.bool(false, "Smooth Colors", tooltip="Uses a crossover based mechanism to determine colors. This often results in less color transitions overall.", inline='1', group='Colors')
lag = input.int(2, "Lag", tooltip="Lag for crossover detection. Lower values result in earlier crossovers. Recommended range: 1-2", inline='1', group='Colors')
size = array.size(array.from(src)) // size of the data series

// Further Reading:
// The Kernel Cookbook: Advice on Covariance functions. David Duvenaud. Published June 2014.
// Estimation of the bandwidth parameter in Nadaraya-Watson kernel non-parametric regression based on universal threshold level. Ali T, Heyam Abd Al-Majeed Hayawi, Botani I. Published February 26, 2021.
kernel_regression(_src, _size, _h) =>
    float _currentWeight = 0.
    float _cumulativeWeight = 0.
    for i = 0 to _size + x_0
        y = _src[i] 
        w = math.pow(1 + (math.pow(i, 2) / ((math.pow(_h, 2) * 2 * r))), -r)
        _currentWeight += y*w
        _cumulativeWeight += w
    _currentWeight / _cumulativeWeight

// Estimations
yhat1 = kernel_regression(src, size, h)
yhat2 = kernel_regression(src, size, h-lag)

// Rates of Change
bool wasBearish = yhat1[2] > yhat1[1]
bool wasBullish = yhat1[2] < yhat1[1]
bool isBearish = yhat1[1] > yhat1
bool isBullish = yhat1[1] < yhat1
bool isBearishChange = isBearish and wasBullish
bool isBullishChange = isBullish and wasBearish

// Crossovers
bool isBullishCross = ta.crossover(yhat2, yhat1)
bool isBearishCross = ta.crossunder(yhat2, yhat1) 
bool isBullishSmooth = yhat2 > yhat1
bool isBearishSmooth = yhat2 < yhat1

// Colors
color c_bullish = input.color(#3AFF17, 'Bullish Color', group='Colors')
color c_bearish = input.color(#FD1707, 'Bearish Color', group='Colors')
color colorByCross = isBullishSmooth ? c_bullish : c_bearish
color colorByRate = isBullish ? c_bullish : c_bearish
color plotColor = smoothColors ? colorByCross : colorByRate

// Plot
plot(yhat1, "RQK", color=plotColor, linewidth=2)

// Alert Variables
bool alertBullish = smoothColors ? isBearishCross : isBearishChange
bool alertBearish = smoothColors ? isBullishCross : isBullishChange

// Alerts for Color Changes
alertcondition(condition=alertBullish, title='Bearish Color Change', message='Nadaraya-Watson: {{ticker}} ({{interval}}) turned Bearish ▼')
alertcondition(condition=alertBearish, title='Bullish Color Change', message='Nadaraya-Watson: {{ticker}} ({{interval}}) turned Bullish ▲')

// Non-Displayed Plot Outputs (i.e., for use in other indicators)
plot(alertBearish ? -1 : alertBullish ? 1 : 0, "Alert Stream", display=display.none)