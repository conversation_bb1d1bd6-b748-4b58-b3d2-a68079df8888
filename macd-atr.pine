//@version=4
study(title="MACD SSLxBB", shorttitle="MACD SSLxBB", resolution="")

// ATR plot
atrlen = input(14, "ATR Period")
smoothing = input(title = "ATR Smoothing", defval = "WMA", options = ["RMA", "SMA", "EMA", "WMA"])
mult = input(1, "ATR Multi", step = 0.1)
signal_length = 9
ma_function(source, atrlen) =>
	if smoothing == "RMA"
		rma(source, atrlen)
	else
		if smoothing == "SMA"
			sma(source, atrlen)
		else
			if smoothing == "EMA"
				ema(source, atrlen)
			else
				wma(source, atrlen)

atr_slen = ma_function(tr(true), atrlen)
upper_band = atr_slen * mult + close
lower_band = close - atr_slen * mult
//u = plot(show_atr ? upper_band : na, "+ATR", color=color.white, transp=80)
//l = plot(show_atr ? lower_band : na, "-ATR", color=color.white, transp=80)

// Plot colors
col_grow_above = #26A69A
col_grow_below = #FFCDD2
col_fall_above = #B2DFDB
col_fall_below = #EF5350
col_macd = #0094ff
col_signal = #ff6a00

// Calculating
fast_ma = upper_band
slow_ma = lower_band
macd = fast_ma - slow_ma
signal = ema(macd, signal_length)
hist = macd - signal

plot(hist, title="Histogram", style=plot.style_columns, color=(hist>=0 ? (hist[1] < hist ? col_grow_above : col_fall_above) : (hist[1] < hist ? col_grow_below : col_fall_below) ), transp=0 )
plot(macd, title="MACD", color=col_macd, transp=0)
plot(signal, title="Signal", color=col_signal, transp=0)