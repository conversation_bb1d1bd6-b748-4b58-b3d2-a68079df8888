//@version=5
indicator(title="MA v5", shorttitle="MA v5", overlay=true)
//indicator(title="MA v5", shorttitle="MA v5", overlay=true,timeframe="60",timeframe_gaps=false)
i_ma_time = input.timeframe(title='Timeframe', defval='30')
i_gaps = input.bool(true,title='Use Gaps')
i_lookahead = input.bool(true,title='Use Lookahead')
i_offset = input.int(0, title="Offset") 
src = input.source(close, "Source")
use_smooth = input.bool(false, title="Use Smoothing")
smooth = input.int(5,title="Smoothing")
use_multiple = input.bool(false, title="Use Multiply")
multi_value = input.int(10, title="Multiply Value")
angle_amount = input.int(14, title="Angle Amount")
show_candles = input.bool(false,title="Show Candle Division")
show_angles = input.bool(false,title='Show Angles')

g_cb = "Show ----------------------------------------------------"
inl_cb = "cb"
show_m1 = input.bool(title="M1", defval=false,group=g_cb,inline=inl_cb)
show_m2 = input.bool(title="M2", defval=true,group=g_cb,inline=inl_cb)
show_m3 = input.bool(title="M3", defval=false,group=g_cb,inline=inl_cb)
show_m4 = input.bool(title="M4", defval=true,group=g_cb,inline=inl_cb)
show_m5 = input.bool(title="M5", defval=false,group=g_cb,inline=inl_cb)
show_m6 = input.bool(title="M6", defval=false,group=g_cb,inline=inl_cb)
show_m7 = input.bool(title="M7", defval=false,group=g_cb,inline=inl_cb)
show_m8 = input.bool(title="M8", defval=false,group=g_cb,inline=inl_cb)
show_m9 = input.bool(title="M9", defval=false,group=g_cb,inline=inl_cb)

g_ma = "MA ----------------------------------------------------"
inl_ma = "ma"
ma_type = input.string(title="MA Type", defval="hma", options=["sma","ema","zema","dema","tema","wma","vwma","smma","rma","hma","lsma","McGinley"],group=g_ma)
use_candles = input.bool(false,title="Colorize Candles")

l1 = input.int(5,title="M1",group=g_ma,inline=inl_ma)
l2 = input.int(8,title="M2",group=g_ma,inline=inl_ma)
l3 = input.int(50,title="M3",group=g_ma,inline=inl_ma)
l4 = input.int(75,title="M4",group=g_ma,inline=inl_ma)
l5 = input.int(100,title="M5",group=g_ma,inline=inl_ma)
l6 = input.int(200,title="M6",group=g_ma,inline=inl_ma)
l7 = input.int(300,title="M7",group=g_ma,inline=inl_ma)
l8 = input.int(500,title="M8",group=g_ma,inline=inl_ma)
l9 = input.int(1000,title="M9",group=g_ma,inline=inl_ma)

g_angles = "MA Angles ----------------------------------------------------"
inl_angles = "angles"
i_ang_1 =  input.int(1, title='A1',group=g_angles,inline=inl_angles)
i_ang_2 =  input.int(2, title='A2',group=g_angles,inline=inl_angles)
i_ang_3 =  input.int(3, title='A3',group=g_angles,inline=inl_angles)
i_ang_4 =  input.int(4, title='A4',group=g_angles,inline=inl_angles)
i_ang_5 =  input.int(5, title='A5',group=g_angles,inline=inl_angles)
i_ang_6 =  input.int(7, title='A6',group=g_angles,inline=inl_angles)
i_ang_7 =  input.int(10, title='A7',group=g_angles,inline=inl_angles)
i_ang_8 =  input.int(12, title='A8',group=g_angles,inline=inl_angles)

g_select = "Select ----------------------------------------------------"
i_sel_ma = input.int(title='MA Select', defval=3, options=[1,2,3,4,5,6,7,8,9], group=g_select)
i_sel_angle = input.int(title="Angle Select", defval=3, options=[1,2,3,4,5,7,10,12,14],group=g_select)
// 2VQu^D!ynhf&
// Equivalent HMA 15 min
// M5 - 125 for M2 20
// M8 - 400 for M4 75

// if use_multiple
//     l1 := l1 * multi_value
//     l2 := l2 * multi_value
//     l3 := l3 * multi_value
//     l4 := l4 * multi_value
//     l5 := l5 * multi_value
//     l6 := l6 * multi_value
//     l7 := l7 * multi_value
//     l8 := l8 * multi_value

g_fill = "Fills"
inl_fill = "fill"
inl_conv = "conv"
i_ma_select = input.int(8, title="Colorized", options=[1,2,3,4,5,6,7,8])
show_fill = input.bool(title="Show Fill", defval=true,inline=inl_fill,group=g_fill)
show_conv = input.bool(title="Show Conv", defval=true,inline=inl_fill,group=g_fill)
conv_amount = input.float(14, title="Conv Amount", step=1,inline=inl_conv,group=g_fill ) // 4
c_type = input.string(title="Type", defval="NAS", options=["NAS","USD", "JPY"],inline=inl_conv,group=g_fill)
line_input = 1 //input(1, title="Line width", type=input.integer,inline=inl_fill )



red = #ff0062
orange = #ff9800
yellow = #FFFF00
green = #005a04
lime = #00E676
aqua = #00bcd4
blue = #2962ff
dark_blue = #2962ff
violet = #814dff
white = #ffffff
gray = #707070

angle(_src, len) =>
    rad2degree=180/3.14159265359  //pi 
    ang=rad2degree*math.atan((_src[0] - _src[1]) / ta.atr(len))
    
// Lines and Angles
ma_graph(len) =>
    ma =0.0
    length = 1
    if use_multiple
        length := len * multi_value
    else 
        length := len

    if ma_type == 'sma' // Simple
        ma := ta.sma(src,length) 

    if ma_type == 'ema' // Exponential
        ma := ta.ema(src,length)

    if ma_type == 'zema' // Zero Lag Exponential
        e1 = ta.ema(close,length)
        e2 = ta.ema(e1,length)
        diff = e1 - e2
        ma := e1 + diff 

    if ma_type=="dema" // Double Exponential
        e = ta.ema(src, length)
        ma := 2 * e - ta.ema(e, length)
    if ma_type == 'tema' // Triple Exponential
        ema1 = ta.ema(src, length)
        ema2 = ta.ema(ema1, length)
        ema3 = ta.ema(ema2, length)
        ma := 3 * (ema1 - ema2) + ema3
    if ma_type == 'wma' // Weighted
        ma := ta.wma(src,length)
    if ma_type == 'vwma' // Volume Weighted
        ma := ta.vwma(src,length)
    if ma_type=="smma" // Smoothed
        w = ta.wma(src, length)
        ma := na(w[1]) ? ta.sma(src, length) : (w[1] * (length - 1) + src) / length
    if ma_type == "rma"
        ma := ta.rma(src, length)
    if ma_type == 'hma' // Hull
        ma := ta.hma(src, length)
       // ma := ta.wma(2*ta.wma(src, length/2)-ta.wma(src, length), math.floor(math.sqrt(length) ))
    if ma_type=="lsma" // Least Squares
        ma := ta.linreg(src, length, 0)
    if ma_type=="McGinley"
        mg = 0.0
        mg := na(mg[1]) ? ta.ema(src, length) : mg[1] + (src - mg[1]) / (length * math.pow(src/mg[1], 4))
        ma :=mg

    if ma_type=="ATR"
        mg = 0.0
        mg := ta.atr(length)
        ma := mg

    if use_smooth
        ma := ta.sma(ma,smooth)

    ma_angle = angle(ma,angle_amount)
    
    [ma,ma_angle]


ma_graph_tmp(len) =>
    ma =0.0
    length = 1
    if use_multiple
        length := len * multi_value
    else 
        length := len

    if ma_type == 'sma' // Simple
        ma := ta.sma(src,length) 

    if ma_type == 'ema' // Exponential
        ma := ta.ema(src,length)

    if ma_type == 'zema' // Zero Lag Exponential
        e1 = ta.ema(close,length)
        e2 = ta.ema(e1,length)
        diff = e1 - e2
        ma := e1 + diff 

    if ma_type=="dema" // Double Exponential
        e = ta.ema(src, length)
        ma := 2 * e - ta.ema(e, length)
    if ma_type == 'tema' // Triple Exponential
        ema1 = ta.ema(src, length)
        ema2 = ta.ema(ema1, length)
        ema3 = ta.ema(ema2, length)
        ma := 3 * (ema1 - ema2) + ema3
    if ma_type == 'wma' // Weighted
        ma := ta.wma(src,length)
    if ma_type == 'vwma' // Volume Weighted
        ma := ta.vwma(src,length)
    if ma_type=="smma" // Smoothed
        w = ta.wma(src, length)
        ma := na(w[1]) ? ta.sma(src, length) : (w[1] * (length - 1) + src) / length
    if ma_type == "rma"
        ma := ta.rma(src, length)
    if ma_type == 'hma' // Hull
        ma := ta.hma(src, length)
       // ma := ta.wma(2*ta.wma(src, length/2)-ta.wma(src, length), math.floor(math.sqrt(length) ))
    if ma_type=="lsma" // Least Squares
        ma := ta.linreg(src, length, 0)
    if ma_type=="McGinley"
        mg = 0.0
        mg := na(mg[1]) ? ta.ema(src, length) : mg[1] + (src - mg[1]) / (length * math.pow(src/mg[1], 4))
        ma :=mg

    if ma_type=="ATR"
        mg = 0.0
        mg := ta.atr(length)
        ma := mg

    if use_smooth
        ma := ta.sma(ma,smooth)

    ma_angle = angle(ma,angle_amount)
    
    ma

ma_conv(t1, t2) =>
    
    float boost = switch syminfo.ticker
        "GBPUSD" => 10000
        "EURUSD" => 10000
        "GBPJPY" => 100
        "EURJPY" => 100
        "GBPNZD" => 1000
        "BTCUSD" => 0.1
        "NAS100" => 1
        "NDQ100" => 1
        "GOLD"   => 10
        "SILVER" => 100
        => 1

    diff = (t1 - t2) * boost
    conv = show_conv and diff < conv_amount and diff > conv_amount * -1 ? true : false
    [diff, conv]

// MA's
change = ta.change(time(i_ma_time))
[m1,m1_a] = request.security(syminfo.tickerid, i_ma_time, ma_graph(l1),gaps=i_gaps ? barmerge.gaps_on : barmerge.gaps_off, lookahead=i_lookahead ? barmerge.lookahead_on : barmerge.lookahead_off )
[m2,m2_a] = request.security(syminfo.tickerid, i_ma_time, ma_graph(l2),gaps=i_gaps ? barmerge.gaps_on : barmerge.gaps_off, lookahead=i_lookahead ? barmerge.lookahead_on : barmerge.lookahead_off ) 
[m3,m3_a] = request.security(syminfo.tickerid, i_ma_time, ma_graph(l3),gaps=i_gaps ? barmerge.gaps_on : barmerge.gaps_off, lookahead=i_lookahead ? barmerge.lookahead_on : barmerge.lookahead_off ) 
[m4,m4_a] = request.security(syminfo.tickerid, i_ma_time, ma_graph(l4),gaps=i_gaps ? barmerge.gaps_on : barmerge.gaps_off, lookahead=i_lookahead ? barmerge.lookahead_on : barmerge.lookahead_off ) 
[m5,m5_a] = request.security(syminfo.tickerid, i_ma_time, ma_graph(l5),gaps=i_gaps ? barmerge.gaps_on : barmerge.gaps_off, lookahead=i_lookahead ? barmerge.lookahead_on : barmerge.lookahead_off ) 
[m6,m6_a] = request.security(syminfo.tickerid, i_ma_time, ma_graph(l6),gaps=i_gaps ? barmerge.gaps_on : barmerge.gaps_off, lookahead=i_lookahead ? barmerge.lookahead_on : barmerge.lookahead_off ) 
[m7,m7_a] = request.security(syminfo.tickerid, i_ma_time, ma_graph(l7),gaps=i_gaps ? barmerge.gaps_on : barmerge.gaps_off, lookahead=i_lookahead ? barmerge.lookahead_on : barmerge.lookahead_off ) 
[m8,m8_a] = request.security(syminfo.tickerid, i_ma_time, ma_graph(l8),gaps=i_gaps ? barmerge.gaps_on : barmerge.gaps_off, lookahead=i_lookahead ? barmerge.lookahead_on : barmerge.lookahead_off ) 
[m9,m9_a] = request.security(syminfo.tickerid, i_ma_time, ma_graph(l9),gaps=i_gaps ? barmerge.gaps_on : barmerge.gaps_off, lookahead=i_lookahead ? barmerge.lookahead_on : barmerge.lookahead_off ) 
// m1 := na(m1) ? m1[1] : m1
// m2 := na(m2) ? m2[1] : m2
// m3 := na(m3) ? m3[1] : m3
// m4 := na(m4) ? m4[1] : m4
// m5 := na(m5) ? m5[1] : m5
// m6 := na(m6) ? m6[1] : m6
// m7 := na(m7) ? m7[1] : m7
// m8 := na(m8) ? m8[1] : m8
// [m1,m1_a] = change ? request.security(syminfo.tickerid, i_ma_time, ma_graph(l1):m1[1] ) 
// [m2,m2_a] = change ? request.security(syminfo.tickerid, i_ma_time, ma_graph(l2):m2[1] ) 
// [m3,m3_a] = change ? request.security(syminfo.tickerid, i_ma_time, ma_graph(l3):m3[1] ) 
// [m4,m4_a] = change ? request.security(syminfo.tickerid, i_ma_time, ma_graph(l4):m4[1] ) 
// [m5,m5_a] = change ? request.security(syminfo.tickerid, i_ma_time, ma_graph(l5):m5[1] ) 
// [m6,m6_a] = change ? request.security(syminfo.tickerid, i_ma_time, ma_graph(l6):m6[1] ) 
// [m7,m7_a] = change ? request.security(syminfo.tickerid, i_ma_time, ma_graph(l7):m7[1] ) 
// [m8,m8_a] = change ? request.security(syminfo.tickerid, i_ma_time, ma_graph(l8):m8[1] ) 
// [m9,m9_a] = change ? request.security(syminfo.tickerid, i_ma_time, ma_graph(l9):m9[1] ) 

//gaps=barmerge.gaps_on,lookahead =barmerge.lookahead_on

// [m1,m1_a] = ma_graph(l1)
// [m2,m2_a] = ma_graph(l2)
// [m3,m3_a] = ma_graph(l3)
// [m4,m4_a] = ma_graph(l4)
// [m5,m5_a] = ma_graph(l5)
// [m6,m6_a] = ma_graph(l6)
// [m7,m7_a] = ma_graph(l7)
// [m8,m8_a] = ma_graph(l8)
// [m9,m9_a] = ma_graph(l9)

[m2_m4_diff,m2_m4_conv] = ma_conv(m2,m4)
[m4_m5_diff,m4_m5_conv] = ma_conv(m4,m5)
[m5_m6_diff,m5_m6_conv] = ma_conv(m5,m6)
[m7_m8_diff,m7_m8_conv] = ma_conv(m7,m8)

// Colorized MA angles
ma_select()=>
    float select = switch i_ma_select
        2 => m2_a
        3 => m3_a
        4 => m4_a
        5 => m5_a
        6 => m6_a
        7 => m7_a
        8 => m8_a
        => m5_a

    ma_a = math.abs(select)
    ma_zone =  
     ma_a   < i_ang_1 ? 0 
     : ma_a < i_ang_2 ? 1 
     : ma_a < i_ang_3 ? 2 
     : ma_a < i_ang_4 ? 3 
     : ma_a < i_ang_5 ? 4 
     : ma_a < i_ang_6 ? 5 
     : ma_a < i_ang_7 ? 6 
     : ma_a < i_ang_8 ? 7 
     : ma_a > i_ang_7 ? 8 
     : na

    ma_color = 
     ma_zone   == 0 ? red 
     : ma_zone == 1 ? orange 
     : ma_zone == 2 ? yellow 
     : ma_zone == 3 ? gray 
     : ma_zone == 4 ? green 
     : ma_zone == 5 ? lime
     : ma_zone == 6 ? blue
     : ma_zone == 7 ? aqua
     : ma_zone == 8 ? white : na

    // ma_a = math.abs(select)
    // ma_zone =  
    //  ma_a   < 1 ? 0 
    //  : ma_a < 2 ? 1 
    //  : ma_a < 3 ? 2 
    //  : ma_a < 4 ? 3 
    //  : ma_a < 5 ? 4 
    //  : ma_a < 6 ? 5 
    //  : ma_a > 6 ? 6 
    //  : na

    // ma_color = 
    //  ma_zone   == 0 ? red 
    //  : ma_zone == 1 ? orange 
    //  : ma_zone == 2 ? yellow 
    //  : ma_zone == 3 ? gray 
    //  : ma_zone == 4 ? blue 
    //  : ma_zone == 5 ? lime 
    //  : ma_zone == 6 ? white : na

    // ma_zone =  
    //  ma_a   < 1 ? 0 
    //  : ma_a < 2 ? 1 
    //  : ma_a < 3 ? 2 
    //  : ma_a < 4 ? 3 
    //  : ma_a < 5 ? 4 
    //  : ma_a < 6 ? 5 
    //  : ma_a < 7 ? 6
    //  : ma_a < 8 ? 7
    //  : ma_a < 9 ? 8
    //  : ma_a > 9 ? 9 
    //  : na

    // ma_color = 
    //  ma_zone   == 0 ? #930000 
    //  : ma_zone == 1 ? #e54500
    //  : ma_zone == 2 ? #e5aa00
    //  : ma_zone == 3 ? #777777
    //  : ma_zone == 4 ? #00429a
    //  : ma_zone == 5 ? #0498b6 
    //  : ma_zone == 6 ? #1d6700
    //  : ma_zone == 7 ? #31b000
    //  : ma_zone == 8 ? #eeeeee 
    //  : ma_zone == 9 ? white 
    //  : na

    // ma_color = 
    //  ma_zone   == 0 ? red 
    //  : ma_zone == 1 ? orange 
    //  : ma_zone == 2 ? yellow 
    //  : ma_zone == 3 ? green 
    //  : ma_zone == 4 ? lime 
    //  : ma_zone == 5 ? aqua 
    //  : ma_zone == 6 ? blue
    //  : ma_zone == 7 ? dark_blue 
    //  : ma_zone == 8 ? violet 
    //  : ma_zone == 9 ? white 
    //  : na

    [ma_color]

[ma_color] = ma_select()

// Colorize candles based on MA angles
barcolor(use_candles? ma_color : na)
// Plots
l_width = 2

// Angles
plot(show_angles and m1_a ? m1_a : na,color=m1_a>0?color.new(aqua,100):color.new(orange,100),title="M1 A")
plot(show_angles and m2_a ? m2_a : na,color=m2_a>0 ? color.new(color.green,100): color.new(color.red,100),title="M2 A")
plot(show_angles and m3_a ? m3_a : na,color=i_ma_select==3 ? color.new(ma_color,100) : color.new(color.blue,100),title="M3 A")
plot(show_angles and m4_a ? m4_a : na,color=i_ma_select==4 ? color.new(ma_color,100) : color.new(yellow,100),title="M4 A")
plot(show_angles and m5_a ? m5_a : na,color=i_ma_select==5 ? color.new(ma_color,100) : color.new(orange,100),title="M5 A")
plot(show_angles and m6_a ? m6_a : na,color=i_ma_select==6 ? color.new(ma_color,100) : color.new(red,100), title="M6 A")
plot(show_angles and m7_a ? m7_a : na,color=i_ma_select==7 ? color.new(ma_color,100) : color.new(orange,100),title="M7 A")
plot(show_angles and m8_a ? m8_a : na,color=i_ma_select==8 ? color.new(ma_color,100) : color.new(red,100),title="M8 A")
plot(show_angles and m9_a ? m9_a : na,color=i_ma_select==9 ? color.new(ma_color,100) : color.new(red,100),title="M9 A")

p_m1 = plot(m1 and show_m1?m1:na, style=plot.style_line, color= i_ma_select==1 ? ma_color : m1_a>0?aqua:orange,title="M1",linewidth=i_ma_select==1?l_width:1,offset = i_offset)
p_m2 = plot(m2 and show_m2?m2:na, style=plot.style_line, color= i_ma_select==2 ? ma_color : m2_a>0 ? green: red,title="M2",linewidth=i_ma_select==2?l_width:1,offset = i_offset)
p_m3 = plot(m3 and show_m3?m3:na, style=plot.style_line, color= i_ma_select==3 ? ma_color : m3_a>0 ? blue : aqua,title="M3",linewidth=i_ma_select==3?l_width:1,offset = i_offset)
p_m4 = plot(m4 and show_m4?m4:na, style=plot.style_line, color= i_ma_select==4 ? ma_color : yellow,title="M4",linewidth=i_ma_select==4?l_width:1,offset = i_offset)
p_m5 = plot(m5 and show_m5?m5:na, style=plot.style_line, color= i_ma_select==5 ? ma_color : m5_a<1 and m5_a>-1 ? aqua : m5_a>0 ? orange : m5_a<0 and m5>m6 ? red : green,title="M5",linewidth=i_ma_select==5?l_width:1,offset = i_offset)
p_m6 = plot(m6 and show_m6?m6:na, style=plot.style_line, color= i_ma_select==6 ? ma_color : m6_a > 0 ? red : lime, title="M6",linewidth=i_ma_select==6?l_width:1,offset = i_offset)
p_m7 = plot(m7 and show_m7?m7:na, style=plot.style_line, color= i_ma_select==7 ? ma_color : m7_a>0 ? orange : m7_a<0 and m7>m8 ? red : green,title="M7",linewidth=i_ma_select==7?l_width:1,offset = i_offset)
p_m8 = plot(m8 and show_m8?m8:na, style=plot.style_line, color= i_ma_select==8 ? ma_color : m8_a>0 ? red : lime,title="M8",linewidth=i_ma_select==8?l_width:1,offset = i_offset)
p_m9 = plot(m9 and show_m9?m9:na, style=plot.style_line, color= i_ma_select==9 ? ma_color : m9_a>0 ? red : lime,title="M9",linewidth=i_ma_select==9?l_width:1,offset = i_offset)


// Fills
fill(p_m5,p_m6,title="S5/S6 Conv", color=show_fill and m5_m6_diff<1?color.new(green,90): show_fill ? color.new(red,90):na,fillgaps=true)
fill(p_m7,p_m8,title="S7/S8 Conv", color=show_fill and m7_m8_diff<1?color.new(green,90): show_fill ? color.new(red,90):na,fillgaps=true)
//Convergence
// fill(p_m2,p_m4,title="M2/M4 Conv", color=m2_m4_conv and m2>m4?color.new(red,70) : m2_m4_conv and m2<m4?color.new(green,70) : na)
fill(p_m5,p_m6,title="M5/M6 Conv", color=m5_m6_conv and m5>m6?color.new(red,70) : m5_m6_conv and m5<m6?color.new(green,70) : na)
fill(p_m7,p_m8,title="M7/M8 Conv", color=m7_m8_conv and m7>m8?color.new(red,70) : m7_m8_conv and m7<m8?color.new(green,70) : na)
// plot(m2_m4_diff, title="M2/4 Conv",color=m2_m4_conv and m2>m4?color.new(red,70) : m2_m4_conv and m2<m4?color.new(green,70) : na )
//plot(m5_m6_diff, title="M5/6 Conv",color=m5_m6_conv and m5>m6?color.new(red,70) : m5_m6_conv and m5<m6?color.new(green,70) : na )
//plot(m7_m8_diff, title="M7/8 Conv",color=m7_m8_conv and m7>m8?color.new(red,70) : m7_m8_conv and m7<m8?color.new(green,70) : na )

// Notes MA angles
// Smooth: 5  Angle amount:25. Let M2 1st higher higher or lower lower


var int flag_u = 0
var int flag_d = 0
// flag_u := m1_a>20 ? 1  : flag_u==1  and m1_a>0 ? 1  : flag_u==1  and m1_a<0 ? 0 : na
// flag_d := m1_a<-10 ? -1 : flag_d==-1 and m1_a<0 ? -1 : flag_d==-1 and m1_a>0 ? 0 : na
// plotshape(flag_u==0 ? 1 : na,title="Flag Up",color=red ,style=shape.circle,location=location.top)
// plotshape(flag_d==0 ? 1 : na,title="Flag Down",color=green ,style=shape.circle,location=location.bottom)

// Buy Green up angle m6
green_up_angle = m5<m6 and m5_a>0 and close<m5 and m3_a>-1 and not(m3<m5 and close>m3)
plotshape(green_up_angle ? 1 : na,title="Green Up Angle", color=green ,style=shape.circle,location=location.bottom, size=size.small)

// Candle change
//plotshape(show_candles?close:na,title="Candles Multi",color=red ,style=shape.circle,location=location.top)
candle = close>open?1:-1
// m8 angle down
sell_cond = m8_a<0 and close>m8
// M2 M4 conv
sell_m2_m4 = m2>m4 and m4>m5 and m4>m7 and m2_a<15 and m2_m4_conv
sell_cond1  = m3_a<0 and m4_a<0 and m7>m8 and close>m3 
 and not(m2_a>0) 
 and not(m7_a<0) ? 1 : na
sell_cond2 = m3_a<0 and m4_a<0 and m7>m8 and close>m4 and not(m7_a>0) ? 1 : na
sell_cond3 = close>m4 and close>m2 and close>m1 and m1>m2 and m2>m3
//plotshape(sell_cond,title="M8",color=red ,style=shape.circle,location=location.top)

// 15 min

buy_cond = m6_a>0 and not(m5_a<0) and m1<m2 and (m1_a[2]<0 and m1_a>0) and close>m1  
// 5 min
buy_cond1  = m8_a<0 and m6_a<0 and m3<m2 and m2_a>-5 and close<m2
 and not(m5>m6)
 and not(m3>m5)
 and not(m1_a<-5)
//buy_cond  = m8_a<0 and m6_a<0 and m3_a>0 and close<m3
//buy_cond  = m3<m2 and m2_a>5
buy_cond2  = m4>m5 and close<m4 and m4_a>-3 and m2_a>-10
//plotshape(buy_cond,title="Buy 1",color=green ,style=shape.circle,location=location.bottom)



// === Labels ===
// label_txt = "Instrument: " + str.tostring(syminfo.ticker)
// if barstate.islastconfirmedhistory
//     buy = label.new(x=bar_index + 20, y=close, style=label.style_label_left,color=color.blue, textcolor=color.white, size=size.normal,text=label_txt )