//@version=4
study(title="Timeframes", shorttitle="timeframes",overlay=true,max_labels_count=500)

red = #ff0062
aqua = #00bcd4
yellow = #FFFF00
orange = #ff9800
green = #4caf50
lime = #00E676
white = #ffffff
c_hide = color.new(#ffffff,100)
blue = #42a5f5
violet = #814dff
gray = #707070
black = #000000
sell_color = color.new(#ff0062,20)
buy_color = color.new(#00c3ff,20)

show_friday= input(title="Show Friday", type=input.bool, defval=false)
show_sessions = input(title="Sessions",type=input.bool,defval=true)
combine = input(title="Combine",type=input.bool,defval=true)

session = input(title="Session", defval="0800-0900", options=
 ["0000-0100", "0100-0200", "0200-0300", "0300-0400", "0400-0500", "0500-0600","0600-0700",
 "0700-0800", "0800-0900", "0900-1000", "1000-1100", "1100-1200", "1200-1300","1300-1400",
 "1400-1500", "1500-1600", "1600-1700", "1700-1800", "1800-1900", "2000-2100","2100-2200",
 "2200-2300","2300-2400"   ])

friday = color.new(sell_color,92)
bgcolor(dayofweek == 1 and show_friday ? friday : na)

// Europe/London - Pacific/Auckland - Asia/Tokyo
timeinrange(res, sess, loc) => not na(time(res, sess, loc)) ? 1 : 0
ny_close = timeinrange("2", "1000-1330", "Pacific/Honolulu")
bgcolor(ny_close == 1 and show_friday  ? color.new(gray,92) : na)
ny_open = timeinrange("2", session, "Pacific/Honolulu")
//ny_open = timeinrange("2", session, "America/New_York")
bgcolor(ny_open == 1 ? color.new(aqua,92) : na)