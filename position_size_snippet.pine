// ===  Trailing Stop ===
// ==================================================
atrlen = 14  //input(14, "ATR Period")
atr_slen = ta.atr(atrlen)
atr_upper = atr_slen * atr_mult + (atr_src=='close' ? close : high)
atr_lower = (atr_src=='close' ? close : low) - atr_slen * atr_mult

// ATR
plot(show_st ? atr_upper : na, '+ATR Upper', color=color.new(#ffffff, 80))
plot(show_st ? atr_lower : na, '-ATR Lower', color=color.new(#ffffff, 80))

// ===  Position Size ===
// ==================================================
upper_band = atr_slen * atr_mult + close
lower_band = close - atr_slen * atr_mult
calc_trade_values() =>
    p = 0.00
    rp = 0.00
    sl = 0.00
    pips = 0.00
    p_value = 0.00
    trade_dir = 0

    // Sell
    if trade_dir < 0
        sl := upper_band
        rp := (1 - upper_band / close) * 100
        p := 2 / rp * account_size
        p
    else
    // Buy
        sl := lower_band
        rp := (1 - lower_band / close) * 100
        p := 2 / rp * account_size
        p

    p := math.round(math.abs(p))
    rp := math.abs(rp)
    pips := math.abs((close - sl) * 10000)
    p_value := account_size * rp / pips

    [p, rp, sl, pips, p_value]