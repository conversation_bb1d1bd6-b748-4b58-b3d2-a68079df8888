//@version=4
study(title = "Trading Bot - JPY", shorttitle="TB - JPY",overlay=true)

show_entry = input(title="Show Entry", type=input.bool, defval=true)
show_atr = input(title="Show ATR", type=input.bool, defval=false)
show_plots = input(title="Show Plots", type=input.bool, defval=false)
show_bb= input(title="Show BB", type=input.bool, defval=true)
use_barcolor = input(title="Use Bar Color", type=input.bool, defval=false)
show_candles = input(title="Show Candlesticks", type=input.bool, defval=false)
use_rsi = input(title="Use RSI", type=input.bool, defval=true)
show_lastprice = input(title="Show Last Price", type=input.bool, defval=false)
use_logic = input(title="Use Logic", type=input.bool, defval=false)

red = #ff0062
aqua = #00bcd4
yellow = #FFFF00
orange = #ff9800
green = #4caf50
lime = #00E676
white = #ffffff
blue = #42a5f5
gray = #707070
black = #000000
sell_color = color.new(#ff0062,20)
buy_color = color.new(#00c3ff,20)

angle(_src, len) =>
    rad2degree=180/3.14159265359  //pi 
    ang=rad2degree*atan((_src[0] - _src[1])/atr(len))

// Change
perc_change() =>
    perc = abs( (1 - (close[1] / close)) * 10000 )


// ===  EMA's ===
// ==================================================
ema_200 = ema(close, 200)
ema_200_angle = angle(ema_200,2)
// ema_len_f = 5//input(5, minval=1, title="EMA Length") //6
// ema_fast = ema(close, ema_len_f)
// ema_angle = angle(ema_fast,2)



// === Bollinger Bands %B ===
// ==================================================
length = input(29, title="BBR",minval=1) // 43
multi = 1.8 //input(2.0, minval=0.001, maxval=50, title="StdDev")
bbr_basis = sma(close, length)
deviation = multi * stdev(close, length)
bbr_upper = bbr_basis + deviation
bbr_lower = bbr_basis - deviation
bbr = (close - bbr_lower)/(bbr_upper - bbr_lower)
bbr_color = bbr[1]>1 ? #ff0062 : bbr[1]<0 ? #00ff64 : #00c3ff



// ===  ATR ===
// ==================================================
atrlen = input(14, "ATR Period")
atr_mult = input(1.8, "ATR Mult", step = 0.1) // 1.15
atr_stop = input(0.0003, "ATR Stop", step = 0.0001) // 0.001
atr_slen = wma(tr(true), atrlen)
atr_upper = atr_slen * atr_mult + close
atr_lower = close - atr_slen * atr_mult

atr_avg_s = sma(atr_upper,atrlen)
plot(atr_avg_s,"ATR AVG - Short",color=#55d51a)
plot(angle(atr_avg_s,10),"ATR AVG - Short")
atr_avg_b = sma(atr_lower,atrlen)
plot(atr_avg_b,"ATR AVG - Short",color=#55d51a)
plot(angle(atr_avg_b,10),"ATR AVG - Short")



// ===  Kijun ===
// ==================================================
middleDonchian(Length) =>
    lower = lowest(Length)
    upper = highest(Length)
    avg(upper, lower)

basePeriods = input(14, minval=1) //26
kijun =  middleDonchian(basePeriods)
k_angle = angle(kijun,3)


// ===  SSL ===
// ==================================================
ssl_len = input(60, minval=1,title="SSL Len") //75
ssl_len2 = input(45, minval=1,title="SSL 2")
ssl_len3 = input(8, minval=1,title="SSL 3") //7
SSL = wma(2*wma(close, ssl_len/2)-wma(close, ssl_len), round(sqrt(ssl_len)))
SSL2 = wma(2*wma(close, ssl_len2/2)-wma(close, ssl_len2), round(sqrt(ssl_len2)))
SSL3 = wma(2*wma(close, ssl_len3/2)-wma(close, ssl_len3), round(sqrt(ssl_len3)))
//ssl_angle = angle(SSL,3)
ssl_angle = angle(SSL,2)
ssl_angle2 = angle(SSL2,2)
ssl_angle3 = angle(SSL3,2)
ssl_color = ssl_angle2 > 0 ? blue : red


// ===  BB ===
// ==================================================
var float bb_squeeze = 0.00
var float bb_diff = 0.00
BB_length = input(43, minval=1, maxval=150) // 45
BB_stdDev = input(2, minval=2.0, maxval=3)
sqz_length = input(100, minval=2, maxval=200) // 100
sqz_threshold = 76 
bb_s = kijun
basis = sma(close, BB_length)
dev = BB_stdDev * stdev(bb_s, BB_length)
bb_upper = basis + dev
bb_lower = basis - dev
bb_spread = bb_upper - bb_lower
avgspread = sma(bb_spread, sqz_length)
bb_squeeze := bb_spread / avgspread * 100
diffspread = sma(bb_spread, 14)
bb_diff := bb_spread / diffspread * 100
basis_angle = angle(basis,3)
bbu_angle = angle(bb_upper,3)
bbl_angle = angle(bb_lower,3)
bbu_diff_ema = abs(bb_upper-ema_200)
bbl_diff_ema = abs(bb_lower-ema_200)
// BB Zones
bb_zone = bb_squeeze < 53 ? 0 : 
 bb_squeeze < sqz_threshold ? 1 : 
 bb_squeeze < 120 ? 2 :
 bb_squeeze < 181 ? 3 :
 bb_squeeze > 181 ? 4 : na
sqz_color = bb_zone == 0 ? #0045b3 :
 bb_zone == 1 ? #ff0062 : 
 bb_zone == 2 ?  gray : 
 bb_zone == 3 ?  #00c3ff : 
 bb_zone == 4 ? white: na

bb_zones_color =  sqz_color


// === Basis to ema 200 distance ===
// ==================================================
be_high = 50
be_up = 10
be_down = -10
be_low = -50

be_dist = (basis - ema_200) * 100
be_angle = angle(be_dist,2)
be_color = be_dist>0?color.green  : color.red



// === MACD Crossover === 
// ==================================================
fastLength = input(8, minval=1) // 8 //8
slowLength = input(25,minval=1) // 16 // 21
signalLength=input(9,minval=1) // 11 // 5
fastMA = ema(close, fastLength)
slowMA = ema(close, slowLength)
macd_c = fastMA - slowMA
sig_c = sma(macd_c, signalLength)
pos = 0
pos := iff(sig_c < macd_c , 1,iff(sig_c > macd_c, -1, nz(pos[1], 0))) 
mc_color = pos == -1 ? red: pos == 1 ? green : blue
mc_angle = angle(macd_c,2)
s_angle = angle(sig_c,2)
mc_diff = macd_c / sig_c

//mcd_line = -0.000910 //-0.00105
currency = syminfo.currency
mch_line = 0.00420
mcbyp_line = 0.00364
mcu_line = syminfo.currency == 'NZD' ? 0.00170 : syminfo.currency == 'JPY' ? 0.395 : 0.12
mcm_line =-0.000820
mcd_line = syminfo.currency == 'NZD' ? -0.00170 : -0.395
mcl_line = -0.00420


// === Breakout/Consolidation Filter ===
// ==================================================
// ATRMultiple = input(4.0)
// BreakoutLength = input(6)
// Lookback = input(7)
// ATRPeriod = input(14)
// bc_line = 50//input(50) //27
// trueRange = atr(ATRPeriod)

// xColor = yellow
// for i = Lookback to 0
//     if ((close[i] - open[i+BreakoutLength]) > trueRange[i] * ATRMultiple)
//         xColor := green
//     if ( (open[i+BreakoutLength] - close[i]) > trueRange[i]*ATRMultiple)
//         xColor := red
// bc = percentrank(trueRange, 100)
// bc_angle = angle(bc,2)
// plot(bc,title="Breakout",style=plot.style_circles)
// plot(bc_angle,title="BC Angle",style=plot.style_circles)


// === WAE ===
// ==================================================
sensitivity = 150
wae_fast= 20 //8
wae_slow= 40 // 50
channelLength= 20 // 45
wae_mult = 2.0//1.85
var float wae_columns = 0.000
wae_dz = nz(rma(tr(true),100)) * 3.7
calc_macd(source, wae_fast, wae_slow) =>
	a = ema(source, wae_fast)
	b = ema(source, wae_slow)
	a - b
calc_BBUpper(source, length, wae_mult) => 
	wae_basis = wma(source, length)
	wae_dev = wae_mult * stdev(source, length)
	t = wae_basis + wae_dev
	[t]
calc_BBLower(source, length, wae_mult) => 
	wae_basis = wma(source, length)
	wae_dev = wae_mult * stdev(source, length)
	t = wae_basis - wae_dev
	[t]

t1 = (calc_macd(close, wae_fast, wae_slow) - calc_macd(close[1], wae_fast, wae_slow))*sensitivity * 100
t2 = (calc_macd(close[2], wae_fast, wae_slow) - calc_macd(close[3], wae_fast, wae_slow))*sensitivity * 100
[e1a] = calc_BBUpper(close, channelLength, wae_mult)
[e1b] = calc_BBLower(close, channelLength, wae_mult)
wae_line = (e1a - e1b)
wae_diff = (wae_line - wae_dz) * 100
//wae_perc = (wae_line / wae_dz) * 100
trendUp = (t1 >= 0) ? t1 : 0
trendDown = (t1 < 0) ? (-1*t1) : 0
wae_columns := abs(t1/100)
wae_color = #000000
wae_angle = angle(wae_line,2)
if t1 >= 0
	wae_color := trendUp<trendUp[1] ? lime : green
if t1 < 0
	wae_color := trendDown<trendDown[1] ? orange : red


// === ADX + DI with SMA ===
// ==================================================
adx_len = input(title="Adx Length",defval=13)
adx_line = 20// input(title="threshold", type=integer, defval=20)
adx_avg = 10// input(title="SMA", type=integer, defval=10)
var float adx_high = 40
var float adx_mid = 34
var float adx_center = 20
var float adx_low = 12
var float smooth_tr = 0
var float smooth_di_plus = 0
var float smooth_di_minus = 0
TrueRange = max(max(high-low, abs(high-nz(close[1]))), abs(low-nz(close[1])))
DI_plus = high-nz(high[1]) > nz(low[1])-low ? max(high-nz(high[1]), 0): 0
DI_minus = nz(low[1])-low > high-nz(high[1]) ? max(nz(low[1])-low, 0): 0
smooth_tr := nz(smooth_tr[1]) - (nz(smooth_tr[1])/adx_len) + TrueRange
smooth_di_plus := nz(smooth_di_plus[1]) - (nz(smooth_di_plus[1])/adx_len) + DI_plus
smooth_di_minus := nz(smooth_di_minus[1]) - (nz(smooth_di_minus[1])/adx_len) + DI_minus

di_plus = smooth_di_plus / smooth_tr * 100
di_minus = smooth_di_minus / smooth_tr * 100
DX = abs(di_plus-di_minus) / (di_plus+di_minus)*100
adx = sma(DX, adx_len)
adx_sma = sma(adx, adx_avg)
adx_angle = (angle(adx,2))



// === Stochastics Momentum Index ===
// ==================================================
stc_mom_a = 10 //input(10, "Percent K Length")
stc_mom_b = 3//input(3, "Percent D Length")
// Range Calculation
ll = lowest (low, stc_mom_a)
hh = highest (high, stc_mom_a)
diff = hh - ll
rdiff = close - (hh+ll)/2
avgrel = ema(ema(rdiff,stc_mom_b),stc_mom_b)
avgdiff = ema(ema(diff,stc_mom_b),stc_mom_b)
// SMI calculations
SMI = avgdiff != 0 ? (avgrel/(avgdiff/2)*100) : 0
mom_sig = ema(SMI,stc_mom_b)
mom_ema = ema(SMI, 10)
mom_angle = angle(mom_ema,2)
mom_high = 40
mom_low = -40



// === MACD Dema === 
// ==================================================
var float dema_histo = 0
dema_sma = input(12,title='DEMA Short') // 12
dema_lma = input(26,title='DEMA Long') // 26
dema_signal_in = input(9,title='Signal') // 7 // 9 
dema_high= 27//input(27,title='Dema high')
dema_mid= 11//input(11,title='Dema mid')
dema_down = -10//input(-10,title='Dema down') 
dema_low = -24//input(-24,title='Dema Low') 
dema_mult = 100 // GBP/USD 10000

MMEslowa = ema(close,dema_lma)
MMEslowb = ema(MMEslowa,dema_lma)
DEMAslow = ((2 * MMEslowa) - MMEslowb )
MMEfasta = ema(close,dema_sma)
MMEfastb = ema(MMEfasta,dema_sma)
DEMAfast = ((2 * MMEfasta) - MMEfastb)
dema_line = (DEMAfast - DEMAslow)*dema_mult
MMEsignala = ema(dema_line, dema_signal_in)
MMEsignalb = ema(MMEsignala, dema_signal_in)
dema_sig = ((2 * MMEsignala) - MMEsignalb )
dema_histo := (dema_line - dema_sig)
dema_color = dema_histo>0?green:red



// === Schaff Trend Cycle (STC) ===
// ==================================================
stc_signal = 12 // input(12,"Length")
stc_fast = 26 //input(26,"FastL/ength")
stc_slow = 55 //input(50,"SlowLength")
stc_line = 50 //(stc_high + stc_low) * 0.5
stc_up = 90
stc_high = 99
stc_down = 12
stc_low = 2
stc_macd(BBB, stc_fast, stc_slow) =>
    stc_fastMA = ema(BBB, stc_fast)
    stc_slowMA = ema(BBB, stc_slow)
    stc_macd = stc_fastMA - stc_slowMA
    stc_macd
    
AAAAA(stc_signal, stc_fast, stc_slow) => 
    AAA= 0.203 //input(0.203,"STC Sensitivity") // 0.25
    var CCCCC = 0.0
    var DDD = 0.0
    var DDDDDD = 0.0
    var EEEEE = 0.0
    tmp_macd = stc_macd(close,stc_fast,stc_slow)     
    lowest = lowest(tmp_macd, stc_signal)
    highest = highest(tmp_macd, stc_signal) - lowest    
    CCCCC := (highest > 0 ? ((tmp_macd - lowest) / highest) * 100 : nz(CCCCC[1])) 
    DDD := (na(DDD[1]) ? CCCCC : DDD[1] + (AAA * (CCCCC - DDD[1]))) 
    DDDD = lowest(DDD, stc_signal) 
    DDDDD = highest(DDD, stc_signal) - DDDD     
    DDDDDD := (DDDDD > 0 ? ((DDD - DDDD) / DDDDD) * 100 : nz(DDDDDD[1])) 
    EEEEE := (na(EEEEE[1]) ? DDDDDD : EEEEE[1] + (AAA * (DDDDDD - EEEEE[1])))
    EEEEE

stc = AAAAA(stc_signal,stc_fast,stc_slow)
stc_color = stc > stc[1] ? buy_color : sell_color


// === RSI Chart Bars ===
// ==================================================
var rsi_color = #000000
rsi_len = 14 //input(14, minval=1, title="Length")
rsi_up = rma(max(change(close), 0), rsi_len)
rsi_down = rma(-min(change(close), 0), rsi_len)
rsi = rsi_down == 0 ? 100 : rsi_up == 0 ? 0 : 100 - (100 / (1 + rsi_up / rsi_down))
isup() => rsi > 70
isdown() => rsi < 30
// plot(rsi,title="RSI",color=color.blue)
rsi_color := isup() ? color.new(buy_color, 10) : isdown() ? color.new(#ff0000, 10)  : na
barcolor(rsi_color, title="Rsi Candles")




// === Candlesticks ===
// ==================================================
// Hammer
h =(((high - low)>3*(open -close)) and  ((close - low)/(.001 + high - low) > 0.6) and ((open - low)/(.001 + high - low) > 0.6))
//plotshape( show_candles?h:na, title= "Hammer", location=location.belowbar, color=white,style=shape.diamond, text="H")
// Inverted Hammer
ih =(((high - low)>3*(open -close)) and  ((high - close)/(.001 + high - low) > 0.6) and ((high - open)/(.001 + high - low) > 0.6))
//plotshape( show_candles?ih:na, title= "Inverted Hammer", location=location.belowbar, color=white,style=shape.diamond, text="IH")
// Bearish Harami
bear_h=(close[1] > open[1] and open > close and open <= close[1] and open[1] <= close and open - close < close[1] - open[1] )
//plotshape( show_candles?bear_h:na, title= "Bearish Harami",  color=red, style=shape.arrowdown, text="BH")
// Bullish Harami
bh=(open[1] > close[1] and close > open and close <= open[1] and close[1] <= open and close - open < open[1] - close[1] )
//plotshape( show_candles?bh:na,  title= "Bullish Harami", location=location.belowbar, color=buy_color, style=shape.arrowup, text="BH")


// === Plot === 
// ==================================================
plot(bbr, color=bbr_color, title="BBR",style=plot.style_circles)
plot(be_dist, color=be_color, title="Basis to ema distance",style=plot.style_circles)
plot(adx, color=yellow, title="ADX",style=plot.style_circles)
plot(adx_sma, color=white, title="ADX SMA",style=plot.style_circles)
plot(di_plus, color=green, title="DI+",style=plot.style_circles)
plot(di_minus, color=red, title="DI-",style=plot.style_circles)
plot(mom_ema, title="Mom EMA", style=plot.style_circles, color=yellow)
plot(mom_sig, title="Mom SMA", style=plot.style_circles, color=red)
plot(dema_line, title="Dema Line", style=plot.style_circles, color=green)
plot(dema_sig, title="Dema Sig", style=plot.style_circles, color=red)
plot(dema_histo, title="Dema Histo", style=plot.style_circles, color=dema_color)
// Angles
//plot(wae_angle, color= wae_color , title="WAE Angle",style=plot.style_circles)
//plot(bb_diff, color= white , title="Basis Diff",style=plot.style_circles)
plot(ema_200_angle, color= white , title="Ema 200 angle",style=plot.style_circles)

plot(basis_angle, color= white , title="Basis angle",style=plot.style_circles)
plot(bbu_angle, color= white , title="BB Upper Angle",style=plot.style_circles)
plot(bbl_angle, color= white , title="BB Lower Angle",style=plot.style_circles)
plot(ssl_angle, color=white , title="SSL angle",style=plot.style_circles)
plot(ssl_angle2, color= white , title="SSL2 angle",style=plot.style_circles)
plot(ssl_angle3, color= white , title="SSL3 angle",style=plot.style_circles)
plot(adx_angle, color=white, title="ADX Angle",style=plot.style_circles)
//plot(mom_angle, title="Angle Stoch_MTM", style=plot.style_circles, color=white)
//plot(mc_angle, color=yellow, title="MACD Angle",style=plot.style_circles)
//plot(s_angle, color=yellow, title="Signal Angle",style=plot.style_circles)



// plot( show_plots ? wae_line:na,title="Explosion Line",style=plot.style_circles,color=wae_color)
// plot( show_plots ? wae_dz:na,title="Dead Zone",style=plot.style_circles)
// plot( show_plots ? wae_angle:na,title="WAE Angle",style=plot.style_circles)
// Kijun
plot(kijun, color=red, title="Kijun",linewidth=2)
// SSL
plot(SSL, color=white , title="SSL")
plot(SSL2, color=ssl_color , title="SSL 2")
plot(SSL3, color= green , title="SSL 3")
// BB

plot(bbu_diff_ema, title="Diff BBU ema 200", color=bb_zones_color,linewidth=3)
plot(basis, title="Basis", color=bb_zones_color,linewidth=3)
p1 = plot(show_bb ? bb_upper : na, "BB Upper ",color=bb_zones_color,linewidth=2)
p2 = plot(show_bb ? bb_lower : na, "BB Lower ",color=bb_zones_color,linewidth=2)

// EMA 200 bands large
plot(ema_200, "EMA 200", color=color.new(yellow,50),linewidth=2 )

// ATR
//plot(show_plots ? atr_upper : na, "+ATR Upper", color=color.new(#ffffff,85))
//plot(show_plots ? atr_lower : na, "-ATR Lower", color=color.new(#ffffff,85))
plot(show_atr ? atr_upper + atr_stop : na, "+ATR stop", color=color.new(#ffff00,85))
plot(show_atr ? atr_lower - atr_stop : na, "-ATR stop", color=color.new(#ffff00,85))
//barcolor(pos == -1 and use_barcolor? #ff0000: pos == 1 and use_barcolor ? #00a000 : na)

var bool enter_exit = false
//var int trade_dir = 0
var int state = 0
var int bar_num = 0
var float lastPrice = 0.0
var mult_diff = 10000
var int num_pips = 20
var int num_bars = 3

entry_signal() =>
	candle = close > open ? 1 : 0
    dir = 0
    counter = 0
    allow = false


    // Uptrend
    if bb_lower>ema_200

        // Strong uptrend 1
        if candle==1 and close>bb_upper and
         di_plus>adx_high
         and not(adx>adx_mid and adx<adx_high)
         and not(mom_ema>mom_sig)
         and not(bb_zone==4 and kijun>SSL)
            dir := -1

        // condition 5
        if candle==1 and close>bb_upper and
         adx_sma>adx_high and di_minus<adx_low
            dir :=-1
        
        // zone 3 - end of uptrend
        if bb_zone==3 and high<bb_upper and atr_upper>bb_upper and
         dema_sig>dema_line and close>kijun
            dir := -1
            counter := 1

        // be_dist + bbr + di_plus
        // if candle==1 and close>bb_upper and 
        //  be_dist>be_high and be_angle>0 and bbr>1 and di_plus>adx_mid 
        //     dir := -1


            

        // bear harami
        // if bb_lower>ema_200 and bear_h==1 and di_plus>adx_mid and adx>adx_center
        //  and not(adx<adx_sma)
        //  and not(SSL<basis)
        //  and not(bb_zone<3 and stc<stc_high)
        //     dir := -1
        //     counter := 1


        // == Counter == 

        // Zone 0
        if bb_zone==0 and candle==0 and close<bb_lower and
         (
         (atr_lower<ema_200 and adx_sma<adx_low) or
         (di_minus>adx_mid and di_plus<adx_low)
         )
            dir :=1
            counter :=1

        // condition 4
        if candle==0 and close<bb_lower and
         di_minus>adx_high and di_plus<adx_low and bbr<0
            dir := 1
            counter := 1

        // ATR lower than ema 200  
        if candle==0 and close<bb_lower and low>ema_200 and atr_lower<ema_200 and
         close<SSL3  and bbl_angle>0
         //and not(di_minus>adx_mid and adx<adx_mid)
         and not(dema_line<dema_low or dema_sig<dema_low)
         and not(bbl_angle<1 and dema_sig>dema_down)
         and not(SSL>basis and di_minus>adx_mid )
         and not(close>SSL3)
         and not(bb_zone==2)
         //and not(dema_sig<dema_down and adx<di_minus)
            dir :=1
            counter := 1

        // ATR lower than ema 200  
        if candle==0 and close<bb_lower and low>ema_200 and 
         atr_lower<ema_200 and bbl_angle>0
         and not(bb_zone==2)
            dir :=1
            counter := 1

        // low lower than ema 200
        if low<ema_200 and adx_sma>adx_center and wae_color==red 
         and not(SSL>basis and bb_zone<3)
         and not(SSL>basis)
         //and not(high<ema_200)
         //and not(bb_zone==2)
         and not(bb_zone==3 and basis_angle<-2)
            dir :=1
            counter := 1

        if dir == 1 and bbl_angle<0
            dir :=0
            counter :=0


    // BB lower less than ema and bb upper more than ema
    if bb_lower<ema_200 and bb_upper>ema_200

        // == Uptrend ==

        // Sell - Strong uptrend
        if basis>ema_200 and candle==1 and close>bb_upper and
         di_plus>adx_high and wae_color==green
         and not(adx>adx_mid and adx<adx_high)
         and not(mom_ema>mom_sig)
         and not(bb_zone==4 and kijun>SSL)
         and not(kijun>SSL2)
            dir := -1

        // Sell - Strong uptrend after a strong downtrend - Jun 21 21
        if basis_angle>7 and candle==1 and close>SSL3 and bbu_angle>10 and
         adx_sma>adx_high and adx_sma>adx and di_minus<adx_low
         and not(bb_zone<3)
         and not(di_plus>adx_high and adx<adx_sma)
         and not(mom_sig<mom_high)
            dir := -1
            counter := 1

        // zone 3 - end of uptrend
        if bb_zone>2 and high<bb_upper and atr_upper>bb_upper and
         dema_sig>dema_line
            dir := -1
            counter := 1

        // Counter uptrend

        // zone 4 - uptrend
        if bb_zone==4 and candle==0 and low<basis and close>basis and SSL>SSL2 and
         SSL2>kijun
            dir := 1
            counter := 1

        // under ema_200 
        if candle==0 and atr_lower<bb_lower and bbl_angle>7 and
         be_dist>-8.0 and adx_sma<di_minus
            dir := 1
            counter := 1 


        // == Downtrend ==

        // Buy - Strong downtrend 1
        if candle==0 and close<bb_lower and close<SSL3 and
         di_minus>adx_high and di_plus<adx_low//and adx>di_minus
         and not(adx>adx_mid and adx<adx_high)
            dir := 1
            
        // Buy - adx_high di_plus low
        if candle==0 and close<bb_lower and close<SSL3 and 
         di_minus>adx_mid and adx>adx_high and adx_sma>adx_center
            dir := 1
            
        // WAE Filter
        if wae_color!=red or wae_line<wae_dz  
            dir := 0  

        // zone 1
        if bb_zone==1 and close<bb_lower and 
         di_minus>adx_mid and di_plus[1]<adx_low
            dir := 1

        // lower angle > 1
        // if bbl_angle>1 and candle==0 and atr_lower<bb_lower and
        //  di_minus>20 and close<SSL3 and wae_color == red
        //  and not(adx<adx_low or adx<di_plus)
        //  and not(di_minus<adx)
        //  and not(bb_zone==3)
        //     dir := 1
        //     counter := 1

        // RSI filter - zone 4
        if use_rsi and isdown() and (bb_zone==2 or bb_zone==4) and dir==1 and rsi[3] > 30
            dir := 0
            counter := 0


        // == Counter Downtrend

        if candle==1 and bbu_angle<-1 and adx_sma>adx_high and adx<adx_high and
         di_minus<adx_mid and di_plus>20 and di_minus>di_plus
            dir := -1
            counter := 1

        // just above basis
        if candle == 1 and close>basis and basis_angle<-3 and
         bbu_angle<-12 and di_plus>24 and adx_sma<adx_center and adx_sma<adx_sma[1]
            dir := -1
            counter := 1 

        if candle==1 and high<basis and atr_upper<basis and
         bbr[1]<0 and close>kijun
            dir :=-1
            counter:=1


    if bb_upper<ema_200 
        
        // Extreme - zone lower than 3
        if bb_zone>2 and candle==0 and close<bb_lower and close<SSL3 and
         di_minus>adx_high and di_plus<adx_low and adx>di_minus
            dir :=1

        // Buy - Strong downtrend 2
        if candle==0 and close<bb_lower and close<SSL3 and
         di_minus>adx_mid and di_plus<adx_low and adx>adx_high and
         mom_sig<mom_low
         and not(di_minus>adx_mid and adx<adx_high)
         and not(bb_zone==4)
            dir := 1

        // lower angle > 1
        if bb_zone<3 and bbl_angle>1 and candle==0 and close<SSL3 and
         atr_lower<bb_lower and dema_sig<0
         and not(adx<adx_low or adx<di_plus)
            dir := 1
            counter := 1

        // zone < 2
        if bb_zone<2 and candle==0 and close<bb_lower
         and bbr<0 and isdown()
            dir := 1
            counter :=1

        // be_dist Filter
        if bb_zone<2 and (be_dist>be_low or be_dist>0)
            dir := 0
            counter :=0


        // == Counter
        // New
        if bb_zone<3 and candle == 1 and close>bb_upper and 
         di_plus>adx_mid and bbr>1 and adx_sma>adx_center
            dir := -1
            counter :=1

        if candle==1 and high>bb_upper and bbu_angle<-2 and basis_angle<-2
            dir := -1
            counter := 1

        if candle == 1 and atr_upper>ema_200 and bbu_angle<-10
            dir := -1
            counter :=1


    if basis_angle<4 or basis_angle>-4

        // Sell - BBR + RSI
        if bb_upper>ema_200 and bb_zone<3 and candle==1 and bbl_angle>-1 and
         ( (bbr>1 and isup() and SSL2>ema_200 and SSL>basis )  
         or (bbr>1 and di_plus>adx_center and not isup()) ) and
         SSL>basis and mom_sig[1]>mom_high
         and not (bb_lower>ema_200 and bb_zone<2 and be_dist>be_up)
         and not (be_dist>0)
            dir := -1
            counter :=1
        // Buy
        if bb_lower<ema_200 and bb_zone<3 and candle==0 and close<bb_lower and
         bbr<0 and bbu_angle<1 and bbl_angle>-2 and SSL2<ema_200 and mom_sig[1]<mom_low
         and not (bb_upper<ema_200 and bb_zone<2 and be_dist>be_down)
         and not (be_dist>0)
         and not(bb_upper<ema_200 and di_minus<adx_mid)
            dir := 1
            counter :=1

    // Sell - be + bbr
    if candle == 1 and close>SSL3 and high<bb_upper and atr_upper>bb_upper and
     bbu_angle<0 and basis_angle<0 and
     bbr>1 and be_dist<be_high
        dir :=-1
        counter := 1
        
    
    // zone 0 + rsi + bbr
    if bb_zone==0 and candle==1 and close>bb_upper

        if bb_lower>ema_200 and candle==1 and 
         bbr>1 and be_dist<be_high
            dir := -1
        if bb_lower>ema_200 and bbl_angle<2 and isup() and bbr>1
            dir := -1
        if bb_upper>ema_200 and bbl_angle<2 and bb_lower<ema_200 and
         be_dist<be_up and be_angle>be_high and bbr>1
            dir := -1

        // Filter out
        if mom_ema<mom_high
            dir :=0

    if bb_zone==0 and candle==0 and close<bb_lower
        if bb_upper<ema_200 and isdown() and bbr<0
            dir := 1
        if bb_lower>ema_200 and isdown() and bbr<0
            dir := 1

    // Filter out same direction trades, wait 4 candles
    // and price higher than 20 pips before allowing another trade
    // if dir == state and use_logic
    //     allow := (bar_index - bar_num) > num_bars ? true : false
    //     // sell
    //     if dir == -1
    //         dir := ((close - lastPrice) * mult_diff) > num_pips and allow ? dir : 0
    //         //counter := dir!=0 ? 1 : 0
    //     // buy
    //     if dir == 1
    //         dir := ((close - lastPrice) * mult_diff) < (num_pips * -1) and allow ? dir : 0
    //         //counter := dir!=0 ? 1 : 0

    // Filter out high percentage trades
    // if wae_columns<4.7
    //     if counter == 0 and abs(perc_change())>20
    //         dir := 0
    //     if counter == 1 and abs(perc_change())>20
    //         dir := 0

    //var int enter_exit = basis_angle > 0 and dir == -1 ? 0 : basis_angle < 0 and dir
    type = dir > 0 ? 1 : dir < 0 ? -1 : 0
    [type,counter]

[trade_dir,counter] = entry_signal() 

if trade_dir != 0 and use_logic
    state := trade_dir

    //enter_exit := counter == 1 ? 1 : 0
    // Sell
    // if basis_angle > 0 and trade_dir == -1
    //     enter_exit := -1
    // // Counter
    // if basis_angle > 0 and trade_dir == 1
    //     enter_exit := 0
    // // Buy
    // if basis_angle < 0 and trade_dir == 1
    //     enter_exit := -1
    // // Counter
    // if basis_angle < 0 and trade_dir == -1
    //     enter_exit := 0
    // if basis>ema_200
    //     enter_exit := trade_dir == -1 ? false : trade_dir == 1 or counter ? true : false
    // if basis<ema_200
    //     enter_exit := trade_dir == 1 ? false : trade_dir == -1 or counter ? true : false
    //enter_exit := counter == 1 ? true : enter_exit == false ? true : false
    lastPrice := close
    bar_num   := bar_index

// if enter_exit != trade_dir and trade_dir != 0
//     enter_exit := enter_exit == false ? true : false

trade_color = trade_dir > 0  ? buy_color : sell_color
plot(show_lastprice and lastPrice?lastPrice:na,title="Last Price", style=plot.style_circles)
plot(counter,title="Counter Trade", style=plot.style_circles)
//plot(state,"State",style=plot.style_circles)

// Buy
tmp_text = counter==1? "B":"Exit"
plotshape(show_entry and trade_dir == 1 and counter == 1 ? 1 : na, title="Entry Buy", color=trade_color, location = location.belowbar, style=shape.labelup, text="B", textcolor=color.white, size=size.small)
plotshape(show_entry and trade_dir == 1 and counter == 0 ? 1 : na, title="Exit Buy", color=trade_color, location = location.belowbar, style=shape.labelup, text="Exit", textcolor=color.white, size=size.small)
// Sell
plotshape(show_entry and trade_dir == -1 and counter == 1 ? -1: na, title="Entry Sell", color=trade_color, location = location.abovebar, style=shape.labeldown, text="S", textcolor=color.white, size=size.small)
plotshape(show_entry and trade_dir == -1 and counter == 0 ? -1 : na, title="Exit Sell", color=trade_color, location = location.abovebar, style=shape.labeldown, text="Exit", textcolor=color.white, size=size.small)
