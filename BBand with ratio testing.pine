//@version=4
//Created by kapipara180@ , last updated 2018/7/20
study("BBand width ratio testing" )

BB_Period = input(20, title="BBand period")
Deviation = input(2.0, "Deviation")

sko = stdev(close,BB_Period)
BBandwr = 2*(Deviation*sko)/sma(close,BB_Period)*1000 //length from UPband to Downband / SMA
plot(BBandwr, linewidth=3, color= color.red)

//Bollinger Awesome 
fast_ma_period = input(3, title="BBand period")
sko_ma = stdev(close,fast_ma_period)
BBandwr_ma = 2*(Deviation*sko_ma)/ema(close,fast_ma_period)*1000
//fast_ma_len = input(3, title="Fast EMA length", minval=2)
//fast_ma = ema(close, fast_ma_len)
plot(BBandwr_ma, title="Fast EMA", color=color.yellow, transp=10, linewidth=2)

histo = BBandwr - BBandwr_ma
plot(BBandwr_ma, title="Fast EMA", color=color.yellow, transp=10, linewidth=2)



