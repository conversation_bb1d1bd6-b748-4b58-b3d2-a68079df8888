//@version=5
indicator(title='SSL channel - Multi', overlay=true, shorttitle='SSL Channel - Multi')

g_channel = 'SSL Channels'
inl_ch = 'inl_ch'
ch_len = input.int(title='Period', defval=75, inline=inl_ch, group=g_channel)  // 100, 60, 25
ch_fill = input.bool(title='Show Fill', defval=true, inline=inl_ch, group=g_channel)
ch1_angles = input.bool(title='Color based on angle', defval=false, inline=inl_ch, group=g_channel)
f_trans = 85  //input(85,title="Fill Transparency", type=input.integer,group=g_channel)
channel_line_width = 1  // input(title="Show Fill",type=input.integer, defval=1)
//show_only_lines = input(title="Only show lines",type=input.bool,defval=true,inline=inl_ch)

g_ch_time = ''
use_ch_curr = input.bool(title='Show Current', defval=true, inline=inl_ch, group=g_ch_time)
use_ch_multi = input.bool(title='Show Multi', defval=true, inline=inl_ch, group=g_ch_time)
ch_time = input.timeframe(title='Timeframe', defval='30', inline=inl_ch, group=g_ch_time)

red = #ff0062
aqua = #00bcd4
yellow = #FFFF00
orange = #ff9800
green = #4caf50
lime = #00E676
white = #ffffff
blue = #42a5f5
gray = #707070

c1_a = lime  // input(title="C1 Color 1", type=input.color, defval=#00e676,inline=inl_c)
c1_b = red  // input(title="C1 Color 2", type=input.color, defval=#ff0062,inline=inl_c)
c2_a = lime  //input(title="C2 Color 1", type=input.color, defval=#00e676,inline=inl_c)
c2_b = red  //input(title="C2 Color 2", type=input.color, defval=#ff0062,inline=inl_c)

angle(_src, len) =>
    rad2degree = 180 / 3.14159265359  //pi 
    ang = rad2degree * math.atan((_src[0] - _src[1]) / ta.atr(len))
    ang


ssl_ch1() =>
    smaHigh = ta.sma(high, ch_len)
    smaLow = ta.sma(low, ch_len)
    ch_Hlv = close > smaHigh ? 1 : -1
    //ch_Hlv := close>smaHigh ? 1 : close<smaLow ? -1 : ch_Hlv[1]
    ch1_d = ch_Hlv < 0 ? smaHigh : smaLow
    ch1_u = ch_Hlv < 0 ? smaLow : smaHigh
    ch1_mid = (ch1_d + ch1_u) * 0.5
    ch1_a = angle(ch1_mid, 3)
    [ch1_d, ch1_u, ch1_mid, ch1_a]

[ch1_d, ch1_u, ch1_mid, ch1_a] = ssl_ch1()

// Color
color1 = ch_len < 25 ? c2_a : c1_a
color2 = ch_len < 25 ? c2_b : c1_b
f_c1 = ch1_d > ch1_u and ch_fill ? color.new(color1, f_trans) : ch1_u > ch1_d and ch_fill ? color.new(color2, f_trans) : na


// Plot
ch_sdplot = plot(use_ch_curr ? ch1_d : na, title='SSL Down ', linewidth=channel_line_width, color=color.new(color1, 50))
ch_suplot = plot(use_ch_curr ? ch1_u : na, title='SSL Up', linewidth=channel_line_width, color=color.new(color2, 50))
plot(use_ch_curr ? ch1_mid : na, title='Mid Point', color=color.new(color.white, 80))
plot(use_ch_curr ? ch1_a : na, title='Angle', color=color.new(color.white, 100))
fill(ch_sdplot, ch_suplot, color=f_c1)

// Multi
[ch1_d_m, ch1_u_m, ch1_mid_m, ch1_a_m ] = request.security(syminfo.tickerid, ch_time, [ch1_d, ch1_u, ch1_mid, ch1_a ]  )
// ch1_d_m = request.security(syminfo.tickerid, ch_time, ch1_d)
// ch1_u_m = request.security(syminfo.tickerid, ch_time, ch1_u)
// ch1_mid_m = request.security(syminfo.tickerid, ch_time, ch1_mid)
// ch1_a_m = request.security(syminfo.tickerid, ch_time, ch1_a)

ch_sdplot_m = plot(use_ch_multi ? ch1_d_m : na, title='SSL Down Multi', linewidth=channel_line_width, color=color.new(color1, 50))
ch_suplot_m = plot(use_ch_multi ? ch1_u_m : na, title='SSL Up Multi', linewidth=channel_line_width, color=color.new(color2, 50))
plot(use_ch_multi ? ch1_mid_m : na, title='Mid Point Multi', color=color.new(color.white, 80))
plot(use_ch_multi ? ch1_a_m : na, title='Angle Multi', color=color.new(color.white, 100))


f_c1_m = ch1_d_m > ch1_u_m and ch_fill ? color.new(color1, f_trans) : ch1_u_m > ch1_d_m and ch_fill ? color.new(color2, f_trans) : na
f_c2 = ch1_a > 0 and ch1_a < 1 ? color.new(gray, f_trans) : ch1_a > 1 and ch1_a < 2 ? color.new(yellow, f_trans) : ch1_a > 2 and ch1_a < 4 ? color.new(orange, f_trans) : ch1_a > 4 and ch1_a < 20 ? color.new(red, f_trans) : na
f_c3 = ch1_a < 0 and ch1_a > -1 ? color.new(gray, f_trans) : ch1_a < -1 and ch1_a > -2 ? color.new(aqua, f_trans) : ch1_a < -2 and ch1_a > -4 ? color.new(green, f_trans) : ch1_a < -4 and ch1_a < 20 ? color.new(lime, f_trans) : na

ch_color_m = ch1_angles == false ? f_c1_m : ch1_a > 0 ? f_c2 : f_c3
fill(ch_sdplot_m, ch_suplot_m, color=ch_color_m, transp=90)

// Alerts
ch_sell = ch1_u>ch1_d
ch_buy  = ch1_u<ch1_d
ch_sell_m = ch1_u_m>ch1_d_m
ch_buy_m  = ch1_u_m<ch1_d_m
ch_cond_sell   = ch1_a_m<1 and ch_sell_m
ch_cond_buy   = ch1_a_m>-1 and ch_buy_m
ch_change_m = ta.barssince(ch_sell_m)
plot(ch_change_m, title='Change', color=color.new(blue,100))
plotshape(ch_cond_sell, title="Sell",color=#ff0000,style=shape.circle,location=location.top)
plotshape(ch_cond_buy, title="Buy",color=#00ff00,style=shape.circle,location=location.bottom)



//
// ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ //
// STOP LOSS
// ------------------------------------------------------------------------------------------------------------------
g_sl = 'Stop Loss ----------------------------------------------------'
atr_group   = 'ATR'
show_sl     = input.bool(true,title="Display ATR",group=atr_group)
sl_Multip   = input.float(1.5, title='Stop Loss',group=g_sl) // 4 1.5
atr_len     = input.int(14, title='ATR Length ',group=atr_group)
//i_plot_trades = input.bool(true, title="Display Trades",group=g_sl)
atr_src     = input.string('close', title='Close or Wicks', options=['close', 'wicks'],group=g_sl) // close
sl_min      = input.float(0.07, title='Stop Loss Minimum Pips')
sl_max      = input.float(0.12, title='Stop Loss Maximum Pips')

atr_type    = input.string(title='ATR Type', defval='ema', options=['sma', 'ema', 'zema', 'dema', 'tema', 'wma', 'vwma', 'smma', 'rma', 'hma', 'lsma', 'McGinley'], group=atr_group)
atr_smooth  = input.int(5, title="ATR Smooth", group=atr_group)

ATR = ta.atr(atr_len)
var float sl_long = 0.0
var float sl_short = 0.0
sl_long     := (atr_src =='close' ? close : low)  - ATR * sl_Multip 
sl_short    := (atr_src =='close' ? close : high) + ATR * sl_Multip 

atr_upper = ta.sma( ta.ema(sl_short, atr_len), atr_smooth ) //ma_types(atr_len, atr_type, sl_short)
atr_lower = ta.sma( ta.ema(sl_long, atr_len), atr_smooth )  //ma_types(atr_len, atr_type, sl_long)
atr_mid = (atr_lower + atr_upper) * 0.5
atr_mid_a = angle(atr_mid,14)

plot(show_sl ? atr_lower  : na,"ATR Lower ", color=lime )
plot(show_sl ? atr_upper  : na,"ATR Upper ", color=red )
plot(show_sl ? atr_mid  : na,"ATR Mid ", color=atr_mid_a > 0 ? white : gray )
plot(show_sl ? atr_mid_a  : na,"ATR Angle ", color=atr_mid_a > 0 ? color.new(green,100) : color.new(red,100) )
plot(show_sl ? sl_short : na,"ATR - ", color=color.new(red,70) )
plot(show_sl ? sl_long  : na,"ATR + ", color=color.new(green,70) )

