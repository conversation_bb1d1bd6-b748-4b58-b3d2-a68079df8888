//@version=4
study(shorttitle="Basis to ema", title="Basis to ema")

angle(_src, len) =>
    rad2degree=180/3.14159265359  //pi 
    ang=rad2degree*atan((_src[0] - _src[1])/atr(len))

ema_200 = ema(close, 200)

// ===  Kijun ===
// ==================================================
middleDonchian(Length) =>
    lower = lowest(Length)
    upper = highest(Length)
    avg(upper, lower)

basePeriods = input(14, minval=1) //26
kijun =  middleDonchian(basePeriods)

var float bb_squeeze = 0.00
var float bb_diff = 0.00
BB_length = input(43, minval=1, maxval=150) // 45
BB_stdDev = input(2, minval=2.0, maxval=3)
sqz_length = input(100, minval=2, maxval=200) // 100
sqz_threshold = 76 
bb_s = kijun
basis = sma(close, BB_length)
dev = BB_stdDev * stdev(bb_s, BB_length)
bb_upper = basis + dev
bb_lower = basis - dev

be_dist = (basis - ema_200)
be_color = be_dist>0?color.green  : color.red
dist_angle = angle(be_dist,2)
// === PLOTTING ===

plot(be_dist,title="Distance",color=be_color,style=plot.style_columns)
//plot(dist_angle,title="Distance Angle",color=color.new(#000000,0) )
// plot(ema_200,title="Basis",color=color.new(color.yellow,10))
// plot(basis,title="Basis",color=color.new(#ffffff,10))
// plot(bb_upper,title="Upper Band", color=color.new(#ff0000,10))
// plot(bb_lower,title="Lower Band",color=color.new(#ff0000,10))


