//@version=5
indicator('RSI - Multi New', overlay=true, max_labels_count=500)

g_rsi = 'RSI Wicks -------------------------------------------------------------'
inl_rsi1 = '1'
inl_rsi2 = '2'
show_rsi = input.bool(title='Show RSI', defval=true, inline=inl_rsi1, group=g_rsi)
rsi_strong = input.bool(title='Strongest', defval=false, inline=inl_rsi1, group=g_rsi)
rsi_len1 = input.int(14, minval=1, title='Length', inline=inl_rsi2, group=g_rsi)
rsi_pos = input.string(title='Position', defval='tb', options=['tb', 't', 'b'], inline=inl_rsi2, group=g_rsi)

g_rsi_time = ''
inl_rsi3 = '3'
use_rsi_curr = input.bool(title='Show Current', defval=true, inline=inl_rsi3, group=g_rsi_time)
use_rsi_multi = input.bool(title='Show Multi', defval=true, inline=inl_rsi3, group=g_rsi_time)
rsi_time = input.timeframe(title='Timeframe', defval='60', group=g_rsi_time)

wicks = input(true,  title="Wicks based on stand-alone RSI")
target_up = 70  // input(70, minval=1, title="RSI Up")
target_down = 30  // input(30, minval=1, title="RSI Down")
src_close = close
src_open = open
src_high = high
src_low = low


orange = #ff9800
yellow = #ffee58
violet = #814dff
aqua = #43d3ff
teal = #36fcf6
blue = #0053ff
green = #00ff00
lime = #50ff00

// Change
perc_change(obj) =>
    perc = math.abs((1 - obj[1] / obj) * 10000)
    perc

rsi_wicks(rsi_len) =>
    norm_close = math.avg(src_close, src_close[1])
    gain_loss_close = ta.change(src_close) / norm_close
    RSI_close = 50 + 50 * ta.rma(gain_loss_close, rsi_len) / ta.rma(math.abs(gain_loss_close), rsi_len)

    norm_open = if wicks == true
        math.avg(src_open, src_open[1])
    else
        math.avg(src_close, src_close[1])
    gain_loss_open = ta.change(src_open) / norm_open
    RSI_open = 50 + 50 * ta.rma(gain_loss_open, rsi_len) / ta.rma(math.abs(gain_loss_open), rsi_len)

    norm_high = if wicks == true
        math.avg(src_high, src_high[1])
    else
        math.avg(src_close, src_close[1])
    gain_loss_high = ta.change(src_high) / norm_high
    RSI_high = 50 + 50 * ta.rma(gain_loss_high, rsi_len) / ta.rma(math.abs(gain_loss_high), rsi_len)

    norm_low = if wicks == true
        math.avg(src_low, src_low[1])
    else
        math.avg(src_close, src_close[1])
    gain_loss_low = ta.change(src_low) / norm_low
    RSI_low = 50 + 50 * ta.rma(gain_loss_low, rsi_len) / ta.rma(math.abs(gain_loss_low), rsi_len)

    [RSI_open, RSI_close, RSI_high, RSI_low]



// Multi

[RSI_open, RSI_close, RSI_high, RSI_low] = rsi_wicks(rsi_len1)

var float RSI_open_m = 0.0
var float RSI_high_m = 0.0
var float RSI_low_m = 0.0
var float RSI_close_m = 0.0

close_m = request.security(syminfo.tickerid, rsi_time, close)
open_m = request.security(syminfo.tickerid, rsi_time, open)
RSI_high_m := request.security(syminfo.tickerid, rsi_time, RSI_high[1], lookahead=barmerge.lookahead_on) 
RSI_open_m :=  request.security(syminfo.tickerid, rsi_time, RSI_open[1], lookahead=barmerge.lookahead_on) 
RSI_close_m := request.security(syminfo.tickerid, rsi_time, RSI_close[1], lookahead=barmerge.lookahead_on) 
RSI_low_m := request.security(syminfo.tickerid, rsi_time, RSI_low[1], lookahead=barmerge.lookahead_on) 
plot(show_rsi ? RSI_high_m : na, title='RSI High', color=color.new(yellow, 100))
plot(show_rsi ? RSI_close_m : na, title='RSI Close', color=color.new(green, 100))
plot(show_rsi ? RSI_open_m : na, title='RSI Open', color=color.new(green, 100))
plot(show_rsi ? RSI_low_m : na, title='RSI Low', color=color.new(violet, 100))

rw_change = perc_change(RSI_close) / 100
//plot(rw_change,title='Change',color=color.new(blue,100),style=plot.style_circles)
rsi_color = RSI_close > RSI_close[1] ? #65D25Bff : #FE6B69ff

// SELL
// up_cond = close > open and (rsi_pos == 'tb' or rsi_pos == 't')
// // Strong
// plotshape(show_rsi and use_rsi_curr and RSI_open > target_up and up_cond ? 1 : na, title='Up High Multi', color=color.new(#ff0000, 0), style=shape.circle, location=location.top)
// plotshape(show_rsi and use_rsi_curr and RSI_open > target_up and up_cond ? 1 : na, title='Up High', color=color.new(#ff0000, 0), style=shape.circle, location=location.top)
// // Mid
// plotshape(show_rsi and use_rsi_curr and RSI_close > target_up and RSI_open < target_up and up_cond and rsi_strong == false ? 1 : na, title='Up Mid', color=color.new(orange, 0), style=shape.circle, location=location.top)
// //plot(perc_change(RSI_close)*.01, title="Percent Change", color=color.new(blue,100),style=plot.style_circles)
// // Weak
// plotshape(show_rsi and use_rsi_curr and RSI_close < target_up and RSI_high > target_up and up_cond and rsi_strong == false ? 1 : na, title='Up Weak', color=color.new(yellow, 0), style=shape.circle, location=location.top)

// // BUY
// down_cond = close < open and (rsi_pos == 'tb' or rsi_pos == 'b')
// // Weak
// plotshape(show_rsi and use_rsi_curr and RSI_close > target_down and RSI_low < target_down and down_cond and rsi_strong == false ? 1 : na, title='Low weak', color=color.new(violet, 0), style=shape.circle, location=location.bottom)
// // Mid
// plotshape(show_rsi and use_rsi_curr and RSI_close < target_down and RSI_open > target_down and down_cond and rsi_strong == false ? 1 : na, title='Low Mid', color=color.new(blue, 0), style=shape.circle, location=location.bottom)
// // Strong
// plotshape(show_rsi and use_rsi_curr and RSI_open < target_down and down_cond ? 1 : na, title='Low strong', color=color.new(lime, 0), style=shape.circle, location=location.bottom)



// Multi
// plot(RSI_open_m,title="RSI open",color=color.new(color.blue,100))
// plot(RSI_high_m,title="RSI high",color=color.new(color.blue,100))
// plot(RSI_low_m,title="RSI low",color=color.new(color.blue,100))
// plot(RSI_close_m,title="RSI close",color=color.new(color.blue,100))

// SELL
up_cond_m = (rsi_pos == 'tb' or rsi_pos == 't') 
// Strong
plotshape(show_rsi and use_rsi_multi and RSI_open_m > target_up and up_cond_m ? 1 : na, title='Up High Multi', color=color.new(#ff0000, 0), style=shape.circle, location=location.top)
plotshape(show_rsi and use_rsi_multi and RSI_open_m > target_up and up_cond_m ? 1 : na, title='Up High', color=color.new(#ff0000, 0), style=shape.circle, location=location.top)
// Mid
plotshape(show_rsi and use_rsi_multi and RSI_close_m > target_up and RSI_open_m < target_up and up_cond_m and rsi_strong == false ? 1 : na, title='Up Mid', color=color.new(orange, 0), style=shape.circle, location=location.top)
//plot(perc_change(RSI_close_m)*.01, title="Percent Change", color=color.new(blue,100),style=plot.style_circles)
// Weak
plotshape(show_rsi and use_rsi_multi and RSI_close_m < target_up and RSI_high_m > target_up and up_cond_m and rsi_strong == false ? 1 : na, title='Up Weak', color=color.new(yellow, 0), style=shape.circle, location=location.top)

// BUY
down_cond_m =  (rsi_pos == 'tb' or rsi_pos == 'b')
// Weak
plotshape(show_rsi and use_rsi_multi and RSI_close_m > target_down and RSI_low_m < target_down and down_cond_m and rsi_strong == false ? 1 : na, title='Low weak', color=color.new(violet, 0), style=shape.circle, location=location.bottom)
// Mid
plotshape(show_rsi and use_rsi_multi and RSI_close_m < target_down and RSI_open_m > target_down and down_cond_m and rsi_strong == false ? 1 : na, title='Low Mid', color=color.new(blue, 0), style=shape.circle, location=location.bottom)
// Strong
plotshape(show_rsi and use_rsi_multi and RSI_open_m < target_down and down_cond_m ? 1 : na, title='Low strong', color=color.new(lime, 0), style=shape.circle, location=location.bottom)

