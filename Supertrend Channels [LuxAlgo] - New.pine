// This work is licensed under a Attribution-NonCommercial-ShareAlike 4.0 International (CC BY-NC-SA 4.0) https://creativecommons.org/licenses/by-nc-sa/4.0/
// © LuxAlgo

//@version=5
indicator("Supertrend Channels [LuxAlgo] - Ben",overlay=true,max_lines_count=500)
i_stch_time = input.timeframe('60', "Resolution") // Daily
length = input.int(18) // 18, 14
mult   = input.int(5)  //  5,  2
i_stch_trans = input.int(80, 'Transp')
i_heikin = input.bool(false, title='Use Heikin Ashi')


//------------------------------------------------------------------------------
f_st_channels()=>
    //timeframe.change(i_stch_time) ? 
    upper = 0.,lower = 0.,os = 0,max = 0.,min = 0.

    src = close
    atr = ta.atr(length)*mult
    up = hl2 + atr
    dn = hl2 - atr
    upper := src[1] < upper[1] ? math.min(up,upper[1]) : up
    lower := src[1] > lower[1] ? math.max(dn,lower[1]) : dn

    os := src > upper ? 1 : src < lower ? 0 : os[1]
    spt = os == 1 ? lower : upper

    max := ta.cross(src,spt) ? nz(math.max(max[1],src),src) : 
     os == 1 ? math.max(src,max[1]) : 
     math.min(spt,max[1])

    min := ta.cross(src,spt) ? nz(math.min(min[1],src),src) : 
     os == 0 ? math.min(src,min[1]) : 
     math.max(spt,min[1])

    avg = math.avg(max,min)
    avg_high = math.avg(max,avg)
    avg_low = math.avg(avg,min)

    [max, min, os, avg, avg_high, avg_low]

[max, min, os, avg, avg_high, avg_low] = request.security(syminfo.tickerid, i_stch_time, f_st_channels() )
[max_h, min_h, os_h, avg_h, avg_high_h, avg_low_h] = request.security(ticker.heikinashi(syminfo.tickerid), i_stch_time, f_st_channels() )

//------------------------------------------------------------------------------
var area_up_col  = color.new(#ff1100,i_stch_trans)
var area_avg_col = color.new(#ffffff , (i_stch_trans + 5) )
var area_dn_col = color.new(#0cb51a ,i_stch_trans)

c_max = i_heikin ? max_h : max 
c_min = i_heikin ? min_h : min   
c_os  = i_heikin ? os_h : os 
c_avg = i_heikin ? avg_h : avg
c_avg_high = i_heikin ? avg_high_h : avg_high
c_avg_low = i_heikin ? avg_low_h : avg_low   

p_max = plot(c_max,'Upper Channel',c_max != c_max[1] and c_os == 1 ? na : area_up_col)
p_avg = plot(c_avg,'Average',area_avg_col)
p_min = plot(c_min,'Lower Channel',c_min != c_min[1] and c_os == 0 ? na : area_dn_col)
plot(c_avg_high,'Avg High',area_up_col)
plot(c_avg_low,'Avg Low',area_dn_col)

fill(p_max,p_avg,area_up_col,'Upper Area')
fill(p_avg,p_min,area_dn_col,'Lower Area')