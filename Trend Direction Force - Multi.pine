//@version=4
study("Trend Direction Force Multi", shorttitle = "TDFI Multi")

td_lb = input(10, title = "Lookback") // 25
td_fh = input(0.13, title = "Filter", step=0.01) // 0.05
td_fl =  td_fh * -1 //input(-0.05, title = "Filter Low") 
td_max = input(0.9, title = "Max Line", step=0.1) // 0.8
show_multi = input(title="Show Multi", type=input.bool, defval=true)
resCustom = input(title="Timeframe", type=input.resolution, defval="60")


tdmi() =>
    mma = ema(close * 1000, td_lb)
    smma = ema(mma, td_lb)

    impetmma = mma - mma[1]
    impetsmma= smma - smma[1]
    divma = abs(mma - smma)
    averimpet = (impetmma + impetsmma) / 2

    number = averimpet
    var int pow = 3 
    var float result = na

    for i = 1 to pow - 1
        if i == 1
            result := number
        result := result * number

    tdf = divma * result
    ntdf = tdf / highest(abs(tdf), td_lb * 3)

    ntdf


ntdf_multi = security(syminfo.tickerid, resCustom, tdmi() )
c = ntdf_multi > td_fh ? #008000 : ntdf_multi < td_fl ? #ff0000 : #505050
plot(ntdf_multi, linewidth = 2, color = c)

var int flag_u = 0
var int flag_d = 0
// M2 angle went above 20 and the first lower angled close == Set Flag up to true
// Flag up is still true but hasn't gone below 0 line yet == Flag up is true
// Fflag up is still true but now has gone below 0 line 
flag_u := m2_a>20 and m2_a>m2_a[1] ? 1 : flag_u==1 and m2_a<m2_a[1] ? 0 : flag_u==1 and m2_a>0 ? 1 : na
flag_d := m2_a<-10 and m2_a<m2_a[1] ? -1 : flag_d==-1 and m2_a>m2_a[1] ? 0 : flag_d==-1 and m2_a<0 ? -1 : na


hline(td_max, color = #ffff00)
hline(td_fh, color = #505050)
hline(td_fl, color = #505050)
hline(td_max * -1, color = #ffff00)