//@version=4
study(title = "Kijun sen on Bollinger Bands", shorttitle="Kijun+BB",overlay=true)

show_entry = input(title="Show Entry", type=input.bool, defval=true)
show_atr = input(title="Show ATR", type=input.bool, defval=false)
show_plots = input(title="Show Plots", type=input.bool, defval=false)
show_bb= input(title="Show BB", type=input.bool, defval=true)

red = #ff0062
aqua = #00bcd4
yellow = #FFFF00
orange = #ff9800
green = #4caf50
white = #ffffff
blue = #42a5f5
gray = #707070
sell_color = color.new(#ff0062,20)
buy_color = color.new(#00c3ff,20)

angle(_src, len) =>
    rad2degree=180/3.14159265359  //pi 
    ang=rad2degree*atan((_src[0] - _src[1])/atr(len))

// Change
perc_change() =>
    perc = abs( (1 - (close[1] / close)) * 10000 )


// ===  EMA's ===
// ==================================================
ema_200 = ema(close, 200)
ema_200_dev = 0.27 * stdev(close, 200)
ema_200_upper = ema_200 + ema_200_dev
ema_200_lower = ema_200 - ema_200_dev
ema_200_outer_dev = 0.23 * stdev(close, 200)
ema_200_outer_upper = ema_200_upper + ema_200_outer_dev
ema_200_outer_lower = ema_200_lower - ema_200_outer_dev
ema_len_f = input(5, minval=1, title="EMA Length") //6
ema_fast = ema(close, ema_len_f)
ema_angle = angle(ema_fast,2)


// ===  ATR ===
// ==================================================
atrlen = 14 //input(14, "ATR Period")
atr_mult = input(1.15, "ATR Mult", step = 0.1) // 1.15
atr_stop = input(0.001, "ATR Stop", step = 0.001) // 0.5
atr_slen = wma(tr(true), atrlen)
atr_upper = atr_slen * atr_mult + close
atr_lower = close - atr_slen * atr_mult


// ===  Kijun ===
// ==================================================
middleDonchian(Length) =>
    lower = lowest(Length)
    upper = highest(Length)
    avg(upper, lower)

basePeriods = input(14, minval=1) //26
kijun =  middleDonchian(basePeriods)
k_angle = angle(kijun,3)
xChikou = close
xPrice = close


// ===  SSL ===
// ==================================================
ssl_len = input(60, minval=1,title="SSL Len") //75
ssl_len2 = input(45, minval=1,title="SSL 2")
SSL = wma(2*wma(close, ssl_len/2)-wma(close, ssl_len), round(sqrt(ssl_len)))
SSL2 = wma(2*wma(close, ssl_len2/2)-wma(close, ssl_len2), round(sqrt(ssl_len2)))
SSL3 = wma(2*wma(close, 7/2)-wma(close, 7), round(sqrt(7)))
//ssl_angle = angle(SSL,3)
ssl_angle = angle(SSL2,2)
ssl_angle2 = angle(SSL3,2)

// ===  BB ===
// ==================================================
BB_length = input(45, minval=1, maxval=150) // 23
BB_stdDev = input(2, minval=2.0, maxval=3)
sqz_length = input(100, minval=2, maxval=200) // 100
sqz_threshold = 76 
bb_s = kijun
basis = sma(bb_s, BB_length)
dev = BB_stdDev * stdev(bb_s, BB_length)
bb_upper = basis + dev
bb_lower = basis - dev
bb_spread = bb_upper - bb_lower
avgspread = sma(bb_spread, sqz_length)
bb_squeeze = 0.00
bb_squeeze := bb_spread / avgspread * 100
basis_angle = angle(basis,3)
bbu_angle = angle(bb_upper,3)
bbl_angle = angle(bb_lower,3)
// BB Zones
bb_zone = bb_squeeze < 53 ? 0 : 
 bb_squeeze < sqz_threshold ? 1 : 
 bb_squeeze < 120 ? 2 :
 bb_squeeze < 181 ? 3 :
 bb_squeeze > 181 ? 4 : na
sqz_color = bb_zone == 0 ? #0045b3 :
 bb_zone == 1 ? #ff0062 : 
 bb_zone == 2 ?  gray : 
 bb_zone == 3 ?  #00c3ff : 
 bb_zone == 4 ? white: na

bb_zones_color =  sqz_color


// === MACD Dema === 
// ==================================================
dema_sma = input(7,title='DEMA Short') // 12
dema_lma = input(45,title='DEMA Long') // 26
dema_signal_in = input(13,title='Signal') // 7 // 9 
MMEslowa = ema(close,dema_lma)
MMEslowb = ema(MMEslowa,dema_lma)
DEMAslow = ((2 * MMEslowa) - MMEslowb )
MMEfasta = ema(close,dema_sma)
MMEfastb = ema(MMEfasta,dema_sma)
DEMAfast = ((2 * MMEfasta) - MMEfastb)
dema_line = (DEMAfast - DEMAslow)
MMEsignala = ema(dema_line, dema_signal_in)
MMEsignalb = ema(MMEsignala, dema_signal_in)
dema_signal = ((2 * MMEsignala) - MMEsignalb )
dema_histo = (dema_line - dema_signal)
dema_up = dema_histo > 0 ? 1 : 0
dema_down = dema_histo < 0 ? 1 : 0
dema_angle = angle(dema_signal,3)


// === MACD === 
// ==================================================
fast_length = 12
slow_length = 26
signal_length = 9
sma_source = false
sma_signal = false
// Plot colors
c_up = #26A69A // teal
c_up_weak = #B2DFDB // light teal
c_down = #EF5350 // peach
c_down_weak = #FFCDD2 // pink
col_macd = #0094ff
col_signal = #ff6a00
// Calculating
fast_ma = sma_source ? sma(close, fast_length) : ema(close, fast_length)
slow_ma = sma_source ? sma(close, slow_length) : ema(close, slow_length)
macd = fast_ma - slow_ma
m_signal = sma_signal ? sma(macd, signal_length) : ema(macd, signal_length)
m_hist = macd - m_signal
m_color = (m_hist>=0 ? (m_hist[1] < m_hist ? c_up : c_up_weak) : (m_hist[1] < m_hist ? c_down_weak : c_down) )


// === MACD Crossover === 
// ==================================================
fastLength = input(8, minval=1) // 8
slowLength = input(16,minval=1) // 16
signalLength=input(11,minval=1) // 11
fastMA = ema(close, fastLength)
slowMA = ema(close, slowLength)
macd_c = fastMA - slowMA
signal_c = sma(macd_c, signalLength)
pos = 0
pos := iff(signal_c < macd_c , 1,iff(signal_c > macd_c, -1, nz(pos[1], 0))) 
mc_color = pos == -1 ? red: pos == 1 ? green : blue
m_angle = angle(macd_c,2)
s_angle = angle(signal_c,2)
m_diff = macd_c / signal_c
m_bline = 0.00150
//barcolor(pos == -1 ? red: pos == 1 ? green : blue)



// === Plot === 
// ==================================================
plot(signal_c, color=red, title="SIGNAL Crossover")
plot(macd_c, color=blue, title="MACD Crossover")
plot(m_angle, color=yellow, title="MACD Angle",style=plot.style_circles)
plot(s_angle, color=yellow, title="Signal Angle",style=plot.style_circles)
plot(m_diff, color=yellow, title="MACD Diff",style=plot.style_circles)
// Angles
plot(basis_angle, color= white , title="Basis angle",style=plot.style_circles)
plot(ssl_angle, color= white , title="SSL angle",style=plot.style_circles)
plot(ssl_angle2, color= white , title="SSL angle 2",style=plot.style_circles)
plot(bbu_angle, color= white , title="BB Upper Angle",style=plot.style_circles)
plot(bbl_angle, color= white , title="BB Lower Angle",style=plot.style_circles)
plot(k_angle, color= white , title="Kijun Angle",style=plot.style_circles)
plot(ema_angle, color= white , title="EMA Fast Angle",style=plot.style_circles)

//plot(ssl_angle2, color= white , title="SSL2 angle",style=plot.style_circles)
//plot(dema_angle, color= white , title="Dema angle",style=plot.style_circles)
//plot(show_plots ? dema_histo : na, title="Dema Histo", color=dema_up ? green : red, style=plot.style_circles)

//plot(show_plots ? bb_zone : na, title="BB Zones", color=bb_zones_color, style=plot.style_circles,transp=20)
plot(show_plots ? dema_signal: na, title="Dema Signal", color=bb_zones_color, style=plot.style_circles)
plot(show_plots ? m_hist: na, title="MACD Histogram", style=plot.style_columns, color = m_color )
plot(show_plots ? macd: na, title="MACD", color=col_macd)
plot(show_plots ? m_signal: na, title="MACD Signal", color=col_signal)
// Kijun
plot(kijun, color=red, title="Kijun",linewidth=2)
// SSL
plot(SSL, color=white , title="SSL")
plot(SSL2, color= yellow , title="SSL 2")
plot(SSL3, color= green , title="SSL 3")
// BB
plot(basis, title="Basis", color=bb_zones_color)
p1 = plot(show_bb ? bb_upper : na, "BB Upper ",color=bb_zones_color)
p2 = plot(show_bb ? bb_lower : na, "BB Lower ",color=bb_zones_color)
//fill(p1,p2, bb_zones_color)
// EMA 200 bands large
plot(ema_200, "EMA 200", color=color.new(yellow,80) )
//e200_up = plot(show_plots ? ema_200_upper : na, title="EMA 200 Upper", color=color.new(#ffe676,70))
//e200_down = plot(show_plots ? ema_200_lower : na, title="EMA 200 Lower", color=color.new(#ffe676,70))
plot(show_plots ? ema_fast : na, color=color.lime, title="EMA Fast")
// ATR
plot(show_plots ? atr_upper : na, "+ATR Upper", color=color.new(#ffffff,85))
plot(show_plots ? atr_lower : na, "-ATR Lower", color=color.new(#ffffff,85))
plot(show_atr ? atr_upper + atr_stop : na, "+ATR stop", color=color.new(#ffff00,85))
plot(show_atr ? atr_lower - atr_stop : na, "-ATR stop", color=color.new(#ffff00,85))


entry_signal() =>
	dir = 0
	trading = 0
    k = kijun

	// Type of candle - Long / Short
	candle = close > open ? 1 : 0

	// === Uptrend ===
	// ==================================================
    if candle == 1 and basis > ema_200 

        if open > bb_upper and
         m_diff < 1.3 and m_angle < 1 and signal_c > m_bline and
         mc_color == green and not(bb_zone == 0)
            dir := -1
            trading := 1
            
        if bb_zone == 0
            // Jun 1
            if basis_angle < 1 and ssl_angle > 2 and atr_upper > bb_upper and 
             close > bb_upper and k > SSL and ema_fast > k and
             signal_c > macd_c and signal_c > 0
				dir := -1
				trading := 1
            // May 18
            if basis_angle < 0 and atr_upper > bb_upper and atr_lower > k and
             SSL < k and k < basis
				dir := -1
				trading := 1

                
        if bb_zone == 1
            // May 30
            if basis_angle > 2 and ssl_angle < 0 and high > bb_upper and 
             SSL > ema_fast and atr_lower > k and ema_fast > k
				dir := -1
				trading := 1

            // Jan 28
            // if basis_angle < -1 and ssl_angle > 2 and SSL2 > SSL and
            //  k > SSL2 and open > basis
			// 	dir := -1
			// 	trading := 1

        if bb_zone == 2
            // Sell - May 21
            if basis_angle > 2 and ssl_angle > 2 and SSL > k and
             atr_lower > k and ema_fast < bb_upper and open > bb_upper
				dir := -1
				trading := 1
            // Sell - May 12
            if basis_angle > 2 and ssl_angle < -2 and 
             atr_upper > bb_upper and atr_lower > k and
             close < bb_upper and SSL < bb_upper and SSL > k 
				dir := -1
				trading := 1

        if bb_zone == 3
            // May 20
            if basis_angle > 2 and atr_lower > k and 
             SSL > bb_upper and k < SSL and ema_fast > SSL and 
             macd_c > signal_c and m_angle < 3
				dir := -1
				trading := 1
            // May 19
            if basis_angle > 2 and atr_lower < k and SSL > ema_fast and 
             open > SSL
				dir := -1
				trading := 1
            // Mar 1
            if atr_upper > bb_upper and close < bb_upper and 
             k > SSL and close > ema_fast and signal_c > 0
				dir := -1
				trading := 1
            // Feb 5
            if basis_angle > 2 and ssl_angle < 5 and 
             SSL2 < SSL and SSL < bb_upper and 
             open > SSL and signal_c > 0
				dir := -1
				trading := 1


        if bb_zone == 4
            // Jun 3
            if atr_lower > k and SSL > k and k > bb_upper and
             dema_down == 1 and m_color == c_up_weak
				dir := -1
				trading := 1
            // May 10
            if SSL > bb_upper and ema_fast < bb_upper and
             atr_lower > k and dema_signal < 0
				dir := -1
				trading := 1

            // May 4
            if SSL2 > bb_upper and open > SSL2 and 
             k < bb_upper and k_angle < 4
				dir := -1
				trading := 1

            // Jan 27
            if SSL2 < SSL and k < SSL2 and open > SSL2
				dir := -1
				trading := 1

    // Counter
    if candle == 0 and basis > ema_200 

        if bb_zone == 0

            if high < bb_lower and perc_change()<2 and
             k < SSL2 and k_angle == 0 and atr_upper < bb_lower
                dir := 1
                trading := 1
                
            // May 31
            if basis_angle < -2 and atr_upper < k and
             SSL < bb_lower and k < SSL
				dir := 1
				trading := 1
            // May 18
            if atr_lower < bb_lower and basis_angle < -2 and ssl_angle > 2 and
             k < SSL and k < bb_lower and close < k and close > bb_lower
				dir := 1
				trading := 1
            // // May 18
            if atr_lower < ema_200 and basis_angle > -0.5 and 
             ssl_angle < -2 and k < SSL and k > bb_lower and 
             ema_fast < bb_lower and open < ema_fast and SSL < basis and
             basis > ema_200_upper
				dir := 1
				trading := 1
            // May 18
            if ssl_angle > 1 and k < SSL and signal_c > 0 and
             SSL3 < k and mc_color == red and perc_change() < 5
				dir := 1
				trading := 1
            // Mar 9
            // if atr_lower < ema_200 and basis_angle > 0 and 
            //  ssl_angle < -2 and SSL > basis and ema_fast < bb_lower
			// 	dir := 1
			// 	trading := 1

            // Mar 2
            if high < bb_lower and k_angle < 2 and basis_angle > 0 and
             SSL2 < bb_lower
				dir := 1
				trading := 1

        if bb_zone == 1
            // May 31
            // if basis_angle < -2 and atr_upper < k and
            //  SSL < bb_lower and k < SSL
			// 	dir := 1
			// 	trading := 1
            // May 20
            if basis_angle < 0 and ssl_angle > -4 and atr_upper < bb_lower and
             atr_lower < ema_200 and k < SSL and SSL > bb_lower
				dir := 1
				trading := 1

        if bb_zone == 2
            // Jun 1
            if basis_angle < -2 and ssl_angle < 2 and k < bb_lower and 
             ema_fast < k and open < ema_fast and SSL > bb_lower
				dir := 1
				trading := 1
            // May 14
            if basis_angle < -2 and SSL < k and k < bb_lower and 
             ema_fast < SSL and close < ema_fast and k < ema_200
				dir := 1
				trading := 1
            //  Jan 28
            if basis_angle > 4 and ssl_angle < -10 and atr_lower < bb_lower and
             SSL2 < SSL and k > basis
				dir := 1
				trading := 1

        if bb_zone == 3

            if basis_angle > 2 and ssl_angle > -5 and atr_lower < ema_200_upper and
             open < basis and SSL > k and k > basis and close > ema_200_upper
				dir := 1
				trading := 1
                
            // SPECIAL
            // May 12
            if perc_change() < 3.5 and close > bb_upper and
             SSL2 > SSL and SSL > bb_upper and k > bb_upper and
             atr_lower > bb_upper and dema_histo > 0
				dir := -1
				trading := 1

        //if bb_zone == 4

            // if basis_angle > 2 and atr_lower < basis and k > bb_upper
			// 	dir := 1
			// 	trading := 1

            // // May 24 
            // if k < bb_lower and ema_fast < k and
            //  and SSL < k
			// 	dir := 1
			// 	trading := 1

            // // Feb 24
            // if k < bb_lower and ema_fast < k
            //  and SSL < k
			// 	dir := 1
			// 	trading := 1


	// === Downtrend ===
	// ===== BUY =====================================
    if candle == 0 and basis < ema_200 

        if bb_zone == 0
            // May 27
            if basis_angle > -2 and SSL < bb_lower and
             k < SSL and ema_fast < SSL and open < ema_fast
				dir := 1
				trading := 1
            // Feb 22
            if close < bb_lower and bbl_angle > 0 and
             SSL > bb_lower and k > SSL2
				dir := 1
				trading := 1

        if bb_zone == 1
            // May 7
            if basis_angle > -2 and close < bb_lower and
             k > basis and SSL > k
				dir := 1
				trading := 1

        if bb_zone == 2
            
            // May 27
            if atr_lower < bb_upper and k > bb_upper and SSL > k
             and close < ema_fast and open < ema_200_lower
				dir := 1
				trading := 1

            // May 25
            if atr_upper > ema_fast and k < bb_lower and ssl_angle < -2 and
             open < ema_fast and SSL > bb_lower and basis < ema_200_lower
				dir := 1
				trading := 1

            // Feb 3
            if basis_angle > -3 and k > SSL and SSL2 < SSL
             and open < bb_lower
				dir := 1
				trading := 1
            


        if bb_zone == 3
            // Jun 2
            if basis_angle > 2 and atr_lower < ema_200 and ssl_angle > 5 and
             SSL2 > bb_upper and k < bb_upper and perc_change() > 8
				dir := 1
				trading := 1
            // May 26
            if basis_angle < -2 and k > bb_lower and 
             SSL < bb_lower and open < ema_fast and
             m_color == c_down_weak and dema_histo > 0
				dir := 1
				trading := 1

            // Apr 26
            if basis_angle < -2 and ssl_angle < -2 and k < SSL and 
             SSL < bb_lower and open < ema_fast and
             m_color == c_down_weak and dema_up == 1 and dema_line < 0
				dir := 1
				trading := 1
            // Apr 8
            if close < bb_lower and open > SSL and
             k > bb_lower and ema_fast > SSL
				dir := 1
				trading := 1
            // Mar 15
            if basis_angle < -2 and ssl_angle < -2 and SSL < bb_lower and 
             high > bb_lower and open > SSL and close < bb_lower and
             m_color == c_down and dema_down == 1 and dema_line < 0
				dir := 1
				trading := 1

            // SPECIAL
            // May 17
            if perc_change() < 2.5 and close > bb_upper and
             SSL2 > SSL and SSL > bb_upper and SSL2 < open and k > bb_upper and
             atr_lower > bb_upper
				dir := -1
				trading := 1

        if bb_zone == 4
            // Jun 2
            if basis_angle < -2 and ssl_angle > 2 and
             atr_lower < bb_lower and SSL2 > SSL and
             SSL > bb_lower and k < bb_lower
                dir := 1
                trading := 1

    // === Counter SELL ===
    if candle == 1 and basis < ema_200 

        if bb_zone == 0
            // May 27
            if atr_upper > ema_200 and atr_lower > k and 
             SSL > bb_upper and k > SSL and k > bb_upper and
             ssl_angle < 10
				dir := -1
				trading := 1
            // Apr 4
            if basis_angle > 0 and low > bb_upper and 
             perc_change() < 3 and ssl_angle < 10
				dir := -1
				trading := 1

        if bb_zone == 2
            // Jun 1
            if atr_lower > bb_upper and ssl_angle > 2 and basis_angle > 0 and
             SSL > ema_200 and k > SSL and ema_fast > atr_lower and k < bb_upper and dema_down == 1
				dir := -1
				trading := 1
            // May 27
            if basis_angle < -2 and ssl_angle > 2 and atr_upper > basis and
             k > SSL and ema_fast > k and close < basis and atr_lower < k
				dir := -1
				trading := 1
            // Apr 27
            if basis_angle > 2 and SSL > bb_upper and close > SSL and
             m_hist < 0 and m_color == c_down_weak and dema_signal < 0
				dir := -1
				trading := 1

        if bb_zone == 3
            // May 28
            if basis_angle > 2 and SSL > k and 
             open > bb_upper and ema_fast > SSL and ema_fast < bb_upper
				dir := -1
				trading := 1
            
            // Sell - May 25
            if basis_angle < -2 and ssl_angle < 6 and atr_upper > basis and close < basis and 
             ssl_angle > 2 and k > SSL and dema_down == 1
				dir := -1
				trading := 1

            // Sell - May 17
            if basis_angle < -2 and SSL > bb_upper and k > SSL and atr_lower > k and
             open > ema_fast and ema_fast > k
				dir := -1
				trading := 1
            // Sell - Apr 18 and Feb 26
            if basis_angle > 5 and bbl_angle < -5 and basis_angle > 2 and
             SSL2 > SSL and (SSL > k or (k > SSL and mc_color == red and perc_change()<5  ) )
				dir := -1
				trading := 1

            // ===== Special ========
            // Buy - May 28
            if perc_change() < 2 and basis_angle > 2 and 
             atr_lower < basis and SSL > k and ema_fast < SSL and open < bb_upper
                dir := 1
                trading := 1

    [dir, trading]

[trade_dir, trading] = entry_signal() 

// Buy / Sell indicators
trade_color = trade_dir > 0  ? buy_color : sell_color
plotshape(trade_dir > 0 and show_entry ? 1 : na, title="Entry Buy", color=trade_color, location = location.belowbar, style=shape.labelup, text="B", textcolor=color.white, size=size.small)
plotshape(trade_dir < 0 and show_entry ? -1 : na, title="Entry Sell", color=trade_color, location = location.abovebar, style=shape.labeldown, text="S", textcolor=color.white, size=size.small)
