//@version=4
study(title = "Trading Bot", shorttitle = "Trading Bot", overlay=true, format = format.price, precision = 4)


show_entry = input(title="Show Entry", type=input.bool, defval=true)
show_atr = input(title = "Show Stop Loss / ATR", type = input.bool, defval = false)
show_BB = input(title="Show BB", type=input.bool, defval=true)
use_trend = input(title="Trend Filter", type=input.bool, defval=false)
use_counter = input(title="Use Counter Trend", type=input.bool, defval=true)
use_rsi_candles = input(title="Use RSI Candles", type=input.bool, defval=true)
use_special = input(title="Use Special Candles", type=input.bool, defval=true)
use_high_greater_fast = input(title="Higher than EMA Filter", type=input.bool, defval=true)
use_ema_cross = input(title="Use EMA Cross", type=input.bool, defval=true)
use_stc = input(title="Use STC Filter", type=input.bool, defval=true)
use_cb_stc = input(title="Filter Counter Buy STC", type=input.bool, defval=true)
use_macd_cross = input(title="Use MACD cross", type=input.bool, defval=true)
use_bsm = input(title="Use BSM", type=input.bool, defval=false)
use_doji = input(title="Filter Doji's", type=input.bool, defval=true)
use_hammer = input(title="Filter Hammer's", type=input.bool, defval=true)
use_be = input(title="Filter Bearish Engulfing", type=input.bool, defval=true)
use_deadzone = input(title="Deadzone", type=input.bool, defval=true)
dz_time = input(title="DZ Timeframe", type=input.session, defval= "1715-1900")

// Colors
sell_color = #ff0062
buy_color = #00c3ff
red = #dd1c1c
green = #55aa1a
blue = #0000ff
white = #ffffff
gray  = #707070

angle(_src) =>
    rad2degree=180/3.14159265359  //pi 
    ang=rad2degree*atan((_src[0] - _src[1])/atr(4))

Session(sess) => na(time(timeframe.period, sess)) == false

// Change
perc_change = abs( (1 - (close[1] / close)) * 10000 )
plot(perc_change,title="Change",style=plot.style_circles)


// === Fast EMA ===
// ==================================================
fast_ema = ema(close,3)
//plot(fast_ema, title="Fast EMA", color=color.yellow, transp=15)


// ===  EMA 200 ===
// ==================================================
len_200 = input(200, minval=1, title="EMA 200")
ema_200 = ema(close, len_200)
plot(ema_200, title="EMA 200", color=#fff176, linewidth=3)
//plot((ema_200 - 0.040), title="EMA 200", color=#fff176, linewidth=3,style=plot.style_circles)



// ===  Moving Average Colosell_color EMA SMA ===
// ==================================================
emaplot = true //input (true, title="Show EMA on chart")
len = 8 //input(8, minval=1, title="ema Length") // default 8
smaplot = true //input (true, title="Show SMA on chart")
len2 = 50 //input(50, minval=1, title="sma Length")
src = close
ema_fast = ema(src, len)
up = ema_fast > ema_fast[1]
down = ema_fast < ema_fast[1]
fast_ema_angle = angle(ema_fast)
sma_slow = sma(close, len2)
up2 = sma_slow > sma_slow[1]
down2 = sma_slow < sma_slow[1]
mycolor = up ?  green : down ? red : #00c3ff
mycolor2 = up2 ? green : down2 ? red : blue
plot(ema_fast and emaplot ? ema_fast :na, title="EMA", color=mycolor, linewidth=3)
plot(sma_slow and smaplot ? sma_slow :na , title="SMA", color=mycolor2, linewidth=3, transp=15)
//plot(fast_ema_angle, title="Fast EMA angle", style=plot.style_circles,color=mycolor)
//plot(angle(sma_slow), title="Slow EMA angle", style=plot.style_circles,color=mycolor)



// === Schaff Trend Cycle (STC) ===
// ==================================================
stc_signal = 12 // input(12,"Length")
stc_fast = 26 //input(26,"FastLength")
stc_slow = 50 //input(50,"SlowLength")
stc_low = 25
stc_high = 75
stc_line = stc_high - stc_low
stc_macd(BBB, stc_fast, stc_slow) =>
    stc_fastMA = ema(BBB, stc_fast)
    stc_slowMA = ema(BBB, stc_slow)
    stc_macd = stc_fastMA - stc_slowMA
    stc_macd
    
AAAAA(stc_signal, stc_fast, stc_slow) => 
    AAA=input(0.203,"STC Sensitivity") // 0.25
    var CCCCC = 0.0
    var DDD = 0.0
    var DDDDDD = 0.0
    var EEEEE = 0.0
    tmp_macd = stc_macd(close,stc_fast,stc_slow)     
    lowest = lowest(tmp_macd, stc_signal)
    highest = highest(tmp_macd, stc_signal) - lowest    
    CCCCC := (highest > 0 ? ((tmp_macd - lowest) / highest) * 100 : nz(CCCCC[1])) 
    DDD := (na(DDD[1]) ? CCCCC : DDD[1] + (AAA * (CCCCC - DDD[1]))) 
    DDDD = lowest(DDD, stc_signal) 
    DDDDD = highest(DDD, stc_signal) - DDDD     
    DDDDDD := (DDDDD > 0 ? ((DDD - DDDD) / DDDDD) * 100 : nz(DDDDDD[1])) 
    EEEEE := (na(EEEEE[1]) ? DDDDDD : EEEEE[1] + (AAA * (DDDDDD - EEEEE[1])))
    EEEEE

stc = AAAAA(stc_signal,stc_fast,stc_slow)
stc_color = stc > stc[1] ? buy_color : sell_color
plot(stc,color=stc_color, title="STC",linewidth=2,style=plot.style_circles)
//plot(stc_line,color=stc_color, title="STC middle line",style=plot.style_circles)


// === SSL ===
// ==================================================
ssl_len = 55
SSL = wma(2 * wma(close, ssl_len / 2) - wma(close, ssl_len), round(sqrt(ssl_len))) 
ssl_multy = 0.2
ssl_range = tr
rangema = ema(ssl_range, ssl_len)
upperk = SSL + rangema * ssl_multy
lowerk = SSL - rangema * ssl_multy
ssl_angle = angle(SSL)
ssl_color_buy = #00c3ff
ssl_color_sell = #ff0062
ssl_color = close > upperk ? ssl_color_buy : close < lowerk ? ssl_color_sell : color.gray
plot(SSL, title="SSL", linewidth=4, color=ssl_color)
plot(ssl_angle,title='SSL Angle', color=ssl_color, linewidth=4,transp=0, style=plot.style_circles)



// === Bollinger Awesome Alert R1.1 by JustUncleL ===
// ==================================================
bb_use_ema = input(false, title="Use EMA for Bollinger Band")
bb_source = input(close, title="Bollinger Source")
bb_length = input(25, minval=1, title="Bollinger Length")
bb_mult = input(2.0, title="Base Multiplier", minval=0.5, maxval=10)
sqz_length = 100
sqz_threshold = input(50, minval=0, title="Squeeze Threshold") // old 54

// EMA inputs
fast_ma_len = input(9, title="Fast EMA length", minval=2)

// Breakout Indicator Inputs
ema_1 = ema(bb_source, bb_length)
sma_1 = sma(bb_source, bb_length)
bb_basis = bb_use_ema ? ema_1 : sma_1
//fast_ma = ema(bb_source, fast_ma_len)
fast_ma = sma(bb_source, fast_ma_len)
dev = stdev(bb_source, bb_length)
bb_dev = bb_mult * dev
// Bands
bb_upper = bb_basis + bb_dev
bb_lower = bb_basis - bb_dev
// Calculate BB spread and average spread
bb_spread = bb_upper - bb_lower
avgspread = sma(bb_spread, sqz_length)
// Calculate BB relative %width for Squeeze indication
bb_squeeze = 0.00
bb_squeeze := bb_spread / avgspread * 100
// Calculate Upper and Lower band painting offsets based on 50% of atr.
bb_offset = atr(14) * 0.5
bb_sqz_upper = bb_upper + bb_offset
bb_sqz_lower = bb_lower - bb_offset
basis_angle = angle(bb_basis)
plot(bb_squeeze[0], title="BB Squeeze", color=bb_squeeze < sqz_threshold ? color.blue : color.gray, transp=10, linewidth=0,style=plot.style_circles)
plot(bb_basis, title="Basis Line", color=color.red, transp=10, linewidth=2)
ubi = plot(show_BB ? bb_upper: na, title="Upper Band Inner", color=color.blue, transp=10, linewidth=1)
usqzi = plot(show_BB ? bb_sqz_upper: na, "Upper band", transp=0, linewidth=1,color=color.red)
lbi = plot(show_BB ? bb_lower:na, title="Lower Band Inner", color=color.blue, transp=10, linewidth=1)
lsqzi = plot(show_BB ? bb_sqz_lower: na, "Lower Band", transp=0, linewidth=1,color=color.red)
fill(ubi, usqzi, color=bb_squeeze > sqz_threshold ? color.black : color.blue, transp=20)
fill(lbi, lsqzi, color=bb_squeeze > sqz_threshold ? color.black : color.blue, transp=20)



// ===  ATR ===
// ==================================================
atrlen = input(14, "ATR Period")
atr_mult = input(1.1, "ATR Mult", step = 0.1)
resis_mult = input(0.205, "ATR Resistance", step = 0.1)
atr_slen = wma(tr(true), atrlen)
atr_upper = atr_slen * atr_mult + close
atr_lower = close - atr_slen * atr_mult
atr_upper_band = atr_slen * resis_mult + close
atr_lower_band = close - atr_slen * resis_mult
// ATR Stop Loss
plot(show_atr ? atr_upper : na, "+ATR Upper", color=#ffffff, transp=80)
plot(show_atr ? atr_lower : na, "-ATR Lower", color=#ffffff, transp=80)
// ATR Bands S/R
fill_upper = plot(atr_upper_band, "+ATR Resistance", color=#ffffff, transp=90)
fill_lower = plot(atr_lower_band, "-ATR Resistance", color=#ffffff, transp=90)
fill(fill_upper, fill_lower, color=gray, transp=80)
atr_angle = angle(atr_lower_band)
plot(atr_angle, "-ATR Angle", color=#ffffff, transp=90,style=plot.style_circles)



// === Bollinger on Macd ===
// ==================================================
SDev = 0.0
bsm_upper_band = 0.0
bsm_lower_band = 0.0
fast = input(8, "Fast Average")
slow = input(26, "Slow Average")
stdv = input(0.8, "Stdv")
m_fast = ema(close,fast)
m_slow = ema(close,slow)
BBMacd = m_fast - m_slow
Avg = ema(BBMacd,9)
SDev := stdev(BBMacd,9)
bsm_upper_band := Avg + stdv * SDev
bsm_lower_band := Avg - stdv * SDev
pcol = BBMacd < bsm_lower_band ? sell_color : BBMacd > bsm_upper_band ? #008000 : color.blue
pcol1 = BBMacd < bsm_lower_band ? sell_color :  na
pcol2 = BBMacd > bsm_upper_band ? #008000  :  na
//bsm_macd=plot(BBMacd, title='Line Macd BB', color=pcol, linewidth=2, style=plot.style_line, transp=0)
//macd_angle = angle(BBMacd) / 10000
//macd_change = change(BBMacd)
//plot(macd_angle, color=pcol, title="MACD angle")
//plot(macd_change, color=pcol, title="MACD change")



// === Bollinger on Macd - BSM ===
// ==================================================
fastLength = 8 //input(8, minval=1)
slowLength = 16 //input(16,minval=1)
signalLength = 12 //input(12,minval=1)
fastMA = ema(close, fastLength)
slowMA = ema(close, slowLength)
macd = fastMA - slowMA
signal = sma(macd, signalLength)
macd_diff = abs(macd - signal) * 1000
//plot(macd_diff,title="MACD Diff",style=plot.style_circles)



// === RSI Chart Bars ===
// ==================================================
var rsi_color = #000000
rsi_len = 14 //input(14, minval=1, title="Length")
rsi_up = rma(max(change(close), 0), rsi_len)
rsi_down = rma(-min(change(close), 0), rsi_len)
rsi = rsi_down == 0 ? 100 : rsi_up == 0 ? 0 : 100 - (100 / (1 + rsi_up / rsi_down))
isup() => rsi > 70
isdown() => rsi < 30
// plot(rsi,title="RSI",color=color.blue)
rsi_color := isup() ? buy_color : isdown() ? sell_color : na
barcolor(rsi_color, title="Rsi Candles")



// === Candlesticks ===
// ==================================================

// Doji
candle_transp = 20
DojiSize = input(0.1, minval=0.01, title="Doji size")
doji=(abs(open - close) <= (high - low) * DojiSize)
plotchar(doji, title="Doji", text='Doji', color=color.white,transp=80)
// Hammer
hammer =(((high - low)>3*(open -close)) and  ((close - low)/(.001 + high - low) > 0.6) and ((open - low)/(.001 + high - low) > 0.6))
plotshape(hammer, title= "Hammer", location=location.belowbar, color=white,transp=80,style=shape.diamond, text="H")
// Inverted Hammer
inverted_hammer =(((high - low)>3*(open -close)) and  ((high - close)/(.001 + high - low) > 0.6) and ((high - open)/(.001 + high - low) > 0.6))
plotshape(inverted_hammer, title= "Inverted Hammer", location=location.belowbar, color=white,transp=80,style=shape.diamond, text="IH")
// Bearish Engulfing
bear_e=(close[1] > open[1] and open > close and open >= close[1] and open[1] >= close and open - close > close[1] - open[1] )
plotshape(use_be ? bear_e : na,  title= "Bearish Engulfing", color=sell_color, style=shape.arrowdown, text="BE",transp=80)
// Bearish Engulfing
bear_h=(close[1] > open[1] and open > close and open <= close[1] and open[1] <= close and open - close < close[1] - open[1] )
plotshape(bear_h, title= "Bearish Harami",  color=red, style=shape.arrowdown, text="BH",transp=80)
// Bullish Harami
bh=(open[1] > close[1] and close > open and close <= open[1] and close[1] <= open and close - open < open[1] - close[1] )
plotshape(bh,  title= "Bullish Harami", location=location.belowbar, color=buy_color, style=shape.arrowup, text="BH",transp=80)
// Bullish Engulfing
be=(open[1] > close[1] and close > open and close >= open[1] and close[1] >= open and close - open > open[1] - close[1] )
plotshape(be, title= "Bullish Engulfing", location=location.belowbar, color=buy_color, style=shape.arrowup, text="BE",transp=80)


entry_signal() =>
	dir = 0
	trading = 0

	// Type of candle - Long / Short
	candle = close > open ? 1 : 0
	wae_limit = 75
	Deadzone = Session(dz_time) ? 1 : 0
	
	// Buy signal
	if (down and candle == 0 and low <= atr_lower_band)
		dir := 1
		trading := 1

		// Touching fast ema Filter
		if use_high_greater_fast and high > ema_fast and perc_change < 10
			dir := na
			trading := 0

		// Trend Filter
		if use_trend and open > ema_200
			dir := na
			trading := 0

		// EMA Cross
		if use_ema_cross and (ema_fast > SSL or ema_fast > bb_basis) 
			dir := na
			trading := 0

		// STC Filter
		if ( use_stc and stc > 15 
		 or use_stc and stc_color == buy_color )
			dir := na
			trading := 0
		// if ( use_stc and stc > stc_low and isdown() ) 
		//  or ( use_stc and stc_color == buy_color and not isdown() 
		//  or (use_stc and not isdown() and stc > stc_line ) )
		// 	dir := na
		// 	trading := 0

		// MACD Cross
		if use_macd_cross and ( (signal < macd and macd_diff > 1) or (signal > 0) and ema_fast < sma_slow )
			dir := na
			trading := 0

		// BSM Filter
		// if use_bsm and BBMacd[2] > bsm_lower_band[2]
		// 	dir := na
		// 	trading := 0

		// Bearish Engulfing
		if use_be and bear_e == 1
			dir := na
			trading := 0

		// Doji
		if use_doji and doji == 1 and not isdown()
			dir := na
			trading := 0

		// Hammer
		if use_hammer and hammer == 1
			dir := na
			trading := 0

		// No upper wick
		if open == high
			dir := na
			trading := 0


	// Downtrend Counter 
	if (use_counter and up and candle == 1 and high >= atr_upper_band)
		dir := -1
		trading := 1

		// EMA Cross
		if use_ema_cross and (ema_fast < bb_basis) 
			dir := na
			trading := 0

		// STC filter counter
		if use_cb_stc and stc < stc_high
			dir := na
			trading := 0

		// Doji
		if use_doji and doji == 1
			dir := na
			trading := 0

		// bullish Engulfing
		if be and stc > stc_line
			dir := na
			trading := 0


	// Downtrend Special Candles 
	if (use_special and candle == 0 and perc_change > 50 and stc_color == sell_color)
	 or (use_special and candle == 1 and doji == 1 and hammer == 1 and rsi_color[1] == sell_color)
	 or (use_special and doji == 1 and open < fast_ema and isdown() )
	 or (use_special and bh and ema_fast < sma_slow and close < fast_ema and stc < stc_line and stc_color == sell_color)
	 or (use_special and (candle == 1 and rsi_color == sell_color) and (rsi_color[1] == sell_color and close[1] < open[1]) and
	  atr_angle > 10 and perc_change <= 6)
		dir := 1
		trading := 1

	// Downtrend Counter below slow SMA - Special Candles 
	// if use_counter and bear_e and open < ema_200 and fast_ema_angle > 0 and ema_fast < sma_slow
	// 	dir := -1
	// 	trading := 1

	// Downtrend Counter above slow SMA - Special Candles 
	// if use_counter and open < ema_200 and fast_ema_angle > 0 and ema_fast > sma_slow

	// 	if open >= fast_ema and close >= fast_ema and high >= (ema_200 - 0.040)
	// 		dir := -1
	// 		trading := 1

	// // Buy - long wicks 
	// if (trend < 0 and bbr <= 0.20 and candle == 0) and (use_wicks and low < (bb_sqz_lower + .00010) ) and (close > bb_sqz_lower)
	// 	dir := 1
	// 	trading := 1

	// // Sell - long wicks
	// if (trend > 0 and close <= bb_sqz_upper and candle == 1) and (use_wicks and high > bb_sqz_upper )
	// 	dir := -1
	// 	trading := 1

	// Don't trade during high spread periods
	if use_deadzone and Deadzone == 1
		dir := na
		trading := 0

	[dir, trading]

[trade_dir, trading] = entry_signal() 


// Buy / Sell indicators
bgcolor(Session(dz_time) ? #b71c1c : na, title="Deadzone",transp=85)
plotshape(trade_dir > 0 and show_entry ? 1 : na, title="Entry Buy", color=trade_dir > 0 ? color.orange : buy_color, location = location.belowbar, style=shape.labelup, text="B", textcolor=color.white, size=size.small)
plotshape(trade_dir < 0 and show_entry ? -1 : na, title="Entry Sell", color= trade_dir < 0 ? color.orange : sell_color, location = location.abovebar, style=shape.labeldown, text="S", textcolor=color.white, size=size.small)
plot(trading ,title="Trading",color= trading ? buy_color : color.gray)