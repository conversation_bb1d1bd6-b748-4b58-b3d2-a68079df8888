//@version=5
indicator("Candles Multi - V2", overlay=true, max_labels_count = 500)

red = #ff0000
orange = #ff9800
yellow = #FFFF00
green = #55d51a
lime = #50e600
aqua = #00bcd4
blue = #2962ff
dark_blue = #2962ff
violet = #814dff
white = #ffffff
gray = #707070
black = #000000

angle(_src, len) =>
    rad2degree=180/3.14159265359  //pi 
    ang=rad2degree*math.atan((_src[0] - _src[1]) / ta.atr(len))

// SYM Info
min_tick = syminfo.mintick
var int decimals = int(math.log10(1/min_tick))
decimals := decimals == 2 ? 1 : decimals == 0 ? 1 : decimals
// plot(min_tick, title='Min Tick')
// plot(decimals, title='Decimals')

get_pip_distance(point1, point2) =>
    diff_points = math.abs( (point1 - point2) )
    pip_value = decimals<3 ? diff_points / syminfo.mintick / 100 : diff_points / syminfo.mintick / 10
    //pip_value = decimals>3 ? diff_points * (math.pow(10, decimals) * syminfo.mintick) : diff_points / syminfo.mintick / 10

newbar(res) => ta.change(time(res)) == 0 ? 0 : 1



// CANDLES MULTI-TIMEFRAME
// -----------------------------------------------------
g_candles   = 'Candles -----------------------------------------------------------'
inl_c1      = 'inl_c1'
i_cand      = input.bool(true, title="Show Candles", group=g_candles)
i_can_type  = input.string("Normal", 'Candle Type', ["Normal", "Heikin", "Renko"], group=g_candles, inline=inl_c1)
i_c_heik1   = input.bool(false,title='Heikin Ashi', group=g_candles, inline=inl_c1)
i_c_renko1  = input.bool(false,title='Renko', group=g_candles, inline=inl_c1)
i_c_time    = input.timeframe('120', "Resolution", group=g_candles) // 1 hour
i_c_trans1  = input.int(30, title='Transp', group=g_candles)
i_s_c_attr  = input.bool(false, title='Candle Attributes', group=g_candles)

g_c2 = 'Candles 2 -----------------------------------'
inl_c2     = 'inl_c2'
i_cand2    = input.bool(true, title="Show Candles 2", group=g_c2, inline=inl_c2)
i_c_heik2  = input.bool(false,title='Heikin Ashi', group=g_c2, inline=inl_c2)
i_c_time2  = input.timeframe('240', "Resolution", group=g_c2) // 4 hour
i_c_trans2 = input.int(65, title='Transp', group=g_c2)
i_s_c_attr2= input.bool(false, title='Candle Attributes', group=g_c2)

g_c3 = 'Candles 3 -----------------------------------'
inl_c3     = 'inl_c3'
i_cand3    = input.bool(true, title="Show Candles 3", group=g_c3, inline=inl_c3)
i_c_heik3  = input.bool(false,title='Heikin Ashi', group=g_c3, inline=inl_c3)
i_c_time3  = input.timeframe('720', "Resolution", group=g_c3) // 12 hour
i_c_trans3 = input.int(90, title='Transp', group=g_c3)
i_s_c_attr3= input.bool(false, title='Candle Attributes', group=g_c3)
i_s_c_label3= input.bool(false, title='Candle Labels', group=g_c3)

g_c4 = 'Additional -----------------------------------'
i_candle_mid = input.bool(false, title='Show Candle Midline', group=g_c4)
i_candle_type = input.bool(false, title='Show Candle Types', group=g_c4)


i_c_lookback = 1 //input.int(1, title='Lookback', group=g_candles)
i_lookahead = true //input.bool(true,title='Use Lookahead', group=g_candles)

// Candle Ratios
f_candle_ratios(c_open,c_high,c_low,c_close) =>
    C_dir = 0.
    C_hratio = 0.
    C_lratio = 0.
    C_color = red

    C_hl    = c_high - c_low
    C_body  = math.abs( (c_close - c_open) / C_hl )
    C_bratio = C_body / C_hl

    if c_close>c_open 
        C_hratio :=  (c_high - c_close) / C_hl
        C_lratio :=  (c_open - c_low) / C_hl
        C_color  := green
        C_dir    := 1
    else 
        C_hratio := (c_high - c_open) / C_hl
        C_lratio := (c_close - c_low) / C_hl
        C_color  := red
        C_dir    := 0

    [C_hl,C_hratio,C_lratio,C_body,C_color]

// Candle Types - Doji ect..
f_candle_type(co1, ch1, cl1, cc1, dist, c_type, show) =>

    if ta.change(cc1) and i_candle_type and show

        short_sm = dist<4 and c_type == 0 ? 1 : 0
        long_sm  = dist<4 and c_type == 1 ? 1 : 0
        if short_sm
            short_sm_txt = "Doji"
            info1 = label.new(x=time,y=ch1,xloc=xloc.bar_time, text=short_sm_txt, textcolor=#ffffff,color =red, style=label.style_label_down)
        if long_sm
            info2 = label.new(x=time,y=cl1,xloc=xloc.bar_time, text="Doji", textcolor=#ffffff,color =green, style=label.style_label_up)

f_cand_breaking(o,h,l,c) =>
    var hi = false
    var lo = false
    var dist = 0.
    var type = 0

    hi := high>h ? true : false
    lo := low<l ? true : false
    dist := get_pip_distance(c,o)
    type := c>o ? 1 : -1
    //strong = type==1 and o==l ? 1 : type==-1 and o==h ? 1 : 0

    [hi,lo,dist]


get_heiken(o,h,l,c)=>
    type   = c > o ? 1 : -1
    dist_h = get_pip_distance(o,h) < 0.5
    dist_l = get_pip_distance(o,l) < 0.5
    strong = type==1 and dist_l ? 1 : type==-1 and dist_h ? 1 : 0
    strong


// CANDLES ATTRIBUTES 1
ticket_type = i_can_type == "Renko" ? ticker.renko(syminfo.tickerid, "ATR", 10) :  i_can_type == "Heikin" ? ticker.heikinashi(syminfo.tickerid) : syminfo.tickerid
// -----------------------------------------------------
[co1,ch1,cl1,cc1] = request.security(ticket_type, i_c_time, 
 [ open[i_c_lookback], high[i_c_lookback], low[i_c_lookback], close[i_c_lookback] ],
 lookahead=barmerge.lookahead_on )

[co1_h,ch1_h,cl1_h,cc1_h] = request.security(ticker.heikinashi(syminfo.tickerid), i_c_time, 
 [ open[i_c_lookback], high[i_c_lookback], low[i_c_lookback], close[i_c_lookback] ] ,  
 lookahead=barmerge.lookahead_on)
cm = (cc1 + co1) * 0.5

[C_hl,C_hratio,C_lratio,C_body,C_color] = f_candle_ratios(co1,ch1,cl1,cc1)

chl = get_pip_distance(ch1,cl1)
cco = get_pip_distance(cc1,co1)
c_type = i_c_heik1 == false and cc1>co1 ? 1 : i_c_heik1 == true and cc1_h>co1_h ? 1 : -1
candle_color = c_type == 1 ? green : red
candle_dist = get_pip_distance(cc1,co1)

[c_hi,c_lo,c_dist] = f_cand_breaking(co1, ch1, cl1, cc1)

c_str_heikin = get_heiken(co1_h,ch1_h,cl1_h,cc1_h)

var float c1_high_low = 0.0
c1_high_low := ta.change(ch1) and ch1 < ch1[1] ? -1 : ta.change(ch1) and ch1 > ch1[1] ? 1 : c1_high_low
plot(c1_high_low, 'Candle High Low 1', style=plot.style_cross, color=color.new(white,100)  )

// PLOT CANDLE 1
// -----------------------------------------------------
// plotcandle(co1, ch1, cl1, cc1, color = cc1>co1 ? color.new(green,i_c_trans1) : color.new(red,i_c_trans1))

plotshape(i_cand and c_str_heikin and c_type==-1 and i_s_c_attr ? 1 : 0, title="Strong", color=red , style=shape.circle, location=location.top)
plotshape(i_cand and c_str_heikin and c_type==1 and i_s_c_attr ? 1 : 0, title="Strong",color=green, style=shape.circle, location=location.bottom)
p_co  = plot(i_cand == false ? na : i_c_heik1 ? co1_h : co1, title='Candle Open',color=color.new(candle_color,i_c_trans1) )
p_ch  = plot(i_cand == false ? na : i_c_heik1 ? ch1_h : ch1, title='Candle High',color=color.new(color.white,i_c_trans1) )
p_cl  = plot(i_cand == false ? na : i_c_heik1 ? cl1_h : cl1, title='Candle Low',color=color.new(color.white,i_c_trans1) )
p_cc  = plot(i_cand == false ? na : i_c_heik1 ? cc1_h : cc1, title='Candle Close',color=color.new(candle_color,i_c_trans1) )
fill(p_cc, p_co, title='Fill Candle 1',color=i_cand ? color.new(candle_color,i_c_trans1) : na )

// Candle Ratios
// plot(i_s_c_attr ? C_hl : na, title='High Low Ratio', color= color.new(white,100) )
// plot(i_s_c_attr ? C_hratio : na, title='High Ratio', color= color.new(white,100) )
// plot(i_s_c_attr ? C_lratio : na, title='Low Ratio', color= color.new(white,100) )
// plot(i_s_c_attr ? C_body : na, title='Body Ratio', color= color.new(white,100) )


// CANDLES ATTRIBUTES 2
// -----------------------------------------------------
[co2,ch2,cl2,cc2] = request.security(syminfo.tickerid, i_c_time2, 
 [ open[i_c_lookback], high[i_c_lookback], low[i_c_lookback], close[i_c_lookback] ], 
 lookahead=barmerge.lookahead_on )

[co2_h,ch2_h,cl2_h,cc2_h] = request.security(ticker.heikinashi(syminfo.tickerid), i_c_time2, 
 [ open[i_c_lookback], high[i_c_lookback], low[i_c_lookback], close[i_c_lookback] ] ,  
 lookahead=barmerge.lookahead_on)

cm2   = (cc2 + co2) * 0.5
chl2 = get_pip_distance(ch2,cl2)
cco2 = get_pip_distance(cc2,co2)

c_type2 = i_c_heik2 == false and cc2>co2 ? 1 : i_c_heik2 == true and cc2_h>co2_h ? 1 : -1
candle_color2 = c_type2 == 1 ? green : red
candle_dist2 = get_pip_distance(cc2,co2)

// Candle Ratios
[C_hl2,C_hratio2,C_lratio2,C_body2,C_color2] = f_candle_ratios(co2,ch2,cl2,cc2)
// Candle Type
f_candle_type(co2, ch2, cl2, cc2, candle_dist2, c_type2, i_s_c_attr2)


[c2_hi,c2_lo,c2_dist] = f_cand_breaking(co2, ch2, cl2, cc2)
c2_str_heikin = get_heiken(co2_h,ch2_h,cl2_h,cc2_h)

var float c2_high_low = 0.0
c2_high_low := ta.change(ch2) and ch2 < ch2[1] ? -1 : ta.change(ch2) and ch2 > ch2[1] ? 1 : c2_high_low
plot(c2_high_low, 'Candle High Low 2', style=plot.style_cross, color=color.new(white,100)  )

// PLOT CANDLE 2
// -----------------------------------------------------
plotshape(i_cand2 and c2_str_heikin and c_type2==-1 and i_s_c_attr2 ? 1 : 0, title="Strong 2", color=red , style=shape.circle, location=location.top)
plotshape(i_cand2 and c2_str_heikin and c_type2==1 and i_s_c_attr2 ? 1 : 0, title="Strong 2",color=green, style=shape.circle, location=location.bottom)
p_co2  = plot(i_cand2 == false ? na : i_c_heik2 ? co2_h : co2, title='Candle Open 2',color=color.new(candle_color2,i_c_trans2) )
p_ch2  = plot(i_cand2 == false ? na : i_c_heik2 ? ch2_h : ch2, title='Candle High 2',color=color.new(color.white,i_c_trans2))
p_cl2  = plot(i_cand2 == false ? na : i_c_heik2 ? cl2_h : cl2, title='Candle Low 2',color=color.new(color.white,i_c_trans2))
p_cc2  = plot(i_cand2 == false ? na : i_c_heik2 ? cc2_h : cc2, title='Candle Close 2',color=color.new(candle_color2,i_c_trans2) )
fill(p_cc2, p_co2, title='Fill Candle 2',color=i_cand2 ? color.new(candle_color2,i_c_trans2) : na )

// Candle Ratios
// plot(i_s_c_attr2 ? C_hl2 : na, title='High Low Ratio', color= color.new(white,100) )
// plot(i_s_c_attr2 ? C_hratio2 : na, title='High Ratio', color= color.new(white,100) )
// plot(i_s_c_attr2 ? C_lratio2 : na, title='Low Ratio', color= color.new(white,100) )
// plot(i_s_c_attr2 ? C_body2 : na, title='Body Ratio', color= color.new(white,100) )

// Higher or Lower
plot(c2_high_low, title='Higher or Lower 2', color= color.new(white,100) )



// CANDLES ATTRIBUTES 3
// -----------------------------------------------------
[co3,ch3,cl3,cc3] = request.security(syminfo.tickerid, i_c_time3, 
 [ open[i_c_lookback], high[i_c_lookback], low[i_c_lookback], close[i_c_lookback] ], 
 lookahead=barmerge.lookahead_on )

[co3_h,ch3_h,cl3_h,cc3_h] = request.security(ticker.heikinashi(syminfo.tickerid), i_c_time3, 
 [ open[i_c_lookback], high[i_c_lookback], low[i_c_lookback], close[i_c_lookback] ] ,  
 lookahead=barmerge.lookahead_on)


cm3   = (cc3 + co3) * 0.5
chl3 = get_pip_distance(ch3,cl3)
cco3 = get_pip_distance(cc3,co3)

c3_type = i_c_heik3 == false and cc3>co3 ? 1 : i_c_heik3 == true and cc3_h>co3_h ? 1 : -1
cand3_c = c3_type == 1 ? green : red
candle_dist3 = get_pip_distance(cc3,co3)

// Candle Ratios
[C_hl3,C_hratio3,C_lratio3,C_body3,C_color3] = f_candle_ratios(co3,ch3,cl3,cc3)

f_candle_type(co3, ch3, cl3, cc3, candle_dist3, c3_type, i_s_c_attr3)



[c3_hi,c3_lo,c3_dist] = f_cand_breaking(co3, ch3, cl3, cc3)

c3_str_heikin = get_heiken(co3_h,ch3_h,cl3_h,cc3_h)

var float c3_high_low = 0.0
c3_high_low := ta.change(ch3) and ch3 < ch3[1] ? -1 : ta.change(ch3) and ch3 > ch3[1] ? 1 : c3_high_low
plot(c3_high_low, 'Candle High Low 3', style=plot.style_cross, color=color.new(white,100)  )


// PLOT CANDLE 3
// -----------------------------------------------------
plotshape(i_cand3 and c3_str_heikin and c3_type==-1 and i_s_c_attr3 ? 1 : 0, title="Strong 3", color=red , style=shape.circle, location=location.top)
plotshape(i_cand3 and c3_str_heikin and c3_type==1 and i_s_c_attr3 ? 1 : 0, title="Strong 3",color=green, style=shape.circle, location=location.bottom)
//plotshape(c3_hi and i_s_c_attr3  ? 1 : 0,title="Hi 3",color=red , style=shape.circle, location=location.top)
//plotshape(c3_lo and i_s_c_attr3  ? 1 : 0,title="Low 3",color=green, style=shape.circle, location=location.bottom)
//plot(c3_dist, 'Dist 3', color=color.new(cand3_c,100))

p_co3  = plot(i_cand3 == false ? na : i_c_heik3 ? co3_h : co3, title='Candle Open 3',color=color.new(cand3_c,i_c_trans3) )
p_ch3  = plot(i_cand3 == false ? na : i_c_heik3 ? ch3_h : ch3, title='Candle High 3',color=color.new(color.white,i_c_trans3))
p_cl3  = plot(i_cand3 == false ? na : i_c_heik3 ? cl3_h : cl3, title='Candle Low 3',color=color.new(color.white,i_c_trans3))
p_cc3  = plot(i_cand3 == false ? na : i_c_heik3 ? cc3_h : cc3, title='Candle Close 3',color=color.new(cand3_c,i_c_trans3) )
fill(p_cc3, p_co3, title='Fill Candle 3',color=i_cand3 ? color.new(cand3_c,i_c_trans3) : na )

// Candle Ratios
// plot(i_s_c_label3 ? C_hl3 : na, title='High Low Ratio 3', color= color.new(white,100) )
// plot(i_s_c_label3 ? C_hratio3 : na, title='High Ratio 3', color= color.new(white,100) )
// plot(i_s_c_label3 ? C_lratio3 : na, title='Low Ratio 3', color= color.new(white,100) )
// plot(i_s_c_label3 ? C_body3 : na, title='Body Ratio 3', color= color.new(white,100) )

cand3_ch = i_cand3 == false ? na : i_c_heik3 ? co3_h : co3
if ta.change(cand3_ch) and i_s_c_label3
    txt3 = str.tostring(math.round(C_hratio3 * 100), '#.##') + '\n'
     + str.tostring(math.round(C_body3 * 100), '#.##') + '\n'
     + str.tostring(math.round(C_lratio3 * 100), '#.##')

    if c3_type == 1
        info2 = label.new(x=time, y= ch3 + 0.0005, xloc=xloc.bar_time, text=txt3, textcolor=#ffffff, style=label.style_label_down, color=red)
    else 
        info2 = label.new(x=time, y= cl3 + 0.0005, xloc=xloc.bar_time, text=txt3, textcolor=#ffffff, style=label.style_label_up, color=green)

    

// Extras
//p_cm  = plot(i_cand and i_candle_mid ? cm : na, title='Candle Mid',color=color.new(color.white,80) )
// plot(chl, title='Pips HL', color=color.new(candle_color,100))
// plot(cco, title='Pips Body', color=color.new(candle_color,100))

//p_cm2   = plot(i_cand2 and i_candle_mid ? cm2 : na, title='Candle Mid 2',color=color.new(color.white,80) )
// plot(chl2, title='Pips High to Low', color=color.new(candle_color2,100))
// plot(cco2, title='Pips Close to Open', color=color.new(candle_color2,100))

//p_cm3   = plot(i_cand3 and i_candle_mid ? cm3 : na, title='Candle Mid 3',color=color.new(color.white,80) )
// plot(chl3, title='Pips High to Low', color=color.new(candle_color3,100))
// plot(cco3, title='Pips Close to Open', color=color.new(candle_color3,100))







// if newbar(i_c_time)
//     plotcandle(co1, ch1, cl1, cc1, title='Title', color = open < close ? green : red, wickcolor=open < close ? green : red)






//C_Len = input.int(14, title='C Body Length')




// PD RETRACE
// -----------------------------------------------------
g_pd = 'PD Retrace -----------------------------------------------------------'
i_pd_fibo = input.bool(false, title='Show Fibo', group=g_pd)
i_pd_labels = input.bool(false, title="Show Labels", group=g_pd)
i_pd_fills = input.bool(false, title='Fill in Fibo Levels', group=g_pd)
i_pd_fills_trans = input.int(90, title='Fill Transparency', group=g_pd)

// FIBO LEVELS
// -----------------------------------------------------
close_m = request.security(syminfo.tickerid, i_c_time, close, lookahead=barmerge.lookahead_on)
fibo0   = request.security(syminfo.tickerid, i_c_time, high[i_c_lookback], lookahead=barmerge.lookahead_on)
fibo100 = request.security(syminfo.tickerid, i_c_time, low[i_c_lookback], lookahead=barmerge.lookahead_on)
fibo23  = (fibo100-fibo0)*0.786+fibo0
fibo38  = (fibo100-fibo0)*0.618+fibo0
fibo50  = (fibo100-fibo0)/2+fibo0
fibo62  = (fibo100-fibo0)*0.382+fibo0
fibo78  = (fibo100-fibo0)*0.236+fibo0

// FIBO PLOTS
// -----------------------------------------------------
p_fib0 = plot(i_pd_fibo ? fibo0 : na ,title='Fibo 0',color=color.new(color.red,50),linewidth=2)
p_fibo100 = plot(i_pd_fibo ? fibo100 : na ,title='Fibo 100',color=color.new(color.green,50),linewidth=2)
p_fibo23  = plot(i_pd_fibo ? fibo23 : na,title='Fibo 23',color=color.new(color.gray,50) )
p_fibo38  = plot(i_pd_fibo ? fibo38 : na,title='Fibo 38',color=color.new(color.gray,50) )
p_fibo50  = plot(i_pd_fibo ? fibo50 : na,title='Fibo 50',color=color.new(color.gray,50) )
p_fibo62  = plot(i_pd_fibo ? fibo62 : na,title='Fibo 62',color=color.new(color.gray,50) )
p_fibo78  = plot(i_pd_fibo ? fibo78 : na,title='Fibo 78',color=color.new(color.gray,50) )

f_get_level() =>
    var int level = 0
    // Above Red
    if close>fibo0 
        level := 8
    // Red 
    if close<fibo0 and close>fibo78
        level := 7
    // Orange 
    if close<fibo78 and close>fibo62
        level := 6
    // Yellow
    if close<fibo62 and close>fibo50
        level := 5
    // Aqua
    if close<fibo50 and close>fibo38
        level := 4
    // Green
    if close<fibo38 and close>fibo23
        level := 3
    // Lime
    if close<fibo23 and close>fibo100
        level := 2
    // Below Lime
    if close<fibo100
        level := 1

    level

//
fill(p_fibo78, p_fib0,title='Fill 100',color=i_pd_fills? color.new(red,i_pd_fills_trans) : na )
fill(p_fibo62, p_fibo78,title='Fill 78',color=i_pd_fills? color.new(orange,i_pd_fills_trans) : na )
fill(p_fibo50, p_fibo62,title='Fill 62',color=i_pd_fills? color.new(yellow,i_pd_fills_trans) : na )
fill(p_fibo50, p_fibo38,title='Fill 38',color=i_pd_fills? color.new(aqua,i_pd_fills_trans) : na )
fill(p_fibo38, p_fibo23,title='Fill 23',color=i_pd_fills? color.new(green,i_pd_fills_trans) : na )
fill(p_fibo23, p_fibo100,title='Fill 0',color=i_pd_fills? color.new(lime,i_pd_fills_trans) : na )

var int p_fb0_ch = 0
var int p_fb100_ch = 0

if ta.change(fibo0) 
    p_fb0_ch := fibo0 > fibo0[1] ? 1 : 0
    p_fb100_ch := fibo100 > fibo100[1] ? 1 : 0

bool f_higher = p_fb0_ch==1 and p_fb100_ch == 1 ? true : false
bool f_lower  = p_fb0_ch==0 and p_fb100_ch == 0 ? true : false
bool f_diff   = (p_fb0_ch==1 and p_fb100_ch==0) or (p_fb0_ch==0 and p_fb100_ch==1) ? true : false

plotshape(ta.change(fibo0) and f_higher and i_pd_labels ? 1 : na,title="Higher Highs",color=green ,style=shape.circle,location=location.bottom, size=size.tiny)
plotshape(ta.change(fibo0) and f_lower and i_pd_labels ? 1 : na,title="Lower Lows",color=red ,style=shape.circle,location=location.bottom, size=size.tiny)
plotshape(ta.change(fibo0) and f_diff and i_pd_labels ? 1 : na,title="Lower Lows",color=blue ,style=shape.circle,location=location.bottom, size=size.tiny)

//fill(p_fib0,p_fibo100, color=#0a111c)

diff_fibo0 = ta.change(fibo0,1) 
diff_fibo100 = ta.change(fibo100,1)  


if ta.change(fibo0) and i_pd_labels

    new_level = f_get_level()

    higher = fibo0 > fibo0[1] ? "Higher: " + str.tostring(diff_fibo0)  : fibo0 < fibo0[1] ? "Lower: " + str.tostring(diff_fibo0) : "No Change: " + str.tostring(diff_fibo0)
    
    txt1 = str.tostring(get_pip_distance(fibo0, fibo100)) + "\nLevel: " + str.tostring(new_level)
    info1 = label.new(x=time,y=fibo0,xloc=xloc.bar_time, text=txt1, textcolor=#ffffff)

    lower = fibo100 > fibo100[1] ? "Higher: " + str.tostring(diff_fibo100)  : fibo100 < fibo100[1] ? "Lower: " + str.tostring(diff_fibo100) : "No Change: " + str.tostring(diff_fibo100)
    txt2 = str.tostring(lower) + "\nLevel: " + str.tostring(new_level)
    info2 = label.new(x=time,y=fibo100,xloc=xloc.bar_time, text=txt2, textcolor=#ffffff, style=label.style_label_up)

// fill(p_fibo38,p_fibo50, color=color.aqua)
// fill(p_fibo50,p_fibo62, color=color.yellow)



// SESSIONS
// -----------------------------------------------------
Session(sess) => na(time("2",sess)) == false

g_sessions = 'Sessions -----------------------------------------------------------'
i_pd_GMT = input.string(title='GMT', defval='GMT-10', options=['GMT-10', 'GMT-9', 'GMT-8', 'GMT-7', 'GMT-6', 'GMT-5', 'GMT-4', 'GMT-3', 'GMT-2', 'GMT-1', 'GMT-0', 'GMT+1', 'GMT+2', 'GMT+3', 'GMT+4', 'GMT+5', 'GMT+6', 'GMT+7', 'GMT+8', 'GMT+9', 'GMT+10', 'GMT+11', 'GMT+12', 'GMT+13'] , group=g_sessions)
i_show_nt = input.bool(false, title='Show No Trade', group=g_sessions)
i_pd_no_trading = input.session(title="No Trading Hours", defval="1145-1315", group=g_sessions)
i_show_tr = input.bool(false, title='Show Session', group=g_sessions)
i_pd_session  = input.session(title="Custom Session", defval="1400-1600", group=g_sessions)
i_show_sessions = input.bool(false, title='Show Sessions', group=g_sessions)
As  = input.session(title="Asia", defval="1800-0300", group=g_sessions)
Lon = input.session(title="London", defval="0300-1200", group=g_sessions)
Ny  = input.session(title="New York", defval="0800-1800", group=g_sessions)
//Dz  = input.session(title="Deadzone - High Spreads", defval="1645-1830", group=g_sessions)
//i_pd_time = input.time('20 Jul 2021 00:00 +00000', title='Time', group=g_sessions) // 1 hour 4 hour

inl_color = "inl_color"
inl_color2 = "inl_color2"
c1_on = true //input.bool(true,title="", group=g_trading,inline=inl_color)
c1 = color.new(#00bcd4,90) //input.color(title="Asia", defval=#00bcd4,group=g_trading,inline=inl_color)
c2_on = true //input.bool(true,title="",group=g_trading,inline=inl_color)
c2 = color.new(#00796b,90) //input.color(title="London", defval=#00796b,group=g_trading,inline=inl_color)
c3_on = true //input.bool(true,title="",group=g_trading,inline=inl_color2)
c3 = color.new(#b71c1c,90) //input.color(title="New York", defval=#b71c1c,group=g_trading,inline=inl_color2)
c4_on = false //input.bool(true,title="", group=g_trading,inline=inl_color2)
c4 = color.new(color.purple,90) //input.color(title="Deadzone", defval=#b71c1c,group=g_trading,inline=inl_color2)

// Set the start of day
start_time = Session(i_pd_session)
timerange = time(timeframe.period, i_pd_session, i_pd_GMT) and i_show_tr
no_trading = time(timeframe.period, i_pd_no_trading, i_pd_GMT) and i_show_nt 

Asia = Session(As) and c1_on and i_show_sessions? c1 : na
London = Session(Lon) and c2_on and i_show_sessions ? c2 : na
NewYork = Session(Ny) and c3_on and i_show_sessions ? c3 : na
//Deadzone = Session(Dz) and c4_on and i_show_sessions ? c4 : na
bgcolor(i_show_tr and timerange ? color.new(white,85) : na)
bgcolor(i_show_nt and no_trading ? color.new(red,85) : na)
bgcolor(Asia, title='Asian')
bgcolor(London, title='London')
bgcolor(NewYork, title='NewYork')
//bgcolor(Deadzone, title='Deadzone')
//plotshape(ta.change(start_time ) ? 1 : na,title="Start Time",color=green ,style=shape.circle,location=location.bottom, size=size.tiny)
// fill(p_fib0,p_fibo100,color=timerange ? color.new(white,85) : na )
// fill(p_fib0,p_fibo100,color=no_trading ? color.new(red,50) : na )




// MOVING AVERAGES
// -----------------------------------------------------
g_pd_ma = 'Moving Averages -----------------------------------------------------------'
i_pd_ma_timeframe = input.timeframe('360', "MA Resolution", group=g_pd_ma) // 1 hour 6 hour
i_show_ma = input.bool(false, title='Show MAs', group=g_pd_ma)
i_ma_len1 = input.int(5, title='MA length', group=g_pd_ma)
i_ma_len2 = input.int(10, title='MA length 2', group=g_pd_ma)
i_tmp_look = input.int(1,title='temp lookback', group=g_pd_ma)

var float m1 = 0.0
var float m1_a = 0.0
var float m2 = 0.0
var float m2_a = 0.0

change = newbar(i_pd_ma_timeframe)
m1 := change ? request.security(syminfo.tickerid, i_pd_ma_timeframe, ta.hma(close,i_ma_len1) ) : m1[1]
m1_a := change ? request.security(syminfo.tickerid, i_pd_ma_timeframe, angle(m1,14) ) : m1_a[1]
m2 := change ? request.security(syminfo.tickerid, i_pd_ma_timeframe, ta.hma(close,i_ma_len2) ) : m2[1]
m2_a := change ? request.security(syminfo.tickerid, i_pd_ma_timeframe, angle(m2,14) ) : m2_a[1]
// m1 := not change ? m1[1] : request.security(syminfo.tickerid, i_pd_ma_timeframe, ta.hma(close,i_ma_len1) ) 
// m1_a := not change ? m1_a[1] :request.security(syminfo.tickerid, i_pd_ma_timeframe, angle(m1,14) )
// m2 := not change ?  m2[1] :request.security(syminfo.tickerid, i_pd_ma_timeframe, ta.hma(close,i_ma_len2) )
// m2_a := not change ? m2_a[1] : request.security(syminfo.tickerid, i_pd_ma_timeframe, angle(m2,14) )

p_m1 = plot(i_show_ma ? m1 : na ,color=m1_a>0?aqua:orange,linewidth=2)
p_m2 = plot(i_show_ma ? m2 : na ,color=m2_a>0?green:red,linewidth=2)
//p_m1a = plot(m1_a,color=color.new(color.blue,100))


m1_cond = close<m1 and m2_a>0 and close<open ? 1 : 0
barssince = ta.barssince(m1_cond == 1)
//plot(barssince,title='Bars Since',color=color.new(color.blue,100))
//plotshape(m1_cond,title="M1",color=#00ff00 ,style=shape.circle,location=location.bottom)

m2_cond = close<m2 and m2_a>0 and close<open ? 1 : 0
//plotshape(m2_cond,title="M2",color=#00ff00 ,style=shape.circle,location=location.bottom)


