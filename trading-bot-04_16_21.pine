//@version=4
study(title="Moving Average Colored EMA/SMA", shorttitle="Colored EMA /SMA", overlay=true,precision = 4)

show_entry = input(title="Show Entry", type=input.bool, defval=true)
show_atr = input(title = "Show ATR", type = input.bool, defval = false)
use_trend = input(title="Trend Filter", type=input.bool, defval=true)
use_rsi_candles = input(title="Use RSI Candles", type=input.bool, defval=true)
use_high_greater_fast = input(title="Higher than EMA Filter", type=input.bool, defval=true)
use_stc = input(title="Use STC Filter", type=input.bool, defval=true)
use_macd_cross = input(title="Use MACD cross", type=input.bool, defval=true)
use_bsm = input(title="Use BSM", type=input.bool, defval=false)
use_doji = input(title="Filter Doji's", type=input.bool, defval=false)
use_hammer = input(title="Filter Hammer's", type=input.bool, defval=true)
use_deadzone = input(title="Deadzone", type=input.bool, defval=true)
dz_time = input(title="DZ Timeframe", type=input.session, defval= "1715-1900")

// Colors
red = #ff0000
green = #00ff00
blue = #0000ff
white = #ffffff
gray  = #707070

angle(_src) =>
    rad2degree=180/3.14159265359  //pi 
    ang=rad2degree*atan((_src[0] - _src[1])/atr(2))

Session(sess) => na(time(timeframe.period, sess)) == false

// Change
perc_change = abs( (1 - (close[1] / close)) * 10000 )
plot(perc_change,title="Change",style=plot.style_circles)



// ===  Moving Average Colored EMA SMA ===
// ==================================================
emaplot = input (true, title="Show EMA on chart")
len = input(8, minval=1, title="ema Length") // default 8
src = close
ema_fast = ema(src, len)
up = ema_fast > ema_fast[1]
down = ema_fast < ema_fast[1]
mycolor = up ?  #55d51a : down ? #e91e63 : #00c3ff
plot(ema_fast and emaplot ? ema_fast :na, title="EMA", color=mycolor, linewidth=3)
fast_ema_angle = angle(ema_fast)
plot(fast_ema_angle, title="Fast EMA angle", style=plot.style_circles,color=mycolor)

smaplot = input (true, title="Show SMA on chart")
len2 = input(50, minval=1, title="sma Length")
sma_slow = sma(close, len2)
up2 = sma_slow > sma_slow[1]
down2 = sma_slow < sma_slow[1]
mycolor2 = up2 ? green : down2 ? red : blue
plot(sma_slow and smaplot ? sma_slow :na , title="SMA", color=mycolor2, linewidth=3, transp=15)
plot(angle(sma_slow), title="Slow EMA angle", style=plot.style_circles,color=mycolor)



// ===  EMA 200 ===
// ==================================================
len_200 = input(200, minval=1, title="EMA 200")
ema_200 = ema(close, len_200)
plot(ema_200, title="EMA 200", color=#fff176, linewidth=3)



// ===  ATR ===
// ==================================================
atrlen = input(14, "ATR Period")
atr_mult = input(1.1, "ATR Mult", step = 0.1)
resis_mult = input(0.205, "ATR Resistance", step = 0.1)
atr_slen = wma(tr(true), atrlen)
atr_upper = atr_slen * atr_mult + close
atr_lower = close - atr_slen * atr_mult
atr_upper_band = atr_slen * resis_mult + close
atr_lower_band = close - atr_slen * resis_mult
plot(show_atr ? atr_upper : na, "+ATR Upper", color=#ffffff, transp=80)
plot(show_atr ? atr_lower : na, "-ATR Lower", color=#ffffff, transp=80)
fill_upper = plot(atr_upper_band, "+ATR Resistance", color=#ffffff, transp=90)
fill_lower = plot(atr_lower_band, "-ATR Resistance", color=#ffffff, transp=90)
fill(fill_upper, fill_lower, color=gray, transp=80)



// === Bollinger on Macd - BSM ===
// ==================================================
SDev = 0.0
bsm_upper_band = 0.0
bsm_lower_band = 0.0
fast = input(8, "Fast Average")
slow = input(26, "Slow Average")
stdv = input(0.8, "Stdv")
m_fast = ema(close,fast)
m_slow = ema(close,slow)
BBMacd = m_fast - m_slow
Avg = ema(BBMacd,9)
SDev := stdev(BBMacd,9)
bsm_upper_band := Avg + stdv * SDev
bsm_lower_band := Avg - stdv * SDev
pcol = BBMacd < bsm_lower_band ? #FF0000 : BBMacd > bsm_upper_band ? #008000 : color.blue
pcol1 = BBMacd < bsm_lower_band ? #FF0000 :  na
pcol2 = BBMacd > bsm_upper_band ? #008000  :  na
//bsm_macd=plot(BBMacd, title='Line Macd BB', color=pcol, linewidth=2, style=plot.style_line, transp=0)
//macd_angle = angle(BBMacd) / 10000
//macd_change = change(BBMacd)
//plot(macd_angle, color=pcol, title="MACD angle")
//plot(macd_change, color=pcol, title="MACD change")



// === Bollinger on Macd - BSM ===
// ==================================================
fastLength = 8 //input(8, minval=1)
slowLength = 16 //input(16,minval=1)
signalLength = 12 //input(12,minval=1)
fastMA = ema(close, fastLength)
slowMA = ema(close, slowLength)
macd = fastMA - slowMA
signal = sma(macd, signalLength)
macd_diff = abs(macd - signal) * 1000
plot(macd_diff,title="MACD Diff",style=plot.style_circles)



// === Schaff Trend Cycle (STC) ===
// ==================================================
stc_signal = 12 // input(12,"Length")
stc_fast = 26 //input(26,"FastLength")
stc_slow = 50 //input(50,"SlowLength")
stc_low = 25
stc_high = 75

stc_macd(BBB, stc_fast, stc_slow) =>
    stc_fastMA = ema(BBB, stc_fast)
    stc_slowMA = ema(BBB, stc_slow)
    stc_macd = stc_fastMA - stc_slowMA
    stc_macd
    
AAAAA(stc_signal, stc_fast, stc_slow) => 
    AAA=input(0.25,"STC Sensitivity")
    var CCCCC = 0.0
    var DDD = 0.0
    var DDDDDD = 0.0
    var EEEEE = 0.0
    tmp_macd = stc_macd(close,stc_fast,stc_slow)     
    lowest = lowest(tmp_macd, stc_signal)
    highest = highest(tmp_macd, stc_signal) - lowest    
    CCCCC := (highest > 0 ? ((tmp_macd - lowest) / highest) * 100 : nz(CCCCC[1])) 
    DDD := (na(DDD[1]) ? CCCCC : DDD[1] + (AAA * (CCCCC - DDD[1]))) 
    DDDD = lowest(DDD, stc_signal) 
    DDDDD = highest(DDD, stc_signal) - DDDD     
    DDDDDD := (DDDDD > 0 ? ((DDD - DDDD) / DDDDD) * 100 : nz(DDDDDD[1])) 
    EEEEE := (na(EEEEE[1]) ? DDDDDD : EEEEE[1] + (AAA * (DDDDDD - EEEEE[1])))
    EEEEE

stc = AAAAA(stc_signal,stc_fast,stc_slow)
stc_color = stc > stc[1] ? color.new(color.green,20) : color.new(color.red,20)
plot(stc,color=stc_color, title="STC",linewidth=2)


// === RSI Chart Bars ===
// ==================================================
var rsi_color = #000000
rsi_len = 14 //input(14, minval=1, title="Length")
rsi_up = rma(max(change(close), 0), rsi_len)
rsi_down = rma(-min(change(close), 0), rsi_len)
rsi = rsi_down == 0 ? 100 : rsi_up == 0 ? 0 : 100 - (100 / (1 + rsi_up / rsi_down))
isup() => rsi > 70
isdown() => rsi < 30
// plot(rsi,title="RSI",color=color.blue)
rsi_color := isup() ? #00ff00 : isdown() ? #ff0000 : na
barcolor( rsi_color)



// === Candlesticks ===
// ==================================================

// Doji
candle_transp = 20
DojiSize = input(0.1, minval=0.01, title="Doji size")
doji=(abs(open - close) <= (high - low) * DojiSize)
plotchar(doji, title="Doji", text='Doji', color=color.white,transp=20)
// Hammer
hammer =(((high - low)>3*(open -close)) and  ((close - low)/(.001 + high - low) > 0.6) and ((open - low)/(.001 + high - low) > 0.6))
plotshape(hammer, title= "Hammer", location=location.belowbar, color=white,transp=20,style=shape.diamond, text="H")
// Inverted Hammer
inverted_hammer =(((high - low)>3*(open -close)) and  ((high - close)/(.001 + high - low) > 0.6) and ((high - open)/(.001 + high - low) > 0.6))
plotshape(inverted_hammer, title= "Inverted Hammer", location=location.belowbar, color=white,transp=20,style=shape.diamond, text="IH")
// Bearish Engulfing
b_engulfing=(close[1] > open[1] and open > close and open >= close[1] and open[1] >= close and open - close > close[1] - open[1] )
//plotshape(b_engulfing,  title= "Bearish Engulfing", color=red, style=shape.arrowdown, text="Bearish\nEngulfing")



entry_signal() =>
	dir = 0
	trading = 0

	// Type of candle - Long / Short
	candle = close > open ? 1 : 0
	wae_limit = 75
	Deadzone = Session(dz_time) ? 1 : 0
	

	// Sell signal
	if (up and candle == 1 and high >= atr_upper_band)
		dir := -1
		trading := 1

		if use_trend and open < ema_200
			dir := na
			trading := 0


	// // Buy signal
	if (down and candle == 0 and low <= atr_lower_band)
		dir := 1
		trading := 1

		// Touching fast ema Filter
		if use_high_greater_fast and high > ema_fast and perc_change < 10
			dir := na
			trading := 0

		// Trend Filter
		if use_trend and open > ema_200
			dir := na
			trading := 0

		// STC Filter
		if use_stc and stc > stc_low and isdown() and perc_change > 5  // and fast_ema_angle < -12
			dir := na
			trading := 0

		// MACD Cross
		if use_macd_cross and ( (signal < macd and macd_diff > 1) or (signal > 0) )
			dir := na
			trading := 0

		// BSM Filter
		if use_bsm and BBMacd[2] > bsm_lower_band[2]
			dir := na
			trading := 0

		if b_engulfing == 1
			dir := na
			trading := 0

		// Change Filter
		// if use_change and perc_change > 5
		// 	dir := na
		// 	trading := 0

		// Doji Filter
		if use_doji and doji == 1 and not isdown()
			dir := na
			trading := 0

		// Hammer Filter
		if use_hammer and hammer == 1
			dir := na
			trading := 0

		// No upper wick
		if open == high
			dir := na
			trading := 0


	// Downtrend Special Candles 
	if (candle == 0 and perc_change > 50) 
	 or (candle == 1 and doji == 1 and hammer == 1 and rsi_color[1] == #ff0000)
		dir := 1
		trading := 1

	// // Buy - long wicks 
	// if (trend < 0 and bbr <= 0.20 and candle == 0) and (use_wicks and low < (bb_sqz_lower + .00010) ) and (close > bb_sqz_lower)
	// 	dir := 1
	// 	trading := 1

	// // Sell - long wicks
	// if (trend > 0 and close <= bb_sqz_upper and candle == 1) and (use_wicks and high > bb_sqz_upper )
	// 	dir := -1
	// 	trading := 1

	// Don't trade during high spread periods
	if use_deadzone and Deadzone == 1
		dir := na
		trading := 0

	[dir, trading]

[trade_dir, trading] = entry_signal() 


// Buy / Sell indicators
bgcolor(Session(dz_time) ? #b71c1c : na, title="Deadzone",transp=85)
plotshape(trade_dir > 0 and show_entry ? 1 : na, title="Entry Buy", color=trade_dir > 0 ? color.orange : color.green, location = location.belowbar, style=shape.labelup, text="B", textcolor=color.white, size=size.small)
plotshape(trade_dir < 0 and show_entry ? -1 : na, title="Entry Sell", color= trade_dir < 0 ? color.orange : color.red, location = location.abovebar, style=shape.labeldown, text="S", textcolor=color.white, size=size.small)
plot(trading ,title="Trading",color= trading ? color.green : color.gray)