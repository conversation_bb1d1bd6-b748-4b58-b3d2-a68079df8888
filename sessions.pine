//@version=4
study("Sessions", overlay=true)

As = input(title="Asia", type=input.session, defval="1800-0300")
Lon = input(title="London", type=input.session, defval="0300-1200")
Ny = input(title="New York", type=input.session, defval="0800-1800")
Dz = input(title="Deadzone", type=input.session, defval="1715-2030")

g_color = "Colors -------------------------------------------------------------"
inl_color = "inl_color"
inl_color2 = "inl_color2"
trans = input(title="Transparency", type=input.float, defval=90.0,group=g_color)

c1_on = input(title="", type=input.bool, defval=true,group=g_color,inline=inl_color)
c1 = input(title="Asia", type=input.color, defval=#00bcd4,group=g_color,inline=inl_color)

c2_on = input(title="", type=input.bool, defval=true,group=g_color,inline=inl_color)
c2 = input(title="London", type=input.color, defval=#00796b,group=g_color,inline=inl_color)

c3_on = input(title="", type=input.bool, defval=true,group=g_color,inline=inl_color2)
c3 = input(title="New York", type=input.color, defval=#b71c1c,group=g_color,inline=inl_color2)

c4_on = input(title="", type=input.bool, defval=true,group=g_color,inline=inl_color2)
c4 = input(title="Deadzone", type=input.color, defval=#b71c1c,group=g_color,inline=inl_color2)

Session(sess) => na(time("2",sess)) == false

Asia = Session(As) and c1_on? c1 : na
London = Session(Lon) and c2_on ? c2 : na
NewYork = Session(Ny) and c3_on ? c3 : na
Deadzone = Session(Dz) and c4_on ? c4 : na
bgcolor(Asia, title="Asia")
bgcolor(London, title="London")
bgcolor(NewYork, title="New York")
bgcolor(Deadzone, title="Deadzone")