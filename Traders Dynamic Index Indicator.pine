//@version=5
//

indicator('Traders Dynamic Index Indicator Alert v1.3 by JustUncleL', shorttitle='TDIALT')

// 
// author: JustUncleL
// date: 1-Mar-2016
// 
// Original code <AUTHOR> for basic TDI indicator.
// If you use this code in its orignal/modified form, do drop me a note. 
//
// Description:
//   This is a Trend following system utilising the Traders Dynamic Index (TDI),
//   Price Action Channel (PAC) and Heikin Ashi candles.
//   About 6months ago I came across the use of TDI in "E.A.S.Y. Method" that I found in
//   forexfactory forums: http://www.forexfactory.com/attachment.php?attachmentid=686629&d=1303831008
//   and I was able to set up a chart based on the specifications by using Kurbelklaus scripts.
//   However, I found that the alerts were being generated one or two bars too late,
//   so I was not successful using it with Binary Options. Later I found a variation of the
//   method in the forums which generates alerts a bit earlier, so this indicator is a
//   variation of that early detection version.
//   The indicator can optionally use Heikin  Ashi candles only for all it's calculations, 
//   I would recommend viewing the chart with normal Heikin Ashi candles, these
//   smooth out the trends and makes them more visible.
//
//   I found that this metod it works OK with currency pairs or commodities.
//   It also seems to work well with 5min+ timeframe charts, 
//   and I would suggest expiry of 2 to 6 candles.
//
//   ALERT GENERATION:
//   =================
//
//   The TDI (Traders Dynamic Index)
//   -------------------------------
//   Volatility Band VB(34), color: Blue, buffer: UpZone, DnZone
//   Relative Strength Index RSI(13)
//   RSI PRICE LINE (2), color: Green, buffer: mab
//   RSI TRADE SIGNAL LINE (7), color: Red, buffer: mbb
//   MARKET BASE LINE MID VB(34), color: Orange, buffer: mid
//
//   Indicator SignalLevels:
//   -----------------------
//   RSI_OversoldLevel : 22 (normally: 32)
//   RSI_OverboughtLevel : 78 (normally: 68)
//
//   Alert Conditions:
//   -----------------
//   Strong Buy (yellow): HIGH>PAC upper && BULL Candle && Candle High>PAC High && RSI>TRADE SIGNAL LINE && RSI>RSI_OversoldLevel && 
//                        && RSI<RSI_OverboughtLevel && TRADE SIGNAL LINE> MARKET BASE LINE
//   Medium Buy (aqua): HIGH>PAC upper && BULL Candle && Candle High>PAC High && RSI>TRADE SIGNAL LINE && RSI>RSI_OversoldLevel 
//                        && RSI<RSI_OverboughtLevel && RSI> MARKET BASE LINE && TRADE SIGNAL LINE< MARKET BASE LINE
//   Weak Buy (blue):   HIGH>PAC upper && BULL Candle && Candle High>PAC High && RSI>TRADE SIGNAL LINE && RSI>RSI_OversoldLevel
//                        && RSI<RSI_OverboughtLevel && TRADE SIGNAL LINE<MARKET BASE LINE && RSI< MARKET BASE LINE
//
//   Strong Sell (fuchsia): LOW<PAC lower && BEAR Candle && Candle Low>PAC Low && RSI<TRADE SIGNAL LINE && RSI>RSI_OversoldLevel 
//                        && RSI<RSI_OverboughtLevel && TRADE SIGNAL LINE< MARKET BASE LINE
//   Medium Sell (purple): LOW<PAC lower && BEAR Candle && Candle Low>PAC Low && RSI<TRADE SIGNAL LINE && RSI>RSI_OversoldLevel 
//                           && RSI<RSI_OverboughtLevel && RSI< MARKET BASE LINE && TRADE SIGNAL LINE> MARKET BASE LINE
//   Weak Sell (black): LOW<PAC lower && BEAR Candle && Candle Low>PAC Low && RSI<TRADE SIGNAL LINE && RSI>RSI_OversoldLevel 
//                        && RSI<RSI_OverboughtLevel && TRADE SIGNAL LINE> MARKET BASE LINE && RSI> MARKET BASE LINE
//
//   HIGH LEVEL FILTER (Overbought): RSI>=RSI_OverboughtLevel or MACD Histogram not green
//   LOW LEVEL FILTER (Oversold): RSI<=RSI_OversoldLevel or MACD Histogram not red
//
//   Hints on How to use:
//   --------------------
//   - When a Medium or Strong alert is generated and MACD histogram colour matches the direction
//     of the alert, then place trade in direction of alert candle and MACD.
//   - Use the multi-Hull MA's for trend direction confirmation.
//   - Best positions occur near the MACD(8,16,1) Histogram crossing the zero line.
//   - The optional coloured Dots along the bottom of the indicator represent the first alert 
//     of this type that was generated in this sequence.
//   - It is advisable to trade in the direction of the main trend as indicated the HULL MA red cloud:
//     if red cloud underneath PAC then BULLISH trend, if red cloud above PAC then BEARISH trend.
//   - Selecting the HeiKin Ashi candles does affect the MACD and MA caculations, so if you select
//     normal candles the result chart will change. Although the TDI calculations and alerts will stay the
//     same.
//   - When using the Heikin Ashi candles, a good buy entry is indicated by long top wick and no bottom wick
//     for bull (green) candles and good sell entry is indicated by long bottom wick and no top wick for
//     bear (red) candles.
//   - When the MACD histogram is flat and close to zero line,
//     this indicates a ranging market, do NOT trade when this occurs.
//   - When the PAC channel on the main chart is spread apart widely, this is an indication
//     of extreme volitity and choppy chart, do NOT try to trade during these timeframe.periods.
//     A choppy chart is also indicated by Heikin Ashi candles with long wicks on both sides
//     of the candles.
//   - You can specify what strength level Alerts are generated (default 2):
//     Level (1) means only generate Strong Alerts only.
//     Level (2) means generate Strong and Medium Alerts.
//     Level (3) means generate Strong, Medium and Weak Alerts.
//
//   Modifications:
//   --------------
//     1.3  7-Aug-2017
//          - Modified Alertcondition code so that it only signals on the last completed alerted
//            candle. This should prevent some false alerts and multiple alerts being generated.
//            When setting alarms I suggest using Frequency "Once per bar (on condition)" to get
//            the earliest signal possible.
//          - Added optional alert long/short spikes that only occur on the first Long or Short
//            signals.
//     1.2  5-Aug-2017:
//          - Added new alerts for Buy and Sell (Long and Short) signals seperately.
//          - Upgraded to version 3 Pinescript.
//          - Added work around patch for opaque bar colouring issue.
//
//     1.1  - Modified code so when viewing Top chart with Heikin Ashi candles, calculations
//            are still based on standard candles, unless Heikin Ashi calculation option selected.
//
//     0.2  - Simplified MACD direction calculation to use just rising/falling.
//     0.1  - Oroginal Version
//   References:
//   -----------
//      Traders Dynamic Index [LazyBear]
//      KK_Traders Dynamic Index_Bar Highlighting by Kurbelklaus
//      KK_Price Action Channel (TDI BH) by Kurbelklaus
//      http://www.forexfactory.com/attachment.php?attachmentid=686629&d=1303831008
//      http://www.forexstrategiesresources.com/trading-system-metatrader-4-iv/504-traders-dynamic-index-how-to-use/
//      http://www.forexfactory.com/showthread.php?t=460148
//      http://www.forexstrategiesresources.com/scalping-forex-strategies-ii/205-scalping-with-tdi-real-macd-stochrainbow/
//
lengthrsi = input(13) // 13
lengthband = input(34) // 34
lengthrsipl = input(2) // 2
lengthtradesl = input(7) // 7
lenH = input.int(5, minval=1, title='Price Action Channel Length') // 5
lenL = lenH
rsiOSL = input.int(22, minval=0, maxval=49, title='RSI Oversold Level') // 22
rsiOBL = input.int(78, minval=51, maxval=100, title='RSI Overbought Level') // 78
strength = input.int(3, minval=1, maxval=3, step=1, title='Strength Level: (1)Strong (2)Medium (3)All') // 3
sgb = input(false, title='Check Box To Turn Bars Gray')
sbr = input(true, title='Highlight TDI Alert Bars')
sal = input(true, title='Show Alert Condition Status')
uha = input(true, title='Use Heikin Ashi Candles for Calculations')
sspikes = input(false, title='Show Spike LONG and SHORT Signals')
//

// Constants colours that include fully non-transparent option.
blue100 = #0000FFFF
aqua100 = #00FFFFFF
fuchsia100 = #FF00FFFF
purple100 = #800080FF
gray100 = #808080FF
gold100 = #FFD700FF
white100 = #FFFFFFFF
black100 = #000000FF
gold = #FFD700

red = #FF0000
orange = #ff9800
yellow = #FFFF00
green = #008000
lime = #50e600
aqua = #00FFFF77
blue = #0000FFFF
dark_blue = #2962ff
violet = #814dff
fuchsia = #FF00FFFF
purple = #800080FF
white = #ffffff
gray = #808080FF
black = #000000


// Use only Heikinashi Candles for all calculations or use Standard Candles for calculations.
srcClose = uha ? request.security(ticker.heikinashi(syminfo.tickerid), timeframe.period, close) : request.security(syminfo.ticker, timeframe.period, close)
srcOpen = uha ? request.security(ticker.heikinashi(syminfo.tickerid), timeframe.period, open) : request.security(syminfo.ticker, timeframe.period, open)
srcHigh = uha ? request.security(ticker.heikinashi(syminfo.tickerid), timeframe.period, high) : request.security(syminfo.ticker, timeframe.period, high)
srcLow = uha ? request.security(ticker.heikinashi(syminfo.tickerid), timeframe.period, low) : request.security(syminfo.ticker, timeframe.period, low)
//
r = ta.rsi(srcClose, lengthrsi)
ma = ta.sma(r, lengthband)
offs = 1.6185 * ta.stdev(r, lengthband)
upZone = ma + offs
dnZone = ma - offs
mid = (upZone + dnZone) / 2
mab = ta.sma(r, lengthrsipl)
mbb = ta.sma(r, lengthtradesl)
//
hline(rsiOSL, color=red, linewidth=1)
hline(50, color=gray, linewidth=1)
hline(rsiOBL, color=lime, linewidth=1)
// Plot the TDI
//i_use_smoothing = input.int
smooth_len = input.int(3,title="Smooth Length") // 3 20
upl = plot(ta.sma(upZone,smooth_len), color=aqua, title='VB Channel High', linewidth=1)
dnl = plot(ta.sma(dnZone,smooth_len), color=aqua, title='VB Channel Low', linewidth=1)
midl = plot(mid, color=color.new(orange, 0), linewidth=1, title='MBL')
mabl = plot(mab, color=color.new(green, 0), linewidth=1, title='RSI PL')
mbbl = plot(mbb, color=color.new(red, 0), linewidth=1, title='TSL Signal')
//
//create RSI TSL cloud to indicate trend direction.
fill(mabl, mbbl, color=mab > mbb ? green : red, transp=90)

// Calculate Price Action Channel (PAC)
smmaH = 0.0
smmaL = 0.0
smmaH := na(smmaH[1]) ? ta.sma(srcHigh, lenH) : (smmaH[1] * (lenH - 1) + srcHigh) / lenH
smmaL := na(smmaL[1]) ? ta.sma(srcLow, lenL) : (smmaL[1] * (lenL - 1) + srcLow) / lenL
//
umacd = input(false, title='Use MACD Filtering')
fastMA = input.int(title='MACD Fast MA Length', defval=8, minval=2)
slowMA = input.int(title='MACD Slow MA Length', defval=16, minval=7)
signal = input.int(title='MACD Signal Length', defval=1, minval=1)
//
//
[currMacd, _, _] = ta.macd(srcClose[0], fastMA, slowMA, signal)
macdH = currMacd > 0 ? ta.rising(currMacd, 2) ? green : red : ta.falling(currMacd, 2) ? red : green

//
// Bar - Highlighting  based on indication strength
long = (not umacd or macdH == green) and mab > mbb and mab < rsiOBL and mab > rsiOSL and srcHigh > smmaH and srcClose > srcOpen ? mbb > mid ? 1 : mab > mid and mbb < mid ? 2 : mab < mid and mbb < mid ? 3 : 0 : 0
short = (not umacd or macdH == red) and mab < mbb and mab < rsiOBL and mab > rsiOSL and srcLow < smmaL and srcClose < srcOpen ? mbb < mid ? 1 : mab < mid and mbb > mid ? 2 : mab > mid and mbb > mid ? 3 : 0 : 0
//
// Find the right Bar colour if enabled.
bcolor = not sbr ? na : long == 1 ? gold100 : long == 2 and strength > 1 ? aqua100 : long == 3 and strength > 2 ? blue100 : short == 1 ? fuchsia100 : short == 2 and strength > 1 ? purple100 : short == 3 and strength > 2 ? black100 : sgb ? gray100 : na
//
barcolor(color=bcolor, title='Bars Colours')
//
//
// create alerts only once per sequence type.
//
long_ = long > 0 and long != long[1] and long <= strength
short_ = short > 0 and short != short[1] and short <= strength
c_alert = long_ or short_
alertcondition(c_alert[1] and barstate.isnew, title='TDIALT Alert', message='TDIALT Alert')
alertcondition(long_[1] and barstate.isnew, title='TDIALT Long Alert', message='TDIALT Long')
alertcondition(short_[1] and barstate.isnew, title='TDIALT Short Alert', message='TDIALT Short')

// Single Alert Strategy
stateLong = 0
stateShort = 0
stateLong_ = nz(stateLong[1])
stateShort_ = nz(stateShort[1])

//Count the long signals before next short
if long[1] > 0 and long[1] <= strength
    stateLong_ := stateLong_ > 0 ? stateLong_ + 1 : 1
    stateShort_ := 0
    stateShort_

//Count the short signals before next long.    
if short[1] > 0 and short[1] <= strength
    stateShort_ := stateShort_ > 0 ? stateShort_ + 1 : 1
    stateLong_ := 0
    stateLong_

//create spikes, hide them by setting transp=100
plot(sspikes ? stateLong_ == 1 ? 100 : 0 : na, color=blue, title='LONG', transp=50)
plot(sspikes ? stateShort_ == 1 ? 100 : 0 : na, color=color.new(red, 50), title='SHORT')

//keep current state counts.
stateLong := stateLong_ > 0 ? stateLong_ + 1 : 0
stateShort := stateShort_ > 0 ? stateShort_ + 1 : 0


//
// show dot only when alert condition is met and bar closed.
plotshape(sal and c_alert[1], title='Alert Indicator', location=location.bottom, color=long[1] == 1 ? gold : long[1] == 2 ? aqua : long[1] == 3 ? blue : short[1] == 1 ? fuchsia : short[1] == 2 ? purple : short[1] == 3 ? black : na, style=shape.circle, offset=-1, transp=0)
//
//EOF

