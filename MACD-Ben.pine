//@version=5
indicator(title="MACD - Ben Copy")

// Calculate Multi Timeframes
newbar(res) => ta.change(time(res)) == 0 ? 0 : 1

// === MACD MULTI ===
// ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ //
g_macd = 'MACD Fast -------------------------------------------------------------'
macd_show_1     = input.bool(true,'MACD Fast', group=g_macd)
macd_time_1     = input.timeframe('30', "Resolution", group=g_macd) // 1 hour
macd_fast_1     = input(title="Fast Length", defval=26, group=g_macd) // 12
macd_slow_1     = input(title="Slow Length", defval=100, group=g_macd) // 26
macd_signal_1   = input.int(title="signal_1 Smoothing",  minval = 1, maxval = 50, defval = 9, group=g_macd) // 9
macd_type_1     = input.string(title="Oscillator MA Type",  defval="EMA", options=["SMA", "EMA"], group=g_macd)
macd_sig_type_1 = input.string(title="Signal 1 Line MA Type", defval="EMA", options=["SMA", "EMA"], group=g_macd)
macd_barcolor_1 = input.bool(false,'Bar Color 1', group=g_macd)

g_macd2 = 'MACD Slow  -------------------------------------------------------------'
macd_show_2     = input.bool(false,'MACD Slow', group=g_macd2)
macd_time_2     = input.timeframe('180', "Resolution", group=g_macd2) // 1 hour
macd_fast_2     = input(title="Fast Length", defval=26, group=g_macd2) // 12
macd_slow_2     = input(title="Slow Length", defval=100, group=g_macd2) // 26
macd_signal_2   = input.int(title="signal_1 Smoothing",  minval = 1, maxval = 50, defval = 9, group=g_macd2) // 9
macd_type_2     = input.string(title="Oscillator MA Type",  defval="EMA", options=["SMA", "EMA"], group=g_macd2)
macd_sig_type_2 = input.string(title="signal_1 Line MA Type", defval="EMA", options=["SMA", "EMA"], group=g_macd2)
macd_barcolor_2 = input.bool(false,'Bar Color 2', group=g_macd2)

// Plot colors
g_macd_col = 'Colors  -------------------------------------------------------------'
col_macd        = input(#2962FF, "MACD", inline="MACD", group=g_macd_col)
col_signal      = input(#FF6D00, "Signal", inline="signal_1", group=g_macd_col)
macd_grow_above = input(#075d55, "Above Grow", inline="Above", group=g_macd_col)
macd_fall_above = input(#bff1ed, "Fall", inline="Above", group=g_macd_col)
macd_grow_below = input(#FFCDD2, "Below Grow", inline="Below", group=g_macd_col)
macd_fall_below = input(#FF5252, "Fall", inline="Below", group=g_macd_col)

// Calculating
f_macd(type, fast, slow, sig_type, signal_len)=>

    fast_ma = type == "SMA" ? ta.sma(close, fast) : ta.ema(close, fast)
    slow_ma = type == "SMA" ? ta.sma(close, slow) : ta.ema(close, slow)
    macd = fast_ma - slow_ma
    signal = sig_type == "SMA" ? ta.sma(macd, signal_len) : ta.ema(macd, signal_len)
    hist = macd - signal

    [macd,signal,hist]

// MACD Fast
[macd_1, signal_1, hist_1] = request.security(syminfo.tickerid, macd_time_1, f_macd(macd_type_1, macd_fast_1, macd_slow_1, macd_sig_type_1, macd_signal_1  ) )
if newbar(macd_time_1) == 0
    macd_1    := macd_1[1]
    signal_1  := signal_1[1]
    hist_1    := hist_1[1]

var color hist_color_1 = #000000
if hist_1>hist_1[1] or hist_1<hist_1[1]
    hist_color_1 := (hist_1>=0 ? (hist_1[1] < hist_1 ? macd_grow_above : macd_fall_above) : (hist_1[1] < hist_1 ? macd_grow_below : macd_fall_below) ) 

hline(0, "Zero Line", color=color.new(#787B86, 50))
plot(macd_show_1 ? macd_1: na, title="MACD", color=col_macd)
plot(macd_show_1 ? signal_1: na, title="signal_1", color=col_signal)
plot(macd_show_1 ? hist_1: na, title="Histogram", style=plot.style_columns, color=hist_color_1 )
barcolor(macd_show_1 and macd_barcolor_1 ? hist_color_1 : na)

// MACD Slow
[macd_2, signal_2, hist_2] = request.security(syminfo.tickerid, macd_time_2, f_macd(macd_type_2, macd_fast_2, macd_slow_2, macd_sig_type_2, macd_signal_2 ) )
if newbar(macd_time_2) == 0
    macd_2    := macd_2[1]
    signal_2  := signal_2[1]
    hist_2    := hist_2[1]

var color hist_color_2 = #000000
if hist_2>hist_2[1] or hist_2<hist_2[1]
    hist_color_2 := (hist_2>=0 ? (hist_2[1] < hist_2 ? macd_grow_above : macd_fall_above) : (hist_2[1] < hist_2 ? macd_grow_below : macd_fall_below) ) 

plot(macd_show_2 ? macd_2: na, title="MACD 2", color=col_macd)
plot(macd_show_2 ? signal_2: na, title="Signal 2", color=col_signal)
plot(macd_show_2 ? hist_2: na, title="Histogram 2", style=plot.style_columns, color=hist_color_2 )
barcolor(macd_show_2 and macd_barcolor_2 ? hist_color_2 : na)
