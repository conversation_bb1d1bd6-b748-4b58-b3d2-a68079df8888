//@version=5
indicator("PD Retrace", overlay=true, max_labels_count = 500)

g_pd = 'PD Retrace -----------------------------------------------------------'
i_pd_fibo = input.bool(true, title='Show Fibo', group=g_pd)
i_pd_timeframe = input.timeframe('D', "Fibo Resolution", group=g_pd) // Daily
i_pd_lookback = input.int(1, title='Lookback', group=g_pd)
i_pd_labels = input.bool(false, title="Show Labels", group=g_pd)
i_pd_fills = input.bool(true, title='Fill in Fibo Levels', group=g_pd)
i_pd_fills_trans = input.int(90, title='Fill Transparency', group=g_pd)

red = #ff0062
orange = #ff9800
yellow = #FFFF00
green = #4caf50
lime = #50e600
aqua = #00bcd4
blue = #2962ff
dark_blue = #2962ff
violet = #814dff
white = #ffffff
gray = #707070
black = #000000

angle(_src, len) =>
    rad2degree=180/3.14159265359  //pi 
    ang=rad2degree*math.atan((_src[0] - _src[1]) / ta.atr(len))

// SYM Info
min_tick = syminfo.mintick
var int decimals = int(math.log10(1/min_tick))
decimals := decimals == 2 ? 1 : decimals == 0 ? 1 : decimals
// plot(min_tick, title='Min Tick')
// plot(decimals, title='Decimals')

get_pip_distance(point1, point2, abs_value) =>
    diff_points = abs_value ? math.abs( (point1 - point2) ) : point1 - point2
    pip_value = decimals<3 ? diff_points / syminfo.mintick / 100 : diff_points / syminfo.mintick / 10
    //pip_value = decimals>3 ? diff_points * (math.pow(10, decimals) * syminfo.mintick) : diff_points / syminfo.mintick / 10



// FIBO LEVELS
// -----------------------------------------------------
close_m = request.security(syminfo.tickerid, i_pd_timeframe, close, lookahead=barmerge.lookahead_on)
fibo0   = request.security(syminfo.tickerid, i_pd_timeframe, high[i_pd_lookback], lookahead=barmerge.lookahead_on)
fibo100 = request.security(syminfo.tickerid, i_pd_timeframe, low[i_pd_lookback], lookahead=barmerge.lookahead_on)
fibo23  = (fibo100-fibo0)*0.786+fibo0
fibo38  = (fibo100-fibo0)*0.618+fibo0
fibo50  = (fibo100-fibo0)/2+fibo0
fibo62  = (fibo100-fibo0)*0.382+fibo0
fibo78  = (fibo100-fibo0)*0.236+fibo0

// Fibo Plots
p_fib0 = plot(i_pd_fibo ? fibo0 : na ,title='Fibo 0',color=color.new(color.red,50),linewidth=2)
p_fibo100 = plot(i_pd_fibo ? fibo100 : na ,title='Fibo 100',color=color.new(color.green,50),linewidth=2)
p_fibo23  = plot(i_pd_fibo ? fibo23 : na,title='Fibo 23',color=color.new(color.gray,50) )
p_fibo38  = plot(i_pd_fibo ? fibo38 : na,title='Fibo 38',color=color.new(color.gray,50) )
p_fibo50  = plot(i_pd_fibo ? fibo50 : na,title='Fibo 50',color=color.new(color.gray,50) )
p_fibo62  = plot(i_pd_fibo ? fibo62 : na,title='Fibo 62',color=color.new(color.gray,50) )
p_fibo78  = plot(i_pd_fibo ? fibo78 : na,title='Fibo 78',color=color.new(color.gray,50) )

f_get_level() =>
    var int level = 0
    // Above Red
    if close>fibo0 
        level := 8
    // Red 
    if close<fibo0 and close>fibo78
        level := 7
    // Orange 
    if close<fibo78 and close>fibo62
        level := 6
    // Yellow
    if close<fibo62 and close>fibo50
        level := 5
    // Aqua
    if close<fibo50 and close>fibo38
        level := 4
    // Green
    if close<fibo38 and close>fibo23
        level := 3
    // Lime
    if close<fibo23 and close>fibo100
        level := 2
    // Below Lime
    if close<fibo100
        level := 1

    level

//

fill(p_fibo78, p_fib0,title='Fill 100',color=i_pd_fills? color.new(red,i_pd_fills_trans) : na )
fill(p_fibo62, p_fibo78,title='Fill 78',color=i_pd_fills? color.new(orange,i_pd_fills_trans) : na )
fill(p_fibo50, p_fibo62,title='Fill 62',color=i_pd_fills? color.new(yellow,i_pd_fills_trans) : na )
fill(p_fibo50, p_fibo38,title='Fill 38',color=i_pd_fills? color.new(aqua,i_pd_fills_trans) : na )
fill(p_fibo38, p_fibo23,title='Fill 23',color=i_pd_fills? color.new(green,i_pd_fills_trans) : na )
fill(p_fibo23, p_fibo100,title='Fill 0',color=i_pd_fills? color.new(lime,i_pd_fills_trans) : na )

var int p_fb0_ch = 0
var int p_fb100_ch = 0

if ta.change(fibo0) 
    p_fb0_ch := fibo0 > fibo0[1] ? 1 : 0
    p_fb100_ch := fibo100 > fibo100[1] ? 1 : 0

bool f_higher = p_fb0_ch==1 and p_fb100_ch == 1 ? true : false
bool f_lower  = p_fb0_ch==0 and p_fb100_ch == 0 ? true : false
bool f_diff   = (p_fb0_ch==1 and p_fb100_ch==0) or (p_fb0_ch==0 and p_fb100_ch==1) ? true : false

plotshape(ta.change(fibo0) and f_higher and i_pd_labels ? 1 : na,title="Higher Highs",color=green ,style=shape.circle,location=location.bottom, size=size.tiny)
plotshape(ta.change(fibo0) and f_lower and i_pd_labels ? 1 : na,title="Lower Lows",color=red ,style=shape.circle,location=location.bottom, size=size.tiny)
plotshape(ta.change(fibo0) and f_diff and i_pd_labels ? 1 : na,title="Lower Lows",color=blue ,style=shape.circle,location=location.bottom, size=size.tiny)

//fill(p_fib0,p_fibo100, color=#0a111c)

diff_fibo0 = ta.change(fibo0,1) 
diff_fibo100 = ta.change(fibo100,1)  


if ta.change(fibo0) and i_pd_labels

    new_level = f_get_level()

    higher = fibo0 > fibo0[1] ? "Higher: " + str.tostring(diff_fibo0)  : fibo0 < fibo0[1] ? "Lower: " + str.tostring(diff_fibo0) : "No Change: " + str.tostring(diff_fibo0)
    
    txt1 = str.tostring(get_pip_distance(fibo0, fibo100, false)) + "\nLevel: " + str.tostring(new_level)
    info1 = label.new(x=time,y=fibo0,xloc=xloc.bar_time, text=txt1, textcolor=#ffffff)

    lower = fibo100 > fibo100[1] ? "Higher: " + str.tostring(diff_fibo100)  : fibo100 < fibo100[1] ? "Lower: " + str.tostring(diff_fibo100) : "No Change: " + str.tostring(diff_fibo100)
    txt2 = str.tostring(lower) + "\nLevel: " + str.tostring(new_level)
    info2 = label.new(x=time,y=fibo100,xloc=xloc.bar_time, text=txt2, textcolor=#ffffff, style=label.style_label_up)

// fill(p_fibo38,p_fibo50, color=color.aqua)
// fill(p_fibo50,p_fibo62, color=color.yellow)



// SESSIONS
// -----------------------------------------------------
Session(sess) => na(time("2",sess)) == false

g_sessions = 'Sessions -----------------------------------------------------------'
i_pd_GMT = input.string(title='GMT', defval='GMT-10', options=['GMT-10', 'GMT-9', 'GMT-8', 'GMT-7', 'GMT-6', 'GMT-5', 'GMT-4', 'GMT-3', 'GMT-2', 'GMT-1', 'GMT-0', 'GMT+1', 'GMT+2', 'GMT+3', 'GMT+4', 'GMT+5', 'GMT+6', 'GMT+7', 'GMT+8', 'GMT+9', 'GMT+10', 'GMT+11', 'GMT+12', 'GMT+13'] , group=g_sessions)
i_show_nt = input.bool(false, title='Show No Trade', group=g_sessions)
i_pd_no_trading = input.session(title="No Trading Hours", defval="1145-1315", group=g_sessions)
i_show_tr = input.bool(false, title='Show Session', group=g_sessions)
i_pd_session  = input.session(title="Custom Session", defval="1400-1600", group=g_sessions)
i_show_sessions = input.bool(false, title='Show Sessions', group=g_sessions)
As  = input.session(title="Asia", defval="1800-0300", group=g_sessions)
Lon = input.session(title="London", defval="0300-1200", group=g_sessions)
Ny  = input.session(title="New York", defval="0800-1800", group=g_sessions)
//Dz  = input.session(title="Deadzone - High Spreads", defval="1645-1830", group=g_sessions)
//i_pd_time = input.time('20 Jul 2021 00:00 +00000', title='Time', group=g_sessions) // 1 hour 4 hour

inl_color = "inl_color"
inl_color2 = "inl_color2"
c1_on = true //input.bool(true,title="", group=g_trading,inline=inl_color)
c1 = color.new(#00bcd4,90) //input.color(title="Asia", defval=#00bcd4,group=g_trading,inline=inl_color)
c2_on = true //input.bool(true,title="",group=g_trading,inline=inl_color)
c2 = color.new(#00796b,90) //input.color(title="London", defval=#00796b,group=g_trading,inline=inl_color)
c3_on = true //input.bool(true,title="",group=g_trading,inline=inl_color2)
c3 = color.new(#b71c1c,90) //input.color(title="New York", defval=#b71c1c,group=g_trading,inline=inl_color2)
c4_on = false //input.bool(true,title="", group=g_trading,inline=inl_color2)
c4 = color.new(color.purple,90) //input.color(title="Deadzone", defval=#b71c1c,group=g_trading,inline=inl_color2)

// Set the start of day
start_time = Session(i_pd_session)
timerange = time(timeframe.period, i_pd_session, i_pd_GMT) and i_show_tr
no_trading = time(timeframe.period, i_pd_no_trading, i_pd_GMT) and i_show_nt 

Asia = Session(As) and c1_on and i_show_sessions? c1 : na
London = Session(Lon) and c2_on and i_show_sessions ? c2 : na
NewYork = Session(Ny) and c3_on and i_show_sessions ? c3 : na
//Deadzone = Session(Dz) and c4_on and i_show_sessions ? c4 : na
bgcolor(i_show_tr and timerange ? color.new(white,85) : na)
bgcolor(i_show_nt and no_trading ? color.new(red,85) : na)
bgcolor(Asia, title='Asian')
bgcolor(London, title='London')
bgcolor(NewYork, title='NewYork')
//bgcolor(Deadzone, title='Deadzone')
//plotshape(ta.change(start_time ) ? 1 : na,title="Start Time",color=green ,style=shape.circle,location=location.bottom, size=size.tiny)
// fill(p_fib0,p_fibo100,color=timerange ? color.new(white,85) : na )
// fill(p_fib0,p_fibo100,color=no_trading ? color.new(red,50) : na )




// MOVING AVERAGES
// -----------------------------------------------------
newbar(res) => ta.change(time(res)) == 0 ? 0 : 1

g_pd_ma = 'Moving Averages -----------------------------------------------------------'
i_pd_ma_timeframe = input.timeframe('360', "MA Resolution", group=g_pd_ma) // 1 hour 6 hour
i_show_ma = input.bool(false, title='Show MAs', group=g_pd_ma)
i_ma_len1 = input.int(5, title='MA length', group=g_pd_ma)
i_ma_len2 = input.int(10, title='MA length 2', group=g_pd_ma)
i_tmp_look = input.int(1,title='temp lookback', group=g_pd_ma)

var float m1 = 0.0
var float m1_a = 0.0
var float m2 = 0.0
var float m2_a = 0.0

change = newbar(i_pd_ma_timeframe)
m1 := change ? request.security(syminfo.tickerid, i_pd_ma_timeframe, ta.hma(close,i_ma_len1) ) : m1[1]
m1_a := change ? request.security(syminfo.tickerid, i_pd_ma_timeframe, angle(m1,14) ) : m1_a[1]
m2 := change ? request.security(syminfo.tickerid, i_pd_ma_timeframe, ta.hma(close,i_ma_len2) ) : m2[1]
m2_a := change ? request.security(syminfo.tickerid, i_pd_ma_timeframe, angle(m2,14) ) : m2_a[1]
// m1 := not change ? m1[1] : request.security(syminfo.tickerid, i_pd_ma_timeframe, ta.hma(close,i_ma_len1) ) 
// m1_a := not change ? m1_a[1] :request.security(syminfo.tickerid, i_pd_ma_timeframe, angle(m1,14) )
// m2 := not change ?  m2[1] :request.security(syminfo.tickerid, i_pd_ma_timeframe, ta.hma(close,i_ma_len2) )
// m2_a := not change ? m2_a[1] : request.security(syminfo.tickerid, i_pd_ma_timeframe, angle(m2,14) )

p_m1 = plot(i_show_ma ? m1 : na ,color=m1_a>0?aqua:orange,linewidth=2)
p_m2 = plot(i_show_ma ? m2 : na ,color=m2_a>0?green:red,linewidth=2)
//p_m1a = plot(m1_a,color=color.new(color.blue,100))


m1_cond = close<m1 and m2_a>0 and close<open ? 1 : 0
barssince = ta.barssince(m1_cond == 1)
//plot(barssince,title='Bars Since',color=color.new(color.blue,100))
//plotshape(m1_cond,title="M1",color=#00ff00 ,style=shape.circle,location=location.bottom)

m2_cond = close<m2 and m2_a>0 and close<open ? 1 : 0
//plotshape(m2_cond,title="M2",color=#00ff00 ,style=shape.circle,location=location.bottom)