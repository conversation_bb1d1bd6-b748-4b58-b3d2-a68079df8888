//@version=4
study(title = "Kijun sen on Bollinger Bands", shorttitle="Kijun+BB",overlay=true)

show_entry = input(title="Show Entry", type=input.bool, defval=true)
show_atr = input(title="Show ATR", type=input.bool, defval=false)
show_plots = input(title="Show Plots", type=input.bool, defval=false)
show_bb= input(title="Show BB", type=input.bool, defval=true)

red = #ff0062
aqua = #00bcd4
yellow = #FFFF00
orange = #ff9800
green = #4caf50
lime = #00E676
white = #ffffff
blue = #42a5f5
gray = #707070
sell_color = color.new(#ff0062,20)
buy_color = color.new(#00c3ff,20)

angle(_src, len) =>
    rad2degree=180/3.14159265359  //pi 
    ang=rad2degree*atan((_src[0] - _src[1])/atr(len))

// Change
perc_change() =>
    perc = abs( (1 - (close[1] / close)) * 10000 )


// ===  EMA's ===
// ==================================================
ema_200 = ema(close, 200)
ema_200_angle = angle(ema_200,2)
// ema_200_dev = 0.27 * stdev(close, 200)
// ema_200_upper = ema_200 + ema_200_dev
// ema_200_lower = ema_200 - ema_200_dev
// ema_200_outer_dev = 0.23 * stdev(close, 200)
// ema_200_outer_upper = ema_200_upper + ema_200_outer_dev
// ema_200_outer_lower = ema_200_lower - ema_200_outer_dev
ema_len_f = input(5, minval=1, title="EMA Length") //6
ema_fast = ema(close, ema_len_f)
ema_angle = angle(ema_fast,2)


// ===  ATR ===
// ==================================================
atrlen = 14 //input(14, "ATR Period")
atr_mult = input(1.15, "ATR Mult", step = 0.1) // 1.15
atr_stop = input(0.0001, "ATR Stop", step = 0.0001) // 0.001
atr_slen = wma(tr(true), atrlen)
atr_upper = atr_slen * atr_mult + close
atr_lower = close - atr_slen * atr_mult


// ===  Kijun ===
// ==================================================
middleDonchian(Length) =>
    lower = lowest(Length)
    upper = highest(Length)
    avg(upper, lower)

basePeriods = input(14, minval=1) //26
kijun =  middleDonchian(basePeriods)
k_angle = angle(kijun,3)
xChikou = close
xPrice = close


// ===  SSL ===
// ==================================================
ssl_len = input(60, minval=1,title="SSL Len") //75
ssl_len2 = input(45, minval=1,title="SSL 2")
SSL = wma(2*wma(close, ssl_len/2)-wma(close, ssl_len), round(sqrt(ssl_len)))
SSL2 = wma(2*wma(close, ssl_len2/2)-wma(close, ssl_len2), round(sqrt(ssl_len2)))
SSL3 = wma(2*wma(close, 7/2)-wma(close, 7), round(sqrt(7)))
//ssl_angle = angle(SSL,3)
ssl_angle = angle(SSL2,2)
ssl_angle2 = angle(SSL3,2)


// ===  BB ===
// ==================================================
BB_length = input(45, minval=1, maxval=150) // 23
BB_stdDev = input(2, minval=2.0, maxval=3)
sqz_length = input(100, minval=2, maxval=200) // 100
sqz_threshold = 76 
bb_s = kijun
basis = sma(bb_s, BB_length)
dev = BB_stdDev * stdev(bb_s, BB_length)
bb_upper = basis + dev
bb_lower = basis - dev
bb_spread = bb_upper - bb_lower
avgspread = sma(bb_spread, sqz_length)
bb_squeeze = 0.00
bb_squeeze := bb_spread / avgspread * 100
basis_angle = angle(basis,3)
bbu_angle = angle(bb_upper,3)
bbl_angle = angle(bb_lower,3)
// BB Zones
bb_zone = bb_squeeze < 53 ? 0 : 
 bb_squeeze < sqz_threshold ? 1 : 
 bb_squeeze < 120 ? 2 :
 bb_squeeze < 181 ? 3 :
 bb_squeeze > 181 ? 4 : na
sqz_color = bb_zone == 0 ? #0045b3 :
 bb_zone == 1 ? #ff0062 : 
 bb_zone == 2 ?  gray : 
 bb_zone == 3 ?  #00c3ff : 
 bb_zone == 4 ? white: na

bb_zones_color =  sqz_color



// === RSI ===
// ==================================================
rsi_len = input(14, minval=1, title="Length")
up = rma(max(change(close), 0), rsi_len)
down = rma(-min(change(close), 0), rsi_len)
rsi = down == 0 ? 100 : up == 0 ? 0 : 100 - (100 / (1 + up / down))

// band1 = hline(70, "Upper Band", color=#C0C0C0)
// bandm = hline(50, "Middle Band", color=color.new(#C0C0C0, 50))
// band0 = hline(30, "Lower Band", color=#C0C0C0)
// fill(band1, band0, color=#9915FF, transp=90, title="Background")


// === MACD Dema === 
// ==================================================
dema_sma = input(7,title='DEMA Short') // 12
dema_lma = input(45,title='DEMA Long') // 26
dema_signal_in = input(13,title='Signal') // 7 // 9 
MMEslowa = ema(close,dema_lma)
MMEslowb = ema(MMEslowa,dema_lma)
DEMAslow = ((2 * MMEslowa) - MMEslowb )
MMEfasta = ema(close,dema_sma)
MMEfastb = ema(MMEfasta,dema_sma)
DEMAfast = ((2 * MMEfasta) - MMEfastb)
dema_line = (DEMAfast - DEMAslow)
MMEsignala = ema(dema_line, dema_signal_in)
MMEsignalb = ema(MMEsignala, dema_signal_in)
dema_signal = ((2 * MMEsignala) - MMEsignalb )
dema_histo = (dema_line - dema_signal)
dema_up = dema_histo > 0 ? 1 : 0
dema_down = dema_histo < 0 ? 1 : 0
dema_angle = angle(dema_signal,3)


// === MACD === 
// ==================================================
// fast_length = 12
// slow_length = 26
// signal_length = 9
// sma_source = false
// sma_signal = false
// // Plot colors
// c_up = #26A69A // teal
// c_up_weak = #B2DFDB // light teal
// c_down = #EF5350 // peach
// c_down_weak = #FFCDD2 // pink
// col_macd = #0094ff
// col_signal = #ff6a00
// // Calculating
// fast_ma = sma_source ? sma(close, fast_length) : ema(close, fast_length)
// slow_ma = sma_source ? sma(close, slow_length) : ema(close, slow_length)
// macd = fast_ma - slow_ma
// m_signal = sma_signal ? sma(macd, signal_length) : ema(macd, signal_length)
// m_hist = macd - m_signal
// m_color = (m_hist>=0 ? (m_hist[1] < m_hist ? c_up : c_up_weak) : (m_hist[1] < m_hist ? c_down_weak : c_down) )



// === Bollinger Bands %B ===
// ==================================================
length = 20 //input(45, minval=1)
multi = 2 //input(2.0, minval=0.001, maxval=50, title="StdDev")
bbr_basis = sma(close, length)
deviation = multi * stdev(close, length)
bbr_upper = bbr_basis + deviation
bbr_lower = bbr_basis - deviation
bbr = (close - bbr_lower)/(bbr_upper - bbr_lower)




// === Stochastic RSI ===
// ==================================================
smoothK = 3 //input(3, "K", minval=1)
smoothD = 3 //input(3, "D", minval=1)
lengthRSI = 14 //input(14, "RSI Length", minval=1)
lengthStoch = 14 //input(14, "Stochastic Length", minval=1)
rsi1 = rsi(close, lengthRSI)
stoch_k = sma(stoch(rsi1, rsi1, rsi1, lengthStoch), smoothK)
stoch_d = sma(stoch_k, smoothD)
stoch_hi = 80
stoch_low = 20



// === Stochastic Slow ===
// ==================================================
slow_len_k = input(14, minval=1), 
slow_len_d = input(3, minval=1)
slow_k = sma(stoch(close, high, low, slow_len_k), 3)
slow_d = sma(slow_k, slow_len_d)
slow_hi = 80
slow_low = 20




// === MACD Crossover === 
// ==================================================
fastLength = input(8, minval=1) // 8 //8
slowLength = input(25,minval=1) // 16 // 21
signalLength=input(9,minval=1) // 11 // 5
fastMA = ema(close, fastLength)
slowMA = ema(close, slowLength)
macd_c = fastMA - slowMA
sig_c = sma(macd_c, signalLength)
pos = 0
pos := iff(sig_c < macd_c , 1,iff(sig_c > macd_c, -1, nz(pos[1], 0))) 
mc_color = pos == -1 ? red: pos == 1 ? green : blue
mc_angle = angle(macd_c,2)
s_angle = angle(sig_c,2)
mc_diff = macd_c / sig_c
//mcu_line = 0.00170
//mcl_line = -0.000910 //-0.00105
currency = syminfo.currency
mch_line = 0.00420
mcu_line = syminfo.currency == 'NZD' ? 0.00170 : syminfo.currency == 'JPY' ? 0.50 : 0.12
mcl_line = syminfo.currency == 'NZD' ? -0.00170 : -0.50



// === ASH ===
// ==================================================
Length = input(9,title="Period of Evaluation", type=input.integer)
Smooth = input(3,title="Period of Smoothing", type=input.integer)
show_histo = input(true, title="Show Histogam", type=input.bool)
//----
src =  input(close,title="Source")
Mode = input(title="Indicator Method", type=input.string, defval="ADX", options=["RSI", "STOCHASTIC","ADX"]) // RSI
ma_type = input(title="MA", type=input.string, defval="EMA", options=["ALMA", "EMA", "WMA", "SMA", "SMMA", "HMA"]) //WMA 
alma_offset  = input(defval=0.85, title="* Arnaud Legoux (ALMA) Only - Offset Value", minval=0, step=0.01)
alma_sigma   = input(defval=6, title="* Arnaud Legoux (ALMA) Only - Sigma Value", minval=0)

ma(type, src, len) =>
    float result = 0
    if type=="SMA" // Simple
        result := sma(src, len)
    if type=="EMA" // Exponential
        result := ema(src, len)
    if type=="WMA" // Weighted
        result := wma(src, len)
    if type=="SMMA" // Smoothed
        w = wma(src, len)
        result := na(w[1]) ? sma(src, len) : (w[1] * (len - 1) + src) / len
    if type=="HMA" // Hull
        result := wma(2 * wma(src, len / 2) - wma(src, len), round(sqrt(len)))
    if type=="ALMA" // Arnaud Legoux
        result := alma(src, len, alma_offset, alma_sigma)
    result

//----
Price = src

//----
Price1 = ma("SMA",Price,1)
Price2 = ma("SMA",Price[1],1)

//RSI
Bulls0 = 0.5*(abs(Price1-Price2)+(Price1-Price2))
Bears0 = 0.5*(abs(Price1-Price2)-(Price1-Price2))

//STOCHASTIC
Bulls1 = Price1 - lowest(Price1,Length)
Bears1 = highest(Price1,Length) - Price1

//ADX
Bulls2 = 0.5*(abs(high-high[1])+(high-high[1]))
Bears2 = 0.5*(abs(low[1]-low)+(low[1]-low))

//
Bulls = Mode == "RSI" ? Bulls0 : Mode == "STOCHASTIC" ? Bulls1 : Bulls2
Bears = Mode == "RSI" ? Bears0 : Mode == "STOCHASTIC" ? Bears1 : Bears2
AvgBulls=ma(ma_type,Bulls,Length)     
AvgBears=ma(ma_type,Bears,Length)

//----
SmthBulls=ma(ma_type,AvgBulls,Smooth)  
SmthBears=ma(ma_type,AvgBears,Smooth)

difference = abs(SmthBulls - SmthBears)

bull_trend_color = (SmthBulls<SmthBulls[1])?lime:green
bear_trend_color = (SmthBears<SmthBears[1])?orange:red




// === Plot === 
// ==================================================
// Angles
plot(basis_angle, color= white , title="Basis angle",style=plot.style_circles)
plot(ssl_angle, color= white , title="SSL angle",style=plot.style_circles)
plot(ssl_angle2, color= white , title="SSL angle 2",style=plot.style_circles)
plot(mc_angle, color=yellow, title="MACD Angle",style=plot.style_circles)
plot(s_angle, color=yellow, title="Signal Angle",style=plot.style_circles)
plot(bbu_angle, color= white , title="BB Upper Angle",style=plot.style_circles)
plot(bbl_angle, color= white , title="BB Lower Angle",style=plot.style_circles)
plot(k_angle, color= white , title="Kijun Angle",style=plot.style_circles)
plot(ema_angle, color= white , title="EMA Fast Angle",style=plot.style_circles)
plot(ema_200_angle, color= white , title="EMA 200 Angle",style=plot.style_circles)

plot(macd_c,title="MACD Crossover",color=mc_color)
plot(sig_c, color=red, title="SIGNAL Crossover")
plot(mc_diff, color=yellow, title="MACD Diff",style=plot.style_circles)
plot(bbr, "Bollinger Bands %B", color=color.teal,linewidth=0)
plot(rsi, "RSI", color=#8E1599)

plot(slow_k, color=blue, title="slow k",style=plot.style_circles)
plot(slow_d, color=red, title="slow D",style=plot.style_circles)
plot(stoch_k, color=blue, title="Stoch k",style=plot.style_circles)
plot(stoch_d, color=red, title="Stoch D",style=plot.style_circles)


plot(SmthBulls,title="ASH Bulls",color=bull_trend_color,linewidth=4)
plot(SmthBears,title="ASH Bears",color=bear_trend_color,linewidth=4)

barcolor(bbr > 1.1 ? aqua : bbr < 0 ? aqua : na )
//barcolor(rsi > 70 ? aqua : rsi < 30 ? aqua : na )
barcolor(pos == -1 ? #ff0000: pos == 1 ? #00a000 : blue)

//plot(ssl_angle2, color= white , title="SSL2 angle",style=plot.style_circles)
//plot(dema_angle, color= white , title="Dema angle",style=plot.style_circles)
//plot(show_plots ? dema_histo : na, title="Dema Histo", color=dema_up ? green : red, style=plot.style_circles)

//plot(show_plots ? bb_zone : na, title="BB Zones", color=bb_zones_color, style=plot.style_circles,transp=20)
plot(show_plots ? dema_signal: na, title="Dema Signal", color=bb_zones_color, style=plot.style_circles)
// plot(show_plots ? m_hist: na, title="MACD Histogram", style=plot.style_columns, color = m_color )
// plot(show_plots ? macd: na, title="MACD", color=col_macd)
// plot(show_plots ? m_signal: na, title="MACD Signal", color=col_signal)
// Kijun
plot(kijun, color=red, title="Kijun",linewidth=2)
// SSL
plot(SSL, color=white , title="SSL")
plot(SSL2, color= yellow , title="SSL 2")
plot(SSL3, color= green , title="SSL 3")
// BB
plot(basis, title="Basis", color=bb_zones_color)
p1 = plot(show_bb ? bb_upper : na, "BB Upper ",color=bb_zones_color)
p2 = plot(show_bb ? bb_lower : na, "BB Lower ",color=bb_zones_color)
//fill(p1,p2, bb_zones_color)
// EMA 200 bands large
plot(ema_200, "EMA 200", color=color.new(yellow,80) )
//e200_up = plot(show_plots ? ema_200_upper : na, title="EMA 200 Upper", color=color.new(#ffe676,70))
//e200_down = plot(show_plots ? ema_200_lower : na, title="EMA 200 Lower", color=color.new(#ffe676,70))
plot(show_plots ? ema_fast : na, color=color.lime, title="EMA Fast")
// ATR
plot(show_plots ? atr_upper : na, "+ATR Upper", color=color.new(#ffffff,85))
plot(show_plots ? atr_lower : na, "-ATR Lower", color=color.new(#ffffff,85))
plot(show_atr ? atr_upper + atr_stop : na, "+ATR stop", color=color.new(#ffff00,85))
plot(show_atr ? atr_lower - atr_stop : na, "-ATR stop", color=color.new(#ffff00,85))


entry_signal() =>
	dir = 0
	trading = 0
    k = kijun

	// Type of candle - Long / Short
	candle = close > open ? 1 : 0

    // === All high perc change === 
    // if candle == 1 and perc_change()>20 and close > bb_upper and
    //  ssl_angle > 0 and not(bb_zone==0)
    //     dir := -1

    // // high perc 2 - Jun 6
    // if basis_angle < -4 and perc_change() > 15 and k < bb_lower and
    //  mc_color == green and not(bb_zone==0)
    //     dir := -1

    // // high perc 3 - Jun 4 - z1 above
    // if basis_angle > 2 and bbu_angle < -2 and perc_change() > 14 and 
    //  close > bb_upper and not(bb_zone==0)
    //     dir := -1

    // // BB %B counter - sell 
    // if candle == 1 and basis_angle < -4 and k > SSL and close < basis and
    //  (bbr > 1 or macd_c > 0) and not(bb_zone == 2)
    //     dir := -1

    // BBR & MACD Cross
    // if bbr>1 and sig_c > 0 and SSL > basis
    //  and not(bb_zone < 4 and close > bb_upper)
    //     dir := -1
    // if bbr<0 and sig_c < 0 and SSL < basis and close < bb_lower
    //  and not(bb_zone == 3 and rsi>30)
    //  and not (bb_zone == 0 and stoch_d > stoch_low)
    //     dir := 1

    // === Zones === 
    // if bb_zone == 4
    //      // RSI + MACD Cross
    //     // if candle == 1 and rsi > 74 and sig_c > mcu_line
    //     //     dir := -1
    //     if candle == 0 and rsi < 30 and rsi[1] < 30 and sig_c < 0
    //         dir := 1
    //     // counter - Jun 10
    //     if basis_angle > 0 and close < basis and high > basis and 
    //      bbr > bbr[1] and macd_c < 0 and sig_c > 0
    //         dir := 1

    // if bb_zone == 3
    //      // RSI + MACD Cross
    //     // if candle == 1 and rsi > 70 and rsi[2] > 70 and sig_c > 0
    //     //  and ssl_angle2 < 10 
    //     //     dir := -1
    //     // Sell - Jun 11
    //     if candle == 1 and rsi > 70 and bbr > 1 and sig_c > mcu_line
    //         dir := -1
    //     if candle == 0 and rsi < 30 and rsi[1] < 30 and sig_c < mcl_line
    //         dir := 1

    // if bb_zone == 2
    //      // RSI + MACD Cross
    //     if candle == 1 and rsi > 70 and rsi[1] > 70 and sig_c > mcu_line
    //      and ssl_angle < 15
    //         dir := -1
    //     // Sell - Jun 8
    //     // if candle == 1 and bbr>0.95 
    //     //     dir := -1
    //     // Buy - Jun 15
    //     if candle == 0 and bbr<0 and macd_c < mcl_line
    //         dir := 1
    //     // if candle == 0 and bbr< 0 and 
    //     //     dir := 1

    // if bb_zone == 1
    //      // RSI + MACD Cross
    //     if candle == 1 and sig_c > 0 and close > bb_upper and
    //      SSL < bb_upper and not(rsi > 70)
    //         dir := -1

    //     if candle == 0 and sig_c < 0 and close < bb_lower and
    //      SSL > bb_lower and not(rsi < 30)
    //         dir := 1

    // if bb_zone == 0
    //      // RSI + MACD Cross
    //     if candle == 1 and rsi > 68 and sig_c > 0 and
    //      macd_c > mcu_line and k < bb_upper
    //         dir := -1
    //     // if candle == 0 and open < bb_lower and mc_color == green
    //     //     dir := 1
    //     //     trading := 1
    //     if candle == 0 and rsi < 30 and sig_c < 0
    //         dir := 1

    if dir == 0

        // if candle == 1 and macd_c > mcu_line and 
        //  stoch_d > stoch_k and mc_color == green
        //     dir := -1

        // MACD highest level + Stock slow Jun 11
        // if basis > ema_200 and bb_zone > 1 and macd_c > mch_line and sig_c > mcu_line and 
        //  slow_d > slow_hi and ssl_angle < 17
        //     dir := -1
        // if basis < ema_200 and bb_zone > 1 and sig_c > mch_line and 
        //  slow_d > slow_hi and mc_diff < 1.1 and mc_color == green
        //     dir := -1
        // // MACD lowest level + Stock - Buy
        // if basis > ema_200 and bb_zone > 1 and sig_c < mcl_line and 
        //  slow_k < slow_low and candle == 0 and ssl_angle > -17
        //     dir := 1

        // // counter Jun 9
        // if macd_c < 0 and sig_c > 0 and slow_k < slow_low
        //  and low < bb_lower and open > bb_lower and bbl_angle > 5
        //     dir := 1

        // // MACD mid and ASH
        if macd_c > mch_line and candle == 1
         and not (bb_zone==0 and sig_c < mcu_line)
            dir := -1
        // if macd_c > mch_line and bull_trend_color == lime and candle == 1
        //  and not(bb_zone ==3 and SSL2 < bb_upper)
        //  and not (bb_zone==0 and sig_c < mcu_line)
        //     dir := -1

        if macd_c < mcl_line
            dir := 1

        // if macd_c < mcl_line and bear_trend_color == orange
        //     dir := 1

    // counter 1
    // if (ema_200_angle > 0 and candle == 0) and
    //  ( 
    //  (basis_angle > 1 and macd < mcl_line and high < bb_lower) or
    //  (basis_angle > 1 and atr_lower < ema_200 and high < bb_lower) or
    //  //(basis_angle > 5 and low < basis and close > basis and SSL > basis) or
    // //  (basis_angle > 0 and atr_lower < bb_lower and close > bb_lower and 
    // //  open < basis and mc_color == green) or
    //  (perc_change() < -9) or
    //  (bbl_angle > 5 and low < bb_lower and high < ema_200 )
    //  )
    //     dir := 1
    //     trading := 1

    // // counter type 2
    // if (bbl_angle > 5 and low < bb_lower and high < ema_200 )
    //     dir := 1
    //     trading := 1

    // // MCROSS highest line - Jun 3
    // if candle == 1 and mc_color == green and 
    //  sig_c > mcu_line and macd_c > sig_c and
    //  stoch_k > 50 and ssl_angle2 < 10
    //     dir := -1
    //     trading := 1

    // // May 21 - Stochastic - Sell 
    // if candle == 1 and stoch_k > stoch_hi and close > bb_upper and
    //  sig_c < 0
    //     dir := -1
    //     trading := 1

    trading := dir > 0 ? 1 : dir < 0 ? 1 : 0
    [dir, trading]

[trade_dir, trading] = entry_signal() 

// Buy / Sell indicators
trade_color = trade_dir > 0  ? buy_color : sell_color
plotshape(trade_dir > 0 and show_entry ? 1 : na, title="Entry Buy", color=trade_color, location = location.belowbar, style=shape.labelup, text="B", textcolor=color.white, size=size.small)
plotshape(trade_dir < 0 and show_entry ? -1 : na, title="Entry Sell", color=trade_color, location = location.abovebar, style=shape.labeldown, text="S", textcolor=color.white, size=size.small)
