// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © LonesomeTheBlue

//@version=5
indicator('Consolidation Zones - Strategy', overlay=true, max_bars_back=1100)
prd = input.int(defval=10, title='Loopback Period', minval=2)
conslen = input.int(defval=5, title='Min Consolidation Length', minval=2)
paintcons = input(defval=true, title='Paint Consolidation Area ')
zonecol = input(defval=color.new(color.blue, 70), title='Zone Color')
i_res = input.timeframe('30', "Resolution")


float hb_ = ta.highestbars(prd) == 0 ? high : na
float lb_ = ta.lowestbars(prd) == 0 ? low : na
var int dir = 0
float zz = na
float pp = na

iff_1 = lb_ and na(hb_) ? -1 : dir
dir := hb_ and na(lb_) ? 1 : iff_1
if hb_ and lb_
    if dir == 1
        zz := hb_
        zz
    else
        zz := lb_
        zz
else
    iff_1 = lb_ ? lb_ : na
    zz := hb_ ? hb_ : iff_1
    zz

for x = 0 to 1000 by 1
    if na(close) or dir != dir[x]
        break
    if zz[x]
        if na(pp)
            pp := zz[x]
            pp
        else
            if dir[x] == 1 and zz[x] > pp
                pp := zz[x]
                pp
            if dir[x] == -1 and zz[x] < pp
                pp := zz[x]
                pp

var int conscnt = 0
var float condhigh = na
var float condlow = na
float H_ = ta.highest(conslen)
float L_ = ta.lowest(conslen)
var line upline = na
var line dnline = na
bool breakoutup = false
bool breakoutdown = false

if ta.change(pp)
    if conscnt > conslen
        if pp > condhigh
            breakoutup := true
            breakoutup
        if pp < condlow
            breakoutdown := true
            breakoutdown
    if conscnt > 0 and pp <= condhigh and pp >= condlow
        conscnt += 1
        conscnt
    else
        conscnt := 0
        conscnt
else
    conscnt += 1
    conscnt

if conscnt >= conslen
    if conscnt == conslen
        condhigh := H_
        condlow := L_
        condlow
    else
        line.delete(upline)
        line.delete(dnline)
        condhigh := math.max(condhigh, high)
        condlow := math.min(condlow, low)
        condlow

    upline := line.new(bar_index, condhigh, bar_index - conscnt, condhigh, color=color.red, style=line.style_dashed)
    dnline := line.new(bar_index, condlow, bar_index - conscnt, condlow, color=color.lime, style=line.style_dashed)
    dnline

// condhigh_m = request.security(syminfo.tickerid, i_res, condhigh)
// condlow_m = request.security(syminfo.tickerid, i_res, condlow)
// conscnt_m = request.security(syminfo.tickerid, i_res, conscnt)

fill(plot(condhigh, color=na, style=plot.style_stepline), plot(condlow, color=na, style=plot.style_stepline), color=paintcons and conscnt > conslen ? zonecol : color.new(color.white, 100), transp=90)

condMid = condhigh or condlow ? (condhigh + condlow) * 0.5 : na
plot(condMid, style=plot.style_stepline, color=conscnt > conslen ? #ffff00 : color.new(color.white, 90))
// buy_cond = condMid<emaSlow and close>condMid
// plotshape(buy_cond,title="Buy 1",color=green ,style=shape.circle,location=location.bottom)

i_show_conds = input.bool(true,title='Show Conditions')
condEnter = i_show_conds and (conscnt > conslen)
condExit = i_show_conds and (conscnt < conslen) and (conscnt > conslen)[1]
plotshape(condEnter ? 1 : na, title='Enter Consolidation', location=location.bottom, style=shape.circle, size=size.tiny, color=#ff0000)
plotshape(condExit ? 1 : na, title='Enter Consolidation', location=location.bottom, style=shape.circle, size=size.tiny, color=#00ff00)


