//@version=4
//@author=smileBTC
study("RSI with wicks - Overlay", shorttitle="RSIC Overlay - func",overlay=true,max_labels_count=500)
////based on RSIC [cI8DH]
////added wicks

std         = false // input(false, title="Show Standard RSI")
rsi_candles = true // input(true,  title="Show Candles")
wicks       = true // input(true,  title="Wicks based on stand-alone RSI")
target_up   = 70 // input(70, minval=1, title="RSI Up")
target_down = 30 // input(30, minval=1, title="RSI Down")
len1        = input(14, minval=1, title="Length")
pos         = input(title="Position", defval="tb", options=["tb", "t", "b"])
only_strong = input(title="Use only strongest", type=input.bool, defval=false)
src_close   = close
src_open    = open
src_high    = high
src_low     = low 

orange      = #ff9800
yellow      = #ffee58
violet      = #814dff
aqua        = #43d3ff
teal        = #36fcf6
blue        = #0053ff
lime        = #50ff00

// Change
perc_change(obj) =>
    perc = abs( (1 - (obj[1] / obj)) * 10000 )

rsi_wicks(rsi_len) =>
    norm_close  = avg(src_close,src_close[1])
    gain_loss_close   = change(src_close)/norm_close
    RSI_close         = 50+50*rma(gain_loss_close, rsi_len)/rma(abs(gain_loss_close), rsi_len)

    norm_open = if wicks==true 
        avg(src_open,src_open[1])
    else 
        avg(src_close,src_close[1])
    gain_loss_open   = change(src_open)/norm_open
    RSI_open         = 50+50*rma(gain_loss_open, rsi_len)/rma(abs(gain_loss_open), rsi_len)
            
    norm_high = if wicks==true 
        avg(src_high,src_high[1])
    else 
        avg(src_close,src_close[1])
    gain_loss_high   = change(src_high)/norm_high
    RSI_high         = 50+50*rma(gain_loss_high, rsi_len)/rma(abs(gain_loss_high), rsi_len)
            
    norm_low  = if wicks==true
        avg(src_low,src_low[1])
    else 
        avg(src_close,src_close[1])
    gain_loss_low   = change(src_low)/norm_low
    RSI_low         = 50+50*rma(gain_loss_low, rsi_len)/rma(abs(gain_loss_low), rsi_len)

    [RSI_open,RSI_close,RSI_high,RSI_low]

[RSI_open,RSI_close,RSI_high,RSI_low] = rsi_wicks(len1)

rw_change = perc_change(RSI_close) / 100
plot(rw_change,title='Change',color=color.new(blue,100),style=plot.style_circles)
rsi_color = RSI_close > RSI_close[1] ? #65D25Bff : #FE6B69ff
plot(RSI_open,title="RSI open",color=color.new(color.blue,100))
plot(RSI_high,title="RSI high",color=color.new(color.blue,100))
plot(RSI_low,title="RSI low",color=color.new(color.blue,100))
plot(RSI_close,title="RSI close",color=color.new(color.blue,100))

// SELL
up_cond = close>open and (pos=='tb' or pos=='t')
// Strong
plotshape(RSI_open>target_up and up_cond?1:na ,title="Up High",color=#ff0000,style=shape.circle,location=location.top)
// Mid
plotshape(RSI_close>target_up and RSI_open<target_up and up_cond and only_strong==false?1:na ,title="Up Mid",color=orange,style=shape.circle,location=location.top)
//plot(perc_change(RSI_close)*.01, title="Percent Change", color=color.new(blue,100),style=plot.style_circles)
// Weak
plotshape(RSI_close<target_up and RSI_high>target_up and up_cond and only_strong==false?1:na ,title="Up Weak",color=yellow,style=shape.circle,location=location.top)

// BUY
down_cond = close<open and (pos=='tb' or pos=='b')
// Weak
plotshape(RSI_close>target_down and RSI_low<target_down and down_cond and only_strong==false?1:na ,title="Low weak",color=violet ,style=shape.circle,location=location.bottom)
// Mid
plotshape(RSI_close<target_down and RSI_open>target_down and down_cond and only_strong==false?1:na ,title="Low Mid",color=blue,style=shape.circle,location=location.bottom)
// Strong
plotshape(RSI_open<target_down and down_cond?1:na ,title="Low strong",color=lime,style=shape.circle,location=location.bottom)

// plot(std ? RSI_close : na, color=#a64d79ff, title="RSI", join=true)
// b1 = hline(30, color=#a64d7933, linestyle=hline.style_dotted, title = "Oversold")
// c = hline(50, color=color.new(#ffffff,50), linestyle=hline.style_dotted, title = "Center")
// b2 = hline(70, color=#a64d7933, linestyle=hline.style_dotted, title = "Overbought")
// fill(b1,b2,color=#5b9cf6, title="RSI Band")
// plotcandle(rsi_candles?RSI_open:na, rsi_candles?RSI_high:na, rsi_candles?RSI_low:na, rsi_candles?RSI_close:na, title='RSI', color = rsi_color)
