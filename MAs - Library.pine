//@version=5
library(title="MAs" )
src = close // input(close, title="Source")
use_smooth = input.bool(false, title="Use Smoothing")
smooth = input.int(5,title="Smoothing")

g_cb = "Show ----------------------------------------------------"
inl_cb = "cb"
show_m1 = input.bool(title="M1", defval=false,group=g_cb,inline=inl_cb)
show_m2 = input.bool(title="M2", defval=true,group=g_cb,inline=inl_cb)
show_m3 = input.bool(title="M3", defval=false,group=g_cb,inline=inl_cb)
show_m4 = input.bool(title="M4", defval=true,group=g_cb,inline=inl_cb)
show_m5 = input.bool(title="M5", defval=false,group=g_cb,inline=inl_cb)
show_m6 = input.bool(title="M6", defval=false,group=g_cb,inline=inl_cb)
show_m7 = input.bool(title="M7", defval=false,group=g_cb,inline=inl_cb)
show_m8 = input.bool(title="M8", defval=false,group=g_cb,inline=inl_cb)

g_ma = "MA ----------------------------------------------------"
inl_ma = "ma"
ma_type = input.string(title="MA Type", defval="hma", options=["ema","dema","tema","wma","vwma","smma","rma","hma","lsma","McGinley"],group=g_ma)
use_candles = input.bool(false,title="Colorize Candles")

l1 = input.int(8,title="M1",group=g_ma,inline=inl_ma)
l2 = input.int(20,title="M2",group=g_ma,inline=inl_ma)
l3 = input.int(50,title="M3",group=g_ma,inline=inl_ma)
l4 = input.int(75,title="M4",group=g_ma,inline=inl_ma)
l5 = input.int(100,title="M5",group=g_ma,inline=inl_ma)
l6 = input.int(200,title="M6",group=g_ma,inline=inl_ma)
l7 = input.int(300,title="M7",group=g_ma,inline=inl_ma)
l8 = input.int(500,title="M8",group=g_ma,inline=inl_ma)

g_fill = "Fills"
inl_fill = "fill"
inl_conv = "conv"
i_ma_select = input.int(6, title="Colorized", options=[1,2,3,4,5,6,7,8])
show_fill = input.bool(title="Show Fill", defval=true,inline=inl_fill,group=g_fill)
show_conv = input.bool(title="Show Conv", defval=true,inline=inl_fill,group=g_fill)
conv_amount = input.float(4, title="Conv Amount", step=1,inline=inl_conv,group=g_fill )
c_type = input.string(title="Type", defval="NAS", options=["NAS","USD", "JPY"],inline=inl_conv,group=g_fill)
line_input = 1 //input(1, title="Line width", type=input.integer,inline=inl_fill )


red = #ff0062
orange = #ff9800
yellow = #FFFF00
green = #005a04
lime = #00E676
aqua = #00bcd4
blue = #2962ff
dark_blue = #2962ff
violet = #814dff
white = #ffffff
gray = #707070

angle(_src, len) =>
    rad2degree=180/3.14159265359  //pi 
    ang=rad2degree*math.atan((_src[0] - _src[1]) / ta.atr(len))
    
// Lines and Angles
ma_graph(len) =>
    ma =0.0
    if ma_type == 'ema' // Exponential
        ma := ta.ema(src,len)
    if ma_type=="dema" // Double Exponential
        e = ta.ema(src, len)
        ma := 2 * e - ta.ema(e, len)
    if ma_type == 'tema' // Triple Exponential
        ema1 = ta.ema(src, len)
        ema2 = ta.ema(ema1, len)
        ema3 = ta.ema(ema2, len)
        ma := 3 * (ema1 - ema2) + ema3
    if ma_type == 'wma' // Weighted
        ma := ta.wma(src,len)
    if ma_type == 'vwma' // Volume Weighted
        ma := ta.vwma(src,len)
    if ma_type=="smma" // Smoothed
        w = ta.wma(src, len)
        ma := na(w[1]) ? ta.sma(src, len) : (w[1] * (len - 1) + src) / len
    if ma_type == "rma"
        ma := ta.rma(src, len)
    if ma_type == 'hma' // Hull
        ma := ta.wma(2*ta.wma(src, len/2)-ta.wma(src, len), math.floor(math.sqrt(len) ))
    if ma_type=="lsma" // Least Squares
        ma := ta.linreg(src, len, 0)
    if ma_type=="McGinley"
        mg = 0.0
        mg := na(mg[1]) ? ta.ema(src, len) : mg[1] + (src - mg[1]) / (len * math.pow(src/mg[1], 4))
        ma :=mg

    if use_smooth
        ma := ta.sma(ma,smooth)

    ma_angle = angle(ma,1)
    [ma,ma_angle]




ma_conv(t1, t2) =>
    
    float boost = switch syminfo.ticker
        "GBPUSD" => 10000
        "EURUSD" => 10000
        "GBPJPY" => 100
        "BTCUSD" => 0.1
        "NAS100" => 1
        "NDQ100" => 1
        "GOLD"   => 10
        "SILVER" => 100
        => 1

    diff = (t1 - t2) * boost
    conv = show_conv and diff < conv_amount and diff > conv_amount * -1 ? true : false
    [diff, conv]
    
[m1,m1_a] = ma_graph(l1)
[m2,m2_a] = ma_graph(l2)
[m3,m3_a] = ma_graph(l3)
[m4,m4_a] = ma_graph(l4)
[m5,m5_a] = ma_graph(l5)
[m6,m6_a] = ma_graph(l6)
[m7,m7_a] = ma_graph(l7)
[m8,m8_a] = ma_graph(l8)

[m2_m4_diff,m2_m4_conv] = ma_conv(m2,m4)
[m4_m5_diff,m4_m5_conv] = ma_conv(m4,m5)
[m5_m6_diff,m5_m6_conv] = ma_conv(m5,m6)
[m7_m8_diff,m7_m8_conv] = ma_conv(m7,m8)

// Colorized MA angles
ma_select()=>
    float select = switch i_ma_select
        2 => m2_a
        3 => m3_a
        4 => m4_a
        5 => m5_a
        6 => m6_a
        7 => m7_a
        8 => m8_a
        => m6_a

    ma_a = math.abs(select)
    ma_zone =  
     ma_a   < 1 ? 0 
     : ma_a < 2 ? 1 
     : ma_a < 3 ? 2 
     : ma_a < 4 ? 3 
     : ma_a < 5 ? 4 
     : ma_a < 6 ? 5 
     : ma_a > 6 ? 6 
     : na

    ma_color = 
     ma_zone   == 0 ? red 
     : ma_zone == 1 ? orange 
     : ma_zone == 2 ? yellow 
     : ma_zone == 3 ? gray 
     : ma_zone == 4 ? blue 
     : ma_zone == 5 ? lime 
     : ma_zone == 6 ? white : na

    // ma_zone =  
    //  ma_a   < 1 ? 0 
    //  : ma_a < 2 ? 1 
    //  : ma_a < 3 ? 2 
    //  : ma_a < 4 ? 3 
    //  : ma_a < 5 ? 4 
    //  : ma_a < 6 ? 5 
    //  : ma_a < 7 ? 6
    //  : ma_a < 8 ? 7
    //  : ma_a < 9 ? 8
    //  : ma_a > 9 ? 9 
    //  : na

    // ma_color = 
    //  ma_zone   == 0 ? #930000 
    //  : ma_zone == 1 ? #e54500
    //  : ma_zone == 2 ? #e5aa00
    //  : ma_zone == 3 ? #777777
    //  : ma_zone == 4 ? #00429a
    //  : ma_zone == 5 ? #0498b6 
    //  : ma_zone == 6 ? #1d6700
    //  : ma_zone == 7 ? #31b000
    //  : ma_zone == 8 ? #eeeeee 
    //  : ma_zone == 9 ? white 
    //  : na

    // ma_color = 
    //  ma_zone   == 0 ? red 
    //  : ma_zone == 1 ? orange 
    //  : ma_zone == 2 ? yellow 
    //  : ma_zone == 3 ? green 
    //  : ma_zone == 4 ? lime 
    //  : ma_zone == 5 ? aqua 
    //  : ma_zone == 6 ? blue
    //  : ma_zone == 7 ? dark_blue 
    //  : ma_zone == 8 ? violet 
    //  : ma_zone == 9 ? white 
    //  : na

    [ma_color]

[ma_color] = ma_select()

export get_ma(int ma = 1) =>
    int select = switch ma
        1 => m1


// Colorize candles based on MA angles
barcolor(use_candles? ma_color : na)
// Plots
l_width = 2
p_m1 = plot(m1 and show_m1?m1:na,color=m1_a>0?green:red,title="M1",linewidth=i_ma_select==1?l_width:1)
p_m2 = plot(m2 and show_m2?m2:na,color=i_ma_select==2? ma_color : color.white,title="M2",linewidth=i_ma_select==2?l_width:1)
p_m3 = plot(m3 and show_m3?m3:na,color=i_ma_select==3? ma_color : blue,title="M3",linewidth=i_ma_select==3?l_width:1)
p_m4 = plot(m4 and show_m4?m4:na,color=i_ma_select==4? ma_color : yellow,title="M4",linewidth=i_ma_select==4?l_width:1)
p_m5 = plot(m5 and show_m5?m5:na,color=i_ma_select==5? ma_color : m5_a>0 ? orange : m5_a<0 and m5>m6 ? red : green,title="M5",linewidth=i_ma_select==5?l_width:1)
p_m6 = plot(m6 and show_m6?m6:na,color=i_ma_select==6? ma_color : m6_a > 0 ? red : lime, title="M6",linewidth=i_ma_select==6?l_width:1)
p_m7 = plot(m7 and show_m7?m7:na,color=i_ma_select==7? ma_color : m7_a>0 ? orange : m7_a<0 and m7>m8 ? red : green,title="M7",linewidth=i_ma_select==7?l_width:1)
p_m8 = plot(m8 and show_m8?m8:na,color=i_ma_select==8? ma_color : m8_a>0 ? red : lime,title="M8",linewidth=i_ma_select==8?l_width:1)
// Angles
plot(m1_a,color=m1_a>0?color.new(green,100):color.new(red,100),title="M1 A")
plot(m2_a,color=i_ma_select==2? color.new(ma_color,100) : color.new(color.white,100),title="M2 A")
plot(m3_a,color=i_ma_select==3? color.new(ma_color,100) : color.new(color.blue,100),title="M3 A")
plot(m4_a,color=i_ma_select==4? color.new(ma_color,100) : color.new(yellow,100),title="M4 A")
plot(m5_a,color=i_ma_select==5? color.new(ma_color,100) : color.new(orange,100),title="M5 A")
plot(m6_a,color=i_ma_select==6? color.new(ma_color,100) : color.new(red,100), title="M6 A")
plot(m7_a,color=i_ma_select==7? color.new(ma_color,100) : color.new(orange,100),title="M7 A")
plot(m8_a,color=i_ma_select==8? color.new(ma_color,100) : color.new(red,100),title="M8 A")

// Fills
fill(p_m5,p_m6,title="S5/S6 Fill", color=show_fill and m5_m6_diff<1?color.new(green,90): show_fill ? color.new(red,90):na,fillgaps=true)
fill(p_m7,p_m8,title="S7/S8 Fill", color=show_fill and m7_m8_diff<1?color.new(green,90): show_fill ? color.new(red,90):na,fillgaps=true)
//Convergence
fill(p_m2,p_m4,title="M2/M4 Conv", color=m2_m4_conv and m2>m4?color.new(red,70) : m2_m4_conv and m2<m4?color.new(green,70) : na,fillgaps=true)
fill(p_m5,p_m6,title="M5/M6 Conv", color=m5_m6_conv and m5>m6?color.new(red,70) : m5_m6_conv and m5<m6?color.new(green,70) : na,fillgaps=true)
fill(p_m7,p_m8,title="M7/M8 Conv", color=m7_m8_conv and m7>m8?color.new(red,70) : m7_m8_conv and m7<m8?color.new(green,70) : na,fillgaps=true)
plot(m2_m4_diff, title="M2/4 Conv",color=m2_m4_conv and m2>m4?color.new(red,70) : m2_m4_conv and m2<m4?color.new(green,70) : na )
plot(m5_m6_diff, title="M5/6 Conv",color=m5_m6_conv and m5>m6?color.new(red,70) : m5_m6_conv and m5<m6?color.new(green,70) : na )
plot(m7_m8_diff, title="M7/8 Conv",color=m7_m8_conv and m7>m8?color.new(red,70) : m7_m8_conv and m7<m8?color.new(green,70) : na )



var int flag_u = 0
var int flag_d = 0
flag_u := m1_a>20 ? 1  : flag_u==1  and m1_a>0 ? 1  : flag_u==1  and m1_a<0 ? 0 : na
flag_d := m1_a<-10 ? -1 : flag_d==-1 and m1_a<0 ? -1 : flag_d==-1 and m1_a>0 ? 0 : na
plotshape(flag_u==0 ? 1 : na,title="Flag Up",color=red ,style=shape.circle,location=location.top)
plotshape(flag_d==0 ? 1 : na,title="Flag Down",color=green ,style=shape.circle,location=location.bottom)






sell_cond  = m3_a<0 and m4_a<0 and m7>m8 and close>m3 
 and not(m2_a>0) 
 and not(m7_a<0) ? 1 : na
sell_cond2 = m3_a<0 and m4_a<0 and m7>m8 and close>m4 and not(m7_a>0) ? 1 : na
plotshape(sell_cond,title="Buy 1",color=red ,style=shape.circle,location=location.top)

// 5 min
buy_cond  = m8_a<0 and m6_a<0 and m3<m2 and m2_a>-5 and close<m2
 and not(m5>m6)
 and not(m3>m5)
 and not(m1_a<-5)
//buy_cond  = m8_a<0 and m6_a<0 and m3_a>0 and close<m3
//buy_cond  = m3<m2 and m2_a>5
plotshape(buy_cond,title="Buy 1",color=green ,style=shape.circle,location=location.bottom)



// === Labels ===
// label_txt = "Instrument: " + str.tostring(syminfo.ticker)
// if barstate.islastconfirmedhistory
//     buy = label.new(x=bar_index + 20, y=close, style=label.style_label_left,color=color.blue, textcolor=color.white, size=size.normal,text=label_txt )