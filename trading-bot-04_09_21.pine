//@version=4
study(title = "Trading Bot", shorttitle = "Trading Bot", overlay=true, format = format.price, precision = 5)


// Custom inputs
account_size = input(2000, minval=1000,title="Account Size")
//trending = input(title="Show Trending/Ranging", type=input.bool, defval=true)
show_entry = input(title="Show Entry", type=input.bool, defval=true)
show_atr = input(title = "Show ATR", type = input.bool, defval = false)
//show_label = input(title="Show Labels", type=input.bool, defval=true)
//show_pips = input(title="Show Pips", type=input.bool, defval=true)
show_angle = input(title="Show Angles", type=input.bool, defval=false)
//show_cross = input(title="Show Crossover", type=input.bool, defval=false)
show_BB = input(title="Show BB", type=input.bool, defval=true)
use_fast_ma = input(title="Fast MA", type=input.bool, defval=false)

use_counter_sell = input(title="Counter Sell", type=input.bool, defval=true)
use_counter_buy = input(title="Counter Buy", type=input.bool, defval=true)

use_ssl_cross = input(title="SSL cross BB Basis", type=input.bool, defval=true)
//use_ema_cross = input(title="SSL cross EMA", type=input.bool, defval=false)
use_rsi = input(title="Use RSI bars", type=input.bool, defval=true)
use_basis_angle = input(title="Use Basis Angle", type=input.bool, defval=false)

//use_res = input(title="RES Filter", type=input.bool, defval=false)
//use_wae = input(title="WAE Filter", type=input.bool, defval=false)
//use_angle_ma = input(title="Angle MA", type=input.bool, defval=false)
//use_wavetrend = input(title="Wave Trend", type=input.bool, defval=true)

// === Global Function ===
// ==================================================
angle(_src) =>
    rad2degree=180/3.14159265359  //pi 
    ang=rad2degree*atan((_src[0] - _src[1])/atr(3))


// === EMA 200 ===
// ==================================================
len_200 = input(200, minval=1, title="EMA 200")
ema_200 = ema(close, len_200)



// === Bollinger Bands %B ===
// ==================================================
length = input(25, minval=1, title="%B Length") // old 56
src = input(close, title="Source")
multi = input(2.0, minval=0.001, maxval=50, title="StdDev")
basis = sma(src, length)
deviation = multi * stdev(src, length)
upper = basis + deviation
lower = basis - deviation
bbr = (src - lower)/(upper - lower)




// === Bollinger Awesome Alert R1.1 by JustUncleL ===
// ==================================================
bb_use_ema = input(false, title="Use EMA for Bollinger Band")
bb_source = input(close, title="Bollinger Source")
bb_length = length //input(54, minval=1, title="Bollinger Length")
bb_mult = input(2.0, title="Base Multiplier", minval=0.5, maxval=10)
sqz_length = 100
sqz_threshold = input(50, minval=0, title="Squeeze Threshold") // old 54

// EMA inputs
fast_ma_len = input(3, title="Fast EMA length", minval=2)

// Breakout Indicator Inputs
ema_1 = ema(bb_source, bb_length)
sma_1 = sma(bb_source, bb_length)
bb_basis = bb_use_ema ? ema_1 : sma_1
fast_ma = ema(bb_source, fast_ma_len)

// Deviation
// * I'm sure there's a way I could write some of this cleaner, but meh.
dev = stdev(bb_source, bb_length)
bb_dev = bb_mult * dev
// Bands
bb_upper = bb_basis + bb_dev
bb_lower = bb_basis - bb_dev
// Calculate BB spread and average spread
bb_spread = bb_upper - bb_lower
avgspread = sma(bb_spread, sqz_length)
// Calculate BB relative %width for Squeeze indication
bb_squeeze = 0.00
bb_squeeze := bb_spread / avgspread * 100
// Calculate Upper and Lower band painting offsets based on 50% of atr.
bb_offset = atr(14) * 0.5
bb_sqz_upper = bb_upper + bb_offset
bb_sqz_lower = bb_lower - bb_offset
basis_angle = angle(bb_basis)



// === SSL Hybrid - Mihkel00 ===
// ==================================================
len = input(title = "SSL1 / Baseline Length", defval = 55) // old 55
show_Baseline = input(title="Show Baseline", type=input.bool, defval=true)
show_SSL1 = input(title = "Show SSL1", type = input.bool, defval = false)

//ATR
atrlen = input(14, "ATR Period")
mult = input(1, "ATR Multi", step = 0.1)
smoothing = input(title = "ATR Smoothing", defval = "WMA", options = ["RMA", "SMA", "EMA", "WMA"])

ma_function(source, atrlen) =>
	if smoothing == "RMA"
		rma(source, atrlen)
	else
		if smoothing == "SMA"
			sma(source, atrlen)
		else
			if smoothing == "EMA"
				ema(source, atrlen)
			else
				wma(source, atrlen)

atr_slen = ma_function(tr(true), atrlen)

// ATR Up/Low Bands
upper_band = atr_slen * mult + close
lower_band = close - atr_slen * mult

// SSL 1 and SSL2
emaHigh = wma(2 * wma(high, len / 2) - wma(high, len), round(sqrt(len)))
emaLow = wma(2 * wma(low, len / 2) - wma(low, len), round(sqrt(len)))

// BASELINE VALUES
SSL_B = wma(2 * wma(close, len / 2) - wma(close, len), round(sqrt(len)))
multy = 0.2
range = tr
rangema = ema(range, len)
upperk =SSL_B + rangema * multy
lowerk = SSL_B - rangema * multy

//SSL1 VALUES
Hlv = int(na)
Hlv := close > emaHigh ? 1 : close < emaLow ? -1 : Hlv[1]
sslDown = Hlv < 0 ? emaHigh : emaLow

//COLORS
color_ssl1 = close > sslDown ? #00c3ff : close < sslDown ? #ff0062 : na
ssl_color_buy = #00c3ff
ssl_color_sell = #ff0062
ssl_color_bar = close > upperk ? ssl_color_buy : close < lowerk ? ssl_color_sell : color.gray



// === RSI Chart Bars ===
// ==================================================
rsi_len = input(14, minval=1, title="Length")
up = rma(max(change(close), 0), rsi_len)
down = rma(-min(change(close), 0), rsi_len)
rsi = down == 0 ? 100 : up == 0 ? 0 : 100 - (100 / (1 + up / down))
src1 = close, len1 = input(70, minval=1, title="UpLevel")
src2 = close, len2 = input(30, minval=1, title="DownLevel")
isup() => rsi > len1
isdown() => rsi < len2
// plot(rsi,title="RSI",color=color.blue)
barcolor(isup() ? #00ff00 : isdown() ? #ff0000 : na )
//hline(len1, title='Upper', color=color.red, linestyle=hline.style_dotted, linewidth=2)
//hline(len2, title='Lower', color=color.green, linestyle=hline.style_dotted, linewidth=2)




// === Trading Properties ===
// ==================================================
var bb_count = 0
allow_trade = 0
entry_price = 0.00
exit_price = 0.00
trend = SSL_B > ema_200 ? 1 : -1
plot(trend, title="Trend Direction", color=color.black,style=plot.style_circles)
// plot 
plot(ema_200, title="EMA 200", color=#fff176, linewidth=3)
plot(angle(ema_200), title="EMA angle", color=#fff176, style=plot.style_circles)
plot(bbr, "Bollinger Bands %B", color=color.teal,linewidth=0)
//plot(wae_ratio, color=color.white, title="WAE Ratio",style=plot.style_circles)

// BBAWE
plot(fast_ma, title="Fast EMA", color=color.yellow, transp=10, linewidth=2)
//plot( (fast_ma / bb_sqz_lower), title="Fast - Lower", color=color.green, transp=10, linewidth=2)
lbi = plot(show_BB ? bb_lower:na, title="Lower Band Inner", color=color.blue, transp=10, linewidth=1)
lsqzi = plot(show_BB ? bb_sqz_lower: na, "Hide Sqz Lower", transp=0, linewidth=1,color=color.red)
ubi = plot(show_BB ? bb_upper: na, title="Upper Band Inner", color=color.blue, transp=10, linewidth=1)
usqzi = plot(show_BB ? bb_sqz_upper: na, "Hide Sqz Upper", transp=0, linewidth=1,color=color.red)
plot(bb_basis, title="Basis Line", color=color.red, transp=10, linewidth=2)
plot(basis_angle, title="Basis Angle", color=color.red, transp=10, linewidth=2, style=plot.style_circles)
 
plot(bb_squeeze[0], title="BB Squeeze", color=bb_squeeze < sqz_threshold ? color.blue : color.gray, transp=10, linewidth=0)

//fill(ubi, lbi, title="Center Channel Fill", color=color.black, transp=80)

fill(ubi, usqzi, color=bb_squeeze > sqz_threshold ? color.black : color.blue, transp=20)
fill(lbi, lsqzi, color=bb_squeeze > sqz_threshold ? color.black : color.blue, transp=20)
// SSL Hybrid
p1 = plot(show_Baseline ? SSL_B : na,title='SSL Baseline', color=ssl_color_bar, linewidth=4,transp=0)
ssl_angle = angle(SSL_B)
plot(ssl_angle,title='SSL Angle', color=ssl_color_bar, linewidth=4,transp=0, style=plot.style_circles)
//plot(show_Baseline ? emaHigh : na,title='Upperk', color=color.gray, linewidth=2,transp=0)
//plot(show_Baseline ? emaLow : na,title='Lowerk', color=color.gray, linewidth=2,transp=0)
//plot(show_Baseline ? upper_band : na,title='Upper band', color=ssl_color_bar, linewidth=2,transp=0)
//plot(show_Baseline ? lower_band : na,title='Lower band', color=ssl_color_bar, linewidth=2,transp=0)
// ATR plot
atr_upper = plot(show_atr ? upper_band : na, "+ATR", color=color.white, transp=50)
atr_lower = plot(show_atr ? lower_band : na, "-ATR", color=color.white, transp=50)
// RES
//plot(res,"RES", style=plot.style_circles, linewidth=0, color=color_res, transp=0)
//barcolor(i_barColor ? c_ntz : na)



// === Trading Methods ===
// ==================================================
entry_signal() =>
	dir = 0
	trading = 0

	// Type of candle - Long / Short
	candle = close > open ? 1 : 0
	waiting = 0
	wae_limit = 75


	// Sell signal
	if (bbr >= 0.98 and candle == 1 and trend > 0)
		dir := -1
		trading := 1

		// Fast Ma Filter
		if (use_fast_ma) and ( (fast_ma < bb_upper ) )  // ( (fast_ma < bb_upper ) or (fast_ma > bb_upper) )
			dir := na
			trading := 0

		// SSL baseline below BB Basis
		if use_ssl_cross and SSL_B < bb_basis and trend > 0
			dir := na
			trading := 0

		// Use RSI candles only
		if use_rsi and not isup() and trend > 0 
			dir := na
			trading := 0

		// Strong trend only look for RSI candles
		// if use_basis_angle and ssl_angle > 0 and rsi < len1
		// 	dir := na
		// 	trading := 0

		// Strong trend wait for trend to settle down
		// if use_basis_angle and ssl_angle > 9
		// 	dir := na
		// 	trading := 0


		
			
		// WAE Filter 2
		// if (use_wae == true and wae_ratio < wae_limit)
		// 	dir := na
		// 	trading := 0


		// Angle of Moving Average
		// if use_angle_ma and (c_ntz == color.gray)
		// 	dir := na
		// 	trading := 0

		// if use_wavetrend and wt2 < obLevel2 and wae_ratio < wae_limit
		// 	dir := na
		// 	trading := 0
		
	// Buy signal
	else if (bbr <= 0.02 and candle == 0 and trend < 0)
		dir := 1
		trading := 1

		// Fast Ma Filter
		if (use_fast_ma) and (fast_ma > bb_lower ) //( (fast_ma > bb_lower ) or (fast_ma / bb_sqz_lower < 0.9) ) 
			dir := na
			trading := 0

		if use_ssl_cross and SSL_B > bb_basis
			dir := na
			trading := 0

		// Strong trend only look for RSI candles
		if use_basis_angle and basis_angle > -4 and rsi < len2
			dir := na
			trading := 0
			waiting := 1

		// Use RSI candles only
		if use_rsi and not isdown() and trend < 0
			dir := na
			trading := 0


	// Counter Trend Sell
	else if use_counter_sell and (bbr <= 0.01 and candle == 0 and trend > 0) and ssl_angle < 0 
		dir := 1
		trading := 1

	// Counter Trend Buy
	else if use_counter_buy and (trend < 0) and (bbr >= 0.99 and candle == 1) and ssl_angle > 1 
		dir := -1
		trading := 1

	else
		dir := na
		trading := 0


	[dir, trading,waiting]

[trade_dir, trading, waiting] = entry_signal() 

calc_trade_values() =>
	p = 0.00
	rp = 0.00
	sl = 0.00
	pips = 0.00
	p_value = 0.00

	// Sell
	if trade_dir < 0
		sl := upper_band
		rp := (1 - (upper_band / close)) * 100
		p  := (2 / rp) * account_size
	// Buy
	else
		sl := lower_band
		rp := (1 - (lower_band / close)) * 100
		p  := (2 / rp) * account_size
    
    p := round(abs(p))
    rp := abs(rp)
    pips := abs( (close - sl) * 10000 )
    p_value := (account_size * rp) / pips
	 
	[p,rp,sl,pips,p_value]
	
[pos_size,risk_perc, stop_loss,pips, pip_value] = calc_trade_values()

// === Labels ===
// labelText = "Lot Size: " + tostring(pos_size) 
//  + "\nRisk: 0" + tostring(risk_perc, "#.00") 
//  + "% \nStop Loss: " + tostring(stop_loss, "#.0000") 
//  + "\nPips: " + tostring(pips, "#.00")
//  + "\nPip Value: " + tostring(pip_value, "#.0000")

// if show_label and trading
// 	if trade_dir > 0
//         buy = label.new(x=time,y=high,text=labelText,xloc=xloc.bar_time, yloc=yloc.belowbar,color=color.green,textcolor=color.white, size=size.normal)
//     else
//         sell = label.new(x=time,y=high,text=labelText,xloc=xloc.bar_time, yloc=yloc.abovebar,color=color.red,textcolor=color.white, size=size.normal)

// pipsText = tostring( (SSL_B - close) * 10000, "#.00") 
//   + "\nBB: " + tostring((bb_spread * 10000), "#.00")
//   + "\n" + tostring( 57.2957795 * atan(bb_upper - bb_upper[3]) * 100, "#.00" )


ma=ema(close,2)
angles=tostring( angle(fast_ma), "#.00" )
 + "\nSSL: " + tostring( angle(SSL_B), "#.00" )
 + "\nUpper: " + tostring( angle(bb_upper), "#.00" )
 + "\nLower: " + tostring( angle(bb_lower), "#.00" )
//plot(ma_slope, title="Angle", linewidth=4)

if show_angle and trading
	if trade_dir > 0
        buy = label.new(x=time,y=high,text=angles,xloc=xloc.bar_time, yloc=yloc.belowbar,color=color.green,textcolor=color.white, size=size.normal)
    else
        sell = label.new(x=time,y=high,text=angles,xloc=xloc.bar_time, yloc=yloc.abovebar,color=color.red,textcolor=color.white, size=size.normal)


// if show_pips and trading
// 	if trade_dir > 0
//         buy = label.new(x=time,y=high,text=pipsText,xloc=xloc.bar_time, yloc=yloc.belowbar,color=color.white,textcolor=color.black, size=size.normal)
//     else
//         sell = label.new(x=time,y=high,text=pipsText,xloc=xloc.bar_time, yloc=yloc.abovebar,color=color.white,textcolor=color.black, size=size.normal)


// Buy / Sell indicators
plotshape(trade_dir > 0 and show_entry ? 1 : na, title="Entry Buy", color=trade_dir > 0 ? color.orange : color.green, location = location.belowbar, style=shape.labelup, text="B", textcolor=color.white, size=size.small)
plotshape(trade_dir < 0 and show_entry ? -1 : na, title="Entry Sell", color= trade_dir < 0 ? color.orange : color.red, location = location.abovebar, style=shape.labeldown, text="S", textcolor=color.white, size=size.small)
plot(trading ,title="Trading",color= trading ? color.green : color.gray)




