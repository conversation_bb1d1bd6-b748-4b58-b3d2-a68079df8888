//@version=5
indicator('Trendilo - Multi', overlay=false)

g_trem = 'Supertrend ----------------------------------------------------'
trem_time = input.timeframe(title='Timeframe', defval='60', group=g_trem)
trend_s = input.int(4, title='Smoothing', minval=1, group=g_trem) // 1
trend_l = input.int(50, title='Lookback', minval=1, group=g_trem)
trend_o = input.float(0.85, title='ALMA Offset', step=0.01, group=g_trem)
trend_as = input.int(6, title='ALMA Sigma', minval=0, group=g_trem)
trend_b = input(1.0, 'Band Multiplier', group=g_trem)
trend_cl = input(true, 'Custom Band trend_l ? (Else same as Lookback)', group=g_trem)
trend_blen = input(20, 'Custom Band trend_l', group=g_trem)
trend_hl = input(true, group=g_trem)
trend_fill = input(true, group=g_trem)
trend_barcolor = input(false, 'Bar Color', group=g_trem)

trendillo_func()=>
    pch = ta.change(close, trend_s) / close * 100
    avpch = ta.alma(pch, trend_l, trend_o, trend_as)
    blength = trend_cl ? trend_blen : trend_l
    rms = trend_b * math.sqrt(math.sum(avpch * avpch, blength) / blength)
    cdir = avpch > rms ? 1 : avpch < -rms ? -1 : 0
    col = cdir == 1 ? color.red : cdir == -1 ? color.lime  : color.gray

    [avpch, rms, cdir, col]

[avpch, rms, cdir, col] = request.security(syminfo.tickerid, trem_time, trendillo_func() )

fplot = plot(avpch, color=trend_hl ? col : color.blue, linewidth=2)
posrms = plot(rms, color=color.new(color.purple, 0))
negrms = plot(-rms, color=color.new(color.purple, 0))
fill(fplot, posrms, color=trend_fill and cdir > 0 ? col : na, transp=50)
fill(fplot, negrms, color=trend_fill and cdir < 0 ? col : na, transp=50)
barcolor(color=trend_barcolor ? col : na)
hline(0)

