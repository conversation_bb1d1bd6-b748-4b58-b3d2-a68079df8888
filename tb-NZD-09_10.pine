//@version=4
study(title = "Trading Bot - NZD - Sept 10", shorttitle="TB - NZD - 09_10",overlay=true)

show_entry = input(title="Show Entry", type=input.bool, defval=true)
show_atr = input(title="Show ATR", type=input.bool, defval=true)
show_cond = input(title="Show Conditions", type=input.bool, defval=true)
use_logic = input(title="Use Logic", type=input.bool, defval=false)
show_friday= input(title="Show Friday", type=input.bool, defval=true)

red = #ff0062
aqua = #00bcd4
yellow = #FFFF00
orange = #ff9800
green = #4caf50
lime = #00E676
white = #ffffff
blue = #42a5f5
violet = #814dff
gray = #707070
black = #000000
sell_color = color.new(#ff0062,20)
buy_color = color.new(#00c3ff,20)

friday = color.new(sell_color,92)
bgcolor(show_friday and dayofweek == 1 ? friday : na)

angle(_src, len) =>
    rad2degree=180/3.14159265359  //pi 
    ang=rad2degree*atan((_src[0] - _src[1])/atr(len))

// Change
perc_change() =>
    perc = abs( (1 - (close[1] / close)) * 10000 )


// ===  EMA's ===
// ==================================================
ema_200 = ema(close, 200)
ema_200_angle = angle(ema_200,2)
ema_len_f = 5 //input(5, minval=1, title="EMA Length") //6
ema_fast = ema(close, ema_len_f)
ema_angle = angle(ema_fast,2)


// ===  ATR ===
// ==================================================
atrlen = 14 //input(14, "ATR Period")
atr_mult = input(1.35, "ATR Mult", step = 0.1) // 1.15
atr_stop = 0.0003 //input(0.0003, "ATR Stop", step = 0.0001) // 0.001
atr_slen = wma(tr(true), atrlen)
atr_upper = atr_slen * atr_mult + close
atr_lower = close - atr_slen * atr_mult


// ===  Kijun ===
// ==================================================
middleDonchian(Length) =>
    lower = lowest(Length)
    upper = highest(Length)
    avg(upper, lower)

basePeriods = 14 //input(14, minval=1) //26
kijun =  middleDonchian(basePeriods)
k_angle = angle(kijun,3)
xChikou = close
xPrice = close


// ===  SSL ===
// ==================================================
ssl_len = input(60, minval=1,title="SSL Len") //75
ssl_len2 = input(45, minval=1,title="SSL 2")
ssl_len3 = input(8, minval=1,title="SSL 3") //7
ssl_len4 = input(150, minval=1,title="SSL 3") //7
ssl_long_len = input(100, minval=1,title="SSL Long")
SSL = wma(2*wma(close, ssl_len/2)-wma(close, ssl_len), round(sqrt(ssl_len)))
SSL2 = wma(2*wma(close, ssl_len2/2)-wma(close, ssl_len2), round(sqrt(ssl_len2)))
SSL3 = wma(2*wma(close, ssl_len3/2)-wma(close, ssl_len3), round(sqrt(ssl_len3)))
SSL4 = wma(2*wma(close, ssl_len4/2)-wma(close, ssl_len4), round(sqrt(ssl_len4)))
ssl_long = wma(2*wma(close, ssl_long_len/2)-wma(close, ssl_long_len), round(sqrt(ssl_long_len)))
//ssl_angle = angle(SSL,3)
ssl_angle = angle(SSL2,2)
ssl_angle2 = angle(SSL3,2)
ssl_angle4 = angle(SSL4,2)
ssl_color = ssl_angle > 0 ? blue : red


// ===  BB ===
// ==================================================
BB_length = input(45) // 23
BB_stdDev = 2 //input(2, minval=2.0, maxval=3)
sqz_length = 100 //input(100, minval=2, maxval=200) // 100
sqz_threshold = 76 
bb_s = kijun
basis = sma(close, BB_length)
dev = BB_stdDev * stdev(bb_s, BB_length)
bb_upper = basis + dev
bb_lower = basis - dev
bb_spread = bb_upper - bb_lower
avgspread = sma(bb_spread, sqz_length)
bb_squeeze = 0.00
bb_squeeze := bb_spread / avgspread * 100
basis_angle = angle(basis,3)
bbu_angle = angle(bb_upper,3)
bbl_angle = angle(bb_lower,3)
// BB Zones
bb_zone = bb_squeeze < 53 ? 0 : 
 bb_squeeze < sqz_threshold ? 1 : 
 bb_squeeze < 120 ? 2 :
 bb_squeeze < 181 ? 3 :
 bb_squeeze > 181 ? 4 : na
sqz_color = bb_zone == 0 ? #0045b3 :
 bb_zone == 1 ? #ff0062 : 
 bb_zone == 2 ?  gray : 
 bb_zone == 3 ?  #00c3ff : 
 bb_zone == 4 ? white: na

bb_zones_color =  sqz_color



// === Bollinger Bands %B ===
// ==================================================
length = 20 //input(45, minval=1)
multi = 2 //input(2.0, minval=0.001, maxval=50, title="StdDev")
bbr_basis = sma(close, length)
deviation = multi * stdev(close, length)
bbr_upper = bbr_basis + deviation
bbr_lower = bbr_basis - deviation
bbr = (close - bbr_lower)/(bbr_upper - bbr_lower)



// === WAE ===
// ==================================================
sensitivity = 150
wae_fast= 20 //8
wae_slow= 40 // 50
channelLength= 20 // 45
wae_mult = 2.0//1.85
wae_dz = nz(rma(tr(true),100)) * 3.7
calc_macd(source, wae_fast, wae_slow) =>
	a = ema(source, wae_fast)
	b = ema(source, wae_slow)
	a - b
calc_BBUpper(source, length, wae_mult) => 
	wae_basis = wma(source, length)
	wae_dev = wae_mult * stdev(source, length)
	t = wae_basis + wae_dev
	[t]
calc_BBLower(source, length, wae_mult) => 
	wae_basis = wma(source, length)
	wae_dev = wae_mult * stdev(source, length)
	t = wae_basis - wae_dev
	[t]

t1 = (calc_macd(close, wae_fast, wae_slow) - calc_macd(close[1], wae_fast, wae_slow))*sensitivity * 100
t2 = (calc_macd(close[2], wae_fast, wae_slow) - calc_macd(close[3], wae_fast, wae_slow))*sensitivity * 100
[e1a] = calc_BBUpper(close, channelLength, wae_mult)
[e1b] = calc_BBLower(close, channelLength, wae_mult)
wae_line = (e1a - e1b)
wae_diff = (wae_line - wae_dz) * 100
//wae_perc = (wae_line / wae_dz) * 100
trendUp = (t1 >= 0) ? t1 : 0
trendDown = (t1 < 0) ? (-1*t1) : 0
wae_color = #000000
if t1 >= 0
	wae_color := trendUp<trendUp[1] ? lime : green
if t1 < 0
	wae_color := trendDown<trendDown[1] ? orange : red




// === ADX + DI with SMA ===
// ==================================================
adx_len = input(9,title="ADX len") // 14
adx_line = 20 // input(title="threshold", defval=20)
adx_avg = input(8,title="ADX SMA") // 10
var float adx_top = 55 // input(55,title="High")
var float adx_high = 38 // 39
var float adx_mid = 33
var float adx_center = 20
var float adx_low = 12
var float smooth_tr = 0
var float smooth_di_plus = 0
var float smooth_di_minus = 0
TrueRange = max(max(high-low, abs(high-nz(close[1]))), abs(low-nz(close[1])))
DI_plus = high-nz(high[1]) > nz(low[1])-low ? max(high-nz(high[1]), 0): 0
DI_minus = nz(low[1])-low > high-nz(high[1]) ? max(nz(low[1])-low, 0): 0
smooth_tr := nz(smooth_tr[1]) - (nz(smooth_tr[1])/adx_len) + TrueRange
smooth_di_plus := nz(smooth_di_plus[1]) - (nz(smooth_di_plus[1])/adx_len) + DI_plus
smooth_di_minus := nz(smooth_di_minus[1]) - (nz(smooth_di_minus[1])/adx_len) + DI_minus

di_plus = smooth_di_plus / smooth_tr * 100
di_minus = smooth_di_minus / smooth_tr * 100
DX = abs(di_plus-di_minus) / (di_plus+di_minus)*100
adx = sma(DX, adx_len)
adx_sma = sma(adx, adx_avg)
adx_angle = (angle(adx,2))
//hline(adx_line, color=black, linestyle=dashed)



// === RSI Wicks ===
// ==================================================
std         = false //input(false, title="Show Standard RSI")
rsi_candles = false //input(true,  title="Show Candles")
wicks       = true  //input(true,  title="Wicks based on stand-alone RSI")
src_close   = close
src_open    = open
src_high    = high
src_low     = low 
rsiw_len    = input(14, title="RSI wicks length")

norm_close  = avg(src_close,src_close[1])
gain_loss_close   = change(src_close)/norm_close
RSI_close         = 50+50*rma(gain_loss_close, rsiw_len)/rma(abs(gain_loss_close), rsiw_len)

norm_open = if wicks==true 
    avg(src_open,src_open[1])
else 
    avg(src_close,src_close[1])
gain_loss_open   = change(src_open)/norm_open
RSI_open         = 50+50*rma(gain_loss_open, rsiw_len)/rma(abs(gain_loss_open), rsiw_len)
        
norm_high = if wicks==true 
    avg(src_high,src_high[1])
else 
    avg(src_close,src_close[1])
gain_loss_high   = change(src_high)/norm_high
RSI_high         = 50+50*rma(gain_loss_high, rsiw_len)/rma(abs(gain_loss_high), rsiw_len)
        
norm_low  = if wicks==true
    avg(src_low,src_low[1])
else 
    avg(src_close,src_close[1])
gain_loss_low   = change(src_low)/norm_low
RSI_low         = 50+50*rma(gain_loss_low, rsiw_len)/rma(abs(gain_loss_low), rsiw_len)
rws_up = 60
rwb_down = 40
rws_weak = RSI_close<70 and RSI_high>70 ? 1 : 0
rws_mid = RSI_close>70 and RSI_open<70 ? 1 : 0 
rws_strong = RSI_open>69 ? 1 : 0
rwb_weak = RSI_close>30 and RSI_low<30 ? 1 : 0
rwb_mid = RSI_close<30 and RSI_open>30 ? 1 : 0
rwb_strong = RSI_open<30 ? 1 : 0


// === RSI Chart Bars ===
// ==================================================
var int pos = 0
var rsi_color = #000000
rsi_len = 14 //input(14, minval=1, title="Length")
rsi_up = rma(max(change(close), 0), rsi_len)
rsi_down = rma(-min(change(close), 0), rsi_len)
rsi = rsi_down == 0 ? 100 : rsi_up == 0 ? 0 : 100 - (100 / (1 + rsi_up / rsi_down))
isup() => rsi > 70
isdown() => rsi < 30 
// plot(rsi,title="RSI",color=color.blue)
rsi_color := isup() ? color.new(buy_color, 10) : isdown() ? color.new(color.purple, 10)  : na
barcolor(rsi_color, title="Rsi Candles")
barcolor(pos == -1 ? #ff0000: pos == 1 ? #00a000 : gray)


// === Stochastic Momentum Index ===
// ==================================================
k_len = 10 // Length") // 12
d_len = 2 // input(3, "Percent D Length")
d_len2 = 2 // input(2, "Percent D Length 2")
mom_high = 40 // input(45, "Overbought")
mom_low = -40 // input(-45, "Oversold")
// Range Calculation
ll = lowest (low, k_len)
hh = highest (high, k_len)
diff = hh - ll
rdiff = close - (hh+ll)/2

avgrel = ema(ema(rdiff,d_len),d_len)
avgdiff = ema(ema(diff,d_len),d_len)

// SMI
SMI = avgdiff != 0 ? (avgrel/(avgdiff/2)*100) : 0
mom_stoch = ema(SMI,d_len)
mom_ema = ema(SMI, k_len)
mom_angle = angle(mom_ema,2)
c_sma = mom_stoch > mom_high or mom_stoch < mom_low ? color.blue : na
// SMI 2
avgrel2 = ema(ema(rdiff,d_len2),d_len2)
avgdiff2 = ema(ema(diff,d_len2),d_len2)
SMI2 = avgdiff != 0 ? (avgrel2/(avgdiff2/2)*100) : 0
mom_sig2 = ema(SMI2,d_len2)
c_sma2 = mom_sig2 > mom_high or mom_sig2 < mom_low ? color.new(color.white,50) : na



// === Basis to ema 200 distance ===
// ==================================================

be_high = 50
be_up = 10
be_down = -10
be_low = -50
be_mult = syminfo.currency == 'JPY' ? 100 : syminfo.currency == 'NZD' ? 10000 : 10000
be_dist = (basis - ema_200) * be_mult
bbu_dist = (bb_upper - ema_200) * be_mult
bbl_dist = (bb_lower - ema_200) * be_mult
be_angle = angle(be_dist,2)
be_color = be_dist>0?color.green  : color.red



// === MACD Crossover === 
// ==================================================
fastLength = 8 //input(8, minval=1) // 8 //8
slowLength = 25 //input(25,minval=1) // 16 // 21
signalLength= 9 //input(9,minval=1) // 11 // 5
fastMA = ema(close, fastLength)
slowMA = ema(close, slowLength)
macd_c = fastMA - slowMA
macd_sig = sma(macd_c, signalLength)
//pos = 0
//pos := iff(macd_sig < macd_c , 1,iff(macd_sig > macd_c, -1, nz(pos[1], 0))) 
//mc_color = pos == -1 ? red: pos == 1 ? green : blue
mc_angle = angle(macd_c,2)
s_angle = angle(macd_sig,2)
mc_diff = macd_c / macd_sig

macd_high = syminfo.currency == 'NZD' ? 0.00170 : syminfo.currency == 'JPY' ? 0.395 : 0.0012
macd_up = 0.160
macd_down = -0.160
macd_low = syminfo.currency == 'NZD' ? -0.00170 : syminfo.currency == 'JPY' ? -0.395 : -0.0012



// === Schaff Trend Cycle (STC) ===
// ==================================================
stc_signal = 12 // input(12,"Length")
stc_fast = 26 //input(26,"FastL/ength")
stc_slow = 50 //input(55,"SlowLength") //55
stc_line = 50 //(stc_high + stc_low) * 0.5
stc_bias = 0.203 // input(0.203,"STC Sensitivity")
stc_high = 92
stc_up = 85// 75
stc_down = 25//12
stc_low = 5
stc_macd(BBB, stc_fast, stc_slow) =>
    stc_fastMA = ema(BBB, stc_fast)
    stc_slowMA = ema(BBB, stc_slow)
    stc_macd = stc_fastMA - stc_slowMA
    stc_macd
    
AAAAA(stc_signal, stc_fast, stc_slow) => 
    AAA= stc_bias
    var CCCCC = 0.0
    var DDD = 0.0
    var DDDDDD = 0.0
    var EEEEE = 0.0
    tmp_macd = stc_macd(close,stc_fast,stc_slow)     
    lowest = lowest(tmp_macd, stc_signal)
    highest = highest(tmp_macd, stc_signal) - lowest    
    CCCCC := (highest > 0 ? ((tmp_macd - lowest) / highest) * 100 : nz(CCCCC[1])) 
    DDD := (na(DDD[1]) ? CCCCC : DDD[1] + (AAA * (CCCCC - DDD[1]))) 
    DDDD = lowest(DDD, stc_signal) 
    DDDDD = highest(DDD, stc_signal) - DDDD     
    DDDDDD := (DDDDD > 0 ? ((DDD - DDDD) / DDDDD) * 100 : nz(DDDDDD[1])) 
    EEEEE := (na(EEEEE[1]) ? DDDDDD : EEEEE[1] + (AAA * (DDDDDD - EEEEE[1])))
    EEEEE

stc = AAAAA(stc_signal,stc_fast,stc_slow)
stc_color = stc > stc[1] ? buy_color : sell_color


// === RES - Ranging EMA Spread  ===
// ==================================================
ema1length = 40 // 12
ema2length = 100 //43
ranginglength = 3
rangingmaxvalue = 0.12// 0.14 0.1
rangingminvalue = -0.1
enablebarcolors = false


// EMA spread
ema1 = ema(close, ema1length)
ema2 = ema(close, ema2length)
spread = ((ema2 / ema1) -1) * 100
r1 = (spread >= rangingminvalue and spread <= rangingmaxvalue) 
r2 = (spread[1] >= rangingminvalue and spread[1] <= rangingmaxvalue)
r3 = (spread[2] >= rangingminvalue and spread[2] <= rangingmaxvalue)
r4 = (spread[3] >= rangingminvalue and spread[3] <= rangingmaxvalue)
r5 = (spread[4] >= rangingminvalue and spread[4] <= rangingmaxvalue)

ranging = false

if (ranginglength == 1) 
    ranging := r1
if (ranginglength == 2) 
    ranging := r1 and r2
if (ranginglength == 3) 
    ranging := r1 and r2 and r3
if (ranginglength == 4) 
    ranging := r1 and r2 and r3 and r4
if (ranginglength == 5) 
    ranging := r1 and r2 and r3 and r4 and r5

res = spread > 0 ? 0-spread : abs(spread)
res := res * 100
col_low = gray // grey
col_mid = blue// navy
col_high = aqua // aqua
res_c = ranging ? col_low : spread > spread[1] ? col_mid : col_high



// === Plot === 
// ==================================================



// Angles
plot(res, color=res_c, title="RES",style=plot.style_circles)
plot(ema_200_angle, color= white , title="EMA 200 Angle",style=plot.style_circles)
plot(bbu_angle, color= white , title="BB Upper Angle",style=plot.style_circles)
plot(bbl_angle, color= white , title="BB Lower Angle",style=plot.style_circles)
plot(basis_angle, color= white , title="Basis angle",style=plot.style_circles)
plot(be_dist, color= white , title="BE Dist",style=plot.style_circles)
plot(mom_stoch, color= white , title="MOM smi",style=plot.style_circles)
plot(mom_ema, color= white , title="MOM ema",style=plot.style_circles)
plot(wae_line,title="Explosion Line",style=plot.style_circles,color=wae_color)
plot(wae_dz,title="Dead Zone",style=plot.style_circles)
plot(di_plus, color=green, title="DI+",style=plot.style_circles)
plot(di_minus, color=red, title="DI-",style=plot.style_circles)
plot(adx, color=yellow, title="ADX",style=plot.style_circles)
plot(adx_sma, color=white, title="ADX SMA",style=plot.style_circles)
plot(ssl_angle, color=white , title="SSL angle",style=plot.style_circles)
plot(ssl_angle2, color= white , title="SSL angle 2",style=plot.style_circles)
plot(ssl_angle4, color= white , title="SSL angle 4",style=plot.style_circles)

plot(stc, color= white , title="STC",style=plot.style_circles)
//plot(k_angle, color= white , title="Kijun Angle",style=plot.style_circles)
//plot(ema_angle, color= white , title="EMA Fast Angle",style=plot.style_circles)


plot(bbr, "Bollinger Bands %B", color=color.teal,linewidth=0)
plot(rsi, "RSI", color=#8E1599)


// Kijun
plot(kijun, color=red, title="Kijun",linewidth=2)
// SSL
plot(SSL, color=orange , title="SSL")
plot(SSL2, color=ssl_color , title="SSL 2")
plot(SSL3, color= green , title="SSL 3")
plot(SSL4, color= green , title="SSL 4",linewidth=2)
plot(ssl_long, color= yellow , title="SSL Long",linewidth=2)

// BB
plot(basis, title="Basis", color=bb_zones_color,linewidth=3)
p1 = plot(bb_upper, "BB Upper ",color=bb_zones_color,linewidth=2)
p2 = plot(bb_lower, "BB Lower ",color=bb_zones_color,linewidth=2)
//fill(p1,p2, bb_zones_color)
// EMA 200 bands large
plot(ema_200, "EMA 200", color=color.new(yellow,50),linewidth=2 )
//plot(ema_fast, color=color.blue, title="EMA Fast")
// ATR
plot(show_atr ? atr_upper : na, "+ATR Upper", color=color.new(#ffffff,85))
plot(show_atr ? atr_lower : na, "-ATR Lower", color=color.new(#ffffff,85))
// plot(show_atr ? atr_upper + atr_stop : na, "+ATR stop", color=color.new(#ffff00,85))
// plot(show_atr ? atr_lower - atr_stop : na, "-ATR stop", color=color.new(#ffff00,85))

plotshape(rws_strong,title="Up High",color=#ff0000,style=shape.circle,location=location.top)
plotshape(rws_mid,title="Up Mid",color=orange,style=shape.circle,location=location.top)
plotshape(rws_weak ,title="Up Weak",color=yellow,style=shape.circle,location=location.top)
plotshape(RSI_close>30 and RSI_low<30 and close<open?1:na ,title="Low weak",color=violet ,style=shape.circle,location=location.bottom)
plotshape(RSI_close<30 and RSI_open>30 and close<open?1:na ,title="Low Mid",color=#0053ff,style=shape.circle,location=location.bottom)
plotshape(RSI_open<30 and close<open?1:na ,title="Low strong",color=lime,style=shape.circle,location=location.bottom)



var bool enter_exit = false
//var int trade_dir = 0
var int state = 0
var int bar_num = 0
var float lastPrice = 0.0
var mult_diff = 10000
var int num_pips = 17
var int num_bars = 4

entry_signal() =>
	candle = close > open ? 1 : 0
    dir = 0
    counter = 0
    cond = ''
    allow = false


    // Sell

    // ADX
    if bb_zone>1 and candle==1 and rws_strong==1 and close>SSL3 and
     adx>adx_high and adx_sma>adx_high and di_plus>di_plus[1]
     and not(adx<adx_sma and mom_ema>mom_high)
        dir := -1
        cond := 'adx'

    // RSI
    if bb_zone>2 and candle==1 and wae_line>wae_dz and wae_color==green and close>SSL3 and 
     mom_ema>0 and stc>88 and mom_stoch>mom_high and res>0
     and not(rwb_weak==0 and rwb_mid==0 and rwb_strong==0)
        dir := 1
        cond := 'rsi'

    // Sell - RSI zone 2
    // if bb_zone<3 and candle==1 and stc>88 and low>ema_200 and ssl_long>ema_200 and
    //  (rws_weak==1 or rws_mid==1 or rws_strong==1)
    //  and not(bb_zone<2 and low>bb_upper)
    //     dir := -1
    //     cond := 'rsi-1-2'

    // Filter out
    if dir == -1 and ssl_long<SSL4
        dir := 0

    // RSI + RES
    if bb_zone>0 and candle == 1 and res_c==gray
     and (rws_weak==1 or rws_mid==1 or rws_strong==1)
     and not(bb_zone<3 and adx<di_plus)
     and not(atr_upper>ema_200 and atr_lower<ema_200 )
     and not(di_minus<adx_high and adx>adx_high)
     //and close<bb_upper and high>bb_upper
        dir := -1
        cond := 'rsi-res'
        counter := 1

    // Zone 0
    if bb_zone==0 and candle==1 and bb_lower>ema_200 and res_c==gray and
     SSL3>basis and stc>50 and adx>adx_mid
        dir := -1
        cond :='0-cnt' 
        counter := 1

    if candle==1 and (rws_weak==1 or rws_mid==1 or rws_strong==1) and
     (
     (close<bb_upper and atr_upper>bb_upper) or 
     (open<ema_200 and atr_upper>ema_200)
     )
     and not(perc_change()>25)
     // and bbu_angle<0
        dir := -1
        cond := 'under-bb'

    if bb_zone>2 and candle==1 and close>bb_upper and SSL2>close
        dir := -1
        cond := 'SSL2-cnt'
        counter := 1
    



    // === Counter Buy ===
    // ===========

    // EMA 200
    if candle==0 and close>ema_200 and atr_lower<ema_200 and 
     bb_lower>ema_200 and bbl_dist<36 and basis_angle>-5 and bbu_angle>-3
     and not(atr_upper>bb_upper or open>basis)
        dir := 1
        cond := 'b-ema-cnt'
        counter := 1

    // bb-cnt
    if bb_zone>1 and candle==0 and low<bb_lower and 
     ema_200_angle>-1.5 and basis>ema_200 and bbl_angle>-1
     and not(open<bb_lower)
     and not(low<ema_200 and bb_lower>ema_200)
        dir := 1
        cond := 'bb-cnt'



    // === Buy ===
    // ===========

    // ADX
    if bb_zone>1 and candle==0 and rwb_strong==1 and close<ema_fast and 
     adx_sma>adx_high and adx>adx_sma and di_minus>adx_mid and di_minus>di_minus[1]
     and not(mom_ema>mom_low)
        dir := 1
        cond := 'adx'

    // RSI
    if bb_zone>2 and candle==0 and wae_line>wae_dz and wae_color==red and close<SSL3 and 
     mom_ema<0 and stc<12 and mom_stoch<mom_low and res<0
     and not(rwb_weak==0 and rwb_mid==0 and rwb_strong==0)
        dir := 1
        cond := 'rsi'


    if candle==0 and bb_zone<3 and open<bb_lower and bb_upper<ema_200 and
     wae_color==red and wae_line>wae_dz and isdown()==1
     and not(isdown()==0)
     and not(di_minus<di_minus[1])
        dir := 1
        cond := 'bbu-below\n0-1-2'


    // when bb_lower>ema_200 only use rws==strong?
    // why RSIC indicator different than TB?

    // Filter out for strong uptrend
    // if dir==-1 and isup()==0 and mom_ema>mom_high and RSI_close<70
    //     dir := 0

    // RSI zone 2
    // if (bb_zone==1 or bb_zone==2) and candle==0 and 
    //  stc<12 and (rwb_weak==1 or rwb_mid==1 or rwb_strong==1)
    //  and not(low<bb_lower and SSL2>open)
    //     dir := 1
    //     cond := 'rsi-1-2'


    // Filter out
    if dir == 1 and ssl_long>SSL4
        dir := 0

    if bb_zone>0 and candle== 0 and res_c==gray
     and (rwb_weak==1 or rwb_mid==1 or rwb_strong==1)
     and not(bb_zone<3 and close<bb_lower and adx<di_minus and di_minus<adx_high)
     and not(adx>adx_top)
     and not(atr_upper>ema_200 and atr_lower<ema_200 and atr_lower>bb_lower)
     and not(di_minus<adx_high and adx>adx_high)
     and not(low<bb_lower and)
     //and not(bb_zone<3 and ssl_long>basis)
        dir := 1
        cond := 'rsi-res'
        counter := 1

    // zone 4 RWB
    if bb_zone==4 and candle==0 and open<bb_lower and rwb_strong==1
     and SSL2>bb_lower
        dir := 1
        cond := 'rwb-4'

    // zone 4 RSI - Feb 21
    if bb_zone==4 and candle==0 and low>bb_lower and atr_lower<bb_lower and 
     rwb_strong==1 and isdown()==1 and SSL2>bb_lower and SSL>close
        dir := 1
        cond := 'rsi-4'
        
    // Zone 0 under ema
    if bb_zone==0 and candle==0 and  
     ( (close<bb_lower) or (low>bb_upper and atr_lower<bb_lower and high<basis) )
     and not(di_minus<di_minus[1])
     and not(bbu_dist>be_low and di_minus<adx_mid)
     and not(perc_change()>20)
     and not(bb_upper>ema_200)
     and not(rwb_weak==1 and rwb_mid==1 and rwb_strong==1)
     and not(stc<12)
     //and not(stc>50)
     //and not(isdown() and RSI_close<40)
        dir := 1
        cond := '0-b'

    // Zone 0 between
    // if bb_zone==0 and candle==0 and close<SSL3 and  
    //  ( (close<bb_lower) or (atr_lower<bb_lower and close>bb_lower) )
    //  and not (bb_upper<ema_200 or bb_lower>ema_200) 
    //  and not(perc_change()>20)
    //  and not(RSI_close>40)
    //  and not(stc>50)
    //     dir := 1
    //     cond := '0-1-be'
    //     counter := basis_angle>0 ? 1 : 0

    // 2 areas strong downtrend. 
    // Feb 18 basis is above ema
    // Feb 24 basis is below ema 


    // === Counter Sell
    // ================

    // basis
    if candle==1 and bb_zone>2 and close>SSL3 and close<ema_200 and basis_angle<0 and 
     atr_upper>basis and bbu_dist<be_high and mom_ema>0 and
     di_plus>di_minus and atr_lower>ssl_long
     and not(mom_ema>mom_high)
     and not(bb_zone==3 and di_plus<adx_center)
     and not(bb_zone==4 and bb_upper<ema_200)
     //and not(bb_zone==3 and RSI_high<69)
     //and not(atr_lower<SSL2 or atr_lower<kijun)
     //stc>stc_high and bbu_dist>be_up
        dir := -1
        cond := 'basis-cnt'
        counter := 1

    // Basis zone 2
    // if candle==1 and bb_zone==2 and close>SSL3 and basis_angle<1 and 
    //  atr_upper>basis and bbu_dist<be_high and mom_ema>0 and mom_stoch>mom_high and
    //  di_plus>di_minus
    //  and not(bb_zone==3 and di_plus<adx_center)
    //  and not(mom_ema>mom_high)
    //     dir := -1
    //     cond := 'basis-2-cnt'
    //     counter := 1

    // Jul 19 - 27 - lot of good entries not being triggered

    if candle==1 and ema_200_angle<1 and atr_upper>ema_200 and
     mom_stoch>mom_high and atr_lower>kijun
     and not(be_dist>be_down or bbu_dist>be_up)
     and not(bb_upper>ema_200)
     and not(low>ema_200)
     and not(di_plus<adx_center or adx>di_plus)
     and not(bb_zone==1 and isup()==0)
     and not(open>bb_upper)
     //and not(bbu_angle>0 and bbl_angle>0)
        dir := -1
        cond := 'ema-cnt'
        counter := 1

    if bb_zone<2 and candle==1 and ema_200_angle<1 and
     atr_upper<ema_200 and bbu_dist<be_down and
     (close>bb_upper or (close<bb_upper and high>bb_upper) )
     and not(low>bb_upper or perc_change()>20 )
     and not(bb_zone==0 and close<bb_upper)
     and not(res_c==gray)
     and not(ssl_long<bb_lower)
        dir := -1
        cond := '0-1-bb\ncnt'
        counter := 1


    // Filter out same direction trades, wait 4 candles
    // and price higher than 20 pips before allowing another trade
    if dir == state and use_logic
        allow := (bar_index - bar_num) >= num_bars ? true : false
        // sell
        if state == -1
            dir := ((close - lastPrice) * mult_diff) > num_pips and allow ? dir : 0
        // buy
        if state == 1
            dir := ((close - lastPrice) * mult_diff) < (num_pips * -1) and allow ? dir : 0

    //var int enter_exit = basis_angle > 0 and dir == -1 ? 0 : basis_angle < 0 and dir
    type = dir > 0 ? 1 : dir < 0 ? -1 : 0
    [type,counter,cond]

[trade_dir,counter,condition] = entry_signal() 

if trade_dir != 0 and use_logic
    state := trade_dir

    enter_exit := counter == 1 ? 1 : 0
    // Sell
    // if basis_angle > 0 and trade_dir == -1
    //     enter_exit := -1
    // // Counter
    // if basis_angle > 0 and trade_dir == 1
    //     enter_exit := 0
    // // Buy
    // if basis_angle < 0 and trade_dir == 1
    //     enter_exit := -1
    // // Counter
    // if basis_angle < 0 and trade_dir == -1
    //     enter_exit := 0
    // if basis>ema_200
    //     enter_exit := trade_dir == -1 ? false : trade_dir == 1 or counter ? true : false
    // if basis<ema_200
    //     enter_exit := trade_dir == 1 ? false : trade_dir == -1 or counter ? true : false
    //enter_exit := counter == 1 ? true : enter_exit == false ? true : false
    lastPrice := close
    bar_num   := bar_index

// if enter_exit != trade_dir and trade_dir != 0
//     enter_exit := enter_exit == false ? true : false
labelText = tostring(condition)
trade_color = trade_dir > 0  ? buy_color : sell_color
if trade_dir != 0 and show_cond
	if trade_dir > 0
        buy = label.new(x=time,y=high,text=labelText,xloc=xloc.bar_time, yloc=yloc.belowbar,color=trade_color,textcolor=color.white, size=size.normal,style=label.style_label_up)
    else
        sell = label.new(x=time,y=high,text=labelText,xloc=xloc.bar_time, yloc=yloc.abovebar,color=trade_color,textcolor=color.white, size=size.normal)

plot(counter,title="Counter Trade", style=plot.style_circles)
plot(state,"State",style=plot.style_circles)

plotshape(show_entry and trade_dir == 1 and enter_exit == 0 ? 1 : na, title="Entry Buy", color=trade_color, location = location.belowbar, style=shape.labelup, text="B", textcolor=color.white, size=size.small)
plotshape(show_entry and trade_dir == 1 and enter_exit == -1 ? 1 : na, title="Exit Buy", color=trade_color, location = location.belowbar, style=shape.labelup, text="Exit", textcolor=color.white, size=size.small)
plotshape(show_entry and trade_dir == -1 and enter_exit == 0 ? -1: na, title="Entry Sell", color=trade_color, location = location.abovebar, style=shape.labeldown, text="S", textcolor=color.white, size=size.small)
plotshape(show_entry and trade_dir == -1 and enter_exit == -1 ? -1 : na, title="Exit Sell", color=trade_color, location = location.abovebar, style=shape.labeldown, text="Exit", textcolor=color.white, size=size.small)


