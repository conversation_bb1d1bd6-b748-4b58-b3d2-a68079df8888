//@version=4
study(title = "Trading Bot", shorttitle = "Trading Bot", overlay=true, format = format.price, precision = 4)


show_entry = input(title="Show Entry", type=input.bool, defval=true)
show_atr = input(title = "Show Stop Loss / ATR", type = input.bool, defval = false)
use_special = input(title="Use Special Setups", type=input.bool, defval=true)
show_c_patterns = input(title="Show Candlestick Patterns", type=input.bool, defval=false)
show_squeeze = input(title="Show BB Zones", type=input.bool, defval=true)
show_BB = true //input(title="Show BB", type=input.bool, defval=true)
show_BB_faded = true //input(title="Show Fill Faded", type=input.bool, defval=true)
use_candles =  false //input(title="Use Special Candles", type=input.bool, defval=false)
use_deadzone = true //input(title="Deadzone", type=input.bool, defval=false)
dz_time = "1715-1830" //input(title="DZ Timeframe", type=input.session, defval= "1715-1830")
//use_basis = input(title="Use Basis Filter", type=input.bool, defval=true)
//use_bb_filter = input(title="BB band inside EMA band", type=input.bool, defval=true)
//use_ssl_cross_up = input(title="Uptrend - SSL above Basis ", type=input.bool, defval=true)
//use_ssl_cross_counter = input(title="Uptrend Counter - SSL above SMA ", type=input.bool, defval=true)


// === Colors ===
sell_color = #ff0062
buy_color = #00c3ff
red = #e04566 // e04566 //dd1c1c
dark_red = #ff0000
orange = #FF7F00
green = #55aa1a
dark_green = #008000
light_green = #00FF00
blue = #2196f3
white = #ffffff
gray  = #707070

// === Functions ===
angle(_src, len) =>
    rad2degree=180/3.14159265359  //pi 
    ang=rad2degree*atan((_src[0] - _src[1])/atr(len))

Session(sess) => na(time(timeframe.period, sess)) == false

// Change
perc_change = abs( (1 - (close[1] / close)) * 10000 )


// === Bollinger Bands %B ===
// ==================================================
bb_length = input(25, minval=1, title="Bollinger Length")
bbr_length = input(25, minval=1, title="BBR length") // 15 or 25
bbr_mult = input(2.0, minval=0.001, maxval=4, title="BBR StdDev") // 2.2 or 2.0
bbr_basis = sma(close, bbr_length)
bbr_dev = bbr_mult * stdev(close, bbr_length)
bbr_upper = bbr_basis + bbr_dev
bbr_lower = bbr_basis - bbr_dev
bbr = (close - bbr_lower)/(bbr_upper - bbr_lower)


// ===  EMA 200 ===
// ==================================================
ema_200 = ema(close, 200)
ema_200_dev = 0.27 * stdev(close, 200)
ema_200_upper = ema_200 + ema_200_dev
ema_200_lower = ema_200 - ema_200_dev
ema_200_outer_dev = 0.23 * stdev(close, 200)
ema_200_outer_upper = ema_200_upper + ema_200_outer_dev
ema_200_outer_lower = ema_200_lower - ema_200_outer_dev


// ===  Moving Average EMA SMA ===
// ==================================================
ema_len = 8 //input(8, minval=1, title="ema Length") // default 8
sma_len = input(50, minval=1, title="SMA Length") // 45
src = close
ema_fast = ema(src, ema_len)
up = ema_fast > ema_fast[1]
down = ema_fast < ema_fast[1]
fast_ema_angle = angle(ema_fast,4)
// Slow SMA
sma_slow = sma(close, sma_len)
up2 = sma_slow > sma_slow[1]
down2 = sma_slow < sma_slow[1]
sma_angle = angle(sma_slow,4)
ema_angle = angle(ema_fast,4)
mycolor = up ?  green : down ? red : #00c3ff
sma_slow_color = up2 ? green : down2 ? red : blue


// === Schaff Trend Cycle (STC) ===
// ==================================================
stc_signal = 12 // input(12,"Length")
stc_fast = 26 //input(26,"FastL/ength")
stc_slow = 50 //input(50,"SlowLength")
stc_low = 12.6
stc_high = 88
stc_line = 50 //(stc_high + stc_low) * 0.5
stc_macd(BBB, stc_fast, stc_slow) =>
    stc_fastMA = ema(BBB, stc_fast)
    stc_slowMA = ema(BBB, stc_slow)
    stc_macd = stc_fastMA - stc_slowMA
    stc_macd
    
AAAAA(stc_signal, stc_fast, stc_slow) => 
    AAA=input(0.203,"STC Sensitivity") // 0.25
    var CCCCC = 0.0
    var DDD = 0.0
    var DDDDDD = 0.0
    var EEEEE = 0.0
    tmp_macd = stc_macd(close,stc_fast,stc_slow)     
    lowest = lowest(tmp_macd, stc_signal)
    highest = highest(tmp_macd, stc_signal) - lowest    
    CCCCC := (highest > 0 ? ((tmp_macd - lowest) / highest) * 100 : nz(CCCCC[1])) 
    DDD := (na(DDD[1]) ? CCCCC : DDD[1] + (AAA * (CCCCC - DDD[1]))) 
    DDDD = lowest(DDD, stc_signal) 
    DDDDD = highest(DDD, stc_signal) - DDDD     
    DDDDDD := (DDDDD > 0 ? ((DDD - DDDD) / DDDDD) * 100 : nz(DDDDDD[1])) 
    EEEEE := (na(EEEEE[1]) ? DDDDDD : EEEEE[1] + (AAA * (DDDDDD - EEEEE[1])))
    EEEEE

stc = AAAAA(stc_signal,stc_fast,stc_slow)
stc_color = stc > stc[1] ? buy_color : sell_color


// === SSL ===
// ==================================================
ssl_len = input(25,"SSL Length") //55
SSL = wma(2 * wma(close, ssl_len / 2) - wma(close, ssl_len), round(sqrt(ssl_len)))
ssl_multy = 0.2
ssl_range = tr
rangema = ema(ssl_range, ssl_len)
upperk = SSL + rangema * ssl_multy
lowerk = SSL - rangema * ssl_multy
ssl_angle = angle(SSL,4)
ssl_color_buy = #00c3ff
ssl_color_sell = #ff0062
ssl_color = close > upperk ? ssl_color_buy : close < lowerk ? ssl_color_sell : gray
// SSL bands
ssl_dev = 0.08 * stdev(close, ssl_len)
ssl_upper = SSL + ssl_dev
ssl_lower = SSL - ssl_dev


// === Bollinger Bands ===
// ==================================================
bb_use_ema = input(false, title="Use EMA for Bollinger Band")
bb_mult = input(2.0, title="Base Multiplier", minval=0.5, maxval=10)
sqz_length = 100
sqz_threshold = input(76, minval=0, title="Squeeze Threshold") // 78 // old 82 //  86  // 73 //  65

// Breakout Indicator Inputs
ema_1 = ema(close, bb_length)
sma_1 = sma(close, bb_length)
bb_basis = bb_use_ema ? ema_1 : sma_1
basis_angle = angle(bb_basis,4)
bb_basis_dev = 0.05 * stdev(close, bb_length)
bb_basis_up = bb_basis + bb_basis_dev
bb_basis_low = bb_basis - bb_basis_dev
bdev = stdev(close, bb_length)
bb_dev = bb_mult * bdev
bb_dev2 = 0.75 * bdev
// Bands
bb_inner_upper = bb_basis + bb_dev
bb_inner_lower = bb_basis - bb_dev
// Calculate BB spread and average spread
bb_spread = bb_inner_upper - bb_inner_lower
avgspread = sma(bb_spread, sqz_length)
// Calculate BB relative %width for Squeeze indication
bb_squeeze = 0.00
bb_squeeze := bb_spread / avgspread * 100
// Calculate Upper and Lower band painting offsets based on 50% of atr.
bb_offset = atr(14) * 0.5
bb_upper = bb_inner_upper + bb_offset
bb_lower = bb_inner_lower - bb_offset



// ===  ATR ===
// ==================================================
atrlen = input(14, "ATR Period")
atr_mult = input(1.25, "ATR Mult", step = 0.1) // 1.15
atr_slen = wma(tr(true), atrlen)
atr_upper = atr_slen * atr_mult + close
atr_lower = close - atr_slen * atr_mult


// === RSI Chart Bars ===
// ==================================================
var rsi_color = #000000
rsi_len = 14 //input(14, minval=1, title="Length")
rsi_up = rma(max(change(close), 0), rsi_len)
rsi_down = rma(-min(change(close), 0), rsi_len)
rsi = rsi_down == 0 ? 100 : rsi_up == 0 ? 0 : 100 - (100 / (1 + rsi_up / rsi_down))
isup() => rsi > 70
isdown() => rsi < 30
// plot(rsi,title="RSI",color=color.blue)
rsi_color := isup() ? buy_color : isdown() ? sell_color : na
barcolor(rsi_color, title="Rsi Candles")



// === WaveTrend with Crosses [LazyBear]  ===
// ==================================================
n1 = input(8, "Channel Length")
n2 = input(25, "Average Length")
obLevel1 = input(60, "Over Bought Level 1")
obLevel2 = input(52, "Over Bought Level 2")
obLevel3 = input(45, "Over Bought Level 3")
osLevel2 = input(-50, "Over Sold Level 2")
osLevel1 = input(-60, "Over Sold Level 1")
 
ap = hlc3 
esa = ema(ap, n1)
d = ema(abs(ap - esa), n1)
ci = (ap - esa) / (0.015 * d)
tci = wma(ci, n2)
 
wt1 = tci
wt2 = sma(wt1,4)
wt_diff = wt1 - wt2
// plot(wt1, title="WT Green", color=green, style=plot.style_circles)
// plot(wt_diff, title="WT Diff", color=white, transp=80, style=plot.style_circles)
plot(wt2, title="WT Red", color=red, style=plot.style_circles)



// === WAE - LazyBear === 
// ==================================================
sensitivity = 150
fastLength= 20
slowLength= 50
channelLength= 20
wae_mult = 1.85 //2.0
DEAD_ZONE = nz(rma(tr(true),100)) * 3.7
calc_macd(source, fastLength, slowLength) =>
	fastMA = ema(source, fastLength)
	slowMA = ema(source, slowLength)
	fastMA - slowMA
calc_BBUpper(source, length, wae_mult) => 
	wae_basis = wma(source, length)
	dev = wae_mult * stdev(source, length)
	t = wae_basis + dev
	[t]
calc_BBLower(source, length, wae_mult) => 
	wae_basis = wma(source, length)
	dev = wae_mult * stdev(source, length)
	t = wae_basis - dev
	[t]

t1 = (calc_macd(close, fastLength, slowLength) - calc_macd(close[1], fastLength, slowLength))*sensitivity * 100
t2 = (calc_macd(close[2], fastLength, slowLength) - calc_macd(close[3], fastLength, slowLength))*sensitivity * 100
[e1a] = calc_BBUpper(close, channelLength, wae_mult)
[e1b] = calc_BBLower(close, channelLength, wae_mult)
e1 = (e1a - e1b)
wae_diff = (e1 - DEAD_ZONE) * 100
trendUp = (t1 >= 0) ? t1 : 0
trendDown = (t1 < 0) ? (-1*t1) : 0
wae_color = #000000
if t1 >= 0
	wae_color := trendUp<trendUp[1] ? light_green : dark_green
if t1 < 0
	wae_color := trendDown<trendDown[1] ? orange : dark_red
plot(wae_diff, color=wae_color, title="WAE Diff",style=plot.style_circles)

// === Candlesticks ===
// ==================================================
DojiSize = input(0.1, minval=0.01, title="Doji size")
c_patterns(_src, type) =>
    if type == 'doji'
		doji=(abs(open - close) <= (high - low) * DojiSize)

candle_transp = 20

// Doji
doji=(abs(open - close) <= (high - low) * DojiSize)
// Hammer
h =(((high - low)>3*(open -close)) and  ((close - low)/(.001 + high - low) > 0.6) and ((open - low)/(.001 + high - low) > 0.6))
// Inverted Hammer
ih = (((high - low)>3*(open -close)) and  ((high - close)/(.001 + high - low) > 0.6) and ((high - open)/(.001 + high - low) > 0.6))
// Bearish Engulfing
bear_e=(close[1] > open[1] and open > close and open >= close[1] and open[1] >= close and open - close > close[1] - open[1] )
// Bearish Harami
bear_h=(close[1] > open[1] and open > close and open <= close[1] and open[1] <= close and open - close < close[1] - open[1] )
// Bullish Harami
bull_h=(open[1] > close[1] and close > open and close <= open[1] and close[1] <= open and close - open < open[1] - close[1] )
// Bullish Engulfing
bull_e=(open[1] > close[1] and close > open and close >= open[1] and close[1] >= open and close - open > open[1] - close[1] )



// === BB Squeeze Zones ===
// ==================================================
bb_sqz_zone = bb_squeeze < 53 ? 0 : 
 bb_squeeze < sqz_threshold ? 1 : 
 bb_squeeze < 120 ? 2 :
 bb_squeeze < 180 ? 3 :
 bb_squeeze > 181 ? 4 : na
sqz_color = bb_sqz_zone == 0 ? #0045b3 :
 bb_sqz_zone == 1 ? sell_color : 
 bb_sqz_zone == 2 ?  gray : 
 bb_sqz_zone == 3 ?  buy_color : 
 bb_sqz_zone == 4 ? white: na
// BB location Zones 
bb_lower_zone = bb_lower > ema_200_upper ? 1 : 
 bb_inner_lower < ema_200_upper and bb_lower > ema_200 ? 2 : 
 bb_inner_lower < ema_200_upper and bb_inner_lower > ema_200 and bb_lower < ema_200 ? 3 : 
 bb_inner_lower < ema_200 and bb_lower > ema_200_lower ? 4 : 
 bb_inner_lower < ema_200 and bb_lower < ema_200_lower ? 5 :
 bb_inner_lower < ema_200_lower and bb_lower < ema_200_lower ? 6 : 7 

bb_upper_zone = bb_upper < ema_200_lower ? 1 : 
 bb_upper > ema_200_lower and bb_inner_upper < ema_200_lower ? 2 : 
 bb_upper > ema_200_lower and  bb_upper < ema_200 and bb_inner_upper > ema_200_lower ? 3 : 
 bb_upper > ema_200 and bb_inner_upper < ema_200 and bb_inner_upper > ema_200_lower ? 4 : 
 bb_upper < ema_200_upper and bb_inner_upper > ema_200 ? 5 : 
 bb_upper > ema_200_upper and bb_inner_upper > ema_200 and bb_inner_upper < ema_200_upper ? 6 :
 bb_upper > ema_200_upper and bb_inner_upper > ema_200_upper ? 7 : 8 

// bb_loc_color = bb_lower_zone == 1 ? sell_color : 
//  bb_lower_zone == 2 ?  #fffa00 : 
//  bb_lower_zone == 3 ?  buy_color : 
//  bb_lower_zone == 4 ?  white :
//  bb_lower_zone == 5 ? green: 
//  bb_lower_zone == 6 ? sell_color: gray 

bb_loc_color = bb_upper_zone == 1 ? sell_color : 
 bb_upper_zone == 2 ?  orange : 
 bb_upper_zone == 3 ?  #fffa00 : 
 bb_upper_zone == 4 ?  buy_color :
 bb_upper_zone == 5 ? green: 
 bb_upper_zone == 6 ? light_green:
 bb_upper_zone == 7 ? white: blue  

bb_zones_color =  show_squeeze ? sqz_color : bb_loc_color


// === Plot ===
// ==================================================
plot(bb_sqz_zone, title="BB Zones", color=sqz_color, style=plot.style_circles)
plot(bb_lower_zone, title="BB Lower Zones", color=bb_loc_color, style=plot.style_circles)
plot(bb_upper_zone, title="BB Upper Zones", color=bb_loc_color, style=plot.style_circles)
plot(bb_squeeze, title="BB Squeeze", color=sqz_color, transp=10, linewidth=0,style=plot.style_circles)
plot(bbr, "Bollinger Bands %B", color=color.teal)
plot(stc,color=stc_color, title="STC",linewidth=2,style=plot.style_circles)
plot(SSL, title="SSL", linewidth=2, color=ssl_color)
plot(sma_slow , title="SMA", color=sma_slow_color, linewidth=3, transp=15)
plot(bb_basis, title="Basis Line", color=color.red, transp=10, linewidth=1)
plot(sma_angle, title="SMA angle", style=plot.style_circles,color=sma_slow_color)
plot(ema_angle, title="EMA angle", style=plot.style_circles,color=mycolor)
plot(ssl_angle,title='SSL Angle', color=ssl_color, linewidth=0,transp=0, style=plot.style_circles)
// === Candles ===
plotchar(doji and show_c_patterns ? doji: na, title="Doji", text='Doji', color=color.white,transp=80)
plotshape(h and show_c_patterns ? h : na,  title= "Hammer", location=location.belowbar, color=color.white, style=shape.arrowup, text="H",transp=80)
plotshape(ih and show_c_patterns ? ih : na,  title= "Inverted hammer", location=location.belowbar, color=color.white, style=shape.arrowup, text="IH",transp=80)
plotshape(bear_h and show_c_patterns ? bear_h: na,title= "Bearish Harami", color=sell_color, style=shape.arrowdown, text="BH",transp=50)
plotshape(bear_e and show_c_patterns ? bear_e: na,title= "Bearish Engulfing", color=sell_color, style=shape.arrowdown, text="BE",transp=50)
plotshape(bull_h and show_c_patterns ? bull_h : na,  title= "Bullish Harami", location=location.belowbar, color=buy_color, style=shape.arrowup, text="BH",transp=80)
plotshape(bull_e and show_c_patterns ? bull_e : na, title= "Bullish Engulfing", location=location.belowbar, color=buy_color, style=shape.arrowup, text="BE",transp=80)
// === Moving Averages ===
plot(ema_200, title="EMA 200", color=#fff176, linewidth=3)
// EMA 200 bands large
e200_up = plot(ema_200_upper, title="EMA 200 Upper", color=#ffe676, linewidth=1,transp=70)
e200_down = plot(ema_200_lower, title="EMA 200 Lower", color=#ffe676, linewidth=1,transp=70)
//e200_outer_up = plot(ema_200_outer_upper, title="EMA 200 Outer Upper", color=#0045b3, linewidth=1,transp=90)
//e200_outer_down = plot(ema_200_outer_lower, title="EMA 200 Outer Lower", color=#0045b3, linewidth=1,transp=90)

// EMA 200 bands small
//ema_200_dev_sm = 0.06 * stdev(close, 200)
//e200_up_sm = plot(ema_200 + ema_200_dev_sm, title="EMA 200 Upper Small", color=#ffe676, linewidth=1,transp=90)
//e200_down_sm = plot(ema_200 - ema_200_dev_sm, title="EMA 200 Lower Small", color=#ffe676, linewidth=1,transp=90)
//fill(e200_up, e200_down, color=#fff176, transp=90)
//fill(e200_outer_up, e200_outer_down, color=#0045b3, transp=90)
//fill(e200_up_sm, e200_down_sm, color=#fff176, transp=90)
// EMA Fast
plot(ema_fast, title="EMA Fast", color=mycolor, linewidth=3)


// BBR
//plot(bbr_upper, "BBR Upper", color=bb_zones_color)
//plot(bbr_lower, "BBR Lower", color=bb_zones_color)
// SSL
// ssl_up = plot(ssl_upper, title="SSL Up", color=ssl_color, linewidth=1,transp=90)
// ssl_down = plot(ssl_lower, title="SSL Down", color=ssl_color, linewidth=1,transp=90)
// fill(ssl_up, ssl_down, color=ssl_color, transp=80)
// STC
//plot(stc_line,color=stc_color, title="STC middle line",style=plot.style_circles)
// Basis Bands
// bb_basis_upper = plot(bb_basis_up, title="Basis Upper", color=color.red, transp=90, linewidth=1)
// bb_basis_lower = plot(bb_basis_low, title="Basis Lower", color=color.red, transp=90, linewidth=1)
// fill(bb_basis_upper, bb_basis_lower, color=red, transp=50)
// BB Bands
usqzi = plot(show_BB ? bb_upper: na, "BB Upper ", transp=0, linewidth=1,color=bb_zones_color)
ubi = plot(show_BB ? bb_inner_upper: na, title="BB Upper Inner", color=bb_zones_color, transp=10, linewidth=1)
lbi = plot(show_BB ? bb_inner_lower:na, title="BB Lower Inner", color=bb_zones_color, transp=10, linewidth=1)
lsqzi = plot(show_BB ? bb_lower: na, "BB Lower", transp=0, linewidth=1,color=bb_zones_color)
fill_transp = show_BB_faded ? 80 : 35
fill(ubi, usqzi, title="BB Fill", color=bb_zones_color, transp=fill_transp)
fill(lbi, lsqzi, title="BB Fill", color=bb_zones_color, transp=fill_transp)
// inner
bb_sm_upper = bb_inner_upper - (bb_dev2)
bb_sm_lower = bb_inner_lower + (bb_dev2)
ubii = plot(show_BB ? bb_sm_upper : na, title="BB Small Upper", color=white, transp=90, linewidth=1)
lbii = plot(show_BB ? bb_sm_lower :na, title="BB Small Lower", color=white, transp=90, linewidth=1)
fill(ubii, lbii, title="BB Fill", color=white, transp=95)

// ATR
plot(show_atr ? atr_upper : na, "+ATR Upper", color=#ffffff, transp=80)
plot(show_atr ? atr_lower : na, "-ATR Lower", color=#ffffff, transp=80)
plot(bb_basis - ema_200, "Diff Basis and ema", color=#ffffff, transp=80)
entry_signal() =>
	dir = 0
	trading = 0

	// Type of candle - Long / Short
	candle = close > open ? 1 : 0
	Deadzone = Session(dz_time) ? 1 : 0
	special = 0

	// === Uptrend ===
	// ==================================================

	// === Sell ===
	if (candle == 1 and sma_slow > ema_200) //  ema_200 - ema_200_dev_sm
		dir := -1
		trading := 1
		// WAE
		if wae_diff < 0
			dir := na
			trading := 0
		// BBR
		if bbr < 0.99
			dir := na
			trading := 0
		// SSL
		if SSL < ema_fast and not(bb_lower_zone == 5)
			dir := na
			trading := 0

		// === Zone 0 ===
		if bb_sqz_zone == 0
		
			if bb_lower_zone == 1
				if ema_fast < SSL or sma_slow < bb_inner_lower
					dir := na
					trading := 0

				if wt1 < obLevel2
					dir := na
					trading := 0

			if bb_lower_zone == 2 
				dir := na
				trading := 0

			if bb_lower_zone == 4 
				dir := na
				trading := 0

			if bb_lower_zone == 5 
				dir := na
				trading := 0

		// === Zone 1 ===
		if bb_sqz_zone == 1

			if bb_lower_zone == 1
			 and not (sma_slow > bb_sm_lower and stc < stc_line and stc > stc_low and ssl_angle < 2)
			 and not (sma_slow > bb_inner_lower and isup() and SSL > bb_sm_upper and sma_slow < bb_inner_lower)
			 and not (SSL > sma_slow and bb_basis < sma_slow and open < bb_sm_upper and ema_fast < bb_sm_upper)
				dir := na
				trading := 0

			// if bb_lower_zone == 1 and (bb_basis > sma_slow or SSL < bb_basis or stc < stc_low or stc > stc_line)
			//  and not (ema_fast > sma_slow and SSL > bb_basis )
			//  and not (sma_slow < bb_inner_lower)
			// 	dir := na
			// 	trading := 0

			// if bb_lower_zone == 1 and 
			//  (stc < stc_low or stc > stc_line or wt2 < 0 or wt2 > wt_diff) 
			//   and not(isup() and stc > stc_low)
			//   and not(isup() and sma_slow < bb_inner_lower)
			//   and not(stc>stc_high and wt2>obLevel2 and not isup())
			// 	dir := na
			// 	trading := 0

			if bb_lower_zone == 2
				if stc < stc_low
					dir := na
					trading := 0

			if bb_lower_zone == 2 and 
			 (stc < stc_low or stc > stc_line or wt2 < 0 or wt2 > wt_diff) 
			  and not(isup() and wt2 > obLevel1)
			  and not(stc>stc_high and wt2>obLevel2 and not isup())
				dir := na
				trading := 0

			// if bb_lower_zone == 2 and isup() and perc_change > 15
			// 	dir := na
			// 	trading := 0

			if bb_lower_zone == 3 or bb_lower_zone == 7
				dir := na
				trading := 0

			if bb_lower_zone == 4 and 
			 (stc < stc_low or stc > stc_line) 
				dir := na
				trading := 0

			if bb_lower_zone == 5 and 
			 (stc < stc_low or stc > stc_line or wt2 > wt_diff) 
				dir := na
				trading := 0

		// === Zone 2 ===
		if bb_sqz_zone == 2

			if bb_basis < sma_slow and bb_lower_zone < 5
				dir := na
				trading := 0

			if bb_lower_zone == 4
				dir := na
				trading := 0

			if bb_lower_zone == 5 and (sma_slow > bb_basis or ssl_angle > 14)
			 and not(open > SSL and ssl_angle < 14 )
				dir := na
				trading := 0

		// === Zone 3 ===
		if bb_sqz_zone == 3

			if bb_basis < sma_slow
				dir := na
				trading := 0
			if bb_lower_zone == 2
				dir := na
				trading := 0
			if bb_lower_zone == 3
				dir := na
				trading := 0

			if bb_lower_zone == 5
				dir := na
				trading := 0

		// === Zone 4 ===
		if bb_sqz_zone == 4

			// if bb_lower_zone == 1 and ema_fast < bb_sm_upper 
			// 	dir := na
			// 	trading := 0

			if bb_lower_zone == 5 and (SSL < ema_fast or wt2 < obLevel1)
				dir := na
				trading := 0


	// === Counter - Buy ===
	if candle == 0 and close < bb_inner_lower and sma_slow > ema_200 
		dir := 1
		trading := 1

		// BBR
		if bbr > 0
			dir := na
			trading := 0

		// === Zone 0 ===
		if bb_sqz_zone == 0

			if bb_lower_zone == 1 and 
			 (stc < stc_low or sma_slow < bb_inner_lower)
				dir := na
				trading := 0

			if bb_lower_zone < 3 and stc > stc_line and
			 wt2 > 0
				dir := na
				trading := 0

			if bb_lower_zone >= 4 and (stc > stc_line or
			 wt2 > 0 or ema_fast > SSL or bb_basis > sma_slow)
				dir := na
				trading := 0

			if bear_e == 1 and not (stc > stc_high)
				dir := na
				trading := 0

		// === Zone 1 ===
		if bb_sqz_zone == 1

			if bb_lower_zone == 1 and 
			 ( SSL > bb_basis or (ema_fast >= sma_slow or ema_fast >= bb_basis ) ) 
			 and not (sma_slow > high and ema_fast < sma_slow and ema_angle > -15 )
				dir := na
				trading := 0

			if bb_lower_zone == 2 and wt2 > osLevel2
				dir := na
				trading := 0

			if bb_lower_zone == 4
				dir := na
				trading := 0

			if bb_lower_zone == 5 and 
			 (wt2 > osLevel2 or SSL < ema_fast)
				dir := na
				trading := 0

			if bb_lower_zone == 7
				dir := na
				trading := 0

		// === Zone 2 ===
		if bb_sqz_zone == 2

			if (stc > stc_line or wt2 > 0)
				dir := na
				trading := 0
			// WAE
			if wae_diff < 0
				dir := na
				trading := 0
			// SSL
			if ssl_angle < -16
				dir := na
				trading := 0

			if bb_lower_zone == 1 and SSL > sma_slow and not 
			 h == 1
				dir := na
				trading := 0

			if bb_lower_zone == 2
				dir := na
				trading := 0

			if bb_lower_zone == 3 and 
			 (stc > stc_low or wt2 > osLevel2)
				dir := na
				trading := 0

			if bb_lower_zone == 5 and sma_slow > bb_basis
				dir := na
				trading := 0

			if bb_lower_zone == 7 and wt2 > osLevel2
				dir := na
				trading := 0

		// === Zone 3 ===
		if bb_sqz_zone == 3

			if bb_lower_zone >= 1

				if SSL > bb_basis or ssl_angle < -12
					dir := na
					trading := 0

			if bb_lower_zone >= 2 and open > ema_200_lower
				dir := na
				trading := 0

			// if bb_lower_zone >= 2 and bb_lower_zone <= 4 and abs(ssl_angle) > 8
			// 	dir := na
			// 	trading := 0

			if bb_lower_zone == 4
				dir := na
				trading := 0

			if bb_lower_zone == 5 
			
				if open < ema_200 and
				 stc > stc_line and stc_color == buy_color
					dir := na
					trading := 0

				if open < ema_200 and
				 abs(ssl_angle) > 16
					dir := na
					trading := 0

				if close < ema_200 and
				 stc_color == buy_color
					dir := na
					trading := 0

			if bb_lower_zone == 7
				dir := na
				trading := 0

		// === Zone 4 ===
		if bb_sqz_zone == 4
			if bb_lower_zone == 5 and bb_basis > sma_slow
				dir := na
				trading := 0
			if bb_lower_zone == 5 and (stc > stc_low or ssl_angle < -12) or 
			 (perc_change > 30)
				dir := na
				trading := 0
			

		// // SSL
		// if SSL > bb_basis and ssl_angle < -6 and bb_sqz_zone > 1
		// 	dir := na
		// 	trading := 0
		
	// === Uptrend Special Candles ===
	if use_special and sma_slow > ema_200

		// === Zone 0 ===
		if bb_sqz_zone == 0
			
			if bb_lower_zone == 1 
				// Sell - Bullish Engulfing
				if bull_e == 1 and perc_change > 10 and
				 SSL > close and high > bb_inner_upper
					dir := -1
					trading := 1
				// IH - Sell
				if bull_e == 1 and perc_change > 10 and
				 SSL > close and high > bb_inner_upper
					dir := -1
					trading := 1
				// Bearish Harami - Sell
				if bear_h == 1 and perc_change < 2.5 and
				 sma_slow < bb_sm_lower and high > bb_upper and
				 open > bb_inner_upper
					dir := -1
					trading := 1
			// IH - Sell
			if bb_lower_zone == 1 and ih == 1 and perc_change < 2 and
			 open > bb_sm_upper and close < bb_inner_upper and candle == 1
				dir := -1
				trading := 1
			// Hammer Buy
			if h == 1 and low < bb_inner_lower and
			 ema_fast < sma_slow
				dir := 1
				trading := 1
			//  Buy
			if bb_lower_zone == 1 and candle == 0 and low < bb_lower and
			 close < bb_sm_lower and close > bb_inner_lower
				dir := 1
				trading := 1
			//  Buy
			if bb_lower_zone == 2 and candle == 0 and bear_e == 1 and
			 SSL < ema_fast and low < bb_inner_lower
				dir := 1
				trading := 1

		// === Zone 1 ===
		if bb_sqz_zone == 1

			if bb_lower_zone == 1 

				// Bearish Engulfing - Sell
				if bear_e == 1 and
				 SSL > ema_fast and 
				 open > ema_fast and close > ema_fast and
				 stc > stc_low and wt2 > obLevel2
					dir := -1
					trading := 1

				// Hammer - Sell
				if (h == 1) and open > SSL and SSL > ema_fast and 
				 bb_basis > sma_slow
					dir := -1
					trading := 1
				// RSI
				if not isup() and
				 close > bb_upper and low < bb_inner_lower and perc_change > 20
					dir := -1
					trading := 1

				// Bearish Harami - Sell
				if bear_h and 
				 open > bb_sm_upper and close > bb_sm_upper and 
				 SSL > bb_sm_upper
					dir := -1
					trading := 1

				// Inverted Hammer - Sell
				if (ih == 1) and bbr > 1
					dir := -1
					trading := 1

				// Bullish Harami - Buy
				if bull_h == 1 and not (doji == 1) and perc_change < 2 and  
				 high < ema_fast and high < sma_slow and low < bb_inner_lower and
				 SSL < bb_basis
					dir := 1
					trading := 1

				// Bearish Engulfing - Buy
				if bear_e == 1 and
				 (SSL > bb_basis and low < bb_lower and close < bb_sm_lower and open > ema_fast) // or (SSL > bb_basis and low > bb_lower and low < bb_inner_lower and close < bb_sm_lower and open > ema_fast)
					dir := 1
					trading := 1

				// Buy
				if candle == 0 and close > bb_inner_lower and
				 high > bb_sm_lower and low < bb_inner_lower and 
				 open < bb_sm_lower and open > sma_slow and close < sma_slow
					dir := 1
					trading := 1
				// Buy
				if candle == 0 and close > bb_inner_lower and
				 high > bb_sm_lower and low < bb_inner_lower and 
				 open < bb_sm_lower and open > sma_slow and close < sma_slow
					dir := 1
					trading := 1
				// Buy
				if candle == 0 and sma_slow < bb_inner_lower and sma_slow > bb_lower and 
				 low < bb_inner_lower and open < ema_fast and ema_fast < sma_slow and
				 close < bb_sm_lower and close > bb_lower and perc_change < 4.5
					dir := 1
					trading := 1

			if bb_lower_zone == 5 

				// Buy
				if candle == 0 and low < bb_lower and 
				 close > bb_inner_lower and close < bb_sm_lower
					dir := 1
					trading := 1

		// === Zone 2 ===
		if bb_sqz_zone == 2
			// Sell
			if bb_lower_zone == 1 

				if isup() and open > bb_upper and
				 SSL > bb_basis and ssl_angle < 22
					dir := -1
					trading := 1
				// Sell
				// if bull_e == 1 and perc_change > 13  
				// 	dir := -1
				// 	trading := 1
				// Buy
				if bull_h == 1 and bbr < 0 and perc_change < 2.5 
					dir := 1
					trading := 1
				// Buy
				if bull_h == 1 and perc_change < 1.5 and ( 
				 (high < ema_fast and low < sma_slow and stc < stc_low) or
				 (high < ema_fast and low < bb_sm_lower and stc < stc_line) 
				 )
					dir := 1
					trading := 1
				// Buy - Hammer
				if candle == 0 and h == 1 and 
				 perc_change < 1.5 and SSL < bb_basis and close < bb_inner_lower
					dir := 1
					trading := 1
				// Buy 
				if candle == 0 and perc_change < 2.5 and 
				 SSL < bb_sm_lower and open > bb_sm_lower and close < bb_sm_lower
					dir := 1
					trading := 1

			if bb_lower_zone == 2 and (ih == 1 and bbr > 1) 
				dir := -1
				trading := 1
			// Buy - Bullish Harami
			if bb_lower_zone == 5 and (bull_h == 1) and
			 close < bb_inner_lower and open < bb_lower
				dir := 1
				trading := 1

			if bb_lower_zone == 7 and close > ema_fast and
			 open < ema_fast and stc < stc_low and wt2 < osLevel1
				dir := -1
				trading := 1


			// Sell
			if bb_lower_zone == 5  
				// Sell - IH
				if ih == 1 and high > bb_sm_upper and
				 close < bb_sm_upper and open > SSL and 
				 perc_change < 2 and stc < stc_low
					dir := -1
					trading := 1
				// Sell - Bullish Engulfing
				if bull_e == 1 and high > bb_sm_upper and
				 high > sma_slow and sma_slow > bb_basis and sma_slow > SSL and
				 open <= SSL and ema_fast > SSL
					dir := -1
					trading := 1
				// Sell - Bullish Engulfing
				if candle == 1 and high > bb_sm_upper and perc_change < 4.5 and
				 SSL > ema_fast and stc > stc_high and sma_angle < 0
					dir := -1
					trading := 1


			
		// === Zone 3 ===
		if bb_sqz_zone == 3
			//  Sell
			if bb_lower_zone == 1 
				// Sell - Bearish Harami 
				if bear_h == 1 and high > bb_inner_upper and close > bb_sm_upper and perc_change < 3
					dir := -1
					trading := 1
				// Sell - Bearish Harami
				if bear_h == 1 and isup() and close > bb_upper
					dir := -1
					trading := 1
				//  Buy
				if candle == 0 and 
				 SSL > ema_fast and ssl_color == ssl_color_sell and ssl_angle > 0 and
				 open < ema_fast
					dir := 1
					trading := 1
			// Sell - Hammer
			if bb_lower_zone == 3 
				if candle == 1 and h == 1 and 
				 ssl_color == ssl_color_buy and bb_basis < sma_slow and
				 SSL > bb_basis
					dir := -1
					trading := 1
				if candle == 1 and low > bb_sm_upper and low < SSL and 
				 SSL > ema_fast and sma_slow > bb_basis
					dir := -1
					trading := 1
			// Buy
			// if bb_lower_zone == 5 and bull_h == 1 and close < ema_200 and 
			//  and SSL < ema_200 and ema_fast < ema_200
			// 	dir := 1
			// 	trading := 1
			// Buy
			if bb_lower_zone == 5 and bull_e == 1 and
			 close < SSL and close < bb_sm_lower and
			 open < SSL and open < bb_sm_lower
				dir := 1
				trading := 1

		// === Zone 4 ===
		if bb_sqz_zone == 4

			if (bb_lower_zone == 4) and wt2 > obLevel1 and bbr > 0.98
			 and perc_change > 12 and low < SSL and close < bb_inner_upper
				dir := -1
				trading := 1

			if bb_lower_zone == 5 and open < ema_200
			 and bbr < 0.35 and isdown() and perc_change > 12 and perc_change < 20
				dir := 1
				trading := 1

			if bb_lower_zone == 5 and isup() and close < SSL and open > ema_fast and 
			 perc_change < 4.5 and sma_slow < ema_200_upper
				dir := -1
				trading := 1
				

			// if (bb_lower_zone == 5) and wt2 > obLevel1 and wt_diff < 3 and bbr > 0.92
			// 	dir := -1
			// 	trading := 1

		special := dir == 1 ? 1 : 0
	//
	// ==================================================
	// === Downtrend ===
	// ==================================================

	// === Buy ===
	if (candle == 0 and sma_slow < ema_200 and close < bb_inner_lower)
		dir := 1
		trading := 1

		if bb_sqz_zone == 0 
		
			if bb_upper_zone == 1 and sma_slow > bb_upper
				dir := na
				trading := 0

			if bb_upper_zone == 1 
				if sma_slow > bb_sm_upper
					dir := na
					trading := 0

			// if bb_upper_zone == 1 
			// 	if stc > stc_low
			// 		dir := na
			// 		trading := 0
			if bb_upper_zone == 5 
				dir := na
				trading := 0

			if bb_upper_zone == 7 
				dir := na
				trading := 0

		if bb_sqz_zone == 1

			if bb_basis > sma_slow or stc > stc_low 
			 and not(isdown() and open < bb_lower)
			 and not(isdown() and bear_e == 1 and close < bb_lower)
				dir := na
				trading := 0

			if bb_upper_zone >= 5 and ssl_angle < -12
				dir := na
				trading := 0

		if bb_sqz_zone == 2
			if sma_slow > bb_sm_upper
				dir := na
				trading := 0
			if (wae_diff < 0 or SSL > ema_fast) and not(isdown() and ema_fast < SSL ) 
				dir := na
				trading := 0

			if bb_upper_zone == 7 
			 and not(isdown() and open < bb_lower and open < bb_sm_lower)
				dir := na
				trading := 0

		if bb_sqz_zone == 3
			if bb_upper_zone == 2 
				dir := na
				trading := 0
			if bb_upper_zone == 3 
				dir := na
				trading := 0
			if bb_upper_zone == 4
			 and not(SSL < ema_fast and bb_basis < sma_slow)
				dir := na
				trading := 0
			if bb_upper_zone > 4
				dir := na
				trading := 0
				
		if bb_sqz_zone == 4
			if bb_upper_zone == 1
				dir := na
				trading := 0
			if bb_upper_zone == 4
				dir := na
				trading := 0


	// === Counter Sell ===
	if (candle == 1 and sma_slow < ema_200)
		dir := -1
		trading := 1

		// BBR
		if bbr < 0.99
			dir := na
			trading := 0

		if bb_sqz_zone == 0 

			if bb_upper_zone == 1 and SSL < ema_fast and
			 sma_slow < bb_sm_upper
				dir := na
				trading := 0

			if bb_basis > sma_slow or ssl_angle > 7
				dir := na
				trading := 0

		if bb_sqz_zone == 1 

			if bb_upper_zone == 1 and wae_diff < 0
			 and not(sma_slow > ema_fast and high > bb_upper and SSL < bb_basis and doji == 0)
				dir := na
				trading := 0

			if bb_upper_zone == 1 and SSL < ema_fast and
			 sma_slow < bb_sm_upper
				dir := na
				trading := 0

			if bb_upper_zone >= 2 and SSL < sma_slow
				dir := na
				trading := 0
			if bb_upper_zone == 2 and SSL < ema_fast
				dir := na
				trading := 0

			if bb_upper_zone == 3  
				dir := na
				trading := 0

			if bb_upper_zone == 6  
				dir := na
				trading := 0

		if bb_sqz_zone == 2 

			if bb_upper_zone == 3  
				dir := na
				trading := 0

			if bb_upper_zone == 4 
			 and not(bull_e == 1 and SSL > ema_fast and bb_basis >= sma_slow) //and not(open < sma_slow and close > sma_slow) 
				dir := na
				trading := 0

			if bb_upper_zone < 4 and (SSL < ema_fast or bull_e == 1)
			 and not(sma_slow > open and sma_slow < close)
				dir := na
				trading := 0

			if bb_upper_zone >= 6
				dir := na
				trading := 0

			// if bb_upper_zone == 1 and SSL < ema_fast
			// 	dir := na
			// 	trading := 0

		if bb_sqz_zone == 3 
			if bb_basis < sma_slow
				dir := na
				trading := 0
			
			if ssl_angle > 17 
			 and not (bb_upper_zone == 1 and SSL > ema_fast and bull_e == 0)
				dir := na
				trading := 0

		if bb_sqz_zone == 4
			if bb_basis < sma_slow
				dir := na
				trading := 0
			
			if ssl_angle > 15 // 26
				dir := na
				trading := 0

			

	// === Downtrend Special Candles ===
	if use_special and sma_slow < ema_200
		
		if bb_sqz_zone == 0
			// Sell
			if bb_upper_zone == 1 and doji == 1 and high > bb_sm_upper and
			 sma_slow > bb_basis and low > bb_basis
				dir := -1
				trading := 1

			// Sell - Bullish Engulfing
			if bb_upper_zone == 7 
				// Sell - Bullish Engulfing
				if bull_e == 1 and high > bb_inner_upper and
				 sma_slow < bb_lower and SSL > ema_fast
					dir := -1
					trading := 1
				// Sell
				if candle == 1 and high > bb_inner_upper and open > bb_sm_upper and
				 low < bb_sm_upper and perc_change < 1.5 and 
				 sma_slow < bb_lower
					dir := -1
					trading := 1

		if bb_sqz_zone == 1

			if bb_upper_zone == 1
				// Buy
				if low < bb_lower and sma_slow > bb_inner_upper and
				 SSL < ema_fast
					dir := 1
					trading := 1
			if bb_upper_zone == 7
				// Sell
				if high > bb_inner_upper and low > bb_sm_upper
					dir := -1
					trading := 1
				
		if bb_sqz_zone == 2
			if bbr < 0 and high > ema_200 and
		 	 close < sma_slow
				dir := 1
				trading := 1
				
			// Sell - Bearish Harami
			if bb_upper_zone == 1
				// Sell - Bearish Harami
				if bear_h == 1 and sma_slow < bb_sm_upper and
				 SSL < bb_sm_lower and ema_fast < bb_basis and
				 open > bb_basis 
					dir := -1
					trading := 1

			if bb_upper_zone == 7
				// Buy - Doji
				if doji == 1 and open < bb_inner_lower 
					dir := 1
					trading := 1
				// Buy - Bullish Engulfing
				if bull_e == 1 and open < SSL and
				 SSL < ema_fast and perc_change < 3.5
					dir := 1
					trading := 1



			// Sell - Bullish Engulfing
			if bb_upper_zone == 5 
				// Sell - Bullish Engulfing
				if bull_e == 1 and
			 	 high > bb_inner_upper and open > ema_fast
					dir := -1
					trading := 1
				// Sell - Bearish Harami and Doji
				if candle == 0 and bear_h == 1 and doji == 1 and
				 close > SSL
					dir := -1
					trading := 1
				// Buy - Bullish Harami or Bearish Engulfing
				if (bull_h == 1 and close < sma_slow and bb_basis > sma_slow) or
				 (bear_e == 1 and open < sma_slow and bb_basis > sma_slow)
					dir := 1
					trading := 1
			// Sell - Hammer
			if bb_upper_zone == 6 and h == 1 and bear_h == 1 and
			 candle == 0 and open > ema_fast
				dir := -1
				trading := 1
		
		if bb_sqz_zone == 3
			// Buy
			if bb_upper_zone == 7 and bull_h ==1 and isdown() and
			 perc_change < 1.5 and SSL < ema_fast
				dir := 1
				trading := 1
			// Sell
			if bb_upper_zone == 7 and bear_e == 1 and ih == 1 and
			 perc_change < 3.5 and SSL < ema_fast and close > ema_fast
				dir := -1
				trading := 1
		if bb_sqz_zone == 4
			// Sell - Hammer
			if h == 1 and SSL > ema_fast and
			 close > bb_sm_upper and bb_basis > ema_200
				dir := -1
				trading := 1
			// Buy
			if bb_upper_zone == 6 and bull_h == 1 and
			 close > SSL and open > SSL and open < bb_sm_lower
				dir := 1
				trading := 1
			// Sell 
			if bb_upper_zone == 7 and candle == 1 and
			 low > ema_fast and close < SSL and perc_change < 3.5 and
			 ssl_angle < 0
				dir := -1
				trading := 1

		// Sell
		// if bb_sqz_zone == 0 and bb_sqz_zone == 5 and doji == 1 
		// 	dir := -1
		// 	trading := 1
		special := dir == -1 ? 1 : 0 


	


	// Don't trade during high spread periods
	if use_deadzone and Deadzone == 1
		dir := na
		trading := 0

	[dir, trading,special]

[trade_dir, trading,special] = entry_signal() 

plot(special, title="Special", style=plot.style_circles)
// Buy / Sell indicators
trade_color = trade_dir > 0  ? buy_color : sell_color
// trade_color = trade_dir > 0 and special == 1 ? blue : 
//  trade_dir > 0 and special == 0 ? buy_color :
//  trade_dir < 0 and special == 1 ? orange :
//  trade_dir < 0 and special == 0 ? sell_color : na
//text_buy = special > 0 ? "BS" : "B"
bgcolor(use_deadzone and Session(dz_time) ? #b71c1c : na, title="Deadzone",transp=85)
plotshape(trade_dir > 0 and show_entry ? 1 : na, title="Entry Buy",transp=20, color=trade_color, location = location.belowbar, style=shape.labelup, text="B", textcolor=color.white, size=size.small)
plotshape(trade_dir < 0 and show_entry ? -1 : na, title="Entry Sell",transp=20, color=trade_color, location = location.abovebar, style=shape.labeldown, text="S", textcolor=color.white, size=size.small)
plot(trading ,title="Trading",color= trading ? buy_color : color.gray)