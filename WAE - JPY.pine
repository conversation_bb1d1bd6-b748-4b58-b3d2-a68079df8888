//@version=4
// <AUTHOR> 
// List of all my indicators: 
// https://docs.google.com/document/d/15AGCufJZ8CIUvwFJ9W-IKns88gkWOKBCvByMEvm5MLo/edit?usp=sharing
//

// Modified for Crypto Market by ShayanKM

study("WAE - JPY June 1", shorttitle="WAE - JPY")
sensitivity = input(150, title="Sensitivity")
fastLength=input(20, title="FastEMA Length")
slowLength=input(40, title="SlowEMA Length")
channelLength=input(20, title="BB Channel Length")
mult=input(2.0, title="BB Stdev Multiplier")
valid=input(0.026, title="Valid line")
wae_angle_amount =input(3, title="E1 Angle Amount")

lime = #00FF00
green = #008000
orange = #FF7F00
red = #ff0000

angle(_src, len) =>
    rad2degree=180/3.14159265359  //pi 
    ang=rad2degree*atan((_src[0] - _src[1])/atr(len))

multi = 1000
DEAD_ZONE = nz(rma(tr(true),100)) * 3.7

calc_macd(source, fastLength, slowLength) =>
	fastMA = ema(source, fastLength)
	slowMA = ema(source, slowLength)
	fastMA - slowMA

calc_BBUpper(source, length, mult) => 
	basis = sma(source, length)
	dev = mult * stdev(source, length)
	basis + dev

calc_BBLower(source, length, mult) => 
	basis = sma(source, length)
	dev = mult * stdev(source, length)
	basis - dev

t1 = (calc_macd(close, fastLength, slowLength) - calc_macd(close[1], fastLength, slowLength))*sensitivity
t2 = (calc_macd(close[2], fastLength, slowLength) - calc_macd(close[3], fastLength, slowLength))*sensitivity

e1 = (calc_BBUpper(close, channelLength, mult) - calc_BBLower(close, channelLength, mult))

trendUp = (t1 >= 0) ? t1 : 0
trendDown = (t1 < 0) ? (-1*t1) : 0

c_tu_trans = e1>DEAD_ZONE?20:50
//plot(c_tu_trans*0.001,title="Trans")

t_up_color = (trendUp<trendUp[1] and e1>DEAD_ZONE) ? color.new(lime,20) : 
 (trendUp<trendUp[1] and e1<DEAD_ZONE) ? color.new(lime,70) :
 (trendUp>trendUp[1] and e1>DEAD_ZONE) ? color.new(green,20) :
 (trendUp>trendUp[1] and e1<DEAD_ZONE) ? color.new(green,70) : na

t_down_color = (trendDown<trendDown[1] and e1>DEAD_ZONE) ? color.new(orange,20) : 
 (trendDown<trendDown[1] and e1<DEAD_ZONE) ? color.new(orange,70) :
 (trendDown>trendDown[1] and e1>DEAD_ZONE) ? color.new(red,20) :
 (trendDown>trendDown[1] and e1<DEAD_ZONE) ? color.new(red,70) : na

wae_color = #000000
if t1 >= 0
	wae_color := trendUp<trendUp[1] ? lime : green
if t1 < 0
	wae_color := trendDown<trendDown[1] ? orange : red

plot(trendUp, style=plot.style_columns, linewidth=1, color=t_up_color, title="UpTrend")
plot(trendDown, style=plot.style_columns, linewidth=1, color=t_down_color, title="DownTrend")

plot(DEAD_ZONE, color=color.new(#ffffff,50), linewidth=1, style=plot.style_cross, title="DeadZoneLine")
plot(e1, linewidth=2, color=color.new(#ffffff,50), title="ExplosionLine")
hline(valid, color=color.new(#ffffff,50), linewidth=1, title="High Line")
wae_a = angle(e1 / 50,wae_angle_amount)
wae_a_avg = sma(wae_a,10) * 10
plot(wae_a,title="Line Angle",color=#ffeb3b)
plot(wae_a_avg,title="Angle Average",color=color.white,linewidth=2)

wae_s1 = trendUp>e1 and wae_color==green and wae_color[1]==red
plotshape(wae_s1,title="WAE Sell",color=red,style=shape.circle,location=location.top)

wae_s2 = e1<DEAD_ZONE and wae_color==green and wae_a>trendUp
plotshape(wae_s2,title="WAE Sell",color=orange,style=shape.circle,location=location.top)
// wae_s2 = wae_color==green and wae_color[1]==red and wae_a<0
// plotshape(wae_s2,title="WAE Sell",color=orange,style=shape.circle,location=location.top)

wae_b1 = wae_color==red and wae_a>e1
plotshape(wae_b1,title="WAE Buy",color=green,style=shape.circle,location=location.bottom)


