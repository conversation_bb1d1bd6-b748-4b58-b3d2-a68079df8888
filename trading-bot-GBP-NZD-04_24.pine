//@version=4
study(title = "Trading Bot", shorttitle = "Trading Bot", overlay=true, format = format.price, precision = 4)


show_entry = input(title="Show Entry", type=input.bool, defval=true)
show_atr = input(title = "Show Stop Loss / ATR", type = input.bool, defval = false)
show_BB = input(title="Show BB", type=input.bool, defval=true)
show_c_patterns = input(title="Show Candlestick Patterns", type=input.bool, defval=false)
show_BB_faded = input(title="Show Fill Faded", type=input.bool, defval=true)
use_bb_filter = input(title="BB band inside EMA band", type=input.bool, defval=true)
//use_ssl_cross_up = input(title="Uptrend - SSL above Basis ", type=input.bool, defval=true)
use_ssl_cross_counter = input(title="Uptrend Counter - SSL above SMA ", type=input.bool, defval=true)
use_bbr = input(title="BBR lines outside BB bands", type=input.bool, defval=true)
use_special = input(title="Use Special Candles", type=input.bool, defval=true)
use_deadzone = input(title="Deadzone", type=input.bool, defval=false)
dz_time = input(title="DZ Timeframe", type=input.session, defval= "1715-1830")


// === Colors ===
sell_color = #ff0062
buy_color = #00c3ff
red = #e04566 // e04566 //dd1c1c
green = #55aa1a
blue = #2196f3
white = #ffffff
gray  = #707070

// === Functions ===
angle(_src, len) =>
    rad2degree=180/3.14159265359  //pi 
    ang=rad2degree*atan((_src[0] - _src[1])/atr(len))

Session(sess) => na(time(timeframe.period, sess)) == false

// Change
perc_change = abs( (1 - (close[1] / close)) * 10000 )
//plot(perc_change,title="Change",style=plot.style_circles)


// === Fast EMA ===
// ==================================================
fast_ema = ema(close,3)
//plot(fast_ema, title="Fast EMA", color=color.yellow, transp=15)


// ===  EMA 200 ===
// ==================================================
len_200 = input(200, minval=1, title="EMA 200")
ema_200 = ema(close, len_200)
ema_200_dev = 0.27 * stdev(close, len_200)
ema_200_upper = ema_200 + ema_200_dev
ema_200_lower = ema_200 - ema_200_dev


// ===  Moving Average EMA SMA ===
// ==================================================
ema_len = 8 //input(8, minval=1, title="ema Length") // default 8
sma_len = input(45, minval=1, title="SMA Length") // 50
src = close
ema_fast = ema(src, ema_len)
up = ema_fast > ema_fast[1]
down = ema_fast < ema_fast[1]
fast_ema_angle = angle(ema_fast,4)
// Slow SMA
sma_slow = sma(close, sma_len)
up2 = sma_slow > sma_slow[1]
down2 = sma_slow < sma_slow[1]
sma_angle = angle(sma_slow,4)
mycolor = up ?  green : down ? red : #00c3ff
sma_slow_color = up2 ? green : down2 ? red : blue


// === Bollinger Bands %B ===
// ==================================================
bbr_length = input(25, minval=1, title="BBR length") // 15 or 25
bbr_mult = input(2.0, minval=0.001, maxval=4, title="BBR StdDev") // 2.2 or 2.0
bbr_basis = sma(close, bbr_length)
bbr_dev = bbr_mult * stdev(close, bbr_length)
bbr_upper = bbr_basis + bbr_dev
bbr_lower = bbr_basis - bbr_dev
bbr = (close - bbr_lower)/(bbr_upper - bbr_lower)
// band1 = hline(1, "Overbought", color=#606060, linestyle=hline.style_dashed)
// band0 = hline(0, "Oversold", color=#606060, linestyle=hline.style_dashed)
// fill(band1, band0, color=color.teal, title="Background")


// === Schaff Trend Cycle (STC) ===
// ==================================================
stc_signal = 12 // input(12,"Length")
stc_fast = 26 //input(26,"FastLength")
stc_slow = 50 //input(50,"SlowLength")
stc_low = 13
stc_high = 88
stc_line = (stc_high + stc_low) * 0.5
stc_macd(BBB, stc_fast, stc_slow) =>
    stc_fastMA = ema(BBB, stc_fast)
    stc_slowMA = ema(BBB, stc_slow)
    stc_macd = stc_fastMA - stc_slowMA
    stc_macd
    
AAAAA(stc_signal, stc_fast, stc_slow) => 
    AAA=input(0.203,"STC Sensitivity") // 0.25
    var CCCCC = 0.0
    var DDD = 0.0
    var DDDDDD = 0.0
    var EEEEE = 0.0
    tmp_macd = stc_macd(close,stc_fast,stc_slow)     
    lowest = lowest(tmp_macd, stc_signal)
    highest = highest(tmp_macd, stc_signal) - lowest    
    CCCCC := (highest > 0 ? ((tmp_macd - lowest) / highest) * 100 : nz(CCCCC[1])) 
    DDD := (na(DDD[1]) ? CCCCC : DDD[1] + (AAA * (CCCCC - DDD[1]))) 
    DDDD = lowest(DDD, stc_signal) 
    DDDDD = highest(DDD, stc_signal) - DDDD     
    DDDDDD := (DDDDD > 0 ? ((DDD - DDDD) / DDDDD) * 100 : nz(DDDDDD[1])) 
    EEEEE := (na(EEEEE[1]) ? DDDDDD : EEEEE[1] + (AAA * (DDDDDD - EEEEE[1])))
    EEEEE

stc = AAAAA(stc_signal,stc_fast,stc_slow)
stc_color = stc > stc[1] ? buy_color : sell_color


// === SSL ===
// ==================================================
ssl_len = input(55,"SSL Length") //55
SSL = wma(2 * wma(close, ssl_len / 2) - wma(close, ssl_len), round(sqrt(ssl_len)))
ssl_multy = 0.2
ssl_range = tr
rangema = ema(ssl_range, ssl_len)
upperk = SSL + rangema * ssl_multy
lowerk = SSL - rangema * ssl_multy
ssl_angle = angle(SSL,4)
ssl_color_buy = #00c3ff
ssl_color_sell = #ff0062
ssl_color = close > upperk ? ssl_color_buy : close < lowerk ? ssl_color_sell : gray
// SSL bands
ssl_dev = 0.08 * stdev(close, ssl_len)
ssl_upper = SSL + ssl_dev
ssl_lower = SSL - ssl_dev


// === Bollinger Bands ===
// ==================================================
bb_use_ema = input(false, title="Use EMA for Bollinger Band")
bb_length = input(25, minval=1, title="Bollinger Length")
bb_mult = input(2.0, title="Base Multiplier", minval=0.5, maxval=10)
sqz_length = 100
sqz_threshold = input(69, minval=0, title="Squeeze Threshold") // old 65

// Breakout Indicator Inputs
ema_1 = ema(close, bb_length)
sma_1 = sma(close, bb_length)
bb_basis = bb_use_ema ? ema_1 : sma_1
basis_angle = angle(bb_basis,4)
bb_basis_dev = 0.05 * stdev(close, bb_length)
bb_basis_up = bb_basis + bb_basis_dev
bb_basis_low = bb_basis - bb_basis_dev
dev = stdev(close, bb_length)
bb_dev = bb_mult * dev
// Bands
bb_inner_upper = bb_basis + bb_dev
bb_inner_lower = bb_basis - bb_dev
// Calculate BB spread and average spread
bb_spread = bb_inner_upper - bb_inner_lower
avgspread = sma(bb_spread, sqz_length)
// Calculate BB relative %width for Squeeze indication
bb_squeeze = 0.00
bb_squeeze := bb_spread / avgspread * 100
// Calculate Upper and Lower band painting offsets based on 50% of atr.
bb_offset = atr(14) * 0.5
bb_upper = bb_inner_upper + bb_offset
bb_lower = bb_inner_lower - bb_offset

bb_zone = bb_squeeze < sqz_threshold ? 1 : 
 bb_squeeze < 120 ? 2 :
 bb_squeeze < 180 ? 3 :
 bb_squeeze > 181 ? 4 : na
sqz_color = bb_zone == 1 ? sell_color : 
 bb_zone == 2 ?  gray : 
 bb_zone == 3 ?  buy_color : 
 bb_zone == 4 ? white: na


// ===  ATR ===
// ==================================================
atrlen = input(14, "ATR Period")
atr_mult = input(1.25, "ATR Mult", step = 0.1) // 1.15
atr_slen = wma(tr(true), atrlen)
atr_upper = atr_slen * atr_mult + close
atr_lower = close - atr_slen * atr_mult


// === RSI Chart Bars ===
// ==================================================
var rsi_color = #000000
rsi_len = 14 //input(14, minval=1, title="Length")
rsi_up = rma(max(change(close), 0), rsi_len)
rsi_down = rma(-min(change(close), 0), rsi_len)
rsi = rsi_down == 0 ? 100 : rsi_up == 0 ? 0 : 100 - (100 / (1 + rsi_up / rsi_down))
isup() => rsi > 70
isdown() => rsi < 30
// plot(rsi,title="RSI",color=color.blue)
rsi_color := isup() ? buy_color : isdown() ? sell_color : na
barcolor(rsi_color, title="Rsi Candles")



// === WaveTrend with Crosses [LazyBear]  ===
// ==================================================
n1 = input(8, "Channel Length")
n2 = input(28, "Average Length")
obLevel1 = input(60, "Over Bought Level 1")
obLevel2 = input(52, "Over Bought Level 2")
osLevel1 = input(-60, "Over Sold Level 1")
osLevel2 = input(-53, "Over Sold Level 2")
 
ap = hlc3 
esa = ema(ap, n1)
d = ema(abs(ap - esa), n1)
ci = (ap - esa) / (0.015 * d)
tci = wma(ci, n2)
 
wt1 = tci
wt2 = sma(wt1,4)
wt_diff = wt1 - wt2
plot(wt1, title="WT Green", color=green, style=plot.style_circles)
plot(wt2, title="WT Red", color=red, style=plot.style_circles)
plot(wt1-wt2, title="WT Diff", color=white, transp=80, style=plot.style_circles)


// === Candlesticks ===
// ==================================================
DojiSize = input(0.1, minval=0.01, title="Doji size")
c_patterns(_src, type) =>
    if type == 'doji'
		doji=(abs(open - close) <= (high - low) * DojiSize)

// Doji
candle_type = 'na'
candle_transp = 20

// Doji
doji=(abs(open - close) <= (high - low) * DojiSize)
// Hammer
h =(((high - low)>3*(open -close)) and  ((close - low)/(.001 + high - low) > 0.6) and ((open - low)/(.001 + high - low) > 0.6))
// Inverted Hammer
ih =(((high - low)>3*(open -close)) and  ((high - close)/(.001 + high - low) > 0.6) and ((high - open)/(.001 + high - low) > 0.6))
// Bearish Engulfing
bear_e=(close[1] > open[1] and open > close and open >= close[1] and open[1] >= close and open - close > close[1] - open[1] )
// Bearish Harami
bear_h=(close[1] > open[1] and open > close and open <= close[1] and open[1] <= close and open - close < close[1] - open[1] )
// Bullish Harami
bull_h=(open[1] > close[1] and close > open and close <= open[1] and close[1] <= open and close - open < open[1] - close[1] )
// Bullish Engulfing
bull_e=(open[1] > close[1] and close > open and close >= open[1] and close[1] >= open and close - open > open[1] - close[1] )

candle_type := doji == 1 ? 'doji' : 
 h == 1 ? 'h' : 
 ih == 1 ? 'ih' : 
 bear_e == 1 ? 'bear_e' :
 bear_h == 1 ? 'bear_e' :
 bull_e == 1 ? 'bull_e' :
 bull_h == 1 ? 'bull_h' : 
 doji == 1 and h == 1 ? 'd_h' :
 doji == 1 and h == 1 and bear_h == 1 ? 'd_h_bear_h' : 'na'


// === Plot ===
// ==================================================
plot(bbr, "Bollinger Bands %B", color=color.teal)
plot(bb_squeeze, title="BB Squeeze", color=sqz_color, transp=10, linewidth=0,style=plot.style_circles)
plot(stc,color=stc_color, title="STC",linewidth=2,style=plot.style_circles)
plot(SSL, title="SSL", linewidth=2, color=ssl_color)
plot(bb_basis, title="Basis Line", color=color.red, transp=10, linewidth=1)
plot(sma_angle, title="SMA angle", style=plot.style_circles,color=sma_slow_color)
plot(ssl_angle,title='SSL Angle', color=ssl_color, linewidth=0,transp=0, style=plot.style_circles)
// === Candles ===
plotshape(bear_e and show_c_patterns ? bear_e: na,title= "Bearish Engulfing", color=sell_color, style=shape.arrowdown, text="BE",transp=50)
plotchar(doji and show_c_patterns ? doji: na, title="Doji", text='Doji', color=color.white,transp=80)
plotshape(bull_h and show_c_patterns ? bull_h : na,  title= "Bullish Harami", location=location.belowbar, color=buy_color, style=shape.arrowup, text="BH",transp=80)
plotshape(bull_e and show_c_patterns ? bull_e : na, title= "Bullish Engulfing", location=location.belowbar, color=buy_color, style=shape.arrowup, text="BE",transp=80)
// === Moving Averages ===
plot(ema_200, title="EMA 200", color=#fff176, linewidth=3)
// EMA 200 bands large
e200_up = plot(ema_200_upper, title="EMA 200 Upper", color=#ffe676, linewidth=1,transp=90)
e200_down = plot(ema_200_lower, title="EMA 200 Lower", color=#ffe676, linewidth=1,transp=90)
// EMA 200 bands small
ema_200_dev_sm = 0.06 * stdev(close, len_200)
e200_up_sm = plot(ema_200 + ema_200_dev_sm, title="EMA 200 Upper Small", color=#ffe676, linewidth=1,transp=90)
e200_down_sm = plot(ema_200 - ema_200_dev_sm, title="EMA 200 Lower Small", color=#ffe676, linewidth=1,transp=90)
fill(e200_up, e200_down, color=#fff176, transp=90)
fill(e200_up_sm, e200_down_sm, color=#fff176, transp=90)
// EMA Fast
plot(ema_fast, title="EMA", color=mycolor, linewidth=3)
// SMA slow
plot(sma_slow , title="SMA", color=sma_slow_color, linewidth=3, transp=15)

// BBR
plot(bbr_upper, "BBR Upper", color=red)
plot(bbr_lower, "BBR Lower", color=green)
// SSL
ssl_up = plot(ssl_upper, title="SSL Up", color=ssl_color, linewidth=1,transp=90)
ssl_down = plot(ssl_lower, title="SSL Down", color=ssl_color, linewidth=1,transp=90)
fill(ssl_up, ssl_down, color=ssl_color, transp=80)
// STC
plot(stc_line,color=stc_color, title="STC middle line",style=plot.style_circles)
// BB Bands
bb_basis_upper = plot(bb_basis_up, title="Basis Line", color=color.red, transp=90, linewidth=1)
bb_basis_lower = plot(bb_basis_low, title="Basis Line", color=color.red, transp=90, linewidth=1)
fill(bb_basis_upper, bb_basis_lower, color=red, transp=50)
usqzi = plot(show_BB ? bb_upper: na, "BB Upper ", transp=0, linewidth=1,color=color.red)
ubi = plot(show_BB ? bb_inner_upper: na, title="BB Upper Inner", color=color.blue, transp=10, linewidth=1)
lbi = plot(show_BB ? bb_inner_lower:na, title="BB Lower Inner", color=color.blue, transp=10, linewidth=1)
lsqzi = plot(show_BB ? bb_lower: na, "BB Lower", transp=0, linewidth=1,color=color.red)
fill_transp = show_BB_faded ? 80 : 35
fill(ubi, usqzi, title="BB Fill", color=sqz_color, transp=fill_transp)
fill(lbi, lsqzi, title="BB Fill", color=sqz_color, transp=fill_transp)
// ATR
plot(show_atr ? atr_upper : na, "+ATR Upper", color=#ffffff, transp=80)
plot(show_atr ? atr_lower : na, "-ATR Lower", color=#ffffff, transp=80)

entry_signal() =>
	dir = 0
	trading = 0

	// Type of candle - Long / Short
	candle = close > open ? 1 : 0
	bb_inside_ema = 0
	squeeze = bb_squeeze < sqz_threshold ? 1 : 0
	Deadzone = Session(dz_time) ? 1 : 0

	// === Uptrend ===
	// ==================================================

	// === Sell ===
	if (candle == 1 and sma_slow > ema_200 - ema_200_dev_sm ) // ema_200
		dir := -1
		trading := 1

		// BBR
		if bbr < 1 or (bbr > 0.985 and doji == 1 and stc > stc_line)
			dir := na
			trading := 0


		// === SMA ===
		// SMA lower than BB Bands
		if sma_slow < bb_inner_lower
			dir := na
			trading := 0


		// === BB Bands ===
		// Inside ema 200 bands
		if bb_inner_lower < ema_200_upper and bb_inner_lower > ema_200_lower
			bb_inside_ema := 1
			//SSL > bb_basis

			if stc > 85 and bb_lower < ema_200_lower
				dir := na
				trading := 0


		// === SSL ===
		// SSL above BB Basis line
		if ssl_upper < bb_basis and
		  close > bb_inner_lower
			dir := na
			trading := 0

		// if SSL < sma_slow and ssl_angle > 2 and open
		// 	dir := na
		// 	trading := 0


		// === BB Squeeze Areas ===
		if squeeze == 1

			if bbr < 1 or stc < stc_low
				dir := na
				trading := 0

			if wt2 > wt_diff
				dir := na
				trading := 0

			// if sma_angle > 6
			// 	dir := na
			// 	trading := 0

		// if bb_squeeze < sqz_threshold and not (sma_slow < bb_lower) 

		// 	if bbr < 1
		// 		dir := na
		// 		trading := 0

		// Medium squeeze range with STC 
		if bb_squeeze > 69 and bb_squeeze < 115 and
		 stc < 94 // 98.8
			dir := na
			trading := 0

		if bb_squeeze > 150 and stc < 99.5
			dir := na
			trading := 0


	// === Uptrend Counter - Buy ===
	if candle == 0 and sma_slow > ema_200 and close < bb_inner_lower
		dir := 1
		trading := 1

		// Zone 1 - red

		// BBR
		if bbr > 0 or ( bbr < 0 and stc > stc_line)
			dir := na
			trading := 0

		// Fast EMA
		if ema_fast >= sma_slow or ema_fast >= bb_basis  
			dir := na
			trading := 0

		// SMA
		// if sma_slow_color == red or sma_angle > 4

		// 	// SSL below SMA
		// 	if use_ssl_cross_counter and SSL > sma_slow
		// 		dir := na
		// 		trading := 0

		// SSL
		if SSL > bb_basis and ssl_angle < -6 and squeeze == 0
			dir := na
			trading := 0
		
		

	// Uptrend Special Candles
	if use_special 

		// === Sell ===

		// Inverted hammer - Sell
		if (ih == 1 and bbr > 1) and (
		 (bb_squeeze > 80 and stc > stc_line) or 
		 (sma_slow < ema_200_upper and sma_slow > ema_200 and SSL < ema_fast and bb_squeeze < 80) )
			dir := -1
			trading := 1

		// Bullish Engulfing - Sell
		if bull_e == 1 and isup() and sma_slow > ema_200 
		 and ema_fast < ssl_upper and ema_fast > ssl_lower
		 and bb_lower < ema_200_lower
			dir := -1
			trading := 1

		// BB bands inside ema 200 bands - Sell
		if use_bb_filter and 
		 (bb_inner_lower < ema_200_upper and bb_inner_lower > ema_200 and bb_lower < ema_200 and bb_lower > ema_200_lower and SSL > bb_basis) and 
		 (bbr > 0.95 and candle == 1 and low > ema_fast and high < bb_inner_upper)
			dir := -1
			trading := 1

		// The Crunch (ssl_color == ssl_color or ssl_color == ssl_color_sell) and
		if candle == 1 and SSL > ema_fast and ssl_angle > 0 and ssl_angle < 7 and 
		 open > ema_fast and high > bb_inner_upper and
		 ( close < bb_inner_upper and open < bb_inner_upper ) and
		 sma_slow > bb_inner_lower and squeeze == 0
		 and doji == 0
			dir := -1
			trading := 1

		// RSI + Doji followed by Bullish Engulfing and outside BB bands
		if isup() and bull_e == 1 and candle_type[1] == 'doji'
		 and close < bb_inner_upper and open > SSL and 
		 perc_change < 4
			dir := -1
			trading := 1



		// === Buy ===

		// Support level - Basis line - buy
		if sma_slow < ema_200_upper and sma_slow > ema_200 and
		 low < bb_basis and close > bb_basis and ema_fast < SSL and candle == 0
			dir := 1
			trading := 1

		// Hammer and NOT a Doji - Buy
		if (h == 1 or ih == 1) and doji == 0 and perc_change <= 2 and candle == 0 and 
		 SSL > bb_basis and ema_fast < sma_slow and stc < stc_low
			dir := 1
			trading := 1

		// Doji - buy
		if doji == 1 and bbr < 0
			dir := 1
			trading := 1

		// Bearish Engulfing - buy
		if bear_e == 1 and open > ema_fast and close < sma_slow and
		 low <= bb_lower and ssl_angle > 0
			dir := 1
			trading := 1

		// Bearish Engulfing - buy
		if open < bb_basis and close < bb_basis and 
		 high > bb_basis and high < ema_fast and 
		 (low - close) <= 2 and
		 SSL > ema_fast and ssl_angle > 4
			dir := 1
			trading := 1

		if sma_angle > 2 and sma_slow > ema_200 and 
		 open > sma_slow and open < ema_fast and
		 close < sma_slow and close > bb_inner_lower and
		 low < bb_inner_lower 
			dir := 1
			trading := 1

		// Short red candle between fast ema and SMA touching BB lower
		if candle == 0 and perc_change <= 6 and 
		 open < ema_fast and open > sma_slow and close < sma_slow and close > bb_inner_lower and low < bb_inner_lower 
			dir := 1
			trading := 1
		 





	// === Downtrend ===
	// ==================================================

	// Buy signal
	if (candle == 0 and sma_slow < ema_200 and close < bb_inner_lower)
		dir := 1
		trading := 1

		// BB band greater than ema band
		if bb_inner_upper > ema_200_lower and bb_squeeze > 68 and bb_squeeze < 85
			dir := na
			trading := 0

		// BB Squeeze areas
		if (bb_squeeze < sqz_threshold)
			
			// SMA is above bb bands
			if sma_slow > bb_inner_upper and SSL > bb_inner_lower
				dir := na
				trading := 0

			if SSL > ema_200_lower
				dir := na
				trading := 0

			// if SSL < ema_fast and close < bb_inner_lower
			// 	dir := na
			// 	trading := 0

			// // RSI Candles
			// if fast_ema < SSL and isdown()
			// 	dir := na
			// 	trading := 0

	// Special Bearish Engulfing - Bottom of Strong downtrend 
	if (use_special and bear_e == 1 and sma_slow < ema_200 and open < ema_fast
	 and SSL < ema_fast and ssl_color == ssl_color_buy and ssl_angle < 0 )
		dir := 1
		trading := 1


	// Don't trade during high spread periods
	if use_deadzone and Deadzone == 1
		dir := na
		trading := 0

	[dir, trading]

[trade_dir, trading] = entry_signal() 


// Buy / Sell indicators
bgcolor(Session(dz_time) ? #b71c1c : na, title="Deadzone",transp=85)
plotshape(trade_dir > 0 and show_entry ? 1 : na, title="Entry Buy",transp=20, color=trade_dir > 0 ? buy_color : buy_color, location = location.belowbar, style=shape.labelup, text="B", textcolor=color.white, size=size.small)
plotshape(trade_dir < 0 and show_entry ? -1 : na, title="Entry Sell",transp=20, color= trade_dir < 0 ? sell_color : sell_color, location = location.abovebar, style=shape.labeldown, text="S", textcolor=color.white, size=size.small)
plot(trading ,title="Trading",color= trading ? buy_color : color.gray)