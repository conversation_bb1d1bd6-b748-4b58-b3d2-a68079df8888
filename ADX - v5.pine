//@version=5
indicator('ADX - v5')
// Colors

adx_len = input(9, title='Length')  // 14
adx_line = input(20, title='threshold')  // 20
adx_avg = input(8, title='SMA')  // 10
adx_top = input(50, title='High')  // 41
adx_high = input(39.5, title='High')  // 41
adx_mid = input(title='Mid', defval=33)
adx_center = input(title='Center', defval=20)
adx_low = input(title='Low', defval=12)
use_cond1 = true  // input(title="Show Condition 1", type=input.bool, defval=true)
use_cond2 = true  // input(title="Show Condition 2", type=input.bool, defval=true)
use_cond3 = true  // input(title="Show Condition 3", type=input.bool, defval=true)
use_cond4 = false  // input(title="Show Condition 4", type=input.bool, defval=false)
use_cond5 = false  // input(title="Show Condition 5", type=input.bool, defval=false)
// Show adx
show_di_plus = input(title='Di Plus', defval=true)
show_di_minus = input(title='Di Minus', defval=true)
show_adx = input(title='ADX', defval=true)
show_adx_sma = input(title='ADX SMA', defval=true)

g_adx_time = ''
inl_adx = '1'
use_adx_curr = input.bool(title='Show Current', defval=true, inline=inl_adx, group=g_adx_time)
use_adx_multi = input.bool(title='Show Multi', defval=true, inline=inl_adx, group=g_adx_time)
adx_time = input.timeframe(title='Timeframe', defval='15', group=g_adx_time)

close_m = close
high_m = high
low_m = low

f_adx() =>
    smooth_tr = 0.0
    smooth_di_plus = 0.0
    smooth_di_minus = 0.0
    TrueRange = math.max(math.max(high_m - low_m, math.abs(high_m - nz(close_m[1]))), math.abs(low_m - nz(close_m[1])))
    DI_plus = high_m - nz(high_m[1]) > nz(low_m[1]) - low_m ? math.max(high_m - nz(high_m[1]), 0) : 0
    DI_minus = nz(low_m[1]) - low_m > high_m - nz(high_m[1]) ? math.max(nz(low_m[1]) - low_m, 0) : 0
    smooth_tr := nz(smooth_tr[1]) - nz(smooth_tr[1]) / adx_len + TrueRange
    smooth_di_plus := nz(smooth_di_plus[1]) - nz(smooth_di_plus[1]) / adx_len + DI_plus
    smooth_di_minus := nz(smooth_di_minus[1]) - nz(smooth_di_minus[1]) / adx_len + DI_minus

    di_plus = smooth_di_plus / smooth_tr * 100
    di_minus = smooth_di_minus / smooth_tr * 100
    DX = math.abs(di_plus - di_minus) / (di_plus + di_minus) * 100
    adx = ta.sma(DX, adx_len)
    adx_sma = ta.sma(adx, adx_avg)

    [adx, adx_sma, di_plus, di_minus]

[adx, adx_sma, di_plus, di_minus] = f_adx()

adx_m = request.security(syminfo.tickerid, adx_time, adx)
adx_sma_m = request.security(syminfo.tickerid, adx_time, adx_sma)
di_plus_m = request.security(syminfo.tickerid, adx_time, di_plus)
di_minus_m = request.security(syminfo.tickerid, adx_time, di_minus)

textcolor = color.new(color.white, 50)
p_di_plus = plot(use_adx_curr and di_plus_m and show_di_plus == true ? di_plus_m : na, color=color.new(#ff0000, 50), title='DI+', style=plot.style_areabr)
p_di_minus = plot(use_adx_curr and di_minus_m and show_di_minus == true ? di_minus_m : na, color=color.new(#3b8c38, 50), title='DI-', style=plot.style_areabr)
p_adx = plot(use_adx_curr and adx_m and show_adx == true ? adx_m : na, color=color.new(#ffeb3b, 50), title='ADX')
p_adx_sma = plot(use_adx_curr and adx_sma_m and show_adx_sma == true ? adx_sma_m : na, color=color.new(#ffffff, 50), title='SMA')
//plot(DX, color=color.new(#00bcd4, 50), title="DX")

i_use_bars = input.bool(true, title='Show Bars')
cond_cross_dn = di_plus_m < di_minus_m and adx_sma_m>adx_high and not(di_plus_m<adx_low) ? true : false
cond_cross_up = di_plus_m > di_minus_m and adx_sma_m>adx_high and not(di_minus_m<adx_low) ? true : false
barcolor(i_use_bars and cond_cross_up ? color.blue :  
 i_use_bars and cond_cross_dn ? color.aqua : 
 i_use_bars and di_plus_m > di_minus_m ? #ff0000 : 
 i_use_bars and di_plus_m < di_minus_m ? #00ff00 
 : na)

// Fill DI + and DI -
fill(p_di_plus, p_di_minus, title='DI Fill', color=di_plus_m > di_minus_m ? color.new(#ff0000, 80) : color.new(#3b8c38, 80), transp=90)
// Buy
b_cond01 = di_minus_m > adx_high and di_minus_m > di_minus_m[1] and adx_m > di_minus_m and adx_sma_m > adx_high and adx_m > adx_m[1] ? 1 : na
b_cond02 = di_minus_m > adx_high and di_plus_m < adx_low and adx_sma_m > adx_high and adx_sma_m > adx_m and di_minus_m > di_minus_m[1] ? 1 : na
b_cond03 = di_minus_m > adx_high and di_minus_m > di_minus_m[1] and di_plus_m < adx_center and adx_m < adx_mid and adx_m > adx_sma_m ? 1 : na
b_cond04 = di_plus_m > adx_sma_m and adx_m < adx_sma_m and adx_m > di_minus_m ? 1 : na
//b_cond04 = adx_m>adx_high and adx_sma_m>adx_mid and di_plus_m<adx_low ? 1 : na
plotshape(use_cond1 and b_cond01 ? 1 : na, style=shape.circle, location=location.bottom, color=#008800, text='1', textcolor=textcolor)
plotshape(use_cond2 and b_cond02 ? 1 : na, style=shape.circle, location=location.bottom, color=#008800, text='2', textcolor=textcolor)
plotshape(use_cond3 and b_cond03 ? 1 : na, style=shape.circle, location=location.bottom, color=#008800, text='3', textcolor=textcolor)
//plotshape(b_cond04?1:na, style=shape.circle,location=location.bottom,color=#008800,text="4",textcolor=textcolor)

// Sell
s_cond01 = di_plus_m > adx_high and di_plus_m > di_plus_m[1] and adx_m > di_plus_m and adx_sma_m > adx_high and adx_m > adx_m[1] ? 1 : na
s_cond02 = di_plus_m > adx_high and di_minus_m < adx_low and adx_sma_m > adx_high and adx_sma_m > adx_m and di_plus_m > di_plus_m[1] ? 1 : na
//s_cond01 = adx_m>adx_sma_m and adx_sma_m>adx_high and adx_sma_m>di_plus_m and di_minus_m<adx_low and di_plus_m>di_plus_m[1] ? 1 : na
//s_cond01 = di_plus_m>adx_mid and (adx_sma_m>adx_high or adx_sma_m<adx_low) ? 1 : na
//s_cond02 = adx_sma_m>adx_m and di_plus_m>adx_m and di_plus_m>adx_mid and di_minus_m<adx_low ? 1 : na
//s_cond02 = adx_sma_m>adx_high and di_minus_m<adx_low ? 1 : na
s_cond03 = di_plus_m > adx_high and di_plus_m > di_plus_m[1] and di_minus_m < adx_center and adx_m < adx_mid and adx_m > adx_sma_m ? 1 : na
//s_cond03 = di_plus_m>adx_mid and di_minus_m<adx_low ? 1 : na
s_cond04 = di_minus_m > adx_sma_m and adx_m < adx_sma_m and adx_m > di_plus_m ? 1 : na
plotshape(use_cond1 and s_cond01 ? 1 : na, style=shape.circle, location=location.top, color=#ff0000, text='1', textcolor=textcolor)
plotshape(use_cond2 and s_cond02 ? 1 : na, style=shape.circle, location=location.top, color=#ff0000, text='2', textcolor=textcolor)
plotshape(use_cond3 and s_cond03 ? 1 : na, style=shape.circle, location=location.top, color=#ff0000, text='3', textcolor=textcolor)
//plotshape(s_cond04?1:na, style=shape.circle,location=location.top,color=#ff0000,text="4",textcolor=textcolor)

hline(adx_top, title='ADX Top', color=color.new(color.white, 70))
hline(adx_high, title='ADX High', color=color.new(color.yellow, 50), linestyle=hline.style_solid)
hline(adx_mid, title='ADX Mid', color=color.new(#ff0000, 50), linestyle=hline.style_solid)
hline(adx_mid, title='ADX Mid', color=color.new(#ff0000, 50), linestyle=hline.style_solid)
hline(adx_line, color=color.new(#ffffff, 50), linestyle=hline.style_dashed)
hline(adx_low, color=color.new(color.yellow, 50), linestyle=hline.style_solid)
//plotshape(DIMinus>34? 1 : na, title="Crossup", color=green, location = location.abovebar, style=shape.diamond, textcolor=green, size=size.small)



