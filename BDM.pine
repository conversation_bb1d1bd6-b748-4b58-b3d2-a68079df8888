// © Dreadblitz
//@version=4
//
study("Bollinger on Macd", shorttitle = "BSM Copy", overlay=false)

angle(_src) =>
    rad2degree=180/3.14159265359  //pi 
    ang=rad2degree*atan((_src[0] - _src[1])/atr(2))

SDev = 0.0
upper_band = 0.0
lower_band = 0.0
fast = input(8, "Fast Average") // 8
slow = input(26, "Slow Average") // 26
stdv = input(0.8, "Stdv") // 0.8
m_fast = ema(close,fast)
m_slow = ema(close,slow)
BBMacd = m_fast - m_slow
Avg = ema(BBMacd,9)
SDev := stdev(BBMacd,9)
upper_band := Avg + stdv * SDev
lower_band := Avg - stdv * SDev
pcol = BBMacd < lower_band ? #FF0000 : BBMacd > upper_band ? #008000 : color.blue
pcol1 = BBMacd < lower_band ? #FF0000 :  na
pcol2 = BBMacd > upper_band ? #008000  :  na
c=plot(BBMacd, title='Line Macd BB', color=pcol, linewidth=2, style=plot.style_line, transp=0)
macd_angle = angle(BBMacd)
macd_change = change(BBMacd)
plot(macd_angle, color=pcol, title="MACD angle",style=plot.style_circles, transp=100)
plot(macd_change, color=pcol, title="MACD change",style=plot.style_circles, transp=100)
a=plot(upper_band, title='Upper Band', color=#00BFFF, linewidth=1, style=plot.style_line, transp=30)
b=plot(lower_band, title='Lower Band', color=#00BFFF, linewidth=1, style=plot.style_line, transp=30)
fill(a, b, color=#00BFFF)

fill(c, b, color=pcol1, transp=50)
fill(c, a, color=pcol2, transp=50)



up = crossover(BBMacd, upper_band)
down = crossunder(BBMacd, lower_band)
// plot(cross(wt1, wt2) ? wt2 : na, color = (wt2 - wt1 > 0 ? color.red : color.lime) , style = plot._style_circles, linewidth = 2)
// barcolor(cross(wt1, wt2) ? (wt2 - wt1 > 0 ? color.aqua : color.yellow) : na)
