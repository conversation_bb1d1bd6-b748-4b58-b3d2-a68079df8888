//@version=4
study(title="MACD SSLxBB", shorttitle="MACD SSLxBB", resolution="")

//SSL1 VALUES
len = 60
emaHigh = wma(2 * wma(high, len / 2) - wma(high, len), round(sqrt(len)))
emaLow = wma(2 * wma(low, len / 2) - wma(low, len), round(sqrt(len)))
Hlv = int(na)
Hlv := close > emaHigh ? 1 : close < emaLow ? -1 : Hlv[1]
sslDown = Hlv < 0 ? emaHigh : emaLow


// BB vars
ema_1 = ema(close, 20)
sma_1 = sma(close, 20)
bb_use_ema = false
fast_ma_len = 3
bb_basis = bb_use_ema ? ema_1 : sma_1

fast_length = bb_basis //12
slow_length = sslDown //26
src = close
signal_length = 9
sma_source = 0
sma_signal = 0
// Plot colors
col_grow_above = #26A69A
col_grow_below = #FFCDD2
col_fall_above = #B2DFDB
col_fall_below = #EF5350
col_macd = #0094ff
col_signal = #ff6a00
// Calculating
fast_ma = bb_basis
slow_ma = sslDown
macd = fast_ma - slow_ma
signal = sma_signal ? sma(macd, signal_length) : ema(macd, signal_length)
hist = macd - signal
plot(hist, title="Histogram", style=plot.style_columns, color=(hist>=0 ? (hist[1] < hist ? col_grow_above : col_fall_above) : (hist[1] < hist ? col_grow_below : col_fall_below) ), transp=0 )
plot(macd, title="MACD", color=col_macd, transp=0)
plot(signal, title="Signal", color=col_signal, transp=0)