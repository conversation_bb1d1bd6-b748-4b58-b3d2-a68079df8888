//@version=4
study(title="TB Multi - GBP-JPY - 3M", shorttitle="TB Multi - GBP-JPY - 3M",overlay=true,max_labels_count=500)

g_main_inputs = "Global Settings -------------------------------------------------------------"
inl_global = "inl_global"
use_logic = input(title="Use Logic", type=input.bool, defval=false,group=g_main_inputs,inline=inl_global)
show_days = input(title="Show Days", type=input.bool, defval=false,group=g_main_inputs,inline=inl_global)
show_trades = input(title="Show Trades", type=input.bool, defval=true,group=g_main_inputs,inline=inl_global)
show_trend = input(title="Show Trend", type=input.bool, defval=false,group=g_main_inputs,inline=inl_global)

g_filters = "Filters -------------------------------------------------------------"
inl_ftr1 = "inl_ftr1"
inl_ftr2 = "inl_ftr2"
filter_perc_input = input(title="Perc",type=input.bool,defval=true,group=g_filters,inline=inl_ftr1) 
filter_adx_input = input(title="ADX",type=input.bool,defval=true,group=g_filters,inline=inl_ftr1) 
filter_s5_input = input(title="S5",type=input.bool,defval=true,group=g_filters,inline=inl_ftr1)
filter_ema_input = input(title="EMA",type=input.bool,defval=true,group=g_filters,inline=inl_ftr1)
filter_s4_input = input(title="S4",type=input.bool,defval=true,group=g_filters,inline=inl_ftr2)
filter_aroon_input = input(title="Aroon",type=input.bool,defval=true,group=g_filters,inline=inl_ftr2)
use_ny_filter = input(title="NY close",type=input.bool,defval=true,group=g_filters,inline=inl_ftr2)
//show_sessions = input(title="Sessions",type=input.bool,defval=true)

// Colors
red = #ff0062
aqua = #00c3ff
yellow = #FFFF00
orange = #ff9800
green = #4caf50
lime = #50ff00
white = #ffffff
blue = #42a5f5
d_blue = #0053ff
violet = #814dff
magenta = #ff0062
purple = #0045b3
gray = #707070
black = #000000
sell_color = color.new(#ff0062,20)
buy_color = color.new(#00c3ff,20)
c_hide = color.new(#ffffff,100)


// Global Functions
angle(_src, len) =>
    rad2degree=180/3.14159265359  //pi 
    ang=rad2degree*atan((_src[0] - _src[1])/atr(len))

// angles(len1,type) =>
//     float v = na
//     if type=="SMA"
//         v := sma(close, len1)
//     if type=="EMA"
//         v := ema(close, len1)
//     if type=="WMA"
//         v := wma(2*wma(close, len1/2)-wma(close, len1), round(sqrt(len1)))

//     obj_angle = sma(angle(v,input_angle), sma_input) 
//     [obj_angle]

// Change
perc_change(obj) =>
    perc = abs( (1 - (obj[1] / obj)) * 10000 )


g_days = "Days -------------------------------------------------------------"
inl_days = "inl_dats"
// Europe/London - Pacific/Auckland - Asia/Tokyo
timeinrange(res, sess, loc) => not na(time(res, sess, loc)) ? 1 : 0
ny_open = timeinrange("2", "0800-0900", "America/New_York")
ny_close = timeinrange("2", "1045-1230", "Pacific/Honolulu")
friday = color.new(sell_color,92)
// bgcolor(show_days and ny_close == 1 and use_ny_filter ? color.new(gray,92) : na)
// bgcolor(show_days and ny_open == 1 and use_ny_filter ? color.new(aqua,92) : na)
// bgcolor(dayofweek == 1 and show_days ? friday : na)



g_cb = "SSL -------------------------------------------------------------"
inl_cb = "cb"
show_ssl = input(title="Show SSL", type=input.bool, defval=true,group=g_cb)
show_s1 = input(title="S1", type=input.bool, defval=false,group=g_cb,inline=inl_cb)
show_s2 = input(title="s2", type=input.bool, defval=false,group=g_cb,inline=inl_cb)
show_s3 = input(title="s3", type=input.bool, defval=false,group=g_cb,inline=inl_cb)
show_s4 = input(title="S4", type=input.bool, defval=false,group=g_cb,inline=inl_cb)
show_s5 = input(title="S5", type=input.bool, defval=false,group=g_cb,inline=inl_cb)
show_s6 = input(title="S6", type=input.bool, defval=false,group=g_cb,inline=inl_cb)
show_s7 = input(title="S7", type=input.bool, defval=true,group=g_cb,inline=inl_cb)
show_s8 = input(title="S8", type=input.bool, defval=true,group=g_cb,inline=inl_cb)

inl_len = "len1"
s_len1 = input(8,title="S1",inline=inl_len) // 8
s_len2 = input(20,title="S2",inline=inl_len) // 20
s_len3 = input(50,title="S3",inline=inl_len) // 50
s_len4 = input(75,title="S4",inline=inl_len) // 75 
s_len5 = input(100,title="S5",inline=inl_len) // 100
s_len6 = input(200,title="S6",inline=inl_len) // 200
s_len7 = input(300,title="S7",inline=inl_len) // 300
s_len8 = input(500,title="S8",inline=inl_len) // 500

g_fill = ""
inl_fill = "fill"
inl_conv = "conv"
show_fill = input(title="Show Fill", type=input.bool, defval=true,inline=inl_fill,group=g_fill)
show_conv = input(title="Show Conv", type=input.bool, defval=true,inline=inl_fill,group=g_fill)
conv_amount = input(defval=14, title="Conv Amount", type=input.float, step=1,inline=inl_conv,group=g_fill )
c_type = input(title="Type", defval="JPY", options=["NZD", "JPY" ],inline=inl_conv,group=g_fill)
line_input = 1 //input(1, title="Line width", type=input.integer,inline=inl_fill )

g_multi = ""
inl_multi = "multi"
ssl_curr = input(title="Show Current", type=input.bool, defval=true,inline=inl_multi,group=g_multi)
use_ssl_multi = input(title="Show Multi", type=input.bool, defval=true,inline=inl_multi,group=g_multi)
ssl_res = input(title="Timeframe", type=input.resolution, defval="15",group=g_multi)
//useCurrentRes = input(true, title="Chart Resolution?",inline=inl_multi,group=g_multi)
angle_input = 14 // input(title="Angle Amount",type=input.integer, defval=14)

s_lines(len) =>
    s = wma(2*wma(close, len/2)-wma(close, len), round(sqrt(len)))
    sa = angle(s,14)
    [s,sa]

[s1,s1_a] = s_lines(s_len1)
[s2,s2_a] = s_lines(s_len2)
[s3,s3_a] = s_lines(s_len3)
[s4,s4_a] = s_lines(s_len4)
[s5,s5_a] = s_lines(s_len5)
[s6,s6_a] = s_lines(s_len6)
[s7,s7_a] = s_lines(s_len7)
[s8,s8_a] = s_lines(s_len8)

// Diff and Convergence
s_conv(t1, t2) =>
    boost = c_type=="JPY" ? 100 : 1000
    diff = (t1 - t2) * boost
    conv = show_conv and diff<conv_amount and diff>(conv_amount * -1) ? true : false
    [diff,conv]

[s5_s6_diff,s5_s6_conv] = s_conv(s5,s6)
[s7_s8_diff,s7_s8_conv] = s_conv(s7,s8)
ssl_conv    = s5_s6_diff<conv_amount and s5_s6_diff>(conv_amount * -1) ? 1 : 0
ssl_conv2   = s7_s8_diff<conv_amount and s7_s8_diff>(conv_amount * -1) ? 1 : 0

// Plot
s1_f = plot(show_ssl and ssl_curr and show_s1?s1:na, color=aqua , title="S1", linewidth=line_input)
s2_f = plot(show_ssl and ssl_curr and show_s2?s2:na, color=white , title="S2", linewidth=2)
s3_f = plot(show_ssl and ssl_curr and show_s3?s3:na, color=aqua , title="S3")
s4_f = plot(show_ssl and ssl_curr and show_s4?s4:na, color=s4_a>0?yellow:orange , title="S4",linewidth=line_input)
s5_f = plot(show_ssl and ssl_curr and show_s5?s5:na, color=s5_a>0?orange : s5_a<0 and s5>s6 ? red : green, title="S5",linewidth=line_input)
s6_f = plot(show_ssl and ssl_curr and show_s6?s6:na, color=s6_a>0?red:lime , title="S6",linewidth=line_input)
s7_f = plot(show_ssl and ssl_curr and show_s7?s7:na, color=s7_a>0?orange : s7_a<0 and s7>s8 ? red : green , title="S7",linewidth=line_input)
s8_f = plot(show_ssl and ssl_curr and show_s8?s8:na, color=s8_a>0?red:lime, title="S8",linewidth=line_input)

// Fills
fill(s5_f,s6_f,title="S5/S6 Fill", color=show_fill and s5_s6_diff<1?color.new(green,90): show_fill ? color.new(red,90):na)
fill(s7_f,s8_f,title="S7/S8 Fill", color=show_fill and s7_s8_diff<1?color.new(green,90): show_fill ? color.new(red,90):na)
// Conv
fill(s5_f,s6_f,title="S5/S6 Conv", color=s5_s6_conv and s5>s6?color.new(red,70): s5_s6_conv and s5<s6?color.new(green,70) : na)
fill(s7_f,s8_f,title="S7/S8 Conv", color=s7_s8_conv and s7>s8?color.new(red,70): s7_s8_conv and s7<s8?color.new(green,70) : na)
  

// === MULTI  ===
// ==============
s1_m = security(syminfo.tickerid, ssl_res, s1)
s1_a_m = security(syminfo.tickerid, ssl_res, s1_a)
s2_m = security(syminfo.tickerid, ssl_res, s2)
s2_a_m = security(syminfo.tickerid, ssl_res, s2_a)
s3_m = security(syminfo.tickerid, ssl_res, s3)
s3_a_m = security(syminfo.tickerid, ssl_res, s3_a)
s4_m = security(syminfo.tickerid, ssl_res, s4)
s4_a_m = security(syminfo.tickerid, ssl_res, s4_a)
s5_m = security(syminfo.tickerid, ssl_res, s5)
s5_a_m = security(syminfo.tickerid, ssl_res, s5_a)
s6_m = security(syminfo.tickerid, ssl_res, s6)
s6_a_m = security(syminfo.tickerid, ssl_res, s6_a)
s7_m = security(syminfo.tickerid, ssl_res, s7)
s7_a_m = security(syminfo.tickerid, ssl_res, s7_a)
s8_m = security(syminfo.tickerid, ssl_res, s8)
s8_a_m = security(syminfo.tickerid, ssl_res, s8_a)

// Diff
s5_s6_diff_m = security(syminfo.tickerid, ssl_res, s5_s6_diff)
s5_s6_conv_m = security(syminfo.tickerid, ssl_res, s5_s6_conv)
s7_s8_diff_m = security(syminfo.tickerid, ssl_res, s7_s8_diff)
s7_s8_conv_m = security(syminfo.tickerid, ssl_res, s7_s8_conv)

// Plot
s1_f_m =plot(show_ssl and use_ssl_multi and show_s1?s1_m:na, title="S1 Multi",linewidth=line_input, color=aqua)
s2_f_m =plot(show_ssl and use_ssl_multi and show_s2?s2_m:na, title="S2 Multi",linewidth=line_input, color=white)
s3_f_m =plot(show_ssl and use_ssl_multi and show_s3?s3_m:na, title="S3 Multi",linewidth=line_input, color=blue)
s4_f_m =plot(show_ssl and use_ssl_multi and show_s4?s4_m:na, title="S4 Multi",linewidth=line_input, color=s4_a_m>0?yellow:orange)
s5_f_m =plot(show_ssl and use_ssl_multi and show_s5?s5_m:na, title="S5 Multi",linewidth=line_input, color=s5_a_m>0 ? orange : s5_a_m<0 and s5_m>s6_m ? red : green)
s6_f_m =plot(show_ssl and use_ssl_multi and show_s6?s6_m:na, title="S6 Multi",linewidth=line_input, color=s6_a_m>0 ? red : lime)
s7_f_m =plot(show_ssl and use_ssl_multi and show_s7?s7_m:na, title="S7 Multi",linewidth=line_input, color=s7_a_m>0 ? orange : s7_a_m<0 and s7_m>s8_m ? red : green)
s8_f_m =plot(show_ssl and use_ssl_multi and show_s8?s8_m:na, title="S8 Multi",linewidth=line_input, color=s8_a_m>0 ? red : lime)

// Fills
fill(s5_f_m,s6_f_m,title="S5/S6 Fill Multi", color=show_fill and s5_s6_diff_m<1?color.new(green,90): show_fill ? color.new(red,90):na)
fill(s7_f_m,s8_f_m,title="S7/S8 Fill Multi", color=show_fill and s7_s8_diff_m<1?color.new(green,90): show_fill ? color.new(red,90):na)
// Conv
fill(s5_f_m,s6_f_m,title="S5/S6 Conv Multi", color=s5_s6_conv_m and s5_m>s6_m?color.new(red,70): s5_s6_conv_m and s5_m<s6_m?color.new(green,70) : na)
fill(s7_f_m,s8_f_m,title="S7/S8 Conv Multi", color=s7_s8_conv_m and s7_m>s8_m?color.new(red,70): s7_s8_conv_m and s7_m<s8_m?color.new(green,70) : na)






g_ema= "EMA's -------------------------------------------------------------"
inl_ema= "inl_ema"
inl_ema1 = "inl_ema1"
show_ema = input(title="Show EMA 200", type=input.bool, defval=true,group=g_ema)
show_zones = input(title="Show Zones", type=input.bool, defval=true,group=g_ema)
ea_input = input(title="E Bias", type=input.float, defval=1,step=0.01,group=g_ema,inline=inl_ema) 
e200_input = input(title="EMA 200",type=input.integer, defval=230,group=g_ema,inline=inl_ema) // 200
ez_candles = input(title="EZ Candles", type=input.bool, defval=true,inline=inl_ema,group=g_ema)
g_ema_time = ""
inl_ema2 = "inl_ema2"
use_ema_curr = input(title="Show Current", type=input.bool, defval=false,inline=inl_ema2,group=g_ema_time)
use_ema_multi = input(title="Show Multi", type=input.bool, defval=true,inline=inl_ema2,group=g_ema_time)
ema_time = input(title="Timeframe", type=input.resolution, defval="15", group=g_ema_time)

e200 = ema(close, e200_input)
//ea_sc = e200_a<5 ? color.new(orange,50): e200_a>5 ? color.new(#ff0000,50) : na
//ea_bc = e200_a>-5 ? color.new(#0000ff,50): e200_a<-5 ? color.new(#00ff00,50) : na

// e_low
e_low = ema(close, 8)
e_low_a = angle(e_low,3)
e_low_m  = security(syminfo.tickerid, ema_time, e_low)
// e20
e20 = ema(close, 20)
e20_a = angle(e20,3)
e20_m  = security(syminfo.tickerid, ema_time, e20)
// e50
e50 = ema(close, 50)
e50_a = angle(e50,3)
e50_m  = security(syminfo.tickerid, ema_time, e50)
// e200
e200_a = angle(e200,3)
ea_zone = abs(e200_a)
// E Dist
//e_dist = (e50 - e20) * 10000
// plot(e_dist,title="E Dist",color=e50>e20?color.new(red,100):color.new(green,100),style=plot.style_circles)

e_zones = ea_zone < ea_input ? 0 : 
 ea_zone < 2 ? 1 : 
 ea_zone < 3 ? 2 :
 ea_zone < 4 ? 3 :
 ea_zone < 5 ? 4 :
 ea_zone < 6 ? 5 :
 ea_zone > 6 ? 6 : na
e_color = e_zones == 0 ? purple :
 e_zones == 1 ? magenta : 
 e_zones == 2 ?  gray : 
 e_zones == 3 ?  #00c3ff : 
 e_zones == 4 ? white:
 e_zones == 5 ? yellow:
 e_zones == 6 ? purple: na


// Multi
e200_m      = security(syminfo.tickerid, ema_time, e200)
e200_a_m    = security(syminfo.tickerid, ema_time, e200_a)
//ea_zone_m   = security(syminfo.tickerid, ema_time, ea_zone)
ea_zone_m = abs(e200_a_m)
e_zones_m = ea_zone_m < ea_input ? 0 : 
 ea_zone_m < 2 ? 1 : 
 ea_zone_m < 3 ? 2 :
 ea_zone_m < 4 ? 3 :
 ea_zone_m < 5 ? 4 :
 ea_zone_m < 6 ? 5 :
 ea_zone_m > 6 ? 6 : na
e_color_m = e_zones_m == 0 ? purple :
 e_zones_m == 1 ? magenta : 
 e_zones_m == 2 ?  gray : 
 e_zones_m == 3 ?  #00c3ff : 
 e_zones_m == 4 ? white:
 e_zones_m == 5 ? yellow:
 e_zones_m == 6 ? purple: na


barcolor(show_ema and ez_candles ? e_color:na, title="E zone color")
// Plot
plot(show_ema and use_ema_curr?e200:na, title="EMA 200", color=show_zones ? e_color : s8_a_m>0 ? red : lime,linewidth=2)
plot(show_ema and use_ema_multi?e200_m:na, title="EMA 200 Multi", color=show_zones ? e_color_m : s8_a_m>0 ? red : lime, linewidth=2 )
plot(show_ema and e200_a?e200_a:na, color=c_hide, title="EMA Angle",style=plot.style_circles)


g_atr = "ATR -------------------------------------------------------------"
show_atr = false //input(title="Show ATR", type=input.bool, defval=false)
atrlen = 14  // input(14, "ATR Period")
atr_mult = 1 // input(1, "ATR Mult", step = 0.1) // 1.35 1.15
atr_stop = 0.0003 //input(0.0003, "ATR Stop", step = 0.0001) // 0.001
atr_slen = wma(tr(true), atrlen)
atr_upper = atr_slen * atr_mult + close
atr_lower = close - atr_slen * atr_mult
sl_upper = atr_slen * 2 + close
sl_lower = close - atr_slen * 2

// atr_new_len = 14
// atr_smoothing = input(title="Smoothing", defval="RMA", options=["RMA", "SMA", "EMA", "WMA"])
// atr_new = ta.rma(ta.tr(true), atr_new_len)
//plot(atr(14) * 10000, title = "ATR", color=color.new(#B71C1C, 100))

// ATR
// plot(show_atr ? atr_upper : na, "+ATR Upper", color=show_atr?color.new(#ffffff,70):color.new(#ffffff,100))
// plot(show_atr ? atr_lower : na, "-ATR Lower", color=show_atr?color.new(#ffffff,70):color.new(#ffffff,100))
// atr_pips = close>open? abs(close - atr_upper) : abs(close - atr_lower)
// plot(atr_pips, title="ATR", color=color.new(blue,100))






g_rsi = "RSI Wicks -------------------------------------------------------------"
inl_rsi1    = "1"
inl_rsi2    = "2"
show_rsi    = input(title="Show RSI", type=input.bool, defval=false, inline=inl_rsi1, group=g_rsi)
rsi_strong  = input(title="Strongest", type=input.bool, defval=false, inline=inl_rsi1, group=g_rsi)
rsi_pos     = input(title="Position", defval="tb", options=["tb", "t", "b"],inline=inl_rsi2,group=g_rsi)

g_rsi_len   = ""
inl_rsi3    = "inl_rsi3"
inl_rsi4    = "inl_rsi4"
rsi_len     = input(10, title="RSI", inline=inl_rsi3,group=g_rsi_len)
rsi_c_len   = input(10, title="RSI Counter", inline=inl_rsi3,group=g_rsi_len)
rsi_len_m   = input(10, title="RSI Multi", inline=inl_rsi4,group=g_rsi_len)
rsi_c_len_m = input(10, title="RSI Counter Multi", inline=inl_rsi4,group=g_rsi_len)
// rsi_b_len   = input(10, title="RSI Buy", inline=inl_rsi_l,group=g_rsi_len)
// rsi_bc_len  = input(10, title="RSI Buy Counter", inline=inl_rsi_l,group=g_rsi_len)

rsiw_len1    = 19 // input(19, title="RSI 1", inline=inl_rsi_l,group=g_rsi_len) // 20
rsiw_len2    = 14 // input(14, title="RSI 2", inline=inl_rsi_l,group=g_rsi_len) // 15 10
rsiw_len3    = 10 // input(10, title="RSI 3", inline=inl_rsi_l,group=g_rsi_len) // 9 
rsiw_len4    = 7 // input(7, title="RSI 4", inline=inl_rsi_l,group=g_rsi_len) // 6
rsiw_len5    = 3 // input(3, title="RSI 5", inline=inl_rsi_l,group=g_rsi_len) // 3

g_rsi_time = ""
inl_rsi5 = "3"
use_rsi_curr = input(title="Show Current", type=input.bool, defval=true,inline=inl_rsi5,group=g_rsi_time)
use_rsi_multi = input(title="Show Multi", type=input.bool, defval=true,inline=inl_rsi5,group=g_rsi_time)
rsi_time = input(title="Timeframe", type=input.resolution, defval="15", group=g_rsi_time)

rsi1_show   = false //input(title="Show RSI1", type=input.bool, defval=false)
rsi2_show   = true //input(title="Show RSI2", type=input.bool, defval=true)
rsi3_show   = false //input(title="Show RSI3", type=input.bool, defval=false)
rsi4_show   = false //input(title="Show RSI4", type=input.bool, defval=false)
rsi5_show   = false //input(title="Show RSI5", type=input.bool, defval=false)
wicks       = true  //input(true,  title="Wicks based on stand-alone RSI")
target_up   = 70 // input(70, minval=1, title="RSI Up")
target_down = 30 // input(30, minval=1, title="RSI Down")
src_close   = close
src_open    = open
src_high    = high
src_low     = low 


rsi_wicks(rsi_len) =>
    norm_close  = avg(src_close,src_close[1])
    gain_loss_close   = change(src_close)/norm_close
    RSI_close         = 50+50*rma(gain_loss_close, rsi_len)/rma(abs(gain_loss_close), rsi_len)

    norm_open = if wicks==true 
        avg(src_open,src_open[1])
    else 
        avg(src_close,src_close[1])
    gain_loss_open   = change(src_open)/norm_open
    RSI_open         = 50+50*rma(gain_loss_open, rsi_len)/rma(abs(gain_loss_open), rsi_len)
            
    norm_high = if wicks==true 
        avg(src_high,src_high[1])
    else 
        avg(src_close,src_close[1])
    gain_loss_high   = change(src_high)/norm_high
    RSI_high         = 50+50*rma(gain_loss_high, rsi_len)/rma(abs(gain_loss_high), rsi_len)
            
    norm_low  = if wicks==true
        avg(src_low,src_low[1])
    else 
        avg(src_close,src_close[1])
    gain_loss_low   = change(src_low)/norm_low
    RSI_low         = 50+50*rma(gain_loss_low, rsi_len)/rma(abs(gain_loss_low), rsi_len)

    // Sell
    rws_weak = RSI_close<70 and RSI_high>70 and rsi_strong==false? 1 : 0
    rws_mid = RSI_close>70 and RSI_open<70 and rsi_strong==false? 1 : 0 
    rws_strong = RSI_open>70 ? 1 : 0

    // Buy
    rwb_weak = RSI_close>30 and RSI_low<30 and rsi_strong==false? 1 : 0
    rwb_mid = RSI_close<30 and RSI_open>30 and rsi_strong==false? 1 : 0
    rwb_strong = RSI_open<30 ? 1 : 0

    [rws_weak,rws_mid,rws_strong,rwb_weak,rwb_mid,rwb_strong,RSI_close,RSI_high,RSI_low]

[rws_w1,rws_m1,rws_s1,rwb_w1,rwb_m1,rwb_s1,rsi_close1,rsi_high1,rsi_low1] = rsi_wicks(rsiw_len1)
[rws_w2,rws_m2,rws_s2,rwb_w2,rwb_m2,rwb_s2,rsi_close2,rsi_high2,rsi_low2] = rsi_wicks(rsiw_len2)
[rws_w3,rws_m3,rws_s3,rwb_w3,rwb_m3,rwb_s3,rsi_close3,rsi_high3,rsi_low3] = rsi_wicks(rsiw_len3)
[rws_w4,rws_m4,rws_s4,rwb_w4,rwb_m4,rwb_s4,rsi_close4,rsi_high4,rsi_low4] = rsi_wicks(rsiw_len4)
[rws_w5,rws_m5,rws_s5,rwb_w5,rwb_m5,rwb_s5,rsi_close5,rsi_high5,rsi_low5] = rsi_wicks(rsiw_len5)

[rs_w,rs_m,rs_s,rb_w,rb_m,rb_s,rs_close,rs_high,rs_low] = rsi_wicks(rsi_len)
[rsc_w,rsc_m,rsc_s,rbc_w,rbc_m,rbc_s,rsc_close,rsc_high,rsc_low] = rsi_wicks(rsi_c_len)

// SELL
trend_up    = s8_a_m>0 //s8_a>0
trend_down  = s8_a_m<0 //s8_a<0
sell_cond = show_rsi and use_rsi_curr and close>open and (rsi_pos=='tb' or rsi_pos=='t')
rsi_sell_c = rs_w and rs_m == false ? yellow : rs_m and rs_s == false ? orange : red
//plotshape(sell_cond and (rs_w or rs_m or rs_s) ? 1 : na ,title="Sell",color=rsi_sell_c,style=shape.circle,location=location.top)
// Sell Counter
sell_cond_counter = show_rsi and use_rsi_curr and open>close and trend_up and (rsi_pos=='tb' or rsi_pos=='t')
rsi_sell_color = rbc_w and rbc_m == false ? violet : rbc_m and rbc_s == false ? d_blue : lime
//plotshape(sell_cond_counter and (rbc_w or rbc_m or rbc_s)?1:na ,title="Sell Counter",color=rsi_sell_color,style=shape.circle,location=location.bottom)

// Buy
down_cond = show_rsi and use_rsi_curr and close<open and (rsi_pos=='tb' or rsi_pos=='b')
rsi_buy_c = rb_w and rb_m == false ? violet : rb_m and rb_s == false ? d_blue : lime
//plotshape(down_cond and (rb_w or rb_m or rb_s) ? 1 : na ,title="Buy",color=rsi_buy_c,style=shape.circle,location=location.bottom)
// Buy Counter
buy_cond_counter = show_rsi and use_rsi_curr and close>open and trend_down and (rsi_pos=='tb' or rsi_pos=='b')
rsi_buy_color = rsc_w and rsc_m == false ? yellow : rsc_m and rsc_s == false ? orange : red
//plotshape(buy_cond_counter and (rsc_w or rsc_m or rsc_s)?1:na ,title="Buy Counter",color=rsi_buy_color,style=shape.circle,location=location.top)


// Multi
[rs_w_t,rs_m_t,rs_s_t,rb_w_t,rb_m_t,rb_s_t,rs_close_t,rs_high_t,rs_low_t] = rsi_wicks(rsi_len_m)
[rsc_w_t,rsc_m_t,rsc_s_t,rbc_w_t,rbc_m_t,rbc_s_t,rsc_close_t,rsc_high_t,rsc_low_t] = rsi_wicks(rsi_c_len_m)
rsi_open = security(syminfo.tickerid, rsi_time, open)
rsi_close = security(syminfo.tickerid, rsi_time, close)
// RSI
rs_w_m = security(syminfo.tickerid, rsi_time, rs_w_t)
rs_m_m = security(syminfo.tickerid, rsi_time, rs_m_t)
rs_s_m = security(syminfo.tickerid, rsi_time, rs_s_t)
rb_w_m = security(syminfo.tickerid, rsi_time, rb_w_t)
rb_m_m = security(syminfo.tickerid, rsi_time, rb_m_t)
rb_s_m = security(syminfo.tickerid, rsi_time, rb_s_t)
rs_close_m = security(syminfo.tickerid, rsi_time, rs_close_t)
rs_high_m = security(syminfo.tickerid, rsi_time, rs_high_t)
rs_low_m = security(syminfo.tickerid, rsi_time, rs_low_t)
// Counter
rsc_w_m = security(syminfo.tickerid, rsi_time, rsc_w_t)
rsc_m_m = security(syminfo.tickerid, rsi_time, rsc_m_t)
rsc_s_m = security(syminfo.tickerid, rsi_time, rsc_s_t)
rbc_w_m = security(syminfo.tickerid, rsi_time, rbc_w_t)
rbc_m_m = security(syminfo.tickerid, rsi_time, rbc_m_t)
rbc_s_m = security(syminfo.tickerid, rsi_time, rbc_s_t)
rsc_close_m = security(syminfo.tickerid, rsi_time, rsc_close_t)
rsc_high_m = security(syminfo.tickerid, rsi_time, rsc_high_t)
rsc_low_m = security(syminfo.tickerid, rsi_time, rsc_low_t)

sell_cond_m = show_rsi and use_rsi_multi and rsi_close>rsi_open and (rsi_pos=='tb' or rsi_pos=='t')
rsi_sell_c_m = rs_w_m and rs_m_m == false ? yellow : rs_m_m and rs_s_m == false ? orange : red
// plotshape(sell_cond_m and (rs_w_m or rs_m_m or rs_s_m) ? 1 : na ,title="Sell",color=rsi_sell_c_m,style=shape.circle,location=location.top)
// Counter
sell_cond_counter_m = show_rsi and use_rsi_multi and rsi_open>rsi_close and trend_up and (rsi_pos=='tb' or rsi_pos=='t')
rsi_sell_color_m = rbc_w_m and rbc_m_m == false ? violet : rbc_m_m and rbc_s_m == false ? d_blue : lime
//plotshape(sell_cond_counter_m and (rbc_w_m or rbc_m_m or rbc_s_m)?1:na ,title="Sell Counter",color=rsi_sell_color_m,style=shape.circle,location=location.bottom)



g_ch = "SSL Channels -------------------------------------------------------------"
inl_ch = "inl_ch"
show_ch = input(title="SSL Channels",type=input.bool,defval=false,inline=inl_ch,group=g_ch)
ch_len=input(title="Period", defval=100,inline=inl_ch,group=g_ch) // 100, 60, 25
f_trans = 85
channel_line_width = 1
ch_fill = true
ch1_angles = false // input(title="Color based on angle",type=input.bool,defval=false,inline=inl_ch,group=g_ch)

g_ch_time = ""
use_ch_curr = input(title="Show Current", type=input.bool, defval=true,inline=inl_ch,group=g_ch_time)
use_ch_multi = input(title="Show Multi", type=input.bool, defval=true,inline=inl_ch,group=g_ch_time)
ch_time = input(title="Timeframe", type=input.resolution, defval="15", group=g_ch_time)

// ch2_input = 40 
// ch3_input = 13 

c1_a = lime 
c1_b = red
c2_a = lime
c2_b = red

ssl_ch1() => 
    smaHigh=sma(high, ch_len)
    smaLow=sma(low, ch_len)
    ch_Hlv = close>smaHigh ? 1 : -1
    //ch_Hlv := close>smaHigh ? 1 : close<smaLow ? -1 : ch_Hlv[1]
    ch1_d = ch_Hlv < 0 ? smaHigh: smaLow
    ch1_u   = ch_Hlv < 0 ? smaLow : smaHigh
    ch1_mid = (ch1_d + ch1_u) * 0.5
    ch1_a = angle(ch1_mid,3)
    [ch1_d,ch1_u,ch1_mid,ch1_a]

[ch1_d,ch1_u,ch1_mid,ch1_a] = ssl_ch1()

// Channel Conditions
ch1_color = ch1_d>ch1_u ? color.new(#33ae42,50) : color.new(#ff350e,50)

// Color
color1 = ch_len<25?c2_a : c1_a
color2 = ch_len<25?c2_b : c1_b
f_c1 = ch1_d>ch1_u and ch_fill?color.new(color1,f_trans):ch1_u>ch1_d and ch_fill?color.new(color2,f_trans):na

// Plot
ch_sdplot= plot(show_ch and use_ch_curr ? ch1_d:na, title="SSL Down ",linewidth=channel_line_width, color=color.new(color1,50))
ch_suplot=plot(show_ch and use_ch_curr ? ch1_u:na, title="SSL Up",linewidth=channel_line_width, color=color.new(color2,50))
plot(show_ch and use_ch_curr ? ch1_mid:na, title="Mid Point",color=color.new(color.white,80))
plot(show_ch and use_ch_curr ? ch1_a : na, title="Ch Angle", color=color.new(color.white,100))
fill(ch_sdplot,ch_suplot, color= f_c1)

// Multi
ch1_d_m = security(syminfo.tickerid, ch_time, ch1_d)
ch1_u_m = security(syminfo.tickerid, ch_time, ch1_u)
ch1_mid_m = security(syminfo.tickerid, ch_time, ch1_mid)
ch1_a_m = security(syminfo.tickerid, ch_time, ch1_a)

ch_sdplot_m = plot(show_ch and use_ch_multi ? ch1_d_m : na, title="SSL Down Multi", linewidth=channel_line_width, color=color.new(color1,50))
ch_suplot_m = plot(show_ch and use_ch_multi ? ch1_u_m : na, title="SSL Up Multi", linewidth=channel_line_width, color=color.new(color2,50))
plot(show_ch and use_ch_multi ? ch1_mid_m : na, title="Mid Point Multi",color=color.new(color.white,80))
plot(show_ch and use_ch_multi ? ch1_a_m : na, title="Angle Multi", color=color.new(color.white,100))

f_c1_m = ch1_d_m>ch1_u_m and ch_fill?color.new(color1,f_trans):ch1_u_m>ch1_d_m and ch_fill?color.new(color2,f_trans):na

f_c2 = 
 ch1_a>0 and ch1_a<1 ? color.new(gray,f_trans) 
 : ch1_a>1 and ch1_a<2 ? color.new(yellow,f_trans) 
 : ch1_a>2 and ch1_a<4 ? color.new(orange,f_trans) 
 : ch1_a>4 and ch1_a<20 ? color.new(red,f_trans) : na

f_c3 = 
 ch1_a<0 and ch1_a>-1 ? color.new(gray,f_trans) 
 : ch1_a<-1 and ch1_a>-2 ? color.new(aqua,f_trans) 
 : ch1_a<-2 and ch1_a>-4 ? color.new(green,f_trans) 
 : ch1_a<-4 and ch1_a<20 ? color.new(lime,f_trans) : na

ch_color_m = ch1_angles==false ? f_c1_m : ch1_a>0 ? f_c2 : f_c3
fill(ch_sdplot_m,ch_suplot_m, color= ch_color_m)



g_macd = "MACD - Chris Moody -----------------------------------------------------"
inl_macd1 = "inl_macd1"
show_macd = input(title="Show MACD", type=input.bool, defval=false,inline=inl_macd1,group=g_macd)

g_macd_time = ""
inl_macd2 = "inl_macd1"
use_macd_curr = input(title="Show Current", type=input.bool, defval=false,inline=inl_macd2,group=g_macd_time)
use_macd_multi = input(title="Show Multi", type=input.bool, defval=true,inline=inl_macd2,group=g_macd_time)
macd_time = input(title="Timeframe", type=input.resolution, defval="15", group=g_macd_time)

cm_fast_len = 12 //input(12, minval=1), 
cm_slow_len = 26 //input(26,minval=1)
ch_sig_len  = 9 //input(9,minval=1)

cm_fastMA   = ema(close, cm_fast_len)
cm_slowMA   = ema(close, cm_slow_len)
cm_macd     = cm_fastMA - cm_slowMA
cm_signal   = sma(cm_macd, ch_sig_len)
cm_hist     = cm_macd - cm_signal

outMacD = security(syminfo.tickerid, macd_time, cm_macd)
outSignal = security(syminfo.tickerid, macd_time, cm_signal)
outHist = security(syminfo.tickerid, macd_time, cm_hist)

// p_color =  cm_hist>0 and open<close ? red : cm_hist<0 and close<open ? lime : gray
// barcolor(show_macd and use_macd_curr ? p_color : na)

p_color_m = outHist>0 and open<close ? red : outHist<0 and close<open ? lime : gray
barcolor(show_macd and use_macd_multi ? p_color_m : na)



g_bb = "Bollinger Bands -------------------------------------------------------------"
inl_bb1 = "inl_bb1"
inl_bb2 = "inl_bb2"
inl_bb3 = "inl_bb3"
show_bb = input(title="Show Bands", type=input.bool, defval=false,group=g_bb)
BB_length = input(24, title="Leng 1",group=g_bb, inline=inl_bb1) //   15  75  20 12 8 20
BB_length2 = input(150, title="Leng 2",group=g_bb, inline=inl_bb1) // 150 300 100 20 40
bb_smooth_type = input(title="Smooth Type", defval="SMA", options=["SMA", "EMA", "WMA" ])
bb_candles1 = input(title="Show Candles", type=input.bool, defval=false,inline=inl_bb2,group=g_bb) 
bb_candles2 = input(title="Show Candles2", type=input.bool, defval=false,inline=inl_bb2,group=g_bb)
bb_bias = input(title="Bias 1", type=input.float, defval=89.65,step=0.1,group=g_bb, inline=inl_bb3) 
bb_bias2 = input(title="Bias 2", type=input.float, defval=89.65,step=0.1,group=g_bb, inline=inl_bb3) // 89.70 // 88.85

bb_smoothing(len1,type) =>
    float v = na
    if type=="SMA"
        v := sma(close, len1)
    if type=="EMA"
        v := ema(close, len1)
    if type=="WMA"
        v := wma(2*wma(close, len1/2)-wma(close, len1), round(sqrt(len1)))
    [v]

// Timeframe
g_bb_time = ""
inl_bb4 = "inl_bb4"
use_bb_curr = input(title="Show Current", type=input.bool, defval=true,inline=inl_bb4,group=g_bb_time)
use_bb_multi = input(title="Show Multi", type=input.bool, defval=false,inline=inl_bb4,group=g_bb_time)
bb_time = input(title="Timeframe", type=input.resolution, defval="15", group=g_bb_time)
sqz_length = 100 //input(100, title="Sqz Len",group=g_bb, inline=inl_bb1) // 100
BB_stdDev = 2 //input(2, minval=2.0, maxval=3)
bb_smooth_input = 1 // input(title="Smoothing",defval=1,type=input.integer,group=g_bb, inline=inl_bb1) 

// === BB 1 ===
[basis] = bb_smoothing(BB_length,bb_smooth_type)
dev = BB_stdDev * stdev(close, BB_length)
bb_upper = basis + dev
bb_lower = basis - dev
bb_spread = bb_upper - bb_lower
avgspread = sma(bb_spread, sqz_length)

bb_squeeze = 0.00
bb_squeeze := bb_spread / avgspread * 100
bb_zone = bb_squeeze < 53 ? 0 : 
 bb_squeeze < sqz_length ? 1 : 
 bb_squeeze < 120 ? 2 :
 bb_squeeze < 160 ? 3 :
 bb_squeeze > 160 ? 4 :
 bb_squeeze > 200 ? 5 : na
sqz_color = bb_zone == 0 ? #0045b3 :
 bb_zone == 1 ? #ff0062 : 
 bb_zone == 2 ?  gray : 
 bb_zone == 3 ?  #00c3ff : 
 bb_zone == 4 ? white:
 bb_zone == 5 ? yellow: na

bb_zones_color =  sqz_color

// === BB 2 ===
[basis2] = bb_smoothing(BB_length2,bb_smooth_type)
dev2 = BB_stdDev * stdev(close, BB_length2)
bb_upper2 = basis2 + dev2
bb_lower2 = basis2 - dev2
bb_spread2 = bb_upper2 - bb_lower2
avgspread2 = sma(bb_spread2, sqz_length)
bb_squeeze2 = 0.00
bb_squeeze2 := bb_spread2 / avgspread2 * 100
bb_smooth2 = sma((basis2 + dev2), bb_smooth_input)

bb_zone2 = bb_squeeze2 < 53 ? 0 : 
 bb_squeeze2 < sqz_length ? 1 : 
 bb_squeeze2 < 120 ? 2 :
 bb_squeeze2 < 160 ? 3 :
 bb_squeeze2 > 160 ? 4 : na
sqz_color2 = bb_zone2 == 0 ? #0045b3 :
 bb_zone2 == 1 ? #ff0062 : 
 bb_zone2 == 2 ?  gray : 
 bb_zone2 == 3 ?  #00c3ff : 
 bb_zone2 == 4 ? white: na


bb_zones_color2 =  sqz_color2

basis_angle = angle(basis,3)
basis_angle2 = angle(basis2,3)
// BB Diff Angles
bb_diff = (bb_upper - bb_lower) * 1000
bb_diff2 = (bb_upper2 - bb_lower2) * 1000
bb_diff_a = angle(bb_diff,3)
bb_diff_a2 = angle(bb_diff2,3)


// === BB Multi ===
bb_basis_m  = security(syminfo.tickerid, bb_time, sma(close, BB_length) )
bb_upper_m  = security(syminfo.tickerid, bb_time, bb_upper)
bb_lower_m  = security(syminfo.tickerid, bb_time, bb_lower)

bb_spread_m = security(syminfo.tickerid, bb_time, (bb_upper - bb_lower) ) 
avgspread_m = security(syminfo.tickerid, bb_time, sma(bb_spread, sqz_length) )  
bb_sqz_m = security(syminfo.tickerid, bb_time, ( bb_spread / avgspread * 100) ) 
bb_diff_m = bb_spread_m * 1000
bb_diff_a_m = angle(bb_diff_m,3)

bb_zone_m = bb_sqz_m < 53 ? 0 : 
 bb_sqz_m < sqz_length ? 1 : 
 bb_sqz_m < 120 ? 2 :
 bb_sqz_m < 160 ? 3 :
 bb_sqz_m < 200 ? 4 :
 bb_sqz_m < 250 ? 5 :
 bb_sqz_m > 250 ? 6 : na
sqz_color_m = bb_zone_m == 0 ? #0045b3 :
 bb_zone_m == 1 ? #ff0062 : 
 bb_zone_m == 2 ?  gray : 
 bb_zone_m == 3 ?  #00c3ff : 
 bb_zone_m == 4 ? white:
 bb_zone_m == 5 ? yellow:
 bb_zone_m == 6 ? white: na

bb_zones_color_m =  sqz_color_m

// Special s4 distance to bb
s4_bb_up = (bb_upper2 - s4_m) * 100
s4_bb_down = (s4_m - bb_lower2) * 100

//plot(s4_bb_up, "S4 BB Dist up", color=color.new(blue,100))
//plot(s4_bb_down, "S4 BB Dist Down", color=color.new(blue,100))

// plot(show_bb and use_bb_multi?bb_basis_m:na, "BB Basis Multi ",color=bb_zones_color_m)
// plot(show_bb and use_bb_multi?bb_upper_m:na, "BB Upper Multi ",color=bb_zones_color_m)
// plot(show_bb and use_bb_multi?bb_lower_m:na, "BB Lower Multi ",color=bb_zones_color_m)



// Bar Color
// barcolor(show_bb and bb_candles1 and bb_diff_a>bb_bias ? aqua:na, title="Candles 1")
// barcolor(show_bb and bb_candles2 and bb_diff_a2>bb_bias2 ? yellow:na, title="Candles 2")
barcolor(show_bb and bb_candles1 and bb_diff_a_m>bb_bias ? yellow:na, title="Candles 1")

//barcolor(bb_diff_a>bb_bias ? color.yellow:na, title="BB Candles")
// plot(bb_diff_a,color=bb_diff_a>bb_bias?color.yellow:na, title="BB Diff")

// Plot
// plot(show_bb?basis:na, title="Basis", color=bb_zones_color,linewidth=3)
p1 = plot(show_bb?bb_upper:na, "BB Upper ",color=bb_zones_color,linewidth=2)
p2 = plot(show_bb?bb_lower:na, "BB Lower ",color=bb_zones_color,linewidth=2)
// plot(basis_angle, color=c_hide, title="BA",style=plot.style_circles)
// plot(basis_angle2, color=c_hide, title="BA 2",style=plot.style_circles)
// plot(bb_diff_a2,color=bb_diff_a2>bb_bias2?color.yellow:na, title="BB Diff")

// Temp plots
// plot(show_bb?bb_smooth2:na, "BB Smooth 2 ",color=bb_zones_color2,linewidth=2)
// plot(show_bb?bb_upper2:na, "BB Upper 2 ",color=bb_zones_color2,linewidth=2)
// plot(show_bb?bb_lower2:na, "BB Lower 2 ",color=bb_zones_color2,linewidth=2)




// === ADX + DI with SMA ===
// ==================================================
adx_len = 9 // input(9,title="ADX len") // 14
adx_line = 20 // input(title="threshold", defval=20)
adx_avg = 8 // input(8,title="ADX SMA") // 10
var float adx_top = 54 // input(55,title="High")
var float adx_high = 38 // 39.5
var float adx_mid = 33
var float adx_center = 20
var float adx_low = 12
var float smooth_tr = 0
var float smooth_di_plus = 0
var float smooth_di_minus = 0
TrueRange = max(max(high-low, abs(high-nz(close[1]))), abs(low-nz(close[1])))
DI_plus = high-nz(high[1]) > nz(low[1])-low ? max(high-nz(high[1]), 0): 0
DI_minus = nz(low[1])-low > high-nz(high[1]) ? max(nz(low[1])-low, 0): 0
smooth_tr := nz(smooth_tr[1]) - (nz(smooth_tr[1])/adx_len) + TrueRange
smooth_di_plus := nz(smooth_di_plus[1]) - (nz(smooth_di_plus[1])/adx_len) + DI_plus
smooth_di_minus := nz(smooth_di_minus[1]) - (nz(smooth_di_minus[1])/adx_len) + DI_minus

di_sell = smooth_di_plus / smooth_tr * 100
di_buy = smooth_di_minus / smooth_tr * 100
DX = abs(di_sell-di_buy) / (di_sell+di_buy)*100
adx = sma(DX, adx_len)
adx_sma = sma(adx, adx_avg)
adx_angle = (angle(adx,2))
//hline(adx_line, color=black, linestyle=dashed)




g_res = "RES - Ranging EMA Spread ---------------------------------------------------"
inl_res1 = "inl_res1"
ema1length = 12 // 40 12
ema2length = 50 // 100 43
ranginglength = 3
rangingmaxvalue = 0.12// 0.14 0.1
rangingminvalue = -0.1
show_res = input(title="Show RES", type=input.bool, defval=false,group=g_res) 

// Rangin EMA spread
ema1 = ema(close, ema1length)
ema2 = ema(close, ema2length)
spread = ((ema2 / ema1) -1) * 100
r1 = (spread >= rangingminvalue and spread <= rangingmaxvalue) 
r2 = (spread[1] >= rangingminvalue and spread[1] <= rangingmaxvalue)
r3 = (spread[2] >= rangingminvalue and spread[2] <= rangingmaxvalue)
r4 = (spread[3] >= rangingminvalue and spread[3] <= rangingmaxvalue)
r5 = (spread[4] >= rangingminvalue and spread[4] <= rangingmaxvalue)

ranging = false
if (ranginglength == 1) 
    ranging := r1
if (ranginglength == 2) 
    ranging := r1 and r2
if (ranginglength == 3) 
    ranging := r1 and r2 and r3
if (ranginglength == 4) 
    ranging := r1 and r2 and r3 and r4
if (ranginglength == 5) 
    ranging := r1 and r2 and r3 and r4 and r5

res = spread > 0 ? 0-spread : abs(spread)
res := res * 100
res_weak = gray // grey
res_mid = d_blue // navy
res_strong = aqua // aqua
res_color = ranging ? res_weak : spread > spread[1] ? res_mid : res_strong
// Targets
res_top = 15
res_high = 6
res_low = -6
res_bottom = -15

barcolor(show_res ? res_color : na)
plot(show_res and res? res : na, title="RES", style=plot.style_circles, color=res_color)



g_aroon = "AROON -------------------------------------------------------------"
aroon_len = input(14, title="Aroon Length") // 9
arn_u = 100 * (highestbars(high, aroon_len+1) + aroon_len)/aroon_len
arn_l = 100 * (lowestbars(low, aroon_len+1) + aroon_len)/aroon_len
//plot(arn_u, "Aroon Up", color=#FB8C00)
//plot(arn_l, "Aroon Down", color=#2962FF)
//plotshape(arn_u, "Aroon Up", color=#FB8C00), style=shape.circle,location=location.top



// === Plot === 
// ==================================================
//plot(perc_change(close), title='Change',color=color.new(red,100),style=plot.style_circles)


var bool enter_exit = false
//var int trade_dir = 0
var int state = 0
var int bar_num = 0
var float lastPrice = 0.0
var mult_diff = 10000
var int num_pips = 20
var int num_bars = 4


// Up Trend
// If price is higher than s8_m and s7_a_m>0
// while s8_m<e200_m the counter trades
// are safer to take?
t_up = s7>e200_m and not(low<e200_m and s8_m>e200_m) // s8_a_m>0 and low>e200_m and 
t_down = t_up==false
barcolor(show_trend == false ? na : t_up ? red : green)

entry_signal() =>
	candle = close > open ? 1 : 0
    dir = 0
    counter = 0
    cond = ''
    allow = false
    ea = e200_a
    ea_m = e200_a_m
    ba = basis_angle
    ba2 = basis_angle2
    ea_cnt = e200_a<5?true:false

    // S5 / S6 cross
    cross_red = s5>s6?true:false
    cross_green = s5<s6?true:false

    adx_sell = adx>adx_sma and adx>di_sell and di_sell>di_sell[1]?true:false
    adx_buy = adx>adx_sma and adx>adx_mid and adx>di_buy and di_buy>di_buy[1]?true:false

    // SSL Channels
    ch1_sell = ch1_d<ch1_u?true:false
    ch1_buy = ch1_d>ch1_u?true:false
    ch1_t = ch1_sell?ch1_u:ch1_d
    ch1_b = ch1_sell?ch1_d:ch1_u
    // Multi
    ch1_sell_m = ch1_d_m<ch1_u_m?true:false
    ch1_buy_m = ch1_d_m>ch1_u_m?true:false
    ch1_t_m = ch1_sell_m?ch1_u_m:ch1_d_m
    ch1_b_m = ch1_sell_m?ch1_d_m:ch1_u_m

    // ch2_sell = ch2_d<ch2_u?true:false
    // ch2_buy = ch2_d>ch2_u?true:false
    // ch2_t = ch2_sell?ch2_u:ch2_d
    // ch2_b = ch2_sell?ch2_d:ch2_u

    // ch3_sell = ch3_d<ch3_u?true:false
    // ch3_buy = ch3_d>ch3_u?true:false
    // ch3_t = ch3_sell?ch3_u:ch3_d
    // ch3_b = ch3_sell?ch3_d:ch3_u

    // Mounds - s5 / s6
    mr = s5>s6?true:false
    mr_conv_u = mr and ssl_conv and s5_a>0
    mr_conv_d = mr and ssl_conv and s5_a<0
    mg = s6>s5?true:false
    mg_conv_u = mg and ssl_conv and s5_a>0
    mg_conv_d = mg and ssl_conv and s6_a<0
    // Mounds - s7 / s6
    mr2 = s7>s8?true:false
    mr2_conv_u = mr2 and ssl_conv2 and s7_a>0
    mr2_conv_d = mr2 and ssl_conv2 and s7_a<0
    mg2 = s8>s7?true:false
    mg2_conv_u = mg2 and ssl_conv2 and s7_a>0
    mg2_conv_d = mg2 and ssl_conv2 and s8_a<0


    // Mounds Multi
    mr_m = s5_m>s6_m?true:false
    mr_conv_u_m = mr_m and ssl_conv and s5_a_m>0
    mr_conv_d_m = mr_m and ssl_conv and s5_a_m<0
    mg_m = s6_m>s5_m?true:false
    mg_conv_u_m = mg_m and ssl_conv and s5_a_m>0
    mg_conv_d_m = mg_m and ssl_conv and s6_a_m<0



    // // Normal
    // rs_s, rbc_s 
    // rb_s, rsc_s
    // // Multi
    // rs_s_m, rbc_s_m
    // rb_s_m, rsc_s_m
    
    // === SELL - Uptrend ===
    // ===========
    if s8_a_m>0
        
        // Basic
        if candle==1 and mr and res_color==aqua
         and not(bb_lower<basis2)
         and not(s8_a_m<0)
         and not(mg_m and s8_m>s6_m)
            dir := -1
            cond := 'r3'

        if bb_zone2<4 and 
         (close>bb_upper2 or mr_conv_u)
            dir := 0

        // (rws_weak3 or rsi_close3>70)

        // Special red candle
        if candle==0 and low>bb_upper2 and perc_change(close)<2 
         and res_color==aqua and s1>high
            dir := -1
            cond := 'red'

        if di_sell and di_sell>adx_high and e_zones_m>0
         and not(basis2<ch1_t_m)
         and not(bb_zone2>0 and outHist<0)
            dir := -1
            cond := 'adx'

        // if (rs_w_m or rs_m_m or rs_s_m)
        //     dir := -1
        //     cond := 'r14'

        if s1_m<s2_m or s2_m<s4_m
            dir := 0
        if (s4_m<e200 or s4_a_m<0 or s1_m<s4_m)
            dir := 0


        if (open>bb_upper2 and res_color!=aqua)
            dir := 0


        // Special
        if ((s4_bb_up<10 and high>bb_upper2 and s4_a_m>0) or 
         (s4_bb_up<10 and close>s4_m and s4_a_m<0) 
         )
         and not(s1_m>s4_m or s2_m>s4_m)
            dir := -1
            cond := 's4'

        if candle==1 and bb_upper>bb_upper2
         and (rs_w or rs_m or rs_s)
         and not(s7_a_m>0)
            dir := -1
            cond := 'bb'

            
    // === BUY  ===
    // ===========
    if s8_a_m<0

        if candle==0 and close<bb_lower2 and mg2 and 
         s7_s8_conv==false and outHist<0 and res_color==d_blue
            dir := 1
            cond := 'mg'

        if low>bb_lower2 and outHist<0 and
         s2_a<-20 and s2_a>s2_a[1] and s4_a<-15
            dir := 1
            cond := 's2'

        if rwb_w2 or rwb_m2 or rwb_s2
            dir := 1
            cond := 'r2'

        if (s2_m>s4_m)
         or (s4_m>e200)
         or (s4_m>s8_m)
            dir := 0

        if filter_perc_input and perc_change(close)>10 and bb_zone2>1
            dir := 0
        if filter_adx_input and adx_sma<di_buy
            dir := 0
        if filter_s5_input and s5>s8
            dir := 0
        if filter_ema_input and ea<2
            dir := 0
        if filter_s4_input and s4>bb_lower2 and open<bb_lower2
            dir := 0

        // Special
        // if close<bb_lower2 and (close<s8_m and close>s7_m) and 
        //  e_low<basis2 and s7_m<s8_m
        //  and not(e_zones==0 and rsi_close3>30)
        //     dir := 1
        //     cond := 's7'

    // === SELL Counter ===
    // ===========
    if candle==0 and s8_a_m>0 // ea>-1

        if (rbc_w or rbc_m or rbc_s)
         and not(outHist>0)
         and not(e_zones>1)
         and not(s2_a_m>0)
         //and not()
            dir := 1
            cond := 'rbc'

        if close<bb_lower2 and mg_m and ea<1
         and not(outHist>0)
            dir := 1
            cond := 'close'

        if s2_m>s4_m 
         or (s2_m>s4_m)
            dir := 0

        // if bb_lower2<e200_m and bb_zone2>2 
        //  and open<basis and bb_zone<2
        //     dir := 1
        //     cond := 'str'
         

        if filter_perc_input and perc_change(close)>10
            dir := 0
        if filter_aroon_input and arn_u>0
            dir := 0

        if candle==0 and bb_lower<bb_lower2
         and (rbc_w or rbc_m or rbc_s)
            dir := 1
            cond := 'bb-cnt'

        if s4_a_m>0 
            dir := 0
            
    // Buy Counter
    if candle==1 and s8_a_m < 0

        if rws_w2 or rws_m2
            dir:= -1
            cond := "r2"

        // Filter SSL
        if s1_m<s2_m or s2_m<s4_m
            dir:= 0

        // Filter s7 multi below
        if s7_m<bb_lower2 or s7_a_m>0
            dir:= 0


        // if bb_upper2>s8_m and e_zones<2
        //     dir:= -1
        //     cond := "bb-above\ns8"






    // Filter out same direction trades, wait 4 candles
    // and price higher than 20 pips before allowing another trade
    if dir == state and use_logic and counter==0
        allow := (bar_index - bar_num) >= num_bars ? true : false
        // sell
        if state == -1
            dir := ((close - lastPrice) * mult_diff) > num_pips and allow ? dir : 0
        // buy
        if state == 1
            dir := ((close - lastPrice) * mult_diff) < (num_pips * -1) and allow ? dir : 0

    // Do not trade during New York close
    if ny_close==1
        cond := cond  + '-NY'

    if ny_close==1 and use_ny_filter
        dir := 0



    //var int enter_exit = basis_angle > 0 and dir == -1 ? 0 : basis_angle < 0 and dir
    // if counter==1 and state==dir
    //     dir := 0
        
    type = dir > 0 ? 1 : dir < 0 ? -1 : 0
    [type,counter,cond]


[trade_dir,counter,condition] = entry_signal() 



if trade_dir != 0 and use_logic
    state := trade_dir

    enter_exit := counter == 1 ? 1 : 0
    // Sell
    // if basis_angle > 0 and trade_dir == -1
    //     enter_exit := -1
    // // Counter
    // if basis_angle > 0 and trade_dir == 1
    //     enter_exit := 0
    // // Buy
    // if basis_angle < 0 and trade_dir == 1
    //     enter_exit := -1
    // // Counter
    // if basis_angle < 0 and trade_dir == -1
    //     enter_exit := 0
    // if basis>e200
    //     enter_exit := trade_dir == -1 ? false : trade_dir == 1 or counter ? true : false
    // if basis<e200
    //     enter_exit := trade_dir == 1 ? false : trade_dir == -1 or counter ? true : false
    //enter_exit := counter == 1 ? true : enter_exit == false ? true : false
    lastPrice := close
    bar_num   := bar_index

// if enter_exit != trade_dir and trade_dir != 0
//     enter_exit := enter_exit == false ? true : false
labelText = tostring(condition)
trade_color = trade_dir > 0  ? buy_color : sell_color
if trade_dir != 0 and show_trades // and show_cond
	if trade_dir > 0
        buy = label.new(x=time,y=high,text=labelText,xloc=xloc.bar_time, yloc=yloc.belowbar,color=trade_color,textcolor=color.white, size=size.normal,style=label.style_label_up)
    else
        sell = label.new(x=time,y=high,text=labelText,xloc=xloc.bar_time, yloc=yloc.abovebar,color=trade_color,textcolor=color.white, size=size.normal)

//plot(counter,title="Counter Trade", style=plot.style_circles)
//plot(state,"State",style=plot.style_circles)

// plotshape(show_entry and trade_dir == 1 and enter_exit == 0 ? 1 : na, title="Entry Buy", color=trade_color, location = location.belowbar, style=shape.labelup, text="B", textcolor=color.white, size=size.small)
// plotshape(show_entry and trade_dir == 1 and enter_exit == -1 ? 1 : na, title="Counter Buy", color=trade_color, location = location.belowbar, style=shape.labelup, text="Exit", textcolor=color.white, size=size.small)
// plotshape(show_entry and trade_dir == -1 and enter_exit == 0 ? -1: na, title="Entry Sell", color=trade_color, location = location.abovebar, style=shape.labeldown, text="S", textcolor=color.white, size=size.small)
// plotshape(show_entry and trade_dir == -1 and enter_exit == -1 ? -1 : na, title="Exit Sell", color=trade_color, location = location.abovebar, style=shape.labeldown, text="Exit", textcolor=color.white, size=size.small)
