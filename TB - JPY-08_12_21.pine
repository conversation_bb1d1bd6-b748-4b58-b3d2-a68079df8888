//@version=4
study(title = "Trading Bot - JPY", shorttitle="TB - JPY",overlay=true)

show_entry = input(title="Show Entry", type=input.bool, defval=true)
show_atr = input(title="Show ATR", type=input.bool, defval=true)
show_plots = input(title="Show Plots", type=input.bool, defval=false)
show_bb= input(title="Show BB", type=input.bool, defval=true)
use_barcolor = input(title="Use Bar Color", type=input.bool, defval=false)
show_candles = input(title="Show Candlesticks", type=input.bool, defval=false)
use_rsi = input(title="Use RSI", type=input.bool, defval=true)
show_lastprice = input(title="Show Last Price", type=input.bool, defval=false)
use_logic = input(title="Use Logic", type=input.bool, defval=false)

red = #ff0062
aqua = #00bcd4
yellow = #FFFF00
orange = #ff9800
green = #4caf50
lime = #00E676
white = #ffffff
blue = #42a5f5
gray = #707070
black = #000000
sell_color = color.new(#ff0062,20)
buy_color = color.new(#00c3ff,20)

angle(_src, len) =>
    rad2degree=180/3.14159265359  //pi 
    ang=rad2degree*atan((_src[0] - _src[1])/atr(len))

// Change
perc_change() =>
    perc = abs( (1 - (close[1] / close)) * 10000 )


// ===  EMA's ===
// ==================================================
ema_200 = ema(close, 200)
ema_200_angle = angle(ema_200,2)
// ema_len_f = 5//input(5, minval=1, title="EMA Length") //6
// ema_fast = ema(close, ema_len_f)
// ema_angle = angle(ema_fast,2)



// === Bollinger Bands %B ===
// ==================================================
length = input(20, title="BBR",minval=1) // 20, 29, 43
multi = 1.8 //input(2.0, minval=0.001, maxval=50, title="StdDev")
bbr_basis = sma(close, length)
deviation = multi * stdev(close, length)
bbr_upper = bbr_basis + deviation
bbr_lower = bbr_basis - deviation
bbr = (close - bbr_lower)/(bbr_upper - bbr_lower)
bbr_color = bbr[1]>1 ? #ff0062 : bbr[1]<0 ? #00ff64 : #00c3ff



// ===  ATR ===
// ==================================================
atrlen = 14 //input(14, "ATR Period")
atr_mult = 1.8 //input(1.8, "ATR Mult", step = 0.1) // 1.15
atr_stop = 0.0003 //input(0.0003, "ATR Stop", step = 0.0001) // 0.001
atr_slen = wma(tr(true), atrlen)
atr_upper = atr_slen * atr_mult + close
atr_lower = close - atr_slen * atr_mult

atr_s = sma(atr_upper,atrlen)
//plot(angle(atr_s,10),"ATR AVG - Short")
atr_b = sma(atr_lower,atrlen)
//plot(angle(atr_b,10),"ATR AVG - Short")


// ===  Kijun ===
// ==================================================
middleDonchian(Length) =>
    lower = lowest(Length)
    upper = highest(Length)
    avg(upper, lower)

basePeriods = 14 //input(14, minval=1) //26
kijun =  middleDonchian(basePeriods)
k_angle = angle(kijun,3)


// ===  BB ===
// ==================================================
var float bb_squeeze = 0.00
var float bb_diff = 0.00
BB_length = input(43, minval=1, maxval=150) // 45
BB_stdDev = 2 //input(2, minval=2.0, maxval=3)
sqz_length = 100 //input(100, minval=2, maxval=200) // 100
sqz_threshold = 76 
bb_s = kijun
basis = sma(close, BB_length)
dev = BB_stdDev * stdev(bb_s, BB_length)
bb_upper = basis + dev
bb_lower = basis - dev
bb_spread = bb_upper - bb_lower
avgspread = sma(bb_spread, sqz_length)
bb_squeeze := bb_spread / avgspread * 100
diffspread = sma(bb_spread, 14)
bb_diff := bb_spread / diffspread * 100
basis_angle = angle(basis,3)
bbu_angle = angle(bb_upper,3)
bbl_angle = angle(bb_lower,3)
bbu_diff_ema = abs(bb_upper-ema_200)
bbl_diff_ema = abs(bb_lower-ema_200)
// BB Zones
bb_zone = bb_squeeze < 53 ? 0 : 
 bb_squeeze < sqz_threshold ? 1 : 
 bb_squeeze < 120 ? 2 :
 bb_squeeze < 181 ? 3 :
 bb_squeeze > 181 ? 4 : na
sqz_color = bb_zone == 0 ? #0045b3 :
 bb_zone == 1 ? #ff0062 : 
 bb_zone == 2 ?  gray : 
 bb_zone == 3 ?  #00c3ff : 
 bb_zone == 4 ? white: na

bb_zones_color =  sqz_color



// ===  SSL ===
// ==================================================
ssl_len = input(60, minval=1,title="SSL Len") //75
ssl_len2 = BB_length //input(45, minval=1,title="SSL 2")
ssl_len3 = 8 //input(8, minval=1,title="SSL 3") //7
SSL = wma(2*wma(close, ssl_len/2)-wma(close, ssl_len), round(sqrt(ssl_len)))
SSL2 = wma(2*wma(close, ssl_len2/2)-wma(close, ssl_len2), round(sqrt(ssl_len2)))
SSL3 = wma(2*wma(close, ssl_len3/2)-wma(close, ssl_len3), round(sqrt(ssl_len3)))
//ssl_angle = angle(SSL,3)
ssl_angle = angle(SSL,2)
ssl_angle2 = angle(SSL2,2)
ssl_angle3 = angle(SSL3,2)
ssl_color = ssl_angle2 > 0 ? blue : red



// === Basis to ema 200 distance ===
// ==================================================
be_high = 50
be_up = 10
be_down = -10
be_low = -50

be_basis = sma(close, 35) // 43
be_dist = (be_basis - ema_200) * 100
be_angle = angle(be_dist,2)
dist_angle = angle(be_dist,BB_length)
be_color = be_dist>0?color.green  : color.red



// === MACD Crossover === 
// ==================================================
fastLength = 8 //input(8, minval=1) // 8 //8
slowLength = 25 //input(25,minval=1) // 16 // 21
signalLength= 9 //input(9,minval=1) // 11 // 5
fastMA = ema(close, fastLength)
slowMA = ema(close, slowLength)
macd_c = fastMA - slowMA
sig_c = sma(macd_c, signalLength)
pos = 0
pos := iff(sig_c < macd_c , 1,iff(sig_c > macd_c, -1, nz(pos[1], 0))) 
mc_color = pos == -1 ? red: pos == 1 ? green : blue
mc_angle = angle(macd_c,2)
s_angle = angle(sig_c,2)
mc_diff = macd_c / sig_c

macd_high = syminfo.currency == 'NZD' ? 0.00170 : syminfo.currency == 'JPY' ? 0.395 : 0.0012
macd_up = 0.160
macd_down = -0.160
macd_low = syminfo.currency == 'NZD' ? -0.00170 : syminfo.currency == 'JPY' ? -0.395 : -0.0012



// === WAE ===
// ==================================================
sensitivity = 150
wae_fast= 20 //8
wae_slow= 40 // 50
channelLength= 20 // 45
wae_mult = 2.0//1.85
var float wae_columns = 0.000
wae_dz = nz(rma(tr(true),100)) * 3.7
calc_macd(source, wae_fast, wae_slow) =>
	a = ema(source, wae_fast)
	b = ema(source, wae_slow)
	a - b
calc_BBUpper(source, length, wae_mult) => 
	wae_basis = wma(source, length)
	wae_dev = wae_mult * stdev(source, length)
	t = wae_basis + wae_dev
	[t]
calc_BBLower(source, length, wae_mult) => 
	wae_basis = wma(source, length)
	wae_dev = wae_mult * stdev(source, length)
	t = wae_basis - wae_dev
	[t]

t1 = (calc_macd(close, wae_fast, wae_slow) - calc_macd(close[1], wae_fast, wae_slow))*sensitivity * 100
t2 = (calc_macd(close[2], wae_fast, wae_slow) - calc_macd(close[3], wae_fast, wae_slow))*sensitivity * 100
[e1a] = calc_BBUpper(close, channelLength, wae_mult)
[e1b] = calc_BBLower(close, channelLength, wae_mult)
wae_line = (e1a - e1b)
wae_diff = (wae_line - wae_dz) * 100
//wae_perc = (wae_line / wae_dz) * 100
trendUp = (t1 >= 0) ? t1 : 0
trendDown = (t1 < 0) ? (-1*t1) : 0
wae_columns := abs(t1/100)
wae_color = #000000
wae_angle = angle(wae_line,2)
if t1 >= 0
	wae_color := trendUp<trendUp[1] ? lime : green
if t1 < 0
	wae_color := trendDown<trendDown[1] ? orange : red


// === ADX + DI with SMA ===
// ==================================================
adx_len = input(title="Adx Length",defval=13)
adx_line = 20// input(title="threshold", type=integer, defval=20)
adx_avg = 10// input(title="SMA", type=integer, defval=10)
var float adx_high = 40
var float adx_mid = 34
var float adx_center = 20
var float adx_low = 12
var float smooth_tr = 0
var float smooth_di_plus = 0
var float smooth_di_minus = 0
TrueRange = max(max(high-low, abs(high-nz(close[1]))), abs(low-nz(close[1])))
DI_plus = high-nz(high[1]) > nz(low[1])-low ? max(high-nz(high[1]), 0): 0
DI_minus = nz(low[1])-low > high-nz(high[1]) ? max(nz(low[1])-low, 0): 0
smooth_tr := nz(smooth_tr[1]) - (nz(smooth_tr[1])/adx_len) + TrueRange
smooth_di_plus := nz(smooth_di_plus[1]) - (nz(smooth_di_plus[1])/adx_len) + DI_plus
smooth_di_minus := nz(smooth_di_minus[1]) - (nz(smooth_di_minus[1])/adx_len) + DI_minus

di_plus = smooth_di_plus / smooth_tr * 100
di_minus = smooth_di_minus / smooth_tr * 100
DX = abs(di_plus-di_minus) / (di_plus+di_minus)*100
adx = sma(DX, adx_len)
adx_sma = sma(adx, adx_avg)
adx_angle = (angle(adx,2))



// === Stochastics Momentum Index ===
// ==================================================
stc_mom_a = 10 //input(10, "Percent K Length")
stc_mom_b = 3//input(3, "Percent D Length")
// Range Calculation
ll = lowest (low, stc_mom_a)
hh = highest (high, stc_mom_a)
diff = hh - ll
rdiff = close - (hh+ll)/2
avgrel = ema(ema(rdiff,stc_mom_b),stc_mom_b)
avgdiff = ema(ema(diff,stc_mom_b),stc_mom_b)
// SMI calculations
SMI = avgdiff != 0 ? (avgrel/(avgdiff/2)*100) : 0
mom_sig = ema(SMI,stc_mom_b)
mom_ema = ema(SMI, 10)
mom_angle = angle(mom_ema,2)
mom_high = 40
mom_low = -40



// === MACD Dema === 
// ==================================================
var float dema_histo = 0
dema_sma = input(12,title='DEMA Short') // 12
dema_lma = input(26,title='DEMA Long') // 26
dema_signal_in = input(9,title='Signal') // 7 // 9 
dema_high= 27//input(27,title='Dema high')
dema_mid= 11//input(11,title='Dema mid')
dema_down = -10//input(-10,title='Dema down') 
dema_low = -24//input(-24,title='Dema Low') 
dema_mult = 100 // GBP/USD 10000

MMEslowa = ema(close,dema_lma)
MMEslowb = ema(MMEslowa,dema_lma)
DEMAslow = ((2 * MMEslowa) - MMEslowb )
MMEfasta = ema(close,dema_sma)
MMEfastb = ema(MMEfasta,dema_sma)
DEMAfast = ((2 * MMEfasta) - MMEfastb)
dema_line = (DEMAfast - DEMAslow)*dema_mult
MMEsignala = ema(dema_line, dema_signal_in)
MMEsignalb = ema(MMEsignala, dema_signal_in)
dema_sig = ((2 * MMEsignala) - MMEsignalb )
dema_histo := (dema_line - dema_sig)
dema_color = dema_histo>0?green:red



// === RSI Chart Bars ===
// ==================================================
var rsi_color = #000000
rsi_len = 14 //input(14, minval=1, title="Length")
rsi_up = rma(max(change(close), 0), rsi_len)
rsi_down = rma(-min(change(close), 0), rsi_len)
rsi = rsi_down == 0 ? 100 : rsi_up == 0 ? 0 : 100 - (100 / (1 + rsi_up / rsi_down))
isup() => rsi > 70
isdown() => rsi < 30
// plot(rsi,title="RSI",color=color.blue)
rsi_color := isup() ? color.new(buy_color, 10) : isdown() ? color.new(#ff0000, 10)  : na
barcolor(rsi_color, title="Rsi Candles")


// === RSI Wicks ===
// ==================================================
std         = false //input(false, title="Show Standard RSI")
rsi_candles = false//input(true,  title="Show Candles")
wicks       = true //input(true,  title="Wicks based on stand-alone RSI")
src_close   = close
src_open    = open
src_high    = high
src_low     = low 
len         = 14 //input(14, minval=1, title="Length")

norm_close  = avg(src_close,src_close[1])
gain_loss_close   = change(src_close)/norm_close
RSI_close         = 50+50*rma(gain_loss_close, len)/rma(abs(gain_loss_close), len)

norm_open = if wicks==true 
    avg(src_open,src_open[1])
else 
    avg(src_close,src_close[1])
gain_loss_open   = change(src_open)/norm_open
RSI_open         = 50+50*rma(gain_loss_open, len)/rma(abs(gain_loss_open), len)
        
norm_high = if wicks==true 
    avg(src_high,src_high[1])
else 
    avg(src_close,src_close[1])
gain_loss_high   = change(src_high)/norm_high
RSI_high         = 50+50*rma(gain_loss_high, len)/rma(abs(gain_loss_high), len)
        
norm_low  = if wicks==true
    avg(src_low,src_low[1])
else 
    avg(src_close,src_close[1])
gain_loss_low   = change(src_low)/norm_low
RSI_low         = 50+50*rma(gain_loss_low, len)/rma(abs(gain_loss_low), len)
rws_strong = RSI_open>69 and close>open
rws_mid = RSI_close>70 and RSI_open<70 and close>open
rws_weak = RSI_close<70 and RSI_high>70 and close>open
rwb_weak = RSI_close>30 and RSI_low<30 and close<open
rwb_mid = RSI_close<30 and RSI_open>30 and close<open
rwb_strong = RSI_open<30 and close<open



// === RES - Ranging EMA Spread  ===
// ==================================================
ema1length = 12
ema2length = 43
ranginglength = 3
rangingmaxvalue = 0.14//0.1
rangingminvalue = -0.1
enablebarcolors = false


// EMA spread
ema1 = ema(close, ema1length)
ema2 = ema(close, ema2length)
spread = ((ema2 / ema1) -1) * 100
r1 = (spread >= rangingminvalue and spread <= rangingmaxvalue) 
r2 = (spread[1] >= rangingminvalue and spread[1] <= rangingmaxvalue)
r3 = (spread[2] >= rangingminvalue and spread[2] <= rangingmaxvalue)
r4 = (spread[3] >= rangingminvalue and spread[3] <= rangingmaxvalue)
r5 = (spread[4] >= rangingminvalue and spread[4] <= rangingmaxvalue)

ranging = false

if (ranginglength == 1) 
    ranging := r1
if (ranginglength == 2) 
    ranging := r1 and r2
if (ranginglength == 3) 
    ranging := r1 and r2 and r3
if (ranginglength == 4) 
    ranging := r1 and r2 and r3 and r4
if (ranginglength == 5) 
    ranging := r1 and r2 and r3 and r4 and r5

res = spread > 0 ? 0-spread : abs(spread)
res := res * 100
col_low = color.new(#707070, 0) // grey
col_mid = color.new(#0045b3, 0)// navy
col_high = color.new(#00c3ff, 0) // aqua
color_res = ranging ? col_low : spread > spread[1] ? col_mid : col_high
plot(res,"RES", style=plot.style_circles, linewidth=0, color=color_res)




// === Plot === 
// ==================================================
plot(basis_angle, color= white , title="Basis angle",style=plot.style_circles)
plot(bbu_angle, color= white , title="BB Upper Angle",style=plot.style_circles)
plot(bbl_angle, color= white , title="BB Lower Angle",style=plot.style_circles)
plot(adx, color=yellow, title="ADX",style=plot.style_circles)
plot(adx_sma, color=white, title="ADX SMA",style=plot.style_circles)
plot(di_plus, color=green, title="DI+",style=plot.style_circles)
plot(di_minus, color=red, title="DI-",style=plot.style_circles)
plot(bbr, color=bbr_color, title="BBR",style=plot.style_circles)
plot(be_dist, color=be_color, title="Basis to ema distance",style=plot.style_circles)
plot(mom_ema, title="Mom EMA", style=plot.style_circles, color=yellow)
plot(mom_sig, title="Mom SMA", style=plot.style_circles, color=red)
plot(dema_line, title="Dema Line", style=plot.style_circles, color=green)
plot(dema_sig, title="Dema Sig", style=plot.style_circles, color=red)
plot(dema_histo, title="Dema Histo", style=plot.style_circles, color=dema_color)
// Angles
//plot(wae_angle, color= wae_color , title="WAE Angle",style=plot.style_circles)
//plot(bb_diff, color= white , title="Basis Diff",style=plot.style_circles)
plot(ema_200_angle, color= white , title="Ema 200 angle",style=plot.style_circles)


plot(ssl_angle, color=white , title="SSL angle",style=plot.style_circles)
plot(ssl_angle2, color= white , title="SSL2 angle",style=plot.style_circles)
plot(ssl_angle3, color= white , title="SSL3 angle",style=plot.style_circles)
plot(adx_angle, color=white, title="ADX Angle",style=plot.style_circles)
//plot(mom_angle, title="Angle Stoch_MTM", style=plot.style_circles, color=white)
//plot(mc_angle, color=yellow, title="MACD Angle",style=plot.style_circles)
//plot(s_angle, color=yellow, title="Signal Angle",style=plot.style_circles)

plot(atr_s,"ATR AVG - Short",color=#55d51a)
plot(atr_b,"ATR AVG - Long",color=#55d51a)

// plot( show_plots ? wae_line:na,title="Explosion Line",style=plot.style_circles,color=wae_color)
// plot( show_plots ? wae_dz:na,title="Dead Zone",style=plot.style_circles)
// plot( show_plots ? wae_angle:na,title="WAE Angle",style=plot.style_circles)
// Kijun
plot(kijun, color=red, title="Kijun",linewidth=2)
// SSL
plot(SSL, color=white , title="SSL")
plot(SSL2, color=ssl_color , title="SSL 2")
plot(SSL3, color= green , title="SSL 3")
// BB

plot(bbu_diff_ema, title="Diff BBU ema 200", color=bb_zones_color,linewidth=3)
plot(basis, title="Basis", color=bb_zones_color,linewidth=3)
p1 = plot(show_bb ? bb_upper : na, "BB Upper ",color=bb_zones_color,linewidth=2)
p2 = plot(show_bb ? bb_lower : na, "BB Lower ",color=bb_zones_color,linewidth=2)

// EMA 200 bands large
plot(ema_200, "EMA 200", color=color.new(yellow,50),linewidth=2 )

// ATR
//plot(show_plots ? atr_upper : na, "+ATR Upper", color=color.new(#ffffff,85))
//plot(show_plots ? atr_lower : na, "-ATR Lower", color=color.new(#ffffff,85))
plot(show_atr ? atr_upper + atr_stop : na, "+ATR stop", color=color.new(#ffff00,75))
plot(show_atr ? atr_lower - atr_stop : na, "-ATR stop", color=color.new(#ffff00,75))
//barcolor(pos == -1 and use_barcolor? #ff0000: pos == 1 and use_barcolor ? #00a000 : na)

var bool enter_exit = false
//var int trade_dir = 0
var int state = 0
var int bar_num = 0
var float lastPrice = 0.0
var mult_diff = 10000
var int num_pips = 25
var int num_bars = 4

entry_signal() =>
	candle = close > open ? 1 : 0
    dir = 0
    counter = 0
    allow = false

    // Sell - after uptrend downward slope
    if bb_zone>0 and candle==1 and bbu_angle<1 and atr_upper>bb_upper and
     close>atr_s and close>SSL3 and atr_lower>SSL2 and
     atr_b>bb_lower and atr_b>basis
        dir := -1

    // Buy
    if bb_zone>2 and candle==0 and atr_lower<bb_lower and
     atr_s<basis and atr_b>bb_lower and 
     close<atr_b and open>bb_lower and
     di_minus>adx_mid and di_minus<adx_high and
     adx_sma>adx and adx_sma>adx_high
        dir := 1

    // high lower than bb_upper
    // if bb_zone>1 and candle==1 and high>bb_upper and close<bb_upper and be_dist>be_high
    //     dir := -1
    //     counter := 1

    //strong uptrend counter
    // if candle==0 and close<SSL3 and atr_upper<atr_s and
    //  atr

    // Uptrend
    if bb_lower>ema_200

        // Extreme highs
        if bb_zone>2 and candle==1 and close>atr_s and close>bb_upper and
         atr_b>basis and adx>adx_center and be_dist>be_high
         and not(di_plus>adx_mid and adx<adx_mid)
            dir := -1

        if bb_zone>0 and bb_zone<3 and candle==1 and 
         atr_b>basis and atr_lower>basis and 
         (close>atr_s or atr_s>bb_upper)
            dir := -1

        // if bb_zone<3 and candle==1 and close>atr_s and 
        //  atr_lower>kijun and atr_b>basis
        //     dir := -1

        
        // high lower than bb_upper
        // if bb_zone>1 and candle==1 and low>basis and high>bb_upper and close<bb_upper and
        //  atr_s>bb_upper and atr_upper>atr_s and atr_b>basis and adx_sma>adx and be_dist>be_high
        //     dir := -1
        //     counter := 1

        // == Counter

        // strong uptrend
        // if bb_zone>2 and candle==0 and close<SSL and
        //  atr_upper<atr_s and basis_angle>2 and atr_lower<atr_b and
        //  mom_ema<0 and macd_c<0
        //     dir := 1
        //     counter := 1

        // atr below ema
        // if candle==0 and atr_lower<ema_200 and basis_angle>-3 and 
        //  atr_b<bb_lower and adx>adx_center and di_plus>adx_low and macd_c<0
        //  and not(bb_zone<2 and be_dist<be_high) 
        //     dir := 1
        //     counter := 1



    // BB lower less than ema and bb upper more than ema
    if bb_lower<ema_200 and bb_upper>ema_200

        // Extreme highs
        // if candle==1 and close>atr_s and 
        //  atr_b>basis and adx>adx_center
        //  and not(di_plus>adx_mid and adx<adx_mid)
        //  and not(bb_zone==4 and atr_lower<SSL)
        //  and not(bb_zone==4 and be_dist<be_high)
        //  and not(atr_lower>open)
        //  and not(be_dist<0)
        //  and not(mom_sig<mom_high)
        //     dir := -1

        // Sell - Strong uptrend
        if basis>ema_200 and candle==1 and close>bb_upper and
         di_plus>adx_high and wae_color==green and be_dist>be_high
         and not(adx>adx_mid and adx<adx_high)
         and not(mom_ema>mom_sig)
         and not(bb_zone==4 and kijun>SSL)
         and not(kijun>SSL2)
            dir := -1


        // Counter

        // strong uptrend
        if bb_zone>2 and candle==0 and close<SSL and
         atr_upper<atr_s and basis_angle>2 and di_minus[1]>di_plus[1]
         and not(be_dist>be_high and atr_lower>atr_b) and
         macd_c<0 and be_dist<be_high
            dir := 1
            counter := 1


        // Downtrend
        // Buy - Strong downtrend 1
        if candle==0 and close<bb_lower and close<SSL3 and
         di_minus>adx_high and di_plus<adx_low//and adx>di_minus
         and not(adx>adx_mid and adx<adx_high)
            dir := 1

    if bb_upper<ema_200 
        
        // Extreme lows
        if bb_zone>2 and candle==0 and close<atr_b and close<bb_lower and
         atr_s<basis and adx>adx_center
         and not(bb_zone<2 and be_dist<be_low)
            dir := 1

        // Extreme - zone lower than 3
        if bb_zone>2 and candle==0 and close<bb_lower and close<SSL3 and
         di_minus>adx_high and di_plus<adx_low and adx>di_minus
            dir :=1
            
        // Buy - Strong downtrend 2
        if bb_zone>1 and candle==0 and close<bb_lower and close<SSL3 and
         di_minus>adx_mid and di_plus<adx_low and adx>adx_high and
         mom_sig<mom_low
         and not(di_minus>adx_mid and adx<adx_high)
         and not(bb_zone==4)
            dir := 1

        // Counter

        if atr_upper>bb_upper and bb_zone<3 and bbu_angle<1 and 
         basis_angle<1 and be_dist<be_low and close>atr_s
            dir :=-1
            counter := 1

    // MACD CROSS
    m_high = sig_c>macd_high
    m_up = sig_c>macd_up
    m_down = sig_c<macd_down
    m_low = sig_c<macd_low

    if (m_high or m_up) and candle==1 
        dir := -1
    if (m_down or m_low) and candle==0
        dir := 1

    if bb_zone<3 and be_dist<be_low and macd_c<0
        dir := 1

    // Filter out MACD Cross
    if dir==-1 and counter==0
        if (bb_zone>2 and atr_b<basis) or mc_color==red
            dir := 0
        if bb_upper<ema_200 and adx_sma<adx_high
            dir := 0
        if bb_zone==4 and be_dist<be_high
            dir := 0
        if wae_color!=green
            dir := 0
        if close<bb_upper or close<SSL3 or close<atr_s
            dir := 0
    if dir==1 and counter==0
        if (bb_zone>2 and atr_s>basis) or mc_color==green
            dir := 0
        if bb_lower>ema_200 and adx_sma<adx_high
            dir := 0
        if wae_color!=red
            dir := 0
        if close>bb_lower or close>SSL3 or close>atr_b
            dir := 0
        if (di_minus>adx_mid and adx<adx_high) or
         (bb_zone==4 and adx<di_minus)or
         (bb_zone==4 and rwb_strong==0)
            dir := 0
        //if basis_angle<-6 and 


    
    if bb_zone==0 and be_dist<-45 and close<atr_b and dema_sig<0
        dir := 1
        counter := 1
    // BBR
    if basis_angle<4 and basis_angle>-4

        if bbr>1
         and not(bb_zone<2 and be_dist<be_high)
         and not(be_angle[1]<0 or close<atr_s)
            dir := -1
        if bbr>1 and rws_strong==1
            dir := -1

        if bbr<0
         and not(bb_zone<2 and be_dist>be_low)
         and not(be_angle[1]>0 or close>atr_b)
         and not(bbu_angle<-3)
            dir := 1
        if bbr<0 and rwb_strong==1
            dir := 1

    if bb_zone==2 and basis_angle<-6 and basis_angle>-9
        if rwb_strong==true
            dir := 1

    // if bb_zone<2 and low<bb_lower and candle==0 and 
    //  be_dist>be_low and (be_angle>be_high or be_angle<be_low) and perc_change()<10
    //     dir := 1



    // Filter out same direction trades, wait 4 candles
    // and price higher than 20 pips before allowing another trade
    if dir == state and use_logic
        allow := (bar_index - bar_num) > num_bars ? true : false
        // sell
        if dir == -1
            dir := ((close - lastPrice) * mult_diff) > num_pips and allow ? dir : 0
            //counter := dir!=0 ? 1 : 0
        // buy
        if dir == 1
            dir := ((close - lastPrice) * mult_diff) < (num_pips * -1) and allow ? dir : 0

            //counter := dir!=0 ? 1 : 0

    // if dir!=state and dir==1
    //     if abs( ((lastPrice - close) * mult_diff) ) < 50
    //         dir==0

    // Filter out high percentage trades
    // if wae_columns<4.7
    //     if counter == 0 and abs(perc_change())>20
    //         dir := 0
    //     if counter == 1 and abs(perc_change())>20
    //         dir := 0

    //var int enter_exit = basis_angle > 0 and dir == -1 ? 0 : basis_angle < 0 and dir
    type = dir > 0 ? 1 : dir < 0 ? -1 : 0
    [type,counter]

[trade_dir,counter] = entry_signal() 
    
// if trade_dir[1]==0 and trade_dir != 0
//     dir == 0

if trade_dir != 0 and use_logic
    state := trade_dir

    //enter_exit := counter == 1 ? 1 : 0
    // Sell
    // if basis_angle > 0 and trade_dir == -1
    //     enter_exit := -1
    // // Counter
    // if basis_angle > 0 and trade_dir == 1
    //     enter_exit := 0
    // // Buy
    // if basis_angle < 0 and trade_dir == 1
    //     enter_exit := -1
    // // Counter
    // if basis_angle < 0 and trade_dir == -1
    //     enter_exit := 0
    // if basis>ema_200
    //     enter_exit := trade_dir == -1 ? false : trade_dir == 1 or counter ? true : false
    // if basis<ema_200
    //     enter_exit := trade_dir == 1 ? false : trade_dir == -1 or counter ? true : false
    //enter_exit := counter == 1 ? true : enter_exit == false ? true : false
    lastPrice := close
    bar_num   := bar_index

// if enter_exit != trade_dir and trade_dir != 0
//     enter_exit := enter_exit == false ? true : false

trade_color = trade_dir > 0  ? buy_color : sell_color
plot(trade_dir!=0?lastPrice:na,title="Last Price", style=plot.style_circles)
//plot(show_lastprice and lastPrice?lastPrice:na,title="Last Price", style=plot.style_circles)
plot(counter,title="Counter Trade", style=plot.style_circles)
//plot(state,"State",style=plot.style_circles)

// Buy
tmp_text = counter==1? "B":"Exit"
plotshape(show_entry and trade_dir == 1 and counter == 1 ? 1 : na, title="Entry Buy", color=trade_color, location = location.belowbar, style=shape.labelup, text="B", textcolor=color.white, size=size.small)
plotshape(show_entry and trade_dir == 1 and counter == 0 ? 1 : na, title="Exit Buy", color=trade_color, location = location.belowbar, style=shape.labelup, text="Exit", textcolor=color.white, size=size.small)
// Sell
plotshape(show_entry and trade_dir == -1 and counter == 1 ? -1: na, title="Entry Sell", color=trade_color, location = location.abovebar, style=shape.labeldown, text="S", textcolor=color.white, size=size.small)
plotshape(show_entry and trade_dir == -1 and counter == 0 ? -1 : na, title="Exit Sell", color=trade_color, location = location.abovebar, style=shape.labeldown, text="Exit", textcolor=color.white, size=size.small)
