//@version=4
//Stochastic Momentum Index
// Author: Surjith <PERSON> (India)
// Copyright: CC 3.0 
//Thanks UCSgears, lonestar108
study("Stochastics Momentum Index", shorttitle = "Stoch_MTM")
k_len = input(10, "Percent K Length")
d_len = input(3, "Percent D Length")
d_len2 = input(2, "Percent D Length")
ob = input(45, "Overbought")
os = input(-45, "Oversold")
// Range Calculation
ll = lowest (low, k_len)
hh = highest (high, k_len)
diff = hh - ll
rdiff = close - (hh+ll)/2

avgrel = ema(ema(rdiff,d_len),d_len)
avgdiff = ema(ema(diff,d_len),d_len)


// SMI calculations
SMI = avgdiff != 0 ? (avgrel/(avgdiff/2)*100) : 0
SMIsignal = ema(SMI,d_len)
emasignal = ema(SMI, k_len)



//Color Definition for Stochastic Line
//col = SMI >= 40 ? green : SMI <= -40 ? red : black
c_sma = SMIsignal > ob or SMIsignal < os ? color.blue : na

//l_sma_ = SMIsignal < os ? SMIsignal : na

avgrel2 = ema(ema(rdiff,d_len2),d_len2)
avgdiff2 = ema(ema(diff,d_len2),d_len2)
SMI2 = avgdiff != 0 ? (avgrel2/(avgdiff2/2)*100) : 0
SMIsignal2 = ema(SMI2,d_len2)
c_sma2 = SMIsignal2 > ob or SMIsignal2 < os ? color.new(color.white,50) : na

plot(SMIsignal, title="Stochastic", color=c_sma)
plot(SMIsignal2, title="Stochastic 2", color=c_sma2)
plot(emasignal, title="EMA", color=emasignal>0 ? #ff0000 : #00ff00)
//p1 = plot(ob,title="Over bought")
//p3 = plot(os,title="Over Sold")
//p2 = plot(SMIsignal,title="Over bought",color=c_sma)
//p4 = plot(l_sma_,title="l_sma_")

h0 = hline(ob,title="Line - OB")
h1 = hline(os,title="Line OS") 
h2 = hline(0,title="Line - 0")
 
//fill(p1, p2, color=red, transp=40, title='OverSold')
//fill(p3, p4, color=green, transp=40, title='OverBought')
 
