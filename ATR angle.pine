//@version=5
indicator("ATR angle", overlay = false)

red = #ff0062
orange = #ff9800
yellow = #FFFF00
green = #005a04
lime = #00E676
aqua = #00bcd4
blue = #2962ff 
dark_blue = #2962ff
violet = #814dff
white = #ffffff
gray = #707070
dark_gray = #333333

angle(_src, len) =>
    rad2degree=180/3.14159265359  //pi 
    ang=rad2degree*math.atan((_src[0] - _src[1]) / ta.atr(len))
    
newbar(res) => ta.change(time(res)) == 0 ? 0 : 1


//
// ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ //
// STOP LOSS
// ------------------------------------------------------------------------------------------------------------------
g_sl = 'Stop Loss ----------------------------------------------------'
atr_1         = 'ATR 1'
sl_time_1     = input.timeframe('30', "Resolution", group=atr_1)
show_sl_1     = input.bool(true,title="Display ATR 1",group=atr_1)
show_upper_1  = input.bool(false,title="Show Upper",group=atr_1)
show_mid_1    = input.bool(true,title="Show Mid",group=atr_1)
show_lower_1  = input.bool(false,title="Show Lower",group=atr_1)
atr_2         = 'ATR 2'
sl_time_2     = input.timeframe('120', "Resolution", group=atr_2)
show_sl_2     = input.bool(true,title="Display ATR 2",group=atr_2)
show_upper_2  = input.bool(false,title="Show Upper",group=atr_2)
show_mid_2    = input.bool(true,title="Show Mid",group=atr_2)
show_lower_2  = input.bool(false,title="Show Lower",group=atr_2)
show_step_2   = input.bool(true,title="Show Steps",group=atr_2)
atr_props         = 'Properties'
sl_Multip   = input.float(2.0, title='Stop Loss',group=atr_props) // 4 1.5
atr_len     = input.int(14, title='ATR Length ',group=atr_props)
//i_plot_trades = input.bool(true, title="Display Trades",group=atr_props)
atr_src     = input.string('close', title='Close or Wicks', options=['close', 'wicks'],group=atr_props) // close
sl_min      = input.float(0.07, title='Stop Loss Minimum Pips', group=atr_props)
sl_max      = input.float(0.12, title='Stop Loss Maximum Pips', group=atr_props)

atr_type    = input.string(title='ATR Type', defval='ema', options=['sma', 'ema', 'zema', 'dema', 'tema', 'wma', 'vwma', 'smma', 'rma', 'hma', 'lsma', 'McGinley'], group=atr_props)
atr_smooth  = input.int(5, title="ATR Smooth", group=atr_props)

f_atr_angle()=>

    ATR = ta.atr(atr_len)
    var float sl_long = 0.0
    var float sl_short = 0.0
    sl_long     := (atr_src =='close' ? close : low)  - ATR * sl_Multip 
    sl_short    := (atr_src =='close' ? close : high) + ATR * sl_Multip 

    atr_upper = ta.sma( ta.ema(sl_short, atr_len), atr_smooth ) //ma_types(atr_len, atr_type, sl_short)
    atr_lower = ta.sma( ta.ema(sl_long, atr_len), atr_smooth )  //ma_types(atr_len, atr_type, sl_long)
    atr_mid = (atr_lower + atr_upper) * 0.5

    atr_upper_a = angle(atr_upper, 14)
    atr_mid_a = angle(atr_mid, 14)
    atr_lower_a = angle(atr_lower, 14)

    [atr_upper_a, atr_mid_a, atr_lower_a]


f_angle_dir(m_angle, obj_time)=>
    var dir = 0.0
    dir := ta.change(m_angle) and m_angle < m_angle[1] ? -1 : ta.change(m_angle) and m_angle > m_angle[1] ? 1 : dir
    //ma_angle_direction = m_angle>m_angle[str.tonumber(obj_time) * i_incre_up_down] ? 1 : -1


[atr_upper_a_1, atr_mid_a_1, atr_lower_a_1] = request.security(syminfo.tickerid, sl_time_1, f_atr_angle() )
[atr_upper_a_2, atr_mid_a_2, atr_lower_a_2] = request.security(syminfo.tickerid, sl_time_2, f_atr_angle() )


if newbar(sl_time_1) == 0
    atr_upper_a_1 := atr_upper_a_1[1]
    atr_mid_a_1 := atr_mid_a_1[1]
    atr_lower_a_1 := atr_lower_a_1[1]

if newbar(sl_time_2) == 0
    atr_upper_a_2 := atr_upper_a_2[1]
    atr_mid_a_2 := atr_mid_a_2[1]
    atr_lower_a_2 := atr_lower_a_2[1]

atr_upper_p = plot(show_sl_1 and show_upper_1 ? atr_upper_a_1  : na,"ATR Angle Up ", color=atr_upper_a_1 > 0 ? green : red  )
atr_mid_p1 = plot(show_sl_1 and show_mid_1 ? atr_mid_a_1  : na,"ATR Angle Mid ", color=atr_mid_a_1 > 0 ? aqua : blue  )
atr_lower_p = plot(show_sl_1 and show_lower_1 ? atr_lower_a_1  : na,"ATR Angle ", color=atr_lower_a_1 > 0 ? green : red  )

atr_upper_p2 = plot(show_sl_2 and show_upper_2 ? atr_upper_a_2  : na,"ATR Angle Mid ", color=atr_upper_a_2 > 0 ? green : red  )
atr_mid_p2 = plot(show_sl_2 and show_mid_1 ? atr_mid_a_2  : na,"ATR Angle Mid ", color=atr_mid_a_2 > 0 ? white : red, linewidth=2  )
atr_lower_p2 = plot(show_sl_2 and show_lower_2 ? atr_lower_a_2  : na,"ATR Angle Mid ", color=atr_lower_a_2 > 0 ? green : red  )

// Stepping
atr_mid_2_dir = f_angle_dir(atr_mid_a_2, sl_time_2) 
//m3a_dir = m3_a>m3_a[1] ? 1 : -1
plotshape(atr_mid_2_dir == -1 ,title="Dir Down",color=show_step_2 ? red : na ,style=shape.circle,location=location.top)
plotshape(atr_mid_2_dir == 1 ,title="Dir Up",color=show_step_2 ? green : na ,style=shape.circle,location=location.bottom)

// plot(show_sl_1 ? atr_lower  : na,"ATR Lower ", color=lime )
// plot(show_sl_1 ? atr_upper  : na,"ATR Upper ", color=red )
// plot(show_sl_1 ? atr_mid  : na,"ATR Mid ", color=atr_mid_a_2 > 0 ? white : gray )
// plot(show_sl_1 ? sl_short : na,"ATR - ", color=color.new(red,70) )
// plot(show_sl_1 ? sl_long  : na,"ATR + ", color=color.new(green,70) )

hline(0)

candle = close>open ? 1 : 0
// atr_sell = sl_short>atr_upper and atr_mid_a_2<0 and candle==1 ? 1 : 0
// atr_buy = sl_long<atr_lower and atr_mid_a_2>0 and candle==1 ? 1 : 0
//plotshape(show_sl and atr_sell ? 1 : 0,"Top End",style=shape.circle, location = location.top, color = color.rgb(230, 0, 0))
//plotshape(show_sl and atr_buy ? 1 : 0,"Bottom End",style=shape.circle, location = location.bottom, color =  #00be00 )


