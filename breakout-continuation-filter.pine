//@version=5

// Normal Settings : 4, 6, 7, 14
//This indicator acts as a filter for determining recent breakouts and consolidations in price.
//The first way to use the indicator is with a short lookback period. It then will paint yellow most of the time, with 
//red marking a sharp recent breakdown in price and green marking a sharp breakout in price. This can be used to follow the breakout, or to fade it.

// Long Duration : 3, 4, 50, 14
//The second way to use the indicator is a long lookback period. This will change the output to be colored most of the time, with small sections of yellow.
// The yellow indicators areas where price has not made a large move in a while, or periods of consolidation. 
//This can then be used to plan reversal trades, or follows any new trend. The blue line is a Average True Range Percent Rank, when this value is high, it means that breakouts are less likely to trigger, since price has been moving rapidly recently, and a relative breakout would have to be a large move. When the line is low, breakouts will trigger more easily, since price has been moving relatively slowly


indicator('Breakout/Consolidation Filter [jwammo12] - copy')

red = #ff0062
orange = #ff9800
yellow = #FFFF00
green = #4caf50
lime = #00E676
aqua = #00bcd4
blue = #2962ff
dark_blue = #2962ff
violet = #814dff
white = #ffffff
gray = #707070
black = #000000

ATRMultiple = input(4.0)
BreakoutLength = input(6)
Lookback = input(7)
ATRPeriod = input(14)
bc_line = input(50)  //27
bc_len = input(100)  //50
trueRange = ta.atr(ATRPeriod)

xColor = yellow
for i = Lookback to 0 by 1
    if close[i] - open[i + BreakoutLength] > trueRange[i] * ATRMultiple
        xColor := green
        xColor
    if open[i + BreakoutLength] - close[i] > trueRange[i] * ATRMultiple
        xColor := red
        xColor

trueRangePR = ta.percentrank(trueRange, bc_len)
plot(trueRangePR, title='true Range', color=aqua)
plot(bc_line, title='BC Line',color=color.new(xColor, 50), style=plot.style_cross)
cross_up = ta.crossover(trueRangePR, bc_line)
plotshape(cross_up, title='Cross Up', style=shape.xcross, color=color.new(aqua, 0), location=location.top)
cross_down = ta.crossunder(trueRangePR, bc_line)
plotshape(cross_down, title='Cross Down', style=shape.xcross, color=color.new(red, 0), location=location.bottom)

