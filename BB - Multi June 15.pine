//@version=5
indicator(title='BB - Jun 15', overlay=true)


white = #ffffff
gray = #707070
yellow = #FFFF00
aqua = #00bcd4
lime = #00E676
red = #ff0000
green = #4caf50

angle(_src, len) =>
    rad2degree = 180 / 3.14159265359  //pi 
    ang = rad2degree * math.atan((_src[0] - _src[1]) / ta.atr(len))
    ang

// New Bar Multi Timeframe
newbar(res) => ta.change(time(res)) == 0 ? 0 : 1

g_g = 'BB Global'
inl_g = 'inl_g1'
show_bb = input.bool(title='Show BB', defval=true, group=g_g, inline=inl_g)
show_overlap = input.bool(title='Show Overlap ', defval=false, group=g_g, inline=inl_g)

g_bb = 'BB 1'
inl_bb1 = 'inl_bb1'
inl_bbb = 'inl_bbb'
sqz_length = 80 //input(80, title='Sqz Len')  // 100

// BB1 Settings
bb1_time = input.timeframe(title='Timeframe', defval='', group=g_bb)
bb1_len = input.int(50, title='Length', group=g_bb, inline=inl_bb1)
bb1_stdev = input.float(1.5, title='Deviations', minval=1.0, maxval=4.0, step=0.1, group=g_bb, inline=inl_bb1) // 1.5
bb1_type = input.string(title='Type', defval='WMA', options=['SMA', 'EMA', 'WMA', 'HULL'], group=g_bb, inline=inl_bb1)
bb1_show = input.bool(title='BB1', defval=false, inline=inl_bbb, group=g_bb)
i_bb1_basis = input.bool(title='Show Basis', defval=false, group=g_bb, inline=inl_bbb)
bb1_fill = input.bool(title='Show fill', defval=false, group=g_bb, inline=inl_bbb)
i_bb1_bias = input.bool(title='Show Bias', defval=false, group=g_bb, inline=inl_bbb)
bb1_zones = input.bool(title='Zone Change', defval=false, group=g_bb, inline=inl_bbb)
bb1_zones2 = input.bool(title='Show Zones ', defval=false, group=g_bb, inline=inl_bbb)
bb1_bias = 89.7 //input.float(title='Bias 1', defval=89.7, step=0.1, group=g_bb, inline=inl_bb1)
//bb1_stdDev = 2  //input(2, minval=2.0, maxval=3)
bb1_smooth_input = 1  // input(title="Smoothing",defval=1,type=input.integer,group=g_bb, inline=inl_bb1) 

// BB2 Settings
g_bb2 = 'BB 2'
inl_bb2 = 'inl_bb2'
inl_bb2_b = 'inl_bb2_b'
bb2_time = input.timeframe(title='Timeframe', defval='15', group=g_bb2)
bb2_len = input.int(50, title='Length', group=g_bb2, inline=inl_bb2)  // 350
bb2_stdev = input.float(1.5, title='Deviations', minval=1.0, maxval=4.0, step=0.1, group=g_bb2, inline=inl_bb2) // 1.5
bb2_type = input.string(title='Type', defval='WMA', options=['SMA', 'EMA', 'WMA', 'HULL'], group=g_bb2, inline=inl_bb2)
bb2_show = input.bool(title='BB2', defval=false, inline=inl_bb2_b, group=g_bb2)
i_bb2_basis = input.bool(title='Show Basis', defval=false, group=g_bb2, inline=inl_bb2_b)
bb2_fill = input.bool(title='Show fill', defval=false, group=g_bb2, inline=inl_bb2_b)
i_bb2_bias = input.bool(title='Show Bias', defval=false, group=g_bb2, inline=inl_bb2_b)
bb2_zones = input.bool(title='Zone Change', defval=false, group=g_bb2, inline=inl_bb2_b)
bb2_zones2 = input.bool(title='Show Zones ', defval=false, group=g_bb2, inline=inl_bb2_b)
bb2_bias = 89.7 // input.float(title='Bias 2', defval=89.7, step=0.1, group=g_bb2, inline=inl_bb2)
// 89.70 // 88.85

// BB3 Settings
g_bb3 = 'BB 3'
inl_bb3 = 'inl_bb3'
inl_bb3_b = 'inl_bb3_b'
bb3_time = input.timeframe(title='Timeframe', defval='30', group=g_bb3)
bb3_len = input.int(200, title='Length', group=g_bb3, inline=inl_bb3)  // 350
bb3_stdev = input.float(1.5, title='Deviations', minval=1.0, maxval=4.0, step=0.1, group=g_bb3, inline=inl_bb3) // 1.5
bb3_type = input.string(title='Type', defval='WMA', options=['SMA', 'EMA', 'WMA', 'HULL'], group=g_bb3, inline=inl_bb3)
bb3_show = input.bool(title='BB3', defval=false, inline=inl_bb3_b, group=g_bb3)
i_bb3_basis = input.bool(title='Show Basis', defval=false, group=g_bb3, inline=inl_bb3_b)
bb3_fill = input.bool(title='Show fill', defval=false, group=g_bb3, inline=inl_bb3_b)
i_bb3_bias = input.bool(title='Show Bias', defval=false, group=g_bb3, inline=inl_bb3_b)
bb3_zones = input.bool(title='Zone Change', defval=false, group=g_bb3, inline=inl_bb3_b)
bb3_zones2 = input.bool(title='Show Zones ', defval=false, group=g_bb3, inline=inl_bb3_b)
bb3_bias = 89.7 // input.float(title='Bias 2', defval=89.7, step=0.1, group=g_bb2, inline=inl_bb2)


bb_ma(len1, type) =>
    float v = na
    if type == 'SMA'
        v := ta.sma(close, len1)
        v
    if type == 'EMA'
        v := ta.ema(close, len1)
        v
    if type == 'WMA'
        v := ta.wma(close, len1)
        v
    if type == 'VWMA'
        v := ta.wma(close, len1)
        v
    if type == 'HULL'
        v := ta.wma(2 * ta.wma(close, len1 / 2) - ta.wma(close, len1), math.round(math.sqrt(len1)))
        v
    v

f_bb(len, type, bb_stdev )=>
    bb_basis    = bb_ma(len, type) 
    dev         = bb_stdev * ta.stdev(close, len)
    bb_upper    = bb_basis + dev
    bb_lower    = bb_basis - dev
    bb_spread   = bb_upper - bb_lower
    avgspread   = ta.sma(bb_spread, sqz_length)
    bb_sqz      = bb_spread / avgspread * 100
    bb_a        = angle(bb_basis, 14)
    bb_diff     = bb_spread * 1000
    bb_diff_a   = angle(bb_diff, 3)
    bb_zone     = bb_sqz < 53 ? 0 : bb_sqz < sqz_length ? 1 : bb_sqz < 120 ? 2 : bb_sqz < 160 ? 3 : bb_sqz < 200 ? 4 : bb_sqz < 250 ? 5 : bb_sqz > 250 ? 6 : na
    sqz_color   = bb_zone == 0 ? #0045b3 : bb_zone == 1 ? #ff0062 : bb_zone == 2 ? gray : bb_zone == 3 ? #00c3ff : bb_zone == 4 ? white : bb_zone == 5 ? white : bb_zone == 6 ? yellow : na

    [bb_basis, bb_upper, bb_lower, bb_sqz, bb_a, bb_diff, bb_diff_a, bb_zone, sqz_color]


// ▒▒▒▒▒ BB 1 ▒▒▒▒
[bb1_basis,bb1_upper,bb1_lower, bb1_squeeze, bb1_a, bb1_diff, bb1_diff_a, bb1_zone, bb1_sqz_color] = 
 request.security(syminfo.tickerid, 
 bb1_time, 
 f_bb(bb1_len, bb1_type, bb1_stdev ) )

bb1a_u = angle(bb1_upper, 3)
bb1a_l = angle(bb1_lower, 3)
bb1_zones_color = bb1_sqz_color

if newbar(bb1_time) == 0
    bb1_basis   := bb1_basis[1]
    bb1_upper   := bb1_upper[1]
    bb1_lower   := bb1_lower[1]
    bb1_a       := bb1_a[1]
    bb1_squeeze := bb1_squeeze[1]
    bb1_diff    := bb1_diff[1]
    bb1_diff_a  := bb1_diff_a[1]
    bb1_zone    := bb1_zone[1]
    bb1_sqz_color  := bb1_sqz_color[1]

// Growing or Expanding
var bb1_zch = bb1_sqz_color
var bb1_bch = bb1_zone
var bb1_grow_squeeze = bb1_zone
if ta.change(bb1_zone)
    bb1_grow_squeeze := bb1_zone>bb1_zone[1] ? 1 : -1
    bb1_zch := bb1_sqz_color[1]
    bb1_bch := bb1_zone[1]

// Plots
plot(i_bb1_basis and bb1_show and show_bb ? bb1_basis : na, title='Basis', color=bb1_zones_color)
p1 = plot(bb1_show and show_bb ? bb1_upper : na, 'BB Upper ', color=bb1_zones_color)
p2 = plot(bb1_show and show_bb ? bb1_lower : na, 'BB Lower ', color=bb1_zones_color)
fill(p1, p2, title='BB 1 Fill', color=bb1_fill and bb1_show ? bb1_zones_color : na, transp=90)

// Touching BB
bb1_touch_d = close > bb1_upper ? 1 : na
plotshape(bb1_zones2 and bb1_touch_d == 1 ? 1 : 0, title='Touch Down 1', color=bb1_sqz_color, style=shape.cross, location=location.top)
bb1_touch_u = close < bb1_lower ? 1 : na
plotshape(bb1_zones2 and bb1_touch_u == 1 ? 1 : 0, title='Touch Up 1', color=bb1_sqz_color, style=shape.cross, location=location.bottom)

// Expanding or Compressing
plot(bb1_bch, title='Zone Change 1', style=plot.style_circles, color=bb1_zch)
plot(bb1_grow_squeeze, title='Grow Squeeze 1', style=plot.style_circles)
// BB Bias Candles
//barcolor(i_bb1_bias and bb1_diff_a > bb1_bias ? yellow : na)



// ▒▒▒▒▒ BB 2 ▒▒▒▒
[bb2_basis,bb2_upper,bb2_lower, bb2_squeeze, bb2_a, bb2_diff, bb2_diff_a, bb2_zone, bb2_sqz_color] = 
 request.security(syminfo.tickerid, 
 bb2_time, 
 f_bb(bb2_len, bb2_type, bb2_stdev ) )

bb2a_u = angle(bb2_upper, 3)
bb2a_l = angle(bb2_lower, 3)
bb2_zones_color = bb2_sqz_color

if newbar(bb2_time) == 0
    bb2_basis   := bb2_basis[1]
    bb2_upper   := bb2_upper[1]
    bb2_lower   := bb2_lower[1]
    bb2_a       := bb2_a[1]
    bb2_squeeze := bb2_squeeze[1]
    bb2_diff    := bb2_diff[1]
    bb2_diff_a  := bb2_diff_a[1]
    bb2_zone    := bb2_zone[1]
    bb2_sqz_color  := bb2_sqz_color[1]

// Growing or Expanding
var bb2_zch = bb2_sqz_color
var bb2_bch = bb2_zone
var bb2_grow_squeeze = bb2_zone
if ta.change(bb2_zone)
    bb2_grow_squeeze := bb2_zone>bb2_zone[1] ? 1 : -1
    bb2_zch := bb2_sqz_color[1]
    bb2_bch := bb2_zone[1]

// PLOTS
plot(i_bb2_basis and bb2_show ? bb2_a : na, title='BB2 Angle', color=bb2_a > 0 ? color.new(green, 100) : color.new(red, 100))
plot(i_bb2_basis and bb2_show and show_bb ? bb2_basis : na, title='Basis 2', color=bb2_sqz_color)
p3 = plot(bb2_show and show_bb ? bb2_upper : na, 'BB Upper 2 ', color=bb2_sqz_color)
p4 = plot(bb2_show and show_bb ? bb2_lower : na, 'BB Lower 2 ', color=bb2_sqz_color)
fill(p3, p4, title='BB 2 Fill', color=bb2_fill and bb2_show ? bb2_sqz_color : na, transp=90)

// Touching BB
bb2_touch_d = close > bb2_upper ? 1 : na
plotshape(bb2_zones2 and bb2_touch_d == 1 ? 1 : 0, title='Touch Down 2', color=bb2_sqz_color, style=shape.cross, location=location.top)
bb2_touch_u =  low < bb2_lower ? 1 : na
plotshape(bb2_zones2 and bb2_touch_u == 1 ? 1 : 0, title='Touch Up 2', color=bb2_sqz_color, style=shape.cross, location=location.bottom)

// Compressing Expanding
plot(bb2_bch, title='Zone Change 2', style=plot.style_circles, color=bb2_zch)
plot(bb2_grow_squeeze, title='Grow Squeeze 2', style=plot.style_circles)
//barcolor(i_bb2_bias and bb2_diff_a > bb2_bias ? aqua : na)


// Temp plots
// plot(bb2_smooth, "BB Smooth 2 ",color=bb_zones_color2,linewidth=2)
// plot(bb2_upper, "BB Upper 2 ",color=bb_zones_color2,linewidth=2)
// plot(bb2_lower, "BB Lower 2 ",color=bb_zones_color2,linewidth=2)



// ▒▒▒▒▒ BB 3 ▒▒▒▒
[bb3_basis,bb3_upper,bb3_lower, bb3_squeeze, bb3_a, bb3_diff, bb3_diff_a, bb3_zone, bb3_sqz_color] = 
 request.security(syminfo.tickerid, 
 bb3_time, 
 f_bb(bb3_len, bb3_type, bb3_stdev ) )

bb3a_u = angle(bb3_upper, 3)
bb3a_l = angle(bb3_lower, 3)
bb3_zones_color = bb3_sqz_color

if newbar(bb3_time) == 0
    bb3_basis   := bb3_basis[1]
    bb3_upper   := bb3_upper[1]
    bb3_lower   := bb3_lower[1]
    bb3_a       := bb3_a[1]
    bb3_squeeze := bb3_squeeze[1]
    bb3_diff    := bb3_diff[1]
    bb3_diff_a  := bb3_diff_a[1]
    bb3_zone    := bb3_zone[1]
    bb3_sqz_color  := bb3_sqz_color[1]

// Growing or Expanding
var bb3_zch = bb3_sqz_color
var bb3_bch = bb3_zone
var bb3_grow_squeeze = bb3_zone
if ta.change(bb3_zone)
    bb3_grow_squeeze := bb3_zone>bb3_zone[1] ? 1 : -1
    bb3_zch := bb3_sqz_color[1]
    bb3_bch := bb3_zone[1]



// PLOTS
plot(i_bb3_basis and bb3_show ? bb3_a : na, title='BB3 Angle', color=bb3_a > 0 ? color.new(green, 100) : color.new(red, 100))
plot(i_bb3_basis and bb3_show and show_bb ? bb3_basis : na, title='Basis 2', color=bb3_sqz_color)
p5 = plot(bb3_show and show_bb ? bb3_upper : na, 'BB Upper 3 ', color=bb3_sqz_color)
p6 = plot(bb3_show and show_bb ? bb3_lower : na, 'BB Lower 3 ', color=bb3_sqz_color)
fill(p5, p6, title='BB 2 Fill', color=bb3_fill and bb3_show ? bb3_sqz_color : na, transp=90)

// Touching BB
bb3_touch_d = close > bb3_upper ? 1 : na
plotshape(bb3_zones2 and bb3_touch_d == 1 ? 1 : 0, title='Touch Down 2', color=bb3_sqz_color, style=shape.cross, location=location.top)
bb3_touch_u =  low < bb3_lower ? 1 : na
plotshape(bb3_zones2 and bb3_touch_u == 1 ? 1 : 0, title='Touch Up 2', color=bb3_sqz_color, style=shape.cross, location=location.bottom)

// Expanding Collapsing
plot(bb3_bch, title='Zone Change 3', style=plot.style_circles, color=bb3_zch)
plot(bb3_grow_squeeze, title='Grow Squeeze 3', style=plot.style_circles)

// Over Lap of BB's
up_cond = show_overlap and bb1_upper > bb2_upper and close > bb2_upper and bb1a_l > -4 ? 1 : 0
barcolor(up_cond ? yellow : na)
down_cond = show_overlap and bb1_lower < bb2_lower and close < bb2_lower and bb1a_u < 4 ? 1 : 0
barcolor(down_cond ? aqua : na)


