//@version=5
indicator('EMA - V5', overlay=true)

ma0 = input(5, 'Len 0')
ma1 = input(15, 'Len 1')
ma2 = input(50, 'Len 2')
ma3 = input(100, 'Len 3')
ma4 = input(200, 'Len 4')
show_ma0 = input(title='MA 0', defval=true)
show_ma1 = input(title='MA 1', defval=true)
show_ma2 = input(title='MA 2', defval=true)
show_ma3 = input(title='MA 3', defval=true)
show_ma4 = input(title='MA 4', defval=true)
ma_angle = input(14, 'EMA Angle')
show_curr = input(title='Show Current', defval=false)
ma_candles = input(title='Candles', defval=false)
show_multi = input(title='Show Multi', defval=true)
ma_candles_m = input(title='Candles Multi', defval=true)
ma_time = input.timeframe(title='Timeframe', defval='180')
ma_type = input.string(title="MA Type", defval="ema", options=["ema","dema","tema","wma","vwma","smma","rma","hma","lsma","<PERSON>c<PERSON><PERSON><PERSON>"])
show_avg = input.bool(false, 'Show Average')

red = #ff0062
aqua = #00bcd4
yellow = #FFFF00
orange = #ff9800
green = #4caf50
lime = #50ff00
white = #ffffff
c_hide = color.new(#ffffff, 100)
blue = #0053ff
magenta = #ff0062
purple = #0045b3
gray = #707070
black = #000000

angle(_src, len) =>
    rad2degree = 180 / 3.14159265359  //pi 
    ang = rad2degree * math.atan((_src[0] - _src[1]) / ta.atr(len))
    ang

ma_graph(len) =>
    src = close
    ma =0.0
    if ma_type == 'ema' // Exponential
        ma := ta.ema(src,len)
    if ma_type=="dema" // Double Exponential
        e = ta.ema(src, len)
        ma := 2 * e - ta.ema(e, len)
    if ma_type == 'tema' // Triple Exponential
        t_ma1 = ta.ema(src, len)
        t_ma2 = ta.ema(t_ma1, len)
        t_ma3 = ta.ema(t_ma2, len)
        ma := 3 * (t_ma1 - t_ma2) + t_ma3
    if ma_type == 'wma' // Weighted
        ma := ta.wma(src,len)
    if ma_type == 'vwma' // Volume Weighted
        ma := ta.vwma(src,len)
    if ma_type=="smma" // Smoothed
        w = ta.wma(src, len)
        ma := na(w[1]) ? ta.sma(src, len) : (w[1] * (len - 1) + src) / len
    if ma_type == "rma"
        ma := ta.rma(src, len)
    if ma_type == 'hma' // Hull
        ma := ta.wma(2*ta.wma(src, len/2)-ta.wma(src, len), math.floor(math.sqrt(len) ))
    if ma_type=="lsma" // Least Squares
        ma := ta.linreg(src, len, 0)
    if ma_type=="McGinley"
        mg = 0.0
        mg := na(mg[1]) ? ta.ema(src, len) : mg[1] + (src - mg[1]) / (len * math.pow(src/mg[1], 4))
        ma :=mg

    ma_angle = angle(ma,3)
    [ma,ma_angle]


[e0,e0_a] = ma_graph(ma0)
[e1,e1_a] = ma_graph(ma1)
[e2,e2_a] = ma_graph(ma2)
[e3,e3_a] = ma_graph(ma3)
[e200,e200_a] = ma_graph(ma4)
ea = math.abs(e200_a)
e_zone = ea < 1 ? 0 : ea < 2 ? 1 : ea < 3 ? 2 : ea < 4 ? 3 : ea < 5 ? 4 : ea < 6 ? 5 : ea > 6 ? 6 : na
e_color = e_zone == 0 ? red : e_zone == 1 ? orange : e_zone == 2 ? yellow : e_zone == 3 ? gray : e_zone == 4 ? blue : e_zone == 5 ? lime : e_zone == 6 ? white : na

plot(show_ma0 and e0 and show_curr ? e0 : na, color=color.new(color.white, 0), title='MA 0')
plot(show_ma1 and e1 and show_curr ? e1 : na, color=color.new(color.aqua, 0), title='MA 1')
plot(show_ma2 and e2 and show_curr ? e2 : na, color=color.new(#0088FA, 0), title='MA 2')
plot(show_ma3 and e3 and show_curr ? e3 : na, color=color.new(#FA00D0, 0), title='MA 3')
e_n = plot(show_ma4 and show_curr ? e200 : na, color=e_color, title='200 EMA')
plot(e200_a, color=color.new(e_color, 100), title='Angel ema 200', style=plot.style_circles)
plot(e1_a, color=e1_a > 0 ? color.new(#FF7000, 100) : color.new(#814dff, 100), title='Angel ema 100', style=plot.style_circles)
barcolor(show_curr and ma_candles ? e_color : na)

// Multi
e_0_m = request.security(syminfo.tickerid, ma_time, ta.ema(close, ma0))
e_1_m = request.security(syminfo.tickerid, ma_time, ta.ema(close, ma1))
e_2_m = request.security(syminfo.tickerid, ma_time, ta.ema(close, ma2))
e_3_m = request.security(syminfo.tickerid, ma_time, ta.ema(close, ma3))
e200_m = request.security(syminfo.tickerid, ma_time, ta.ema(close, ma4))
e200_a_m = request.security(syminfo.tickerid, ma_time, angle(e200_m, ma_angle))
ea_m = request.security(syminfo.tickerid, ma_time, math.abs(e200_a_m))
e_zone_m = ea_m < 0.5 ? 0 : ea_m < 2 ? 1 : ea_m < 3 ? 2 : ea_m < 4 ? 3 : ea_m < 5 ? 4 : ea_m < 6 ? 5 : ea_m > 6 ? 6 : na

e_color_m = e_zone_m == 0 ? red : e_zone_m == 1 ? orange : e_zone_m == 2 ? yellow : e_zone_m == 3 ? gray : e_zone_m == 4 ? blue : e_zone_m == 5 ? lime : e_zone_m == 6 ? white : na

// Step Up or Down
var ema_step = 0.0
ema_step := ta.change(e_1_m) and e_1_m < e_1_m[1] ? -1 : ta.change(e_1_m) and e_1_m > e_1_m[1] ? 1 : ema_step
plot(ema_step, 'Stepping', color = ema_step == -1 ? color.new(red,100) :  color.new(green,100)  )
// Average
// averageMA = math.avg(e_2_m, e_3_m, e200_m)
// plot(show_avg ? averageMA : na, title="Average MA", color=red)

plot(show_ma0 and e0 and show_multi ? e_0_m : na, color=color.new(color.white, 0), title='EMA 0')
plot(show_ma1 and e1 and show_multi ? e_1_m : na, color=color.new(color.aqua, 0), title='EMA 1')
plot(show_ma2 and e2 and show_multi ? e_2_m : na, color=color.new(#0088FA, 0), title='20 EMA')
plot(show_ma3 and e3 and show_multi ? e_3_m : na, color=color.new(#FA00D0, 0), title='50 EMA')
e_m = plot(show_ma4 and show_multi ? e200_m : na, color=e_color_m, title='Multi 200')
barcolor(show_multi and ma_candles_m ? e_color_m : na)

e1_cond = e_1_m > e_2_m and e_1_m[1] < e_2_m[1]
//plotshape(e1_cond, 'e1 cross up', color=color.new(#00ff00, 0), style=shape.circle, location=location.bottom)

// Diff
e_diff = (e200 - e200_m) * 100
plot(show_multi ? e_diff : na, title='Diff', color=color.new(blue, 100))
fill(e_n, e_m, title='EMA Diff', color=e200_a > 0 ? color.new(red, 90) : color.new(green, 90), transp=90)


