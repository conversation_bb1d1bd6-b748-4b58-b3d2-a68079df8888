//@version=4
study(title = "Trading Bot", shorttitle = "Trading Bot", overlay=true, format = format.price, precision = 4)


show_entry = input(title="Show Entry", type=input.bool, defval=true)
show_atr = input(title = "Show Stop Loss / ATR", type = input.bool, defval = false)
show_BB = input(title="Show BB", type=input.bool, defval=true)
use_trend = input(title="Trend Filter", type=input.bool, defval=true)
use_counter = input(title="Use Counter Trend", type=input.bool, defval=true)
//use_rsi_candles = input(title="Use RSI Candles", type=input.bool, defval=true)
use_special = input(title="Use Special Candles", type=input.bool, defval=true)
//use_high_greater_fast = input(title="Higher than EMA Filter", type=input.bool, defval=true)
use_ema_cross = input(title="Use EMA Cross", type=input.bool, defval=true)
//use_stc = input(title="Use STC Filter", type=input.bool, defval=true)
use_cb_stc = input(title="Filter Counter Buy STC", type=input.bool, defval=true)
//use_macd_cross = input(title="Use MACD cross", type=input.bool, defval=true)
//use_bsm = input(title="Use BSM", type=input.bool, defval=false)
use_doji = input(title="Filter Doji's", type=input.bool, defval=true)
use_hammer = input(title="Filter Hammer's", type=input.bool, defval=true)
use_be = input(title="Filter Bearish Engulfing", type=input.bool, defval=true)
use_ema_bands = input(title="EMA 200 Filter", type=input.bool, defval=true)
use_ssl_bands = input(title="SSL Bands Filter", type=input.bool, defval=true)
use_deadzone = input(title="Deadzone", type=input.bool, defval=true)
dz_time = input(title="DZ Timeframe", type=input.session, defval= "1715-1830")


// Colors
sell_color = #ff0062
buy_color = #00c3ff
red = #dd1c1c
green = #55aa1a
blue = #0000ff
white = #ffffff
gray  = #707070

angle(_src) =>
    rad2degree=180/3.14159265359  //pi 
    ang=rad2degree*atan((_src[0] - _src[1])/atr(4))

Session(sess) => na(time(timeframe.period, sess)) == false

// Change
perc_change = abs( (1 - (close[1] / close)) * 10000 )
plot(perc_change,title="Change",style=plot.style_circles)


// === Fast EMA ===
// ==================================================
fast_ema = ema(close,3)
//plot(fast_ema, title="Fast EMA", color=color.yellow, transp=15)


// ===  EMA 200 ===
// ==================================================
len_200 = input(200, minval=1, title="EMA 200")
ema_200 = ema(close, len_200)
ema_200_dev = 0.285 * stdev(close, len_200)
ema_200_upper = ema_200 + ema_200_dev
ema_200_lower = ema_200 - ema_200_dev
plot(ema_200, title="EMA 200", color=#fff176, linewidth=3)
// EMA 200 bands
e200_up = plot(use_ema_bands ? ema_200_upper: na, title="EMA 200 Upper", color=#ffe676, linewidth=1,transp=90)
e200_down = plot(use_ema_bands ? ema_200_lower : na, title="EMA 200 Lower", color=#ffe676, linewidth=1,transp=90)
fill(e200_up, e200_down, color=#fff176, transp=80)



// ===  Moving Average EMA SMA ===
// ==================================================
emaplot = true //input (true, title="Show EMA on chart")
len = 8 //input(8, minval=1, title="ema Length") // default 8
smaplot = true //input (true, title="Show SMA on chart")
len2 = 50 //input(50, minval=1, title="sma Length")
src = close
ema_fast = ema(src, len)
up = ema_fast > ema_fast[1]
down = ema_fast < ema_fast[1]
fast_ema_angle = angle(ema_fast)
// Slow SMA
sma_slow = sma(close, len2)
up2 = sma_slow > sma_slow[1]
down2 = sma_slow < sma_slow[1]
mycolor = up ?  green : down ? red : #00c3ff
sma_slow_color = up2 ? green : down2 ? red : blue
plot(ema_fast and emaplot ? ema_fast :na, title="EMA", color=mycolor, linewidth=3)
plot(sma_slow and smaplot ? sma_slow :na , title="SMA", color=sma_slow_color, linewidth=3, transp=15)
//plot(fast_ema_angle, title="Fast EMA angle", style=plot.style_circles,color=mycolor)
plot(angle(sma_slow), title="Slow EMA angle", style=plot.style_circles,color=mycolor)



// === Schaff Trend Cycle (STC) ===
// ==================================================
stc_signal = 12 // input(12,"Length")
stc_fast = 26 //input(26,"FastLength")
stc_slow = 50 //input(50,"SlowLength")
stc_low = 25
stc_high = 88
stc_line = stc_high - stc_low
stc_macd(BBB, stc_fast, stc_slow) =>
    stc_fastMA = ema(BBB, stc_fast)
    stc_slowMA = ema(BBB, stc_slow)
    stc_macd = stc_fastMA - stc_slowMA
    stc_macd
    
AAAAA(stc_signal, stc_fast, stc_slow) => 
    AAA=input(0.203,"STC Sensitivity") // 0.25
    var CCCCC = 0.0
    var DDD = 0.0
    var DDDDDD = 0.0
    var EEEEE = 0.0
    tmp_macd = stc_macd(close,stc_fast,stc_slow)     
    lowest = lowest(tmp_macd, stc_signal)
    highest = highest(tmp_macd, stc_signal) - lowest    
    CCCCC := (highest > 0 ? ((tmp_macd - lowest) / highest) * 100 : nz(CCCCC[1])) 
    DDD := (na(DDD[1]) ? CCCCC : DDD[1] + (AAA * (CCCCC - DDD[1]))) 
    DDDD = lowest(DDD, stc_signal) 
    DDDDD = highest(DDD, stc_signal) - DDDD     
    DDDDDD := (DDDDD > 0 ? ((DDD - DDDD) / DDDDD) * 100 : nz(DDDDDD[1])) 
    EEEEE := (na(EEEEE[1]) ? DDDDDD : EEEEE[1] + (AAA * (DDDDDD - EEEEE[1])))
    EEEEE

stc = AAAAA(stc_signal,stc_fast,stc_slow)
stc_color = stc > stc[1] ? buy_color : sell_color
plot(stc,color=stc_color, title="STC",linewidth=2,style=plot.style_circles)
plot(stc_line,color=stc_color, title="STC middle line",style=plot.style_circles)


// === SSL ===
// ==================================================
ssl_len = 55
SSL = wma(2 * wma(close, ssl_len / 2) - wma(close, ssl_len), round(sqrt(ssl_len))) 
ssl_multy = 0.2
ssl_range = tr
rangema = ema(ssl_range, ssl_len)
upperk = SSL + rangema * ssl_multy
lowerk = SSL - rangema * ssl_multy
ssl_angle = angle(SSL)
ssl_color_buy = #00c3ff
ssl_color_sell = #ff0062
ssl_color = close > upperk ? ssl_color_buy : close < lowerk ? ssl_color_sell : color.gray
plot(SSL, title="SSL", linewidth=2, color=ssl_color)
plot(ssl_angle,title='SSL Angle', color=ssl_color, linewidth=0,transp=0, style=plot.style_circles)
// SSL bands
ssl_dev = 0.08 * stdev(close, ssl_len)
ssl_upper = SSL + ssl_dev
ssl_lower = SSL - ssl_dev
ssl_up = plot(use_ssl_bands ? ssl_upper: na, title="EMA 200 Upper", color=#ffe676, linewidth=1,transp=90)
ssl_down = plot(use_ssl_bands ? ssl_lower : na, title="EMA 200 Lower", color=#ffe676, linewidth=1,transp=90)
fill(ssl_up, ssl_down, color=ssl_color, transp=80)



// === Bollinger Bands ===
// ==================================================
bb_use_ema = input(false, title="Use EMA for Bollinger Band")
bb_length = input(25, minval=1, title="Bollinger Length")
bb_mult = input(2.0, title="Base Multiplier", minval=0.5, maxval=10)
sqz_length = 100
sqz_threshold = input(65, minval=0, title="Squeeze Threshold") // old 54

// EMA inputs
fast_ma_len = input(9, title="Fast EMA length", minval=2)

// Breakout Indicator Inputs
ema_1 = ema(close, bb_length)
sma_1 = sma(close, bb_length)
bb_basis = bb_use_ema ? ema_1 : sma_1
//fast_ma = ema(bb_source, fast_ma_len)
fast_ma = sma(close, fast_ma_len)
dev = stdev(close, bb_length)
bb_dev = bb_mult * dev
// Bands
bb_inner_upper = bb_basis + bb_dev
bb_inner_lower = bb_basis - bb_dev
// Calculate BB spread and average spread
bb_spread = bb_inner_upper - bb_inner_lower
avgspread = sma(bb_spread, sqz_length)
// Calculate BB relative %width for Squeeze indication
bb_squeeze = 0.00
bb_squeeze := bb_spread / avgspread * 100
// Calculate Upper and Lower band painting offsets based on 50% of atr.
bb_offset = atr(14) * 0.5
bb_upper = bb_inner_upper + bb_offset
bb_sqz_lower = bb_inner_lower - bb_offset
basis_angle = angle(bb_basis)
plot(bb_squeeze[0], title="BB Squeeze", color=bb_squeeze < sqz_threshold ? color.blue : color.gray, transp=10, linewidth=0,style=plot.style_circles)
plot(bb_basis, title="Basis Line", color=color.red, transp=10, linewidth=2)
ubi = plot(show_BB ? bb_inner_upper: na, title="Upper Band Inner", color=color.blue, transp=10, linewidth=1)
usqzi = plot(show_BB ? bb_upper: na, "Upper band", transp=0, linewidth=1,color=color.red)
lbi = plot(show_BB ? bb_inner_lower:na, title="Lower Band Inner", color=color.blue, transp=10, linewidth=1)
lsqzi = plot(show_BB ? bb_sqz_lower: na, "Lower Band", transp=0, linewidth=1,color=color.red)
fill(ubi, usqzi, color=bb_squeeze > sqz_threshold ? color.black : color.blue, transp=20)
fill(lbi, lsqzi, color=bb_squeeze > sqz_threshold ? color.black : color.blue, transp=20)
plot( abs(SSL - bb_basis), title="SSL and Basis Diff", color=color.red, transp=10, linewidth=2)


// ===  ATR ===
// ==================================================
atrlen = input(14, "ATR Period")
atr_mult = input(1.1, "ATR Mult", step = 0.1)
resis_mult = input(0.205, "ATR Resistance", step = 0.1)
atr_slen = wma(tr(true), atrlen)
atr_upper = atr_slen * atr_mult + close
atr_lower = close - atr_slen * atr_mult
atr_upper_band = atr_slen * resis_mult + close
atr_lower_band = close - atr_slen * resis_mult
// ATR Stop Loss
plot(show_atr ? atr_upper : na, "+ATR Upper", color=#ffffff, transp=80)
plot(show_atr ? atr_lower : na, "-ATR Lower", color=#ffffff, transp=80)
// ATR Bands S/R
// fill_upper = plot(atr_upper_band, "+ATR Resistance", color=#ffffff, transp=90)
// fill_lower = plot(atr_lower_band, "-ATR Resistance", color=#ffffff, transp=90)
// fill(fill_upper, fill_lower, color=gray, transp=80)
// atr_angle = angle(atr_lower_band)
// plot(atr_angle, "-ATR Angle", color=#ffffff, transp=90,style=plot.style_circles)


// === RSI Chart Bars ===
// ==================================================
var rsi_color = #000000
rsi_len = 14 //input(14, minval=1, title="Length")
rsi_up = rma(max(change(close), 0), rsi_len)
rsi_down = rma(-min(change(close), 0), rsi_len)
rsi = rsi_down == 0 ? 100 : rsi_up == 0 ? 0 : 100 - (100 / (1 + rsi_up / rsi_down))
isup() => rsi > 70
isdown() => rsi < 30
// plot(rsi,title="RSI",color=color.blue)
rsi_color := isup() ? buy_color : isdown() ? sell_color : na
barcolor(rsi_color, title="Rsi Candles")


// === WaveTrend with Crosses [LazyBear]  ===
// ==================================================
n1 = input(8, "Channel Length")
n2 = input(28, "Average Length")
obLevel1 = input(60, "Over Bought Level 1")
obLevel2 = input(52, "Over Bought Level 2")
osLevel1 = input(-60, "Over Sold Level 1")
osLevel2 = input(-53, "Over Sold Level 2")
 
ap = hlc3 
esa = ema(ap, n1)
d = ema(abs(ap - esa), n1)
ci = (ap - esa) / (0.015 * d)
tci = wma(ci, n2)
 
wt1 = tci
wt2 = sma(wt1,4)


// === Candlesticks ===
// ==================================================

// Doji
candle_transp = 20
DojiSize = input(0.1, minval=0.01, title="Doji size")
doji=(abs(open - close) <= (high - low) * DojiSize)
plotchar(doji, title="Doji", text='Doji', color=color.white,transp=80)
// Hammer
hammer =(((high - low)>3*(open -close)) and  ((close - low)/(.001 + high - low) > 0.6) and ((open - low)/(.001 + high - low) > 0.6))
//plotshape(hammer, title= "Hammer", location=location.belowbar, color=white,transp=80,style=shape.diamond, text="H")
// Inverted Hammer
inverted_hammer =(((high - low)>3*(open -close)) and  ((high - close)/(.001 + high - low) > 0.6) and ((high - open)/(.001 + high - low) > 0.6))
//plotshape(inverted_hammer, title= "Inverted Hammer", location=location.belowbar, color=white,transp=80,style=shape.diamond, text="IH")
// Bearish Engulfing
bear_e=(close[1] > open[1] and open > close and open >= close[1] and open[1] >= close and open - close > close[1] - open[1] )
plotshape(use_be ? bear_e : na,  title= "Bearish Engulfing", color=sell_color, style=shape.arrowdown, text="BE",transp=50)
// Bearish Harami
bear_h=(close[1] > open[1] and open > close and open <= close[1] and open[1] <= close and open - close < close[1] - open[1] )
//plotshape(bear_h, title= "Bearish Harami",  color=red, style=shape.arrowdown, text="BH",transp=80)
// Bullish Harami
bull_h=(open[1] > close[1] and close > open and close <= open[1] and close[1] <= open and close - open < open[1] - close[1] )
//plotshape(bull_h,  title= "Bullish Harami", location=location.belowbar, color=buy_color, style=shape.arrowup, text="BH",transp=80)
// Bullish Engulfing
bull_e=(open[1] > close[1] and close > open and close >= open[1] and close[1] >= open and close - open > open[1] - close[1] )
plotshape(bull_e, title= "Bullish Engulfing", location=location.belowbar, color=buy_color, style=shape.arrowup, text="BE",transp=80)


entry_signal() =>
	dir = 0
	trading = 0

	// Type of candle - Long / Short
	candle = close > open ? 1 : 0
	Deadzone = Session(dz_time) ? 1 : 0

	// === Uptrend ===
	// ==================================================

	// Sell signal
	if (candle == 1 and sma_slow > ema_200 and close > bb_inner_upper)
		dir := -1
		trading := 1

		// SMA inside EMA 200 bands
		if sma_slow < ema_200_upper
			dir := na
			trading := 0

		// EMA 200 outside BB bands
		if ema_200_upper < bb_inner_lower
			dir := na
			trading := 0

		// SSL angle

	// Sell signal Special
	if (use_special and candle == 1 and sma_slow > ema_200)
	 and (ema_fast < SSL and close > ssl_upper and open > SSL)
		dir := -1
		trading := 1

	// Counter Trade - Buy
	if (sma_slow > ema_200 and SSL > ema_fast and down) and 
	 ( (bull_e == 1 and close < ema_fast) or 
	  (doji==1 and perc_change <= 2 and SSL > bb_basis and close < ema_fast ) ) // 
		dir := 1
		trading := 1

	// if (sma_slow > ema_200 and SSL > ema_fast and down) and 
	//  ( (open < ema_fast and close < ema_fast and low > sma_slow and perc_change <= 4) 
	//  or (bull_e == 1 and close < ema_fast) )
	// 	dir := 1
	// 	trading := 1



	// === Downtrend ===
	// ==================================================

	// Buy signal
	if (candle == 0 and sma_slow < ema_200 and close < bb_inner_lower)
		dir := 1
		trading := 1

		// EMA 200 outside BB bands
		if bb_inner_upper > ema_200_lower
			dir := na
			trading := 0

		// BB Squeeze areas
		if (bb_squeeze < sqz_threshold)
			
			// SMA is above bb bands
			if sma_slow > bb_inner_upper
				dir := na
				trading := 0

			if SSL > ema_200_lower
				dir := na
				trading := 0

			// if SSL < ema_fast and close < bb_inner_lower
			// 	dir := na
			// 	trading := 0

			// // RSI Candles
			// if fast_ema < SSL and isdown()
			// 	dir := na
			// 	trading := 0

	// Special Bearish Engulfing - Bottom of Strong downtrend 
	if (use_special and bear_e == 1 and sma_slow < ema_200 and open < ema_fast
	 and SSL < ema_fast and ssl_color == ssl_color_buy and ssl_angle < 0 )
		dir := 1
		trading := 1

	// EMA 200 Filter lower bands
	// if use_ema_bands and close < bb_inner_lower and close < ema_200 and close > ema_200_lower
	// 	dir := na
	// 	trading := 0

	// EMA 200 Filter upper bands
	// if use_ema_bands and close > bb_inner_upper and close > ema_200 and close < ema_200_upper
	// 	dir := na
	// 	trading := 0

	// Wave Trend red line above
	// if wt2 > osLevel1 and perc_change < 11
	// 	dir := na
	// 	trading := 0
		
	// === Counter Downtrend - Sell ===
	// ==================================================

	// Below Basis line and fast ema above SSL
	// if (SSL < bb_basis and ema_fast < bb_basis and ema_fast > SSL)
	// 	if (use_special and be == 1 and up and perc_change < 7 )
	// 		dir := -1
	// 		trading := 1

	// 	if (use_special and bear_h == 1 and up and perc_change < 6 )
	// 		dir := -1
	// 		trading := 1


		
	// Slow SMA is Bearish 
	// if (use_counter and up and candle == 1 and sma_slow_color == red)
	// 	dir := -1
	// 	trading := 1

	// 	// EMA Cross
	// 	if use_ema_cross and (ema_fast < bb_basis) 
	// 		dir := na
	// 		trading := 0
			
	// 	// Not inside BB bands
	// 	if close < bb_inner_upper 
	// 		dir := na

	// Downtrend Counter - Slow SMA is Bullish 
	// if (use_counter and up and candle == 1 and sma_slow_color == green and sma_slow < ema_200)
	// 	dir := -1
	// 	trading := 1

	// 	// EMA 200 Filter RSI candles with Wave Trend
	// 	if use_ema_bands and close < ema_200_upper and isup() and wt2 < obLevel2
	// 		dir := na
	// 		trading := 0

	// 	// EMA 200 Filter
	// 	if use_ema_bands and close < ema_200_upper and not isup()
	// 		dir := na
	// 		trading := 0

	// 	// EMA Cross
	// 	if ema_fast < bb_basis 
	// 		dir := na
	// 		trading := 0

	// 	// Not inside BB bands
	// 	if close < bb_inner_upper 
	// 		dir := na
	// 		trading := 0

	// 	// SSL below slow SMA
	// 	if SSL < sma_slow
	// 		dir := na
	// 		trading := 0

	// 	// STC filter counter normal candles
	// 	if stc < stc_low and not isup()
	// 		dir := na
	// 		trading := 0
			
	// 	// STC filter counter RSI candles
	// 	if stc < stc_line and isup()
	// 		dir := na
	// 		trading := 0

	// 	// Doji and RSI candle
	// 	if use_special and doji == 1 and isup()
	// 		dir := na
	// 		trading := 0

	// 	// Doji
	// 	if use_doji and doji == 1
	// 		dir := na
	// 		trading := 0

	// 	// bullish Engulfing
	// 	if be and stc > stc_line
	// 		dir := na
	// 		trading := 0

	


	// Sell - Downtrend counter - Special
	if (SSL < bb_basis and ema_fast < bb_basis and ema_fast > SSL)
		if (use_special and bull_e == 1 and up and perc_change < 7 )
			dir := -1
			trading := 1

	
	if (use_special and up and high > bb_basis and high > SSL 
	 and SSL > open and ssl_angle < 0
	 and ssl_angle < -3 and close < bb_basis and perc_change < 9 )
		dir := -1
		trading := 1
		

	// Don't trade during high spread periods
	if use_deadzone and Deadzone == 1
		dir := na
		trading := 0

	[dir, trading]

[trade_dir, trading] = entry_signal() 


// Buy / Sell indicators
bgcolor(Session(dz_time) ? #b71c1c : na, title="Deadzone",transp=85)
plotshape(trade_dir > 0 and show_entry ? 1 : na, title="Entry Buy", color=trade_dir > 0 ? color.orange : buy_color, location = location.belowbar, style=shape.labelup, text="B", textcolor=color.white, size=size.small)
plotshape(trade_dir < 0 and show_entry ? -1 : na, title="Entry Sell", color= trade_dir < 0 ? color.orange : sell_color, location = location.abovebar, style=shape.labeldown, text="S", textcolor=color.white, size=size.small)
plot(trading ,title="Trading",color= trading ? buy_color : color.gray)