//@version=4
study("SSL- Multi ", overlay=true)

show_fill = input(title="Show Fill", type=input.bool, defval=true)
show_conv = input(title="Show Conv", type=input.bool, defval=true)
conv_amount = input(defval=7, title="Convergence Amount", type=input.float, step=1 )
line_input = input(1, title="Line width", type=input.integer )
c_type = input(title="Type", defval="NZD", options=
 ["NZD", "JPY" ])

useCurrentRes = input(true, title="Use Current Chart Resolution?")
resCustom = input(title="Use Different Timeframe? Uncheck Box Above", type=input.resolution, defval="10")

red = #ff0062
orange = #ff9800
yellow = #FFFF00
green = #4caf50
lime = #00E676
aqua = #00bcd4
blue = #42a5f5
dark_blue = #2962ff
violet = #814dff
white = #ffffff

res = useCurrentRes ? timeframe.period : resCustom

angle(_src, len) =>
    rad2degree=180/3.14159265359  //pi 
    ang=rad2degree*atan((_src[0] - _src[1])/atr(len))

angle_input = input(title="Angle Amount",type=input.integer, defval=14)

e200 = ema(close, 230)
e200_a = angle(e200,14)


// ===  SSL ===
// ==================================================
show_ema = input(title="EMA", type=input.bool, defval=false)
show_s1 = input(title="S1", type=input.bool, defval=true)
show_s2 = input(title="S2", type=input.bool, defval=false)
show_s3 = input(title="S3", type=input.bool, defval=false)
show_s4 = input(title="S4", type=input.bool, defval=false)
show_s5 = input(title="S5", type=input.bool, defval=true)
show_s6 = input(title="S6", type=input.bool, defval=true)
show_s7 = input(title="S7", type=input.bool, defval=true)
show_s8 = input(title="S8", type=input.bool, defval=true)

ssl_len1 = input(20, minval=1,title="SSL 1") // 45
ssl_len2 = input(38, minval=1,title="SSL 2") //75
ssl_len3 = input(8, minval=1,title="SSL 3") //
ssl_len3_b = input(15, minval=1,title="SSL 3 b") // 8 7
ssl_len4 = input(75, minval=1,title="SSL 4") // 75 100
ssl_len5 = input(100, minval=1,title="SSL 5") // 150
ssl_len6 = input(200, minval=1,title="SSL 6") // 150
ssl_len7 = input(300, minval=1,title="SSL 7") // 150
ssl_len8 = input(500, minval=1,title="SSL 8") // 150

trans = input(50,title="Transparency")

s_lines(len) =>
    result = wma(2*wma(close, len/2)-wma(close, len), round(sqrt(len)))

//s1 = wma(2*wma(close, ssl_len1/2)-wma(close, ssl_len1), round(sqrt(ssl_len1)))
s1 = s_lines(ssl_len1)
s2 = s_lines(ssl_len2)
s3 = s_lines(ssl_len3)
s3b = s_lines(ssl_len3_b)
s4 = s_lines(ssl_len4)
s5 = s_lines(ssl_len5)
s6 = s_lines(ssl_len6)
s7 = s_lines(ssl_len7)
s8 = s_lines(ssl_len8)
// Angles
s1_a = angle(s1,angle_input)
s3_a = angle(s3,angle_input)
s4_a = angle(s4,angle_input)
s5_a = angle(s5,angle_input)
s6_a = angle(s6,angle_input)
s7_a = angle(s7,angle_input)
s8_a = angle(s8,angle_input)


// Diff
boost = c_type=="JPY"?100 : 1000
s1_s3_diff = (s1 - s3) * boost
s1_s4_diff = (s1 - s4) * boost
s4_s5_diff = (s4 - s5) * boost
s5_s6_diff = (s5 - s6) * boost
s5_s6_conv = show_conv and s5_s6_diff<conv_amount and s5_s6_diff>(conv_amount * -1) ? true : false

s5_multi = security(syminfo.tickerid, res, s5)
s6_multi = security(syminfo.tickerid, res, s6)
s5_a_multi = security(syminfo.tickerid, res, s5_a)
s6_a_multi = security(syminfo.tickerid, res, s6_a)
s5_s6_diff_multi = security(syminfo.tickerid, res, s5_s6_diff)
s5_s6_conv_multi = security(syminfo.tickerid, res, s5_s6_conv)

s8_multi = security(syminfo.tickerid, res, s8)
s8_a_multi = security(syminfo.tickerid, res, s8_a)

s1_f = plot(show_s1?s1:na, color=white , title="SSL 1", linewidth=line_input)
s3_f = plot(show_s3?s3:na, color=aqua , title="SSL 3")
s4_f = plot(show_s4?s4:na, color=s4_a>0?yellow:orange , title="SSL 4",linewidth=line_input)

s5_f_multi =plot(show_s5?s5_multi:na, color=s5_a_multi<0?red:orange , title="SSL 5 Multi",linewidth=line_input)
s6_f_multi =plot(show_s6?s6_multi:na, color=s6_a_multi>0?#2962ff:aqua , title="SSL 6 Multi",linewidth=line_input)
s8_f_multi =plot(show_s8?s8_multi:na, color=s8_a_multi>0?#2962ff:aqua , title="SSL 8 Multi",linewidth=line_input)


s7_f =plot(show_s7?s7:na, color=s7_a<0?green:red , title="SSL 7",linewidth=line_input)
s8_f =plot(show_s8?s8:na, color=s8_a<0?dark_blue:aqua , title="SSL 8",linewidth=line_input)


fill(s5_f_multi,s6_f_multi,title="S5/S6 Fill", color=show_fill and s5_s6_diff_multi<1?color.new(green,80): show_fill ? color.new(red,80):na)

//s5 s6 Conv
fill(s5_f_multi,s6_f_multi,title="S5/S6 Conv", color=s5_s6_conv_multi and s5_multi>s6_multi?color.new(red,70): s5_s6_conv_multi and s5_multi<s6_multi?color.new(green,70) : na)

plot(s5_a_multi,title="S5 Angle",color=s5_a_multi>0?color.new(orange, 100):color.new(red,70), style=plot.style_circles)
plot(s5_s6_diff_multi,title="S5/S6 Conv",color=s5_a_multi>0?color.new(orange, 100):color.new(red,70), style=plot.style_circles)

