//@version=4
study(title = "Kijun sen on Bollinger Bands", shorttitle="Kijun+BB")

red = #ff0062
aqua = #00bcd4
yellow = #FFFF00
orange = #ff9800
green = #4caf50
white = #ffffff
blue = #0000ff

angle(_src, len) =>
    rad2degree=180/3.14159265359  //pi 
    ang=rad2degree*atan((_src[0] - _src[1])/atr(len))


// ===  EMA's ===
// ==================================================
ema_200 = ema(close, 200)
ema_len_f = input(5, minval=1, title="EMA Length") //6
ema_fast = ema(close, ema_len_f)

// ===  ATR ===
// ==================================================
atrlen = 14 //input(14, "ATR Period")
atr_mult = 1.25//input(1.25, "ATR Mult", step = 0.1) // 1.15
atr_slen = wma(tr(true), atrlen)
atr_upper = atr_slen * atr_mult + close
atr_lower = close - atr_slen * atr_mult

// Kijun
middleDonchian(Length) =>
    lower = lowest(Length)
    upper = highest(Length)
    avg(upper, lower)

basePeriods = input(14, minval=1) //26
//displacement = input(26, minval=1)
Kijun =  middleDonchian(basePeriods)
xChikou = close
xPrice = close

// Hull MA
ssl_len = input(60, minval=1,title="SSL Length") //75
SSL = wma(2*wma(close, ssl_len/2)-wma(close, ssl_len), round(sqrt(ssl_len)))
ssl_angle = angle(SSL,3)
// BB
BB_length = input(45, minval=1, maxval=50) // 23
BB_stdDev = input(2, minval=2.0, maxval=3)

bb_s = Kijun
basis = sma(bb_s, BB_length)
dev = BB_stdDev * stdev(bb_s, BB_length)
upper = basis + dev
lower = basis - dev

plot(ema_fast, color=color.lime, title="EMA Fast")
plot(Kijun, color=red, title="Kijun")
//plot(xChikou[26], color= aqua , title="Chikou")
//plot(xChikou, color= red , title="xChikou", offset = -displacement)
plot(xPrice, color= yellow , title="Price")
plot(SSL, color= white , title="SSL")
plot(ssl_angle, color= white , title="SSL angle",style=plot.style_circles)

plot(basis, title="Basis", color=orange)
p1 = plot(upper,title="BB upper", color=blue)
p2 = plot(lower,title="BB lower", color=blue)
fill(p1,p2, blue)
plot(ema_200, "EMA 200", color=yellow)
plot(atr_upper, "+ATR Upper", color=#ffffff, transp=95)
plot(atr_lower, "-ATR Lower", color=#ffffff, transp=95)