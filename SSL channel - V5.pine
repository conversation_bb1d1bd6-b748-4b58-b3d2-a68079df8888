//@version=5
indicator(title='SSL channel - V5', overlay=true )

c1_a = input(title='C1 Color 1', defval=#00c3ff)
c1_b = input(title='C1 Color 2', defval=#ff0062)

c2_a = input(title='C2 Color 1', defval=#00E676)
c2_b = input(title='C2 Color 2', defval=#FF5252)

show_channel_fill = input(title='Show Fill', defval=false)
channel_line_width = input(title='Show Fill', defval=1)
show_only_lines = input(title='Only show lines', defval=false)
use_angle_color = input(title='Color based on angle', defval=false)
f_trans = input(80, title='Fill Transparency')

red = #ff0062
aqua = #00bcd4
yellow = #FFFF00
orange = #ff9800
green = #4caf50
lime = #00E676
white = #ffffff
blue = #42a5f5
gray = #707070

angle(_src, len) =>
    rad2degree = 180 / 3.14159265359  //pi 
    ang = rad2degree * math.atan((_src[0] - _src[1]) / ta.atr(len))
    ang

ch_len = input(title='Period', defval=40)  // 10
smaHigh = ta.sma(high, ch_len)
smaLow = ta.sma(low, ch_len)
var ch_Hlv = 0
ch_Hlv := close > smaHigh ? 1 : close < smaLow ? -1 : ch_Hlv[1]
sslDown = ch_Hlv < 0 ? smaHigh : smaLow
sslUp = ch_Hlv < 0 ? smaLow : smaHigh
mid_way = (sslDown + sslUp) * 0.5
ch_a = angle(mid_way, 3)

color1 = ch_len < 25 ? c2_a : c1_a
color2 = ch_len < 25 ? c2_b : c1_b



sdplot = plot(sslDown, linewidth=channel_line_width, color=color.new(color1, 50))
suplot = plot(sslUp, linewidth=channel_line_width, color=color.new(color2, 50))
plot(mid_way, title='Mid Point', color=color.new(color.white, 50))
plot(ch_a, title='Angle', color=color.new(color.white, 100))

//f_trans = 80
f_c1 = sslDown > sslUp and show_channel_fill ? color.new(color1, f_trans) : sslUp > sslDown and show_channel_fill ? color.new(color2, f_trans) : na

f_c2 = ch_a > 0 and ch_a < 1 ? color.new(gray, f_trans) : ch_a > 1 and ch_a < 2 ? color.new(yellow, f_trans) : ch_a > 2 and ch_a < 4 ? color.new(orange, f_trans) : ch_a > 4 and ch_a < 20 ? color.new(red, f_trans) : na

f_c3 = ch_a < 0 and ch_a > -1 ? color.new(gray, f_trans) : ch_a < -1 and ch_a > -2 ? color.new(aqua, f_trans) : ch_a < -2 and ch_a > -4 ? color.new(green, f_trans) : ch_a < -4 and ch_a < 20 ? color.new(lime, f_trans) : na

ch_color = use_angle_color == false ? f_c1 : ch_a > 0 ? f_c2 : f_c3
fill(sdplot, suplot, color=ch_color, transp=90)
//plotshape(sslUp>sslDown, title="Sell",color=#ff0000,style=shape.circle,location=location.top)
//plotshape(sslUp<sslDown, title="Buy",color=#00ff00,style=shape.circle,location=location.bottom)

