//@version=5
indicator(title="<PERSON> Angles - Ben", overlay=false)

g_ma = "MA ----------------------------------------------------"
inl_ma = "ma"
inl_type = "inl_type"
inl_color = "inl_color"
inl_smooth = "inl_smooth"
inl_angle = "inl_angle"
inl_mult = "inl_mult"
i_show_ma = input.bool(false,title='Show MAs',group=g_ma)
i_ma_time = input.timeframe(title='Timeframe', defval='239',group=g_ma,inline=inl_type)
ma_type = input.string(title="MA Type", defval="hma", options=["sma","ema","dema","tema","wma","vwma","smma","rma","hma","lsma","<PERSON>cG<PERSON><PERSON>", "ATR"],group=g_ma,inline=inl_type)
i_ma_select = input.int(5, title="Colorize Candles", options=[1,2,3,4,5,6,7,8,9],group=g_ma,inline=inl_color)
i_ma_color_alpha = input.int(50, title='Alpha',group=g_ma,inline=inl_color)
use_smooth = input.bool(false, title=" ",inline=inl_smooth)
smooth = input.int(1,title="Smooth",inline=inl_smooth)
i_use_angle = input.bool(true,title = " ",inline=inl_angle)
i_display = input.string(title="MA Type", defval="line", options=["line","area","column"])
use_candles = input.bool(false,title=" ",group=g_ma,inline=inl_color)
i_show_time_breaks = input.bool(false, title='Show Time Intervals')
i_angle = 14 //input.int(14,title="Angle Amount",inline=inl_angle)
use_multiple = input.bool(false, title=" ",inline=inl_mult)
multi_value = input.int(1, title="Multiply Value",inline=inl_mult)
i_use_gaps = false //input.bool(false,title='Use Gaps')
src = close // input(close, title="Source")



// Default HMA - M3, M5 (colorzied) // Default2 HMA M2, M5, M6 (colorzied)
g_cb = "Show MA ----------------------------------------------------"
inl_cb = "cb"
show_m1 = input.bool(title="M1", defval=true,group=g_cb,inline=inl_cb)
show_m2 = input.bool(title="M2", defval=true,group=g_cb,inline=inl_cb)
show_m3 = input.bool(title="M3", defval=true,group=g_cb,inline=inl_cb)
show_m4 = input.bool(title="M4", defval=false,group=g_cb,inline=inl_cb)
show_m5 = input.bool(title="M5", defval=true,group=g_cb,inline=inl_cb)
show_m6 = input.bool(title="M6", defval=true,group=g_cb,inline=inl_cb)
show_m7 = input.bool(title="M7", defval=false,group=g_cb,inline=inl_cb)
show_m8 = input.bool(title="M8", defval=false,group=g_cb,inline=inl_cb)
show_m9 = input.bool(title="M9", defval=false,group=g_cb,inline=inl_cb)

g_angles = "MA Angles ----------------------------------------------------"
inl_angles = "angles"
i_ang_1 =  input.int(1, title='A1',group=g_angles,inline=inl_angles)
i_ang_2 =  input.int(2, title='A2',group=g_angles,inline=inl_angles)
i_ang_3 =  input.int(3, title='A3',group=g_angles,inline=inl_angles)
i_ang_4 =  input.int(4, title='A4',group=g_angles,inline=inl_angles)
i_ang_5 =  input.int(5, title='A5',group=g_angles,inline=inl_angles)
i_ang_6 =  input.int(6, title='A6',group=g_angles,inline=inl_angles)
i_ang_7 =  input.int(7, title='A7',group=g_angles,inline=inl_angles)
i_ang_8 =  input.int(8, title='A8',group=g_angles,inline=inl_angles)
i_ang_9 =  input.int(9, title='A9',group=g_angles,inline=inl_angles)

g_ma_len = "Length ----------------------------------------------------"
inl_ma_len = "ma_len"
l1 = input.int(5,title="M1",group=g_ma_len,inline=inl_ma_len) // 8
l2 = input.int(20,title="M2",group=g_ma_len,inline=inl_ma_len)
l3 = input.int(50,title="M3",group=g_ma_len,inline=inl_ma_len)
l4 = input.int(75,title="M4",group=g_ma_len,inline=inl_ma_len)
l5 = input.int(100,title="M5",group=g_ma_len,inline=inl_ma_len)
l6 = input.int(200,title="M6",group=g_ma_len,inline=inl_ma_len)
l7 = input.int(300,title="M7",group=g_ma_len,inline=inl_ma_len)
l8 = input.int(500,title="M8",group=g_ma_len,inline=inl_ma_len)
l9 = input.int(1000,title="M9",group=g_ma_len,inline=inl_ma_len)

g_fill = "Fills"
inl_fill = "fill"
inl_conv = "conv"
show_fill = input.bool(title="Show Fill", defval=true,inline=inl_fill,group=g_fill)
show_conv = input.bool(title="Show Conv", defval=true,inline=inl_fill,group=g_fill)
conv_amount = input.float(8, title="Conv Amount", step=1,inline=inl_conv,group=g_fill )
c_type = input.string(title="Type", defval="NAS", options=["NAS","USD", "JPY"],inline=inl_conv,group=g_fill)
line_input = 1 //input(1, title="Line width", type=input.integer,inline=inl_fill )

g_ma_dir = "MA Direction ----------------------------------------------------"
i_show_rising_falling = input.bool(false, 'Rising Falling')
i_show_rising_falling_ba = input.bool(false, 'Rising Falling BB')
//i_ma_dir = input.source(close, "Source", group=g_ma_dir)


red = #ff0062
orange = #ff9800
yellow = #FFFF00
green = #005a04
lime = #00E676
aqua = #00bcd4
blue = #2962ff 
dark_blue = #2962ff
violet = #814dff
white = #ffffff
gray = #707070

angle(_src, len) =>
    rad2degree=180/3.14159265359  //pi 
    ang=rad2degree*math.atan((_src[0] - _src[1]) / ta.atr(len))
    
newbar(res) => ta.change(time(res)) == 0 ? 0 : 1



// Lines and Angles
ma_graph(len) =>
    ma =0.0
    length = len
    if use_multiple
        length := len * multi_value
    if ma_type == 'sma' // Simple Moving Average
        ma := ta.sma(src,length)
    if ma_type == 'ema' // Exponential
        ma := ta.ema(src,length)
    if ma_type=="dema" // Double Exponential
        e = ta.ema(src, length)
        ma := 2 * e - ta.ema(e, length)
    if ma_type == 'tema' // Triple Exponential
        ema1 = ta.ema(src, length)
        ema2 = ta.ema(ema1, length)
        ema3 = ta.ema(ema2, length)
        ma := 3 * (ema1 - ema2) + ema3
    if ma_type == 'wma' // Weighted
        ma := ta.wma(src,length)
    if ma_type == 'vwma' // Volume Weighted
        ma := ta.vwma(src,length)
    if ma_type=="smma" // Smoothed
        w = ta.wma(src, length)
        ma := na(w[1]) ? ta.sma(src, length) : (w[1] * (length - 1) + src) / length
    if ma_type == "rma"
        ma := ta.rma(src, length)
    if ma_type == 'hma' // Hull
        ma := ta.hma(src, length)
        //ma := ta.wma(2*ta.wma(src, length/2)-ta.wma(src, length), math.floor(math.sqrt(length) ))
    if ma_type=="lsma" // Least Squares
        ma := ta.linreg(src, length, 0)
    if ma_type=="McGinley"
        mg = 0.0
        mg := na(mg[1]) ? ta.ema(src, length) : mg[1] + (src - mg[1]) / (length * math.pow(src/mg[1], 4))
        ma :=mg

    if ma_type=="ATR"
        mg = 0.0
        mg := ta.atr(length)
        ma := mg

    if use_smooth
        ma := ta.sma(ma,smooth)

    angle = i_use_angle ? i_angle : 1
    ma_angle = angle(ma,angle)
    [ma,ma_angle]




ma_conv(t1, t2) =>
    
    float boost = switch syminfo.ticker
        "GBPUSD" => 10000
        "EURUSD" => 10000
        "GBPJPY" => 100
        "BTCUSD" => 0.1
        "NAS100" => 1
        "NDQ100" => 1
        "GOLD"   => 10
        "SILVER" => 100
        => 1

    diff = (t1 - t2) * boost
    conv = show_conv and diff < conv_amount and diff > conv_amount * -1 ? true : false
    [diff, conv]


[m1,m1_a] = request.security(syminfo.tickerid, i_ma_time, ma_graph(l1))
[m2,m2_a] = request.security(syminfo.tickerid, i_ma_time, ma_graph(l2)) 
[m3,m3_a] = request.security(syminfo.tickerid, i_ma_time, ma_graph(l3)) 
[m4,m4_a] = request.security(syminfo.tickerid, i_ma_time, ma_graph(l4)) 
[m5,m5_a] = request.security(syminfo.tickerid, i_ma_time, ma_graph(l5)) 
[m6,m6_a] = request.security(syminfo.tickerid, i_ma_time, ma_graph(l6)) 
[m7,m7_a] = request.security(syminfo.tickerid, i_ma_time, ma_graph(l7)) 
[m8,m8_a] = request.security(syminfo.tickerid, i_ma_time, ma_graph(l8)) 
[m9,m9_a] = request.security(syminfo.tickerid, i_ma_time, ma_graph(l9)) 

[m2_m4_diff,m2_m4_conv] = ma_conv(m2,m4)
[m4_m5_diff,m4_m5_conv] = ma_conv(m4,m5)
[m5_m6_diff,m5_m6_conv] = ma_conv(m5,m6)
[m7_m8_diff,m7_m8_conv] = ma_conv(m7,m8)


if newbar(i_ma_time) == 0
    m1_a := m1_a[1]
    m2_a := m2_a[1]
    m3_a := m3_a[1]
    m4_a := m4_a[1]
    m5_a := m5_a[1]
    m6_a := m6_a[1]
    m7_a := m7_a[1]
    m8_a := m8_a[1]

// Colorized MA angles
ma_select()=>
    float select = switch i_ma_select
        2 => m2_a
        3 => m3_a
        4 => m4_a
        5 => m5_a
        6 => m6_a
        7 => m7_a
        8 => m8_a
        => m6_a

    ma_a = math.abs(select)
    ma_zone =  
     ma_a   < i_ang_1 ? 0 
     : ma_a < i_ang_2 ? 1 
     : ma_a < i_ang_3 ? 2 
     : ma_a < i_ang_4 ? 3 
     : ma_a < i_ang_5 ? 4 
     : ma_a < i_ang_6 ? 5 
     : ma_a < i_ang_7 ? 6 
     : ma_a < i_ang_8 ? 7 
     : ma_a > i_ang_7 ? 8 
     : na

    ma_color = 
     ma_zone   == 0 ? red 
     : ma_zone == 1 ? orange 
     : ma_zone == 2 ? yellow 
     : ma_zone == 3 ? gray 
     : ma_zone == 4 ? green 
     : ma_zone == 5 ? lime
     : ma_zone == 6 ? blue
     : ma_zone == 7 ? aqua
     : ma_zone == 8 ? white : na

    ma_state = ta.change(ma_zone)

    [ma_color, ma_state]

[ma_color, ma_state] = ma_select()

// var int ma_state_ch = 0
// ma_state_ch := not na(ma_state) ? ma_state : ma_state_ch
// // ma state
// plot(ma_state_ch, title='MA State', color=color.new(ma_color,100)  )

// Growing or Shrinking
// f_get_state(color) =>

//     switch

// ma_state = 0
// ma_state = ta.change(ma_color) ? f_get_state(ma_color) : ma_state

// Colorize candles based on MA angles
barcolor(use_candles? ma_color : na)

display = switch i_display
    "line" => plot.style_line
    "area" => plot.style_area
    "column" => plot.style_columns
    => plot.style_line


// Stepping down or up
i_incre_up_down = input.float(0.5, 'Increment Up Down')
f_ma_angle_dir(m_angle, obj_time)=>
    var dir = 0.0
    dir := ta.change(m_angle) and m_angle < m_angle[1] ? -1 : ta.change(m_angle) and m_angle > m_angle[1] ? 1 : dir
    //ma_angle_direction = m_angle>m_angle[str.tonumber(obj_time) * i_incre_up_down] ? 1 : -1


m3a_dir = f_ma_angle_dir(m2_a, i_ma_time) 
//m3a_dir = m3_a>m3_a[1] ? 1 : -1
plotshape(m3a_dir == -1 ,title="Dir Down",color=red ,style=shape.circle,location=location.top)
plotshape(m3a_dir == 1 ,title="Dir Up",color=green ,style=shape.circle,location=location.bottom)
//m3a_dir = f_ma_angle_dir(m3_a, i_ma_time) 
// m2_a>m2_a[str.tonumber(i_ma_time) * 0.5] ? 1 : -1
//plot(i_show_rising_falling ? m3a_dir : na, 'Higher or Lower Previous',color=m3a_dir==1 ? green : red, style=plot.style_circles)

// Step Up or Down
// var m2a_dir = 0.0
// m2a_dir := ta.change(e_1_m) and e_1_m < e_1_m[1] ? -1 : ta.change(e_1_m) and e_1_m > e_1_m[1] ? 1 : m2a_dir
// plot(m2a_dir, 'Stepping', color = m2a_dir == -1 ? color.new(red,100) :  color.new(green,100)  )


plot(i_show_ma and m1_a and show_m1 ? m1_a: na, title="M1 A", color = i_ma_select==1 ? color.new(ma_color,i_ma_color_alpha) : m1_a>0?color.new(green,0):color.new(red,0),style=i_ma_select==1?plot.style_area : plot.style_line)
plot(i_show_ma and m2_a and show_m2 ? m2_a: na, title="M2 A", color = i_ma_select==2 ? color.new(ma_color,i_ma_color_alpha) : m2_a < 0 ? blue : aqua,style=i_ma_select==2?plot.style_area : plot.style_line )
plot(i_show_ma and m3_a and show_m3 ? m3_a: na, title="M3 A", color = i_ma_select==3 ? color.new(ma_color,i_ma_color_alpha) : color.new(color.blue,0), style=i_ma_select==3?plot.style_area : plot.style_line )
plot(i_show_ma and m4_a and show_m4 ? m4_a: na, title="M4 A", color = i_ma_select==4 ? color.new(ma_color,i_ma_color_alpha) : color.new(yellow,0), style=i_ma_select==4?plot.style_area : plot.style_line )
plot(i_show_ma and m5_a and show_m5 ? m5_a: na, title="M5 A", color = i_ma_select==5 ? color.new(ma_color,i_ma_color_alpha) : color.new(orange,0), style=i_ma_select==5?plot.style_area : plot.style_line )
plot(i_show_ma and m6_a and show_m6 ? m6_a: na, title="M6 A", color = i_ma_select==6 ? color.new(ma_color,i_ma_color_alpha) : m6_a > 0 ? red : green, style=i_ma_select==6?plot.style_area : plot.style_line )
plot(i_show_ma and m7_a and show_m7 ? m7_a: na, title="M7 A", color = i_ma_select==7 ? color.new(ma_color,i_ma_color_alpha) : color.new(orange,0), style=i_ma_select==7?plot.style_area : plot.style_line )
plot(i_show_ma and m8_a and show_m8 ? m8_a: na, title="M8 A", color = i_ma_select==8 ? color.new(ma_color,i_ma_color_alpha) : color.new(green,0), style=i_ma_select==8?plot.style_area : plot.style_line )
plot(i_show_ma and m9_a and show_m9 ? m9_a: na, title="M9 A", color = i_ma_select==9 ? color.new(ma_color,i_ma_color_alpha) : color.new(green,0), style=i_ma_select==8?plot.style_area : plot.style_line )

hline(0)
// hline(10, color=color.new(#ffffff, 80))
// hline(-10, color=color.new(#ffffff, 80))
// hline(15, color=color.new(red, 80))
// hline(-15, color=color.new(green, 80))
// hline(20, color=color.new(red, 80))
// hline(-20, color=color.new(green, 80))
// // Cross up
// hline(-5, color=color.new(#2962ff, 80))

plotshape(ta.change(m1_a) and i_show_time_breaks==true ,title="Change",color=red ,style=shape.circle,location=location.top)

// Alerts
sell_cond  = m3_a<0 and m4_a<0 and m7>m8 and close>m3 
 and not(m2_a>0) 
 and not(m7_a<0) ? 1 : na
//sell_cond2 = m3_a<0 and m4_a<0 and m7>m8 and close>m4 and not(m7_a>0) ? 1 : na
//plotshape(sell_cond,title="Buy 1",color=red ,style=shape.circle,location=location.top)

// 5 min
//buy_cond  = m8_a<0 and m6_a<0 and m3<m2 and m2_a>-5 and close<m2
 and not(m5>m6)
 and not(m3>m5)
 and not(m1_a<-5)
//buy_cond  = m8_a<0 and m6_a<0 and m3_a>0 and close<m3
//buy_cond  = m3<m2 and m2_a>5
//plotshape(buy_cond,title="Buy 1",color=green ,style=shape.circle,location=location.bottom)





// Notes EMA Flags
// Strong uptrend -- Exit
// When m1_a>20 and than m1_a<m1_a[1]
// But the down turn needs to be lower than 10 otherwise probably still trending up

var int flag_u = 0
var int flag_d = 0
//flag_amount = m6_a>20 ? // maybe use BB bands below or above 0 line

flag_u := m2_a>20 and m2_a>m2_a[1] ? 1 : flag_u==1 and m2_a<m2_a[1] ? 0 : flag_u==1 and m2_a>0 ? 1 : na
flag_d := m2_a<-10 and m2_a<m2_a[1] ? -1 : flag_d==-1 and m2_a>m2_a[1] ? 0 : flag_d==-1 and m2_a<0 ? -1 : na

// plotshape(flag_u==0 ? 1 : na,title="Flag Up",color=red ,style=shape.circle,location=location.top)
// plotshape(flag_d==0 ? 1 : na,title="Flag Down",color=green ,style=shape.circle,location=location.bottom)
//plot(flag_u, title="Flag Up")
//plot(flag_d, title="Flag Down")

// Uptrend close below m6
// m6_cond = close<open and close<m6 and m6_a>0
// plotshape(m6_cond, title="M6 buy", color=lime ,style=shape.circle,location=location.bottom)

// M1 green and red candle
// BB Cross
//bb_cross = m1_a>m1_a[1] and m1_a[1]<0 and m2_a > ba and m1_a>m2_a
//plotshape(bb_cross,title="BB Cross",color=m1_a>0?green:blue ,style=shape.circle,location=location.bottom)
// BB upper
//plotshape(close>bb_upper? 1 : na,title="BB Up",color=bb_color ,style=shape.circle,location=location.top)
//plotshape(close<bb_lower? 1 : na,title="BB Down",color=bb_color ,style=shape.circle,location=location.bottom)


// Trading
//plotshape(m2_a<m2_a[1] ? 1 : na, title="M2 Sell", color=red ,style=shape.circle,location=location.top)
//plotshape(m2_a>m2_a[1] ? 1 : na, title="M2 buy", color=lime ,style=shape.circle,location=location.bottom)



// ▒▒▒▒▒ Bollinger Bands ▒▒▒▒▒
g_bb = 'Bollinger Bands ----------------------------------------------------' 
g_bb1 = 'BB 1'
inl_bb1 = 'inl_bb1'
inl_bbb = 'inl_bbb'
bb_squeez_m = input(80, title='Sqz Len')  // 100

// BB1 Settings
bb1_len = input.int(50, title='Length', group=g_bb1, inline=inl_bb1)
bb1_type = input.string(title='Type', defval='SMA', options=['SMA', 'EMA', 'WMA', 'HULL'], group=g_bb1, inline=inl_bb1)
bb1_show = input.bool(title='BB1', defval=false, inline=inl_bbb, group=g_bb1)
i_bb1_basis = input.bool(title='Show Basis', defval=false, group=g_bb1, inline=inl_bbb)
bb1_fill = input.bool(title='Show fill', defval=false, group=g_bb1, inline=inl_bbb)
i_bb1_bias = input.bool(title='Show Bias', defval=false, group=g_bb1, inline=inl_bbb)
bb1_zones = input.bool(title='Zone Change', defval=false, group=g_bb1, inline=inl_bbb)
bb1_zones2 = input.bool(title='Show Zones ', defval=false, group=g_bb1, inline=inl_bbb)
bb1_bias = input.float(title='Bias 1', defval=89.7, step=0.1, group=g_bb1, inline=inl_bb1)
bb1_stdDev = 2  //input(2, minval=2.0, maxval=3)
bb1_smooth_input = 1  // input(title="Smoothing",defval=1,type=input.integer,group=g_bb, inline=inl_bb1) 

// BB Multi Settings
g_bb_m = 'BB Multi ----------------------------------------------------'
inl_bb_m = 'inl_bb_m'
inl_bb_m2 = 'inl_bb_m2'
show_bb = input.bool(title='Show BB', defval=false, group=g_bb_m)
bb_time = input.timeframe(title='Timeframe', defval='60', group=g_bb_m)
bb_len_m = input.int(200, title='Length', group=g_bb_m, inline=inl_bb_m)
i_bb_stdev = input.float(1.5, title='Deviations', minval=1.0, maxval=4.0, step=0.1, group=g_bb_m, inline=inl_bb_m) // 2
bb_type_m = input.string(title='Type', defval='EMA', options=['SMA', 'EMA', 'WMA', 'HULL'], group=g_bb_m, inline=inl_bb_m)
bb_show_m = input.bool(title='BB Multi', defval=true, inline=inl_bb_m2, group=g_bb_m)
i_bb_basis_m = input.bool(title='Basis', defval=true, group=g_bb_m, inline=inl_bb_m2)
bb_zones_m = input.bool(title='Zone Change', defval=false, group=g_bb_m, inline=inl_bb_m2)
bb_zones2_m = input.bool(title='Show Zones', defval=false, group=g_bb_m, inline=inl_bb_m2)
i_bb_smooth = input.bool(title='Use Smoothing', defval=false, group=g_bb_m)
i_bb_smooth_amount = input.int(5,'Smoothing Amount')
bb_fill_m = false //input.bool(title='fill', defval=false, group=g_bb_m, inline=inl_bb_m2)
i_bb_bias_m = false // input.bool(title='Show Bias', defval=false, group=g_bb_m, inline=inl_bb_m2)
bb_bias_m = 89.7 //input.float(title='Bias', defval=89.7, step=0.1, group=g_bb_m, inline=inl_bb_m)
bb_gaps_m = false // input.bool(true,'Use Gaps', group=g_bb_m)

// Timeframe
g_bb_time = ''
inl_bb4 = 'inl_bb4'

bb_ma(len1, type) =>
    float v = na
    if type == 'SMA'
        v := ta.sma(close, len1)
        v
    if type == 'EMA'
        v := ta.ema(close, len1)
        v
    if type == 'WMA'
        v := ta.wma(close, len1)
        v
    if type == 'VWMA'
        v := ta.wma(close, len1)
        v
    if type == 'HULL'
        v := ta.wma(2 * ta.wma(close, len1 / 2) - ta.wma(close, len1), math.round(math.sqrt(len1)))
        v

    if i_bb_smooth
        v := ta.sma(v,i_bb_smooth_amount)
    v

// === BB Multi ===
f_bb(len)=>
    bb_basis    = bb_ma(len, bb_type_m) 
    dev         = i_bb_stdev * ta.stdev(close, len)
    bb_upper    = bb_basis + dev
    bb_lower    = bb_basis - dev
    bb_spread   = bb_upper - bb_lower
    avgspread   = ta.sma(bb_spread, bb_squeez_m)
    bb_sqz      = bb_spread/ avgspread * 100
    bb_diff     = bb_spread * 1000
    bb_zone     = bb_sqz < 53 ? 0 : bb_sqz < bb_squeez_m ? 1 : bb_sqz < 120 ? 2 : bb_sqz < 160 ? 3 : bb_sqz < 200 ? 4 : bb_sqz < 250 ? 5 : bb_sqz > 250 ? 6 : na
    sqz_color   = bb_zone == 0 ? #0045b3 : bb_zone == 1 ? #ff0062 : bb_zone == 2 ? gray : bb_zone == 3 ? #00c3ff : bb_zone == 4 ? white : bb_zone == 5 ? white : bb_zone == 6 ? yellow : na
    bb_a        = angle(bb_basis, 14)
    bb_diff_a   = angle(bb_diff, 14)
    [bb_basis, bb_upper, bb_lower, bb_sqz, bb_zone, sqz_color, bb_a, bb_diff, bb_diff_a]

[ bb_basis_m, bb_upper_m, bb_lower_m, bb_sqz_m, bb_zone_m, sqz_color_m, bb_a_m, bb_diff_m, bb_diff_a_m ] = 
 request.security(syminfo.tickerid, 
 bb_time, 
 f_bb(bb_len_m))

if newbar(bb_time) == 0 and bb_gaps_m==false
    bb_basis_m := bb_basis_m[1]
    bb_upper_m := bb_upper_m[1]
    bb_lower_m := bb_lower_m[1]
    bb_sqz_m := bb_sqz_m[1]

    bb_zone_m := bb_zone_m[1]
    sqz_color_m := sqz_color_m[1]
    bb_a_m := bb_a_m[1]

    bb_diff_m := bb_diff_m[1]
    bb_diff_a_m := bb_diff_a_m[1]

bb_zones_color_m = sqz_color_m

// Zone Change Multi / Growing Squeezing
var zch_m = sqz_color_m
var bch_m = bb_zone_m
var grow_squeeze = bb_zone_m
if ta.change(bb_zone_m)
    grow_squeeze := bb_zone_m>bb_zone_m[1] ? 1 : -1
    zch_m := sqz_color_m[1]
    bch_m := bb_zone_m[1]

// plot(bch_m, title='Zone Change 1', style=plot.style_circles, color=zch_m)
// plot(grow_squeeze, title='Grow Squeeze', style=plot.style_circles)

//bba_m_between = bb_a_m < 1 and bb_a_m > -1 ? true : false
plot(i_bb_basis_m and show_bb and bb_show_m ? bb_a_m : na, 'BB Basis Multi ', color=bb_zones_color_m)

bb_a_dir = f_ma_angle_dir(bb_a_m, bb_time) 
plot(i_show_rising_falling_ba ? bb_a_dir : na, 'Higher or Lower Previous',color=bb_a_dir==1 ? green : red, style=plot.style_circles)
// p5 = plot(bb_show_m and show_bb ? bb_upper_m : na, 'BB Upper Multi ', color=bb_zones_color_m)
// p6 = plot(bb_show_m and show_bb ? bb_lower_m : na, 'BB Lower Multi ', color=bb_zones_color_m)
// fill(p5, p6, title='BB 1 Fill', color=bb_fill_m and bb_show_m ? bb_zones_color_m : na, transp=90)
// barcolor(i_bb_bias_m and bb_diff_a_m > bb_bias_m ? lime : na)

