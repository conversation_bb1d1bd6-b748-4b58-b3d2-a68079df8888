//@version=4
//Original code from alexgrover
//Modified the value of Price1 and Price2 with SMA period of 1
//Added additional MA types
//Modified columns to reflect trend strength or ranging periods

study("Absolute Strength Histogram v2 | jh")

//----
Length = input(9,title="Period of Evaluation", type=input.integer)
Smooth = input(3,title="Period of Smoothing", type=input.integer)
show_histo = input(true, title="Show Histogam", type=input.bool)
//----
src =  input(close,title="Source")
Mode = input(title="Indicator Method", type=input.string, defval="RSI", options=["RSI", "STOCHASTIC","ADX"])
ma_type = input(title="MA", type=input.string, defval="SMA", options=["ALMA", "EMA", "WMA", "SMA", "SMMA", "HMA"])
alma_offset  = input(defval=0.85, title="* <PERSON><PERSON><PERSON> (ALMA) Only - Offset Value", minval=0, step=0.01)
alma_sigma   = input(defval=6, title="* <PERSON><PERSON><PERSON> (ALMA) Only - Sigma Value", minval=0)


angle(_src, len) =>
    rad2degree=180/3.14159265359  //pi 
    ang=rad2degree*atan((_src[0] - _src[1])/atr(len))
    
ma(type, src, len) =>
    float result = 0
    if type=="SMA" // Simple
        result := sma(src, len)
    if type=="EMA" // Exponential
        result := ema(src, len)
    if type=="WMA" // Weighted
        result := wma(src, len)
    if type=="SMMA" // Smoothed
        w = wma(src, len)
        result := na(w[1]) ? sma(src, len) : (w[1] * (len - 1) + src) / len
    if type=="HMA" // Hull
        result := wma(2 * wma(src, len / 2) - wma(src, len), round(sqrt(len)))
    if type=="ALMA" // Arnaud Legoux
        result := alma(src, len, alma_offset, alma_sigma)
    result

//----
Price = src

//----
Price1 = ma("SMA",Price,1)
Price2 = ma("SMA",Price[1],1)

//RSI
Bulls0 = 0.5*(abs(Price1-Price2)+(Price1-Price2))
Bears0 = 0.5*(abs(Price1-Price2)-(Price1-Price2))

//STOCHASTIC
Bulls1 = Price1 - lowest(Price1,Length)
Bears1 = highest(Price1,Length) - Price1

//ADX
Bulls2 = 0.5*(abs(high-high[1])+(high-high[1]))
Bears2 = 0.5*(abs(low[1]-low)+(low[1]-low))

//
Bulls = Mode == "RSI" ? Bulls0 : Mode == "STOCHASTIC" ? Bulls1 : Bulls2
Bears = Mode == "RSI" ? Bears0 : Mode == "STOCHASTIC" ? Bears1 : Bears2
AvgBulls=ma(ma_type,Bulls,Length)     
AvgBears=ma(ma_type,Bears,Length)

//----
SmthBulls=ma(ma_type,AvgBulls,Smooth)  
SmthBears=ma(ma_type,AvgBears,Smooth)

difference = abs(SmthBulls - SmthBears)

bull_trend_color = (SmthBulls<SmthBulls[1])?color.lime:color.green
bear_trend_color = (SmthBears<SmthBears[1])?color.orange:color.red

difference_color = difference > SmthBulls ? ((SmthBears<SmthBears[1])?color.orange:color.red) : difference > SmthBears ? ((SmthBulls<SmthBulls[1])?color.lime:color.green) : color.gray


//----
A = plot(SmthBulls * 5000,color=bull_trend_color,linewidth=3)
B = plot(SmthBears * 5000,color=bear_trend_color,linewidth=3)
plot(difference * 5000, style=plot.style_histogram, linewidth=3, color=show_histo ? difference_color : na, transp=45, title="Strength")
plot(angle(SmthBulls,14), title="Angle up", color=bull_trend_color, style=plot.style_circles)
plot(angle(SmthBears,14), title="Angle Down", color=bear_trend_color, style=plot.style_circles)
//fill(A,B,color=trend==1?color.aqua:color.fuchsia,transp=80)
