//@version=5
// With levels candle border must be removed for better view  
// By Glaz
indicator(title='RSI Chart Bars - V5', overlay=false)
src = close
len = input.int(14, minval=1, title='Length') // 28
i_smooth_rsi = input.bool(true,title='Use Smoothing')
i_show_ma = input.bool(true,title='Show MA')
i_smooth_len = input.int(5, minval=1, title='Smooth Length') // 5
len1 = input.int(70, minval=1, title='UpLevel')
len2 = input.int(30, minval=1, title='DownLevel')

up = ta.rma(math.max(ta.change(src), 0), len)
down = ta.rma(-math.min(ta.change(src), 0), len)
if i_smooth_rsi
    up   := ta.sma(up,i_smooth_len)
    down := ta.sma(down,i_smooth_len)

rsi = down == 0 ? 100 : up == 0 ? 0 : 100 - 100 / (1 + up / down)
var float ma = 0

if i_show_ma
    ma := ta.sma(rsi,i_smooth_len)
    
//coloring method below
src1 = src
src2 = src

isup() =>
    rsi > len1
isdown() =>
    rsi < len2
plot(rsi, title='RSI', color=color.new(color.blue, 0))
plot(ma, title='Smoothed RSI MA', color=color.new(color.white, 50))
plot(isup() ? 1 : na, title='Is Up', color=color.new(color.green, 0), style=plot.style_circles)
plot(isdown() ? -1 : na, title='Is Down', color=color.new(#ff0000, 0), style=plot.style_circles)
barcolor(isup() ? #00ff00 : isdown() ? #ff0000 : na)
hline(len1, title='Upper', color=color.new(color.red, 50), linestyle=hline.style_dotted, linewidth=2)
hline(len2, title='Lower', color=color.new(color.green, 50), linestyle=hline.style_dotted, linewidth=2)
hline(50, title='Mid', color=color.new(color.white, 20), linewidth=1)

