//@version=5
indicator('Supertrend - Multi', overlay=true, format=format.price, precision=6)

i_time_sup = input.timeframe(title='Timeframe', defval='15')
inl_sup1 = "inl_sup1"
i_sup_show1= input.bool(false,title='Sup 1',inline=inl_sup1)
i_sup1_a = input.int(10,title='',inline=inl_sup1)
i_sup1_b = input.int(1,title='',inline=inl_sup1)
inl_sup2 = "inl_sup2"
i_sup_show2= input.bool(true,title='Sup 2',inline=inl_sup2)
i_sup2_a = input.int(11,title='',inline=inl_sup2)
i_sup2_b = input.int(2,title='',inline=inl_sup2)
inl_sup3 = "inl_sup3"
i_sup_show3= input.bool(false,title='Sup 3',inline=inl_sup3)
i_sup3_a = input.int(12,title='',inline=inl_sup3)
i_sup3_b = input.int(3,title='',inline=inl_sup3)
inl_sup4 = "inl_sup4"
i_sup_show4 = input.bool(true,title='Sup 4',inline=inl_sup4) 
i_sup4_a = input.int(5,title='',inline=inl_sup4) 
i_sup4_b = input.int(5,title='',inline=inl_sup4) 

red = #ff0062
orange = #ff9800
yellow = #bebe00
green = #4caf50
lime = #55d51a
aqua = #00bcd4
blue = #2962ff
dark_blue = #2962ff
violet = #814dff
white = #ffffff
gray = #707070
black = #000000

src = input(hl2, title='Source')
//Multiplier = input.float(title='ATR Multiplier', step=0.1, defval=3.0)
changeATR = true //input(title='Change ATR Calculation Method ?', defval=true)
showsignals = false //input(title='Show Buy/Sell Signals ?', defval=true)
i_bars_merge = true //input.bool(true, title='Lookahead On')
//highlighting = input(title='Highlighter On/Off ?', defval=true)

get_point_value(point1, point2, abs_value) =>
    float diff_points = 0.0
    float point_value = 0.0
    diff_points := (point1 - point2)
    point_value := diff_points / syminfo.mintick / 10


supertend(p, m) =>
    atr2 = ta.sma(ta.tr, p)
    atr = changeATR ? ta.atr(p) : atr2
    up = src - m * atr
    up1 = nz(up[1], up)
    up := close[1] > up1 ? math.max(up, up1) : up
    dn = src + m * atr
    dn1 = nz(dn[1], dn)
    dn := close[1] < dn1 ? math.min(dn, dn1) : dn
    trend = 1
    trend := nz(trend[1], trend)
    trend := trend == -1 and close > dn1 ? 1 : trend == 1 and close < up1 ? -1 : trend
    b_sig = trend == 1 and trend[1] == -1

    s_sig = trend == -1 and trend[1] == 1


    [trend, up, dn, b_sig, s_sig]

[trend1, up1, dn1, b_sig1, s_sig1]  = request.security(syminfo.tickerid, i_time_sup, supertend(i_sup1_a, i_sup1_b), lookahead=barmerge.lookahead_on  )
[trend2, up2, dn2, b_sig2, s_sig2]  = request.security(syminfo.tickerid, i_time_sup, supertend(i_sup2_a, i_sup2_b), lookahead=barmerge.lookahead_on  )
[trend3, up3, dn3, b_sig3, s_sig3]  = request.security(syminfo.tickerid, i_time_sup, supertend(i_sup3_a, i_sup3_b), lookahead=barmerge.lookahead_on  )
[trend4, up4, dn4, b_sig4, s_sig4]  = request.security(syminfo.tickerid, i_time_sup, supertend(i_sup4_a, i_sup4_b), lookahead=barmerge.lookahead_on  )

//[trend3, up3, dn3, b_sig3, s_sig3]  = request.security(syminfo.tickerid, i_time_sup, supertend(12, 3), lookahead=barmerge.lookahead_on  )


// Close and Distance to trend line
// close_m = request.security(syminfo.tickerid, i_time_sup, close[1], lookahead=barmerge.lookahead_on  ) 
// plot(close_m, title='Change', color=close_m>0 ? color.new(green,100) : color.new(red,100)  )



newbar(res) => ta.change(time(res)) == 0 ? 0 : 1
change = newbar(i_time_sup)
plot(change)


// Supertrend 1
up1 := change ? up1 : up1[1]
trend1  := change ? trend1 : trend1[1]
upPlot1 = plot(trend1 == 1 ? up1 : na, title='Up Trend 1', style=plot.style_linebr, linewidth=1, color=i_sup_show1 ? aqua:na )
dn1 := change ? dn1 : dn1[1]
dnPlot1 = plot(trend1 == -1 ? dn1 : na , title='Down Trend 1', style=plot.style_linebr, linewidth=1, color=i_sup_show1 ? yellow :na )

// Supertrend 2
up2 := change ? up2 : up2[1]
trend2  := change ? trend2 : trend2[1]
upPlot2 = plot(trend2 == 1 ? up2 : na, title='Up Trend 2', style=plot.style_linebr, linewidth=1, color=i_sup_show2 ? blue:na )
dn2 := change ? dn2 : dn2[1]
dnPlot2 = plot(trend2 == -1 ? dn2 : na , title='Down Trend 2', style=plot.style_linebr, linewidth=1, color=i_sup_show2 ? orange : na )

// Supertrend 3
up3     := change ? up3 : up3[1]
trend3  := change ? trend3 : trend3[1]
upPlot3 = plot(trend3==1 ? up3 : na, title='Up Trend 3', style=plot.style_linebr, linewidth=1, color=i_sup_show3 ? lime:na )
dn3 := change ? dn3 : dn3[1]
dnPlot3 = plot(trend3==-1 ? dn3 : na , title='Down Trend 3', style=plot.style_linebr, linewidth=1, color=i_sup_show3 ? violet :na )

// Supertrend 4
up4     := change ? up4 : up4[1]
trend4  := change ? trend4 : trend4[1]
upPlot4 = plot(trend4==1 ? up4 : na, title='Up Trend 4', style=plot.style_linebr, linewidth=1, color=i_sup_show4 ? green:na )
dn4 := change ? dn4 : dn4[1]
dnPlot4 = plot(trend4==-1 ? dn4 : na , title='Down Trend 4', style=plot.style_linebr, linewidth=1, color=i_sup_show4 ?red :na )
// plotshape(b_sig2 and close_m? up2 : na, title='UpTrend 2', location=location.absolute, style=shape.circle, size=size.tiny, color=i_sup_show2 ? aqua:na )
// plotshape(b_sig2 and close_m and showsignals ? up2 : na, title='Buy 2', text='Buy 2', location=location.absolute, style=shape.labelup, size=size.tiny, color=i_sup_show2 ? aqua : na , textcolor=i_sup_show2 ? white : na)

// plotshape(s_sig2 and close_m ? dn2 : na, title='DownTrend 2', location=location.absolute, style=shape.circle, size=size.tiny, color=i_sup_show2 ? orange:na)
// plotshape(s_sig2 and close_m and showsignals ? dn2 : na, title='Sell 2', text='Sell 2', location=location.absolute, style=shape.labeldown, size=size.tiny, color=i_sup_show2 ?orange :na, textcolor=i_sup_show2 ? white : na)

// [10, 1]
// upPlot = plot(trend == 1 ? up : na, title='Up Trend', style=plot.style_linebr, linewidth=1, color=i_sup_show1 ? green:na )
// plotshape(b_sig and close_m ? up : na, title='UpTrend 1', location=location.absolute, style=shape.circle, size=size.tiny, color=i_sup_show1 ? green:na )
// plotshape(b_sig and showsignals and close_m ? up : na, title='Buy 1', text='Buy 1', location=location.absolute, style=shape.labelup, size=size.tiny, color=i_sup_show1 ? green : na , textcolor=i_sup_show1 ? white : na)
// dnPlot = plot(trend == 1 ? na : dn, title='Down Trend', style=plot.style_linebr, linewidth=1, color=i_sup_show1 ?red :na )
// plotshape(s_sig and close_m? dn : na, title='DownTrend 1', location=location.absolute, style=shape.circle, size=size.tiny, color=i_sup_show1 ? red:na)
// plotshape(s_sig and showsignals and close_m ? dn : na, title='Sell 1', text='Sell 1', location=location.absolute, style=shape.labeldown, size=size.tiny, color=i_sup_show1 ?red :na, textcolor=i_sup_show1 ? white : na)

// // [12, 3]
// upPlot3 = plot(trend3 == 1 ? up3 : na, title='Up Trend', style=plot.style_linebr, linewidth=1, color=i_sup_show3 ? lime:na )
// plotshape(b_sig3 and close_m ? up3 : na, title='UpTrend 3', location=location.absolute, style=shape.circle, size=size.tiny, color=i_sup_show3 ? lime:na )
// plotshape(b_sig3 and close_m and showsignals ? up3 : na, title='Buy 3', text='Buy 3', location=location.absolute, style=shape.labelup, size=size.tiny, color=i_sup_show3 ? lime : na , textcolor=i_sup_show3 ? white : na)
// dnPlot3 = plot(trend3 == 1 ? na : dn3, title='Down Trend', style=plot.style_linebr, linewidth=1, color=i_sup_show3 ?yellow :na )
// plotshape(s_sig3 and close_m ? dn3 : na, title='DownTrend 3', location=location.absolute, style=shape.circle, size=size.tiny, color=i_sup_show3 ? yellow:na)
// plotshape(s_sig3 and close_m and showsignals ? dn3 : na, title='Sell 3', text='Sell 3', location=location.absolute, style=shape.labeldown, size=size.tiny, color=i_sup_show3 ?yellow :na, textcolor=i_sup_show3 ? white : na)

//cond_down = ta.change(close_m) and trend4 == -1 ? dn4 : trend4 == -1 ? dn4[1] : na
//cond_up = ta.change(up4) and trend4 == 1 ? up4[1] : trend4 == 1 ? up4[2] : na
// plotshape(b_sig4 and close_m ? up4 : na, title='UpTrend 4', location=location.absolute, style=shape.circle, size=size.tiny, color=i_sup_show4 ? green:na )
// plotshape(b_sig4 and close_m and showsignals ? up4 : na, title='Buy 4', text='Buy 4', location=location.absolute, style=shape.labelup, size=size.tiny, color=i_sup_show4 ? green : na , textcolor=i_sup_show4 ? white : na)

// plotshape(s_sig4 and close_m ? dn4 : na, title='DownTrend 4', location=location.absolute, style=shape.circle, size=size.tiny, color=i_sup_show4 ? red:na)
// plotshape(s_sig4 and close_m and showsignals ? dn4 : na, title='Sell 4', text='Sell 4', location=location.absolute, style=shape.labeldown, size=size.tiny, color=i_sup_show4 ?red :na, textcolor=i_sup_show4 ? white : na)
