//@version=4
study(shorttitle="Basis to ema - Jan 7th", title="Basis to ema - Jan 7th")

red = #ff0062
aqua = #00bcd4
yellow = #FFFF00
orange = #ff9800
green = #4caf50
lime = #00E676
white = #ffffff
blue = #42a5f5
gray = #707070
black = #000000

angle(_src, len) =>
    rad2degree=180/3.14159265359  //pi 
    ang=rad2degree*atan((_src[0] - _src[1])/atr(len))

ema_200 = ema(close, 200)

// ===  Kijun ===
// ==================================================
middleDonchian(Length) =>
    lower = lowest(Length)
    upper = highest(Length)
    avg(upper, lower)

basePeriods = input(14, minval=1) //26
kijun =  middleDonchian(basePeriods)

var float bb_squeeze = 0.00
var float bb_diff = 0.00
BB_length = input(20,title="BB Length") // 100 40 43
BB_stdDev = 2 //input(2,title="Deviations")
bb_s = close //kijun
basis = sma(close, BB_length)
dev = BB_stdDev * stdev(bb_s, BB_length)
bb_upper = basis + dev
bb_lower = basis - dev


be_mult = syminfo.currency == 'JPY' ? 100 : syminfo.currency == 'NZD' ? 10000 : 10000
bbu_dist = (bb_upper - ema_200) * be_mult
close_dist = (close - ema_200) * be_mult
be_dist = (basis - ema_200) * be_mult
bbl_dist = (bb_lower - ema_200) * be_mult
be_high = 50
be_up = 10
be_down = -10
be_low = -50
dist_angle = angle(abs(be_dist),5) // 2

be_color = be_dist>0?color.red : color.green 

// Diverge Converge
// di_co_mult = 3
// divg_dist = bb_upper[di_co_mult]>bb_upper and bb_lower[di_co_mult]<bb_lower ? 1 : 0
// conv_dist = bb_upper[di_co_mult]<bb_upper and bb_lower[di_co_mult]>bb_lower ? 1 : 0
// div_cov_dist = divg_dist == 1 ? divg_dist : conv_dist == -1 ? conv_dist : na
// be_color = div_cov_dist>0?green:div_cov_dist<0?red:gray


// ===  SSL ===
// ==================================================
ssl_len = input(14, minval=1,title="SSL Len") //43 60 75
ssl_len2 = input(20, minval=1,title="SSL 2")
ssl_len3 = 8 //input(8, minval=1,title="SSL 3") //7
ssl_long = input(75, minval=1,title="SSL Long")
SSL = wma(2*wma(close, ssl_len/2)-wma(close, ssl_len), round(sqrt(ssl_len)))
SSL2 = wma(2*wma(close, ssl_len2/2)-wma(close, ssl_len2), round(sqrt(ssl_len2)))
SSL3 = wma(2*wma(close, ssl_len3/2)-wma(close, ssl_len3), round(sqrt(ssl_len3)))
SSL_long = wma(2*wma(close, ssl_long/2)-wma(close, ssl_len3), round(sqrt(ssl_len3)))
//ssl_angle = angle(SSL,3)
ssl_angle = angle(SSL,2)
ssl_angle2 = angle(SSL2,2)
ssl_angle3 = angle(SSL3,2)
ssl_diff = abs(SSL - SSL2) * 100
ssl_color = ssl_angle > 0 ? lime : red
ssl_color2 = ssl_angle2 > 0 ? yellow : orange


ssl_sell_cond1 = SSL>bb_upper and close>close[1] and close>SSL3 ? 1 : na
ssl_sell_cond2 = SSL3<SSL and SSL2>bb_upper and close>close[1] ? 1 : na
//ssl_sell_cond3 = ssl_angle2<10 and close>close[1] and close>SSL2 and close>basis ? 1 : na
ssl_buy_cond1 = SSL<bb_lower and close<close[1]? 1 : na
ssl_buy_cond2 = SSL3>SSL and SSL2<bb_lower and close<close[1]? 1 : na
plotshape(ssl_sell_cond1, style=shape.circle,location=location.top,color=red)
plotshape(ssl_sell_cond2, style=shape.circle,location=location.top,color=orange)
plotshape(ssl_buy_cond1, style=shape.circle,location=location.bottom,color=green,text="1")
plotshape(ssl_buy_cond2, style=shape.circle,location=location.bottom,color=lime,text="2")

ssl2_sell_cond = SSL2>bb_upper and close>close[1] and close>SSL3 ? 1 : na
// ssl2_buy_cond = SSL2<bb_lower and close<close[1]? 1 : na
// plot(SSL2,title="SSL2",color=color.black)
plotshape(ssl2_sell_cond, style=shape.circle,location=location.top,color=orange)
// plotshape(ssl2_buy_cond, style=shape.circle,location=location.bottom,color=green)

// === PLOTTING ===
//plot(ssl_angle2,title="SSL2 angle",color=color.black)
plot(SSL,title="SSL",color=color.lime)
plot(SSL2,title="SSL2",color=color.orange)
plot(SSL_long,title="SSL Long",color=color.orange)
plot(bb_upper,title="BB Upper",color=color.black)
plot(bb_lower,title="BB lower",color=color.black)
plot(be_dist,title="Distance",color=color.new(be_color,50),style=plot.style_area)
//plot(div_cov_dist ,title="Div Conv dist",color=div_cov_dist>0?green:div_cov_dist<0?red:gray)
plot(dist_angle,title="Angle",color=dist_angle<0?#008800:#ff0000)
plot(bbu_dist,title="BBU Dist",color=color.new(#00c3ff,30))
plot(bbl_dist,title="BBL Dist",color=color.new(#00c3ff,30))

hline(be_high,title="High", color=color.new(color.red,50))
hline(be_up,title="Up", color=color.new(color.red,50))
hline(be_down,title="Down", color=color.new(color.green,50))
hline(be_low,title="Low", color=color.new(color.green,50))

//plot(dist_angle,title="Distance Angle",color=color.new(#000000,0) )
// plot(ema_200,title="Basis",color=color.new(color.yellow,10))
// plot(basis,title="Basis",color=color.new(#ffffff,10))
// plot(bb_upper,title="Upper Band", color=color.new(#ff0000,10))
// plot(bb_lower,title="Lower Band",color=color.new(#ff0000,10))


