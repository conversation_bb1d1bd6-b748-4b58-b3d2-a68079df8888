//@version=5
indicator(title="MA - 2025", overlay=true)

red = #ff0062
orange = #ff9800
yellow = #FFFF00
green = #005a04
lime = #00E676
aqua = #00bcd4
blue = #2962ff
dark_blue = #2962ff
black = #222222
violet = #814dff
white = #ffffff
gray = #707070


g_ma_props = "Moving Averages ----------------------------------------------------"
inl_smooth = "smooth"
//inl_ma = "ma"
i_ma_time = input.timeframe(title='Timeframe', defval='5', group=g_ma_props)
i_plot_ch = input.bool(true, title='Use Plot Change', group=g_ma_props)
i_gaps = input.bool(false,title='Use Gaps', group=g_ma_props)
i_ma_use_smooth = input.bool(false, title="Use Smoothing", group=g_ma_props, inline=inl_smooth)

i_ma_smooth = input.int(5,title="Smoothing", group=g_ma_props, inline=inl_smooth)
//mult = input.bool(false, title="Use Multiply", group=g_ma_props)
//use_multiple_after = input.bool(false, title="Use Multiply After", group=g_ma_props)
ma1_multiply1 = input.int(3, title="M1 Fast Multiply", group=g_ma_props)
ma1_multiply2 = input.int(3, title="M1 Multiply", group=g_ma_props)
ma2_multiply1 = input.int(3, title="M2 Fast Multiply", group=g_ma_props)
ma2_multiply2 = input.int(3, title="M2 Multiply", group=g_ma_props)

//show_candles = input.bool(false,title="Show Candle Division", group=g_ma_props)
show_angles = input.bool(false,title='Show Angles', group=g_ma_props)
i_show_shapes = input.bool(false, 'Show Dots', group=g_ma_props)
angle_amount = 14 //input.int(14, title="Angle Amount", group=g_ma_props)
i_lookahead = false //input.bool(false,title='Use Lookahead', group=g_ma_props)
i_use_heikin = false // input.bool(false, 'Heikin Ashi', group=g_ma_props)
i_offset = 0 // input.int(0, title="Offset", group=g_ma_props) 
ma_src = close //input.source(close, "Source", group=g_ma_props)


// g_ma_colors = "Colors"
// ma_inl_c1 = "ma_col1"
// ma_inl_c2 = "ma_col2"
i_ma_col1 = white // input.color(white, "C1", group=g_ma_colors, inline=ma_inl_c1)
i_ma_col2 = aqua // input.color(aqua, "C2", group=g_ma_colors, inline=ma_inl_c1)
i_ma_col3 = blue // input.color(blue, "C3", group=g_ma_colors, inline=ma_inl_c1)

g_fill = "Fills"
inl_fill = "fill"
inl_conv = "conv"
i_ma_select = input.int(3, title="Colorized", options=[1,2,3,4,5,6,7,8,9],group=g_fill)
conv_amount = input.float(7, title="Conv Amount", step=1,inline=inl_conv,group=g_fill ) // 50 4
show_fill = input.bool(title="Show Fill", defval=true,inline=inl_fill,group=g_fill)
show_conv = input.bool(title="Show Conv", defval=true,inline=inl_fill,group=g_fill)
//line_input = 1 //input(1, title="Line width", type=input.integer,inline=inl_fill )
//c_type = input.string(title="Type", defval="NAS", options=["NAS","USD", "JPY"],inline=inl_conv,group=g_fill)

g_cb = "Show ----------------------------------------------------"
inl_cb = "cb"
show_h1 = input.bool(title="M1", defval=true,group=g_cb,inline=inl_cb)
show_h2 = input.bool(title="M2", defval=true,group=g_cb,inline=inl_cb)
show_h3 = input.bool(title="M3", defval=true,group=g_cb,inline=inl_cb)
show_h4 = input.bool(title="M4", defval=false,group=g_cb,inline=inl_cb)
show_h5 = input.bool(title="M5", defval=true,group=g_cb,inline=inl_cb)
show_h6 = input.bool(title="M6", defval=true,group=g_cb,inline=inl_cb)
show_h7 = input.bool(title="M7", defval=false,group=g_cb,inline=inl_cb)
show_h8 = input.bool(title="M8", defval=false,group=g_cb,inline=inl_cb)
show_h9 = input.bool(title="M9", defval=true,group=g_cb,inline=inl_cb)

g_ma = "MA ----------------------------------------------------"
inl_ma = "ma"
ma_type1 = input.string(title="MA Type 1", defval="hma", options=["sma","ema","zema","dema","tema","wma","vwma","smma","rma","hma","lsma","McGinley"],group=g_ma)
ma_type2 = input.string(title="MA Type 2", defval="McGinley", options=["sma","ema","zema","dema","tema","wma","vwma","smma","rma","hma","lsma","McGinley"],group=g_ma)
use_candles = input.bool(false,title="Colorize Candles")


l1 = input.int(5,title="M1",group=g_ma,inline=inl_ma)
l2 = input.int(15,title="M2",group=g_ma,inline=inl_ma)
l3 = input.int(50,title="M3",group=g_ma,inline=inl_ma)
l4 = input.int(75,title="M4",group=g_ma,inline=inl_ma)
l5 = input.int(100,title="M5",group=g_ma,inline=inl_ma)
l6 = input.int(200,title="M6",group=g_ma,inline=inl_ma)
l7 = input.int(300,title="M7",group=g_ma,inline=inl_ma)
l8 = input.int(500,title="M8",group=g_ma,inline=inl_ma)
l9 = input.int(750,title="M9",group=g_ma,inline=inl_ma)

g_angles = "MA Angles ----------------------------------------------------"
inl_angles = "angles"
i_ang_1 =  input.int(1, title='A1',group=g_angles,inline=inl_angles)
i_ang_2 =  input.int(2, title='A2',group=g_angles,inline=inl_angles)
i_ang_3 =  input.int(3, title='A3',group=g_angles,inline=inl_angles)
i_ang_4 =  input.int(4, title='A4',group=g_angles,inline=inl_angles)
i_ang_5 =  input.int(5, title='A5',group=g_angles,inline=inl_angles)
i_ang_6 =  input.int(7, title='A6',group=g_angles,inline=inl_angles)
i_ang_7 =  input.int(9, title='A7',group=g_angles,inline=inl_angles)
i_ang_8 =  input.int(12, title='A8',group=g_angles,inline=inl_angles)
i_ang_9 =  input.int(14, title='A9',group=g_angles,inline=inl_angles)

g_select = "Select ----------------------------------------------------"
i_sel_ma = input.int(title='MA Select', defval=3, options=[1,2,3,4,5,6,7,8,9], group=g_select)
i_sel_angle = input.int(title="Angle Select", defval=3, options=[1,2,3,4,5,7,10,12,14],group=g_select)


// SYM Info
min_tick = syminfo.mintick
decimals = math.abs(math.log(min_tick) / math.log(10) )
point_value = syminfo.pointvalue
//plot(min_tick, title='Min Tick')
//plot(decimals, title='Decimals')
//plot(point_value, title='Point Value')


get_pip_distance(point1, point2) =>
    diff_points = math.abs( (point1 - point2) )
    pip_value = decimals<3 ? diff_points / syminfo.mintick / 100 : diff_points / syminfo.mintick / 10
    //pip_value = decimals>3 ? diff_points * (math.pow(10, decimals) * syminfo.mintick) : diff_points / syminfo.mintick / 10


angle(_src, len) =>
    rad2degree=180/3.14159265359  //pi 
    ang=rad2degree*math.atan((_src[0] - _src[1]) / ta.atr(len))

// New Bar Multi Timeframe
newbar(res) => ta.change(time(res)) == 0 ? 0 : 1


// Lines and Angles
ma_graph(len,type,smooth_amount) =>
    ma =0.0
    length = 1
    if smooth_amount > 1
        length := len * smooth_amount
    else 
        length := len

    if type == 'sma' // Simple
        ma := ta.sma(ma_src,length) 

    if type == 'ema' // Exponential
        ma := ta.ema(ma_src,length)

    if type == 'zema' // Zero Lag Exponential
        e1 = ta.ema(close,length)
        e2 = ta.ema(e1,length)
        diff = e1 - e2
        ma := e1 + diff 

    if type=="dema" // Double Exponential
        e = ta.ema(ma_src, length)
        ma := 2 * e - ta.ema(e, length)
    if type == 'tema' // Triple Exponential
        ema1 = ta.ema(ma_src, length)
        ema2 = ta.ema(ema1, length)
        ema3 = ta.ema(ema2, length)
        ma := 3 * (ema1 - ema2) + ema3
    if type == 'wma' // Weighted
        ma := ta.wma(ma_src,length)
    if type == 'vwma' // Volume Weighted
        ma := ta.vwma(ma_src,length)
    if type=="smma" // Smoothed
        w = ta.wma(ma_src, length)
        ma := na(w[1]) ? ta.sma(ma_src, length) : (w[1] * (length - 1) + ma_src) / length
    if type == "rma"
        ma := ta.rma(ma_src, length)
    if type == 'hma' // Hull
        ma := ta.hma(ma_src, length)
       // ma := ta.wma(2*ta.wma(ma_src, length/2)-ta.wma(ma_src, length), math.floor(math.sqrt(length) ))
    if type=="lsma" // Least Squares
        ma := ta.linreg(ma_src, length, 0)
    if type=="McGinley"
        mg = 0.0
        mg := na(mg[1]) ? ta.ema(ma_src, length) : mg[1] + (ma_src - mg[1]) / (length * math.pow(ma_src/mg[1], 4))
        ma :=mg

    //if i_ma_use_smooth
        //ma := ta.sma(ma,i_ma_smooth)

    ma


ma_conv(t1, t2) =>
    diff = get_pip_distance(t1, t2)
    conv = show_conv and diff < conv_amount and diff > conv_amount * -1 ? true : false
    [diff, conv]

is_between(p1, p2) =>
    is_inside = p1>p2 and (close<p1) and (open<p1) and (close>p2) and (open>p2) ? 1 : 0
    is_inside


// MA's

// === HMA ===
// ==================================================
[h1,h2,h3,h4,h5,h6,h7,h8,h9] = request.security(syminfo.tickerid, "5", 
 [ma_graph(l1,ma_type1, ma1_multiply1) 
 ,ma_graph(l2,ma_type1, ma1_multiply1)
 ,ma_graph(l3,ma_type1, ma1_multiply1)
 ,ma_graph(l4,ma_type1, ma1_multiply1)
 ,ma_graph(l5,ma_type1, ma1_multiply1)
 ,ma_graph(l6,ma_type1, ma1_multiply1)
 ,ma_graph(l7,ma_type1, ma1_multiply1)
 ,ma_graph(l8,ma_type1, ma1_multiply1)
 ,ma_graph(l9,ma_type1, ma1_multiply1)
 ]
 ,gaps=i_gaps?barmerge.gaps_on:barmerge.gaps_off
 ,lookahead=i_lookahead ? barmerge.lookahead_on : barmerge.lookahead_off ) 

[h1_a,h2_a,h3_a,h4_a,h5_a,h6_a,h7_a,h8_a,h9_a] = request.security(syminfo.tickerid, i_ma_time, 
 [angle(h1,angle_amount)
 ,angle(h2,angle_amount)
 ,angle(h3,angle_amount)
 ,angle(h4,angle_amount)
 ,angle(h5,angle_amount)
 ,angle(h6,angle_amount)
 ,angle(h7,angle_amount)
 ,angle(h8,angle_amount)
 ,angle(h9,angle_amount)
 ]
 ,gaps=i_gaps?barmerge.gaps_on:barmerge.gaps_off
 ,lookahead=i_lookahead ? barmerge.lookahead_on : barmerge.lookahead_off ) 


// === Multi-Timeframe ===
use_heikin = i_use_heikin ? ticker.heikinashi(syminfo.tickerid) : syminfo.tickerid
[h1_m,h2_m,h3_m,h4_m,h5_m,h6_m,h7_m,h8_m,h9_m] = request.security(use_heikin, i_ma_time, 
 [ma_graph(l1,ma_type1, ma1_multiply2)
 ,ma_graph(l2,ma_type1, ma1_multiply2)
 ,ma_graph(l3,ma_type1, ma1_multiply2)
 ,ma_graph(l4,ma_type1, ma1_multiply2)
 ,ma_graph(l5,ma_type1, ma1_multiply2)
 ,ma_graph(l6,ma_type1, ma1_multiply2)
 ,ma_graph(l7,ma_type1, ma1_multiply2)
 ,ma_graph(l8,ma_type1, ma1_multiply2)
 ,ma_graph(l9,ma_type1, ma1_multiply2)
 ]
 ,gaps=i_gaps?barmerge.gaps_on:barmerge.gaps_off
 ,lookahead=i_lookahead ? barmerge.lookahead_on : barmerge.lookahead_off ) 

// Angles
[h1_a_m,h2_a_m,h3_a_m,h4_a_m,h5_a_m,h6_a_m,h7_a_m,h8_a_m,h9_a_m] = request.security(syminfo.tickerid, i_ma_time, 
 [angle(h1_m,angle_amount)
 ,angle(h2_m,angle_amount)
 ,angle(h3_m,angle_amount)
 ,angle(h4_m,angle_amount)
 ,angle(h5_m,angle_amount)
 ,angle(h6_m,angle_amount)
 ,angle(h7_m,angle_amount)
 ,angle(h8_m,angle_amount)
 ,angle(h9_m,angle_amount)
 ]
 ,gaps=i_gaps?barmerge.gaps_on:barmerge.gaps_off
 ,lookahead=i_lookahead ? barmerge.lookahead_on : barmerge.lookahead_off ) 

[h1_h2_diff_m,h1_h2_conv_m] = ma_conv(h1_m,h2_m)
[h2_h4_diff_m,h2_h4_conv_m] = ma_conv(h2_m,h4_m)
[h4_h5_diff_m,h4_h5_conv_m] = ma_conv(h4_m,h5_m)
[h5_h6_diff_m,h5_h6_conv_m] = ma_conv(h5_m,h6_m)
[h7_h8_diff_m,h7_h8_conv_m] = ma_conv(h7_m,h8_m)

// Colorized MA angles
ma_select()=>

    float select = switch i_ma_select
        2 => h2_a_m
        3 => h3_a_m
        4 => h4_a_m
        5 => h5_a_m
        6 => h6_a_m
        7 => h7_a_m
        8 => h8_a_m
        9 => h9_a_m
        => h3_a_m

    ma_a = math.abs(select)
    ma_a := i_ma_use_smooth ? ta.sma(ma_a, i_ma_smooth) : ma_a

    ma_color =  
     ma_a   < i_ang_1 ? #333333
     : ma_a < i_ang_2 ? red  
     : ma_a < i_ang_3 ? orange  
     : ma_a < i_ang_4 ? yellow  
     : ma_a < i_ang_5 ? gray  
     : ma_a < i_ang_6 ? green  
     : ma_a < i_ang_7 ? lime  
     : ma_a < i_ang_8 ? blue  
     : ma_a < i_ang_9 ? aqua 
     : ma_a > i_ang_9 ? white 
     : na

    [ma_color]

[ma_color] = ma_select()


// === EMA ===
// ==================================================
[m1,m2,m3,m4,m5,m6,m7,m8,m9] = request.security(syminfo.tickerid, "", 
 [ma_graph(l1,ma_type2, ma2_multiply1)
 ,ma_graph(l2,ma_type2, ma2_multiply1)
 ,ma_graph(l3,ma_type2, ma2_multiply1)
 ,ma_graph(l4,ma_type2, ma2_multiply1)
 ,ma_graph(l5,ma_type2, ma2_multiply1)
 ,ma_graph(l6,ma_type2, ma2_multiply1)
 ,ma_graph(l7,ma_type2, ma2_multiply1)
 ,ma_graph(l8,ma_type2, ma2_multiply1)
 ,ma_graph(l9,ma_type2, ma2_multiply1)
 ]
 ,gaps=i_gaps?barmerge.gaps_on:barmerge.gaps_off
 ,lookahead=i_lookahead ? barmerge.lookahead_on : barmerge.lookahead_off ) 


[m1_a,m2_a,m3_a,m4_a,m5_a,m6_a,m7_a,m8_a,m9_a] = request.security(syminfo.tickerid, i_ma_time, 
 [angle(m1,angle_amount)
 ,angle(m2,angle_amount)
 ,angle(m3,angle_amount)
 ,angle(m4,angle_amount)
 ,angle(m5,angle_amount)
 ,angle(m6,angle_amount)
 ,angle(m7,angle_amount)
 ,angle(m8,angle_amount)
 ,angle(m9,angle_amount)
 ]
 ,gaps=i_gaps?barmerge.gaps_on:barmerge.gaps_off
 ,lookahead=i_lookahead ? barmerge.lookahead_on : barmerge.lookahead_off ) 

[m2_m4_diff,m2_m4_conv] = ma_conv(m2,m4)
[m4_m5_diff,m4_m5_conv] = ma_conv(m4,m5)
[m5_m6_diff,m5_m6_conv] = ma_conv(m5,m6)
[m7_m8_diff,m7_m8_conv] = ma_conv(m7,m8)

// Multi Timeframe
[m1_m,m2_m,m3_m,m4_m,m5_m,m6_m,m7_m,m8_m,m9_m] = request.security(syminfo.tickerid, i_ma_time, 
 [ma_graph(l1,ma_type2, ma2_multiply2)
 ,ma_graph(l2,ma_type2, ma2_multiply2)
 ,ma_graph(l3,ma_type2, ma2_multiply2)
 ,ma_graph(l4,ma_type2, ma2_multiply2)
 ,ma_graph(l5,ma_type2, ma2_multiply2)
 ,ma_graph(l6,ma_type2, ma2_multiply2)
 ,ma_graph(l7,ma_type2, ma2_multiply2)
 ,ma_graph(l8,ma_type2, ma2_multiply2)
 ,ma_graph(l9,ma_type2, ma2_multiply2)
 ]
 ,gaps=i_gaps?barmerge.gaps_on:barmerge.gaps_off
 ,lookahead=i_lookahead ? barmerge.lookahead_on : barmerge.lookahead_off ) 

[m1_a_m,m2_a_m,m3_a_m,m4_a_m,m5_a_m,m6_a_m,m7_a_m,m8_a_m,m9_a_m] = request.security(syminfo.tickerid, i_ma_time, 
 [angle(m1_m,angle_amount)
 ,angle(m2_m,angle_amount)
 ,angle(m3_m,angle_amount)
 ,angle(m4_m,angle_amount)
 ,angle(m5_m,angle_amount)
 ,angle(m6_m,angle_amount)
 ,angle(m7_m,angle_amount)
 ,angle(m8_m,angle_amount)
 ,angle(m9_m,angle_amount)
 ]
 ,gaps=i_gaps?barmerge.gaps_on:barmerge.gaps_off
 ,lookahead=i_lookahead ? barmerge.lookahead_on : barmerge.lookahead_off ) 

[m2_m4_diff_m,m2_m4_conv_m] = ma_conv(m2_m,m4_m)
[m4_m5_diff_m,m4_m5_conv_m] = ma_conv(m4_m,m5_m)
[m5_m6_diff_m,m5_m6_conv_m] = ma_conv(m5_m,m6_m)
[m7_m8_diff_m,m7_m8_conv_m] = ma_conv(m7_m,m8_m)



// Colorize candles based on MA angles
barcolor(use_candles? ma_color : na)
// Plots
l_width = 2

get_distance = get_pip_distance(h7_m, h8_m)
//plot(get_distance, title='Distance',color=color.new(blue,100))

if newbar(i_ma_time) == 0 and i_plot_ch

    h1_m := h1_m[1]
    h2_m := h2_m[1]
    h3_m := h3_m[1]
    h4_m := h4_m[1]
    h5_m := h5_m[1]
    h6_m := h6_m[1]
    h7_m := h7_m[1]
    h8_m := h8_m[1]
    h9_m := h9_m[1]

    h1_a_m := h1_a_m[1]
    h2_a_m := h2_a_m[1]
    h3_a_m := h3_a_m[1]
    h4_a_m := h4_a_m[1]
    h5_a_m := h5_a_m[1]
    h6_a_m := h6_a_m[1]
    h7_a_m := h7_a_m[1]
    h8_a_m := h8_a_m[1]
    h9_a_m := h9_a_m[1]


ma_get_hl(type, multi) =>

    ma_high = math.max(h1_m, h2_m, h3_m, h4_m, h5_m, h6_m)
    ma_low  = math.min(h1_m, h2_m, h3_m, h4_m, h5_m, h6_m)

    [ma_high, ma_low]


[ema_highest, ema_lowest] = ma_get_hl('ema', true)

f_ma_step_dir(m_angle, obj_time)=>
    var dir = 0.0
    dir := ta.change(m_angle) and m_angle < m_angle[1] ? -1 : ta.change(m_angle) and m_angle > m_angle[1] ? 1 : dir

m1a_dir = f_ma_step_dir(h1_m, i_ma_time) 

// Stepping Direction
plot(m1a_dir, 'Step Dir', color=m1a_dir == 1 ? green : red, display=display.data_window)
// Highest Lowest
plot(ema_highest, 'MA Highest', color=blue, display=display.data_window)
plot(ema_lowest,  'MA Lowest', color=blue, display=display.data_window)
plotshape(ma_type1 == 'ema' and h6_m == ema_highest ? 1 : na , title='M6 Highest', color=ma_color, style=shape.cross, location=location.top, display=display.data_window)
plotshape(ma_type1 == 'ema' and h6_m == ema_lowest ? 1 : na , title='M6 Lowest', color=ma_color, style=shape.cross, location=location.bottom, display=display.data_window)

// Angles
plot(show_angles and h1_a_m ? h1_a_m : na,color=h1_a_m>0?color.new(aqua,100):color.new(orange,100),title="M1 A", style=plot.style_circles, display=display.data_window)
plot(show_angles and h2_a_m ? h2_a_m : na,color=h2_a_m>0 ? color.new(color.green,100): color.new(color.red,100),title="M2 A", style=plot.style_circles, display=display.data_window)
plot(show_angles and h3_a_m ? h3_a_m : na,color=i_ma_select==3 ? color.new(ma_color,100) : color.new(color.blue,100),title="M3 A", style=plot.style_circles, display=display.data_window)
plot(show_angles and h4_a_m ? h4_a_m : na,color=i_ma_select==4 ? color.new(ma_color,100) : color.new(yellow,100),title="M4 A", style=plot.style_circles, display=display.data_window)
plot(show_angles and h5_a_m ? h5_a_m : na,color=i_ma_select==5 ? color.new(ma_color,100) : color.new(orange,100),title="M5 A", style=plot.style_circles, display=display.data_window)
plot(show_angles and h6_a_m ? h6_a_m : na,color=i_ma_select==6 ? color.new(ma_color,100) : color.new(red,100), title="M6 A", style=plot.style_circles, display=display.data_window)
plot(show_angles and h7_a_m ? h7_a_m : na,color=i_ma_select==7 ? color.new(ma_color,100) : color.new(orange,100),title="M7 A", style=plot.style_circles, display=display.data_window)
plot(show_angles and h8_a_m ? h8_a_m : na,color=i_ma_select==8 ? color.new(ma_color,100) : color.new(red,100),title="M8 A", style=plot.style_circles, display=display.data_window)
plot(show_angles and h9_a_m ? h9_a_m : na,color=i_ma_select==9 ? color.new(ma_color,100) : color.new(red,100),title="M9 A", style=plot.style_circles, display=display.data_window)


// Bars Since
i_bars_ma = input.int(14, 'Ma Bars angle number')
h2_bars_sell = ta.barssince(ma_color==white and h2_a_m>0)
h2_bars_buy = ta.barssince(ma_color==white and h2_a_m<0)
// h3_bars_sell = ta.barssince(ma_color==white and h3_a_m>0)
// h3_bars_buy = ta.barssince(ma_color==white and h3_a_m<0)
//h3_bars_sell := h3_bars_sell > 20 ? 0 : h3_bars_sell
//h3_bars_buy := h3_bars_buy > 20 ? 0 : h3_bars_buy
plot(h2_bars_sell, 'H2 bars sell', color=blue, display=display.data_window)
plot(h2_bars_buy, 'H2 bars buy', color=blue, display=display.data_window)

// H1 Dots
i_incre_up_down = 0.5 //input.float(0.5, 'Increment Up Down')
f_ma_angle_dir(m_angle)=>
    ma_angle_direction = m_angle>m_angle[str.tonumber(i_ma_time) * i_incre_up_down] ? 1 : -1

//m1a_dir = f_ma_angle_dir(h1_a_m) 
//plotshape(i_show_shapes and m1a_dir==-1 ? 1 : na , title='H3 Multi Dots', color=ma_color, style=shape.cross, location=location.top)
//plotshape(i_show_shapes and m1a_dir==1 ? 1 : na , title='H3 Multi Dots', color=ma_color, style=shape.cross, location=location.bottom)

//var float hh1_m = 0.0
// hh1_m = request.security(syminfo.tickerid, i_ma_time, ma_graph(l1,ma_type1) )
// plot(show_h1 ? hh1_m : na, 
//  color= i_ma_select==1 ? ma_color : h1_a_m>0?aqua:orange, 
//  title="HH1",style=plot.style_linebr)

//p_h5 = plot(show_h5?h5:na, style=plot.style_line, color= i_ma_select==5 ? ma_color : yellow,title="M5")

// Multi
p_h1_m = plot(h1_m and show_h1?h1_m:na, title="M1", color= i_ma_select==1 ? ma_color : h1_a_m > 0 ? i_ma_col1 : orange, linewidth = i_ma_select==1 ? l_width : 1 )
p_h2_m = plot(h2_m and show_h2?h2_m:na, title="M2", color= i_ma_select==2 ? ma_color : h2_a_m > 0 ? i_ma_col2 : red, linewidth = i_ma_select==2 ? l_width : 1 )
p_h3_m = plot(h3_m and show_h3?h3_m:na, title="M3", color= i_ma_select==3 ? ma_color : h3_a_m > 0 ? i_ma_col3 : aqua, linewidth = i_ma_select==3 ? l_width : 1 )

p_h4_m = plot(h4_m and show_h4?h4_m:na, style=plot.style_line, color= i_ma_select==4 ? ma_color : yellow, title="M4",linewidth=i_ma_select==4?l_width:1,offset = i_offset)

p_h5_m = plot(h5_m and show_h5?h5_m:na, style=plot.style_line, color= i_ma_select==5 ? ma_color : h5_a_m<1 and h5_a_m>-1 ? aqua : h5_a_m>0 ? orange : h5_a_m<0 and h5_m>h6_m ? red : green,title="M5",linewidth=i_ma_select==5?l_width:1,offset = i_offset)

p_h6_m = plot(h6_m and show_h6?h6_m:na, style=plot.style_line, color= i_ma_select==6 ? ma_color : h6_a_m > 0 ? red : lime, title="M6",linewidth=i_ma_select==6?l_width:1,offset = i_offset)

p_h7_m = plot(h7_m and show_h7?h7_m:na, style=plot.style_line, color= i_ma_select==7 ? ma_color : h7_a_m>0 ? orange : h7_a_m<0 and h7_m>h8_m ? red : green,title="M7",linewidth=i_ma_select==7?l_width:1,offset = i_offset)

p_h8_m = plot(h8_m and show_h8?h8_m:na, style=plot.style_line, color= i_ma_select==8 ? ma_color : h8_a_m>0 ? red : lime,title="M8",linewidth=i_ma_select==8?l_width:1,offset = i_offset)

p_h9_m = plot(h9_m and show_h9?h9_m:na, style=plot.style_line, color= i_ma_select==9 ? ma_color : h9_a_m>0 ? red : lime,title="M9",linewidth=i_ma_select==9?l_width:1,offset = i_offset)


// Fills

// H1 and H2
fill(p_h1_m,p_h2_m,title="H1/H2 Conv", color=h1_h2_conv_m and h1_m>h2_m?color.new(red,50) : h1_h2_conv_m and h1_m<h2_m?color.new(green,50) : na)
// H5 and H6
fill(p_h5_m,p_h6_m,title="S5/S6 Conv", color=show_fill and h5_m<h6_m?color.new(green,90): show_fill ? color.new(red,90):na)
fill(p_h7_m,p_h8_m,title="S7/S8 Conv", color=show_fill and h7_m<h8_m?color.new(green,90): show_fill ? color.new(red,90):na)
//Convergence
// fill(p_h2,p_h4,title="M2/M4 Conv", color=h2_h4_conv and h2>h4?color.new(red,70) : h2_h4_conv and h2<h4?color.new(green,70) : na)
fill(p_h5_m,p_h6_m,title="M5/M6 Conv", color=h5_h6_conv_m and h5_m>h6_m?color.new(red,70) : h5_h6_conv_m and h5_m<h6_m?color.new(green,70) : na)
fill(p_h7_m,p_h8_m,title="M7/M8 Conv", color=h7_h8_conv_m and h7_m>h8_m?color.new(red,70) : h7_h8_conv_m and h7_m<h8_m?color.new(green,70) : na)
// plot(h2_h4_diff, title="M2/4 Conv",color=h2_h4_conv and h2>h4?color.new(red,70) : h2_h4_conv and h2<h4?color.new(green,70) : na )
//plot(h5_h6_diff, title="M5/6 Conv",color=h5_h6_conv and h5>h6?color.new(red,70) : h5_h6_conv and h5<h6?color.new(green,70) : na )
//plot(h7_h8_diff, title="M7/8 Conv",color=h7_h8_conv and h7>h8?color.new(red,70) : h7_h8_conv and h7<h8?color.new(green,70) : na )

// Notes MA angles
// Smooth: 5  Angle amount:25. Let M2 1st higher higher or lower lower


// Is Between BARCOLOR
// barcolor(  is_between(h5_m, h6_m) ? #000000 : na, title= 'Bar Color')
// barcolor(  is_between(h6_m,h5_m ) ? #000000 : na, title= 'Bar Color')


// Red or Green Hump 
f_ma_dir(ma_type, multi) =>
    var dir   = 0
    var dir2  = 0

    if ma_type == 'HMA'
        if multi == true 
            dir  := h5_m>h6_m ? 1 : -1
            dir2 := h7_m>h8_m ? 1 : -1
        else 
            dir  := h5>h6 ? 1 : -1
            dir2 := h7>h8 ? 1 : -1

    if ma_type == 'SMA'
        if multi == true 
            dir  := m5_m>m6_m ? 1 : -1
            dir2 := m7_m>m8_m ? 1 : -1
        else 
            dir  := m5>m6 ? 1 : -1
            dir2 := m7>m8 ? 1 : -1

    [dir,dir2]



f_ma_cond(dir, dir2, multi, cond, src) =>

    var in1 = 0 
    var in2 = 0 

    // Is price Outside
    if cond == 'inside'

        if multi == false
            if dir == 1 
                in1 := src<h5 and src>h6 ? 1 : 0
            if dir2 == 1 
                in2 := src<h7 and src>h8 ? 1 : 0
            if dir == -1 
                in1 := src>h5 and src<h6 ? 1 : 0
            if dir2 == -1 
                in2 := src>h7 and src<h8 ? 1 : 0
        else
            if dir == 1 
                in1 := src<h5_m and src>h6_m ? 1 : 0 
            if dir2 == 1 
                in2 := src<h7_m and src>h8_m ? 1 : 0
            if dir == -1 
                in1 := src>h5_m and src<h6_m ? 1 : 0
            if dir2 == -1 
                in2 := src>h7_m and src<h8_m ? 1 : 0

    [in1, in2]


f_ma_cross(m1_t,m2_t) =>
    dir = m1_t>m2_t ? 1 : -1



f_ma_stages(m1_t, m2_t, m3_t, a1, a2, a3) =>

    dir = f_ma_cross(m1_t, m2_t)
    ma_conv = get_pip_distance(m1_t ,m2_t) <= conv_amount ? 1 : 0
    stage = 0

    if dir == 1
        stage :=
         a1>1 and a2<0 and ma_conv == 1 ?   1
         : a1>1 and a1>1 and ma_conv == 1   ? 2
         : a1>1 and a1>1 and ma_conv == 0   ? 3
         : a1<1 and a1>-1 and ma_conv == 0  ? 4 
         : a1<-1 and ma_conv == 0           ? 5
         : a1<1  and ma_conv == 1           ? 6 : 0

    if dir == -1
        stage := 
         a1<-1 and a2>0    ? 1
         : a1<-1 and a2<0 and ma_conv==1 ? 2
         : a1<-1 and a2<0 and ma_conv==0 ? 3 
         : a1>-1 and a1<1 and ma_conv==0 ? 4
         : a1>1 and ma_conv==0           ? 5
         : a1>1 and ma_conv==1           ? 6 : 0

    [dir,stage]

[ma_dir,ma_stage] = f_ma_stages(h5_m, h6_m, h3_m, h5_a_m, h6_a_m, h3_a_m)
[ma_dir2,ma_stage2] = f_ma_stages(h7_m, h8_m, h3_m, h7_a_m, h8_a_m, h3_a_m)
plot(ma_stage, title='HMA Stage', color= ma_dir ==1 ? red : green, display=display.data_window )
//plot(ma_stage2, title='HMA Stage 2', color= ma_dir ==1 ? color.new(red,100) : color.new(green,100) )

// MA Cross
// bars_cross_over = ta.barssince( ta.crossover(h4,h5) ) < 20 ? 1 : 0
// barcolor(bars_cross_over ? lime : na)
// bars_cross = ta.barssince( ta.cross(h3,h5) ) < 20 ? 1 : 0
// barcolor(bars_cross ? yellow : na)

// Plot circles based on ma_dir
plotshape(ma_dir==1, title="MA Direction Circles", location=location.top, color=ma_color, style=shape.circle, size=size.tiny)
plotshape(ma_dir==-1, title="MA Direction Circles", location=location.bottom, color=ma_color, style=shape.circle, size=size.tiny)

// MA - Rising and Falling
ma_RF_length =  120 // str.tonumber(i_ma_time) * 3
ma_RF_source = h2
//ma_falling = ta.falling(ma_RF_source, ma_RF_length)
//ma_rising = ta.rising(ma_RF_source, ma_RF_length)

//plotshape(ma_falling, title='MA Falling', color=red, style=shape.cross, location=location.top)
//plotshape(ma_rising, title='MA Rising', color=green, style=shape.cross, location=location.bottom)

// SMA
// [s_d1, s_d2] = f_ma_dir('SMA', false)
// [s_d1_m, s_d2_m] = f_ma_dir('SMA', true)
// plot(s_d1, 'SMA dir 1', color=color.new(blue, 100))
// plot(s_d2, 'SMA dir 2', color=color.new(blue, 100))
// plot(s_d1_m, 'SMA Multi dir 1', color=color.new(blue, 100))
// plot(s_d2_m, 'SMA Multi dir 2', color=color.new(blue, 100))

// HMA
// [h_d1, h_d2] = f_ma_dir('HMA', false)
// h_col = h_d1 ? red : green
// h_col2 = h_d2 ? red : green
// plot(h_d1, 'HMA dir 1', color=color.new(h_col, 100))
// plot(h_d2, 'HMA dir 2', color=color.new(h_col2, 100))

// Multi
[h_d1_m, h_d2_m] = f_ma_dir('HMA', true)
//[h_in1, h_in2] = f_ma_cond(h_d1_m, h_d2_m, false, "inside", close)
[h_in1_m, h_in2_m] = f_ma_cond(h_d1_m, h_d2_m, true, "inside", h3_m)
h_col_m = h_d1_m == 1 ? red : green
h_col2_m = h_d2_m == 1 ? red : green
//plot(h_d1_m, 'HMA Multi dir 1', color=color.new(h_col_m, 100))
//plot(h_d2_m, 'HMA Multi dir 2', color=color.new(h_col2_m, 100))
// plot(h_in1, 'Inside', color=color.new(h_col_m, 100), style=plot.style_circles)
// plot(h_in2, 'Inside Multi', color=color.new(h_col2_m, 100), style=plot.style_circles)
plot(h_in1_m, 'Inside', color=color.new(h_col_m, 100), style=plot.style_circles, display=display.data_window)
plot(h_in2_m, 'Inside Multi', color=color.new(h_col2_m, 100), style=plot.style_circles, display=display.data_window)

var int flag_u = 0
var int flag_d = 0
// flag_u := h1_a>20 ? 1  : flag_u==1  and h1_a>0 ? 1  : flag_u==1  and h1_a<0 ? 0 : na
// flag_d := h1_a<-10 ? -1 : flag_d==-1 and h1_a<0 ? -1 : flag_d==-1 and h1_a>0 ? 0 : na
// plotshape(flag_u==0 ? 1 : na,title="Flag Up",color=red ,style=shape.circle,location=location.top)
// plotshape(flag_d==0 ? 1 : na,title="Flag Down",color=green ,style=shape.circle,location=location.bottom)

// Buy Green up angle h6
green_up_angle = h5_m<h6_m and h5_a_m>0 and close<h5_m and h3_a_m>-1 and not(h3_m<h5_m and close>h3_m)
//plotshape(green_up_angle ? 1 : na,title="Green Up Angle", color=green ,style=shape.circle,location=location.bottom, size=size.small)

// Candle change
//plotshape(show_candles?close:na,title="Candles Multi",color=red ,style=shape.circle,location=location.top)
candle = close>open?1:-1
// h8 angle down
sell_cond = h8_a_m<0 and close>h8_m
// M2 M4 conv
sell_h2_h4 = h2_m>h4_m and h4_m>h5_m and h4_m>h7_m and h2_a_m<15 and h2_h4_conv_m
sell_cond1  = h3_a_m<0 and h4_a_m<0 and h7_m>h8_m and close>h3_m 
 and not(h2_a_m>0) 
 and not(h7_a_m<0) ? 1 : na
sell_cond2 = h3_a_m<0 and h4_a_m<0 and h7_m>h8_m and close>h4_m and not(h7_a_m>0) ? 1 : na
sell_cond3 = close>h4_m and close>h2_m and close>h1_m and h1_m>h2_m and h2_m>h3_m
//plotshape(sell_cond,title="M8",color=red ,style=shape.circle,location=location.top)

// 15 min

buy_cond = h6_a_m>0 and not(h5_a_m<0) and h1_m<h2_m and (h1_a_m[2]<0 and h1_a_m>0) and close>h1_m  
// 5 min
buy_cond1  = h8_a_m<0 and h6_a_m<0 and h3_m<h2_m and h2_a_m>-5 and close<h2_m
 and not(h5_m>h6_m)
 and not(h3_m>h5_m)
 and not(h1_a_m<-5)
//buy_cond  = h8_a<0 and h6_a<0 and h3_a>0 and close<h3
//buy_cond  = h3<h2 and h2_a>5
buy_cond2  = h4_m>h5_m and close<h4_m and h4_a_m>-3 and h2_a_m>-10
//plotshape(buy_cond,title="Buy 1",color=green ,style=shape.circle,location=location.bottom)



// === Labels ===
// label_txt = "Instrument: " + str.tostring(syminfo.ticker)
// if barstate.islastconfirmedhistory
//     buy = label.new(x=bar_index + 20, y=close, style=label.style_label_left,color=color.blue, textcolor=color.white, size=size.normal,text=label_txt )