// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
//
// © Stratfather
//
// @version=5

var string VERSION = "2022.07.31-r1"

indicator(title = "Trade Pro - Rejection Zone Indicator", overlay = true)

// █████████████████████████████████████████████████████████████████████████████
// █ Constants █████████████████████████████████████████████████████████████████
// █████████████████████████████████████████████████████████████████████████████

var string MA_TYPE_EMA = "EMA (Exponential Moving Average)"
var string MA_TYPE_SMA = "SMA (Simple Moving Average)"
var string MA_TYPE_WMA = "WMA (Weighted Moving Average)"
var string MA_TYPE_HMA = "HMA (Hull Moving Average)"
var string MA_TYPE_RMA = "RMA (Relative Moving Average)"
var string MA_TYPE_SWMA = "SWMA (Symmetrically-Weighted Moving Average)"
var string MA_TYPE_ALMA = "ALMA (Arnaud Legoux Moving Average)"
var string MA_TYPE_VWMA = "VWMA (Volume-Weighted Moving Average)"
var string MA_TYPE_VWAP = "VWAP (Volume-Weighted Average Price)"

// █████████████████████████████████████████████████████████████████████████████
// █ Inputs ████████████████████████████████████████████████████████████████████
// █████████████████████████████████████████████████████████████████████████████

// About ███████████████████████████████████████████████████████████████████████

var string aboutGroup = "About"

// string i_version = input.string(title = "Version", defval = VERSION, options = [VERSION], group = aboutGroup, tooltip = "Current version of the indicator.")

// Fast MA █████████████████████████████████████████████████████████████████████

var float conv = input.float(10.0, title="Conversion")
var string maFastGroup = "Fast MA Settings"

string i_maFastType = input.string(title = "Type", defval = MA_TYPE_EMA, options = [MA_TYPE_EMA, MA_TYPE_SMA, MA_TYPE_WMA, MA_TYPE_HMA, MA_TYPE_RMA, MA_TYPE_SWMA, MA_TYPE_ALMA, MA_TYPE_VWMA, MA_TYPE_VWAP], group = maFastGroup)
int i_maFastLength = input.int(20, minval = 1, title = "Length", group = maFastGroup)
float i_maFastSource = input.source(close, title = "Source", group = maFastGroup)
float i_maFastAlmaOffset = input.float(0.85, title = "ALMA Offset", step = 0.01, group = maFastGroup, tooltip = "Only used when ALMA is selected.")
int i_maFastAlmaSigma = input.int(6, title = "ALMA Sigma", group = maFastGroup, tooltip = "Only used when ALMA is selected.")

// Slow MA █████████████████████████████████████████████████████████████████████

var string maSlowGroup = "Slow MA Settings"

string i_maSlowType = input.string(title = "Type", defval = MA_TYPE_EMA, options = [MA_TYPE_EMA, MA_TYPE_SMA, MA_TYPE_WMA, MA_TYPE_HMA, MA_TYPE_RMA, MA_TYPE_SWMA, MA_TYPE_ALMA, MA_TYPE_VWMA, MA_TYPE_VWAP], group = maSlowGroup)
int i_maSlowLength = input.int(50, minval = 1, title = "Length", group = maSlowGroup)
float i_maSlowSource = input.source(close, title = "Source", group = maSlowGroup)
float i_maSlowAlmaOffset = input.float(0.85, title = "ALMA Offset", step = 0.01, group = maSlowGroup, tooltip = "Only used when ALMA is selected.")
int i_maSlowAlmaSigma = input.int(6, title = "ALMA Sigma", group = maSlowGroup, tooltip = "Only used when ALMA is selected.")

// █████████████████████████████████████████████████████████████████████████████
// █ Functions █████████████████████████████████████████████████████████████████
// █████████████████████████████████████████████████████████████████████████████

calculateMA(_type, _length, _src, _almaOffset, _almaSigma) =>
	switch _type
        MA_TYPE_EMA => ta.ema(_src, _length)
        MA_TYPE_SMA => ta.sma(_src, _length)
        MA_TYPE_WMA => ta.wma(_src, _length)
        MA_TYPE_HMA => ta.hma(_src, _length)
        MA_TYPE_RMA => ta.rma(_src, _length)
        MA_TYPE_SWMA => ta.swma(_src)
        MA_TYPE_ALMA => ta.alma(_src, _length, _almaOffset, _almaSigma)
        MA_TYPE_VWMA => ta.vwma(_src, _length)
        MA_TYPE_VWAP => ta.vwap(_src)
        => na

// █████████████████████████████████████████████████████████████████████████████
// █ Calculations ██████████████████████████████████████████████████████████████
// █████████████████████████████████████████████████████████████████████████████

float maFast = calculateMA(i_maFastType, i_maFastLength, i_maFastSource, i_maFastAlmaOffset, i_maFastAlmaSigma)
float maSlow = calculateMA(i_maSlowType, i_maSlowLength, i_maSlowSource, i_maSlowAlmaOffset, i_maSlowAlmaSigma)
float maSlow2= calculateMA(i_maSlowType, 100, i_maSlowSource, i_maSlowAlmaOffset, i_maSlowAlmaSigma)
float diff   = math.abs(maFast - maSlow)

// █████████████████████████████████████████████████████████████████████████████
// █ Plots █████████████████████████████████████████████████████████████████████
// █████████████████████████████████████████████████████████████████████████████

plotMaFast = plot(maFast, color = #2962FF, title = "Fast MA", linewidth = 2)
plotMaSlow = plot(maSlow, color = #FF6D00, title = "Slow MA", linewidth = 2)
plotMaSlow2     = plot(maSlow2, color = #ffffff, title = "100 MA", linewidth = 2)
plotdiff   = plot(diff, color = color.new(color.blue,100), title = "Diff")

fill(plotMaFast, plotMaSlow, color = diff>conv ? color.new(color.blue,100) : maFast > maSlow ? color.new(color.green, 70) : color.new(color.red, 70), title = "Rejection Zone")
