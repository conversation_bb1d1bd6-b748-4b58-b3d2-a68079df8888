
// ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒* STOP LOSS *▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒
// # ======================== percent2point inputs =========================== #
percent2points(percent) => strategy.position_avg_price * percent / 100 / syminfo.mintick

ActivateStopLoss  = input.bool( defval = true  , title = "Activate Stoploss"                                        , inline = "00" , group = "⛔ SL & TP ⛔")
sl_input          = input.int(  defval = 5     , title = "SL %"                                                     , inline = "01" , group = "⛔ SL & TP ⛔")
tp1_input         = input.int(  defval = 5     , title = "TP 1 %"                                                   , inline = "01" , group = "⛔ SL & TP ⛔")
tp2_input         = input.int(  defval = 10    , title = "TP 2 %"                                                   , inline = "01" , group = "⛔ SL & TP ⛔")
tp3_input         = input.int(  defval = 15    , title = "TP 3 %"                                                   , inline = "01" , group = "⛔ SL & TP ⛔")
trailing_bool     = input.bool( defval = false , title = "Activate Trailing -> TP3 is amount, TP 2 is offset level" , inline = "03" , group = "⛔ SL & TP ⛔")
// # ======================== percent2point inputs =========================== #

// # =========================== SL Calculation ============================== #
sl                = percent2points( sl_input  )
tp1               = percent2points( tp1_input )
tp2               = percent2points( tp2_input )
tp3               = percent2points( tp3_input )

curProfitInPts() =>
    if strategy.position_size > 0
        (high - strategy.position_avg_price) / syminfo.mintick
    else if strategy.position_size < 0
        (strategy.position_avg_price - low) / syminfo.mintick
    else
        0    

calcStopLossPrice(OffsetPts) =>
    if strategy.position_size > 0
        strategy.position_avg_price - OffsetPts * syminfo.mintick
    else if strategy.position_size < 0
        strategy.position_avg_price + OffsetPts * syminfo.mintick
    else
        na        

calcProfitTrgtPrice(OffsetPts) =>
    calcStopLossPrice(-OffsetPts)

getCurrentStage() =>
    var stage = 0
    if strategy.position_size == 0 
        stage := 0
    if stage == 0 and strategy.position_size != 0
        stage := 1
    else if stage == 1 and curProfitInPts() >= tp1
        stage := 2
    else if stage == 2 and curProfitInPts() >= tp2
        stage := 3
    stage

calcTrailingAmountLevel(points) =>
    var float level = na
    level := calcProfitTrgtPrice(points)
    if not na(level)
        if strategy.position_size > 0
            if not na(level[1])
                level := max(level[1], level)
            if not na(level)
                level := max(high, level)
        else if strategy.position_size < 0
            if not na(level[1])
                level := min(level[1], level)
            if not na(level)
                level := min(low, level)

calcTrailingOffsetLevel(points, offset) =>
    float result = na
    amountLevel = calcTrailingAmountLevel(points)
    if strategy.position_size > 0
        trailActiveDiff = amountLevel - calcProfitTrgtPrice(points)
        if trailActiveDiff > 0
            result := trailActiveDiff + calcProfitTrgtPrice(offset)
    else if strategy.position_size < 0
        trailActiveDiff = calcProfitTrgtPrice(points) - amountLevel
        if trailActiveDiff > 0
            result := calcProfitTrgtPrice(offset) - trailActiveDiff
    result

float stopLevel = na
float trailOffsetLevel = na
float profitLevel = trailing_bool ? calcTrailingAmountLevel(tp3) : calcProfitTrgtPrice(tp3)

trailOffsetLevelTmp = calcTrailingOffsetLevel(tp3, tp2)

curStage = getCurrentStage()
if ActivateStopLoss
   if curStage == 1
       stopLevel := calcStopLossPrice(sl)
       strategy.exit("ID LONG", loss = sl, profit = tp3, comment = "SL or TP 3")
       strategy.exit("ID SHORT", loss = sl, profit = tp3, comment = "SL or TP 3")
   else if curStage == 2
       stopLevel := calcStopLossPrice(0)
       strategy.exit("ID LONG", stop = stopLevel, profit = tp3, comment = "BH or TP 3")
       strategy.exit("ID SHORT", stop = stopLevel, profit = tp3, comment = "BH or TP 3")
   else if curStage == 3
       stopLevel := calcStopLossPrice(-tp1)
       if trailing_bool
           trailOffsetLevel := trailOffsetLevelTmp
           strategy.exit("ID LONG", stop = stopLevel, trail_points = tp3, trail_offset = tp3-tp2, comment = "stop tp1 or trailing tp3 with offset tp2")
           strategy.exit("ID SHORT", stop = stopLevel, trail_points = tp3, trail_offset = tp3-tp2, comment = "stop tp1 or trailing tp3 with offset tp2")
       else
           strategy.exit("ID LONG", stop = stopLevel, profit = tp3, comment = "tp1 or tp3")
           strategy.exit("ID SHORT", stop = stopLevel, profit = tp3, comment = "tp1 or tp3")
   else
       strategy.cancel("ID LONG")
       strategy.cancel("ID SHORT")
// # =========================== SL Calculation ============================== #
// URL https://www.tradingview.com/script/jjhUHcje-Stepped-trailing-strategy-example/
// ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒* STOP LOSS *▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒
