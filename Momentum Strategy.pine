//@version=5
// https://www.tradingview.com/pine-script-docs/en/v4/essential/Strategies.html
// https://www.tradingcode.net/tradingview/orders/
strategy("Momentum Strategy - v1", overlay=true)

// Live Trading Sun, 12:39

red = #ff0062
orange = #ff9800
yellow = #FFFF00
green = #4caf50
lime = #00E676
aqua = #00bcd4
blue = #2962ff
dark_blue = #2962ff
violet = #814dff
white = #ffffff
gray = #707070

// Angle
angle(_src, len) =>
    rad2degree = 180 / 3.14159265359  //pi 
    ang = rad2degree * math.atan((_src[0] - _src[1]) / ta.atr(len))
    ang

// Momentum
length = input(5) // 5 NAS 1 BTC
price = close
momentum(seria, length) =>
	mom = seria - seria[length]
	mom
mom0 = momentum(price, length)
mom1 = momentum( mom0, 1)

momState = mom0 > 0 and mom1 > 0 ? 1 : mom0 < 0 and mom1 < 0 ? -1 : 0
plot(momState,color=momState==1 ? green : momState==-1 ? red : gray)


// RSI - Exits
rsiLength   = input( 14 )
overSold    = input( 30 )
overBought  = input( 70 )
rsi         = ta.rsi(close, rsiLength)
goLong      = rsi < overSold
goShort     = rsi > overBought
plot(rsi)

g_GMMA_OSC = 'GMMA OSC ----------------------------------------------------'
inl_g_GMMA_OSC = 'inl-g_GMMA_OSC'


// Use Alternate Anchor TF for MAs 
anchor = 0 //input.int(0, minval=0, maxval=1440, title='Use Alternate Anchor TimeFrame (0=none, max=1440 (mins,D,W)')
gmmaType = 'Guppy' //input.string('Guppy', title='Calculate Oscillator From Which GMMA Sets', options=['Guppy', 'SuperGuppy'])
smoothLen = input.int(3, minval=1, title='Oscillator Smoothing Length (1=none)', group=g_GMMA_OSC)  // 1
signalLen = input.int(13, minval=1, title='GMMA Oscillator Signal Length', group=g_GMMA_OSC)
showZones = input(true, title='Show Bullish/Bearish Zones', group=g_GMMA_OSC)
//
src = close //input(close, title='Source')
angle_amount = 14 //input.int(14, minval=1, title='Angle Len')
show_angles = false //input(title='Show Angles', defval=false)
use_zero_line = true //input(title='Zero Line', defval=true)

//Fast Guppy Avg EMA
GMMAFast(src, mult) =>
    ema1 = ta.ema(src, 3 * mult)
    ema2 = ta.ema(src, 5 * mult)
    ema3 = ta.ema(src, 8 * mult)
    ema4 = ta.ema(src, 10 * mult)
    ema5 = ta.ema(src, 12 * mult)
    ema6 = ta.ema(src, 15 * mult)
    return_1 = ema1 + ema2 + ema3 + ema4 + ema5 + ema6
    return_1

//Slow Guppy Avg EMA
GMMASlow(src, mult) =>
    ema7 = ta.ema(src, 30 * mult)
    ema8 = ta.ema(src, 35 * mult)
    ema9 = ta.ema(src, 40 * mult)
    ema10 = ta.ema(src, 45 * mult)
    ema11 = ta.ema(src, 50 * mult)
    ema12 = ta.ema(src, 60 * mult)
    return_2 = ema7 + ema8 + ema9 + ema10 + ema11 + ema12
    return_2

//Fast SuperGuppy Avg EMA
superGMMAFast(src, mult) =>
    emaF1 = ta.ema(src, 3 * mult)
    emaF2 = ta.ema(src, 5 * mult)
    emaF3 = ta.ema(src, 7 * mult)
    emaF4 = ta.ema(src, 9 * mult)
    emaF5 = ta.ema(src, 11 * mult)
    emaF6 = ta.ema(src, 13 * mult)
    emaF7 = ta.ema(src, 15 * mult)
    emaF8 = ta.ema(src, 17 * mult)
    emaF9 = ta.ema(src, 19 * mult)
    emaF10 = ta.ema(src, 21 * mult)
    emaF11 = ta.ema(src, 23 * mult)
    return_3 = (emaF1 + emaF2 + emaF3 + emaF4 + emaF5 + emaF6 + emaF7 + emaF8 + emaF9 + emaF10 + emaF11) / 11
    return_3

//Slow SuperGuppy Avg EMA
superGMMASlow(src, mult) =>
    emaS1 = ta.ema(src, 25 * mult)
    emaS2 = ta.ema(src, 28 * mult)
    emaS3 = ta.ema(src, 31 * mult)
    emaS4 = ta.ema(src, 34 * mult)
    emaS5 = ta.ema(src, 37 * mult)
    emaS6 = ta.ema(src, 40 * mult)
    emaS7 = ta.ema(src, 43 * mult)
    emaS8 = ta.ema(src, 46 * mult)
    emaS9 = ta.ema(src, 49 * mult)
    emaS10 = ta.ema(src, 52 * mult)
    emaS11 = ta.ema(src, 55 * mult)
    emaS12 = ta.ema(src, 58 * mult)
    emaS13 = ta.ema(src, 61 * mult)
    emaS14 = ta.ema(src, 64 * mult)
    emaS15 = ta.ema(src, 67 * mult)
    emaS16 = ta.ema(src, 70 * mult)
    // average
    return_4 = (emaS1 + emaS2 + emaS3 + emaS4 + emaS5 + emaS6 + emaS7 + emaS8 + emaS9 + emaS10 + emaS11 + emaS12 + emaS13 + emaS14 + emaS15 + emaS16) / 16
    return_4

// Calculate the Multiplier for Anchor MAs.
mult = not timeframe.isintraday or anchor == 0 or timeframe.multiplier <= 0 or timeframe.multiplier >= anchor or anchor > 1440 ? 1 : math.round(anchor / timeframe.multiplier) > 1 ? math.round(anchor / timeframe.multiplier) : 1
mult := timeframe.isintraday or anchor == 0 or timeframe.multiplier <= 0 or timeframe.multiplier >= anchor or anchor > 52 ? mult : math.round(anchor / timeframe.multiplier) > 1 ? math.round(anchor / timeframe.multiplier) : 1

// Select type of Oscillator calculation
gmmaFast = gmmaType == 'Guppy' ? GMMAFast(src, mult) : superGMMAFast(src, mult)
gmmaSlow = gmmaType == 'Guppy' ? GMMASlow(src, mult) : superGMMASlow(src, mult)

// Calculate Oscillator, Smoothed Osc and signal line
gmmaOscRaw = (gmmaFast - gmmaSlow) / gmmaSlow * 100
gmmaOsc = ta.sma(gmmaOscRaw, smoothLen)
gmmaSignal = ta.ema(gmmaOscRaw, signalLen)
gmmaClr = gmmaOsc < gmmaSignal ? red : gmmaOsc > gmmaSignal ? green : gray

// Angles 
gmmaOsc_a = show_angles ? angle(gmmaOsc, angle_amount) * 0.001 : na
gmmaSignal_a = show_angles ? angle(gmmaSignal, angle_amount) * 0.001 : na

// bullish signal rule: 
bullishRule = ta.crossover(gmmaOsc, gmmaSignal)
// bearish signal rule: 
bearishRule = ta.crossunder(gmmaOsc, gmmaSignal)
// current trading State
ruleState = 0
ruleState := bullishRule ? 1 : bearishRule ? -1 : nz(ruleState[1])
bgcolor(showZones ? ruleState == 1 ? color.new(green,90) : ruleState == -1 ? color.new(red,90) : color.new(gray,90) : na, title='Guppy Bullish/Bearish Zones')


trade_dir() =>
    candle  = close>open ? 1 : 0
    dir = 0
    if mom0 > 0 and mom1 > 0
        dir := 1
    if mom0 < 0 and mom1 < 0
        dir := -1

	// Filters
	// if rsi > 70
	// 	dir := 0
	//if dir==1

    dir

direction = trade_dir()


// SMA
// m50 = ta.sma(price,50)
// m50_a = angle(m50,3)
// // HMA
m8 = ta.hma(close,500)
m8_a = angle(m8,3)
m8_m = request.security(syminfo.tickerid, "5", m8)
m8_a_m = request.security(syminfo.tickerid, "5", m8_a)
plot(m8_m,title="m8",color=m8_a_m>0?red:green)

// Plots
// plot(m50, color=m50_a>0 ? red : green)
// plot(m8, color=m8_a>0 ? red : green)
//plot(m8_a, color=m8_a>0 ? red : green)

cd=close>open?1:0
if (direction==-1)
	strategy.entry("MomLE", strategy.long,stop= cd==1 ?high+syminfo.mintick:low-syminfo.mintick, comment="MomLE")
else
	strategy.cancel("MomLE")
if (direction==1)
	//strategy.close(id="MomLE",comment="Close \n MomLE ")
	strategy.entry("MomSE", strategy.short, stop=low-syminfo.mintick, comment="MomSE")
else
	strategy.cancel("MomSE")


// if direction==1
// 	strategy.entry("MomLE", strategy.long, stop=high+syminfo.mintick, comment="MomLE" )
// 	if rsi > 70
// 		strategy.close(id="MomLE")
//strategy.entry("MomSE", strategy.short, when=direction==-1,stop=low-syminfo.mintick, comment="MomSE")

// b_color = direction==1 ? aqua : direction==-1? orange : na
// barcolor(b_color)

//plot(high+syminfo.mintick, title="Stop Loss")
//plot(strategy.equity, title="equity", color=color.new(red,90), style=plot.style_areabr)





// Notes

// The strategy works by reversing longs and shorts. 
// No stop loss is used
// 10 days worth of backtesting on the 1 min chart
// It starts by making a long entry then a sell entry to close the previous long trade. Then immediately makes a short entry and Vice Versa
// Are you sure you aren't getting these signals through MT4?
// You can't manually trade this especially with pyramiding
// I think slippage would be the biggest issue
// Commissions and slippage do effect the strategy a lot
	// 1 tick == 0.01% or 0.0001

//CRYPTOS
// I remeber there was an issue with shorting Cryptos need to research
// Can't short with Bitstamp - No margin lending
// KuCoin you can short crypto and better spreads
// https://www.youtube.com/watch?v=rhWroaDii1w&t=16s
// https://www.youtube.com/watch?v=2qMNf14EtME


// Why does this seem to work better with Cryptos. There is more volatility in cryptos so short term movements have more ticks / percentage to gain
// Which broker does FTMO use for Cryptos, hopefully KuCoin. Ask them if you can margin trade if they do margin lending for shorting Cryptos
// Does FTMO allow scalping. Their whole concept are good traders
// Are you sure you can even use this BOT with the broker