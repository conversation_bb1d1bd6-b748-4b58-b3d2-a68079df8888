//@version=4
study("MA - Multi", overlay=true)

g_gg = "Global ----------------------------------------------------"
inl_gg = "gg"
ma_type = input(title="MA Type", defval="hma", options=["ema","dema","tema","wma","vwma","smma","rma","hma","lsma","McGinley"],group=g_gg,inline=inl_gg)
show_s2a = input(title="S2 Angle", type=input.bool, defval=false,group=g_gg,inline=inl_gg)
show_bars2 = input(title="Bars 2", type=input.bool, defval=false,group=g_gg,inline=inl_gg)

g_cb = "SSL ----------------------------------------------------"
inl_cb = "cb"
show_s1 = input(title="S1", type=input.bool, defval=false,group=g_cb,inline=inl_cb)
show_s2 = input(title="s2", type=input.bool, defval=true,group=g_cb,inline=inl_cb)
show_s3 = input(title="s3", type=input.bool, defval=false,group=g_cb,inline=inl_cb)
show_s4 = input(title="S4", type=input.bool, defval=true,group=g_cb,inline=inl_cb)
show_s5 = input(title="S5", type=input.bool, defval=false,group=g_cb,inline=inl_cb)
show_s6 = input(title="S6", type=input.bool, defval=false,group=g_cb,inline=inl_cb)
show_s7 = input(title="S7", type=input.bool, defval=false,group=g_cb,inline=inl_cb)
show_s8 = input(title="S8", type=input.bool, defval=false,group=g_cb,inline=inl_cb)

inl_len = "len1"
s_len1 = input(8, minval=1,title="S1 Length",inline=inl_len) // 8
s_len2 = input(20, minval=1,title="S2 Length",inline=inl_len) // 20
s_len3 = input(50, minval=1,title="S3 Length",inline=inl_len) // 50
s_len4 = input(75, minval=1,title="S4 Length",inline=inl_len) // 75 
s_len5 = input(100, minval=1,title="S5 Length",inline=inl_len) // 100
s_len6 = input(200, minval=1,title="S6 Length",inline=inl_len) // 200
s_len7 = input(300, minval=1,title="S7 Length",inline=inl_len) // 300
s_len8 = input(500, minval=1,title="S8 Length",inline=inl_len) // 500

g_fill = "Plot"
inl_fill = "fill"
inl_conv = "conv"
show_fill = input(title="Show Fill", type=input.bool, defval=true,inline=inl_fill,group=g_fill)
show_conv = input(title="Show Conv", type=input.bool, defval=true,inline=inl_fill,group=g_fill)
conv_amount = input(defval=4, title="Conv Amount", type=input.float, step=1,inline=inl_conv,group=g_fill )
c_type = input(title="Type", defval="USD", options=["USD", "JPY"],inline=inl_conv,group=g_fill)
line_input = 1 //input(1, title="Line width", type=input.integer,inline=inl_fill )

g_multi = "Multi Settings"
inl_multi = "multi"
show_current = input(title="Show Current", type=input.bool, defval=true,inline=inl_multi,group=g_multi)
show_multi = input(title="Show Multi", type=input.bool, defval=true,inline=inl_multi,group=g_multi)
resCustom = input(title="Timeframe", type=input.resolution, defval="15",group=g_multi)
candle_close = input(title="Candle Close", type=input.resolution, defval="15",group=g_multi)
//useCurrentRes = input(true, title="Chart Resolution?",inline=inl_multi,group=g_multi)
angle_input = 14 // input(title="Angle Amount",type=input.integer, defval=14)

red = #ff0062
orange = #ff9800
yellow = #FFFF00
green = #4caf50
lime = #00E676
aqua = #00bcd4
blue = #2962ff
dark_blue = #2962ff
violet = #814dff
white = #ffffff
gray = #707070

angle(_src, len) =>
    rad2degree=180/3.14159265359  //pi 
    ang=rad2degree*atan((_src[0] - _src[1])/atr(len))


// ===  SSL ===
// ==================================================

// Lines and Angles
s_lines(len) =>
    s=0.0
    if ma_type == 'ema' // Exponential
        s := ema(close,len)
    if ma_type=="dema" // Double Exponential
        e = ema(close, len)
        s := 2 * e - ema(e, len)
    if ma_type == 'tema' // Triple Exponential
        ema1 = ema(close, len)
        ema2 = ema(ema1, len)
        ema3 = ema(ema2, len)
        s := 3 * (ema1 - ema2) + ema3
    if ma_type == 'wma' // Weighted
        s := wma(close,len)
    if ma_type == 'vwma' // Volume Weighted
        s := vwma(close,len)
    if ma_type=="smma" // Smoothed
        w = wma(close, len)
        s := na(w[1]) ? sma(close, len) : (w[1] * (len - 1) + close) / len
    if ma_type == "rma"
        s := rma(close, len)
    if ma_type == 'hma' // Hull
        s := wma(2*wma(close, len/2)-wma(close, len), round(sqrt(len)))
    if ma_type=="lsma" // Least Squares
        s := linreg(close, len, 0)
    if ma_type=="McGinley"
        mg = 0.0
        mg := na(mg[1]) ? ema(close, len) : mg[1] + (close - mg[1]) / (len * pow(close/mg[1], 4))
        s :=mg

    sa = angle(s,angle_input)
    [s,sa]


[s1,s1_a] = s_lines(s_len1)
[s2,s2_a] = s_lines(s_len2)
[s3,s3_a] = s_lines(s_len3)
[s4,s4_a] = s_lines(s_len4)
[s5,s5_a] = s_lines(s_len5)
[s6,s6_a] = s_lines(s_len6)
[s7,s7_a] = s_lines(s_len7)
[s8,s8_a] = s_lines(s_len8)

// Diff and Convergence
s_conv(t1, t2) =>
    //syminfo.currency == 'NZD' ? 0.00170 : syminfo.currency == 'JPY'
    boost = c_type=="USD" ? 1000 : 100
    diff = (t1 - t2) * boost
    conv = show_conv and diff<conv_amount and diff>(conv_amount * -1) ? true : false
    [diff,conv]

[s2_s4_diff,s2_s4_conv] = s_conv(s2,s4)
[s4_s5_diff,s4_s5_conv] = s_conv(s4,s5)
[s5_s6_diff,s5_s6_conv] = s_conv(s5,s6)
[s7_s8_diff,s7_s8_conv] = s_conv(s7,s8)


//plot(tmp_diff, title="Base Currency", color=color.new(blue, 100) )

// Plot
// S4 S5 S6 convergence
// ssl_diff = abs(s4_s5_diff)<1.5 and abs(s5_s6_diff)<1.5 ? 1 : na
// ssl_conv = plot(s4_s5_diff, title="s4 diff", color=color.new(blue,100), style=plot.style_circles)
// plot(s5_s6_diff, title="s5 diff", color=color.new(blue,100), style=plot.style_circles)
// plotshape(show_current and ssl_diff? 1 : na ,title="S4 Conv",color=white,style=shape.cross,location=location.top)

s1_f = plot(show_current and show_s1?s1:na, color=aqua , title="S1", linewidth=line_input)
s2_f = plot(show_current and show_s2?s2:na, color=white , title="S2", linewidth=2)
s3_f = plot(show_current and show_s3?s3:na, color=blue , title="S3")
s4_f = plot(show_current and show_s4?s4:na, color=s4_a>0?yellow:orange , title="S4",linewidth=line_input)
s5_f = plot(show_current and show_s5?s5:na, color=s5_a>0?orange : s5_a<0 and s5>s6 ? red : green, title="S5",linewidth=line_input)
s6_f = plot(show_current and show_s6?s6:na, color=s6_a>0?red:lime , title="S6",linewidth=line_input)
s7_f = plot(show_current and show_s7?s7:na, color=s7_a>0?orange : s7_a<0 and s7>s8 ? red : green , title="S7",linewidth=line_input)
s8_f = plot(show_current and show_s8?s8:na, color=s8_a>0?red:lime, title="S8",linewidth=line_input)
s8_f_a = plot(show_current and show_s8?s8_a:na, color=s8_a>0?color.new(red,100):color.new(lime,100), title="S8 A")

// Fills
fill(s5_f,s6_f,title="S5/S6 Fill", color=show_fill and s5_s6_diff<1?color.new(green,90): show_fill ? color.new(red,90):na)
fill(s7_f,s8_f,title="S7/S8 Fill", color=show_fill and s7_s8_diff<1?color.new(green,90): show_fill ? color.new(red,90):na)
// Conv
fill(s5_f,s6_f,title="S5/S6 Conv", color=s5_s6_conv and s5>s6?color.new(red,70): s5_s6_conv and s5<s6?color.new(green,70) : na)
fill(s7_f,s8_f,title="S7/S8 Conv", color=s7_s8_conv and s7>s8?color.new(red,70): s7_s8_conv and s7<s8?color.new(green,70) : na)



// === MULTI  ===
// ==============
close_m = security(syminfo.tickerid, candle_close, close)
s1_m = security(syminfo.tickerid, resCustom, s1)
s1_a_m = security(syminfo.tickerid, resCustom, s1_a)
s2_m = security(syminfo.tickerid, resCustom, s2)
s2_a_m = security(syminfo.tickerid, resCustom, s2_a)
s3_m = security(syminfo.tickerid, resCustom, s3)
s3_a_m = security(syminfo.tickerid, resCustom, s3_a)
s4_m = security(syminfo.tickerid, resCustom, s4)
s4_a_m = security(syminfo.tickerid, resCustom, s4_a)
s5_m = security(syminfo.tickerid, resCustom, s5)
s5_a_m = security(syminfo.tickerid, resCustom, s5_a)
s6_m = security(syminfo.tickerid, resCustom, s6)
s6_a_m = security(syminfo.tickerid, resCustom, s6_a)
s7_m = security(syminfo.tickerid, resCustom, s7)
s7_a_m = security(syminfo.tickerid, resCustom, s7_a)
s8_m = security(syminfo.tickerid, resCustom, s8)
s8_a_m = security(syminfo.tickerid, resCustom, s8_a)

// Diff
s2_s4_diff_m = security(syminfo.tickerid, resCustom, s2_s4_diff)
s2_s4_conv_m = security(syminfo.tickerid, resCustom, s2_s4_conv)
s5_s6_diff_m = security(syminfo.tickerid, resCustom, s5_s6_diff)
s5_s6_conv_m = security(syminfo.tickerid, resCustom, s5_s6_conv)
s7_s8_diff_m = security(syminfo.tickerid, resCustom, s7_s8_diff)
s7_s8_conv_m = security(syminfo.tickerid, resCustom, s7_s8_conv)

// Plot
//plot(s2_s4_diff_m * 0.5 - 1, title="S2/s4 Diff Multi",color=color.new(blue,50))
s1_f_m = plot(show_multi and show_s1?s1_m:na, color=aqua , title="S1", linewidth=line_input)
s2_f_m = plot(show_multi and show_s2?s2_m:na, color=white , title="S2", linewidth=2)
s3_f_m = plot(show_multi and show_s3?s3_m:na, color=blue , title="S3")
s4_f_m = plot(show_multi and show_s4?s4_m:na, color=s4_a_m>0?yellow:orange , title="S4",linewidth=line_input)
s5_f_m = plot(show_multi and show_s5?s5_m:na, title="S5 Multi",linewidth=line_input, color=s5_a_m>0 ? orange : s5_a_m<0 and s5_m>s6_m ? red : green)
s6_f_m = plot(show_multi and show_s6?s6_m:na, title="S6 Multi",linewidth=line_input, color=s6_a_m>0 ? red : lime)
s7_f_m = plot(show_multi and show_s7?s7_m:na, title="S7 Multi",linewidth=line_input, color=s7_a_m>0 ? orange : s7_a_m<0 and s7_m>s8_m ? red : green)
s8_f_m = plot(show_multi and show_s8?s8_m:na, title="S8 Multi",linewidth=line_input, color=s8_a_m>0 ? red : lime)

// Plot Angles
plot(show_multi and show_s2?s2_a_m:na, title="S2 Multi A", color=s2_a_m>0 ? color.new(red,100) : color.new(lime,100))
s8_f_a_m = plot(show_multi and show_s8?s8_a_m:na, title="S8 Multi A", color=s8_a_m>0 ? color.new(red,100) : color.new(lime,100))

// Fills
fill(s5_f_m,s6_f_m,title="S5/S6 Fill Multi", color=show_fill and s5_s6_diff_m<1?color.new(green,90): show_fill ? color.new(red,90):na)
fill(s7_f_m,s8_f_m,title="S7/S8 Fill Multi", color=show_fill and s7_s8_diff_m<1?color.new(green,90): show_fill ? color.new(red,90):na)
// Conv
fill(s2_f_m,s4_f_m,title="S2/S4 Conv Multi", color=s2_s4_conv_m and s2_m>s4_m?color.new(red,70): s2_s4_conv_m and s2_m<s4_m?color.new(green,70) : na)
fill(s5_f_m,s6_f_m,title="S5/S6 Conv Multi", color=s5_s6_conv_m and s5_m>s6_m?color.new(red,70): s5_s6_conv_m and s5_m<s6_m?color.new(green,70) : na)
fill(s7_f_m,s8_f_m,title="S7/S8 Conv Multi", color=s7_s8_conv_m and s7_m>s8_m?color.new(red,70): s7_s8_conv_m and s7_m<s8_m?color.new(green,70) : na)


// bar color
bc_cond = show_s2a and s2_a_m>s2_a_m[1] and low<s2_m ? 1 : 0
barcolor(bc_cond ? lime : na )
// bc_cond = show_s2a and s2_a_m>s2_a_m[1] and close<s2_m ? 1 : 0
// barcolor(bc_cond ? lime : na )
//plotshape(change(close_m),title="Close Multi",color=#ff0000,style=shape.triangleup,location=location.top)

// Previous State
var state_change = blue
ss_red2 = s5_m>s6_m and s5_a_m>0
ss_orange2 = s5_m>s6_m and s5_a_m<0
ss_lime2 = s5_m<s6_m and s6_a_m>0
ss_green2 = s5_m<s6_m and s6_a_m<0
s5_s6_c = ss_red2 ? red :
 ss_orange2 ? orange :
 ss_lime2 ? lime :
 ss_green2 ? green : na
// if (show_zone and bb_zone>bb_zone[1]) or (show_zone and bb_zone<bb_zone[1])
//     state_change := sqz_color[1]
bar_cond2 = show_bars2 and s5_s6_conv_m ? s5_s6_c : show_bars2 and s5_s6_conv_m==false ? gray : na
barcolor( bar_cond2 )

//plot(s5_a_m,title="S5 Angle",color=s5_a_m>0?color.new(orange, 100):color.new(red,70), style=plot.style_circles)
//plot(s5_s6_diff_m,title="S5/S6 Conv",color=s5_a_m>0?color.new(orange, 100):color.new(red,70), style=plot.style_circles)

