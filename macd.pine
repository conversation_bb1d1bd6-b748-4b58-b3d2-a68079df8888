//@version=4
study(title="MACD", shorttitle="MACD", resolution="")
// Getting inputs
fast_length = 12//input(title="Fast Length", type=input.integer, defval=12) // 12
slow_length = 26//input(title="Slow Length", type=input.integer, defval=26) // 26
signal_length = 9//input(title="Signal Smoothing", type=input.integer, minval = 1, maxval = 50, defval = 9)
// Plot colors
col_grow_above = #26A69A
col_grow_below = #FFCDD2
col_fall_above = #B2DFDB
col_fall_below = #EF5350
col_macd = #0094ff
col_signal = #ff6a00
fast_ma = sma_source ? sma(close, fast_length) : ema(close, fast_length)
slow_ma = sma_source ? sma(close, slow_length) : ema(close, slow_length)
macd = fast_ma - slow_ma
signal = sma_signal ? sma(macd, signal_length) : ema(macd, signal_length)
hist = macd - signal
plot(hist, title="Histogram", style=plot.style_columns, color=(hist>=0 ? (hist[1] < hist ? col_grow_above : col_fall_above) : (hist[1] < hist ? col_grow_below : col_fall_below) ), transp=0 )
plot(macd, title="MACD", color=col_macd, transp=0)
plot(signal, title="Signal", color=col_signal, transp=0)