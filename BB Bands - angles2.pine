//@version=4
study(title="BB Bands - Angles 2", overlay=false, shorttitle="BB Bands - Angles 2")

// two bb bands working together
// large-77 Small - 20 or 33
bb_trans = input(50,title="Bands transparency")
fill_trans = input(80,title="Fill transparency")
use_fill = input(title="Use Fill", type=input.bool, defval=true)
bias = input(title="Bias", type=input.float, defval=89.0,step=0.1)
bias2 = input(title="Bias", type=input.float, defval=-89.0,step=0.1)
use_barcolor = input(title="Enable bar color", type=input.bool, defval=true)
bar_color = input(title="Bar Color", type=input.color, defval=color.yellow)
bar_color2 = input(title="Bar Color", type=input.color, defval=color.aqua)

white = #ffffff
gray = #707070
yellow = #FFFF00
red = #ff0062
aqua = #00bcd4
lime = #00E676

// Gradient 1
g1 = #0045b3
g2 = #ff0062
g3 = #707070
g4 = #00c3ff
g5 = #ffffff
g6 = #FFFF00

// Gradient 1
// g1 = #662400
// g2 = #B33F00
// g3 = #FF6B1A
// g4 = #006663
// g5 = #00B3AD
// g6 = #00f7ef

angle(_src, len) =>
    rad2degree=180/3.14159265359  //pi 
    ang=rad2degree*atan((_src[0] - _src[1])/atr(len))

// ===  Bollinger Bands ===
// ==================================================
BB_length = input(125, title="BB Length 1")
BB_stdDev = 2 //input(2, minval=2.0, maxval=3)
sqz_length = 100 //input(100, minval=2, maxval=200) // 100
sqz_threshold = 76 
basis = sma(close, BB_length)
dev = BB_stdDev * stdev(close, BB_length)
bb_upper = basis + dev
bb_lower = basis - dev
bb_spread = bb_upper - bb_lower
avgspread = sma(bb_spread, sqz_length)
bb_squeeze = 0.00
bb_squeeze := bb_spread / avgspread * 100
bb_diff = (bb_upper - bb_lower) * 1000
basis_angle = angle(basis,3)
bbu_angle = angle(bb_upper,3)
bbl_a = angle(bb_lower,3)
bb_diff_a = angle(bb_diff,3)



bb_zone = bb_squeeze < 53 ? 0 : 
 bb_squeeze < sqz_threshold ? 1 : 
 bb_squeeze < 120 ? 2 :
 bb_squeeze < 160 ? 3 :
 bb_squeeze < 200 ? 4 :
 bb_squeeze < 250 ? 5 :
 bb_squeeze > 250 ? 6 : na
sqz_color = bb_zone == 0 ? g1 :
 bb_zone == 1 ? g2 : 
 bb_zone == 2 ? g3 : 
 bb_zone == 3 ? g4 :  
 bb_zone == 4 ? g5:
 bb_zone == 5 ? g6:
 bb_zone == 6 ? white: na

bb_zones_color =  color.new(sqz_color,bb_trans)

barcolor(use_barcolor==true and (bb_diff_a>bias) ? bar_color:na, title="BB Candles")
barcolor(use_barcolor==true and (bb_diff_a<bias2) ? bar_color2:na, title="BB Candles")
plot(bb_diff_a,title="BB Diff Angle",color=bb_diff_a>bias ? bar_color : bb_diff_a<bias2 ? bar_color2 : color.gray)