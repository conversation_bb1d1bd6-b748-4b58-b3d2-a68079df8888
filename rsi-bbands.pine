//@version=5
indicator(title="RSI BB")

g_time = "TimeFrame"
inl_adx = "1"
rsi_time = input.timeframe("10",title="Timeframe", group=g_time) // 1 min
isBB = true

// Angle
angle(_src, len) =>
    rad2degree = 180 / 3.14159265359  //pi 
    ang = rad2degree * math.atan((_src[0] - _src[1]) / ta.atr(len))
    ang


ma(source, length, type) =>
    switch type
        "SMA" => ta.sma(source, length)
        "Bollinger Bands" => ta.sma(source, length)
        "EMA" => ta.ema(source, length)
        "SMMA (RMA)" => ta.rma(source, length)
        "WMA" => ta.wma(source, length)
        "VWMA" => ta.vwma(source, length)


rsiLengthInput = input.int(14, minval=1, title="RSI Length", group="RSI Settings")
rsiSourceInput = input.source(close, "Source", group="RSI Settings")
maTypeInput = input.string("Bollinger Bands", title="MA Type", options=["SMA", "Bollinger Bands", "EMA", "SMMA (RMA)", "WMA", "VWMA"], group="MA Settings")
maLengthInput = input.int(20, title="MA Length", group="MA Settings") // 14
bbMultInput = input.float(1.5, minval=0.001, maxval=50, title="BB StdDev", group="MA Settings") // 1.0 2.0

rsi_bb()=>
    up = ta.rma(math.max(ta.change(rsiSourceInput), 0), rsiLengthInput)
    down = ta.rma(-math.min(ta.change(rsiSourceInput), 0), rsiLengthInput)
    rsi = down == 0 ? 100 : up == 0 ? 0 : 100 - (100 / (1 + up / down))
    rsiMA = ma(rsi, maLengthInput, maTypeInput)
    isBB = maTypeInput == "Bollinger Bands"
    bbUpperBand = rsiMA + ta.stdev(rsi, maLengthInput) * bbMultInput
    bbLowerBand = rsiMA - ta.stdev(rsi, maLengthInput) * bbMultInput

    [rsi,rsiMA,bbUpperBand,bbLowerBand]

[rsi,rsiMA,bbUpperBand,bbLowerBand] = rsi_bb()

rsi_m = request.security(syminfo.tickerid, rsi_time, rsi )
rsiMA_m = request.security(syminfo.tickerid, rsi_time, rsiMA )
rsiMA_a_m = request.security(syminfo.tickerid, rsi_time, angle(rsiMA_m,14) )
bbUpperBand_m = request.security(syminfo.tickerid, rsi_time, bbUpperBand )
bbLowerBand_m = request.security(syminfo.tickerid, rsi_time, bbLowerBand )

// Plots
change_m = ta.change(rsi_m)
candle_c = change_m and rsi_m>rsi_m[1] ? color.green : change_m and rsi_m<rsi_m[1] ? color.red : color.gray
plot(rsi_m, "RSI", color=candle_c)
plot(rsiMA_m, "RSI-based MA", color=color.yellow)
plot(rsiMA_a_m, "RSIMA Angle", color=color.new(color.yellow,100)  )
plot(ta.mfi(hlc3,rsiLengthInput), color=color.new(color.blue,70)   )


rsiUpperBand = hline(70, "RSI Upper Band", color=#787B86)
hline(50, "RSI Middle Band", color=color.new(#787B86, 50))
rsiLowerBand = hline(30, "RSI Lower Band", color=#787B86)
fill(rsiUpperBand, rsiLowerBand, color=color.rgb(126, 87, 194, 90), title="RSI Background Fill")
fill_bbup = plot(isBB ? bbUpperBand_m : na, title = "Upper Bollinger Band", color=color.green)
fill_bbdown = plot(isBB ? bbLowerBand_m : na, title = "Lower Bollinger Band", color=color.green)
fill(fill_bbup, fill_bbdown, color= isBB ? color.new(color.green, 90) : na, title="Bollinger Bands Background Fill")




// Alerts
cond = rsi_m<bbLowerBand_m and  rsiMA_m<50 ? 10 : na
plot(cond,style=plot.style_circles,color=color.green)

cond2 = rsi_m>bbUpperBand_m and rsiMA_m>50 ? 90 : na
plot(cond2,style=plot.style_circles,color=color.red)