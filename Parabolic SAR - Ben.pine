// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © LonesomeTheBlue

//@version=5
indicator('Parabolic SAR - Ben', overlay=true,max_labels_count=500)

g_sar = 'Parabolic SAR -----------------------------------------------------------'
i_show_sar = input.bool(true, title='Show SAR', group=g_sar)
i_psar_timeframe = input.timeframe('30', "SAR Timeframe", group=g_sar)
i_psar_heikin = input.bool(false,title='Use Heikin Ashi Candles', group=g_sar)
psar_start = input.float(title='Start', defval=0.02, step=0.001, group=g_sar)
psar_inc = input.float(title='Increment', defval=0.02, step=0.001, group=g_sar)
psar_max = input.float(title='Max Value', defval=0.2, step=0.01, group=g_sar)
putlabel = input(title='Put Labels', defval=false, group=g_sar)
colup = input.color(title='Colors', defval=color.lime, inline='col', group=g_sar)
coldn = input.color(title='', defval=color.red, inline='col', group=g_sar)

angle(_src, len) =>
    rad2degree=180/3.14159265359  //pi 
    ang=rad2degree*math.atan((_src[0] - _src[1]) / ta.atr(len))

// SYM Info
min_tick = syminfo.mintick
var int decimals = int(math.log10(1/min_tick))
decimals := decimals == 2 ? 1 : decimals == 0 ? 1 : decimals
// plot(min_tick, title='Min Tick')
// plot(decimals, title='Decimals')

get_pip_distance(point1, point2) =>
    diff_points = math.abs( (point1 - point2) )
    pip_value = decimals<3 ? diff_points / syminfo.mintick / 100 : diff_points / syminfo.mintick / 10
    //pip_value = decimals>3 ? diff_points * (math.pow(10, decimals) * syminfo.mintick) : diff_points / syminfo.mintick / 10


//psar_m = request.security(syminfo.tickerid, i_psar_timeframe, ta.sar(psar_start, psar_inc, psar_max), lookahead=barmerge.lookahead_on )

psar_m  = 
 i_psar_heikin ? request.security(ticker.heikinashi(syminfo.tickerid), i_psar_timeframe, ta.sar(psar_start, psar_inc, psar_max) ) 
 : request.security(syminfo.tickerid, i_psar_timeframe, ta.sar(psar_start, psar_inc, psar_max) )

close_m = i_psar_heikin ? request.security(ticker.heikinashi(syminfo.tickerid), i_psar_timeframe, close ) 
 : request.security(syminfo.tickerid, i_psar_timeframe, close )

psar_dir  = psar_m < close_m ? 1 : -1
psar_dist = get_pip_distance(psar_m,psar_dir==1 ? close : open)
psarColor = psar_dir == 1 ? colup : coldn
plot(i_show_sar ? psar_m : na, title="PSAR", style=plot.style_circles, color=psarColor, linewidth=2)
plot(psar_dist, title='SAR Distance', color=color.new(psarColor,100))
//plot(i_show_sar ? psar_m : na, title='Parabolic SAR', color=trend > 0 ? colup : coldn, linewidth=2, style=plot.style_circles)
bs_sar = ta.barssince(ta.change(psar_dir))
p_bs_sar = plot(bs_sar, 'Bars Since', color.new(psarColor,100) ) 

psar_sell = psar_dir==-1 and close>open and high>psar_m ? 1 : 0
psar_buy = psar_dir==1 and open>close and low<psar_m ? 1 : 0
plotshape(psar_sell, title="Strong", color=coldn , style=shape.circle, location=location.top, size=size.tiny)
plotshape( psar_buy, title="Strong",color=colup, style=shape.circle, location=location.bottom, size=size.tiny)