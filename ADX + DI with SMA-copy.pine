//@version=4
study("ADX and DI with SMA Copy")
// Colors

adx_len = input(9,title="Length") // 14
adx_line = input(20,title="threshold") // 20
adx_avg = input(8,title="SMA") // 10
adx_top = input(50,title="High") // 41
adx_high = input(39.5,title="High") // 41
adx_mid = input(title="Mid", defval=33)
adx_center = input(title="Center", defval=20)
adx_low = input(title="Low", defval=12)
use_cond1 = true // input(title="Show Condition 1", type=input.bool, defval=true)
use_cond2 = true // input(title="Show Condition 2", type=input.bool, defval=true)
use_cond3 = true // input(title="Show Condition 3", type=input.bool, defval=true)
use_cond4 = false // input(title="Show Condition 4", type=input.bool, defval=false)
use_cond5 = false // input(title="Show Condition 5", type=input.bool, defval=false)
// Show adx
show_di_plus = input(title="Di Plus", type=input.bool, defval=true)
show_di_minus = input(title="Di Minus", type=input.bool, defval=true)
show_adx = input(title="ADX", type=input.bool, defval=true)
show_adx_sma = input(title="ADX SMA", type=input.bool, defval=true)

g_adx_time = ""
inl_adx = "1"
use_adx_curr = input(title="Show Current", type=input.bool, defval=true,inline=inl_adx,group=g_adx_time)
use_adx_multi = input(title="Show Multi", type=input.bool, defval=true,inline=inl_adx,group=g_adx_time)
adx_time = input(title="Timeframe", type=input.resolution, defval="15", group=g_adx_time)

var float smooth_tr = 0
var float smooth_di_plus = 0
var float smooth_di_minus = 0

TrueRange = max(max(high-low, abs(high-nz(close[1]))), abs(low-nz(close[1])))
DI_plus = high-nz(high[1]) > nz(low[1])-low ? max(high-nz(high[1]), 0): 0
DI_minus = nz(low[1])-low > high-nz(high[1]) ? max(nz(low[1])-low, 0): 0
smooth_tr := nz(smooth_tr[1]) - (nz(smooth_tr[1])/adx_len) + TrueRange
smooth_di_plus := nz(smooth_di_plus[1]) - (nz(smooth_di_plus[1])/adx_len) + DI_plus
smooth_di_minus := nz(smooth_di_minus[1]) - (nz(smooth_di_minus[1])/adx_len) + DI_minus

di_plus = smooth_di_plus / smooth_tr * 100
di_minus = smooth_di_minus / smooth_tr * 100
DX = abs(di_plus-di_minus) / (di_plus+di_minus)*100
adx = sma(DX, adx_len)
adx_sma = sma(adx, adx_avg)
textcolor= color.new(color.white,50)


p_di_plus = plot(use_adx_curr and di_plus and show_di_plus==true?di_plus:na, color=color.new(#ff0000, 50), title="DI+")
p_di_minus = plot(use_adx_curr and di_minus and show_di_minus==true?di_minus:na, color=color.new(#3b8c38, 50), title="DI-")
p_adx = plot(use_adx_curr and adx and show_adx==true?adx:na, color=color.new(#ffeb3b, 50), title="ADX")
p_adx_sma = plot(use_adx_curr and adx_sma and show_adx_sma==true?adx_sma:na, color=color.new(#ffffff, 50), title="SMA")
//plot(DX, color=color.new(#00bcd4, 50), title="DX")


// Multi
// di_plus_m = security(syminfo.tickerid, adx_time, di_plus)
// di_minus_m = security(syminfo.tickerid, adx_time, di_minus)
// DX_m = security(syminfo.tickerid, adx_time, DX)
// adx_m = security(syminfo.tickerid, adx_time, adx)
// adx_sma_m = security(syminfo.tickerid, adx_time, adx_sma)

// plot(use_adx_multi and di_plus_m and show_di_plus==true?di_plus:na, color=color.new(#ff0000, 50), title="DI+")
// plot(use_adx_multi and di_minus_m and show_di_minus==true?di_minus:na, color=color.new(#3b8c38, 50), title="DI-")
// plot(use_adx_multi and adx_m and show_adx==true?adx:na, color=color.new(#ffeb3b, 50), title="ADX")
// plot(use_adx_multi and adx_sma_m and show_adx_sma==true?adx_sma:na, color=color.new(#ffffff, 50), title="SMA")
//plot(DX, color=color.new(#00bcd4, 50), title="DX")


// Fill DI + and DI -
fill(p_di_plus,p_di_minus,title="DI Fill", color=di_plus>di_minus?color.new(#ff0000, 80) : color.new(#3b8c38, 80))
// Buy
b_cond01 = di_minus>adx_high and di_minus>di_minus[1] and adx>di_minus and
 adx_sma>adx_high and adx>adx[1] ? 1 : na
b_cond02 = di_minus>adx_high and di_plus<adx_low and adx_sma>adx_high and adx_sma>adx and di_minus>di_minus[1] ? 1 : na
b_cond03 = di_minus>adx_high and di_minus>di_minus[1] and di_plus<adx_center and 
 adx<adx_mid and adx>adx_sma ? 1 : na
b_cond04 = di_plus>adx_sma and adx<adx_sma and adx>di_minus ? 1 : na
//b_cond04 = adx>adx_high and adx_sma>adx_mid and di_plus<adx_low ? 1 : na
plotshape(use_cond1 and b_cond01?1:na, style=shape.circle,location=location.bottom,color=#008800,text="1",textcolor=textcolor)
plotshape(use_cond2 and b_cond02?1:na, style=shape.circle,location=location.bottom,color=#008800,text="2",textcolor=textcolor)
plotshape(use_cond3 and b_cond03?1:na, style=shape.circle,location=location.bottom,color=#008800,text="3",textcolor=textcolor)
//plotshape(b_cond04?1:na, style=shape.circle,location=location.bottom,color=#008800,text="4",textcolor=textcolor)

// Sell
s_cond01 = di_plus>adx_high and di_plus>di_plus[1] and adx>di_plus and
 adx_sma>adx_high and adx>adx[1] ? 1 : na
s_cond02 = di_plus>adx_high and di_minus<adx_low and adx_sma>adx_high and 
 adx_sma>adx and di_plus>di_plus[1] ? 1 : na
//s_cond01 = adx>adx_sma and adx_sma>adx_high and adx_sma>di_plus and di_minus<adx_low and di_plus>di_plus[1] ? 1 : na
//s_cond01 = di_plus>adx_mid and (adx_sma>adx_high or adx_sma<adx_low) ? 1 : na
//s_cond02 = adx_sma>adx and di_plus>adx and di_plus>adx_mid and di_minus<adx_low ? 1 : na
//s_cond02 = adx_sma>adx_high and di_minus<adx_low ? 1 : na
s_cond03 = di_plus>adx_high and di_plus>di_plus[1] and di_minus<adx_center and 
 adx<adx_mid and adx>adx_sma ? 1 : na
//s_cond03 = di_plus>adx_mid and di_minus<adx_low ? 1 : na
s_cond04 = di_minus>adx_sma and adx<adx_sma and adx>di_plus ? 1 : na
plotshape(use_cond1 and s_cond01?1:na, style=shape.circle,location=location.top,color=#ff0000,text="1",textcolor=textcolor)
plotshape(use_cond2 and s_cond02?1:na, style=shape.circle,location=location.top,color=#ff0000,text="2",textcolor=textcolor)
plotshape(use_cond3 and s_cond03?1:na, style=shape.circle,location=location.top,color=#ff0000,text="3",textcolor=textcolor)
//plotshape(s_cond04?1:na, style=shape.circle,location=location.top,color=#ff0000,text="4",textcolor=textcolor)

hline(adx_top,title="ADX Top",color=color.new(color.white, 70))
hline(adx_high,title="ADX High",color=color.new(color.yellow, 50),linestyle=hline.style_solid)
hline(adx_mid,title="ADX Mid",color=color.new(#ff0000, 50),linestyle=hline.style_solid)
hline(adx_mid,title="ADX Mid",color=color.new(#ff0000, 50),linestyle=hline.style_solid)
hline(adx_line, color=color.new(#ffffff, 50), linestyle=hline.style_dashed)
hline(adx_low, color=color.new(color.yellow, 50), linestyle=hline.style_solid)
//plotshape(DIMinus>34? 1 : na, title="Crossup", color=green, location = location.abovebar, style=shape.diamond, textcolor=green, size=size.small)
