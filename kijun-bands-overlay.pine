//@version=4
study(title = "Kijun sen on Bollinger Bands", shorttitle="Kijun+BB",overlay=true)

show_entry = input(title="Show Entry", type=input.bool, defval=true)

red = #ff0062
aqua = #00bcd4
yellow = #FFFF00
orange = #ff9800
green = #4caf50
white = #ffffff
blue = #0000ff
gray = #707070
sell_color = #ff0062
buy_color = #00c3ff

angle(_src, len) =>
    rad2degree=180/3.14159265359  //pi 
    ang=rad2degree*atan((_src[0] - _src[1])/atr(len))


// ===  EMA's ===
// ==================================================
ema_200 = ema(close, 200)
ema_200_dev = 0.27 * stdev(close, 200)
ema_200_upper = ema_200 + ema_200_dev
ema_200_lower = ema_200 - ema_200_dev
ema_200_outer_dev = 0.23 * stdev(close, 200)
ema_200_outer_upper = ema_200_upper + ema_200_outer_dev
ema_200_outer_lower = ema_200_lower - ema_200_outer_dev
ema_len_f = input(5, minval=1, title="EMA Length") //6
ema_fast = ema(close, ema_len_f)


// ===  ATR ===
// ==================================================
atrlen = 14 //input(14, "ATR Period")
atr_mult = input(1.25, "ATR Mult", step = 0.1) // 1.15
atr_slen = wma(tr(true), atrlen)
atr_upper = atr_slen * atr_mult + close
atr_lower = close - atr_slen * atr_mult


// ===  Kijun ===
// ==================================================
middleDonchian(Length) =>
    lower = lowest(Length)
    upper = highest(Length)
    avg(upper, lower)

basePeriods = input(14, minval=1) //26
kijun =  middleDonchian(basePeriods)
xChikou = close
xPrice = close

// ===  SSL ===
// ==================================================
ssl_len = input(60, minval=1,title="SSL Length") //75
SSL = wma(2*wma(close, ssl_len/2)-wma(close, ssl_len), round(sqrt(ssl_len)))
ssl_angle = angle(SSL,3)

// ===  BB ===
// ==================================================
BB_length = input(45, minval=1, maxval=150) // 23
BB_stdDev = input(2, minval=2.0, maxval=3)
sqz_length = input(100, minval=2, maxval=200) // 100
sqz_threshold = 76 
bb_s = kijun
basis = sma(bb_s, BB_length)
dev = BB_stdDev * stdev(bb_s, BB_length)
bb_upper = basis + dev
bb_lower = basis - dev
bb_spread = bb_upper - bb_lower
avgspread = sma(bb_spread, sqz_length)
bb_squeeze = 0.00
bb_squeeze := bb_spread / avgspread * 100
basis_angle = angle(basis,3)
// BB Zones
bb_zone = bb_squeeze < 53 ? 0 : 
 bb_squeeze < sqz_threshold ? 1 : 
 bb_squeeze < 120 ? 2 :
 bb_squeeze < 181 ? 3 :
 bb_squeeze > 181 ? 4 : na
sqz_color = bb_zone == 0 ? #0045b3 :
 bb_zone == 1 ? #ff0062 : 
 bb_zone == 2 ?  gray : 
 bb_zone == 3 ?  #00c3ff : 
 bb_zone == 4 ? white: na

bb_zones_color =  sqz_color


// === MACD Dema === 
// ==================================================
dema_sma = input(7,title='DEMA Short') // 12
dema_lma = input(45,title='DEMA Long') // 26
dema_signal_in = input(13,title='Signal') // 7 // 9 
MMEslowa = ema(close,dema_lma)
MMEslowb = ema(MMEslowa,dema_lma)
DEMAslow = ((2 * MMEslowa) - MMEslowb )
MMEfasta = ema(close,dema_sma)
MMEfastb = ema(MMEfasta,dema_sma)
DEMAfast = ((2 * MMEfasta) - MMEfastb)
dema_line = (DEMAfast - DEMAslow)
MMEsignala = ema(dema_line, dema_signal_in)
MMEsignalb = ema(MMEsignala, dema_signal_in)
dema_signal = ((2 * MMEsignala) - MMEsignalb )
dema_histo = (dema_line - dema_signal)
dema_up = dema_histo > 0 ? 1 : 0
dema_down = dema_histo < 0 ? 1 : 0

plot(bb_zone, title="BB Zones", color=bb_zones_color, style=plot.style_circles)
// Angles
plot(basis_angle, color= white , title="Basis angle",style=plot.style_circles)
plot(ssl_angle, color= white , title="SSL angle",style=plot.style_circles)
plot(dema_histo, title="Dema Histo", color=dema_up ? green : red, style=plot.style_circles)
// Kijun
plot(kijun, color=red, title="Kijun",linewidth=2)
// BB
plot(basis, title="Basis", color=orange)
p1 = plot(bb_upper, "BB Upper ", transp=0, linewidth=1,color=bb_zones_color)
p2 = plot(bb_lower, "BB Lower ", transp=0, linewidth=1,color=bb_zones_color)
fill(p1,p2, bb_zones_color)
plot(atr_upper, "+ATR Upper", color=#ffffff, transp=85)
plot(atr_lower, "-ATR Lower", color=#ffffff, transp=85)
// SSL
plot(SSL, color= white , title="SSL")
// EMA 200 bands large
plot(ema_200, "EMA 200", color=yellow)
e200_up = plot(ema_200_upper, title="EMA 200 Upper", color=#ffe676, linewidth=1,transp=70)
e200_down = plot(ema_200_lower, title="EMA 200 Lower", color=#ffe676, linewidth=1,transp=70)
plot(ema_fast, color=color.lime, title="EMA Fast")


entry_signal() =>
	dir = 0
	trading = 0
    k = kijun

	// Type of candle - Long / Short
	candle = close > open ? 1 : 0

	// === Uptrend ===
	// ==================================================
    if candle == 1 and basis > ema_200 

        if bb_zone == 0
            // Jun 1
            if basis_angle < 1 and ssl_angle > 2 and atr_upper > bb_upper and 
             close > bb_upper and k > SSL and ema_fast > k and dema_down == 1
				dir := -1
				trading := 1
        if bb_zone == 1
            // May 30
            if basis_angle > 2 and ssl_angle < 0 and SSL > ema_fast and 
             atr_lower > k and ema_fast > k
				dir := -1
				trading := 1

        if bb_zone == 3
            // May 21
            if basis_angle > 2 and atr_lower > k and SSL > bb_upper and 
             k < bb_upper and ema_fast > SSL
				dir := -1
				trading := 1

        if bb_zone == 4
            if basis_angle > 2 and atr_lower > k and ema_fast > bb_upper
				dir := -1
				trading := 1
    // Counter
    if candle == 0 and basis > ema_200 

        if bb_zone == 0
            // May 31
            if basis_angle < -2 and atr_upper < k and
             SSL < bb_lower and k < SSL
				dir := 1
				trading := 1

        if bb_zone == 2
            // Jun 1
            if basis_angle < -2 and ssl_angle < 2 and k < bb_lower and 
             ema_fast < k and open < ema_fast and SSL > bb_lower
				dir := 1
				trading := 1

        if bb_zone == 3

            if basis_angle > 2 and ssl_angle < -2 and atr_lower < basis and
             open < basis and SSL > k and k > basis
				dir := 1
				trading := 1

        if bb_zone == 4

            if basis_angle > 2 and atr_lower < basis and k > bb_upper
				dir := 1
				trading := 1

            // Buy - May 24 
            if basis_angle < -2 and k < bb_lower and ema_fast < k
             and SSL < k
				dir := 1
				trading := 1


	// === Downtrend ===
	// ===== BUY =====================================
    if candle == 0 and basis < ema_200 
        
        //ssl_angle_up = ssl_angle > 2 ? 1 : 0
        
        // === Zone 0 ===
        // Buy - May 27
        if bb_zone == 0

            if basis_angle > -2 and SSL < bb_lower and
             k < SSL and ema_fast < SSL and open < ema_fast
				dir := 1
				trading := 1

        // === Zone 2 ===
        if bb_zone == 2
            // May 27
            if atr_lower < bb_upper and k > bb_upper and SSL > k
             and close < ema_fast
				dir := 1
				trading := 1
            // May 25
            if atr_upper > ema_fast and k < bb_lower and ssl_angle < -2 and
             open < ema_fast
				dir := 1
				trading := 1

        // === Zone 3 ===
        if bb_zone == 3
            // May 28
            if basis_angle > 2 and atr_lower < basis and 
             SSL > k and ema_fast < SSL
				dir := 1
				trading := 1

            // May 26
            if basis_angle < -2 and k > bb_lower and 
             SSL < bb_lower and open < ema_fast
				dir := 1
				trading := 1
    // === Counter SELL ===
    if candle == 1 and basis < ema_200 

        // === Zone 0 ===
        // Sell - May 27
        if bb_zone == 0
            if atr_upper > ema_200 and atr_lower > k and 
             SSL > bb_upper and k > SSL and k > bb_upper
				dir := -1
				trading := 1

        // === Zone 2 ===
        if bb_zone == 2
            // Sell - May 27
            if basis_angle < -2 and ssl_angle > 2 and atr_upper > basis and
             k > SSL and ema_fast > k and close < basis
				dir := -1
				trading := 1

        // === Zone 3 ===
        if bb_zone == 3
            // Buy - May 28
            if basis_angle > 2 and SSL > k and 
             open > bb_upper and ema_fast > SSL and ema_fast < bb_upper
				dir := -1
				trading := 1
            // Sell - May 25
            if basis_angle < -2 and atr_upper > basis and close < basis and 
             ssl_angle > 2 and k > SSL
				dir := -1
				trading := 1

    [dir, trading]

[trade_dir, trading] = entry_signal() 

// Buy / Sell indicators
trade_color = trade_dir > 0  ? buy_color : sell_color
plotshape(trade_dir > 0 and show_entry ? 1 : na, title="Entry Buy",transp=20, color=trade_color, location = location.belowbar, style=shape.labelup, text="B", textcolor=color.white, size=size.small)
plotshape(trade_dir < 0 and show_entry ? -1 : na, title="Entry Sell",transp=20, color=trade_color, location = location.abovebar, style=shape.labeldown, text="S", textcolor=color.white, size=size.small)
