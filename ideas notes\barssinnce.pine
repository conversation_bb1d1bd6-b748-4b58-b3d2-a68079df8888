// Check if strategy has no open position
i_barsWait = input.int(20,'Bars Wait')
i_use_bars = input.bool(true,'Use Bars Wait')
var int bars_since_cnt = 0
var lastTradeWasLoss = false
not_trading = (strategy.position_size == 0)


if (strategy.losstrades > strategy.losstrades[1])
    lastTradeWasLoss := true
    
if (strategy.wintrades[0] > strategy.wintrades[1])
    lastTradeWasLoss := false

if (i_use_bars and lastTradeWasLoss and bars_since_cnt < i_barsWait)
    entryLong := 0
    entryShort := 0
    bars_since_cnt := bars_since_cnt + 1

else 
    lastTradeWasLoss := false
    bars_since_cnt := 0

plot(lastTradeWasLoss ? 1 : 0, title='Loss Trade')
plot( bars_since_cnt, title='Bars Since' )