//@version=5
strategy('Marks Strategy', overlay=true, precision=4, initial_capital=100000, default_qty_value=10, currency=currency.USD, process_orders_on_close=true, pyramiding=1,  max_labels_count=500)

red = #ff0062
orange = #ff9800
yellow = #FFFF00
green = #4caf50
lime = #50e600
aqua = #00bcd4
blue = #2962ff
dark_blue = #2962ff
violet = #814dff
white = #ffffff
gray = #707070
black = #000000

// Angle
angle(_src, len) =>
    rad2degree = 180 / 3.14159265359  //pi 
    ang = rad2degree * math.atan((_src[0] - _src[1]) / ta.atr(len))
    ang

// Change
perc_change() =>
    perc = math.abs((1 - close[1] / close) * 10000)
    perc


// g_1min = '1 Minute MA\'s ----------------------------------------------------'
// inl_1min = '1-min'
// inl_1min_len = '1-min-len'
// show_ma_1min = input.bool(title='Show', defval=false, group=g_1min, inline=inl_1min)
// m_1min_len1 = input.int(8, minval=1, title='M1', inline=inl_1min_len) 
// m_1min_len2 = input.int(20, minval=1, title='M2', inline=inl_1min_len) 
// m_1min_len3 = input.int(50, minval=1, title='SMA', inline=inl_1min_len) 

// ma_1min() =>
//     src = close
//     m1 = ta.hma(src,m_1min_len1)
//     m2 = ta.hma(src,m_1min_len2)
//     m3 = ta.sma(src,m_1min_len3)

//     [m1,m2,m3]

// [m1f,m2f,m3f] = ma_1min()

// plot(m1f and show_ma_1min?m1f:na,title="m1",color=#53bad1,linewidth=2)
// plot(m2f and show_ma_1min?m2f:na,title="m2",color=#c04045,linewidth=2)
// plot(m3f and show_ma_1min?m3f:na,title="m3",color=#fdea60,linewidth=2)



// ===  MA's ===
// ==================================================

g_ma = 'MA\' s ----------------------------------------------------'
i_show_ma = input.bool(false, title='Show MAs', group=g_ma)
i_ma_time = input.timeframe(title='Timeframe', defval='', group=g_ma) // chart
i_bars_merge = input.bool(true, title='Lookahead On', group=g_ma)
ma_inl1 = 'len1'
ma_type = input.string(title='Type', defval='hma', options=['sma', 'ema', 'dema', 'tema', 'wma', 'vwma', 'smma', 'rma', 'hma', 'lsma', 'McGinley'], group=g_ma)
show_m1 = input.bool(title='m1', defval=false, group=g_ma, inline=ma_inl1)
show_m2 = input.bool(title='m2', defval=true, group=g_ma, inline=ma_inl1)
show_m3 = input.bool(title='m3', defval=false, group=g_ma, inline=ma_inl1)
show_m4 = input.bool(title='m4', defval=true, group=g_ma, inline=ma_inl1)
show_m5 = input.bool(title='m5', defval=false, group=g_ma, inline=ma_inl1)
show_m6 = input.bool(title='m6', defval=false, group=g_ma, inline=ma_inl1)
show_m7 = input.bool(title='m7', defval=false, group=g_ma, inline=ma_inl1)
show_m8 = input.bool(title='m8', defval=true, group=g_ma, inline=ma_inl1)

ma_inl2 = 'len2'
m_len1 = input.int(5, minval=1, title='M1', inline=ma_inl2)  // 8
m_len2 = input.int(10, minval=1, title='M2', inline=ma_inl2)  // 20
m_len3 = input.int(50, minval=1, title='M3', inline=ma_inl2)  // 50
m_len4 = input.int(75, minval=1, title='M4', inline=ma_inl2)  // 75 
m_len5 = input.int(100, minval=1, title='M5', inline=ma_inl2)  // 100
m_len6 = input.int(200, minval=1, title='M6', inline=ma_inl2)  // 200
m_len7 = input.int(300, minval=1, title='M7', inline=ma_inl2)  // 300
m_len8 = input.int(500, minval=1, title='M8', inline=ma_inl2)  // 500

ma_inl3 = 'len3'
ma_inl4 = 'len4'
ma_inl5 = 'len5'
i_use_smooth  = input.bool(false, title="", inline=ma_inl3)
i_smooth      = input.int(10, title="Smooth", inline=ma_inl3)
i_use_angle  = input.bool(true, title="", inline=ma_inl4)
i_angle =  input.int(6,title="Angle Amount", inline=ma_inl4) // 14 25
i_ma_multiple = input.bool(false, title="", inline=ma_inl5)
i_ma_multi_value = input.int(9, title="Multiply Value", inline=ma_inl5) // 10


g_ma_plot = 'Plot'
i_ma_select = input.int(6, title="Colorized", options=[1,2,3,4,5,6,7,8],group=g_ma_plot)
i_ma_candles = input.bool(false,title='MA Candles',group=g_ma_plot)
inl_fill = 'fill'
inl_conv = 'conv'
show_fill = input.bool(title='Show Fill', defval=true, inline=inl_fill, group=g_ma_plot)
show_conv = input.bool(title='Show Conv', defval=true, inline=inl_fill, group=g_ma_plot)
conv_amount = input.float(defval=25, title='Conv Amount', step=1, inline=inl_conv, group=g_ma_plot) //4 
c_type = input.string(title='Type', defval='NAS', options=['NAS', 'USD', 'JPY'], inline=inl_conv, group=g_ma_plot)
line_input = 1  //input(1, title="Line width", type=input.integer,inline=inl_fill )
l_width = 2

// g_multi = 'Multi Settings'
// inl_multi = 'multi'

//show_current = input.bool(title='Show Current', defval=false, inline=inl_multi, group=g_multi)
//show_multi = input.bool(title='Show Multi', defval=false, inline=inl_multi, group=g_multi)
//candle_close = input.timeframe(title='Candle Close', defval='', group=g_multi)

//useCurrentRes = input(true, title="Chart Resolution?",inline=inl_multi,group=g_multi)



// Lines and Angles
ma_types(len) =>
    obj = 0.0
    this_src = close
    length = len
    if i_ma_multiple 
        length := i_ma_multi_value * len 
    if ma_type == 'sma' // Simple Moving Average
        obj := ta.sma(this_src,length)
    if ma_type == 'ema' // Exponential
        obj := ta.ema(this_src,length)
    if ma_type=="dema" // Double Exponential
        e = ta.ema(this_src, length)
        obj := 2 * e - ta.ema(e, length)
    if ma_type == 'tema' // Triple Exponential
        ema1 = ta.ema(this_src, length)
        ema2 = ta.ema(ema1, length)
        ema3 = ta.ema(ema2, length)
        obj := 3 * (ema1 - ema2) + ema3
    if ma_type == 'wma' // Weighted
        obj := ta.wma(this_src,length)
    if ma_type == 'vwma' // Volume Weighted
        obj := ta.vwma(this_src,length)
    // if ma_type=="smma" // Smoothed
    //     w = ta.wma(src, length)
    //     ma := na(w[1]) ? ta.sma(src, length) : (w[1] * (length - 1) + src) / length
    if ma_type == "rma"
        obj := ta.rma(this_src, length)
    if ma_type == 'hma' // Hull
        obj := ta.wma(2*ta.wma(this_src, length/2)-ta.wma(this_src, length), math.floor(math.sqrt(length) ))
    if ma_type=="lsma" // Least Squares
        obj := ta.linreg(this_src, length, 0)
    // if ma_type=="McGinley"
    //     mg = 0.0
    //     mg := na(mg[1]) ? ta.ema(this_src, length) : mg[1] + (this_src - mg[1]) / (length * math.pow(this_src/mg[1], 4))
    //     obj :=mg

    // if i_use_smooth
    //     ma := ta.sma(ma,i_smooth)

    obj

ma_angles(obj) =>
    this_ma = obj
    if i_use_smooth
        this_ma := ta.sma(this_ma,i_smooth)

    ma_angle = angle(this_ma,i_use_angle ? i_angle : 1)

    ma_angle

m1   = request.security(syminfo.tickerid, i_ma_time, ma_types(m_len1), lookahead=i_bars_merge ? barmerge.lookahead_on : barmerge.lookahead_off )
m1_a = request.security(syminfo.tickerid, i_ma_time, ma_angles(m1), lookahead=i_bars_merge ? barmerge.lookahead_on : barmerge.lookahead_off )
m2   = request.security(syminfo.tickerid, i_ma_time, ma_types(m_len2), lookahead=i_bars_merge ? barmerge.lookahead_on : barmerge.lookahead_off )
m2_a = request.security(syminfo.tickerid, i_ma_time, ma_angles(m2), lookahead=i_bars_merge ? barmerge.lookahead_on : barmerge.lookahead_off )
m3   = request.security(syminfo.tickerid, i_ma_time, ma_types(m_len3), lookahead=i_bars_merge ? barmerge.lookahead_on : barmerge.lookahead_off )
m3_a = request.security(syminfo.tickerid, i_ma_time, ma_angles(m3), lookahead=i_bars_merge ? barmerge.lookahead_on : barmerge.lookahead_off )
m4   = request.security(syminfo.tickerid, i_ma_time, ma_types(m_len4), lookahead=i_bars_merge ? barmerge.lookahead_on : barmerge.lookahead_off )
m4_a = request.security(syminfo.tickerid, i_ma_time, ma_angles(m4), lookahead=i_bars_merge ? barmerge.lookahead_on : barmerge.lookahead_off )
m5   = request.security(syminfo.tickerid, i_ma_time, ma_types(m_len5), lookahead=i_bars_merge ? barmerge.lookahead_on : barmerge.lookahead_off )
m5_a = request.security(syminfo.tickerid, i_ma_time, ma_angles(m5), lookahead=i_bars_merge ? barmerge.lookahead_on : barmerge.lookahead_off )
m6   = request.security(syminfo.tickerid, i_ma_time, ma_types(m_len6), lookahead=i_bars_merge ? barmerge.lookahead_on : barmerge.lookahead_off )
m6_a = request.security(syminfo.tickerid, i_ma_time, ma_angles(m6), lookahead=i_bars_merge ? barmerge.lookahead_on : barmerge.lookahead_off )
m7   = request.security(syminfo.tickerid, i_ma_time, ma_types(m_len7), lookahead=i_bars_merge ? barmerge.lookahead_on : barmerge.lookahead_off )
m7_a = request.security(syminfo.tickerid, i_ma_time, ma_angles(m7), lookahead=i_bars_merge ? barmerge.lookahead_on : barmerge.lookahead_off )
m8   = request.security(syminfo.tickerid, i_ma_time, ma_types(m_len8), lookahead=i_bars_merge ? barmerge.lookahead_on : barmerge.lookahead_off )
m8_a = request.security(syminfo.tickerid, i_ma_time, ma_angles(m8), lookahead=i_bars_merge ? barmerge.lookahead_on : barmerge.lookahead_off )


// Diff and Convergence
ma_conv(t1, t2) =>
    
    float boost = switch syminfo.ticker
        "GBPUSD" => 10000
        "EURUSD" => 10000
        "GBPJPY" => 100
        "BTCUSD" => 0.1
        "NAS100" => 1
        "NDQ100" => 1
        "GOLD"   => 10
        "SILVER" => 100
        => 1

    diff = (t1 - t2) * boost
    conv = show_conv and diff < conv_amount and diff > conv_amount * -1 ? true : false
    [diff, conv]

[m2_m4_diff, m2_m4_conv] = ma_conv(m2, m4)
[m4_m5_diff, m4_m5_conv] = ma_conv(m4, m5)
[m5_m6_diff, m5_m6_conv] = ma_conv(m5, m6)
[m7_m8_diff, m7_m8_conv] = ma_conv(m7, m8)

// Colorized MA angles
ma_select()=>
    float select = switch i_ma_select
        2 => m2_a
        3 => m3_a
        4 => m4_a
        5 => m5_a
        6 => m6_a
        7 => m7_a
        8 => m8_a
        => m6_a

    ma_a = math.abs(select)
    ma_zone =  
     ma_a   < 1 ? 0 
     : ma_a < 2 ? 1 
     : ma_a < 3 ? 2 
     : ma_a < 4 ? 3 
     : ma_a < 5 ? 4 
     : ma_a < 6 ? 5 
     : ma_a > 6 ? 6 
     : na

    ma_color = 
     ma_zone   == 0 ? red 
     : ma_zone == 1 ? orange 
     : ma_zone == 2 ? yellow 
     : ma_zone == 3 ? gray 
     : ma_zone == 4 ? blue 
     : ma_zone == 5 ? lime 
     : ma_zone == 6 ? white : na

    [ma_color]

[ma_color] = ma_select()

// Colorize candles based on MA angles
barcolor(i_ma_candles? ma_color : na)

// Plot Angles
//plot(show_m1 ? m1_a : na, color=m1_a>0?color.new(green,100):color.new(red,100), title='M1 A', linewidth=0)
//plot(show_m2 ? m2_a: na, title='M2 A', color=m2_a> 0 ? color.new(red, 0) : color.new(lime, 0))
//m8_f_a = plot(show_m8 ? m8_a : na, title='m8 Multi A', color=m8_a > 0 ? color.new(red, 100) : color.new(lime, 100))

// Plot
p_m1 = plot(i_show_ma and m1 and show_m1?m1:na,color=m1_a>0?green:red,title="M1",linewidth=i_ma_select==1?l_width:1)
p_m2 = plot(i_show_ma and m2 and show_m2?m2:na,color=white,title="M2",linewidth=i_ma_select==2?l_width:1)
p_m3 = plot(i_show_ma and m3 and show_m3?m3:na,color=blue,title="M3",linewidth=i_ma_select==3?l_width:1)
p_m4 = plot(i_show_ma and m4 and show_m4?m4:na,color=yellow,title="M4",linewidth=i_ma_select==4?l_width:1)
p_m5 = plot(i_show_ma and m5 and show_m5?m5:na,color=m5_a > 0 ? orange : m5_a < 0 and m5 > m6 ? red : green,title="M5",linewidth=i_ma_select==5?l_width:1)
p_m6 = plot(i_show_ma and m6 and show_m6?m6:na,color=i_ma_select==6? ma_color : m6_a > 0 ? red : lime, title="M6",linewidth=i_ma_select==6?l_width:1)
p_m7 = plot(i_show_ma and m7 and show_m7?m7:na,color=i_ma_select==7? ma_color : m7_a>0 ? orange : m7_a<0 and m7>m8 ? red : green,title="M7",linewidth=i_ma_select==7?l_width:1)
p_m8 = plot(i_show_ma and m8 and show_m8?m8:na,color=i_ma_select==8? ma_color : m8_a>0 ? red : lime,title="M8",linewidth=i_ma_select==8?l_width:1)

// Fills
// fill(p_m5, p_m6, title='m5/m6 Fill Multi', color=show_fill and m5_m6_diff < 1 ? color.new(green, 90) : show_fill ? color.new(red, 90) : na)
// fill(p_m7, p_m8, title='m7/m8 Fill Multi', color=show_fill and m7_m8_diff < 1 ? color.new(green, 90) : show_fill ? color.new(red, 90) : na)
// Conv
// fill(p_m2, p_m4, title='m2/m4 Conv Multi', color=m2_m4_conv and m2 > m4 ? color.new(red, 70) : m2_m4_conv and m2 < m4 ? color.new(green, 70) : na)
// fill(p_m5, p_m6, title='m5/m6 Conv Multi', color=m5_m6_conv and m5 > m6 ? color.new(red, 70) : m5_m6_conv and m5 < m6 ? color.new(green, 70) : na)
// fill(p_m7, p_m8, title='m7/m8 Conv Multi', color=m7_m8_conv and m7 > m8 ? color.new(red, 70) : m7_m8_conv and m7 < m8 ? color.new(green, 70) : na)
//plot(m5_m6_diff, title='m5/m6 Diff Multi', linewidth=0, color=color.new(blue, 100))
// 
// Previous State
var state_change = blue
ss_red2 = m5 > m6 and m5_a > 0
ss_orange2 = m5 > m6 and m5_a < 0
ss_lime2 = m5 < m6 and m6_a > 0
ss_green2 = m5 < m6 and m6_a < 0
m5_m6_c = ss_red2 ? red : ss_orange2 ? orange : ss_lime2 ? lime : ss_green2 ? green : na





g_sar = 'Parabolic SAR ----------------------------------------------------'
i_show_sar = input.bool(false, title='Show SAR', group=g_sar)
i_show_labels = input(title='Put Labels', defval=true, group=g_sar)
start = input.float(title='Start', defval=0.02, step=0.001, group=g_sar)
increment = input.float(title='Increment', defval=0.02, step=0.001, group=g_sar)
maximum = input.float(title='Max Value', defval=0.2, step=0.01, group=g_sar)

colup = input.color(title='Colors', defval=color.lime, inline='col', group=g_sar)
coldn = input.color(title='', defval=color.red, inline='col', group=g_sar)

int sar_trend = 0
float sar = 0.0
float ep = 0.0
float af = 0.0

sar_trend := nz(sar_trend[1])
ep := nz(ep[1])
af := nz(af[1])
sar := sar[1]

if sar_trend == 0 and not na(high[1])
    sar_trend := high >= high[1] or low >= low[1] ? 1 : -1
    sar := sar_trend > 0 ? low[1] : high[1]
    ep := sar_trend > 0 ? high[1] : low[1]
    af := start
    af
else
    nextsar = sar
    if sar_trend > 0
        if high[1] > ep
            ep := high[1]
            af := math.min(maximum, af + increment)
            af

        nextsar := sar + af * (ep - sar)
        nextsar := math.min(math.min(low[1], low[2]), nextsar)

        //Reversal
        if nextsar > low
            sar_trend := -1
            nextsar := ep
            ep := low
            af := start
            af
    else
        if low[1] < ep
            ep := low[1]
            af := math.min(maximum, af + increment)
            af

        nextsar := sar + af * (ep - sar)
        nextsar := math.max(math.max(high[1], high[2]), nextsar)

        //Reversal
        if nextsar < high
            sar_trend := 1
            nextsar := ep
            ep := high
            af := start
            af
    sar := nextsar
    sar

plot(i_show_sar and sar ? sar : na, title='Parabolic SAR', color=sar_trend > 0 ? colup : coldn, linewidth=2, style=plot.style_circles)

// Close 
var float change_level = 0.0
var float prev_level   = 0.0
candle = close>open ? 1 : 0
if ta.change(sar_trend) > 0 and i_show_labels and i_show_sar
    // up
    change_level := math.round_to_mintick(sar)
    //prev_level  := math.round_to_mintick(sar)
    label.new(bar_index, sar, text=str.tostring(change_level ), color=colup, style=label.style_label_up, size=size.small)
    //label.new(bar_index - 1, change_level[1], text=str.tostring(change_level[1]), color=coldn, style=label.style_label_down, size=size.small)
if ta.change(sar_trend) < 0 and i_show_labels and i_show_sar
    // Down
    change_level := math.round_to_mintick(sar)
    label.new(bar_index, sar, text=str.tostring(change_level ), color=coldn, style=label.style_label_down, size=size.small)




g_super = 'Supertrend ----------------------------------------------------'
i_show_sup = input.bool(false, title='Show Supertrend', group=g_super)
i_time_sup = input.timeframe(title='Timeframe', defval='', group=g_super)
i_show1 = input.bool(true,title='Sup - [10, 1]', group=g_super)
i_show2= input.bool(true,title='Sup - [11, 2]', group=g_super)
i_show3 = input.bool(true,title='Sup - [12, 3]', group=g_super)
i_show4 = input.bool(true,title='Sup - [7 ,5]', group=g_super) 
i_4a = input.int(7,title='Input A', group=g_super) 
i_4b = input.int(5,title='Input B', group=g_super) 
i_show_close = input.bool(false,title='Show close line', group=g_super)
sup_src = input(hl2, title='Source', group=g_super)
//Multiplier = input.float(title='ATR Multiplier', step=0.1, defval=3.0)
changeATR = input(title='Change ATR Calculation Method?', defval=true, group=g_super)
showsignals = input(title='Show Buy/Sell Signals ?', defval=true, group=g_super)

supertend(p, m) =>
    atr2 = ta.sma(ta.tr, p)
    atr = changeATR ? ta.atr(p) : atr2
    up = sup_src - m * atr
    up1 = nz(up[1], up)
    up := close[1] > up1 ? math.max(up, up1) : up
    dn = sup_src + m * atr
    dn1 = nz(dn[1], dn)
    dn := close[1] < dn1 ? math.min(dn, dn1) : dn
    trend = 1
    trend := nz(trend[1], trend)
    trend := trend == -1 and close > dn1 ? 1 : trend == 1 and close < up1 ? -1 : trend
    b_sig = trend == 1 and trend[1] == -1

    s_sig = trend == -1 and trend[1] == 1

    [trend, up, dn, b_sig, s_sig]


close_m = request.security(syminfo.tickerid, i_time_sup, close ) 
candles_mod = ta.change(close_m)
// [trend, up, dn, b_sig, s_sig]       = request.security(syminfo.tickerid, i_time_sup, supertend(10, 1) )
// [trend2, up2, dn2, b_sig2, s_sig2]  = request.security(syminfo.tickerid, i_time_sup, supertend(11, 2) )
// [trend3, up3, dn3, b_sig3, s_sig3]  = request.security(syminfo.tickerid, i_time_sup, supertend(12, 3) )
[trend4, up4, dn4, b_sig4, s_sig4]  = request.security(syminfo.tickerid, i_time_sup, supertend(i_4a, i_4b) )

//lot(trend4,title='trend 4')
// Uptrend - [7, 5]
upPlot4 = plot(trend4 == 1 and i_show_sup ? up4 : na, title='Supertrend Up', style=plot.style_linebr, linewidth=2, color=i_show4 ? green:na )
plotshape(b_sig4 and candles_mod and i_show_sup ? up4 : na, title='Dot', location=location.absolute, style=shape.circle, size=size.tiny, color=i_show4 ? green:na )
plotshape(b_sig4 and candles_mod and i_show_sup and showsignals ? up4 : na, title='Buy 4', text='Buy 4', location=location.absolute, style=shape.labelup, size=size.tiny, color=i_show4 ? green : na , textcolor=i_show4 ? white : na)
// Downtrend
dnPlot4 = plot(trend4 == -1 and i_show_sup ? dn4 : na , title='Supertrend Down', style=plot.style_linebr, linewidth=2, color=i_show4 ? red :na )
plotshape(s_sig4 and candles_mod and i_show_sup ? dn4 : na, title='Dot', location=location.absolute, style=shape.circle, size=size.tiny, color=i_show4 ? red:na)
plotshape(s_sig4 and candles_mod and showsignals and i_show_sup ? dn4 : na, title='Sell 4', text='Sell 4', location=location.absolute, style=shape.labeldown, size=size.tiny, color=i_show4 ?red :na, textcolor=i_show4 ? white : na)
s4_close = plot(i_show_sup and i_show_close and i_show4 and (b_sig4 or s_sig4) ? close : na, title="S4 Close", style=plot.style_linebr, color=b_sig4 ? green : s_sig4 ? red : na)





g_bb = 'BB Bands ----------------------------------------------------'
i_bb_len = input(20, title='BB Len', group=g_bb)
i_show_back = input.bool(false, title='Show Background')
basis = ta.sma(close, i_bb_len)
dev = 2 * ta.stdev(close, i_bb_len)
bb_upper = basis + dev
bb_lower = basis - dev
bb_spread = bb_upper - bb_lower
//avgspread = sma(bb_spread, sqz_length)
// plot(basis,title="Basis")
// plot(bb_upper,title="bb_upper")
// plot(bb_lower,title="bb_lower")
bb_cond = m1 < bb_lower ? 1 : m1 > bb_upper ? -1 : na
bgcolor(i_show_back and bb_cond == 1 ? aqua : i_show_back and bb_cond == -1 ? orange : na)
//barcolor(bb_cond == 1 ? aqua : bb_cond == -1 ? orange : na)





g_rsi_band = 'RSI + Bands ----------------------------------------------------'
rsi_time = input.timeframe("",title="Timeframe", group=g_rsi_band)
isBB = true
rsiLengthInput = input.int(14, minval=1, title="RSI Length", group=g_rsi_band)
rsiSourceInput = input.source(close, "Source", group=g_rsi_band)
maLengthInput = input.int(20, title="MA Length", group=g_rsi_band) // 14
bbMultInput = input.float(1.0, minval=0.001, maxval=50, title="BB StdDev", group=g_rsi_band) // 2.0

rsi_bb()=>
    rsi_up = ta.rma(math.max(ta.change(rsiSourceInput), 0), rsiLengthInput)
    rsi_down = ta.rma(-math.min(ta.change(rsiSourceInput), 0), rsiLengthInput)
    rsi = rsi_down == 0 ? 100 : rsi_up == 0 ? 0 : 100 - (100 / (1 + rsi_up / rsi_down))
    rsiMA = ta.sma(rsi, maLengthInput)
    bbUpperBand = rsiMA + ta.stdev(rsi, maLengthInput) * bbMultInput
    bbLowerBand = rsiMA - ta.stdev(rsi, maLengthInput) * bbMultInput

    [rsi,rsiMA,bbUpperBand,bbLowerBand]

[rsi,rsiMA,bbUpperBand,bbLowerBand] = rsi_bb()

rsi_m = request.security(syminfo.tickerid, rsi_time, rsi )
rsiMA_m = request.security(syminfo.tickerid, rsi_time, rsiMA )
bbUpperBand_m = request.security(syminfo.tickerid, rsi_time, bbUpperBand )
bbLowerBand_m = request.security(syminfo.tickerid, rsi_time, bbLowerBand )

// Plot
rsi_up = rsi_m>bbUpperBand_m and rsiMA_m>50 ? rsiMA_m : na
plot(rsi_up,title="RSI Up", style=plot.style_circles,color=color.red)
rsi_down = rsi_m<bbLowerBand_m and  rsiMA_m<50 ? rsiMA_m : na
plot(rsi_down,title="RSI Down", style=plot.style_circles,color=color.green)





// ===  Fibo Trend ===
// ==================================================
g_fibo_trend = 'G Fibo Trend ----------------------------------------------------'
inl_fibo = 'inl-fib'
show_gfibo = false //input.bool(false,"Show G Fibo Trend",group=g_fibo_trend)
show_candles = input.bool(false,"Fibo Candles",group=g_fibo_trend)
BackStep = input.int(25,"Analysis Period") // 50
lowerValue = input.float(0.382,"Lower Fibonacci Level",options=[0.236, 0.382, 0.50, 0.618, 0.786])
upperValue = input.float(0.618,"Upper Fibonacci Level",options=[0.236, 0.382, 0.50, 0.618, 0.786])
showFill = input.bool(true,"Show Filling")
changeCandle = input.bool(true,"Change Candle Color")
atr = ta.atr(200)
max = ta.highest(close,BackStep)
min = ta.lowest(close,BackStep)

lowerFib = min + (max-min)*lowerValue
upperFib = min + (max-min)*upperValue
ma_val = input.int(10, title="MA" ) // 6
ma = ta.wma(close,ma_val)

float closeVal = ma
float openVal = ma
color clrToUse = closeVal>upperFib and openVal>upperFib?green:closeVal<lowerFib and openVal<lowerFib?red:yellow

// maxLine = plot(max and show_gfibo?max : na,color=color.green,title="Max")
// minLine = plot(min and show_gfibo?min : na,color=color.red,title="Min")
// LowerFibLine = plot(lowerFib and show_gfibo?lowerFib : na,color=color.rgb(228, 255, 75, 20),title="Lower Fib")
// UpperFibLine = plot(upperFib and show_gfibo?upperFib : na,color=color.rgb(228, 255, 75, 20),title="Upper Fib")
// fill(maxLine,UpperFibLine,color=showFill?color.rgb(0,255,0,changeCandle?95:70):na)
// fill(UpperFibLine,LowerFibLine,color=showFill?color.rgb(228, 255, 75, changeCandle?95:70):na)
// fill(LowerFibLine,minLine,color=showFill?color.rgb(255,0,0,changeCandle?95:70):na)
barcolor(show_candles? clrToUse : na)
//plotcandle(open,high,low,close,"Bar",color=changeCandle?clrToUse:na,wickcolor=changeCandle?clrToUse:na,bordercolor=changeCandle?clrToUse:na)

float LowerRetracement = (max-min)*0.318
float UpperRetracement = (max-min)*0.618



// ===  RSI Wicks ===
// ==================================================
g_rsi = 'RSI Wicks -------------------------------------------------------------'
inl_rsi1 = '1'
inl_rsi2 = '2'
show_rsi = input.bool(title='Show RSI', defval=true, inline=inl_rsi1, group=g_rsi)
rsi_strong = input.bool(title='Strongest', defval=false, inline=inl_rsi1, group=g_rsi)
rsi_len1 = input.int(20, minval=1, title='Length', inline=inl_rsi2, group=g_rsi) // 14
rsi_pos = input.string(title='Position', defval='tb', options=['tb', 't', 'b'], inline=inl_rsi2, group=g_rsi)

g_rsi_time = ''
inl_rsi3 = '3'
use_rsi_curr = input.bool(title='Show Current', defval=true, inline=inl_rsi3, group=g_rsi_time)
use_rsi_multi = input.bool(title='Show Multi', defval=false, inline=inl_rsi3, group=g_rsi_time)
i_rsi_time = input.timeframe(title='Timeframe', defval='15', group=g_rsi_time)

wicks = true  // input(true,  title="Wicks based on stand-alone RSI")
target_up = 70  // input(70, minval=1, title="RSI Up")
target_down = 30  // input(30, minval=1, title="RSI Down")
src_close = close
src_open = open
src_high = high
src_low = low


// Change
perc_change(obj) =>
    perc = math.abs((1 - obj[1] / obj) * 10000)
    perc

rsi_wicks(rsi_len) =>
    norm_close = math.avg(src_close, src_close[1])
    gain_loss_close = ta.change(src_close) / norm_close
    RSI_close = 50 + 50 * ta.rma(gain_loss_close, rsi_len) / ta.rma(math.abs(gain_loss_close), rsi_len)

    norm_open = if wicks == true
        math.avg(src_open, src_open[1])
    else
        math.avg(src_close, src_close[1])
    gain_loss_open = ta.change(src_open) / norm_open
    RSI_open = 50 + 50 * ta.rma(gain_loss_open, rsi_len) / ta.rma(math.abs(gain_loss_open), rsi_len)

    norm_high = if wicks == true
        math.avg(src_high, src_high[1])
    else
        math.avg(src_close, src_close[1])
    gain_loss_high = ta.change(src_high) / norm_high
    RSI_high = 50 + 50 * ta.rma(gain_loss_high, rsi_len) / ta.rma(math.abs(gain_loss_high), rsi_len)

    norm_low = if wicks == true
        math.avg(src_low, src_low[1])
    else
        math.avg(src_close, src_close[1])
    gain_loss_low = ta.change(src_low) / norm_low
    RSI_low = 50 + 50 * ta.rma(gain_loss_low, rsi_len) / ta.rma(math.abs(gain_loss_low), rsi_len)

    [RSI_open, RSI_close, RSI_high, RSI_low]

[RSI_open, RSI_close, RSI_high, RSI_low] = rsi_wicks(rsi_len1)

rw_change = perc_change(RSI_close) / 100
//plot(rw_change,title='Change',color=color.new(blue,100),style=plot.style_circles)
rsi_color = RSI_close > RSI_close[1] ? #65D25Bff : #FE6B69ff

// SELL
up_cond = close > open and (rsi_pos == 'tb' or rsi_pos == 't')
// Strong
plotshape(show_rsi and use_rsi_curr and RSI_open > target_up and up_cond ? 1 : na, title='Up High Multi', color=color.new(#ff0000, 0), style=shape.circle, location=location.top)
plotshape(show_rsi and use_rsi_curr and RSI_open > target_up and up_cond ? 1 : na, title='Up High', color=color.new(#ff0000, 0), style=shape.circle, location=location.top)
// Mid
plotshape(show_rsi and use_rsi_curr and RSI_close > target_up and RSI_open < target_up and up_cond and rsi_strong == false ? 1 : na, title='Up Mid', color=color.new(orange, 0), style=shape.circle, location=location.top)
//plot(perc_change(RSI_close)*.01, title="Percent Change", color=color.new(blue,100),style=plot.style_circles)
// Weak
plotshape(show_rsi and use_rsi_curr and RSI_close < target_up and RSI_high > target_up and up_cond and rsi_strong == false ? 1 : na, title='Up Weak', color=color.new(yellow, 0), style=shape.circle, location=location.top)

// BUY
down_cond = close < open and (rsi_pos == 'tb' or rsi_pos == 'b')
// Weak
plotshape(show_rsi and use_rsi_curr and RSI_close > target_down and RSI_low < target_down and down_cond and rsi_strong == false ? 1 : na, title='Low weak', color=color.new(violet, 0), style=shape.circle, location=location.bottom)
// Mid
plotshape(show_rsi and use_rsi_curr and RSI_close < target_down and RSI_open > target_down and down_cond and rsi_strong == false ? 1 : na, title='Low Mid', color=color.new(blue, 0), style=shape.circle, location=location.bottom)
// Strong
plotshape(show_rsi and use_rsi_curr and RSI_open < target_down and down_cond ? 1 : na, title='Low strong', color=color.new(lime, 0), style=shape.circle, location=location.bottom)






g_trading = 'Trading ----------------------------------------------------'
//account_size    = input.int(100000, title='Account Size', group=g_trading)
i_equity        = input.string("Equity", options=["Initial", "Equity"], title="Initial or Equity",group=g_trading)
i_long_trades   = input.bool(true, title='Long Trades',group=g_trading)
i_short_trades  = input.bool(true, title='Short Trades',group=g_trading)
i_use_pos       = input.bool(true,title="Use Percentage based Position Size",group=g_trading)
i_pctStop       = input(100.0, '% of Risk to Starting Equity Use to Size Positions',group=g_trading) / 100


// Sessions
show_sessions = input.bool(false,title='Sessions', group=g_trading)
As  = input.session(title="Asia", defval="1800-0300")
Lon = input.session(title="London", defval="0300-1200")
Ny  = input.session(title="New York", defval="0800-1800")
Dz  = input.session(title="Deadzone - High Spreads", defval="1645-1830")


inl_color = "inl_color"
inl_color2 = "inl_color2"
c1_on = false //input.bool(true,title="", group=g_trading,inline=inl_color)
c1 = color.new(#00bcd4,90) //input.color(title="Asia", defval=#00bcd4,group=g_trading,inline=inl_color)
c2_on = true //input.bool(true,title="",group=g_trading,inline=inl_color)
c2 = color.new(#00796b,90) //input.color(title="London", defval=#00796b,group=g_trading,inline=inl_color)
c3_on = true //input.bool(true,title="",group=g_trading,inline=inl_color2)
c3 = color.new(#b71c1c,90) //input.color(title="New York", defval=#b71c1c,group=g_trading,inline=inl_color2)
c4_on = true //input.bool(true,title="", group=g_trading,inline=inl_color2)
c4 = color.new(color.purple,90) //input.color(title="Deadzone", defval=#b71c1c,group=g_trading,inline=inl_color2)

Session(sess) => na(time("2",sess)) == false
Asia = Session(As) and c1_on and show_sessions? c1 : na
London = Session(Lon) and c2_on and show_sessions ? c2 : na
NewYork = Session(Ny) and c3_on and show_sessions ? c3 : na
Deadzone = Session(Dz) and c4_on and show_sessions ? c4 : na
bgcolor(Asia)
bgcolor(London)
bgcolor(NewYork)
bgcolor(Deadzone)


// Filters
g_filters = 'Filters'
i_use_filters = input.bool(false, title='Enable Filters',group=g_filters)
i_use_rsi_filter = input.bool(false, title='RSI Filter',group=g_filters)
i_use_time_filter = input.bool(false, title='Time Restraint',group=g_filters)
i_deadzone = input.bool(false, title='Do not take trades during',group=g_filters)

// var int flag_d = na
// flag_d := m2_a<-10 and m2_a<m2_a[1] ? -1 : flag_d==-1 and m2_a>m2_a[1] ? 0 : flag_d==-1 and m2_a<0 ? -1 : na


var int flag_u = 0
var int flag_d = 0
flag_u := m2_a>20 and m2_a>m2_a[1] ? 1 : flag_u==1 and m2_a<m2_a[1] ? 0 : flag_u==1 and m2_a>0 ? 1 : na
flag_d := m2_a<-10 and m2_a<m2_a[1] ? -1 : flag_d==-1 and m2_a>m2_a[1] ? 0 : flag_d==-1 and m2_a<0 ? -1 : na
plotshape(flag_u==0 ? 1 : na,title="Flag Up",color=red ,style=shape.circle,location=location.top)
plotshape(flag_d==0?1:na,title="Flag Down",color=green ,style=shape.circle,location=location.bottom)

i_allow_super = input.bool(true, title='Use Super for Entries')
trade_dir() =>
    c      = close>open ? 1 : 0
    dir         = 0
    entryLong   = 0
    entryShort  = 0
    exitLong    = 0
    exitShort   = 0
    closeAll    = 0
    longSL      = 0.0
    cond        = ''

    red_s       = m5>m6
    green_s     = m5<m6
    green_conv  = m5_m6_conv==1 and green_s 
    green_conv2  = m7_m8_conv==1 
    btw_m7_m8 = ( m7>m8 and (close<m7) and (close>m8) ) or ( m7<m8 and (close>m7) and (close<m8) )? true : false

    // Uptrend
    if i_long_trades

        // M1
        if close<m1 and m2_a>0 and c==0
         and not(m4>m2)
            entryLong := 1
            cond := 'M1'

        // M2
        if close<m2 and m2_a>0 and c==0
         //and not(m4>m2)
            entryLong := 1
            cond := 'M2'
        // M4
        if close<m4 and m2_a>0 and m4_a>0 and c==0
         and not(m4>m2)
            entryLong := 1
            cond := 'M4'

        // Supertrend
        if i_allow_super and c==0 and m1_a>0 and trend4==-1 and clrToUse == red
         and not(m4>m2)
            entryLong := 1
            cond := 'SUPER'

        if m1_a<0 and i_use_filters
            entryLong := 0

        // Supertrend +  MA == HMA
        // if m7_a>1 and trend4 == 1 and c==0 and up4<m8 and clrToUse == red
        //  and not(rsi_m>bbLowerBand_m)
        //  and not(btw_m7_m8)
        //  and not(m2_a<-15)
        //  //and not(m2_a<m2_a[1])
        //  //and not(m7_m8_conv==false)
        //     entryLong := 1
        //     cond := 'SUPER'

        // Flag
        // Flag Down - MA == EMA 5 mins
        // if m8_a>0 and flag_d==0
        //  and not(m5_a<0)
        //     entryLong := 1
        //     cond := 'M8'


        // Filter Out Trades
        // if clrToUse != red
        //     entryLong := 0

        if i_use_time_filter and not (Session(Lon) or Session(Ny) )
            entryLong := 0


        // if m8_a>3 or m8_a<0
        //     entryLong := 0


        // Deadzone
        // if i_deadzone and Session(Dz)
        //     entryLong := 0


        // Reversals

        // Exits
        if RSI_close>70 and i_use_rsi_filter
            exitLong := 1

        // if Session(Dz) and i_deadzone
        //     exitLong := 1



            
    // Downtrend
    if i_short_trades

        // M1
        if close>m1 and m1_a<0 and c==1
            entryShort := 1
            cond := 'M1'

        // M2
        if close>m2 and m2_a<0 and c==1
            entryShort := 1
            cond := 'M2'

        if close>m4 and m2_a<0 and m4_a<0 and c==1
            entryShort := 1
            cond := 'M4'

        if clrToUse != green
            entryShort := 0

        // if trend4 == -1 and c==1 and clrToUse == green
        //  and not(rsi_m<bbLowerBand_m)
        //     entryShort := 1

        // Flag Down - MA == EMA 5 mins
        // if m8_a<0 and flag_u==0
        //  and not(m5_a>0)
        //     entryShort := 1
        //     cond := 'M8'


        if m1_a>0 and i_use_filters
            entryShort := 0

        // Exits
        if RSI_open<30 and RSI_close<30 and i_use_rsi_filter
            exitShort := 1

        // if Session(Dz) and i_deadzone
        //     exitShort := 1



    [entryLong,entryShort,exitLong,exitShort,closeAll,cond]

[entryLong,entryShort,exitLong,exitShort,closeAll,cond] = trade_dir()

// check if live trading or market closed
//plotshape(barstate.islastconfirmedhistory, title='Real Time', color=red, style=shape.circle, location=location.top)


// STOP LOSS
g_sl = 'Stop Loss ----------------------------------------------------'
i_sl_type   = input.string("ATR", title="SL Type", options=["ATR","SUP","Lowest"],group=g_sl) // ATR
Multip      = input.float(3, title='Stop Loss',group=g_sl) // 4 1.5
atr_src     = input.string('close', title='Close or Wicks', options=['close', 'wicks'],group=g_sl) // close
show_sl     = input.bool(false,title="Stop Loss",group=g_sl)

// Take Profit
g_tp = 'Take Profit ----------------------------------------------------'
i_tpFactor  = input(3.5, 'Target Profit',group=g_tp) // 3
i_qty_mult  = input.float(10.0, title='Quantity Multiplier',group=g_tp) // 5.0 2
i_tsFactor  = input(1.0, 'Trailing Stop',group=g_tp) // 1.25
i_ts        = input.bool(true, title="Use Trailing Stop",group=g_tp)
i_ticks     = input.float(100, title='Min Ticks',group=g_tp) // 100 for Forex 1000 for NAS
show_ts     = input.bool(false,title="Trailing Stop",group=g_tp)
i_bkcandles = 11 //input.int(11, title="Lowerest range - Number of candles",group=g_sl)


float qty_value = switch syminfo.type
    "forex" => 100000.0
    "futures" => 10.0
    "index" => 10.0
    => 10.0
    
stop_loss()=>

    float sl_short  = na
    float sl_long   = na

    if i_sl_type == "ATR"
        atr_len = 14
        ATR = ta.atr(atr_len)
        sl_long     := (atr_src =='close' ? close : low)  - ATR * Multip
        sl_short    := (atr_src =='close' ? close : high) + ATR * Multip

    if i_sl_type == "SUP"
        sl_long     := up4
        sl_short    := dn4

    if i_sl_type == "Lowest"
        sl_short    = ta.highest(high, i_bkcandles)[1]
        sl_long     = ta.lowest(low, i_bkcandles)[1]

    // Long
    longDiff  = math.abs(close - sl_long)
    longTS    = close + (i_tsFactor * longDiff)
    longTP    = close + (i_tpFactor * longDiff)
    plValue   = (i_equity=="Initial" ? strategy.initial_capital : strategy.equity) * i_pctStop / (longDiff / close)
    pl_size   = i_use_pos ? plValue / close : qty_value * i_qty_mult
    // Short
    shortDiff = math.abs(close - sl_short)
    shortTS   = close - (i_tsFactor * shortDiff)
    shortTP   = close - (i_tpFactor * shortDiff)
    psValue   = (i_equity=="Initial" ? strategy.initial_capital : strategy.equity) * i_pctStop / (shortDiff / close)
    ps_size   = i_use_pos ? psValue / close : qty_value * i_qty_mult


    [sl_short,sl_long, close, close, shortTP, longTP, longTS, shortTS, pl_size, ps_size, longDiff ]

float shortSL = 0.0
float longSL  = 0.0
float ratio_l = 0.0
float short_ticks = 0.0
float long_ticks = 0.0

[atr_short, atr_long, long_close, short_close, shortTP, longTP, longTS, shortTS, pl_size, ps_size, longDiff] = stop_loss()

// ATR
plot(show_sl? atr_long  : na,"ATR + ", color=color.new(green,70) )
plot(show_sl? atr_short : na,"ATR - ", color=color.new(red,70) )

// longDiff := entryLong and strategy.opentrades == 0 ? longDiff * 100 : strategy.opentrades > 0 ? longDiff[1] : 0
// plot(longDiff,"Long Diff ", color=color.new(red,100) )

// Short Plots
short_close := entryShort and strategy.opentrades == 0 ? short_close : strategy.opentrades > 0 ? short_close[1] : 0
short_ticks := short_close - (syminfo.mintick * i_ticks)
shortSL     := entryShort and strategy.opentrades == 0 ? atr_short : strategy.opentrades > 0 ? shortSL[1] : 0
shortTS     := entryShort and strategy.opentrades == 0 ? shortTS : strategy.opentrades > 0 ? shortTS[1] : 0
shortTP     := entryShort and strategy.opentrades == 0 ? shortTP : strategy.opentrades > 0 ? shortTP[1] : 0
p_s_c       = plot( strategy.opentrades > 0 ? short_close : na, title="Short Close", color=color.new(green,75), linewidth=1, style=plot.style_linebr)
p_s_ticks   = plot( strategy.opentrades > 0 ? short_ticks : na, title="Short Ticks", color=lime, linewidth=3, style=plot.style_linebr)
p_s_sl      = plot( strategy.opentrades > 0 ? shortSL : na, title="Short SL", color=color.new(red,75), linewidth=1, style=plot.style_linebr)
p_s_ts      = plot( strategy.opentrades > 0 ? shortTS : na, title="Short TS", color=color.new(green,75), linewidth=1, style=plot.style_linebr)
p_s_tp      = plot( strategy.opentrades > 0 ? shortTP : na, title="Short TP", color=color.new(green,75), linewidth=1, style=plot.style_linebr)
fill(p_s_sl, p_s_c,title='Fill Short SL',color=color.new(red,75) )
fill(p_s_ts, p_s_c,title='Fill Short TS',color=color.new(lime,75) )
fill(p_s_ts, p_s_tp,title='Fill Short TP',color=color.new(green,75) )

// Long Plots
long_close  := entryLong and strategy.opentrades == 0 ? long_close : strategy.opentrades > 0 ? long_close[1] : 0
long_ticks  := long_close + (syminfo.mintick * i_ticks)
longSL      := entryLong and strategy.opentrades == 0 ? atr_long : strategy.opentrades > 0 ? longSL[1] : 0
longTS      := entryLong and strategy.opentrades == 0 ? longTS : strategy.opentrades > 0 ? longTS[1] : 0
longTP      := entryLong and strategy.opentrades == 0 ? longTP : strategy.opentrades > 0 ? longTP[1] : 0
ratio_l     := ( close  - long_close )
p_l_c       = plot( strategy.opentrades > 0 ? long_close : na, title="Long Close", color=color.new(red,75), linewidth=1, style=plot.style_linebr)
p_l_ticks   = plot( strategy.opentrades > 0 ? long_ticks : na, title="Long Ticks", color=close>longTS ? lime : color.new(lime,100), linewidth=1, style=plot.style_linebr)
p_l_sl      = plot( strategy.opentrades > 0 ? longSL : na, title="Long SL", color=color.new(red,75), linewidth=1, style=plot.style_linebr)
p_l_ts      = plot( strategy.opentrades > 0 ? longTS : na, title="Trailing Stop", color=color.new(yellow,75), linewidth=1, style=plot.style_linebr)
p_l_tp      = plot( strategy.opentrades > 0 ? longTP : na, title="Long TP", color=color.new(green,75), linewidth=1, style=plot.style_linebr)

//psize       = plot(pl_size, title="Position Size")
//p_l_ratio   = plot( ratio_l, title="Ratio", color=color.new(red,75), linewidth=1, style=plot.style_linebr)
fill(p_l_sl,p_l_c, title='Fill Long SL', color=color.new(red,75))
fill(p_l_ts,p_l_c, title='Fill Long Trailing Stop', color=color.new(lime,75) )
fill(p_l_tp,p_l_ts, title='Fill Long Take Profit', color=color.new(green,75))



//close>longTP
//close>longTP ? long_close: longSL
cd=close>open?1:0

if Session(Dz) and i_deadzone
    entryShort := 0
    entryLong := 0

// Long
if (entryLong and not (strategy.position_size < 0) )
	strategy.entry("L", strategy.long, comment=cond, qty = pl_size)
    //strategy.exit('EXIT L', 'L', stop = longSL)
        
else
	strategy.cancel("S")

if (exitLong)
	strategy.close("L", comment = "Close L")

// Short
if (entryShort)
	strategy.entry("S", strategy.short, qty = ps_size, comment=cond)
    //strategy.exit('EXIT S', 'S', stop=shortSL)
else
	strategy.cancel("S")

if (exitShort)
	strategy.close("S", comment = "Close S")

// Filters
if Session(Dz) and i_deadzone
    strategy.close_all(comment = "close all entries")

in_profit() =>
    if strategy.position_size > 0 and close > longTS
        true
    else if strategy.position_size < 0 and close < shortTS
        true
    else
        false

getCurrentStage() =>
    var stage = 0
    if strategy.position_size == 0
        stage := 0
        stage
    if stage == 0 and strategy.position_size != 0
        stage := 1
        stage
    else if stage == 1 
        if strategy.position_size > 0 and close > longTS
            stage := 2
        if strategy.position_size < 0 and close < shortTS
            stage := 2
    else if stage == 2
        if strategy.position_size > 0 and close > longTP
            stage := 3
        if strategy.position_size < 0 and close < shortTP
            stage := 3
            stage
    stage

curStage = getCurrentStage()


float stopLevel = na
string comment  = ''
float limit     = na //strategy.position_size > 0 and close>longTP and m2>m4 ? close : strategy.position_size < 0 and close<shortTP and m2<m4 ? close : na
string win     = 'Take Profit'
string loss    = 'Loss'
string bkeven  = 'Break Even'
string ts      = 'Trailing Stop'

if curStage == 1
    // Comment Strings

    if strategy.position_size > 0
        stopLevel := longSL
        comment   := close < long_close ? loss : win
        limit     := high>longTP ? high : na
    else
        stopLevel := shortSL
        comment   := close > short_close ? loss : win
        limit     := low<shortTP ? low : na
        
    strategy.exit('x', stop=stopLevel, limit=limit, comment=comment)

else if curStage == 2

    if strategy.position_size > 0
        stopLevel := long_close + (syminfo.mintick * i_ticks)
        comment   := close <= longTS ? bkeven : win
        limit     := high>longTP ? high : na
    else
        stopLevel := short_close - (syminfo.mintick * i_ticks)
        comment   := close >= shortTS ? bkeven : win
        limit     := low<shortTP ? low : na

    strategy.exit('x', stop=stopLevel, limit=limit, comment=comment)

else if curStage == 3

    if strategy.position_size > 0
        stopLevel := longTS
        comment   := close >= longTS and close<longTP ? ts : win
        limit     := high>longTP ? high : na
    else
        stopLevel := shortTS
        comment   := close <= shortTS and close>shortTP ? ts : win
        limit     := low<shortTP ? low : na

    strategy.exit('x', stop=stopLevel, limit=limit, comment=comment)

else
    strategy.cancel('x')





