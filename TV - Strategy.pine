// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © TradingView

//@version=5
strategy("TV - Strategy", overlay=true,
     currency="USD",
     initial_capital=100000,
     default_qty_type=strategy.percent_of_equity,
     default_qty_value=100, // 100% of balance invested on each trade
     commission_type=strategy.commission.cash_per_contract)

import TradingView/Strategy/3 as TV

// ———————————————————— Example code {


// —————————— Calculations

// Inputs for stop and limit orders in percent.
float percentStop = input.float(1.0, "Percent Stop",  minval = 0.0, step = 0.25, group = "Exit Orders")
float percentTP   = input.float(2.0, "Percent Limit", minval = 0.0, step = 0.25, group = "Exit Orders")

// Using the input stop/ limit percent, we can convert to ticks and use the ticks to level functions.
// This can be used to calculate the take profit and stop levels.
float sl = TV.ticksToStopLevel (TV.percentToTicks (percentStop))
float tp = TV.ticksToTpLevel   (TV.percentToTicks (percentTP))  

// Conditions used to reference position and determine trade bias
bool long       = strategy.position_size > 0
bool short      = strategy.position_size < 0
bool enterLong  = long  and not long [1]
bool enterShort = short and not short[1]
bool enter      = enterLong or enterShort
bool exit       = strategy.position_size == 0 and not (strategy.position_size == 0)[1]

// Condition to flip labels based on trade bias.
string stopStyle  = short ? label.style_label_up : label.style_label_down
string limitStyle = long  ? label.style_label_up : label.style_label_down

// Used to determine the exit price to draw an arrow for stop and limit exits
// and determine a color for the arrow (fuchsia for stop exits, lime for limits).
// We can use the trade bias as a multiplier to invert the condition for short trades.
exitPrice = strategy.closedtrades.exit_price(strategy.closedtrades-1)
bias      = math.sign(strategy.position_size)
avg       = strategy.position_avg_price

plotCol = exitPrice * bias[1] < avg[1] * bias[1] ? color.fuchsia : color.lime


// —————————— Strategy Calls

// Calculate two moving averages.
i_ma1 = input(100) // 14
i_ma2 = input(200) // 28
i_ma3 = input(500) // 200
float maFast = ta.sma(close, i_ma1)
float maSlow = ta.sma(close, i_ma2)


// Create cross conditions for the averages.
bool longCondition  = ta.crossover(maFast, maSlow)
bool shortCondition = na //ta.crossunder(maFast, maSlow)

// Create entries based on the cross conditions for both trades biases.
if longCondition
    strategy.entry("My Long Entry Id", strategy.long)
if shortCondition
    strategy.entry("My Short Entry Id", strategy.short)

// Create a hard exit level for a take profit and stop loss.
// Using the `strategy.exit` wrapper function, we can simply specify the percent stop and limit using the input variables.
TV.exitPercent("exit", percentStop, percentTP)
// Another option is to use the "ticksToLevel" functions with the percent to ticks functions to convert percent directly to the stop or limit level:
// strategy.exit("exit", stop = ticksToStopLevel (percentToTicks (percentStop)), limit = ticksToTpLevel (percentToTicks (percentTP)), comment = "exit")


// —————————— Plots

// Plot the entry level.
plot(strategy.position_avg_price, "strategy.position_avg_price", not enter ? color.new(color.orange, 60) : na, 2, plot.style_linebr)

// Plot the exit levels.
plot(sl, "Stop2", not enter ? color.new(color.fuchsia, 60) : na, 6, plot.style_linebr)
plot(sl, "Stop2", not enter ? color.new(color.fuchsia, 60) : na, 6, plot.style_linebr)
plot(tp, "TP2"  , not enter ? color.new(color.green,   60) : na, 6, plot.style_linebr)

// Plot an entry tag for both directions
plotshape(enterLong,  "longCondition",  shape.arrowup,   location.belowbar, color.new(color.green,   30), text = "Long")
plotshape(enterShort, "shortCondition", shape.arrowdown, location.abovebar, color.new(color.fuchsia, 30), text = "Short")

// Plot and arrow for stop or limit exits at the trade exit price. We use the price and the color from above.  
plotchar(exit ? exitPrice : na, "Exit Arrow", "◄", location.absolute, color.new(plotCol, 20))

// Plot labels using the percentProfit() functions to display the distance from entry to the level for the current trade
label labLimit = label.new(bar_index, sl, str.tostring(TV.percentProfit(sl), format.percent), color = color.new(color.red,   70), style = limitStyle, textcolor = color.white)
label labStop  = label.new(bar_index, tp, str.tostring(TV.percentProfit(tp), format.percent), color = color.new(color.green, 70), style = stopStyle,  textcolor = color.white)
// We delete the labels drawn one bar ago, or we end up with many labels drawn 
label.delete(labLimit[1])
label.delete(labStop [1])

// Show ratios in indicator values and the Data Window.
plotchar(TV.sortinoRatio(2.0), "Sortino", "", location.top, size = size.tiny)
plotchar(TV.sharpeRatio(2.0),  "Sharpe", "", location.top, size = size.tiny)
// }
