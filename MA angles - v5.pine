//@version=5
indicator(title="MA Angles - v5", overlay=false)
g_ma = "MA ----------------------------------------------------"
inl_ma = "ma"
inl_type = "inl_type"
inl_color = "inl_color"
inl_smooth = "inl_smooth"
inl_angle = "inl_angle"
inl_mult = "inl_mult"
i_use_gaps = input.bool(true,title='Use Gaps')
i_ma_time = input.timeframe(title='Timeframe', defval='30',group=g_ma,inline=inl_type)
ma_type = input.string(title="MA Type", defval="hma", options=["sma","ema","dema","tema","wma","vwma","smma","rma","hma","lsma","McGinley", "ATR"],group=g_ma,inline=inl_type)
use_candles = input.bool(false,title=" ",group=g_ma,inline=inl_color)
i_ma_select = input.int(6, title="Colorize Candles", options=[1,2,3,4,5,6,7,8],group=g_ma,inline=inl_color)
i_ma_color_alpha = input.int(50, title='Alpha',group=g_ma,inline=inl_color)
src = close // input(close, title="Source")
use_smooth = input.bool(false, title=" ",inline=inl_smooth)
smooth = input.int(1,title="Smooth",inline=inl_smooth)
i_use_angle = input.bool(true,title = " ",inline=inl_angle)
i_angle = input.int(14,title="Angle Amount",inline=inl_angle)
use_multiple = input.bool(false, title=" ",inline=inl_mult)
multi_value = input.int(10, title="Multiply Value",inline=inl_mult)
i_display = input.string(title="MA Type", defval="line", options=["line","area","column"])

// Default HMA - M3, M5 (colorzied) // Default2 HMA M2, M5, M6 (colorzied)
g_cb = "Show MA ----------------------------------------------------"
inl_cb = "cb"
show_m1 = input.bool(title="M1", defval=false,group=g_cb,inline=inl_cb)
show_m2 = input.bool(title="M2", defval=true,group=g_cb,inline=inl_cb)
show_m3 = input.bool(title="M3", defval=false,group=g_cb,inline=inl_cb)
show_m4 = input.bool(title="M4", defval=false,group=g_cb,inline=inl_cb)
show_m5 = input.bool(title="M5", defval=true,group=g_cb,inline=inl_cb)
show_m6 = input.bool(title="M6", defval=true,group=g_cb,inline=inl_cb)
show_m7 = input.bool(title="M7", defval=false,group=g_cb,inline=inl_cb)
show_m8 = input.bool(title="M8", defval=false,group=g_cb,inline=inl_cb)

g_angles = "MA Angles ----------------------------------------------------"
inl_angles = "angles"
i_ang_1 =  input.int(1, title='A1',group=g_angles,inline=inl_angles)
i_ang_2 =  input.int(2, title='A2',group=g_angles,inline=inl_angles)
i_ang_3 =  input.int(3, title='A3',group=g_angles,inline=inl_angles)
i_ang_4 =  input.int(4, title='A4',group=g_angles,inline=inl_angles)
i_ang_5 =  input.int(5, title='A5',group=g_angles,inline=inl_angles)
i_ang_6 =  input.int(6, title='A6',group=g_angles,inline=inl_angles)
i_ang_7 =  input.int(7, title='A7',group=g_angles,inline=inl_angles)
i_ang_8 =  input.int(8, title='A8',group=g_angles,inline=inl_angles)

g_ma_len = "Length ----------------------------------------------------"
inl_ma_len = "ma_len"
l1 = input.int(5,title="M1",group=g_ma_len,inline=inl_ma_len) // 8
l2 = input.int(20,title="M2",group=g_ma_len,inline=inl_ma_len)
l3 = input.int(50,title="M3",group=g_ma_len,inline=inl_ma_len)
l4 = input.int(75,title="M4",group=g_ma_len,inline=inl_ma_len)
l5 = input.int(100,title="M5",group=g_ma_len,inline=inl_ma_len)
l6 = input.int(200,title="M6",group=g_ma_len,inline=inl_ma_len)
l7 = input.int(300,title="M7",group=g_ma_len,inline=inl_ma_len)
l8 = input.int(500,title="M8",group=g_ma_len,inline=inl_ma_len)

g_fill = "Fills"
inl_fill = "fill"
inl_conv = "conv"
show_fill = input.bool(title="Show Fill", defval=true,inline=inl_fill,group=g_fill)
show_conv = input.bool(title="Show Conv", defval=true,inline=inl_fill,group=g_fill)
conv_amount = input.float(8, title="Conv Amount", step=1,inline=inl_conv,group=g_fill )
c_type = input.string(title="Type", defval="NAS", options=["NAS","USD", "JPY"],inline=inl_conv,group=g_fill)
line_input = 1 //input(1, title="Line width", type=input.integer,inline=inl_fill )


red = #ff0062
orange = #ff9800
yellow = #FFFF00
green = #005a04
lime = #00E676
aqua = #00bcd4
blue = #2962ff 
dark_blue = #2962ff
violet = #814dff
white = #ffffff
gray = #707070

angle(_src, len) =>
    rad2degree=180/3.14159265359  //pi 
    ang=rad2degree*math.atan((_src[0] - _src[1]) / ta.atr(len))
    

// Bollinger bands
g_bb = 'Basis'
inl_bb = '1'
show_ba = input(title='Show BA\'s', defval=false)
basis_len = input.int(20, 'Basis Length', group=g_bb, inline=inl_bb)  // 20
BB_stdDev = 2

// BB 1
basis = use_smooth ? ta.sma(ta.sma(close, basis_len),smooth) : ta.sma(close, basis_len)
dev = BB_stdDev * ta.stdev(close, basis_len)
bb_upper = basis + dev
bb_lower = basis - dev
ba = angle(basis, 1)
ba_u = angle(bb_upper, 1)
ba_l = angle(bb_lower, 1)
// BB Spread
sqz_length = 80
bb_spread = bb_upper - bb_lower
avgspread = ta.sma(bb_spread, sqz_length)
bb_squeeze = 0.00
bb_squeeze := bb_spread / avgspread * 100
// BB Colorize
bb_zone = bb_squeeze < 53 ? 0 : 
 bb_squeeze < sqz_length ? 1 : 
 bb_squeeze < 120 ? 2 :
 bb_squeeze < 160 ? 3 :
 bb_squeeze > 160 ? 4 :
 bb_squeeze > 200 ? 5 : na
bb_color = bb_zone == 0 ? #0045b3 :
 bb_zone == 1 ? #ff0062 : 
 bb_zone == 2 ?  gray : 
 bb_zone == 3 ?  #00c3ff : 
 bb_zone == 4 ? white:
 bb_zone == 5 ? yellow: na

plot(show_ba ? ba : na,title="BA",color=bb_color,linewidth=3)
//plot(ba_u,title="BA Upper",color=red)
//plot(ba_l,title="BA Lower",color=white)



// Lines and Angles
ma_graph(len) =>
    ma =0.0
    length = len
    if use_multiple
        length := len * multi_value
    if ma_type == 'sma' // Simple Moving Average
        ma := ta.sma(src,length)
    if ma_type == 'ema' // Exponential
        ma := ta.ema(src,length)
    if ma_type=="dema" // Double Exponential
        e = ta.ema(src, length)
        ma := 2 * e - ta.ema(e, length)
    if ma_type == 'tema' // Triple Exponential
        ema1 = ta.ema(src, length)
        ema2 = ta.ema(ema1, length)
        ema3 = ta.ema(ema2, length)
        ma := 3 * (ema1 - ema2) + ema3
    if ma_type == 'wma' // Weighted
        ma := ta.wma(src,length)
    if ma_type == 'vwma' // Volume Weighted
        ma := ta.vwma(src,length)
    if ma_type=="smma" // Smoothed
        w = ta.wma(src, length)
        ma := na(w[1]) ? ta.sma(src, length) : (w[1] * (length - 1) + src) / length
    if ma_type == "rma"
        ma := ta.rma(src, length)
    if ma_type == 'hma' // Hull
        ma := ta.wma(2*ta.wma(src, length/2)-ta.wma(src, length), math.floor(math.sqrt(length) ))
    if ma_type=="lsma" // Least Squares
        ma := ta.linreg(src, length, 0)
    if ma_type=="McGinley"
        mg = 0.0
        mg := na(mg[1]) ? ta.ema(src, length) : mg[1] + (src - mg[1]) / (length * math.pow(src/mg[1], 4))
        ma :=mg

    if ma_type=="ATR"
        mg = 0.0
        mg := ta.atr(length)
        ma := mg

    if use_smooth
        ma := ta.sma(ma,smooth)

    angle = i_use_angle ? i_angle : 1
    ma_angle = angle(ma,angle)
    [ma,ma_angle]




ma_conv(t1, t2) =>
    
    float boost = switch syminfo.ticker
        "GBPUSD" => 10000
        "EURUSD" => 10000
        "GBPJPY" => 100
        "BTCUSD" => 0.1
        "NAS100" => 1
        "NDQ100" => 1
        "GOLD"   => 10
        "SILVER" => 100
        => 1

    diff = (t1 - t2) * boost
    conv = show_conv and diff < conv_amount and diff > conv_amount * -1 ? true : false
    [diff, conv]


[m1,m1_a] = request.security(syminfo.tickerid, i_ma_time, ma_graph(l1), gaps=i_use_gaps ? barmerge.gaps_on : barmerge.gaps_off )
[m2,m2_a] = request.security(syminfo.tickerid, i_ma_time, ma_graph(l2), gaps=i_use_gaps ? barmerge.gaps_on : barmerge.gaps_off ) 
[m3,m3_a] = request.security(syminfo.tickerid, i_ma_time, ma_graph(l3), gaps=i_use_gaps ? barmerge.gaps_on : barmerge.gaps_off ) 
[m4,m4_a] = request.security(syminfo.tickerid, i_ma_time, ma_graph(l4), gaps=i_use_gaps ? barmerge.gaps_on : barmerge.gaps_off ) 
[m5,m5_a] = request.security(syminfo.tickerid, i_ma_time, ma_graph(l5), gaps=i_use_gaps ? barmerge.gaps_on : barmerge.gaps_off ) 
[m6,m6_a] = request.security(syminfo.tickerid, i_ma_time, ma_graph(l6), gaps=i_use_gaps ? barmerge.gaps_on : barmerge.gaps_off ) 
[m7,m7_a] = request.security(syminfo.tickerid, i_ma_time, ma_graph(l7), gaps=i_use_gaps ? barmerge.gaps_on : barmerge.gaps_off ) 
[m8,m8_a] = request.security(syminfo.tickerid, i_ma_time, ma_graph(l8), gaps=i_use_gaps ? barmerge.gaps_on : barmerge.gaps_off ) 

[m2_m4_diff,m2_m4_conv] = ma_conv(m2,m4)
[m4_m5_diff,m4_m5_conv] = ma_conv(m4,m5)
[m5_m6_diff,m5_m6_conv] = ma_conv(m5,m6)
[m7_m8_diff,m7_m8_conv] = ma_conv(m7,m8)

// Colorized MA angles
ma_select()=>
    float select = switch i_ma_select
        2 => m2_a
        3 => m3_a
        4 => m4_a
        5 => m5_a
        6 => m6_a
        7 => m7_a
        8 => m8_a
        => m6_a

    ma_a = math.abs(select)
    ma_zone =  
     ma_a   < i_ang_1 ? 0 
     : ma_a < i_ang_2 ? 1 
     : ma_a < i_ang_3 ? 2 
     : ma_a < i_ang_4 ? 3 
     : ma_a < i_ang_5 ? 4 
     : ma_a < i_ang_6 ? 5 
     : ma_a < i_ang_7 ? 6 
     : ma_a < i_ang_8 ? 7 
     : ma_a > i_ang_7 ? 8 
     : na

    ma_color = 
     ma_zone   == 0 ? red 
     : ma_zone == 1 ? orange 
     : ma_zone == 2 ? yellow 
     : ma_zone == 3 ? gray 
     : ma_zone == 4 ? green 
     : ma_zone == 5 ? lime
     : ma_zone == 6 ? blue
     : ma_zone == 7 ? aqua
     : ma_zone == 8 ? white : na

    ma_state = ta.change(ma_zone)

    [ma_color, ma_state]

[ma_color, ma_state] = ma_select()

// var int ma_state_ch = 0
// ma_state_ch := not na(ma_state) ? ma_state : ma_state_ch
// // ma state
// plot(ma_state_ch, title='MA State', color=color.new(ma_color,100)  )

// Growing or Shrinking
// f_get_state(color) =>

//     switch

// ma_state = 0
// ma_state = ta.change(ma_color) ? f_get_state(ma_color) : ma_state

// Colorize candles based on MA angles
barcolor(use_candles? ma_color : na)

display = switch i_display
    "line" => plot.style_line
    "area" => plot.style_area
    "column" => plot.style_columns
    => plot.style_line


// Angles
plot(m1_a and show_m1 ? m1_a: na, title="M1 A", color = i_ma_select==1 ? color.new(ma_color,i_ma_color_alpha) : m1_a>0?color.new(green,0):color.new(red,0),style=i_ma_select==1?plot.style_area : plot.style_line)
plot(m2_a and show_m2 ? m2_a: na, title="M2 A", color = i_ma_select==2 ? color.new(ma_color,i_ma_color_alpha) : m2_a < ba ? aqua : #3179f5,style=i_ma_select==2?plot.style_area : plot.style_line )
plot(m3_a and show_m3 ? m3_a: na, title="M3 A", color = i_ma_select==3 ? color.new(ma_color,i_ma_color_alpha) : color.new(color.blue,0), style=i_ma_select==3?plot.style_area : plot.style_line )
plot(m4_a and show_m4 ? m4_a: na, title="M4 A", color = i_ma_select==4 ? color.new(ma_color,i_ma_color_alpha) : color.new(yellow,0), style=i_ma_select==4?plot.style_area : plot.style_line )
plot(m5_a and show_m5 ? m5_a: na, title="M5 A", color = i_ma_select==5 ? color.new(ma_color,i_ma_color_alpha) : color.new(orange,0), style=i_ma_select==5?plot.style_area : plot.style_line )
plot(m6_a and show_m6 ? m6_a: na, title="M6 A", color = i_ma_select==6 ? color.new(ma_color,i_ma_color_alpha) : color.new(red,0), style=i_ma_select==6?plot.style_area : plot.style_line )
plot(m7_a and show_m7 ? m7_a: na, title="M7 A", color = i_ma_select==7 ? color.new(ma_color,i_ma_color_alpha) : color.new(orange,0), style=i_ma_select==7?plot.style_area : plot.style_line )
plot(m8_a and show_m8 ? m8_a: na, title="M8 A", color = i_ma_select==8 ? color.new(ma_color,i_ma_color_alpha) : color.new(red,0), style=i_ma_select==8?plot.style_area : plot.style_line )

hline(0)
hline(10, color=color.new(#ffffff, 80))
hline(-10, color=color.new(#ffffff, 80))
hline(15, color=color.new(red, 80))
hline(-15, color=color.new(green, 80))
hline(20, color=color.new(red, 80))
hline(-20, color=color.new(green, 80))
// Cross up
hline(-5, color=color.new(#2962ff, 80))

//plotshape(ta.change(m1_a),title="Change",color=red ,style=shape.circle,location=location.top)

// Alerts
sell_cond  = m3_a<0 and m4_a<0 and m7>m8 and close>m3 
 and not(m2_a>0) 
 and not(m7_a<0) ? 1 : na
//sell_cond2 = m3_a<0 and m4_a<0 and m7>m8 and close>m4 and not(m7_a>0) ? 1 : na
//plotshape(sell_cond,title="Buy 1",color=red ,style=shape.circle,location=location.top)

// 5 min
//buy_cond  = m8_a<0 and m6_a<0 and m3<m2 and m2_a>-5 and close<m2
 and not(m5>m6)
 and not(m3>m5)
 and not(m1_a<-5)
//buy_cond  = m8_a<0 and m6_a<0 and m3_a>0 and close<m3
//buy_cond  = m3<m2 and m2_a>5
//plotshape(buy_cond,title="Buy 1",color=green ,style=shape.circle,location=location.bottom)





// Notes EMA Flags
// Strong uptrend -- Exit
// When m1_a>20 and than m1_a<m1_a[1]
// But the down turn needs to be lower than 10 otherwise probably still trending up

var int flag_u = 0
var int flag_d = 0
//flag_amount = m6_a>20 ? // maybe use BB bands below or above 0 line

flag_u := m2_a>20 and m2_a>m2_a[1] ? 1 : flag_u==1 and m2_a<m2_a[1] ? 0 : flag_u==1 and m2_a>0 ? 1 : na
flag_d := m2_a<-10 and m2_a<m2_a[1] ? -1 : flag_d==-1 and m2_a>m2_a[1] ? 0 : flag_d==-1 and m2_a<0 ? -1 : na

// plotshape(flag_u==0 ? 1 : na,title="Flag Up",color=red ,style=shape.circle,location=location.top)
// plotshape(flag_d==0 ? 1 : na,title="Flag Down",color=green ,style=shape.circle,location=location.bottom)
//plot(flag_u, title="Flag Up")
//plot(flag_d, title="Flag Down")

// Uptrend close below m6
// m6_cond = close<open and close<m6 and m6_a>0
// plotshape(m6_cond, title="M6 buy", color=lime ,style=shape.circle,location=location.bottom)

// M1 green and red candle
// BB Cross
bb_cross = m1_a>m1_a[1] and m1_a[1]<0 and m2_a > ba and m1_a>m2_a
//plotshape(bb_cross,title="BB Cross",color=m1_a>0?green:blue ,style=shape.circle,location=location.bottom)
// BB upper
//plotshape(close>bb_upper? 1 : na,title="BB Up",color=bb_color ,style=shape.circle,location=location.top)
//plotshape(close<bb_lower? 1 : na,title="BB Down",color=bb_color ,style=shape.circle,location=location.bottom)


// Trading
//plotshape(m2_a<m2_a[1] ? 1 : na, title="M2 Sell", color=red ,style=shape.circle,location=location.top)
//plotshape(m2_a>m2_a[1] ? 1 : na, title="M2 buy", color=lime ,style=shape.circle,location=location.bottom)