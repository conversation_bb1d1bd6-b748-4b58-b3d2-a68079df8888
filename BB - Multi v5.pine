//@version=5
indicator(title='BB - Ben', overlay=true)

white = #ffffff
gray = #707070
yellow = #FFFF00
aqua = #00bcd4
lime = #00E676
red = #ff0000
green = #4caf50

angle(_src, len) =>
    rad2degree = 180 / 3.14159265359  //pi 
    ang = rad2degree * math.atan((_src[0] - _src[1]) / ta.atr(len))
    ang

g_g = 'Bollinger Bands'
inl_g = 'inl_g1'
show_bb = input.bool(title='Show BB', defval=true, group=g_g, inline=inl_g)
show_overlap = input.bool(title='Show Overlap ', defval=false, group=g_g, inline=inl_g)

g_bb = 'BB 1'
inl_bb1 = 'inl_bb1'
inl_bbb = 'inl_bbb'
sqz_length = input(80, title='Sqz Len')  // 100

BB_length = input.int(50, title='Length', group=g_bb, inline=inl_bb1)
bb_type = input.string(title='Type', defval='SMA', options=['SMA', 'EMA', 'WMA', 'HULL'], group=g_bb, inline=inl_bb1)
bb_bias = input.float(title='Bias 1', defval=89.7, step=0.1, group=g_bb, inline=inl_bb1)
use_bb_curr1 = input.bool(title='BB1', defval=false, inline=inl_bbb, group=g_bb)
show_basis = input.bool(title='Show Basis', defval=false, group=g_bb, inline=inl_bbb)
show_fill = input.bool(title='Show fill', defval=false, group=g_bb, inline=inl_bbb)
show_bias = input.bool(title='Show Bias', defval=false, group=g_bb, inline=inl_bbb)
show_zone = input.bool(title='Zone Change', defval=false, group=g_bb, inline=inl_bbb)
show_zones1 = input.bool(title='Show Zones ', defval=false, group=g_bb, inline=inl_bbb)

g_bb2 = 'BB 2'
inl_bb2 = 'inl_bb2'
inl_bb2_b = 'inl_bb2_b'
BB_len2 = input.int(150, title='Length', group=g_bb2, inline=inl_bb2)  // 350
bb2_type = input.string(title='Type', defval='SMA', options=['SMA', 'EMA', 'WMA', 'HULL'], group=g_bb2, inline=inl_bb2)
bb_bias2 = input.float(title='Bias 2', defval=89.7, step=0.1, group=g_bb2, inline=inl_bb2)
use_bb_curr2 = input.bool(title='BB2', defval=false, inline=inl_bb2_b, group=g_bb2)
show_basis2 = input.bool(title='Show Basis', defval=false, group=g_bb2, inline=inl_bb2_b)
show_fill2 = input.bool(title='Show fill', defval=false, group=g_bb2, inline=inl_bb2_b)
show_bias2 = input.bool(title='Show Bias', defval=false, group=g_bb2, inline=inl_bb2_b)
show_zone2 = input.bool(title='Zone Change', defval=false, group=g_bb2, inline=inl_bb2_b)
show_zones2 = input.bool(title='Show Zones ', defval=false, group=g_bb2, inline=inl_bb2_b)
// 89.70 // 88.85

g_bb_m = 'BB Multi'
inl_bb_m = 'inl_bb_m'
inl_bb_m2 = 'inl_bb_m2'
bb_gaps_m = input.bool(true,'Use Gaps', group=g_bb_m)
bb_len_m = input.int(10, title='Length', group=g_bb_m, inline=inl_bb_m)
i_bb_stdev = input.float(2.0, title='Deviations', minval=1.0, maxval=4.0, step=0.1, group=g_bb_m, inline=inl_bb_m) // 2
bb_type_m = input.string(title='Type', defval='SMA', options=['SMA', 'EMA', 'WMA', 'HULL'], group=g_bb_m, inline=inl_bb_m)
use_bb_multi = input.bool(title='BB Multi', defval=false, inline=inl_bb_m2, group=g_bb_m)
show_basis_m = input.bool(title='Basis', defval=false, group=g_bb_m, inline=inl_bb_m2)
show_fill_m = input.bool(title='fill', defval=false, group=g_bb_m, inline=inl_bb_m2)
show_bias_m = input.bool(title='Show Bias', defval=false, group=g_bb_m, inline=inl_bb_m2)
show_zone_m = input.bool(title='Zone Change', defval=false, group=g_bb_m, inline=inl_bb_m2)
show_zones3 = input.bool(title='Show Zones ', defval=false, group=g_bb_m, inline=inl_bb_m2)
bb_bias_m = 89.7 //input.float(title='Bias', defval=89.7, step=0.1, group=g_bb_m, inline=inl_bb_m)

// Timeframe
g_bb_time = ''
inl_bb4 = 'inl_bb4'
bb_time = input.timeframe(title='Timeframe', defval='120', group=g_bb_time)

BB_stdDev = 2  //input(2, minval=2.0, maxval=3)
bb_smooth_input = 1  // input(title="Smoothing",defval=1,type=input.integer,group=g_bb, inline=inl_bb1) 

bb_ma(len1, type) =>
    float v = na
    if type == 'SMA'
        v := ta.sma(close, len1)
        v
    if type == 'EMA'
        v := ta.ema(close, len1)
        v
    if type == 'WMA'
        v := ta.wma(close, len1)
        v
    if type == 'VWMA'
        v := ta.wma(close, len1)
        v
    if type == 'HULL'
        v := ta.wma(2 * ta.wma(close, len1 / 2) - ta.wma(close, len1), math.round(math.sqrt(len1)))
        v
    v

// === BB 1 ===
basis = bb_ma(BB_length, bb_type)
dev = BB_stdDev * ta.stdev(close, BB_length)
bb_upper = basis + dev
bb_lower = basis - dev
bb_spread = bb_upper - bb_lower
avgspread = ta.sma(bb_spread, sqz_length)

bb_squeeze = 0.00
bb_squeeze := bb_spread / avgspread * 100
bb_zone = bb_squeeze < 53 ? 0 : bb_squeeze < sqz_length ? 1 : bb_squeeze < 120 ? 2 : bb_squeeze < 160 ? 3 : bb_squeeze > 160 ? 4 : bb_squeeze > 200 ? 5 : na
sqz_color = bb_zone == 0 ? #0045b3 : bb_zone == 1 ? #ff0062 : bb_zone == 2 ? gray : bb_zone == 3 ? #00c3ff : bb_zone == 4 ? white : bb_zone == 5 ? yellow : na

bb_zones_color = sqz_color
basis_angle = angle(basis, 3)
bb_diff = (bb_upper - bb_lower) * 1000
bb_diff_a = angle(bb_diff, 3)
ba_u = angle(bb_upper, 3)
ba_l = angle(bb_lower, 3)


// === BB 2 ===
basis2 = bb_ma(BB_len2, bb2_type)
dev2 = BB_stdDev * ta.stdev(close, BB_len2)
bb_upper2 = basis2 + dev2
bb_lower2 = basis2 - dev2
bb_spread2 = bb_upper2 - bb_lower2
avgspread2 = ta.sma(bb_spread2, sqz_length)
bb_squeeze2 = 0.00
bb_squeeze2 := bb_spread2 / avgspread2 * 100
bb_smooth2 = ta.sma(basis2 + dev2, bb_smooth_input)

bb_zone2 = bb_squeeze2 < 53 ? 0 : bb_squeeze2 < sqz_length ? 1 : bb_squeeze2 < 120 ? 2 : bb_squeeze2 < 160 ? 3 : bb_squeeze2 > 160 ? 4 : na
sqz_color2 = bb_zone2 == 0 ? #0045b3 : bb_zone2 == 1 ? #ff0062 : bb_zone2 == 2 ? gray : bb_zone2 == 3 ? #00c3ff : bb_zone2 == 4 ? white : na

bb_zones_color2 = sqz_color2

basis_angle2 = angle(basis2, 3)
bb_diff2 = (bb_upper2 - bb_lower2) * 1000
bb_diff_a2 = angle(bb_diff2, 3)

plot(show_basis and use_bb_curr1 ? basis : na, title='Basis', color=bb_zones_color)
p1 = plot(use_bb_curr1 and show_bb ? bb_upper : na, 'BB Upper ', color=bb_zones_color)
p2 = plot(use_bb_curr1 and show_bb ? bb_lower : na, 'BB Lower ', color=bb_zones_color)
fill(p1, p2, title='BB 1 Fill', color=show_fill and use_bb_curr1 ? color.new(bb_zones_color, 90) : na)

// Touching BB
touch_d = show_zones1 and close > bb_upper ? 1 : na
plotshape(touch_d, title='Touch Down', color=bb_zones_color, style=shape.cross, location=location.top)
touch_u = show_zones1 and low < bb_lower ? 1 : na
plotshape(touch_u, title='Touch Up', color=bb_zones_color, style=shape.cross, location=location.bottom)
// BB Bias Candles
barcolor(show_bias and bb_diff_a > bb_bias ? yellow : na)

// BB2
plot(show_basis2 and use_bb_curr2 ? basis_angle2 : na, title='BB2 Angle', color=basis_angle2 > 0 ? color.new(green, 100) : color.new(red, 100))
plot(show_basis2 and use_bb_curr2 ? basis2 : na, title='Basis 2', color=bb_zones_color2)
p3 = plot(use_bb_curr2 and show_bb ? bb_upper2 : na, 'BB Upper 2 ', color=bb_zones_color2)
p4 = plot(use_bb_curr2 and show_bb ? bb_lower2 : na, 'BB Lower 2 ', color=bb_zones_color2)
fill(p3, p4, title='BB 2 Fill', color=show_fill2 and use_bb_curr2 ? color.new(bb_zones_color2, 90) : na)
// Touching BB
touch_d2 = show_zones2 and close > bb_upper2 ? 1 : na
plotshape(touch_d2, title='Touch Down 2', color=bb_zones_color2, style=shape.cross, location=location.top)
touch_u2 = show_zones2 and low < bb_lower2 ? 1 : na
plotshape(touch_u2, title='Touch Up 2', color=bb_zones_color2, style=shape.cross, location=location.bottom)

barcolor(show_bias2 and bb_diff_a2 > bb_bias2 ? aqua : na)


// Temp plots
// plot(bb_smooth2, "BB Smooth 2 ",color=bb_zones_color2,linewidth=2)
// plot(bb_upper2, "BB Upper 2 ",color=bb_zones_color2,linewidth=2)
// plot(bb_lower2, "BB Lower 2 ",color=bb_zones_color2,linewidth=2)

// New Bar Multi Timeframe
newbar(res) => ta.change(time(res)) == 0 ? 0 : 1

// === BB Multi ===
f_bb(len)=>
    bb_basis    = bb_ma(len, bb_type_m) 
    dev         = i_bb_stdev * ta.stdev(close, len)
    bb_upper    = bb_basis + dev
    bb_lower    = bb_basis - dev
    bb_spread   = bb_upper - bb_lower
    avgspread   = ta.sma(bb_spread, sqz_length)
    bb_sqz      = bb_spread/ avgspread * 100
    bb_diff     = bb_spread * 1000
    bb_zone     = bb_sqz < 53 ? 0 : bb_sqz < sqz_length ? 1 : bb_sqz < 120 ? 2 : bb_sqz < 160 ? 3 : bb_sqz < 200 ? 4 : bb_sqz < 250 ? 5 : bb_sqz > 250 ? 6 : na
    sqz_color   = bb_zone == 0 ? #0045b3 : bb_zone == 1 ? #ff0062 : bb_zone == 2 ? gray : bb_zone == 3 ? #00c3ff : bb_zone == 4 ? white : bb_zone == 5 ? white : bb_zone == 6 ? yellow : na
    bb_a        = angle(bb_basis, 14)
    bb_diff_a   = angle(bb_diff, 14)
    [bb_basis, bb_upper, bb_lower, bb_sqz, bb_zone, sqz_color, bb_a, bb_diff, bb_diff_a]

[ bb_basis_m, bb_upper_m, bb_lower_m, bb_sqz_m, bb_zone_m, sqz_color_m, bb_a_m, bb_diff_m, bb_diff_a_m ] = 
 request.security(syminfo.tickerid, 
 bb_time, 
 f_bb(bb_len_m),
 gaps=bb_gaps_m ? barmerge.gaps_on : barmerge.gaps_off,
 lookahead=bb_gaps_m==false ? barmerge.lookahead_on : barmerge.lookahead_off )

if newbar(bb_time) == 0 and bb_gaps_m==false
    bb_basis_m := bb_basis_m[1]
    bb_upper_m := bb_upper_m[1]
    bb_lower_m := bb_lower_m[1]
    bb_sqz_m := bb_sqz_m[1]

    bb_zone_m := bb_zone_m[1]
    sqz_color_m := sqz_color_m[1]
    bb_a_m := bb_a_m[1]

    bb_diff_m := bb_diff_m[1]
    bb_diff_a_m := bb_diff_a_m[1]


//[bb_basis_m] = request.security(syminfo.tickerid, bb_time, bb_ma(bb_len_m, bb2_type) )

// dev_m = request.security(syminfo.tickerid, bb_time, 2 * ta.stdev(close, bb_len_m))
// bb_upper_m = request.security(syminfo.tickerid, bb_time, bb_basis_m + dev_m)
// bb_lower_m = request.security(syminfo.tickerid, bb_time, bb_basis_m - dev_m)
//bb_upper_m  = security(syminfo.tickerid, bb_time, bb_upper)
//bb_lower_m  = security(syminfo.tickerid, bb_time, bb_lower)


// bb_spread_m = request.security(syminfo.tickerid, bb_time, bb_upper_m - bb_lower_m)
// avgspread_m = request.security(syminfo.tickerid, bb_time, ta.sma(bb_spread_m, sqz_length))
// bb_sqz_m = request.security(syminfo.tickerid, bb_time, bb_spread_m / avgspread_m * 100)
// bb_diff_m = request.security(syminfo.tickerid, bb_time, bb_spread_m * 1000)
// bb_diff_a_m = request.security(syminfo.tickerid, bb_time, angle(bb_diff_m, 3))


bb_zones_color_m = sqz_color_m

// Zone Change Multi / Growing Squeezing
var zch_m = sqz_color_m
var bch_m = bb_zone_m
var grow_squeeze = bb_zone_m
if ta.change(bb_zone_m)
    grow_squeeze := bb_zone_m>bb_zone_m[1] ? 1 : -1
    zch_m := sqz_color_m[1]
    bch_m := bb_zone_m[1]

plot(bch_m, title='Zone Change 1', style=plot.style_circles, color=zch_m)
plot(grow_squeeze, title='Grow Squeeze', style=plot.style_circles, color=color.new(bb_zones_color_m, 100))
plot(use_bb_multi and show_bb ? bb_a_m : na, 'BB Angle Multi ', color=color.new(bb_zones_color_m, 100), style=plot.style_circles )

plot(show_basis_m and show_bb and use_bb_multi ? bb_basis_m : na, 'BB Basis Multi ', color=bb_zones_color_m)
p5 = plot(use_bb_multi and show_bb ? bb_upper_m : na, 'BB Upper Multi ', color=bb_zones_color_m)
p6 = plot(use_bb_multi and show_bb ? bb_lower_m : na, 'BB Lower Multi ', color=bb_zones_color_m)
fill(p5, p6, title='BB 1 Fill', color=show_fill_m and use_bb_multi ? color.new(bb_zones_color_m, 90) : na)
barcolor(show_bias_m and bb_diff_a_m > bb_bias_m ? lime : na)

// Candle buy or sell
c_state = close > open ? 1 : 0
// Close higher than bb
touch_d_m = show_zones3 and close > bb_upper_m ? 1 : 0
plotshape(touch_d_m, title='Touch Down', color=bb_zones_color_m, style=shape.cross, location=location.top)
// Close lower than bb
touch_u_m = show_zones3 and close < bb_lower_m ? 1 : 0
plotshape(touch_u_m, title='Touch Up', color=bb_zones_color_m, style=shape.cross, location=location.bottom)

// Over Lap of BB's
up_cond = show_overlap and bb_upper > bb_upper2 and close > bb_upper2 and ba_l > -4 ? 1 : 0
barcolor(up_cond ? yellow : na)
down_cond = show_overlap and bb_lower < bb_lower2 and close < bb_lower2 and ba_u < 4 ? 1 : 0
barcolor(down_cond ? aqua : na)


