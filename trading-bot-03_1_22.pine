//@version=4
study(title = "Trading Bot", shorttitle = "Trading Bot", overlay=true, format = format.price, precision = 5)


// Custom inputs
account_size = input(2000, minval=1000)
trending = input(title="Show Trending/Ranging", type=input.bool, defval=true)
show_entry = input(title="Show Entry", type=input.bool, defval=true)
show_label = input(title="Show Labels", type=input.bool, defval=false)
show_cross = input(title="Show Crossover", type=input.bool, defval=false)
show_BB = input(title="Show BB", type=input.bool, defval=true)
use_res = input(title="RES Filter", type=input.bool, defval=true)
use_wae = input(title="WAE Filter", type=input.bool, defval=true)



// === EMA 200 ===
// ==================================================
len_200 = 200
offset = input(title="Offset", type=input.integer, defval=0, minval=-500, maxval=500)
ema_200 = ema(close, len_200)
plot(ema_200, title="EMA 200", color=#fff176, offset=offset, linewidth=3)



// === Bollinger Bands %B ===
// ==================================================
length = input(20, minval=1)
src = input(close, title="Source")
multi = input(2.0, minval=0.001, maxval=50, title="StdDev")
basis = sma(src, length)
deviation = multi * stdev(src, length)
upper = basis + deviation
lower = basis - deviation
bbr = (src - lower)/(upper - lower)
//band1 = hline(1, "Overbought", color=#606060, linestyle=hline.style_dashed)
//band0 = hline(0, "Oversold", color=#606060, linestyle=hline.style_dashed)
//fill(band1, band0, color=color.teal, title="Background")
plot(bbr, "Bollinger Bands %B", color=color.teal,linewidth=0)



// === WAE - LazyBear === 
// ==================================================
sensitivity = 150
fastLength= 20
slowLength= 40
channelLength= 20
wae_mult = 2.0

DEAD_ZONE = nz(rma(tr(true),100)) * 3.7

calc_macd(source, fastLength, slowLength) =>
	fastMA = ema(source, fastLength)
	slowMA = ema(source, slowLength)
	fastMA - slowMA

calc_BBUpper(source, length, wae_mult) => 
	wae_basis = sma(source, length)
	dev = wae_mult * stdev(source, length)
	t = wae_basis + dev
	[t]

calc_BBLower(source, length, wae_mult) => 
	wae_basis = sma(source, length)
	dev = wae_mult * stdev(source, length)
	t = wae_basis - dev
	[t]

t1 = (calc_macd(close, fastLength, slowLength) - calc_macd(close[1], fastLength, slowLength))*sensitivity * 100
t2 = (calc_macd(close[2], fastLength, slowLength) - calc_macd(close[3], fastLength, slowLength))*sensitivity * 100

[e1a] = calc_BBUpper(close, channelLength, wae_mult)
[e1b] = calc_BBLower(close, channelLength, wae_mult)
e1 = (e1a - e1b)
wae_ratio = (e1 / DEAD_ZONE) * 100
//wae_diff = (e1 - DEAD_ZONE) * 100

trendUp = (t1 >= 0) ? t1 : 0
trendDown = (t1 < 0) ? (-1*t1) : 0

plot(trendUp, style=plot.style_circles, linewidth=1, color=(trendUp<trendUp[1])?color.lime:color.green, transp=45, title="UpTrend")
plot(trendDown, style=plot.style_circles, linewidth=1, color=(trendDown<trendDown[1])?color.orange:color.red, transp=45, title="DownTrend")
//plot(e1, color=#A0522D, title="ExplosionLine")
//plot(DEAD_ZONE, color=color.blue, title="DeadZoneLine")
plot(wae_ratio, color=color.white, title="WAE Ratio",style=plot.style_circles)
//plot(wae_diff, color=color.white, title="WAE Ratio",style=plot.style_circles)



// === Bollinger Awesome Alert R1.1 by JustUncleL ===
// ==================================================
bb_use_ema = input(false, title="Use EMA for Bollinger Band")
bb_source = input(close, title="Bollinger Source")
bb_length = input(20, minval=1, title="Bollinger Length")
bb_mult = input(2.0, title="Base Multiplier", minval=0.5, maxval=10)
sqz_length = 100
sqz_threshold = input(60, minval=60, title="Squeeze Threshold")

// EMA inputs
fast_ma_len = input(3, title="Fast EMA length", minval=2)

// Breakout Indicator Inputs
ema_1 = ema(bb_source, bb_length)
sma_1 = sma(bb_source, bb_length)
bb_basis = bb_use_ema ? ema_1 : sma_1
fast_ma = ema(bb_source, fast_ma_len)

// Deviation
// * I'm sure there's a way I could write some of this cleaner, but meh.
dev = stdev(bb_source, bb_length)
bb_dev = bb_mult * dev
// Bands
bb_upper = bb_basis + bb_dev
bb_lower = bb_basis - bb_dev
// Calculate BB spread and average spread
bb_spread = bb_upper - bb_lower
avgspread = sma(bb_spread, sqz_length)
// Calculate BB relative %width for Squeeze indication
bb_squeeze = 0.00
bb_squeeze := bb_spread / avgspread * 100
// Calculate Upper and Lower band painting offsets based on 50% of atr.
bb_offset = atr(14) * 0.5
bb_sqz_upper = bb_upper + bb_offset
bb_sqz_lower = bb_lower - bb_offset

// plot 
plot(fast_ma, title="Fast EMA", color=color.yellow, transp=10, linewidth=2)
plot(bb_basis, title="Basis Line", color=color.red, transp=10, linewidth=2)
plot(bb_squeeze[0], title="BB Squeeze", color=bb_squeeze < sqz_threshold ? color.blue : color.gray, transp=10, linewidth=0)

// plot BB upper and lower bands
ubi = plot(show_BB ? bb_upper: na, title="Upper Band Inner", color=color.blue, transp=10, linewidth=1)
lbi = plot(show_BB ? bb_lower:na, title="Lower Band Inner", color=color.blue, transp=10, linewidth=1)
// center BB channel fill
fill(ubi, lbi, title="Center Channel Fill", color=color.silver, transp=90)

//Indicate BB squeeze based on threshold.
usqzi = plot(show_BB ? bb_sqz_upper: na, "Hide Sqz Upper", transp=100)
lsqzi = plot(show_BB ? bb_sqz_lower: na, "Hide Sqz Lower", transp=100)
fill(ubi, usqzi, color=bb_squeeze > sqz_threshold ? color.white : color.blue, transp=50)
fill(lbi, lsqzi, color=bb_squeeze > sqz_threshold ? color.white : color.blue, transp=50)



// === SSL Hybrid - Mihkel00 ===
// ==================================================
len = input(title = "SSL1 / Baseline Length", defval = 60)
show_Baseline = input(title="Show Baseline", type=input.bool, defval=true)
show_SSL1 = input(title = "Show SSL1", type = input.bool, defval = false)
show_atr = input(title = "Show ATR bands", type = input.bool, defval = true)
//ATR
atrlen = input(14, "ATR Period")
mult = input(1, "ATR Multi", step = 0.1)
smoothing = input(title = "ATR Smoothing", defval = "WMA", options = ["RMA", "SMA", "EMA", "WMA"])

ma_function(source, atrlen) =>
	if smoothing == "RMA"
		rma(source, atrlen)
	else
		if smoothing == "SMA"
			sma(source, atrlen)
		else
			if smoothing == "EMA"
				ema(source, atrlen)
			else
				wma(source, atrlen)

atr_slen = ma_function(tr(true), atrlen)

////ATR Up/Low Bands
upper_band = atr_slen * mult + close
lower_band = close - atr_slen * mult

//SSL 1 and SSL2
emaHigh = wma(2 * wma(high, len / 2) - wma(high, len), round(sqrt(len)))
emaLow = wma(2 * wma(low, len / 2) - wma(low, len), round(sqrt(len)))

// BASELINE VALUES
BBMC = wma(2 * wma(close, len / 2) - wma(close, len), round(sqrt(len)))
multy = 0.2
range = tr
rangema = ema(range, len)
upperk =BBMC + rangema * multy
lowerk = BBMC - rangema * multy

//SSL1 VALUES
Hlv = int(na)
Hlv := close > emaHigh ? 1 : close < emaLow ? -1 : Hlv[1]
sslDown = Hlv < 0 ? emaHigh : emaLow

//COLORS
color_ssl1 = close > sslDown ? #00c3ff : close < sslDown ? #ff0062 : na
ssl_color_buy = #00c3ff
ssl_color_sell = #ff0062
ssl_color_bar = close > upperk ? ssl_color_buy : close < lowerk ? ssl_color_sell : color.gray

//Plot Baseline & SSL1
p1 = plot(show_Baseline ? BBMC : na,title='SSL Baseline', color=ssl_color_bar, linewidth=4,transp=0)
//DownPlot = plot( show_SSL1 ? sslDown : na, title="SSL1", linewidth=3, color=color_ssl1, transp=10)

// ATR plot
atr_upper = plot(show_atr ? upper_band : na, "+ATR", color=color.white, transp=80)
atr_lower = plot(show_atr ? lower_band : na, "-ATR", color=color.white, transp=80)



// === RES - Ranging EMA Spread  ===
// ==================================================
ema1length = 12
ema2length = 50
ranginglength = 3
rangingmaxvalue = 0.1
rangingminvalue = -0.1
enablebarcolors = false

// EMA spread
ema1 = ema(close, ema1length)
ema2 = ema(close, ema2length)
spread = ((ema2 / ema1) -1) * 100
r1 = (spread >= rangingminvalue and spread <= rangingmaxvalue) 
r2 = (spread[1] >= rangingminvalue and spread[1] <= rangingmaxvalue)
r3 = (spread[2] >= rangingminvalue and spread[2] <= rangingmaxvalue)
r4 = (spread[3] >= rangingminvalue and spread[3] <= rangingmaxvalue)
r5 = (spread[4] >= rangingminvalue and spread[4] <= rangingmaxvalue)

ranging = false

if (ranginglength == 1) 
    ranging := r1
if (ranginglength == 2) 
    ranging := r1 and r2
if (ranginglength == 3) 
    ranging := r1 and r2 and r3
if (ranginglength == 4) 
    ranging := r1 and r2 and r3 and r4
if (ranginglength == 5) 
    ranging := r1 and r2 and r3 and r4 and r5

res = spread > 0 ? 0-spread : abs(spread)
res := res * 100
col_low = #ffff00 // yelow
col_mid = #000080 // navy
col_high = #00FFFF // aqua
color_res = ranging ? col_low : spread > spread[1] ? col_mid : col_high
plot(res,"RES", style=plot.style_circles, linewidth=0, color=color_res, transp=0)


// === QQE MOD ===
// ==================================================
RSI_Period = 6
SF = 5
QQE = 3
ThreshHold = 3
qqe_src = close
Wilders_Period = RSI_Period * 2 - 1
Rsi = rsi(qqe_src, RSI_Period)
RsiMa = ema(Rsi, SF)
AtrRsi = abs(RsiMa[1] - RsiMa)
MaAtrRsi = ema(AtrRsi, Wilders_Period)
dar = ema(MaAtrRsi, Wilders_Period) * QQE

longband = 0.0
shortband = 0.0
trend = 0

DeltaFastAtrRsi = dar
RSIndex = RsiMa
newshortband = RSIndex + DeltaFastAtrRsi
newlongband = RSIndex - DeltaFastAtrRsi
longband := RSIndex[1] > longband[1] and RSIndex > longband[1] ? 
   max(longband[1], newlongband) : newlongband
shortband := RSIndex[1] < shortband[1] and RSIndex < shortband[1] ? 
   min(shortband[1], newshortband) : newshortband
cross_1 = cross(longband[1], RSIndex)
trend := cross(RSIndex, shortband[1]) ? 1 : cross_1 ? -1 : nz(trend[1], 1)
FastAtrRsiTL = trend == 1 ? longband : shortband

qqe_length = 50
qqe_mult = 0.35
qqe_basis = sma(FastAtrRsiTL - 50, qqe_length)
qqe_dev = qqe_mult * stdev(FastAtrRsiTL - 50, qqe_length)
qqe_upper = qqe_basis + qqe_dev
qqe_lower = qqe_basis - qqe_dev
color_bar = RsiMa - 50 > qqe_upper ? #00c3ff : RsiMa - 50 < qqe_lower ? #ff0062 : color.gray

// Zero cross
QQEzlong = 0
QQEzlong := nz(QQEzlong[1])
QQEzshort = 0
QQEzshort := nz(QQEzshort[1])
QQEzlong := RSIndex >= 50 ? QQEzlong + 1 : 0
QQEzshort := RSIndex < 50 ? QQEzshort + 1 : 0
//  
//Zero = hline(0, color=color.white, linestyle=hline.style_dotted, linewidth=1)

////////////////////////////////////////////////////////////////

RSI_Period2 = 6
SF2 = 5
QQE2 = 1.61
ThreshHold2 = 3
src2 = close
//

//
Wilders_Period2 = RSI_Period2 * 2 - 1
Rsi2 = rsi(src2, RSI_Period2)
RsiMa2 = ema(Rsi2, SF2)
AtrRsi2 = abs(RsiMa2[1] - RsiMa2)
MaAtrRsi2 = ema(AtrRsi2, Wilders_Period2)
dar2 = ema(MaAtrRsi2, Wilders_Period2) * QQE2
longband2 = 0.0
shortband2 = 0.0
trend2 = 0

DeltaFastAtrRsi2 = dar2
RSIndex2 = RsiMa2
newshortband2 = RSIndex2 + DeltaFastAtrRsi2
newlongband2 = RSIndex2 - DeltaFastAtrRsi2
longband2 := RSIndex2[1] > longband2[1] and RSIndex2 > longband2[1] ? 
   max(longband2[1], newlongband2) : newlongband2
shortband2 := RSIndex2[1] < shortband2[1] and RSIndex2 < shortband2[1] ? 
   min(shortband2[1], newshortband2) : newshortband2
cross_2 = cross(longband2[1], RSIndex2)
trend2 := cross(RSIndex2, shortband2[1]) ? 1 : cross_2 ? -1 : nz(trend2[1], 1)
FastAtrRsi2TL = trend2 == 1 ? longband2 : shortband2


//
// Zero cross
QQE2zlong = 0
QQE2zlong := nz(QQE2zlong[1])
QQE2zshort = 0
QQE2zshort := nz(QQE2zshort[1])
QQE2zlong := RSIndex2 >= 50 ? QQE2zlong + 1 : 0
QQE2zshort := RSIndex2 < 50 ? QQE2zshort + 1 : 0
//  

hcolor2 = RsiMa2 - 50 > ThreshHold2 ? color.silver :
   RsiMa2 - 50 < 0 - ThreshHold2 ? color.silver : na
plot(FastAtrRsi2TL - 50, title='QQE Line', color=color.white, transp=0, linewidth=2, style=plot.style_circles)
plot(RsiMa2 - 50, color=hcolor2, transp=50, title='Histo2', style=plot.style_circles)

Greenbar1 = RsiMa2 - 50 > ThreshHold2
Greenbar2 = RsiMa - 50 > upper

Redbar1 = RsiMa2 - 50 < 0 - ThreshHold2
Redbar2 = RsiMa - 50 < lower
plot(Greenbar1 and Greenbar2 == 1 ? RsiMa2 - 50 : na, title="QQE Up", style=plot.style_circles, color=#00c3ff, transp=0)
plot(Redbar1 and Redbar2 == 1 ? RsiMa2 - 50 : na, title="QQE Down", style=plot.style_circles, color=#ff0062, transp=0)


// === Trading Properties ===
// ==================================================
var bb_count = 0
allow_trade = 0
entry_price = 0.00
exit_price = 0.00
// Initialize an empty array to store variables
props = array.new_float(4)


// === Trading Methods ===
// ==================================================
entry_signal() =>
	dir = 0
	trading = 0
	blue_zone = 0
	// Type of candle - Long / Short
	candle = close > open ? 1 : 0

	// === Ranging ===
	if bb_squeeze < sqz_threshold == 1
		blue_zone := 1
		// Sell signal
		if (bbr > 0.97 and candle == 1) or (fast_ma < open and high > bb_upper and candle == 1)
			dir := -1
			trading := 1

			// SSL baseline Filter - Strong trend
			// if (BBMC > bb_basis) or (BBMC < bb_lower)
			// 	dir := na
			// 	trading := 0

			// WAE Filter 2
			if (use_wae == true and wae_ratio < 40)
				dir := na
				trading := 0
			
			
		// Buy signal
		else if (bbr < 0.03 and candle == 0) or (fast_ma > open and low < bb_lower and candle == 0)
			dir := 1
			trading := 1

			// WAE Filter 2
			if (use_wae == true and wae_ratio < 50)
				dir := na
				trading := 0

			// SSL baseline Filter - Strong trend
			// if BBMC < bb_basis
			// 	dir := na
			// 	trading := 0

		// else if (BBMC > bb_upper) and (ssl_color_bar == ssl_color_sell) and color_res == col_high
		// 	dir := -1
		// 	trading := 1

		// Don't trade
		else
			dir := na
			trading := 0

	// === Trending ===	
	else if bb_squeeze < sqz_threshold == 0
		blue_zone := 0
		// Sell signal
		if (bbr >= 0.98 and candle == 1)
			dir := -1
			trading := 1

			// RES FILTER 1
			if use_res == true and res < -3
			//if use_res == true and res < 0 and color_res != col_high
			//if res < 0 and use_res == true
				dir := na
				trading := 0

			// WAE Filter 2
			if (use_wae == true and wae_ratio < 70)
				dir := na
				trading := 0
			
		// Buy signal
		else if (bbr <= 0.02 and candle == 0)
			dir := 1
			trading := 1

			// RES FILTER 1
			if res > 0 and use_res == 1
				dir := na
				trading := 0

			// WAE Filter 2
			if (use_wae == true and wae_ratio < 70) or (trendDown<trendDown[1]) // orange WAE candle
				dir := na
				trading := 0

		// Buy - Retracements
		// else if (BBMC > bb_basis) and (ssl_color_bar == ssl_color_buy) and (open < fast_ma) and (low < ema_200)
		// 	dir := 1
		// 	trading := 1
		// else if (BBMC > bb_basis) and (ssl_color_bar == ssl_color_buy) and (close < fast_ma) and (open < fast_ma)
		// 	dir := 1
		// 	trading := 1
		// else if (BBMC < bb_basis) and (ssl_color_bar == ssl_color_sell) and (close < fast_ma) and (open < fast_ma)
		// 	dir := 1
		// 	trading := 1
		// else if (BBMC < bb_basis) and (ssl_color_bar == ssl_color_buy) and (low < basis) and (close < fast_ma)
		// 	dir := 1
		// 	trading := 1
		
	else if (RsiMa2 - 50 > ThreshHold2 ) or (RsiMa2 - 50 < 0 - ThreshHold2)
		dir := na
		trading := 0
	else
		dir := na
		trading := 0

	[dir, trading, blue_zone]

[trade_dir, trading, blue_zone] = entry_signal() 

calc_trade_values() =>
	p = 0.00
	rp = 0.00
	sl = 0.00
	pips = 0.00
	p_value = 0.00

	// Sell
	if trade_dir < 0
		sl := upper_band
		rp := (1 - (upper_band / close)) * 100
		p  := (2 / rp) * account_size
	// Buy
	else
		sl := lower_band
		rp := (1 - (lower_band / close)) * 100
		p  := (2 / rp) * account_size
    
    p := round(abs(p))
    rp := abs(rp)
    pips := abs( (close - sl) * 10000 )
    p_value := (account_size * rp) / pips
	 
	[p,rp,sl,pips,p_value]
	
[pos_size,risk_perc, stop_loss,pips, pip_value] = calc_trade_values()

// === Labels ===
//l_offset = 2 * (time - time[1])
labelText = "Lot Size: " + tostring(pos_size) 
 + "\nRisk: 0" + tostring(risk_perc, "#.00") 
 + "% \nStop Loss: " + tostring(stop_loss, "#.0000") 
 + "\nPips: " + tostring(pips, "#.00")
 + "\nPip Value: " + tostring(pip_value, "#.0000")

if show_label and trading
	if trade_dir > 0
        buy = label.new(x=time,y=high,text=labelText,xloc=xloc.bar_time, yloc=yloc.belowbar,color=color.green,textcolor=color.white, size=size.normal)
    else
        sell = label.new(x=time,y=high,text=labelText,xloc=xloc.bar_time, yloc=yloc.abovebar,color=color.red,textcolor=color.white, size=size.normal)

// BB count
if trading > 0
	bb_count := bb_count+1
else 
	bb_count := 0

if bb_count > 2
	allow_trade := trade_dir
if bb_count > 4
	bb_count := 0

// BB count
bb_color = bb_count < 2 ? #555555 : #00ff00
plot(bb_count,title="BB Count", color=bb_color, transp=100)

// Buy / Sell indicators
plotshape(trade_dir > 0 and show_entry ? 1 : na, title="Entry Buy", color=res < 0 and trade_dir > 0 ? color.orange : color.green, location = location.belowbar, style=shape.labelup, text="B", textcolor=color.white, size=size.small)
plotshape(trade_dir < 0 and show_entry ? -1 : na, title="Entry Sell", color= res > 0 and trade_dir < 0 ? color.orange : color.red, location = location.abovebar, style=shape.labeldown, text="S", textcolor=color.white, size=size.small)
plot(trading ,title="Trading",color= trading ? color.green : color.gray)

// Cross: Basis line & SSL baseline
// co = crossover(BBMC,bb_basis) 
// cu = crossunder(BBMC,bb_basis)
// plotshape(co and show_cross ? 1 : na, title="Cross Up", color=color.white, location = location.belowbar, style=shape.labelup, text="x", textcolor=color.black, size=size.small)
// plotshape(cu and show_cross ? 1 : na, title="Cross Up", color=color.white, location = location.abovebar, style=shape.labeldown, text="x", textcolor=color.black, size=size.small)




