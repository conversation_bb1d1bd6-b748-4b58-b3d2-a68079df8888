// This work is licensed under a Attribution-NonCommercial-ShareAlike 4.0 International (CC BY-NC-SA 4.0) https://creativecommons.org/licenses/by-nc-sa/4.0/
// © LuxAlgo

//@version=5
indicator("Predictive Channels [LuxAlgo]", "LuxAlgo - Predictive Channels", overlay = true)
//-----------------------------------------------------------------------------}
//Settings
//-----------------------------------------------------------------------------{
g_PC = 'Predictive Channels -------------------------------------------------------------'
PC_mult  = input.float(5, 'Factor', minval = 0, group=g_PC)
PC_slope = input.float(50, minval = 0, group=g_PC) * PC_mult
i_PC_show_plot = input.bool(true,title = 'Show Plots', group=g_PC)
pc_i_show_info = input.bool(true,title = 'Show Info', group=g_PC)
i_PC_show_ma = input.bool(true,title = 'Show MA', group=g_PC)

var upper_bound = 0.0
var pc_direction = 0
var pc_bars = 0


//Style
red = #ff0062
orange = #ff9800
yellow = #FFFF00
green = #4caf50
lime = #50e600
aqua = #00bcd4
blue = #2962ff
dark_blue = #2962ff
violet = #814dff
white = #ffffff
gray = #707070
black = #000000

// r2Css = input(red, 'Upper Resistance', inline = 'resistance', group = 'Colors')
// r1Css = input(color.new(red, 50), 'Lower', inline = 'resistance', group = 'Colors')

// s1Css = input(color.new(green, 50), 'Upper Support', inline = 'support', group = 'Colors')
// s2Css = input(green, 'Lower', inline = 'support', group = 'Colors')

// avgCss = input(gray, 'Average', group = 'Colors')

// areaBull = input(color.new(green, 80), 'Fill', inline = 'area', group = 'Colors')
// areaBear = input(color.new(red, 80), '', inline = 'area', group = 'Colors')


//-----------------------------------------------------------------------------}
//Calculation
//-----------------------------------------------------------------------------{
var PC_avg = close
var PC_os = 1
var PC_hold_atr = 0.0

PC_atr = nz(ta.atr(200)) * PC_mult

PC_avg := math.abs(close - PC_avg) > PC_atr ? close 
  : PC_avg + PC_os * PC_hold_atr / PC_slope

PC_hold_atr := PC_avg == close ? PC_atr / 2 : PC_hold_atr
PC_os := PC_avg > PC_avg[1] ? 1 : PC_avg < PC_avg[1] ? -1 : PC_os

PC_R2 = PC_avg + PC_hold_atr
PC_R1 = PC_avg + PC_hold_atr/2
PC_S1 = PC_avg - PC_hold_atr/2
PC_S2 = PC_avg - PC_hold_atr

h2 = ta.hma(close,20)
h3 = ta.hma(close,50)

// MA's
ema20 = request.security(syminfo.tickerid, "30", ta.ema(close,20) )

//-----------------------------------------------------------------------------}
//Functions
//-----------------------------------------------------------------------------{
// SYM Info
min_tick = syminfo.mintick
var int decimals = int(math.log10(1/min_tick))
decimals := decimals == 2 ? 1 : decimals == 0 ? 1 : decimals
// plot(min_tick, title='Min Tick')
// plot(decimals, title='Decimals')
PC_get_pip_value(point1, point2, abs_value) =>
    diff_points = abs_value ? math.abs( (point1 - point2) ) : point1 - point2
    pip_value = decimals<3 ? diff_points / syminfo.mintick / 100 : diff_points / syminfo.mintick / 10

PC_get_angle() =>
    PC_R2>PC_R2[1] ? 1 : -1

PC_get_level(obj) =>
  
    var int level = 0
    // Above R2
    if obj>PC_R2 
        level := 6
    // Above R1 
    if obj<PC_R2 and obj>PC_R1
        level := 5
    // Above Average 
    if obj<PC_R1 and obj>PC_avg
        level := 4
    // Below Average
    if obj<PC_avg and obj>PC_S1
        level := 3
    // Below S1
    if obj<PC_S1 and obj>PC_S2
        level := 2
    // Below S2
    if obj<PC_S2
        level := 1

    level

PC_is_between(obj) =>
    between = PC_R2>obj and PC_S2<obj ? 1 : 0

PC_barssince()=>
    bars = ta.barssince( PC_get_pip_value(PC_R2,PC_R2[1], true)>5 )

if PC_get_pip_value(PC_R2,PC_R2[1], true)>5
    upper_bound := PC_R2
    pc_direction := upper_bound>upper_bound[1] ? 1 : -1

pc_bars := PC_barssince()


//-----------------------------------------------------------------------------}
//Plot
//-----------------------------------------------------------------------------{
plot(pc_i_show_info ? pc_bars : na, title='Bars Since', color=color.new(white,100))
plot(pc_i_show_info ? pc_direction : na, title='direction', color=color.new(white,100))
plot(pc_i_show_info ? PC_is_between(ema20) : na, title='is between', color=color.new(white,100))
plot(pc_i_show_info ? PC_get_angle() : na, title='Get Angle', color=color.new(white,100))

plot_0 = plot(i_PC_show_plot ? close : na, color = na, display = display.none, editable = false)

//SR PLots
plot(i_PC_show_plot ? PC_R2 : na, 'Upper Resistance', close == PC_avg ? na : red)
plot(i_PC_show_plot ? PC_R1 : na, 'Lower Resistance', close == PC_avg ? na : color.new(red, 50))
plot_avg = plot(i_PC_show_plot ? PC_avg : na, 'Average', close == PC_avg ? na : gray)
plot(i_PC_show_plot ? PC_S1 : na, 'Upper Support', close == PC_avg ? na : color.new(green, 50))
plot(i_PC_show_plot ? PC_S2 : na, 'Lower Support', close == PC_avg ? na : green)



//Fill
topcss = close > PC_avg ? color.new(green, 80) : color.new(chart.bg_color, 100)
btmcss = close < PC_avg ? color.new(red, 80) : color.new(chart.bg_color, 100)
fill(plot_0, plot_avg, PC_R2, PC_S2, topcss, btmcss)


//plotshape(close>PC_R2 ? 1 : 0,"Top End",style=shape.circle, location = location.top, color = close>open ? red : color.rgb(230, 122, 0))
//plotshape(close<PC_S2 ? 1 : 0,"Bottom End",style=shape.circle, location = location.bottom, color = close<open ? #00be00 : color.rgb(0, 45, 208))

// Plot MA
plot(i_PC_show_ma ? h2 : na, title = 'H2', color = color.white)
plot(i_PC_show_ma ? h3 : na, title = 'H3', color = color.blue)
// Plot Levels
plot(pc_i_show_info ? PC_get_level(h3) : na, title = 'Get Level', color = color.new(color.blue,100))

// Alerts
pc_sell_cond = close>PC_R2 and h3>PC_R2
pc_buy_cond = close<PC_S2 and h3<PC_S2

plotshape(pc_sell_cond ? 1 : na,title="Sell",color=red ,style=shape.circle,location=location.top)
plotshape(pc_buy_cond  ? 1 : na,title="Buy",color=green ,style=shape.circle,location=location.bottom)

//-----------------------------------------------------------------------------}