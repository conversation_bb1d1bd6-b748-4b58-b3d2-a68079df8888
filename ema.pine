//@version=4
study("EMA ", overlay=true)

ema1 = input(8, "Ema 1")
ema2 = input(20, "Ema 2")
ema3 = input(50, "Ema 3")
ema4 = input(200, "Ema 4")
e1 = input(title="EZ 1", type=input.bool, defval=false)
e2 = input(title="EZ 2", type=input.bool, defval=false)
e3 = input(title="EZ 3", type=input.bool, defval=false)
e4 = input(title="EZ 4", type=input.bool, defval=true)
e_angle = input(100, "EMA Angle")
ez_candles = input(title="EZ Candles", type=input.bool, defval=false)

ema_time = input(title="Timeframe", type=input.resolution, defval="15")
show_multi = input(title="Show Multi", type=input.bool, defval=false)

red = #ff0062
aqua = #00bcd4
yellow = #FFFF00
orange = #ff9800
green = #4caf50
lime = #50ff00
white = #ffffff
c_hide = color.new(#ffffff,100)
blue = #0053ff
magenta = #ff0062
purple = #0045b3
gray = #707070
black = #000000

angle(_src, len) =>
    rad2degree=180/3.14159265359  //pi 
    ang=rad2degree*atan((_src[0] - _src[1])/atr(len))

e100 = ema(close, 100)
e100_a = angle(e100,e_angle)
e200 = ema(close, ema4)
e200_a = angle(e200,e_angle)
ea = abs(e200_a)
e_zone = ea < 1 ? 0 : 
 ea < 2 ? 1 : 
 ea < 3 ? 2 :
 ea < 4 ? 3 :
 ea < 5 ? 4 :
 ea < 6 ? 5 :
 ea > 6 ? 6 : na
e_color = e_zone == 0 ? red :
 e_zone == 1 ? orange : 
 e_zone == 2 ? yellow : 
 e_zone == 3 ? gray : 
 e_zone == 4 ? blue:
 e_zone == 5 ? lime:
 e_zone == 6 ? white: na

plot(e1 ? ema(close, ema1) : na, color=color.white, title='EMA 1')
plot(e2 ? ema(close, ema2) : na, color=#0088FA , title='20 EMA')
plot(e3 ? ema(close, ema3) : na, color=#FA00D0, title='50 EMA')
plot(e4 ? e200 : na, color=e_color, title='200 EMA')
plot(e200_a, color=color.new(e_color,100), title='Angel ema 200',style=plot.style_circles)
plot(e100_a, color=e100_a>0?color.new(#FF7000,100):color.new(#814dff,100), title='Angel ema 100',style=plot.style_circles)
barcolor(ez_candles ? e_color : na)

// Multi
e200_m = security(syminfo.tickerid, ema_time, ema(close, ema4) )
e200_a_m = security(syminfo.tickerid, ema_time, angle(e200_m,e_angle) ) 
ea_m = security(syminfo.tickerid, ema_time, abs(e200_a_m) ) 
e_zone_m = ea_m < 1 ? 0 : 
 ea_m < 2 ? 1 : 
 ea_m < 3 ? 2 :
 ea_m < 4 ? 3 :
 ea_m < 5 ? 4 :
 ea_m < 6 ? 5 :
 ea_m > 6 ? 6 : na

e_color_m = e_zone_m == 0 ? red :
 e_zone_m == 1 ? orange : 
 e_zone_m == 2 ? yellow : 
 e_zone_m == 3 ? gray : 
 e_zone_m == 4 ? blue:
 e_zone_m == 5 ? lime:
 e_zone_m == 6 ? white: na

plot(show_multi ? e200_m : na, color=e_color_m, title='Multi 200')