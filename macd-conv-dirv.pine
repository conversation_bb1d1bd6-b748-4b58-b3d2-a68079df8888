//@version=4
study(title="MACD - Convergence ", shorttitle="MACD - convergence", resolution="")

angle(_src, len) =>
    rad2degree=180/3.14159265359  //pi 
    ang=rad2degree*atan((_src[0] - _src[1])/atr(len))

// Getting inputs
fast_length = input(title="Fast Length", type=input.integer, defval=8) // 12
slow_length = input(title="Slow Length", type=input.integer, defval=25) // 26
src = input(title="Source", type=input.source, defval=close)
signal_length = input(title="Signal Smoothing", type=input.integer, minval = 1, maxval = 50, defval = 9)
sma_source = input(title="Oscillator MA Type", type=input.string, defval="EMA", options=["SMA", "EMA"])
sma_signal = input(title="Signal Line MA Type", type=input.string, defval="EMA", options=["SMA", "EMA"])
show_conv_divr = input(title="Show Divergence Convergence",type=input.bool,defval=true)



// Plot colors
col_macd = input(#2962FF, "MACD Line  ", input.color, group="Color Settings", inline="MACD")
col_signal = input(#FF6D00, "Signal Line  ", input.color, group="Color Settings", inline="Signal")
col_grow_above = input(#26A69A, "Above   Grow", input.color, group="Histogram", inline="Above")
col_fall_above = input(#B2DFDB, "Fall", input.color, group="Histogram", inline="Above")
col_grow_below = input(#FFCDD2, "Below Grow", input.color, group="Histogram", inline="Below")
col_fall_below = input(#FF5252, "Fall", input.color, group="Histogram", inline="Below")
// Calculating
fast_ma = sma_source == "SMA" ? sma(src, fast_length) : ema(src, fast_length)
slow_ma = sma_source == "SMA" ? sma(src, slow_length) : ema(src, slow_length)
macd = fast_ma - slow_ma
signal = sma_signal == "SMA" ? sma(macd, signal_length) : ema(macd, signal_length)
m_histo = macd - signal


plot(m_histo, title="Histogram", style=plot.style_columns, color=(m_histo>=0 ? (m_histo[1] < m_histo ? col_grow_above : col_fall_above) : (m_histo[1] < m_histo ? col_grow_below : col_fall_below)))
plot(macd, title="MACD", color=col_macd)
plot(signal, title="Signal", color=col_signal)

// Plot convergence
var int conv_s = 0
var int conv_b = 0
candle = close > open ? 1 : 0
conv_s := candle==1 and m_histo>0 and show_conv_divr and macd>signal and angle(macd,2)<0 and angle(signal,2)>0 ? 1 : na
plotshape(conv_s,title="Sell Convergence",color=#ff0000,style=shape.circle,location=location.top)
conv_b := candle==0 and m_histo<0 and show_conv_divr and macd<signal and angle(macd,2)>0 and angle(signal,2)<0 ? -1 : na
plotshape(conv_b,title="Buy Convergence",color=color.lime,style=shape.circle,location=location.bottom)