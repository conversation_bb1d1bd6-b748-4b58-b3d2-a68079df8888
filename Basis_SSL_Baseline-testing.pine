//@version=4
study(shorttitle="Basis_SSL testing", title="BB Basis and SSL baseline testing")

bb_use_ema = input(false, title="Use EMA for Bollinger Band")

// Bollinger Band Basis line
bb_length = 20
bb_source = close


// === SERIES ===

// Breakout Indicator Inputs
ema_1 = ema(bb_source, bb_length)
sma_1 = sma(bb_source, bb_length)
bb_basis = bb_use_ema ? ema_1 : sma_1


// === PLOTTING ===

// plot BB basis
//plot(bb_basis, title="Basis Line", color=color.red, transp=10, linewidth=2)


// === SSL Hybrid ===
len = input(title = "SSL1 / Baseline Length", defval = 60)
BBMC = wma(2 * wma(close, len / 2) - wma(close, len), round(sqrt(len)))
//Plot Baseline & SSL1
//p1 = plot(BBMC, color=color.blue, linewidth=4,transp=0, title='MA Baseline')

// plot difference BB basis & SSL baseline
diff2 = ema( (BBMC - bb_basis), 11)
c1 = diff2 > 0 ? #00c3ff : #ff0062
p1 = plot(diff2, title='Diff SSL baseline & BB basis', color=c1, linewidth=4,transp=0, style=plot.style_columns )




// eof
